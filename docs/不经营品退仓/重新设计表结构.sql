-- ========================================
-- 不经营品退仓表结构重新设计（基于整体闭环）
-- 参考 importReturnWarehouseExec 逻辑
-- 数据最终落地到 iscm_store_return_execute_order_main 和 iscm_store_return_execute_order_detail
-- ========================================

-- 1. 枚举扩展
-- ReturnWarehouseBusinessTypeEnum 新增：
-- UNMANAGE_GOODS_RETURN((byte)8, "不经营品退仓")
-- 
-- ReturnTypeEnum 复用现有：
-- DEFECTIVE(3, "不良品退仓") // 不经营品属于不良品的一种

-- ========================================
-- 核心设计思路：
-- 1. 简化表结构，只保留必要的任务跟踪和进度管理
-- 2. 参考导入逻辑，直接生成退仓执行单
-- 3. 使用Redis缓存处理进度（参考导入逻辑）
-- 4. 异步处理，按门店分组
-- ========================================

-- 1. 主表：不经营品退仓任务表（简化版）
CREATE TABLE `unmanage_goods_return_task` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `task_code` varchar(64) NOT NULL COMMENT '任务编码',
  `task_name` varchar(128) NOT NULL COMMENT '任务名称',
  `platform_org_id` bigint(20) NOT NULL COMMENT '平台机构ID',
  `platform_name` varchar(128) NOT NULL COMMENT '平台名称',
  `org_type` tinyint(4) NOT NULL COMMENT '选中机构类型：1-参数配置 2-自选门店',
  `org_ids` text NOT NULL COMMENT '选中机构org_ids集合，逗号隔开',
  `goods_type` tinyint(4) NOT NULL COMMENT '商品类型：1-全部不经营商品 2-自定义商品',
  `goods_nos`  text COMMENT '选中商品goods_nos集合，自定义时必填，逗号隔开',
  `store_count` int(11) DEFAULT 0 COMMENT '涉及门店数量',
  `goods_count` int(11) DEFAULT 0 COMMENT '涉及商品数量',
  `total_return_qty` decimal(18,4) DEFAULT 0 COMMENT '总退仓数量',
  `total_return_amount` decimal(18,4) DEFAULT 0 COMMENT '总退仓金额',
  `task_status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '任务状态：1-计算中 2-计算完成 3-处理失败',
  `process_count` int(11) DEFAULT 0 COMMENT '总处理数量',
  `success_count` int(11) DEFAULT 0 COMMENT '成功处理数量',
  `error_count` int(11) DEFAULT 0 COMMENT '错误处理数量',
  `error_msg`  text COMMENT '错误信息',
  `start_time` datetime COMMENT '开始时间',
  `end_time` datetime COMMENT '结束时间',
  `status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '状态(-1删除，0正常)',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_update` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `extend` text COMMENT '扩展字段',
  `version` int(11) NOT NULL DEFAULT 1 COMMENT '版本号',
  `created_by` bigint(20) NOT NULL COMMENT '创建人ID',
  `created_name` varchar(64) NOT NULL COMMENT '创建人',
  `updated_by` bigint(20) COMMENT '更新人ID',
  `updated_name` varchar(64) COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_task_code` (`task_code`),
  KEY `idx_platform_org_id` (`platform_org_id`),
  KEY `idx_task_status` (`task_status`),
  KEY `idx_created_by` (`created_by`),
  KEY `idx_gmt_create` (`gmt_create`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='不经营品退仓任务表';

-- 2. 处理明细表：记录每个门店的处理结果
CREATE TABLE `unmanage_goods_return_process_detail` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `task_id` bigint(20) NOT NULL COMMENT '任务ID',
  `task_code` varchar(64) NOT NULL COMMENT '任务编码',
  `store_org_id` bigint(20) NOT NULL COMMENT '门店机构ID',
  `store_code` varchar(64) NOT NULL COMMENT '门店编码',
  `store_name` varchar(128) NOT NULL COMMENT '门店名称',
  `company_org_id` bigint(20) NOT NULL COMMENT '企业机构ID',
  `company_code` varchar(64) NOT NULL COMMENT '企业编码',
  `company_name` varchar(128) NOT NULL COMMENT '企业名称',
  `goods_count` int(11) DEFAULT 0 COMMENT '商品数量',
  `return_qty` decimal(18,4) DEFAULT 0 COMMENT '退仓数量',
  `return_amount` decimal(18,4) DEFAULT 0 COMMENT '退仓金额',
  `return_order_count` int(11) DEFAULT 0 COMMENT '生成退仓单数量',
  `return_order_nos`  varchar(3000) COMMENT '退仓单号列表，逗号隔开',
  `process_status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '处理状态：0-待处理 1-处理中 2-处理成功 3-处理失败',
  `error_msg`  text COMMENT '错误信息',
  `process_time` datetime COMMENT '处理时间',
  `extend`  text COMMENT '扩展字段',
  `version` int(11) NOT NULL DEFAULT 1 COMMENT '版本号',
  `status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '状态(-1删除，0正常)',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_update` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_task_code` (`task_code`),
  KEY `idx_store_code` (`store_code`),
  KEY `idx_process_status` (`process_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='不经营品退仓处理明细表';

-- 3. 错误记录表：记录处理失败的明细
CREATE TABLE `unmanage_goods_return_error_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `task_id` bigint(20) NOT NULL COMMENT '任务ID',
  `task_code` varchar(64) NOT NULL COMMENT '任务编码',
  `store_org_id` bigint(20) COMMENT '门店机构ID',
  `store_code` varchar(64) COMMENT '门店编码',
  `goods_no` varchar(64) COMMENT '商品编码',
  `batch_no` varchar(128) COMMENT '批号',
  `error_type` tinyint(4) NOT NULL COMMENT '错误类型：1-门店权限 2-商品不存在 3-库存不足 4-商品不可退 5-系统异常',
  `error_msg`text NOT NULL COMMENT '错误信息',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_task_code` (`task_code`),
  KEY `idx_store_code` (`store_code`),
  KEY `idx_error_type` (`error_type`),
  KEY `idx_gmt_create` (`gmt_create`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='不经营品退仓错误记录表';

-- ========================================
-- 数据流向说明
-- ========================================

-- 1. 任务创建流程
-- 用户发起 -> 创建 unmanage_goods_return_task 记录 -> 异步处理

-- 2. 异步处理流程（参考导入逻辑）
-- 按门店分组 -> 筛选不经营商品 -> 计算退仓数量 -> 直接写入退仓执行单表

-- 3. 最终数据落地
-- iscm_store_return_execute_order_main (主表)
-- iscm_store_return_execute_order_detail (明细表)

-- 4. 字段映射关系
-- return_type = 3 (ReturnTypeEnum.DEFECTIVE)
-- return_business_type = 8 (ReturnWarehouseBusinessTypeEnum.UNMANAGE_GOODS_RETURN)
-- stock_type = "不经营品"
-- warehouse_code = null (退仓接收仓库为空)
-- plan_return_amount = 0 (计划退仓金额为0)

-- ========================================
-- 处理逻辑设计
-- ========================================

-- 1. Redis缓存Key设计（参考导入逻辑）
-- ISCM_UNMANAGE_GOODS_RETURN_CACHE + userId

-- 2. 进度跟踪结构
-- {
--   "processCount": 100,        // 总处理数量（门店数量）
--   "successCount": 80,         // 成功处理数量
--   "errorCount": 20,           // 错误处理数量
--   "processFinished": false,   // 是否处理完成
--   "errorList": []            // 错误列表
-- }

-- 3. 异步处理步骤
-- Step1: 按门店分组处理
-- Step2: 每个门店独立处理（参考 importByStore 逻辑）
-- Step3: 筛选不经营商品
-- Step4: 查询库存信息
-- Step5: 计算退仓数量
-- Step6: 生成退仓执行单
-- Step7: 更新处理状态

-- ========================================
-- 核心业务逻辑
-- ========================================

-- 1. 不经营商品筛选
-- SELECT goods_no FROM store_goods_contents 
-- WHERE store_org_id = ? AND manage_status = 4

-- 2. 可退商品验证（调用商品中台）
-- 退仓说明 = "可退仓" AND 返厂说明 = "无条件可退"

-- 3. 库存查询（调用库存中台）
-- 获取门店商品的所有渠道批号可用库存

-- 4. 效期库存判断
-- 根据平台配置参数和商品有效期判断

-- 5. 保留数量计算
-- 季节品：后期3个月销售数据
-- 非季节品：近180天销售数据

-- 6. 退仓数量计算
-- 退仓数量 = 非效期库存 - 保留数量

-- 7. 批号分配
-- 按失效天数倒序分配退仓数量

-- ========================================
-- 索引优化
-- ========================================

-- 主表复合索引
ALTER TABLE `unmanage_goods_return_task` 
ADD INDEX `idx_platform_status_create` (`platform_org_id`, `task_status`, `gmt_create`);

-- 处理明细表复合索引
ALTER TABLE `unmanage_goods_return_process_detail` 
ADD INDEX `idx_task_process_time` (`task_id`, `process_status`, `process_time`);

-- 错误记录表复合索引
ALTER TABLE `unmanage_goods_return_error_log` 
ADD INDEX `idx_task_error_create` (`task_id`, `error_type`, `gmt_create`);

-- ========================================
-- 数据清理策略
-- ========================================

-- 保留3个月的历史数据，定期清理
-- DELETE FROM unmanage_goods_return_error_log WHERE gmt_create < DATE_SUB(NOW(), INTERVAL 3 MONTH);
-- DELETE FROM unmanage_goods_return_process_detail WHERE gmt_create < DATE_SUB(NOW(), INTERVAL 3 MONTH);
-- DELETE FROM unmanage_goods_return_task WHERE gmt_create < DATE_SUB(NOW(), INTERVAL 3 MONTH);

-- ========================================
-- 监控指标
-- ========================================

-- 1. 任务处理成功率
-- SELECT 
--   COUNT(CASE WHEN task_status = 2 THEN 1 END) / COUNT(*) * 100 as success_rate
-- FROM unmanage_goods_return_task 
-- WHERE gmt_create >= DATE_SUB(NOW(), INTERVAL 1 DAY);

-- 2. 平均处理时长
-- SELECT 
--   AVG(TIMESTAMPDIFF(SECOND, start_time, end_time)) as avg_process_seconds
-- FROM unmanage_goods_return_task 
-- WHERE task_status = 2 AND gmt_create >= DATE_SUB(NOW(), INTERVAL 1 DAY);

-- 3. 错误类型分布
-- SELECT 
--   error_type, COUNT(*) as error_count
-- FROM unmanage_goods_return_error_log 
-- WHERE gmt_create >= DATE_SUB(NOW(), INTERVAL 1 DAY)
-- GROUP BY error_type;

-- ========================================
-- 与现有系统集成点
-- ========================================

-- 1. 权限校验：复用 permissionService.listUserDataScopeTreesByOrgIdAndTypes
-- 2. 门店信息：复用 permissionService.getOrgBaseCacheBySapCode
-- 3. 商品信息：复用 forestService.batchFindSpuProperty
-- 4. 库存查询：复用 stockService.batchFindStockGoodsBatchCodeSimpleInfo
-- 5. 仓库信息：复用 purchaseService.getBatchWarehouseList
-- 6. 退仓单号生成：复用现有的退仓单号生成逻辑
-- 7. 不良品服务：集成现有的不良品记录服务接口

-- ========================================
-- 配置参数
-- ========================================

-- 1. 效期判断配置（存储在系统参数表）
-- unmanage.goods.return.expire.long.term.days = 300    # 长期有效期阈值
-- unmanage.goods.return.expire.medium.term.days = 120  # 中期有效期阈值  
-- unmanage.goods.return.expire.short.term.days = 60    # 短期有效期阈值

-- 2. 销售数据配置
-- unmanage.goods.return.seasonal.sales.days = 90       # 季节品销售数据天数
-- unmanage.goods.return.normal.sales.days = 180        # 非季节品销售数据天数

-- 3. 处理性能配置
-- unmanage.goods.return.batch.store.size = 50          # 单次处理门店数量
-- unmanage.goods.return.batch.goods.size = 100         # 单次处理商品数量
-- unmanage.goods.return.task.timeout = 3600            # 任务超时时间（秒）
