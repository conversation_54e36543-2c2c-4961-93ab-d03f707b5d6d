一、退仓操作入口与初始配置流程
https://app.mockplus.cn/app/share-a2ba821f2330519a89b041c6db0b9bfdshare-YgKLejUWI7/comment/8mYnLaVWP/aquxEVkPKfQ

**入口与页面进入：用户通过点击指定(不经营品退仓计算)按钮进入退仓操作页面，页面核心配置步骤需按顺序完成：**
平台选择：首先必填单选 “区域平台”，为后续组织范围与数据筛选奠定基础。
门店列表获取：根据选中的平台参数，自动调取对应门店列表；门店范围可通过两种方式确定：
方式一：从参数中获取，即按企业范围筛选，同时排除 “门店黑名单”、增加 “门店白名单”。
方式二：按页面输入确认，不使用参数过滤，直接以用户手动选中的门店为准。
商品列表确定：支持用户选中配置的商品列表或自定义输入，商品范围规则如下：
默认：选择全部不经营商品，但需排除参数中的 “商品黑名单”。
自定义：用户可输入商品编码（多个编码用逗号分隔，最多支持 1000 个），框定目标商品，且不排除商品黑名单。
组织范围选择：退仓组织以树状结构展示，支持选择任何节点，为必填项；默认按参数设置的企业范围计算，也可手动选择自定义组织范围。
计算发起与进度展示：点击【发起计算】按钮后，弹出计算进度框，进度统计以 “门店个数” 为依据，直观显示当前处理进度。
二、退仓核心逻辑（含不经营品退仓能力）
（一）门店 * 商品清单确定：筛选不经营可退商品
第一步：筛选不经营商品：查询门店经营目录表（store_goods_contents），筛选出 “经营状态（manageStatus）= 4” 的不经营商品列表。
第二步：筛选可退商品：根据 “门店所属法人公司 + 不经营商品编码” 查询商品中台，仅保留满足以下条件的商品：
退仓说明（rtreport）= “可退仓”
返厂说明（rereport）= “无条件可退”
（二）库存查询与分类
库存查询：针对上述筛选后的 “门店 * 商品” 清单，查询库存中台，获取全部门店商品的 “所有渠道批号可用库存”。
库存分类：将批号可用库存分为两大类：
效期库存：需结合平台配置参数判断（如距离失效日期的时间范围）。
非效期库存：不满足效期库存判断条件的剩余库存。
（三）效期库存判断规则
判断依据：以平台统一配置的参数为标准，相关效期信息优先从 MDM（主数据管理）获取（注：MDM 信息可能存在易用性问题，需进一步确认）。
具体逻辑：不同商品有不同总有效期（如 5 年、1 年），根据 “当前时间距离商品失效日期的剩余时间”，将商品归到对应时间范围判断是否为效期库存。
示例 1：5 年有效期的商品，若剩余失效时间为 6 个月，可能按 10 个月的判断标准归为效期库存。
示例 2：1 年有效期的商品，按专属短周期标准判断（如剩余 4 个月失效则归为效期库存）。
数据问题：库存中台返回的批号中，“生产日期” 字段可能不准确，仅 “有效期” 字段可用，需后续补充查找生产日期信息。
（四）非效期库存处理规则
保留数量计算：根据商品类型确定保留库存，数据均取自国辉推送的表格：
季节品：按 “后期三个月销售数据” 计算保留数量。
非季节品：按 “近 180 天销售数据” 计算保留数量（若表格中无对应数据，保留数量按 0 算）。
退仓数量与批号拆分：
退仓数量 = 非效期库存总数量 - 保留数量（如总库存 10 个、保留 3 个，则退 7 个）。
批号拆分逻辑：优先按 “失效天数倒序” 拆分（失效天数越大的批号先退），或保留效期较差的库存、退效期较好的库存，直至退满需退数量（如某批号库存 7 个，直接全退；若某批号库存 5 个，退完后剩余 2 个从下一批号中扣除）。
（五）预退仓单创建与信息填充
退仓单基础信息：
退仓接收仓库：空
退仓类型：不良品退仓
退仓明细信息：
计划退仓数量：等于该批号的可用库存量
计划退仓金额：0
数据版本、数据来源 ID：留空
不良库存类型：不经营品
采购经理处理意见：留空
处理数量：全部（即该批号可用库存全额）
状态：未下发
三、系统技术处理与性能保障
异步处理机制：为避免 IS（集成服务）系统过载，采用 “ 线程池” 模式，将库存计算、退仓单生成等操作放在异步任务中循环执行，降低实时处理压力。
数据填充与复用：调用已有 service 接口填充退仓单数据，可复制历史字段配置并补充新增字段（如 “不良库存类型 = 不经营品”），减少重复开发。


