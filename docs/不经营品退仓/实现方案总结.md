# 不经营品退仓实现方案总结

## 1. 方案概述

### 1.1 设计原则
基于现有 `importReturnWarehouseExec` 逻辑重新设计，遵循以下原则：
- **复用现有流程**：最大化复用现有退仓执行单的处理逻辑
- **简化表结构**：只保留必要的任务跟踪和进度管理
- **统一数据流**：直接写入现有退仓执行单表，避免数据冗余
- **异步处理**：使用Redis缓存进度，按门店分组处理

### 1.2 核心改进
1. **枚举扩展**：新增 `ReturnWarehouseBusinessTypeEnum.UNMANAGE_GOODS_RETURN`
2. **表结构简化**：从4张表简化为3张表
3. **处理流程优化**：参考导入逻辑，直接生成退仓执行单
4. **进度跟踪**：使用Redis缓存，实时展示处理进度

## 2. 枚举定义

### 2.1 新增枚举项
```java
// ReturnWarehouseBusinessTypeEnum 新增
UNMANAGE_GOODS_RETURN((byte)8, "不经营品退仓"),

// ReturnTypeEnum 复用现有
DEFECTIVE(3, "不良品退仓"), // 不经营品属于不良品的一种
```

### 2.2 固定值配置
```java
// 退仓执行单固定值
return_type = 3                    // ReturnTypeEnum.DEFECTIVE
return_business_type = 8           // ReturnWarehouseBusinessTypeEnum.UNMANAGE_GOODS_RETURN
stock_type = "不经营品"            // 库存类型
warehouse_code = null              // 退仓接收仓库为空
plan_return_amount = 0             // 计划退仓金额为0
process_status = 0                 // 状态=未下发
```

## 3. 表结构设计

### 3.1 主表：unmanage_goods_return_task
**用途**：记录任务基本信息和整体进度
**关键字段**：
- `task_code`：任务编码（唯一）
- `org_type`：机构类型（1-参数配置 2-自选门店）
- `org_ids`：选中机构ID集合
- `goods_type`：商品类型（1-全部不经营 2-自定义）
- `goods_nos`：选中商品编码集合
- `task_status`：任务状态（1-计算中 2-计算完成 3-处理失败）
- `process_count/success_count/error_count`：处理统计

### 3.2 处理明细表：unmanage_goods_return_process_detail
**用途**：记录每个门店的处理结果
**关键字段**：
- `store_org_id`：门店机构ID
- `goods_count`：商品数量
- `return_qty/return_amount`：退仓数量和金额
- `return_order_nos`：生成的退仓单号列表
- `process_status`：处理状态

### 3.3 错误记录表：unmanage_goods_return_error_log
**用途**：记录处理失败的详细信息
**关键字段**：
- `error_type`：错误类型（1-门店权限 2-商品不存在 3-库存不足 4-商品不可退 5-系统异常）
- `error_msg`：错误信息
- `error_detail`：错误详情

## 4. 处理流程设计

### 4.1 整体流程
```
用户发起 → 创建任务 → 异步处理 → 按门店分组 → 筛选商品 → 计算退仓 → 生成退仓单 → 完成
```

### 4.2 详细步骤

#### Step 1: 任务创建
1. 参数验证（平台权限、门店权限、商品权限）
2. 创建任务记录（`unmanage_goods_return_task`）
3. 初始化Redis缓存进度
4. 提交异步处理任务

#### Step 2: 异步处理（参考importReturnWarehouseExec）
1. **门店分组**：按门店维度分组处理
2. **权限校验**：验证门店操作权限
3. **商品筛选**：
   - 查询门店经营目录（`manage_status = 4`）
   - 排除商品黑名单
   - 调用商品中台验证可退条件
4. **库存计算**：
   - 调用库存中台获取批号库存
   - 分类效期/非效期库存
   - 计算保留数量（销售数据）
   - 计算退仓数量
5. **生成退仓单**：
   - 直接写入 `iscm_store_return_execute_order_main`
   - 直接写入 `iscm_store_return_execute_order_detail`
6. **更新进度**：更新Redis缓存和数据库记录

#### Step 3: 进度跟踪
1. **Redis缓存结构**：
```json
{
  "processCount": 100,
  "successCount": 80,
  "errorCount": 20,
  "processFinished": false,
  "errorList": []
}
```
2. **实时更新**：每处理完一个门店更新一次
3. **前端轮询**：前端定时查询进度展示

## 5. 核心算法

### 5.1 效期库存判断
```java
public boolean isExpireStock(BatchInfo batch, GoodsInfo goods, PlatformConfig config) {
    int totalValidityYears = goods.getTotalValidityYears();
    long remainingDays = ChronoUnit.DAYS.between(LocalDate.now(), batch.getExpireDate());
    
    int threshold;
    if (totalValidityYears >= 5) {
        threshold = config.getLongTermExpireThresholdDays(); // 300天
    } else if (totalValidityYears >= 1) {
        threshold = config.getMediumTermExpireThresholdDays(); // 120天
    } else {
        threshold = config.getShortTermExpireThresholdDays(); // 60天
    }
    
    return remainingDays <= threshold;
}
```

### 5.2 保留数量计算
```java
public BigDecimal calculateReserveQuantity(Long storeId, String goodsNo, boolean isSeasonal) {
    if (isSeasonal) {
        // 季节品：后期3个月销售数据
        return salesDataService.getSeasonalSalesData(storeId, goodsNo, 90);
    } else {
        // 非季节品：近180天销售数据
        return salesDataService.getNormalSalesData(storeId, goodsNo, 180);
    }
}
```

### 5.3 批号分配算法
```java
public List<BatchReturnInfo> allocateReturnQuantity(List<BatchInfo> batchList, BigDecimal totalReturnQty) {
    // 按失效天数倒序排序（失效天数大的先退）
    batchList.sort((b1, b2) -> b2.getExpireDays().compareTo(b1.getExpireDays()));
    
    List<BatchReturnInfo> result = new ArrayList<>();
    BigDecimal remainingQty = totalReturnQty;
    
    for (BatchInfo batch : batchList) {
        if (remainingQty.compareTo(BigDecimal.ZERO) <= 0) break;
        
        BigDecimal batchReturnQty = remainingQty.compareTo(batch.getAvailableQty()) >= 0 
            ? batch.getAvailableQty() 
            : remainingQty;
            
        result.add(new BatchReturnInfo(batch.getBatchNo(), batchReturnQty));
        remainingQty = remainingQty.subtract(batchReturnQty);
    }
    
    return result;
}
```

## 6. 关键接口设计

### 6.1 任务创建接口
```java
@PostMapping("/create")
public Result<String> createUnmanageGoodsReturnTask(@RequestBody CreateTaskRequest request) {
    // 1. 参数验证
    validateRequest(request);
    
    // 2. 权限校验
    checkPermission(request.getPlatformOrgId(), request.getOrgIds());
    
    // 3. 创建任务
    String taskCode = generateTaskCode();
    UnmanageGoodsReturnTask task = createTask(request, taskCode);
    
    // 4. 初始化进度缓存
    initProgressCache(taskCode, getCurrentUserId());
    
    // 5. 异步处理
    unmanageGoodsReturnExecutor.execute(() -> processTask(task));
    
    return Result.success(taskCode);
}
```

### 6.2 进度查询接口
```java
@GetMapping("/progress/{taskCode}")
public Result<TaskProgressResponse> getTaskProgress(@PathVariable String taskCode) {
    String cacheKey = ISCM_UNMANAGE_GOODS_RETURN_CACHE + getCurrentUserId();
    TaskProgressResponse progress = redisTemplate.opsForValue().get(cacheKey);
    
    if (progress == null) {
        // 从数据库查询
        progress = buildProgressFromDatabase(taskCode);
    }
    
    return Result.success(progress);
}
```

### 6.3 结果查询接口
```java
@GetMapping("/result/{taskCode}")
public Result<PageResult<ProcessDetailResponse>> getTaskResult(
    @PathVariable String taskCode,
    @RequestParam(defaultValue = "1") int pageNum,
    @RequestParam(defaultValue = "20") int pageSize) {
    
    PageResult<ProcessDetailResponse> result = processDetailService.queryByTaskCode(
        taskCode, pageNum, pageSize);
    
    return Result.success(result);
}
```

## 7. 配置参数

### 7.1 系统参数配置
```yaml
unmanage-goods-return:
  # 效期判断配置
  expire-config:
    long-term-threshold: 300      # 长期有效期阈值（天）
    medium-term-threshold: 120    # 中期有效期阈值（天）
    short-term-threshold: 60      # 短期有效期阈值（天）
  
  # 销售数据配置
  sales-config:
    seasonal-days: 90             # 季节品销售数据天数
    normal-days: 180              # 非季节品销售数据天数
  
  # 性能配置
  performance:
    batch-store-size: 50          # 单次处理门店数量
    batch-goods-size: 100         # 单次处理商品数量
    task-timeout: 3600            # 任务超时时间（秒）
  
  # Redis配置
  redis:
    cache-prefix: "ISCM_UNMANAGE_GOODS_RETURN_CACHE"
    cache-timeout: 7200           # 缓存超时时间（秒）
```

## 8. 监控和运维

### 8.1 关键监控指标
1. **任务处理成功率**：成功任务数 / 总任务数
2. **平均处理时长**：任务开始到结束的平均时间
3. **错误类型分布**：各种错误类型的统计
4. **系统资源使用**：CPU、内存、数据库连接等

### 8.2 告警规则
1. **任务失败率 > 10%**：发送告警
2. **处理时长 > 30分钟**：发送告警
3. **系统异常错误 > 5个/小时**：发送告警

## 9. 部署方案

### 9.1 数据库变更
1. 执行表结构创建SQL
2. 添加新的枚举配置
3. 创建必要的索引

### 9.2 应用部署
1. 更新枚举类定义
2. 部署新的服务代码
3. 配置系统参数
4. 验证功能正常

### 9.3 回滚方案
1. 保留原有表结构
2. 代码支持开关控制
3. 数据可回滚清理

## 10. 测试策略

### 10.1 功能测试
- 各种参数组合的任务创建
- 异步处理流程验证
- 错误处理和重试机制
- 进度跟踪准确性

### 10.2 性能测试
- 大量门店和商品的处理能力
- 并发任务处理能力
- 系统资源使用情况

### 10.3 集成测试
- 与现有退仓执行单流程集成
- 与不良品记录服务集成
- 权限系统集成验证

## 11. 风险控制

### 11.1 数据安全
- 操作权限严格控制
- 重要操作记录审计日志
- 数据变更可追溯

### 11.2 业务风险
- 多层数据校验
- 异常情况处理
- 人工干预机制

### 11.3 技术风险
- 完善的异常处理
- 系统降级策略
- 监控告警机制

## 12. 总结

本方案基于现有 `importReturnWarehouseExec` 逻辑重新设计，具有以下优势：

1. **复用性强**：最大化复用现有代码和流程
2. **结构简化**：表结构从4张简化为3张，减少维护成本
3. **性能优化**：异步处理、分片处理、批量操作
4. **可扩展性**：预留扩展字段，支持未来功能扩展
5. **监控完善**：完整的进度跟踪和错误处理机制

该方案可以作为开发实施的技术指导文档，确保项目按时高质量交付。
