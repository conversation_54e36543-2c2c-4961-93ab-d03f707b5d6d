# ===================================================================
# Spring Boot configuration for the "dev" profile.
#
# This configuration overrides the application.yml file.
#
# More information on profiles: http://www.jhipster.tech/profiles/
# More information on configuration properties: http://www.jhipster.tech/common-application-properties/
# ===================================================================

# ===================================================================
# Standard Spring Boot properties.
# Full reference is available at:
# http://docs.spring.io/spring-boot/docs/current/reference/html/common-application-properties.html
# ===================================================================

logging:
    level:
        ROOT: DEBUG
        com.cowell.iscm: DEBUG
        io.github.jhipster: DEBUG

eureka:
    instance:
        prefer-ip-address: true
    client:
        service-url:
            defaultZone: http://admin:${jhipster.registry.password}@************:8761/eureka/

com:
    cowell:
        metric:
            undertow:
                enabled: true


spring:
    profiles:
        active: dev
        include: swagger,no-liquibase
    devtools:
        restart:
            enabled: true
        livereload:
            enabled: false # we use gulp + BrowserSync for livereload
    jackson:
        serialization:
            indent_output: true
    ds0:
        type: com.alibaba.druid.pool.DruidDataSource
        driver-class-name: com.mysql.jdbc.Driver
        url: ************************************************************************************************************************************************************************************************************************************************
        username: scrmrw
        password: 123qaz!@#
        initial-size: 5
        min-idle: 1
        max-active: 5
        max-wait: 60000
        time-between-eviction-runs-millis: 60000
        min-evictable-idle-time-millis: 30000
        max-evictable-idle-time-millis: 60000
        jmx-enabled: true
        validation-query: select 1
        test-on-borrow: true
        true test-while-idle: true

    datasource:
        type: com.alibaba.druid.pool.DruidDataSource
        driver-class-name: com.mysql.jdbc.Driver
        url: ****************************************************************************************************************************************************************************
        username: scrmrw
        password: 123qaz!@#
        initial-size: 5
        min-idle: 1
        max-active: 5
        max-wait: 60000
        time-between-eviction-runs-millis: 60000
        min-evictable-idle-time-millis: 30000
        max-evictable-idle-time-millis: 60000
        jmx-enabled: true

    datasource-Tidb:
        type: com.alibaba.druid.pool.DruidDataSource
        driver-class-name: com.mysql.jdbc.Driver
        url: *****************************************************************************************************************************************************************
        username: supply-chain
        password: YWluCg1#=
        initial-size: 5
        min-idle: 1
        max-active: 5
        max-wait: 60000
        validationQuery: SELECT 1
        validationQueryTimeout: 10000
        testWhileIdle: true
        testOnBorrow: true
        testOnReturn: false
        time-between-eviction-runs-millis: 60000
        min-evictable-idle-time-millis: 30000
        max-evictable-idle-time-millis: 60000

    datasource-Scib:
        type: com.alibaba.druid.pool.DruidDataSource
        driver-class-name: com.mysql.jdbc.Driver
        url: *************************************************************************************************************************
        username: supply-chain-test
        password: LXRlc3QK
        initial-size: 2
        min-idle: 1
        max-active: 10
        max-wait: 60000
        validation-query: select 1
        validation-query-timeout: 10000
        test-while-idle: true
        test-on-borrow: false
        test-on-return: false
        pool-prepared-statements: true
        max-pool-prepared-statement-per-connection-size: 50
        time-between-eviction-runs-millis: 60000
        min-evictable-idle-time-millis: 300000
        max-evictable-idle-time-millis: 600000
        filters: stat

    datasource-hana:
        type: com.alibaba.druid.pool.DruidDataSource
        driver-class-name: com.sap.db.jdbc.Driver
        url: ***************************************************************
        username: GJ_MDTC
        password: Gaoji_001#
        initial-size: 2
        min-idle: 1
        max-active: 10
        max-wait: 60000
        validation-query: select 1
        validation-query-timeout: 10000
        test-while-idle: true
        test-on-borrow: false
        test-on-return: false
        pool-prepared-statements: true
        max-pool-prepared-statement-per-connection-size: 50
        time-between-eviction-runs-millis: 60000
        min-evictable-idle-time-millis: 300000
        max-evictable-idle-time-millis: 600000
        filters: stat

    mail:
        host: localhost
        port: 25
        username:
        password:
    messages:
        cache-seconds: 1
    thymeleaf:
        cache: false
    zipkin: # Use the "zipkin" Maven profile to have the Spring Cloud Zipkin dependencies
        base-url: http://localhost:9411
        enabled: false
        locator:
            discovery:
                enabled: true
    rocketmq:
        erpsaas:
            name-server-address: ***********:9876;***********:9876
            sync-pos-allot-suggest-approve-consumer:
                topic: ERP_CALLBACK_ISCM_SERVICE_TOPIC_DEV
                group: ERP_ISCM_ALLOT_SUGGEST_APPROVE_SERVICE_GROUP_DEV
                tag: ERP_CALLBACK_ISCM_ALLOT_SUGGEST_APPROVE_SERVICE_TAG_DEV
                maxReconsumeTimes: 3
                consumeThreadMax: 1
                consumeThreadMin: 1
            sync-bdp-allot-suggest-approve-consumer:
                topic: ERP_CALLBACK_ISCM_ALLOT_SUGGEST_APPROVE_BY_BDP_TOPIC_DEV
                group: ERP_ISCM_ALLOT_SUGGEST_APPROVE_BY_BDP_SERVICE_GROUP_DEV
                tag: ERP_CALLBACK_ISCM_ALLOT_SUGGEST_APPROVE_BY_BDP_SERVICE_TAG_DEV
                maxReconsumeTimes: 3
                consumeThreadMax: 1
                consumeThreadMin: 1

        iscm:
            # NameServer地址
            name-server-address: ***********:9876;***********:9876
            # 创建登记单自产自销
            register-create-topic: REGISTER_CREATE_TOPIC_DEV
            register-create-topic-group: REGISTER_CREATE_TOPIC_GROUP_DEV
            # 审批建议自产自销
            apporve-suggest-topic: APPROVE_SUGGEST_TOPIC_DEV
            apporve-suggest-topic-group: APPROVE_SUGGEST_TOPIC_GROUP_DEV

            # 同步推式补货kpi数据自产自销
            push-replenishment-kpi-topic: PUSH_REPLENISHMENT_KPI_TOPIC_DEV
            push-replenishment-kpi-topic-group: PUSH_REPLENISHMENT_KPI_TOPIC_GROUP_DEV

            # 同步推式补货kpi汇总数据自产自销
            collect-replenishment-kpi-topic: COLLECT_REPLENISHMENT_KPI_TOPIC_DEV
            collect-replenishment-kpi-topic-group: COLLECT_REPLENISHMENT_KPI_TOPIC_GROUP_DEV

            # 创建自动登记单自产自销
            auto-register-create-topic: AUTO_REGISTER_CREATE_TOPIC_DEV
            auto-register-create-topic-group: AUTO_REGISTER_CREATE_TOPIC_GROUP_DEV

            # 创建近效期临时登记单自产自销
            gen-near-expiry-register-temp-topic: GEN_NEAR_EXPIRY_REGISTER_TEMP_TOPIC_DEV
            gen-near-expiry-register-temp-topic-group: GEN_NEAR_EXPIRY_REGISTER_TEMP_TOPIC_GROUP_DEV

            # 自主调拨导入自产自销
            autonomy-allot-import-topic: AUTONOMY_ALLOT_IMPORT_TOPIC_DEV
            autonomy-allot-import-topic-group: AUTONOMY_ALLOT_IMPORT_TOPIC_GROUP_DEV

            # 登记近效期临时登记单自产自销
            register-expiry-temp-topic: REGISTER_EXPIRY_TEMP_TOPIC_DEV
            register-expiry-temp-topic-group: REGISTER_EXPIRY_TEMP_TOPIC_GROUP_DEV

            # 自主调拨下发门店
            autonomy-allot-sendPos-topic: AUTONOMY_ALLOT_SENDPOS_TOPIC_DEV
            autonomy-allot-sendPos-topic-group: AUTONOMY_ALLOT_SENDPOS_TOPIC_GROUP_DEV

            # 创建退仓单自产自销
            return-warehouse-create-topic: RETURN_WAREHOUSE_CREATE_TOPIC_DEV
            return-warehouse-create-topic-group: RETURN_WAREHOUSE_CREATE_TOPIC_GROUP_DEV

            # 创建退仓单明细自产自销
            return-warehouse-detail-create-topic: RETURN_WAREHOUSE_DETAIL_CREATE_TOPIC_DEV
            return-warehouse-detail-create-topic-group: RETURN_WAREHOUSE_DETAIL_CREATE_TOPIC_GROUP_DEV

            # 创建退仓执行单自产自销
            return-warehouse-exec-topic: RETURN_WAREHOUSE_EXEC_TOPIC_DEV
            return-warehouse-exec-topic-group: RETURN_WAREHOUSE_EXEC_TOPIC_GROUP_DEV

            # 复算自产自销
            recalculation-store-apply-topic: RECALCULATION_STORE_APPLY_TOPIC_DEV
            recalculation-store-apply-topic-group: RECALCULATION_STORE_APPLY_TOPIC_GROUP_DEV

            # 日均销自产自销
            deal-bdp-avg-sales-topic: DEAL_BDP_AVG_SALES_TOPIC_DEV
            deal-bdp-avg-sales-topic-group: DEAL_BDP_AVG_SALES_TOPIC_GROUP_DEV

            # 断货调拨自产自销
            suggest-approve-outofstock-topic: SUGGEST_APPROVE_OUTOFSTOCK_TOPIC_DEV
            suggest-approve-outofstock-topic-group: SUGGEST_APPROVE_OUTOFSTOCK_TOPIC_GROUP_DEV

            # 控制塔自产自销
            control-tower-warn-topic: CONTROL_TOWER_WARN_TOPIC_DEV
            control-tower-warn-topic-group: CONTROL_TOWER_WARN_TOPIC_GROUP_DEV

        scib:
            # NameServer地址
            name-server-address: ***********:9876;***********:9876
            # 季节品更新一店一目最小陈列量
            season-goods:
                topic: SEASON_GOODS_TOPIC_DEV
                group: SEASON_GOODS_GROUP_DEV
            #同步小程序状态变更
            topic:
                order-notify-oms: ORDER_CHANGE_SYNC_OMS_DEV
                order-notify-oms-group: ORDER_CHANGE_SYNC_OMS_GROUP_DEV

    cloud:
        stream:
            kafka:
                binder:
                    brokers: ************:9092,************:9092,************:9092
                    zkNodes: ************:2181,************:2181,************:2181/kafka-elk
            bindings:
                sleuth:      #这里用stream给我们提供的默认output，后面会讲到自定义output
                    destination: sleuth
                    content-type: application/json
                    producer:
                        headerMode: raw
    sleuth:
        stream:
            enabled: false


liquibase:
    contexts: dev

# ===================================================================
# To enable SSL, generate a certificate using:
# keytool -genkey -alias iscm -storetype PKCS12 -keyalg RSA -keysize 2048 -keystore keystore.p12 -validity 3650
#
# You can also use Let's Encrypt:
# https://maximilian-boehm.com/hp2121/Create-a-Java-Keystore-JKS-from-Let-s-Encrypt-Certificates.htm
#
# Then, modify the server.ssl properties so your "server" configuration looks like:
#
# server:
#    port: 8443
#    ssl:
#        key-store: keystore.p12
#        key-store-password: <your-password>
#        key-store-type: PKCS12
#        key-alias: iscm
# ===================================================================
server:
    port: 9062

# ===================================================================
# JHipster specific properties
#
# Full reference is available at: http://www.jhipster.tech/common-application-properties/
# ===================================================================

jhipster:
    http:
        version: V_1_1 # To use HTTP/2 you will need SSL support (see above the "server.ssl" configuration)
        # CORS is disabled by default on microservices, as you should access them through a gateway.
        # If you want to enable it, please uncomment the configuration below.
        # cors:
        # allowed-origins: "*"
        # allowed-methods: "*"
        # allowed-headers: "*"
        # exposed-headers: "Authorization,Link,X-Total-Count"
        # allow-credentials: true
        # max-age: 1800
    security:
        client-authorization:
            access-token-uri: http://uaa/oauth/token
            token-service-id: uaa
            client-id: internal
            client-secret: internal
    mail: # specific JHipster mail property, for standard properties see MailProperties
        from: iscm@localhost
        base-url: http://127.0.0.1:9060
    metrics: # DropWizard Metrics configuration, used by MetricsConfiguration
        jmx.enabled: true
        graphite: # Use the "graphite" Maven profile to have the Graphite dependencies
            enabled: false
            host: localhost
            port: 2003
            prefix: iscm
        prometheus: # Use the "prometheus" Maven profile to have the Prometheus dependencies
            enabled: false
            endpoint: /prometheusMetrics
        logs: # Reports Dropwizard metrics in the logs
            enabled: false
            report-frequency: 60 # in seconds
    logging:
        logstash: # Forward logs to logstash over a socket, used by LoggingConfiguration
            enabled: false
            host: localhost
            port: 5000
            queue-size: 512
        spectator-metrics: # Reports Spectator Circuit Breaker metrics in the logs
            enabled: false
            # edit spring.metrics.export.delay-millis to set report frequency

oauth2:
    signature-verification:
        public-key-endpoint-uri: http://uaa/oauth/token_key
        #ttl for public keys to verify JWT tokens (in ms)
        ttl: 3600000
        #max. rate at which public keys will be fetched (in ms)
        public-key-refresh-rate-limit: 10000
    web-client-configuration:
        #keep in sync with UAA configuration
        client-id: web_app
        secret: changeit

#xxl-job
xxl:
    job:
        admin:
            addresses: http://xxl-job-admin/xxl-job-admin
        executor:
            ip:
            port: 9999
            appname: iscm
            logretentiondays: -1
            logpath: /data/applogs/xxl-job/jobhandler
        accessToken:

#Redis
spring.redisson:
    address[0]: redis://common_microservice_redis-01_test.cowelltech.com:6379
    read-mode: MASTER

# ===================================================================
# Application specific properties
# Add your own application properties here, see the ApplicationProperties class
# to have type-safe configuration, like in the JHipsterProperties above
#
# More documentation is available at:
# http://www.jhipster.tech/common-application-properties/
# ===================================================================

application:



#本地开发联调标识
local:
    dev:
        flag: true

#人员数据权限 b2b resourceID 测试475  预发265  线上465; srm resourceiId 测试870  预发24  线上24
permission:
    resourceid: 475
    srm-resourceid: 870
    zhi-resourceid: 127
    srm-sup-resourceid: 29

cowell:
    common:
        securityaudit:
            audit: false
apollo:
    meta: http://apollo
env.active: dev


erpsaas.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
erpsaas.ribbon.listOfServers: http://**********:10049
ai-bear.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
ai-bear.ribbon.listOfServers: http://**********4:10000
ai-common-business-ner.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
ai-common-business-ner.ribbon.listOfServers: http://**********4:10001
ai-control.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
ai-control.ribbon.listOfServers: http://**********4:10002
ai-data-compass.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
ai-data-compass.ribbon.listOfServers: http://**********4:10003
ai-department-intent.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
ai-department-intent.ribbon.listOfServers: http://**********4:10004
ai-dialog-control.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
ai-dialog-control.ribbon.listOfServers: http://**********4:10005
ai-dialog-h5.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
ai-dialog-h5.ribbon.listOfServers: http://**********4:10006
ai-doctor.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
ai-doctor.ribbon.listOfServers: http://**********4:10007
ai-dtp.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
ai-dtp.ribbon.listOfServers: http://**********4:10008
ai-external-info-checker.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
ai-external-info-checker.ribbon.listOfServers: http://**********4:10009
ai-faq-asthma.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
ai-faq-asthma.ribbon.listOfServers: http://**********4:10010
ai-form-service.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
ai-form-service.ribbon.listOfServers: http://**********4:10011
ai-h5-dtp.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
ai-h5-dtp.ribbon.listOfServers: http://**********4:10012
ai-hunt.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
ai-hunt.ribbon.listOfServers: http://**********4:10013
ai-job-admin.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
ai-job-admin.ribbon.listOfServers: http://**********4:10014
ai-manager.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
ai-manager.ribbon.listOfServers: http://**********4:10015
ai-medical-ner.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
ai-medical-ner.ribbon.listOfServers: http://**********4:10016
ai-pay-center.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
ai-pay-center.ribbon.listOfServers: http://**********4:10017
ai-recommand-scenes.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
ai-recommand-scenes.ribbon.listOfServers: http://**********4:10018
ai-recommend-server.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
ai-recommend-server.ribbon.listOfServers: http://**********4:10019
ai-robot-manage.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
ai-robot-manage.ribbon.listOfServers: http://**********4:10020
ai-sand.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
ai-sand.ribbon.listOfServers: http://**********4:10021
ai-scenario-servcie.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
ai-scenario-servcie.ribbon.listOfServers: http://**********4:10022
ai-suffer-info-checker.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
ai-suffer-info-checker.ribbon.listOfServers: http://**********4:10023
ai-task-manager.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
ai-task-manager.ribbon.listOfServers: http://**********4:10024
ai-task-runner.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
ai-task-runner.ribbon.listOfServers: http://**********4:10025
app-system.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
app-system.ribbon.listOfServers: http://**********4:10026
assetscenter.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
assetscenter.ribbon.listOfServers: http://**********4:10027
b2b-system.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
b2b-system.ribbon.listOfServers: http://**********4:10028
bam.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
bam.ribbon.listOfServers: http://**********4:10029
bdp-application-es.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
bdp-application-es.ribbon.listOfServers: http://**********4:10029
bdp-ddp-queryengine.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
bdp-ddp-queryengine.ribbon.listOfServers: http://**********4:10030
bdp-ddp-recommend.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
bdp-ddp-recommend.ribbon.listOfServers: http://**********4:10031
benefit-app.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
benefit-app.ribbon.listOfServers: http://**********4:10032
biz-tool-console.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
biz-tool-console.ribbon.listOfServers: http://**********4:10033
business-system.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
business-system.ribbon.listOfServers: http://**********4:10034
cart.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
cart.ribbon.listOfServers: http://**********4:10035
cboard.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
cboard.ribbon.listOfServers: http://**********4:10036
chatbot-action-server.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
chatbot-action-server.ribbon.listOfServers: http://**********4:10037
chronicdisease.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
chronicdisease.ribbon.listOfServers: http://**********4:10038
cmall-system.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
cmall-system.ribbon.listOfServers: http://**********4:10039
config-server.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
config-server.ribbon.listOfServers: http://**********4:10040
config-server-cvp.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
config-server-cvp.ribbon.listOfServers: http://**********4:10041
conveyor.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
conveyor.ribbon.listOfServers: http://**********4:10042
cube.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
cube.ribbon.listOfServers: http://**********4:10043
digitalstore.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
digitalstore.ribbon.listOfServers: http://**********4:10044
doctor-react-system.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
doctor-react-system.ribbon.listOfServers: http://**********4:10045
doctor-transform-system.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
doctor-transform-system.ribbon.listOfServers: http://**********:10046
docusaurus.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
docusaurus.ribbon.listOfServers: http://**********:10047
elearning.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
elearning.ribbon.listOfServers: http://**********:10048
erp-saas.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
erp-saas.ribbon.listOfServers: http://**********:10049
etf-app.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
etf-app.ribbon.listOfServers: http://**********:10050
etfapp.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
etfapp.ribbon.listOfServers: http://**********:10051
express.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
express.ribbon.listOfServers: http://**********:10052
forest.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
forest.ribbon.listOfServers: http://**********:10053
fund.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
fund.ribbon.listOfServers: http://**********:10054
gaea.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
gaea.ribbon.listOfServers: http://**********:10055
galaxy.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
galaxy.ribbon.listOfServers: http://**********:10056
gateway.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
gateway.ribbon.listOfServers: http://**********:10057
gateway-retail.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
gateway-retail.ribbon.listOfServers: http://**********:10058
gjdocusaurus.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
gjdocusaurus.ribbon.listOfServers: http://**********:10059
gulosity.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
gulosity.ribbon.listOfServers: http://**********:10060
hades.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
hades.ribbon.listOfServers: http://**********:10061
health-b2c-zha.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
health-b2c-zha.ribbon.listOfServers: http://**********:10062
health-o2o.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
health-o2o.ribbon.listOfServers: http://**********:10063
health-o2o-h5.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
health-o2o-h5.ribbon.listOfServers: http://**********:10064
his-assist-tool.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
his-assist-tool.ribbon.listOfServers: http://**********:10065
his-basic-data.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
his-basic-data.ribbon.listOfServers: http://**********:10066
his-basic-intelligent.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
his-basic-intelligent.ribbon.listOfServers: http://**********:10067
his-biz-tool.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
his-biz-tool.ribbon.listOfServers: http://**********:10068
his-intelligent.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
his-intelligent.ribbon.listOfServers: http://**********:10069
his-order.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
his-order.ribbon.listOfServers: http://**********:10070
his-prescription.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
his-prescription.ribbon.listOfServers: http://**********:10071
his-search.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
his-search.ribbon.listOfServers: http://**********:10072
his-supervision.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
his-supervision.ribbon.listOfServers: http://**********:10073
hospital-app.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
hospital-app.ribbon.listOfServers: http://**********:10074
hotkey-dashboard.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
hotkey-dashboard.ribbon.listOfServers: http://**********:10075
hotkey-work.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
hotkey-work.ribbon.listOfServers: http://**********:10076
huijin-platform.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
huijin-platform.ribbon.listOfServers: http://**********:10077
i-hospital-h5.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
i-hospital-h5.ribbon.listOfServers: http://**********:10078
i-hospital-inquiry.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
i-hospital-inquiry.ribbon.listOfServers: http://**********:10079
iscm.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
iscm.ribbon.listOfServers: http://**********:10080
iscm-system.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
iscm-system.ribbon.listOfServers: http://**********:10081
itemcenter.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
itemcenter.ribbon.listOfServers: http://**********:10082
itemupload.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
itemupload.ribbon.listOfServers: http://**********:10083
keycenter.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
keycenter.ribbon.listOfServers: http://**********:10084
log-zz.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
log-zz.ribbon.listOfServers: http://**********:10085
marketing.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
marketing.ribbon.listOfServers: http://**********:10086
mdm-classification.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
mdm-classification.ribbon.listOfServers: http://**********:10087
membeapp-system.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
membeapp-system.ribbon.listOfServers: http://**********:10088
message.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
message.ribbon.listOfServers: http://**********:10089
message-bus.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
message-bus.ribbon.listOfServers: http://**********:10090
message-bus-console.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
message-bus-console.ribbon.listOfServers: http://**********3:10091
message-bus-sentinel.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
message-bus-sentinel.ribbon.listOfServers: http://**********3:10092
mm-ai-account.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
mm-ai-account.ribbon.listOfServers: http://**********3:10093
mm-ai-acitvity.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
mm-ai-acitvity.ribbon.listOfServers: http://**********3:10094
mm-ai-admin.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
mm-ai-admin.ribbon.listOfServers: http://**********3:10095
mm-ai-api.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
mm-ai-api.ribbon.listOfServers: http://**********3:10096
mm-ai-center.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
mm-ai-center.ribbon.listOfServers: http://**********3:10097
mm-ai-data.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
mm-ai-data.ribbon.listOfServers: http://**********3:10098
mm-ai-data-api.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
mm-ai-data-api.ribbon.listOfServers: http://**********3:10099
mm-ai-data-cms.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
mm-ai-data-cms.ribbon.listOfServers: http://**********3:10100
mm-ai-external.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
mm-ai-external.ribbon.listOfServers: http://**********3:10101
mm-ai-gateway.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
mm-ai-gateway.ribbon.listOfServers: http://**********3:10102
mm-ai-h5.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
mm-ai-h5.ribbon.listOfServers: http://**********3:10103
mm-ai-hospital.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
mm-ai-hospital.ribbon.listOfServers: http://**********3:10104
mm-ai-info.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
mm-ai-info.ribbon.listOfServers: http://**********3:10105
mm-ai-insurance.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
mm-ai-insurance.ribbon.listOfServers: http://**********3:10106
mm-ai-login.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
mm-ai-login.ribbon.listOfServers: http://**********3:10107
mm-ai-mall.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
mm-ai-mall.ribbon.listOfServers: http://**********3:10108
mm-ai-message.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
mm-ai-message.ribbon.listOfServers: http://**********3:10109
mm-ai-mobile.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
mm-ai-mobile.ribbon.listOfServers: http://**********3:10110
mm-ai-product.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
mm-ai-product.ribbon.listOfServers: http://**********3:10111
mm-ai-task.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
mm-ai-task.ribbon.listOfServers: http://**********3:10112
mm-base-info.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
mm-base-info.ribbon.listOfServers: http://**********3:10113
mm-chronicdisease.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
mm-chronicdisease.ribbon.listOfServers: http://**********3:10114
mm-digitalstore.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
mm-digitalstore.ribbon.listOfServers: http://**********3:10115
mm-emr.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
mm-emr.ribbon.listOfServers: http://**********3:10116
mm-medical-contract.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
mm-medical-contract.ribbon.listOfServers: http://**********3:10117
mobile-system.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
mobile-system.ribbon.listOfServers: http://**********3:10118
niubility-system.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
niubility-system.ribbon.listOfServers: http://**********3:10119
oms.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
oms.ribbon.listOfServers: http://**********3:10120
online-hospital.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
online-hospital.ribbon.listOfServers: http://**********3:10121
operate-system.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
operate-system.ribbon.listOfServers: http://**********3:10122
order.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
order.ribbon.listOfServers: http://**********3:10123
order-sync.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
order-sync.ribbon.listOfServers: http://**********3:10124
order-transfer.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
order-transfer.ribbon.listOfServers: http://**********3:10125
patient-system.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
patient-system.ribbon.listOfServers: http://**********3:10126
paycenter.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
paycenter.ribbon.listOfServers: http://**********3:10127
performance.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
performance.ribbon.listOfServers: http://**********3:10128
permission.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
permission.ribbon.listOfServers: http://**********3:10129
permission-system.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
permission-system.ribbon.listOfServers: http://**********3:10130
portal.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
portal.ribbon.listOfServers: http://**********3:10131
pos.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
pos.ribbon.listOfServers: http://**********3:10132
pos-system.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
pos-system.ribbon.listOfServers: http://**********3:10133
pricecenter.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
pricecenter.ribbon.listOfServers: http://**********3:10134
promotion-biz-cp.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
promotion-biz-cp.ribbon.listOfServers: http://**********3:10135
purchase.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
purchase.ribbon.listOfServers: http://10.2.13.130:10136
rasa-duckling-server.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
rasa-duckling-server.ribbon.listOfServers: http://10.2.13.130:10137
satellite.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
satellite.ribbon.listOfServers: http://10.2.13.130:10138
schedule-delete-business-flow.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
schedule-delete-business-flow.ribbon.listOfServers: http://10.2.13.130:10139
scib.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
scib.ribbon.listOfServers: http://10.2.13.130:10140
scrm.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
scrm.ribbon.listOfServers: http://10.2.13.130:10141
scrm-system.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
scrm-system.ribbon.listOfServers: http://10.2.13.130:10142
searchapi.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
searchapi.ribbon.listOfServers: http://10.2.13.130:10143
seckill.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
seckill.ribbon.listOfServers: http://10.2.13.130:10144
sekiro.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
sekiro.ribbon.listOfServers: http://10.2.13.130:10145
sekiro1.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
sekiro1.ribbon.listOfServers: http://10.2.13.130:10146
sekiro2.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
sekiro2.ribbon.listOfServers: http://10.2.13.130:10147
selfmall.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
selfmall.ribbon.listOfServers: http://10.2.13.130:10148
sentinel-admin.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
sentinel-admin.ribbon.listOfServers: http://10.2.13.130:10149
sentinel-admin-port.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
sentinel-admin-port.ribbon.listOfServers: http://10.2.13.130:10150
sentinel-dashboard.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
sentinel-dashboard.ribbon.listOfServers: http://10.2.13.130:10151
sentinel-token-server.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
sentinel-token-server.ribbon.listOfServers: http://10.2.13.130:10152
sentinel-token-server-client.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
sentinel-token-server-client.ribbon.listOfServers: http://10.2.13.130:10153
sop-admin.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
sop-admin.ribbon.listOfServers: http://10.2.13.130:10154
sop-gateway.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
sop-gateway.ribbon.listOfServers: http://10.2.13.130:10155
sop-out.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
sop-out.ribbon.listOfServers: http://10.2.13.130:10156
srm-system.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
srm-system.ribbon.listOfServers: http://10.2.13.130:10157
stockcenter.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
stockcenter.ribbon.listOfServers: http://10.2.13.130:10158
stockcenter-sync.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
stockcenter-sync.ribbon.listOfServers: http://10.2.13.130:10159
store.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
store.ribbon.listOfServers: http://10.2.13.130:10160
tms.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
tms.ribbon.listOfServers: http://10.2.13.130:10161
toc.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
toc.ribbon.listOfServers: http://10.2.13.130:10162
train-system.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
train-system.ribbon.listOfServers: http://10.2.13.130:10163
uaa.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
uaa.ribbon.listOfServers: http://10.2.13.130:10164
weapp-system.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
weapp-system.ribbon.listOfServers: http://10.2.13.130:10165
web-harvester.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
web-harvester.ribbon.listOfServers: http://10.2.13.130:10166
web-log.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
web-log.ribbon.listOfServers: http://10.2.13.130:10167
wxgateway.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
wxgateway.ribbon.listOfServers: http://10.2.13.130:10168
xxl-job-admin.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
xxl-job-admin.ribbon.listOfServers: http://10.2.13.130:10169


# api 管控
security.api.white_req_url: https://api-store-test-internal.gaojihealth.cn/nyuwa/api/intranet/records/290/cowell_api?filter=app_name,eq,iscm&filter=is_white,eq,否
security.api.white_increment_url: https://api-test-internal.gaojihealth.cn/nyuwa/api/intranet/records/290/cowell_api?filter=app_name,eq,iscm&filter=is_white,eq,否&filter=gmt_update,bt,{startTime},{endTime}
security.api.report_health_url: https://api-test-internal.gaojihealth.cn/nyuwa/api/intranet/mdd/290/bu_app/batchSaveMerge?indexName={indexName}
security.api.open_log: false
security.api.processKey: 290.cowell_api.iscm
apollosdkconfig:
    is_online: false
    apollo_env: DEV
    cookie_val: bb07bf1a2ee6bd855646708a51c68362f6a7b3e8
    list:
        - business_key: cowellApiProcessor
          app_id: nyuwa
          cur_namespace: middleware.nyuwaCommon
          accurate_keys: 290.cowell_api.iscm,urgent_open
permission_config:
    open: false
