<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.IscmParamOrgManagementMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.IscmParamOrgManagement">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="param_unique_mark" jdbcType="VARCHAR" property="paramUniqueMark" />
    <result column="param_id" jdbcType="BIGINT" property="paramId" />
    <result column="param_code" jdbcType="VARCHAR" property="paramCode" />
    <result column="param_name" jdbcType="VARCHAR" property="paramName" />
    <result column="param_desc" jdbcType="VARCHAR" property="paramDesc" />
    <result column="param_scope" jdbcType="TINYINT" property="paramScope" />
    <result column="param_level" jdbcType="INTEGER" property="paramLevel" />
    <result column="param_type" jdbcType="TINYINT" property="paramType" />
    <result column="param_source" jdbcType="TINYINT" property="paramSource" />
    <result column="param_sequence" jdbcType="INTEGER" property="paramSequence" />
    <result column="param_order" jdbcType="INTEGER" property="paramOrder" />
    <result column="param_sequence_desc" jdbcType="VARCHAR" property="paramSequenceDesc" />
    <result column="org_id" jdbcType="BIGINT" property="orgId" />
    <result column="sap_code" jdbcType="VARCHAR" property="sapCode" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="bdp_code" jdbcType="VARCHAR" property="bdpCode" />
    <result column="org_param_status" jdbcType="TINYINT" property="orgParamStatus" />
    <result column="effect_date" jdbcType="TIMESTAMP" property="effectDate" />
    <result column="invalid_date" jdbcType="TIMESTAMP" property="invalidDate" />
    <result column="param_value" jdbcType="VARCHAR" property="paramValue" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, param_unique_mark, param_id, param_code, param_name, param_desc, param_scope, 
    param_level, param_type, param_source, param_sequence, param_order, param_sequence_desc, 
    org_id, sap_code, org_name, bdp_code, org_param_status, effect_date, invalid_date, 
    param_value, status, extend, version, created_by, created_name, updated_by, updated_name, 
    gmt_create, gmt_update
  </sql>
  <select id="selectByExample" parameterType="com.cowell.iscm.entity.IscmParamOrgManagementExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from iscm_param_org_management
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from iscm_param_org_management
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from iscm_param_org_management
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.iscm.entity.IscmParamOrgManagementExample">
    delete from iscm_param_org_management
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.iscm.entity.IscmParamOrgManagement">
    insert into iscm_param_org_management (id, param_unique_mark, param_id, 
      param_code, param_name, param_desc, 
      param_scope, param_level, param_type, 
      param_source, param_sequence, param_order, 
      param_sequence_desc, org_id, sap_code, 
      org_name, bdp_code, org_param_status, 
      effect_date, invalid_date, param_value, 
      status, extend, version, 
      created_by, created_name, updated_by, 
      updated_name, gmt_create, gmt_update
      )
    values (#{id,jdbcType=BIGINT}, #{paramUniqueMark,jdbcType=VARCHAR}, #{paramId,jdbcType=BIGINT}, 
      #{paramCode,jdbcType=VARCHAR}, #{paramName,jdbcType=VARCHAR}, #{paramDesc,jdbcType=VARCHAR}, 
      #{paramScope,jdbcType=TINYINT}, #{paramLevel,jdbcType=INTEGER}, #{paramType,jdbcType=TINYINT}, 
      #{paramSource,jdbcType=TINYINT}, #{paramSequence,jdbcType=INTEGER}, #{paramOrder,jdbcType=INTEGER}, 
      #{paramSequenceDesc,jdbcType=VARCHAR}, #{orgId,jdbcType=BIGINT}, #{sapCode,jdbcType=VARCHAR}, 
      #{orgName,jdbcType=VARCHAR}, #{bdpCode,jdbcType=VARCHAR}, #{orgParamStatus,jdbcType=TINYINT}, 
      #{effectDate,jdbcType=TIMESTAMP}, #{invalidDate,jdbcType=TIMESTAMP}, #{paramValue,jdbcType=VARCHAR}, 
      #{status,jdbcType=TINYINT}, #{extend,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER}, 
      #{createdBy,jdbcType=BIGINT}, #{createdName,jdbcType=VARCHAR}, #{updatedBy,jdbcType=BIGINT}, 
      #{updatedName,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.iscm.entity.IscmParamOrgManagement">
    insert into iscm_param_org_management
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="paramUniqueMark != null">
        param_unique_mark,
      </if>
      <if test="paramId != null">
        param_id,
      </if>
      <if test="paramCode != null">
        param_code,
      </if>
      <if test="paramName != null">
        param_name,
      </if>
      <if test="paramDesc != null">
        param_desc,
      </if>
      <if test="paramScope != null">
        param_scope,
      </if>
      <if test="paramLevel != null">
        param_level,
      </if>
      <if test="paramType != null">
        param_type,
      </if>
      <if test="paramSource != null">
        param_source,
      </if>
      <if test="paramSequence != null">
        param_sequence,
      </if>
      <if test="paramOrder != null">
        param_order,
      </if>
      <if test="paramSequenceDesc != null">
        param_sequence_desc,
      </if>
      <if test="orgId != null">
        org_id,
      </if>
      <if test="sapCode != null">
        sap_code,
      </if>
      <if test="orgName != null">
        org_name,
      </if>
      <if test="bdpCode != null">
        bdp_code,
      </if>
      <if test="orgParamStatus != null">
        org_param_status,
      </if>
      <if test="effectDate != null">
        effect_date,
      </if>
      <if test="invalidDate != null">
        invalid_date,
      </if>
      <if test="paramValue != null">
        param_value,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedName != null">
        updated_name,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="paramUniqueMark != null">
        #{paramUniqueMark,jdbcType=VARCHAR},
      </if>
      <if test="paramId != null">
        #{paramId,jdbcType=BIGINT},
      </if>
      <if test="paramCode != null">
        #{paramCode,jdbcType=VARCHAR},
      </if>
      <if test="paramName != null">
        #{paramName,jdbcType=VARCHAR},
      </if>
      <if test="paramDesc != null">
        #{paramDesc,jdbcType=VARCHAR},
      </if>
      <if test="paramScope != null">
        #{paramScope,jdbcType=TINYINT},
      </if>
      <if test="paramLevel != null">
        #{paramLevel,jdbcType=INTEGER},
      </if>
      <if test="paramType != null">
        #{paramType,jdbcType=TINYINT},
      </if>
      <if test="paramSource != null">
        #{paramSource,jdbcType=TINYINT},
      </if>
      <if test="paramSequence != null">
        #{paramSequence,jdbcType=INTEGER},
      </if>
      <if test="paramOrder != null">
        #{paramOrder,jdbcType=INTEGER},
      </if>
      <if test="paramSequenceDesc != null">
        #{paramSequenceDesc,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=BIGINT},
      </if>
      <if test="sapCode != null">
        #{sapCode,jdbcType=VARCHAR},
      </if>
      <if test="orgName != null">
        #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="bdpCode != null">
        #{bdpCode,jdbcType=VARCHAR},
      </if>
      <if test="orgParamStatus != null">
        #{orgParamStatus,jdbcType=TINYINT},
      </if>
      <if test="effectDate != null">
        #{effectDate,jdbcType=TIMESTAMP},
      </if>
      <if test="invalidDate != null">
        #{invalidDate,jdbcType=TIMESTAMP},
      </if>
      <if test="paramValue != null">
        #{paramValue,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        #{updatedName,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.iscm.entity.IscmParamOrgManagementExample" resultType="java.lang.Long">
    select count(*) from iscm_param_org_management
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update iscm_param_org_management
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.paramUniqueMark != null">
        param_unique_mark = #{record.paramUniqueMark,jdbcType=VARCHAR},
      </if>
      <if test="record.paramId != null">
        param_id = #{record.paramId,jdbcType=BIGINT},
      </if>
      <if test="record.paramCode != null">
        param_code = #{record.paramCode,jdbcType=VARCHAR},
      </if>
      <if test="record.paramName != null">
        param_name = #{record.paramName,jdbcType=VARCHAR},
      </if>
      <if test="record.paramDesc != null">
        param_desc = #{record.paramDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.paramScope != null">
        param_scope = #{record.paramScope,jdbcType=TINYINT},
      </if>
      <if test="record.paramLevel != null">
        param_level = #{record.paramLevel,jdbcType=INTEGER},
      </if>
      <if test="record.paramType != null">
        param_type = #{record.paramType,jdbcType=TINYINT},
      </if>
      <if test="record.paramSource != null">
        param_source = #{record.paramSource,jdbcType=TINYINT},
      </if>
      <if test="record.paramSequence != null">
        param_sequence = #{record.paramSequence,jdbcType=INTEGER},
      </if>
      <if test="record.paramOrder != null">
        param_order = #{record.paramOrder,jdbcType=INTEGER},
      </if>
      <if test="record.paramSequenceDesc != null">
        param_sequence_desc = #{record.paramSequenceDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.orgId != null">
        org_id = #{record.orgId,jdbcType=BIGINT},
      </if>
      <if test="record.sapCode != null">
        sap_code = #{record.sapCode,jdbcType=VARCHAR},
      </if>
      <if test="record.orgName != null">
        org_name = #{record.orgName,jdbcType=VARCHAR},
      </if>
      <if test="record.bdpCode != null">
        bdp_code = #{record.bdpCode,jdbcType=VARCHAR},
      </if>
      <if test="record.orgParamStatus != null">
        org_param_status = #{record.orgParamStatus,jdbcType=TINYINT},
      </if>
      <if test="record.effectDate != null">
        effect_date = #{record.effectDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.invalidDate != null">
        invalid_date = #{record.invalidDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.paramValue != null">
        param_value = #{record.paramValue,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedName != null">
        updated_name = #{record.updatedName,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update iscm_param_org_management
    set id = #{record.id,jdbcType=BIGINT},
      param_unique_mark = #{record.paramUniqueMark,jdbcType=VARCHAR},
      param_id = #{record.paramId,jdbcType=BIGINT},
      param_code = #{record.paramCode,jdbcType=VARCHAR},
      param_name = #{record.paramName,jdbcType=VARCHAR},
      param_desc = #{record.paramDesc,jdbcType=VARCHAR},
      param_scope = #{record.paramScope,jdbcType=TINYINT},
      param_level = #{record.paramLevel,jdbcType=INTEGER},
      param_type = #{record.paramType,jdbcType=TINYINT},
      param_source = #{record.paramSource,jdbcType=TINYINT},
      param_sequence = #{record.paramSequence,jdbcType=INTEGER},
      param_order = #{record.paramOrder,jdbcType=INTEGER},
      param_sequence_desc = #{record.paramSequenceDesc,jdbcType=VARCHAR},
      org_id = #{record.orgId,jdbcType=BIGINT},
      sap_code = #{record.sapCode,jdbcType=VARCHAR},
      org_name = #{record.orgName,jdbcType=VARCHAR},
      bdp_code = #{record.bdpCode,jdbcType=VARCHAR},
      org_param_status = #{record.orgParamStatus,jdbcType=TINYINT},
      effect_date = #{record.effectDate,jdbcType=TIMESTAMP},
      invalid_date = #{record.invalidDate,jdbcType=TIMESTAMP},
      param_value = #{record.paramValue,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=TINYINT},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_name = #{record.createdName,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_name = #{record.updatedName,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.iscm.entity.IscmParamOrgManagement">
    update iscm_param_org_management
    <set>
      <if test="paramUniqueMark != null">
        param_unique_mark = #{paramUniqueMark,jdbcType=VARCHAR},
      </if>
      <if test="paramId != null">
        param_id = #{paramId,jdbcType=BIGINT},
      </if>
      <if test="paramCode != null">
        param_code = #{paramCode,jdbcType=VARCHAR},
      </if>
      <if test="paramName != null">
        param_name = #{paramName,jdbcType=VARCHAR},
      </if>
      <if test="paramDesc != null">
        param_desc = #{paramDesc,jdbcType=VARCHAR},
      </if>
      <if test="paramScope != null">
        param_scope = #{paramScope,jdbcType=TINYINT},
      </if>
      <if test="paramLevel != null">
        param_level = #{paramLevel,jdbcType=INTEGER},
      </if>
      <if test="paramType != null">
        param_type = #{paramType,jdbcType=TINYINT},
      </if>
      <if test="paramSource != null">
        param_source = #{paramSource,jdbcType=TINYINT},
      </if>
      <if test="paramSequence != null">
        param_sequence = #{paramSequence,jdbcType=INTEGER},
      </if>
      <if test="paramOrder != null">
        param_order = #{paramOrder,jdbcType=INTEGER},
      </if>
      <if test="paramSequenceDesc != null">
        param_sequence_desc = #{paramSequenceDesc,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null">
        org_id = #{orgId,jdbcType=BIGINT},
      </if>
      <if test="sapCode != null">
        sap_code = #{sapCode,jdbcType=VARCHAR},
      </if>
      <if test="orgName != null">
        org_name = #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="bdpCode != null">
        bdp_code = #{bdpCode,jdbcType=VARCHAR},
      </if>
      <if test="orgParamStatus != null">
        org_param_status = #{orgParamStatus,jdbcType=TINYINT},
      </if>
      <if test="effectDate != null">
        effect_date = #{effectDate,jdbcType=TIMESTAMP},
      </if>
      <if test="invalidDate != null">
        invalid_date = #{invalidDate,jdbcType=TIMESTAMP},
      </if>
      <if test="paramValue != null">
        param_value = #{paramValue,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        updated_name = #{updatedName,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.iscm.entity.IscmParamOrgManagement">
    update iscm_param_org_management
    set param_unique_mark = #{paramUniqueMark,jdbcType=VARCHAR},
      param_id = #{paramId,jdbcType=BIGINT},
      param_code = #{paramCode,jdbcType=VARCHAR},
      param_name = #{paramName,jdbcType=VARCHAR},
      param_desc = #{paramDesc,jdbcType=VARCHAR},
      param_scope = #{paramScope,jdbcType=TINYINT},
      param_level = #{paramLevel,jdbcType=INTEGER},
      param_type = #{paramType,jdbcType=TINYINT},
      param_source = #{paramSource,jdbcType=TINYINT},
      param_sequence = #{paramSequence,jdbcType=INTEGER},
      param_order = #{paramOrder,jdbcType=INTEGER},
      param_sequence_desc = #{paramSequenceDesc,jdbcType=VARCHAR},
      org_id = #{orgId,jdbcType=BIGINT},
      sap_code = #{sapCode,jdbcType=VARCHAR},
      org_name = #{orgName,jdbcType=VARCHAR},
      bdp_code = #{bdpCode,jdbcType=VARCHAR},
      org_param_status = #{orgParamStatus,jdbcType=TINYINT},
      effect_date = #{effectDate,jdbcType=TIMESTAMP},
      invalid_date = #{invalidDate,jdbcType=TIMESTAMP},
      param_value = #{paramValue,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_name = #{createdName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_name = #{updatedName,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>