<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.IscmRiskConfigMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.IscmRiskConfig">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="org_id" jdbcType="BIGINT" property="orgId" />
    <result column="sap_code" jdbcType="VARCHAR" property="sapCode" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="enable_mail_notifier" jdbcType="TINYINT" property="enableMailNotifier" />
    <result column="mail_notify_over_days" jdbcType="INTEGER" property="mailNotifyOverDays" />
    <result column="mail_send_time_daily" jdbcType="VARCHAR" property="mailSendTimeDaily" />
    <result column="mail_send_time" jdbcType="TIMESTAMP" property="mailSendTime" />
    <result column="mail_send_result" jdbcType="TINYINT" property="mailSendResult" />
    <result column="risk_type" jdbcType="TINYINT" property="riskType" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, org_id, sap_code, org_name, enable_mail_notifier, mail_notify_over_days, mail_send_time_daily, 
    mail_send_time, mail_send_result, risk_type, status, extend, version, created_by, 
    created_name, updated_by, updated_name, gmt_create, gmt_update
  </sql>
  <select id="selectByExample" parameterType="com.cowell.iscm.entity.IscmRiskConfigExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from iscm_risk_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from iscm_risk_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from iscm_risk_config
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.iscm.entity.IscmRiskConfigExample">
    delete from iscm_risk_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.iscm.entity.IscmRiskConfig">
    insert into iscm_risk_config (id, org_id, sap_code, 
      org_name, enable_mail_notifier, mail_notify_over_days, 
      mail_send_time_daily, mail_send_time, mail_send_result, 
      risk_type, status, extend, 
      version, created_by, created_name, 
      updated_by, updated_name, gmt_create, 
      gmt_update)
    values (#{id,jdbcType=BIGINT}, #{orgId,jdbcType=BIGINT}, #{sapCode,jdbcType=VARCHAR}, 
      #{orgName,jdbcType=VARCHAR}, #{enableMailNotifier,jdbcType=TINYINT}, #{mailNotifyOverDays,jdbcType=INTEGER}, 
      #{mailSendTimeDaily,jdbcType=VARCHAR}, #{mailSendTime,jdbcType=TIMESTAMP}, #{mailSendResult,jdbcType=TINYINT}, 
      #{riskType,jdbcType=TINYINT}, #{status,jdbcType=TINYINT}, #{extend,jdbcType=VARCHAR}, 
      #{version,jdbcType=INTEGER}, #{createdBy,jdbcType=BIGINT}, #{createdName,jdbcType=VARCHAR}, 
      #{updatedBy,jdbcType=BIGINT}, #{updatedName,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtUpdate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.iscm.entity.IscmRiskConfig">
    insert into iscm_risk_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="orgId != null">
        org_id,
      </if>
      <if test="sapCode != null">
        sap_code,
      </if>
      <if test="orgName != null">
        org_name,
      </if>
      <if test="enableMailNotifier != null">
        enable_mail_notifier,
      </if>
      <if test="mailNotifyOverDays != null">
        mail_notify_over_days,
      </if>
      <if test="mailSendTimeDaily != null">
        mail_send_time_daily,
      </if>
      <if test="mailSendTime != null">
        mail_send_time,
      </if>
      <if test="mailSendResult != null">
        mail_send_result,
      </if>
      <if test="riskType != null">
        risk_type,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedName != null">
        updated_name,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=BIGINT},
      </if>
      <if test="sapCode != null">
        #{sapCode,jdbcType=VARCHAR},
      </if>
      <if test="orgName != null">
        #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="enableMailNotifier != null">
        #{enableMailNotifier,jdbcType=TINYINT},
      </if>
      <if test="mailNotifyOverDays != null">
        #{mailNotifyOverDays,jdbcType=INTEGER},
      </if>
      <if test="mailSendTimeDaily != null">
        #{mailSendTimeDaily,jdbcType=VARCHAR},
      </if>
      <if test="mailSendTime != null">
        #{mailSendTime,jdbcType=TIMESTAMP},
      </if>
      <if test="mailSendResult != null">
        #{mailSendResult,jdbcType=TINYINT},
      </if>
      <if test="riskType != null">
        #{riskType,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        #{updatedName,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.iscm.entity.IscmRiskConfigExample" resultType="java.lang.Long">
    select count(*) from iscm_risk_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update iscm_risk_config
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.orgId != null">
        org_id = #{record.orgId,jdbcType=BIGINT},
      </if>
      <if test="record.sapCode != null">
        sap_code = #{record.sapCode,jdbcType=VARCHAR},
      </if>
      <if test="record.orgName != null">
        org_name = #{record.orgName,jdbcType=VARCHAR},
      </if>
      <if test="record.enableMailNotifier != null">
        enable_mail_notifier = #{record.enableMailNotifier,jdbcType=TINYINT},
      </if>
      <if test="record.mailNotifyOverDays != null">
        mail_notify_over_days = #{record.mailNotifyOverDays,jdbcType=INTEGER},
      </if>
      <if test="record.mailSendTimeDaily != null">
        mail_send_time_daily = #{record.mailSendTimeDaily,jdbcType=VARCHAR},
      </if>
      <if test="record.mailSendTime != null">
        mail_send_time = #{record.mailSendTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mailSendResult != null">
        mail_send_result = #{record.mailSendResult,jdbcType=TINYINT},
      </if>
      <if test="record.riskType != null">
        risk_type = #{record.riskType,jdbcType=TINYINT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedName != null">
        updated_name = #{record.updatedName,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update iscm_risk_config
    set id = #{record.id,jdbcType=BIGINT},
      org_id = #{record.orgId,jdbcType=BIGINT},
      sap_code = #{record.sapCode,jdbcType=VARCHAR},
      org_name = #{record.orgName,jdbcType=VARCHAR},
      enable_mail_notifier = #{record.enableMailNotifier,jdbcType=TINYINT},
      mail_notify_over_days = #{record.mailNotifyOverDays,jdbcType=INTEGER},
      mail_send_time_daily = #{record.mailSendTimeDaily,jdbcType=VARCHAR},
      mail_send_time = #{record.mailSendTime,jdbcType=TIMESTAMP},
      mail_send_result = #{record.mailSendResult,jdbcType=TINYINT},
      risk_type = #{record.riskType,jdbcType=TINYINT},
      status = #{record.status,jdbcType=TINYINT},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_name = #{record.createdName,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_name = #{record.updatedName,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.iscm.entity.IscmRiskConfig">
    update iscm_risk_config
    <set>
      <if test="orgId != null">
        org_id = #{orgId,jdbcType=BIGINT},
      </if>
      <if test="sapCode != null">
        sap_code = #{sapCode,jdbcType=VARCHAR},
      </if>
      <if test="orgName != null">
        org_name = #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="enableMailNotifier != null">
        enable_mail_notifier = #{enableMailNotifier,jdbcType=TINYINT},
      </if>
      <if test="mailNotifyOverDays != null">
        mail_notify_over_days = #{mailNotifyOverDays,jdbcType=INTEGER},
      </if>
      <if test="mailSendTimeDaily != null">
        mail_send_time_daily = #{mailSendTimeDaily,jdbcType=VARCHAR},
      </if>
      <if test="mailSendTime != null">
        mail_send_time = #{mailSendTime,jdbcType=TIMESTAMP},
      </if>
      <if test="mailSendResult != null">
        mail_send_result = #{mailSendResult,jdbcType=TINYINT},
      </if>
      <if test="riskType != null">
        risk_type = #{riskType,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        updated_name = #{updatedName,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.iscm.entity.IscmRiskConfig">
    update iscm_risk_config
    set org_id = #{orgId,jdbcType=BIGINT},
      sap_code = #{sapCode,jdbcType=VARCHAR},
      org_name = #{orgName,jdbcType=VARCHAR},
      enable_mail_notifier = #{enableMailNotifier,jdbcType=TINYINT},
      mail_notify_over_days = #{mailNotifyOverDays,jdbcType=INTEGER},
      mail_send_time_daily = #{mailSendTimeDaily,jdbcType=VARCHAR},
      mail_send_time = #{mailSendTime,jdbcType=TIMESTAMP},
      mail_send_result = #{mailSendResult,jdbcType=TINYINT},
      risk_type = #{riskType,jdbcType=TINYINT},
      status = #{status,jdbcType=TINYINT},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_name = #{createdName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_name = #{updatedName,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>