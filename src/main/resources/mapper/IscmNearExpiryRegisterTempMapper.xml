<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.IscmNearExpiryRegisterTempMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.IscmNearExpiryRegisterTemp">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="org_id" jdbcType="BIGINT" property="orgId" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="platform_org_id" jdbcType="BIGINT" property="platformOrgId" />
    <result column="platform_org_name" jdbcType="VARCHAR" property="platformOrgName" />
    <result column="company_org_id" jdbcType="BIGINT" property="companyOrgId" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="store_org_id" jdbcType="BIGINT" property="storeOrgId" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="allot_group_code" jdbcType="VARCHAR" property="allotGroupCode" />
    <result column="allot_group_name" jdbcType="VARCHAR" property="allotGroupName" />
    <result column="store_name" jdbcType="VARCHAR" property="storeName" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="bar_code" jdbcType="VARCHAR" property="barCode" />
    <result column="goods_common_name" jdbcType="VARCHAR" property="goodsCommonName" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="goods_unit" jdbcType="VARCHAR" property="goodsUnit" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="specifications" jdbcType="VARCHAR" property="specifications" />
    <result column="dosage_form" jdbcType="VARCHAR" property="dosageForm" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="approval_number" jdbcType="VARCHAR" property="approvalNumber" />
    <result column="habitat" jdbcType="VARCHAR" property="habitat" />
    <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
    <result column="validity_date" jdbcType="TIMESTAMP" property="validityDate" />
    <result column="produce_date" jdbcType="TIMESTAMP" property="produceDate" />
    <result column="register_quantity" jdbcType="DECIMAL" property="registerQuantity" />
    <result column="stock_quantity" jdbcType="DECIMAL" property="stockQuantity" />
    <result column="stock_upper_limit" jdbcType="DECIMAL" property="stockUpperLimit" />
    <result column="stock_lower_limit" jdbcType="DECIMAL" property="stockLowerLimit" />
    <result column="stock_cost_amount" jdbcType="DECIMAL" property="stockCostAmount" />
    <result column="cost_amount" jdbcType="DECIMAL" property="costAmount" />
    <result column="register_status" jdbcType="TINYINT" property="registerStatus" />
    <result column="register_error_reason" jdbcType="VARCHAR" property="registerErrorReason" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, org_id, org_name, platform_org_id, platform_org_name, company_org_id, company_code, 
    company_name, store_org_id, store_code, allot_group_code, allot_group_name, store_name, 
    goods_no, bar_code, goods_common_name, goods_name, goods_unit, description, specifications, 
    dosage_form, manufacturer, approval_number, habitat, batch_no, validity_date, produce_date, 
    register_quantity, stock_quantity, stock_upper_limit, stock_lower_limit, stock_cost_amount, 
    cost_amount, register_status, register_error_reason, `status`, gmt_create, gmt_update, 
    extend, version, created_by, created_name, updated_by, updated_name
  </sql>
  <select id="selectByExample" parameterType="com.cowell.iscm.entity.IscmNearExpiryRegisterTempExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from iscm_near_expiry_register_temp
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from iscm_near_expiry_register_temp
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from iscm_near_expiry_register_temp
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.iscm.entity.IscmNearExpiryRegisterTempExample">
    delete from iscm_near_expiry_register_temp
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entity.IscmNearExpiryRegisterTemp" useGeneratedKeys="true">
    insert into iscm_near_expiry_register_temp (org_id, org_name, platform_org_id, 
      platform_org_name, company_org_id, company_code, 
      company_name, store_org_id, store_code, 
      allot_group_code, allot_group_name, store_name, 
      goods_no, bar_code, goods_common_name, 
      goods_name, goods_unit, description, 
      specifications, dosage_form, manufacturer, 
      approval_number, habitat, batch_no, 
      validity_date, produce_date, register_quantity, 
      stock_quantity, stock_upper_limit, stock_lower_limit, 
      stock_cost_amount, cost_amount, register_status, 
      register_error_reason, `status`, gmt_create, 
      gmt_update, extend, version, 
      created_by, created_name, updated_by, 
      updated_name)
    values (#{orgId,jdbcType=BIGINT}, #{orgName,jdbcType=VARCHAR}, #{platformOrgId,jdbcType=BIGINT}, 
      #{platformOrgName,jdbcType=VARCHAR}, #{companyOrgId,jdbcType=BIGINT}, #{companyCode,jdbcType=VARCHAR}, 
      #{companyName,jdbcType=VARCHAR}, #{storeOrgId,jdbcType=BIGINT}, #{storeCode,jdbcType=VARCHAR}, 
      #{allotGroupCode,jdbcType=VARCHAR}, #{allotGroupName,jdbcType=VARCHAR}, #{storeName,jdbcType=VARCHAR}, 
      #{goodsNo,jdbcType=VARCHAR}, #{barCode,jdbcType=VARCHAR}, #{goodsCommonName,jdbcType=VARCHAR}, 
      #{goodsName,jdbcType=VARCHAR}, #{goodsUnit,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, 
      #{specifications,jdbcType=VARCHAR}, #{dosageForm,jdbcType=VARCHAR}, #{manufacturer,jdbcType=VARCHAR}, 
      #{approvalNumber,jdbcType=VARCHAR}, #{habitat,jdbcType=VARCHAR}, #{batchNo,jdbcType=VARCHAR}, 
      #{validityDate,jdbcType=TIMESTAMP}, #{produceDate,jdbcType=TIMESTAMP}, #{registerQuantity,jdbcType=DECIMAL}, 
      #{stockQuantity,jdbcType=DECIMAL}, #{stockUpperLimit,jdbcType=DECIMAL}, #{stockLowerLimit,jdbcType=DECIMAL}, 
      #{stockCostAmount,jdbcType=DECIMAL}, #{costAmount,jdbcType=DECIMAL}, #{registerStatus,jdbcType=TINYINT}, 
      #{registerErrorReason,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtUpdate,jdbcType=TIMESTAMP}, #{extend,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER}, 
      #{createdBy,jdbcType=BIGINT}, #{createdName,jdbcType=VARCHAR}, #{updatedBy,jdbcType=BIGINT}, 
      #{updatedName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entity.IscmNearExpiryRegisterTemp" useGeneratedKeys="true">
    insert into iscm_near_expiry_register_temp
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orgId != null">
        org_id,
      </if>
      <if test="orgName != null">
        org_name,
      </if>
      <if test="platformOrgId != null">
        platform_org_id,
      </if>
      <if test="platformOrgName != null">
        platform_org_name,
      </if>
      <if test="companyOrgId != null">
        company_org_id,
      </if>
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="companyName != null">
        company_name,
      </if>
      <if test="storeOrgId != null">
        store_org_id,
      </if>
      <if test="storeCode != null">
        store_code,
      </if>
      <if test="allotGroupCode != null">
        allot_group_code,
      </if>
      <if test="allotGroupName != null">
        allot_group_name,
      </if>
      <if test="storeName != null">
        store_name,
      </if>
      <if test="goodsNo != null">
        goods_no,
      </if>
      <if test="barCode != null">
        bar_code,
      </if>
      <if test="goodsCommonName != null">
        goods_common_name,
      </if>
      <if test="goodsName != null">
        goods_name,
      </if>
      <if test="goodsUnit != null">
        goods_unit,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="specifications != null">
        specifications,
      </if>
      <if test="dosageForm != null">
        dosage_form,
      </if>
      <if test="manufacturer != null">
        manufacturer,
      </if>
      <if test="approvalNumber != null">
        approval_number,
      </if>
      <if test="habitat != null">
        habitat,
      </if>
      <if test="batchNo != null">
        batch_no,
      </if>
      <if test="validityDate != null">
        validity_date,
      </if>
      <if test="produceDate != null">
        produce_date,
      </if>
      <if test="registerQuantity != null">
        register_quantity,
      </if>
      <if test="stockQuantity != null">
        stock_quantity,
      </if>
      <if test="stockUpperLimit != null">
        stock_upper_limit,
      </if>
      <if test="stockLowerLimit != null">
        stock_lower_limit,
      </if>
      <if test="stockCostAmount != null">
        stock_cost_amount,
      </if>
      <if test="costAmount != null">
        cost_amount,
      </if>
      <if test="registerStatus != null">
        register_status,
      </if>
      <if test="registerErrorReason != null">
        register_error_reason,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedName != null">
        updated_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orgId != null">
        #{orgId,jdbcType=BIGINT},
      </if>
      <if test="orgName != null">
        #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="platformOrgId != null">
        #{platformOrgId,jdbcType=BIGINT},
      </if>
      <if test="platformOrgName != null">
        #{platformOrgName,jdbcType=VARCHAR},
      </if>
      <if test="companyOrgId != null">
        #{companyOrgId,jdbcType=BIGINT},
      </if>
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="storeOrgId != null">
        #{storeOrgId,jdbcType=BIGINT},
      </if>
      <if test="storeCode != null">
        #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="allotGroupCode != null">
        #{allotGroupCode,jdbcType=VARCHAR},
      </if>
      <if test="allotGroupName != null">
        #{allotGroupName,jdbcType=VARCHAR},
      </if>
      <if test="storeName != null">
        #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="goodsNo != null">
        #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="barCode != null">
        #{barCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsCommonName != null">
        #{goodsCommonName,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="goodsUnit != null">
        #{goodsUnit,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="specifications != null">
        #{specifications,jdbcType=VARCHAR},
      </if>
      <if test="dosageForm != null">
        #{dosageForm,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="approvalNumber != null">
        #{approvalNumber,jdbcType=VARCHAR},
      </if>
      <if test="habitat != null">
        #{habitat,jdbcType=VARCHAR},
      </if>
      <if test="batchNo != null">
        #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="validityDate != null">
        #{validityDate,jdbcType=TIMESTAMP},
      </if>
      <if test="produceDate != null">
        #{produceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="registerQuantity != null">
        #{registerQuantity,jdbcType=DECIMAL},
      </if>
      <if test="stockQuantity != null">
        #{stockQuantity,jdbcType=DECIMAL},
      </if>
      <if test="stockUpperLimit != null">
        #{stockUpperLimit,jdbcType=DECIMAL},
      </if>
      <if test="stockLowerLimit != null">
        #{stockLowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="stockCostAmount != null">
        #{stockCostAmount,jdbcType=DECIMAL},
      </if>
      <if test="costAmount != null">
        #{costAmount,jdbcType=DECIMAL},
      </if>
      <if test="registerStatus != null">
        #{registerStatus,jdbcType=TINYINT},
      </if>
      <if test="registerErrorReason != null">
        #{registerErrorReason,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        #{updatedName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.iscm.entity.IscmNearExpiryRegisterTempExample" resultType="java.lang.Long">
    select count(*) from iscm_near_expiry_register_temp
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update iscm_near_expiry_register_temp
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.orgId != null">
        org_id = #{record.orgId,jdbcType=BIGINT},
      </if>
      <if test="record.orgName != null">
        org_name = #{record.orgName,jdbcType=VARCHAR},
      </if>
      <if test="record.platformOrgId != null">
        platform_org_id = #{record.platformOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.platformOrgName != null">
        platform_org_name = #{record.platformOrgName,jdbcType=VARCHAR},
      </if>
      <if test="record.companyOrgId != null">
        company_org_id = #{record.companyOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.companyCode != null">
        company_code = #{record.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.companyName != null">
        company_name = #{record.companyName,jdbcType=VARCHAR},
      </if>
      <if test="record.storeOrgId != null">
        store_org_id = #{record.storeOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.storeCode != null">
        store_code = #{record.storeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.allotGroupCode != null">
        allot_group_code = #{record.allotGroupCode,jdbcType=VARCHAR},
      </if>
      <if test="record.allotGroupName != null">
        allot_group_name = #{record.allotGroupName,jdbcType=VARCHAR},
      </if>
      <if test="record.storeName != null">
        store_name = #{record.storeName,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsNo != null">
        goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="record.barCode != null">
        bar_code = #{record.barCode,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsCommonName != null">
        goods_common_name = #{record.goodsCommonName,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsName != null">
        goods_name = #{record.goodsName,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsUnit != null">
        goods_unit = #{record.goodsUnit,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.specifications != null">
        specifications = #{record.specifications,jdbcType=VARCHAR},
      </if>
      <if test="record.dosageForm != null">
        dosage_form = #{record.dosageForm,jdbcType=VARCHAR},
      </if>
      <if test="record.manufacturer != null">
        manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="record.approvalNumber != null">
        approval_number = #{record.approvalNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.habitat != null">
        habitat = #{record.habitat,jdbcType=VARCHAR},
      </if>
      <if test="record.batchNo != null">
        batch_no = #{record.batchNo,jdbcType=VARCHAR},
      </if>
      <if test="record.validityDate != null">
        validity_date = #{record.validityDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.produceDate != null">
        produce_date = #{record.produceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.registerQuantity != null">
        register_quantity = #{record.registerQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.stockQuantity != null">
        stock_quantity = #{record.stockQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.stockUpperLimit != null">
        stock_upper_limit = #{record.stockUpperLimit,jdbcType=DECIMAL},
      </if>
      <if test="record.stockLowerLimit != null">
        stock_lower_limit = #{record.stockLowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="record.stockCostAmount != null">
        stock_cost_amount = #{record.stockCostAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.costAmount != null">
        cost_amount = #{record.costAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.registerStatus != null">
        register_status = #{record.registerStatus,jdbcType=TINYINT},
      </if>
      <if test="record.registerErrorReason != null">
        register_error_reason = #{record.registerErrorReason,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedName != null">
        updated_name = #{record.updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update iscm_near_expiry_register_temp
    set id = #{record.id,jdbcType=BIGINT},
      org_id = #{record.orgId,jdbcType=BIGINT},
      org_name = #{record.orgName,jdbcType=VARCHAR},
      platform_org_id = #{record.platformOrgId,jdbcType=BIGINT},
      platform_org_name = #{record.platformOrgName,jdbcType=VARCHAR},
      company_org_id = #{record.companyOrgId,jdbcType=BIGINT},
      company_code = #{record.companyCode,jdbcType=VARCHAR},
      company_name = #{record.companyName,jdbcType=VARCHAR},
      store_org_id = #{record.storeOrgId,jdbcType=BIGINT},
      store_code = #{record.storeCode,jdbcType=VARCHAR},
      allot_group_code = #{record.allotGroupCode,jdbcType=VARCHAR},
      allot_group_name = #{record.allotGroupName,jdbcType=VARCHAR},
      store_name = #{record.storeName,jdbcType=VARCHAR},
      goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      bar_code = #{record.barCode,jdbcType=VARCHAR},
      goods_common_name = #{record.goodsCommonName,jdbcType=VARCHAR},
      goods_name = #{record.goodsName,jdbcType=VARCHAR},
      goods_unit = #{record.goodsUnit,jdbcType=VARCHAR},
      description = #{record.description,jdbcType=VARCHAR},
      specifications = #{record.specifications,jdbcType=VARCHAR},
      dosage_form = #{record.dosageForm,jdbcType=VARCHAR},
      manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      approval_number = #{record.approvalNumber,jdbcType=VARCHAR},
      habitat = #{record.habitat,jdbcType=VARCHAR},
      batch_no = #{record.batchNo,jdbcType=VARCHAR},
      validity_date = #{record.validityDate,jdbcType=TIMESTAMP},
      produce_date = #{record.produceDate,jdbcType=TIMESTAMP},
      register_quantity = #{record.registerQuantity,jdbcType=DECIMAL},
      stock_quantity = #{record.stockQuantity,jdbcType=DECIMAL},
      stock_upper_limit = #{record.stockUpperLimit,jdbcType=DECIMAL},
      stock_lower_limit = #{record.stockLowerLimit,jdbcType=DECIMAL},
      stock_cost_amount = #{record.stockCostAmount,jdbcType=DECIMAL},
      cost_amount = #{record.costAmount,jdbcType=DECIMAL},
      register_status = #{record.registerStatus,jdbcType=TINYINT},
      register_error_reason = #{record.registerErrorReason,jdbcType=VARCHAR},
      `status` = #{record.status,jdbcType=TINYINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_name = #{record.createdName,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_name = #{record.updatedName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.iscm.entity.IscmNearExpiryRegisterTemp">
    update iscm_near_expiry_register_temp
    <set>
      <if test="orgId != null">
        org_id = #{orgId,jdbcType=BIGINT},
      </if>
      <if test="orgName != null">
        org_name = #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="platformOrgId != null">
        platform_org_id = #{platformOrgId,jdbcType=BIGINT},
      </if>
      <if test="platformOrgName != null">
        platform_org_name = #{platformOrgName,jdbcType=VARCHAR},
      </if>
      <if test="companyOrgId != null">
        company_org_id = #{companyOrgId,jdbcType=BIGINT},
      </if>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="storeOrgId != null">
        store_org_id = #{storeOrgId,jdbcType=BIGINT},
      </if>
      <if test="storeCode != null">
        store_code = #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="allotGroupCode != null">
        allot_group_code = #{allotGroupCode,jdbcType=VARCHAR},
      </if>
      <if test="allotGroupName != null">
        allot_group_name = #{allotGroupName,jdbcType=VARCHAR},
      </if>
      <if test="storeName != null">
        store_name = #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="goodsNo != null">
        goods_no = #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="barCode != null">
        bar_code = #{barCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsCommonName != null">
        goods_common_name = #{goodsCommonName,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        goods_name = #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="goodsUnit != null">
        goods_unit = #{goodsUnit,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="specifications != null">
        specifications = #{specifications,jdbcType=VARCHAR},
      </if>
      <if test="dosageForm != null">
        dosage_form = #{dosageForm,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        manufacturer = #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="approvalNumber != null">
        approval_number = #{approvalNumber,jdbcType=VARCHAR},
      </if>
      <if test="habitat != null">
        habitat = #{habitat,jdbcType=VARCHAR},
      </if>
      <if test="batchNo != null">
        batch_no = #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="validityDate != null">
        validity_date = #{validityDate,jdbcType=TIMESTAMP},
      </if>
      <if test="produceDate != null">
        produce_date = #{produceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="registerQuantity != null">
        register_quantity = #{registerQuantity,jdbcType=DECIMAL},
      </if>
      <if test="stockQuantity != null">
        stock_quantity = #{stockQuantity,jdbcType=DECIMAL},
      </if>
      <if test="stockUpperLimit != null">
        stock_upper_limit = #{stockUpperLimit,jdbcType=DECIMAL},
      </if>
      <if test="stockLowerLimit != null">
        stock_lower_limit = #{stockLowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="stockCostAmount != null">
        stock_cost_amount = #{stockCostAmount,jdbcType=DECIMAL},
      </if>
      <if test="costAmount != null">
        cost_amount = #{costAmount,jdbcType=DECIMAL},
      </if>
      <if test="registerStatus != null">
        register_status = #{registerStatus,jdbcType=TINYINT},
      </if>
      <if test="registerErrorReason != null">
        register_error_reason = #{registerErrorReason,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        updated_name = #{updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.iscm.entity.IscmNearExpiryRegisterTemp">
    update iscm_near_expiry_register_temp
    set org_id = #{orgId,jdbcType=BIGINT},
      org_name = #{orgName,jdbcType=VARCHAR},
      platform_org_id = #{platformOrgId,jdbcType=BIGINT},
      platform_org_name = #{platformOrgName,jdbcType=VARCHAR},
      company_org_id = #{companyOrgId,jdbcType=BIGINT},
      company_code = #{companyCode,jdbcType=VARCHAR},
      company_name = #{companyName,jdbcType=VARCHAR},
      store_org_id = #{storeOrgId,jdbcType=BIGINT},
      store_code = #{storeCode,jdbcType=VARCHAR},
      allot_group_code = #{allotGroupCode,jdbcType=VARCHAR},
      allot_group_name = #{allotGroupName,jdbcType=VARCHAR},
      store_name = #{storeName,jdbcType=VARCHAR},
      goods_no = #{goodsNo,jdbcType=VARCHAR},
      bar_code = #{barCode,jdbcType=VARCHAR},
      goods_common_name = #{goodsCommonName,jdbcType=VARCHAR},
      goods_name = #{goodsName,jdbcType=VARCHAR},
      goods_unit = #{goodsUnit,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      specifications = #{specifications,jdbcType=VARCHAR},
      dosage_form = #{dosageForm,jdbcType=VARCHAR},
      manufacturer = #{manufacturer,jdbcType=VARCHAR},
      approval_number = #{approvalNumber,jdbcType=VARCHAR},
      habitat = #{habitat,jdbcType=VARCHAR},
      batch_no = #{batchNo,jdbcType=VARCHAR},
      validity_date = #{validityDate,jdbcType=TIMESTAMP},
      produce_date = #{produceDate,jdbcType=TIMESTAMP},
      register_quantity = #{registerQuantity,jdbcType=DECIMAL},
      stock_quantity = #{stockQuantity,jdbcType=DECIMAL},
      stock_upper_limit = #{stockUpperLimit,jdbcType=DECIMAL},
      stock_lower_limit = #{stockLowerLimit,jdbcType=DECIMAL},
      stock_cost_amount = #{stockCostAmount,jdbcType=DECIMAL},
      cost_amount = #{costAmount,jdbcType=DECIMAL},
      register_status = #{registerStatus,jdbcType=TINYINT},
      register_error_reason = #{registerErrorReason,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_name = #{createdName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_name = #{updatedName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>