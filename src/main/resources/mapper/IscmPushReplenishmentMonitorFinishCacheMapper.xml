<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.IscmPushReplenishmentMonitorFinishCacheMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.IscmPushReplenishmentMonitorFinishCache">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="business_type" jdbcType="TINYINT" property="businessType" />
    <result column="plan_start_time" jdbcType="DATE" property="planStartTime" />
    <result column="start_time" jdbcType="DATE" property="startTime" />
    <result column="finish_time" jdbcType="DATE" property="finishTime" />
    <result column="target_rate" jdbcType="DECIMAL" property="targetRate" />
    <result column="delay_time" jdbcType="INTEGER" property="delayTime" />
    <result column="diff_quantity" jdbcType="INTEGER" property="diffQuantity" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, company_code, business_type, plan_start_time, start_time, finish_time, target_rate, 
    delay_time, diff_quantity, `status`, gmt_create, gmt_update, extend, version, created_by, 
    created_name, updated_by, updated_name
  </sql>
  <select id="selectByExample" parameterType="com.cowell.iscm.entity.IscmPushReplenishmentMonitorFinishCacheExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from iscm_push_replenishment_monitor_finish_cache
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from iscm_push_replenishment_monitor_finish_cache
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from iscm_push_replenishment_monitor_finish_cache
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.iscm.entity.IscmPushReplenishmentMonitorFinishCacheExample">
    delete from iscm_push_replenishment_monitor_finish_cache
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.iscm.entity.IscmPushReplenishmentMonitorFinishCache">
    insert into iscm_push_replenishment_monitor_finish_cache (id, company_code, business_type, 
      plan_start_time, start_time, finish_time, 
      target_rate, delay_time, diff_quantity, 
      `status`, gmt_create, gmt_update, 
      extend, version, created_by, 
      created_name, updated_by, updated_name
      )
    values (#{id,jdbcType=BIGINT}, #{companyCode,jdbcType=VARCHAR}, #{businessType,jdbcType=TINYINT}, 
      #{planStartTime,jdbcType=DATE}, #{startTime,jdbcType=DATE}, #{finishTime,jdbcType=DATE}, 
      #{targetRate,jdbcType=DECIMAL}, #{delayTime,jdbcType=INTEGER}, #{diffQuantity,jdbcType=INTEGER}, 
      #{status,jdbcType=TINYINT}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}, 
      #{extend,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER}, #{createdBy,jdbcType=BIGINT}, 
      #{createdName,jdbcType=VARCHAR}, #{updatedBy,jdbcType=BIGINT}, #{updatedName,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.iscm.entity.IscmPushReplenishmentMonitorFinishCache">
    insert into iscm_push_replenishment_monitor_finish_cache
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="businessType != null">
        business_type,
      </if>
      <if test="planStartTime != null">
        plan_start_time,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="finishTime != null">
        finish_time,
      </if>
      <if test="targetRate != null">
        target_rate,
      </if>
      <if test="delayTime != null">
        delay_time,
      </if>
      <if test="diffQuantity != null">
        diff_quantity,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedName != null">
        updated_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=TINYINT},
      </if>
      <if test="planStartTime != null">
        #{planStartTime,jdbcType=DATE},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=DATE},
      </if>
      <if test="finishTime != null">
        #{finishTime,jdbcType=DATE},
      </if>
      <if test="targetRate != null">
        #{targetRate,jdbcType=DECIMAL},
      </if>
      <if test="delayTime != null">
        #{delayTime,jdbcType=INTEGER},
      </if>
      <if test="diffQuantity != null">
        #{diffQuantity,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        #{updatedName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.iscm.entity.IscmPushReplenishmentMonitorFinishCacheExample" resultType="java.lang.Long">
    select count(*) from iscm_push_replenishment_monitor_finish_cache
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update iscm_push_replenishment_monitor_finish_cache
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.companyCode != null">
        company_code = #{record.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.businessType != null">
        business_type = #{record.businessType,jdbcType=TINYINT},
      </if>
      <if test="record.planStartTime != null">
        plan_start_time = #{record.planStartTime,jdbcType=DATE},
      </if>
      <if test="record.startTime != null">
        start_time = #{record.startTime,jdbcType=DATE},
      </if>
      <if test="record.finishTime != null">
        finish_time = #{record.finishTime,jdbcType=DATE},
      </if>
      <if test="record.targetRate != null">
        target_rate = #{record.targetRate,jdbcType=DECIMAL},
      </if>
      <if test="record.delayTime != null">
        delay_time = #{record.delayTime,jdbcType=INTEGER},
      </if>
      <if test="record.diffQuantity != null">
        diff_quantity = #{record.diffQuantity,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedName != null">
        updated_name = #{record.updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update iscm_push_replenishment_monitor_finish_cache
    set id = #{record.id,jdbcType=BIGINT},
      company_code = #{record.companyCode,jdbcType=VARCHAR},
      business_type = #{record.businessType,jdbcType=TINYINT},
      plan_start_time = #{record.planStartTime,jdbcType=DATE},
      start_time = #{record.startTime,jdbcType=DATE},
      finish_time = #{record.finishTime,jdbcType=DATE},
      target_rate = #{record.targetRate,jdbcType=DECIMAL},
      delay_time = #{record.delayTime,jdbcType=INTEGER},
      diff_quantity = #{record.diffQuantity,jdbcType=INTEGER},
      `status` = #{record.status,jdbcType=TINYINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_name = #{record.createdName,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_name = #{record.updatedName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.iscm.entity.IscmPushReplenishmentMonitorFinishCache">
    update iscm_push_replenishment_monitor_finish_cache
    <set>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        business_type = #{businessType,jdbcType=TINYINT},
      </if>
      <if test="planStartTime != null">
        plan_start_time = #{planStartTime,jdbcType=DATE},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=DATE},
      </if>
      <if test="finishTime != null">
        finish_time = #{finishTime,jdbcType=DATE},
      </if>
      <if test="targetRate != null">
        target_rate = #{targetRate,jdbcType=DECIMAL},
      </if>
      <if test="delayTime != null">
        delay_time = #{delayTime,jdbcType=INTEGER},
      </if>
      <if test="diffQuantity != null">
        diff_quantity = #{diffQuantity,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        updated_name = #{updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.iscm.entity.IscmPushReplenishmentMonitorFinishCache">
    update iscm_push_replenishment_monitor_finish_cache
    set company_code = #{companyCode,jdbcType=VARCHAR},
      business_type = #{businessType,jdbcType=TINYINT},
      plan_start_time = #{planStartTime,jdbcType=DATE},
      start_time = #{startTime,jdbcType=DATE},
      finish_time = #{finishTime,jdbcType=DATE},
      target_rate = #{targetRate,jdbcType=DECIMAL},
      delay_time = #{delayTime,jdbcType=INTEGER},
      diff_quantity = #{diffQuantity,jdbcType=INTEGER},
      `status` = #{status,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_name = #{createdName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_name = #{updatedName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>