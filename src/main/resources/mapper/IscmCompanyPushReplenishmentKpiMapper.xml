<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.IscmCompanyPushReplenishmentKpiMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.IscmCompanyPushReplenishmentKpi">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="bdp_company_code" jdbcType="VARCHAR" property="bdpCompanyCode" />
    <result column="org_id" jdbcType="BIGINT" property="orgId" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="platform_org_id" jdbcType="BIGINT" property="platformOrgId" />
    <result column="platform_org_name" jdbcType="VARCHAR" property="platformOrgName" />
    <result column="company_org_id" jdbcType="BIGINT" property="companyOrgId" />
    <result column="company_business_id" jdbcType="BIGINT" property="companyBusinessId" />
    <result column="company_org_name" jdbcType="VARCHAR" property="companyOrgName" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="model_version" jdbcType="VARCHAR" property="modelVersion" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="real_sales" jdbcType="DECIMAL" property="realSales" />
    <result column="wmape" jdbcType="DECIMAL" property="wmape" />
    <result column="forecast_sales" jdbcType="DECIMAL" property="forecastSales" />
    <result column="forecast_date" jdbcType="TIMESTAMP" property="forecastDate" />
    <result column="bar_code" jdbcType="VARCHAR" property="barCode" />
    <result column="goods_common_name" jdbcType="VARCHAR" property="goodsCommonName" />
    <result column="goods_unit" jdbcType="VARCHAR" property="goodsUnit" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="specifications" jdbcType="VARCHAR" property="specifications" />
    <result column="dosage_form" jdbcType="VARCHAR" property="dosageForm" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="approval_number" jdbcType="VARCHAR" property="approvalNumber" />
    <result column="habitat" jdbcType="VARCHAR" property="habitat" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, bdp_company_code, org_id, org_name, platform_org_id, platform_org_name, company_org_id, 
    company_business_id, company_org_name, company_code, model_version, goods_no, goods_name, 
    real_sales, wmape, forecast_sales, forecast_date, bar_code, goods_common_name, goods_unit, 
    description, specifications, dosage_form, manufacturer, approval_number, habitat, 
    status, gmt_create, gmt_update, extend, version, created_by, created_name, updated_by, 
    updated_name
  </sql>
  <select id="selectByExample" parameterType="com.cowell.iscm.entity.IscmCompanyPushReplenishmentKpiExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from iscm_company_push_replenishment_kpi
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from iscm_company_push_replenishment_kpi
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from iscm_company_push_replenishment_kpi
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.iscm.entity.IscmCompanyPushReplenishmentKpiExample">
    delete from iscm_company_push_replenishment_kpi
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.iscm.entity.IscmCompanyPushReplenishmentKpi">
    insert into iscm_company_push_replenishment_kpi (id, bdp_company_code, org_id, 
      org_name, platform_org_id, platform_org_name, 
      company_org_id, company_business_id, company_org_name, 
      company_code, model_version, goods_no, 
      goods_name, real_sales, wmape, 
      forecast_sales, forecast_date, bar_code, 
      goods_common_name, goods_unit, description, 
      specifications, dosage_form, manufacturer, 
      approval_number, habitat, status, 
      gmt_create, gmt_update, extend, 
      version, created_by, created_name, 
      updated_by, updated_name)
    values (#{id,jdbcType=BIGINT}, #{bdpCompanyCode,jdbcType=VARCHAR}, #{orgId,jdbcType=BIGINT}, 
      #{orgName,jdbcType=VARCHAR}, #{platformOrgId,jdbcType=BIGINT}, #{platformOrgName,jdbcType=VARCHAR}, 
      #{companyOrgId,jdbcType=BIGINT}, #{companyBusinessId,jdbcType=BIGINT}, #{companyOrgName,jdbcType=VARCHAR}, 
      #{companyCode,jdbcType=VARCHAR}, #{modelVersion,jdbcType=VARCHAR}, #{goodsNo,jdbcType=VARCHAR}, 
      #{goodsName,jdbcType=VARCHAR}, #{realSales,jdbcType=DECIMAL}, #{wmape,jdbcType=DECIMAL}, 
      #{forecastSales,jdbcType=DECIMAL}, #{forecastDate,jdbcType=TIMESTAMP}, #{barCode,jdbcType=VARCHAR}, 
      #{goodsCommonName,jdbcType=VARCHAR}, #{goodsUnit,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, 
      #{specifications,jdbcType=VARCHAR}, #{dosageForm,jdbcType=VARCHAR}, #{manufacturer,jdbcType=VARCHAR}, 
      #{approvalNumber,jdbcType=VARCHAR}, #{habitat,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}, #{extend,jdbcType=VARCHAR}, 
      #{version,jdbcType=INTEGER}, #{createdBy,jdbcType=BIGINT}, #{createdName,jdbcType=VARCHAR}, 
      #{updatedBy,jdbcType=BIGINT}, #{updatedName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.iscm.entity.IscmCompanyPushReplenishmentKpi">
    insert into iscm_company_push_replenishment_kpi
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="bdpCompanyCode != null">
        bdp_company_code,
      </if>
      <if test="orgId != null">
        org_id,
      </if>
      <if test="orgName != null">
        org_name,
      </if>
      <if test="platformOrgId != null">
        platform_org_id,
      </if>
      <if test="platformOrgName != null">
        platform_org_name,
      </if>
      <if test="companyOrgId != null">
        company_org_id,
      </if>
      <if test="companyBusinessId != null">
        company_business_id,
      </if>
      <if test="companyOrgName != null">
        company_org_name,
      </if>
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="modelVersion != null">
        model_version,
      </if>
      <if test="goodsNo != null">
        goods_no,
      </if>
      <if test="goodsName != null">
        goods_name,
      </if>
      <if test="realSales != null">
        real_sales,
      </if>
      <if test="wmape != null">
        wmape,
      </if>
      <if test="forecastSales != null">
        forecast_sales,
      </if>
      <if test="forecastDate != null">
        forecast_date,
      </if>
      <if test="barCode != null">
        bar_code,
      </if>
      <if test="goodsCommonName != null">
        goods_common_name,
      </if>
      <if test="goodsUnit != null">
        goods_unit,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="specifications != null">
        specifications,
      </if>
      <if test="dosageForm != null">
        dosage_form,
      </if>
      <if test="manufacturer != null">
        manufacturer,
      </if>
      <if test="approvalNumber != null">
        approval_number,
      </if>
      <if test="habitat != null">
        habitat,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedName != null">
        updated_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="bdpCompanyCode != null">
        #{bdpCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=BIGINT},
      </if>
      <if test="orgName != null">
        #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="platformOrgId != null">
        #{platformOrgId,jdbcType=BIGINT},
      </if>
      <if test="platformOrgName != null">
        #{platformOrgName,jdbcType=VARCHAR},
      </if>
      <if test="companyOrgId != null">
        #{companyOrgId,jdbcType=BIGINT},
      </if>
      <if test="companyBusinessId != null">
        #{companyBusinessId,jdbcType=BIGINT},
      </if>
      <if test="companyOrgName != null">
        #{companyOrgName,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="modelVersion != null">
        #{modelVersion,jdbcType=VARCHAR},
      </if>
      <if test="goodsNo != null">
        #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="realSales != null">
        #{realSales,jdbcType=DECIMAL},
      </if>
      <if test="wmape != null">
        #{wmape,jdbcType=DECIMAL},
      </if>
      <if test="forecastSales != null">
        #{forecastSales,jdbcType=DECIMAL},
      </if>
      <if test="forecastDate != null">
        #{forecastDate,jdbcType=TIMESTAMP},
      </if>
      <if test="barCode != null">
        #{barCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsCommonName != null">
        #{goodsCommonName,jdbcType=VARCHAR},
      </if>
      <if test="goodsUnit != null">
        #{goodsUnit,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="specifications != null">
        #{specifications,jdbcType=VARCHAR},
      </if>
      <if test="dosageForm != null">
        #{dosageForm,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="approvalNumber != null">
        #{approvalNumber,jdbcType=VARCHAR},
      </if>
      <if test="habitat != null">
        #{habitat,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        #{updatedName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.iscm.entity.IscmCompanyPushReplenishmentKpiExample" resultType="java.lang.Long">
    select count(*) from iscm_company_push_replenishment_kpi
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update iscm_company_push_replenishment_kpi
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.bdpCompanyCode != null">
        bdp_company_code = #{record.bdpCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.orgId != null">
        org_id = #{record.orgId,jdbcType=BIGINT},
      </if>
      <if test="record.orgName != null">
        org_name = #{record.orgName,jdbcType=VARCHAR},
      </if>
      <if test="record.platformOrgId != null">
        platform_org_id = #{record.platformOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.platformOrgName != null">
        platform_org_name = #{record.platformOrgName,jdbcType=VARCHAR},
      </if>
      <if test="record.companyOrgId != null">
        company_org_id = #{record.companyOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.companyBusinessId != null">
        company_business_id = #{record.companyBusinessId,jdbcType=BIGINT},
      </if>
      <if test="record.companyOrgName != null">
        company_org_name = #{record.companyOrgName,jdbcType=VARCHAR},
      </if>
      <if test="record.companyCode != null">
        company_code = #{record.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.modelVersion != null">
        model_version = #{record.modelVersion,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsNo != null">
        goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsName != null">
        goods_name = #{record.goodsName,jdbcType=VARCHAR},
      </if>
      <if test="record.realSales != null">
        real_sales = #{record.realSales,jdbcType=DECIMAL},
      </if>
      <if test="record.wmape != null">
        wmape = #{record.wmape,jdbcType=DECIMAL},
      </if>
      <if test="record.forecastSales != null">
        forecast_sales = #{record.forecastSales,jdbcType=DECIMAL},
      </if>
      <if test="record.forecastDate != null">
        forecast_date = #{record.forecastDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.barCode != null">
        bar_code = #{record.barCode,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsCommonName != null">
        goods_common_name = #{record.goodsCommonName,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsUnit != null">
        goods_unit = #{record.goodsUnit,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.specifications != null">
        specifications = #{record.specifications,jdbcType=VARCHAR},
      </if>
      <if test="record.dosageForm != null">
        dosage_form = #{record.dosageForm,jdbcType=VARCHAR},
      </if>
      <if test="record.manufacturer != null">
        manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="record.approvalNumber != null">
        approval_number = #{record.approvalNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.habitat != null">
        habitat = #{record.habitat,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedName != null">
        updated_name = #{record.updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update iscm_company_push_replenishment_kpi
    set id = #{record.id,jdbcType=BIGINT},
      bdp_company_code = #{record.bdpCompanyCode,jdbcType=VARCHAR},
      org_id = #{record.orgId,jdbcType=BIGINT},
      org_name = #{record.orgName,jdbcType=VARCHAR},
      platform_org_id = #{record.platformOrgId,jdbcType=BIGINT},
      platform_org_name = #{record.platformOrgName,jdbcType=VARCHAR},
      company_org_id = #{record.companyOrgId,jdbcType=BIGINT},
      company_business_id = #{record.companyBusinessId,jdbcType=BIGINT},
      company_org_name = #{record.companyOrgName,jdbcType=VARCHAR},
      company_code = #{record.companyCode,jdbcType=VARCHAR},
      model_version = #{record.modelVersion,jdbcType=VARCHAR},
      goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      goods_name = #{record.goodsName,jdbcType=VARCHAR},
      real_sales = #{record.realSales,jdbcType=DECIMAL},
      wmape = #{record.wmape,jdbcType=DECIMAL},
      forecast_sales = #{record.forecastSales,jdbcType=DECIMAL},
      forecast_date = #{record.forecastDate,jdbcType=TIMESTAMP},
      bar_code = #{record.barCode,jdbcType=VARCHAR},
      goods_common_name = #{record.goodsCommonName,jdbcType=VARCHAR},
      goods_unit = #{record.goodsUnit,jdbcType=VARCHAR},
      description = #{record.description,jdbcType=VARCHAR},
      specifications = #{record.specifications,jdbcType=VARCHAR},
      dosage_form = #{record.dosageForm,jdbcType=VARCHAR},
      manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      approval_number = #{record.approvalNumber,jdbcType=VARCHAR},
      habitat = #{record.habitat,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=TINYINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_name = #{record.createdName,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_name = #{record.updatedName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.iscm.entity.IscmCompanyPushReplenishmentKpi">
    update iscm_company_push_replenishment_kpi
    <set>
      <if test="bdpCompanyCode != null">
        bdp_company_code = #{bdpCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null">
        org_id = #{orgId,jdbcType=BIGINT},
      </if>
      <if test="orgName != null">
        org_name = #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="platformOrgId != null">
        platform_org_id = #{platformOrgId,jdbcType=BIGINT},
      </if>
      <if test="platformOrgName != null">
        platform_org_name = #{platformOrgName,jdbcType=VARCHAR},
      </if>
      <if test="companyOrgId != null">
        company_org_id = #{companyOrgId,jdbcType=BIGINT},
      </if>
      <if test="companyBusinessId != null">
        company_business_id = #{companyBusinessId,jdbcType=BIGINT},
      </if>
      <if test="companyOrgName != null">
        company_org_name = #{companyOrgName,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="modelVersion != null">
        model_version = #{modelVersion,jdbcType=VARCHAR},
      </if>
      <if test="goodsNo != null">
        goods_no = #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        goods_name = #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="realSales != null">
        real_sales = #{realSales,jdbcType=DECIMAL},
      </if>
      <if test="wmape != null">
        wmape = #{wmape,jdbcType=DECIMAL},
      </if>
      <if test="forecastSales != null">
        forecast_sales = #{forecastSales,jdbcType=DECIMAL},
      </if>
      <if test="forecastDate != null">
        forecast_date = #{forecastDate,jdbcType=TIMESTAMP},
      </if>
      <if test="barCode != null">
        bar_code = #{barCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsCommonName != null">
        goods_common_name = #{goodsCommonName,jdbcType=VARCHAR},
      </if>
      <if test="goodsUnit != null">
        goods_unit = #{goodsUnit,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="specifications != null">
        specifications = #{specifications,jdbcType=VARCHAR},
      </if>
      <if test="dosageForm != null">
        dosage_form = #{dosageForm,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        manufacturer = #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="approvalNumber != null">
        approval_number = #{approvalNumber,jdbcType=VARCHAR},
      </if>
      <if test="habitat != null">
        habitat = #{habitat,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        updated_name = #{updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.iscm.entity.IscmCompanyPushReplenishmentKpi">
    update iscm_company_push_replenishment_kpi
    set bdp_company_code = #{bdpCompanyCode,jdbcType=VARCHAR},
      org_id = #{orgId,jdbcType=BIGINT},
      org_name = #{orgName,jdbcType=VARCHAR},
      platform_org_id = #{platformOrgId,jdbcType=BIGINT},
      platform_org_name = #{platformOrgName,jdbcType=VARCHAR},
      company_org_id = #{companyOrgId,jdbcType=BIGINT},
      company_business_id = #{companyBusinessId,jdbcType=BIGINT},
      company_org_name = #{companyOrgName,jdbcType=VARCHAR},
      company_code = #{companyCode,jdbcType=VARCHAR},
      model_version = #{modelVersion,jdbcType=VARCHAR},
      goods_no = #{goodsNo,jdbcType=VARCHAR},
      goods_name = #{goodsName,jdbcType=VARCHAR},
      real_sales = #{realSales,jdbcType=DECIMAL},
      wmape = #{wmape,jdbcType=DECIMAL},
      forecast_sales = #{forecastSales,jdbcType=DECIMAL},
      forecast_date = #{forecastDate,jdbcType=TIMESTAMP},
      bar_code = #{barCode,jdbcType=VARCHAR},
      goods_common_name = #{goodsCommonName,jdbcType=VARCHAR},
      goods_unit = #{goodsUnit,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      specifications = #{specifications,jdbcType=VARCHAR},
      dosage_form = #{dosageForm,jdbcType=VARCHAR},
      manufacturer = #{manufacturer,jdbcType=VARCHAR},
      approval_number = #{approvalNumber,jdbcType=VARCHAR},
      habitat = #{habitat,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_name = #{createdName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_name = #{updatedName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>