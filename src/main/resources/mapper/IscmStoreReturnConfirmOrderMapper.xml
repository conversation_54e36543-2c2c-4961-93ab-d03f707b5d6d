<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.IscmStoreReturnConfirmOrderMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.IscmStoreReturnConfirmOrder">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="return_confirm_order_no" jdbcType="VARCHAR" property="returnConfirmOrderNo" />
    <result column="return_business_type" jdbcType="TINYINT" property="returnBusinessType" />
    <result column="platform_org_id" jdbcType="BIGINT" property="platformOrgId" />
    <result column="platform_org_name" jdbcType="VARCHAR" property="platformOrgName" />
    <result column="warehouse_code" jdbcType="VARCHAR" property="warehouseCode" />
    <result column="warehouse_name" jdbcType="VARCHAR" property="warehouseName" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="forecast_sales" jdbcType="DECIMAL" property="forecastSales" />
    <result column="bar_code" jdbcType="VARCHAR" property="barCode" />
    <result column="goods_common_name" jdbcType="VARCHAR" property="goodsCommonName" />
    <result column="goods_unit" jdbcType="VARCHAR" property="goodsUnit" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="specifications" jdbcType="VARCHAR" property="specifications" />
    <result column="dosage_form" jdbcType="VARCHAR" property="dosageForm" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="approval_number" jdbcType="VARCHAR" property="approvalNumber" />
    <result column="goods_class_id" jdbcType="BIGINT" property="goodsClassId" />
    <result column="goods_class_name" jdbcType="VARCHAR" property="goodsClassName" />
    <result column="goods_pur_channel" jdbcType="VARCHAR" property="goodsPurChannel" />
    <result column="warehouse_stock" jdbcType="DECIMAL" property="warehouseStock" />
    <result column="warehouse_purchase_non_clear_stock" jdbcType="DECIMAL" property="warehousePurchaseNonClearStock" />
    <result column="warehouse_return_non_clear_stock" jdbcType="DECIMAL" property="warehouseReturnNonClearStock" />
    <result column="warehouse_return_non_send_stock" jdbcType="DECIMAL" property="warehouseReturnNonSendStock" />
    <result column="warehouse_validity_days_min" jdbcType="INTEGER" property="warehouseValidityDaysMin" />
    <result column="warehouse_consume_days_max" jdbcType="INTEGER" property="warehouseConsumeDaysMax" />
    <result column="warehouse_suggest_receive_return_quantity" jdbcType="DECIMAL" property="warehouseSuggestReceiveReturnQuantity" />
    <result column="warehouse_plan_receive_return_quantity" jdbcType="DECIMAL" property="warehousePlanReceiveReturnQuantity" />
    <result column="return_distr_type" jdbcType="VARCHAR" property="returnDistrType" />
    <result column="register_month" jdbcType="INTEGER" property="registerMonth" />
    <result column="store_quantity" jdbcType="INTEGER" property="storeQuantity" />
    <result column="detail_lines" jdbcType="INTEGER" property="detailLines" />
    <result column="return_amount" jdbcType="DECIMAL" property="returnAmount" />
    <result column="single_goods_return_amount" jdbcType="DECIMAL" property="singleGoodsReturnAmount" />
    <result column="single_goods_return_qty" jdbcType="DECIMAL" property="singleGoodsReturnQty" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, return_confirm_order_no, return_business_type, platform_org_id, platform_org_name, 
    warehouse_code, warehouse_name, goods_no, goods_name, forecast_sales, bar_code, goods_common_name, 
    goods_unit, description, specifications, dosage_form, manufacturer, approval_number, 
    goods_class_id, goods_class_name, goods_pur_channel, warehouse_stock, warehouse_purchase_non_clear_stock, 
    warehouse_return_non_clear_stock, warehouse_return_non_send_stock, warehouse_validity_days_min, 
    warehouse_consume_days_max, warehouse_suggest_receive_return_quantity, warehouse_plan_receive_return_quantity, 
    return_distr_type, register_month, store_quantity, detail_lines, return_amount, single_goods_return_amount, 
    single_goods_return_qty, `status`, gmt_create, gmt_update, extend, version, created_by, 
    created_name, updated_by, updated_name
  </sql>
  <select id="selectByExample" parameterType="com.cowell.iscm.entity.IscmStoreReturnConfirmOrderExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from iscm_store_return_confirm_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from iscm_store_return_confirm_order
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from iscm_store_return_confirm_order
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.iscm.entity.IscmStoreReturnConfirmOrderExample">
    delete from iscm_store_return_confirm_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entity.IscmStoreReturnConfirmOrder" useGeneratedKeys="true">
    insert into iscm_store_return_confirm_order (return_confirm_order_no, return_business_type, 
      platform_org_id, platform_org_name, warehouse_code, 
      warehouse_name, goods_no, goods_name, 
      forecast_sales, bar_code, goods_common_name, 
      goods_unit, description, specifications, 
      dosage_form, manufacturer, approval_number, 
      goods_class_id, goods_class_name, goods_pur_channel, 
      warehouse_stock, warehouse_purchase_non_clear_stock, 
      warehouse_return_non_clear_stock, warehouse_return_non_send_stock, 
      warehouse_validity_days_min, warehouse_consume_days_max, 
      warehouse_suggest_receive_return_quantity, warehouse_plan_receive_return_quantity, 
      return_distr_type, register_month, store_quantity, 
      detail_lines, return_amount, single_goods_return_amount, 
      single_goods_return_qty, `status`, gmt_create, 
      gmt_update, extend, version, 
      created_by, created_name, updated_by, 
      updated_name)
    values (#{returnConfirmOrderNo,jdbcType=VARCHAR}, #{returnBusinessType,jdbcType=TINYINT}, 
      #{platformOrgId,jdbcType=BIGINT}, #{platformOrgName,jdbcType=VARCHAR}, #{warehouseCode,jdbcType=VARCHAR}, 
      #{warehouseName,jdbcType=VARCHAR}, #{goodsNo,jdbcType=VARCHAR}, #{goodsName,jdbcType=VARCHAR}, 
      #{forecastSales,jdbcType=DECIMAL}, #{barCode,jdbcType=VARCHAR}, #{goodsCommonName,jdbcType=VARCHAR}, 
      #{goodsUnit,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, #{specifications,jdbcType=VARCHAR}, 
      #{dosageForm,jdbcType=VARCHAR}, #{manufacturer,jdbcType=VARCHAR}, #{approvalNumber,jdbcType=VARCHAR}, 
      #{goodsClassId,jdbcType=BIGINT}, #{goodsClassName,jdbcType=VARCHAR}, #{goodsPurChannel,jdbcType=VARCHAR}, 
      #{warehouseStock,jdbcType=DECIMAL}, #{warehousePurchaseNonClearStock,jdbcType=DECIMAL}, 
      #{warehouseReturnNonClearStock,jdbcType=DECIMAL}, #{warehouseReturnNonSendStock,jdbcType=DECIMAL}, 
      #{warehouseValidityDaysMin,jdbcType=INTEGER}, #{warehouseConsumeDaysMax,jdbcType=INTEGER}, 
      #{warehouseSuggestReceiveReturnQuantity,jdbcType=DECIMAL}, #{warehousePlanReceiveReturnQuantity,jdbcType=DECIMAL}, 
      #{returnDistrType,jdbcType=VARCHAR}, #{registerMonth,jdbcType=INTEGER}, #{storeQuantity,jdbcType=INTEGER}, 
      #{detailLines,jdbcType=INTEGER}, #{returnAmount,jdbcType=DECIMAL}, #{singleGoodsReturnAmount,jdbcType=DECIMAL}, 
      #{singleGoodsReturnQty,jdbcType=DECIMAL}, #{status,jdbcType=TINYINT}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtUpdate,jdbcType=TIMESTAMP}, #{extend,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER}, 
      #{createdBy,jdbcType=BIGINT}, #{createdName,jdbcType=VARCHAR}, #{updatedBy,jdbcType=BIGINT}, 
      #{updatedName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entity.IscmStoreReturnConfirmOrder" useGeneratedKeys="true">
    insert into iscm_store_return_confirm_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="returnConfirmOrderNo != null">
        return_confirm_order_no,
      </if>
      <if test="returnBusinessType != null">
        return_business_type,
      </if>
      <if test="platformOrgId != null">
        platform_org_id,
      </if>
      <if test="platformOrgName != null">
        platform_org_name,
      </if>
      <if test="warehouseCode != null">
        warehouse_code,
      </if>
      <if test="warehouseName != null">
        warehouse_name,
      </if>
      <if test="goodsNo != null">
        goods_no,
      </if>
      <if test="goodsName != null">
        goods_name,
      </if>
      <if test="forecastSales != null">
        forecast_sales,
      </if>
      <if test="barCode != null">
        bar_code,
      </if>
      <if test="goodsCommonName != null">
        goods_common_name,
      </if>
      <if test="goodsUnit != null">
        goods_unit,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="specifications != null">
        specifications,
      </if>
      <if test="dosageForm != null">
        dosage_form,
      </if>
      <if test="manufacturer != null">
        manufacturer,
      </if>
      <if test="approvalNumber != null">
        approval_number,
      </if>
      <if test="goodsClassId != null">
        goods_class_id,
      </if>
      <if test="goodsClassName != null">
        goods_class_name,
      </if>
      <if test="goodsPurChannel != null">
        goods_pur_channel,
      </if>
      <if test="warehouseStock != null">
        warehouse_stock,
      </if>
      <if test="warehousePurchaseNonClearStock != null">
        warehouse_purchase_non_clear_stock,
      </if>
      <if test="warehouseReturnNonClearStock != null">
        warehouse_return_non_clear_stock,
      </if>
      <if test="warehouseReturnNonSendStock != null">
        warehouse_return_non_send_stock,
      </if>
      <if test="warehouseValidityDaysMin != null">
        warehouse_validity_days_min,
      </if>
      <if test="warehouseConsumeDaysMax != null">
        warehouse_consume_days_max,
      </if>
      <if test="warehouseSuggestReceiveReturnQuantity != null">
        warehouse_suggest_receive_return_quantity,
      </if>
      <if test="warehousePlanReceiveReturnQuantity != null">
        warehouse_plan_receive_return_quantity,
      </if>
      <if test="returnDistrType != null">
        return_distr_type,
      </if>
      <if test="registerMonth != null">
        register_month,
      </if>
      <if test="storeQuantity != null">
        store_quantity,
      </if>
      <if test="detailLines != null">
        detail_lines,
      </if>
      <if test="returnAmount != null">
        return_amount,
      </if>
      <if test="singleGoodsReturnAmount != null">
        single_goods_return_amount,
      </if>
      <if test="singleGoodsReturnQty != null">
        single_goods_return_qty,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedName != null">
        updated_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="returnConfirmOrderNo != null">
        #{returnConfirmOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="returnBusinessType != null">
        #{returnBusinessType,jdbcType=TINYINT},
      </if>
      <if test="platformOrgId != null">
        #{platformOrgId,jdbcType=BIGINT},
      </if>
      <if test="platformOrgName != null">
        #{platformOrgName,jdbcType=VARCHAR},
      </if>
      <if test="warehouseCode != null">
        #{warehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="warehouseName != null">
        #{warehouseName,jdbcType=VARCHAR},
      </if>
      <if test="goodsNo != null">
        #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="forecastSales != null">
        #{forecastSales,jdbcType=DECIMAL},
      </if>
      <if test="barCode != null">
        #{barCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsCommonName != null">
        #{goodsCommonName,jdbcType=VARCHAR},
      </if>
      <if test="goodsUnit != null">
        #{goodsUnit,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="specifications != null">
        #{specifications,jdbcType=VARCHAR},
      </if>
      <if test="dosageForm != null">
        #{dosageForm,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="approvalNumber != null">
        #{approvalNumber,jdbcType=VARCHAR},
      </if>
      <if test="goodsClassId != null">
        #{goodsClassId,jdbcType=BIGINT},
      </if>
      <if test="goodsClassName != null">
        #{goodsClassName,jdbcType=VARCHAR},
      </if>
      <if test="goodsPurChannel != null">
        #{goodsPurChannel,jdbcType=VARCHAR},
      </if>
      <if test="warehouseStock != null">
        #{warehouseStock,jdbcType=DECIMAL},
      </if>
      <if test="warehousePurchaseNonClearStock != null">
        #{warehousePurchaseNonClearStock,jdbcType=DECIMAL},
      </if>
      <if test="warehouseReturnNonClearStock != null">
        #{warehouseReturnNonClearStock,jdbcType=DECIMAL},
      </if>
      <if test="warehouseReturnNonSendStock != null">
        #{warehouseReturnNonSendStock,jdbcType=DECIMAL},
      </if>
      <if test="warehouseValidityDaysMin != null">
        #{warehouseValidityDaysMin,jdbcType=INTEGER},
      </if>
      <if test="warehouseConsumeDaysMax != null">
        #{warehouseConsumeDaysMax,jdbcType=INTEGER},
      </if>
      <if test="warehouseSuggestReceiveReturnQuantity != null">
        #{warehouseSuggestReceiveReturnQuantity,jdbcType=DECIMAL},
      </if>
      <if test="warehousePlanReceiveReturnQuantity != null">
        #{warehousePlanReceiveReturnQuantity,jdbcType=DECIMAL},
      </if>
      <if test="returnDistrType != null">
        #{returnDistrType,jdbcType=VARCHAR},
      </if>
      <if test="registerMonth != null">
        #{registerMonth,jdbcType=INTEGER},
      </if>
      <if test="storeQuantity != null">
        #{storeQuantity,jdbcType=INTEGER},
      </if>
      <if test="detailLines != null">
        #{detailLines,jdbcType=INTEGER},
      </if>
      <if test="returnAmount != null">
        #{returnAmount,jdbcType=DECIMAL},
      </if>
      <if test="singleGoodsReturnAmount != null">
        #{singleGoodsReturnAmount,jdbcType=DECIMAL},
      </if>
      <if test="singleGoodsReturnQty != null">
        #{singleGoodsReturnQty,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        #{updatedName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.iscm.entity.IscmStoreReturnConfirmOrderExample" resultType="java.lang.Long">
    select count(*) from iscm_store_return_confirm_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update iscm_store_return_confirm_order
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.returnConfirmOrderNo != null">
        return_confirm_order_no = #{record.returnConfirmOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.returnBusinessType != null">
        return_business_type = #{record.returnBusinessType,jdbcType=TINYINT},
      </if>
      <if test="record.platformOrgId != null">
        platform_org_id = #{record.platformOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.platformOrgName != null">
        platform_org_name = #{record.platformOrgName,jdbcType=VARCHAR},
      </if>
      <if test="record.warehouseCode != null">
        warehouse_code = #{record.warehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="record.warehouseName != null">
        warehouse_name = #{record.warehouseName,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsNo != null">
        goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsName != null">
        goods_name = #{record.goodsName,jdbcType=VARCHAR},
      </if>
      <if test="record.forecastSales != null">
        forecast_sales = #{record.forecastSales,jdbcType=DECIMAL},
      </if>
      <if test="record.barCode != null">
        bar_code = #{record.barCode,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsCommonName != null">
        goods_common_name = #{record.goodsCommonName,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsUnit != null">
        goods_unit = #{record.goodsUnit,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.specifications != null">
        specifications = #{record.specifications,jdbcType=VARCHAR},
      </if>
      <if test="record.dosageForm != null">
        dosage_form = #{record.dosageForm,jdbcType=VARCHAR},
      </if>
      <if test="record.manufacturer != null">
        manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="record.approvalNumber != null">
        approval_number = #{record.approvalNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsClassId != null">
        goods_class_id = #{record.goodsClassId,jdbcType=BIGINT},
      </if>
      <if test="record.goodsClassName != null">
        goods_class_name = #{record.goodsClassName,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsPurChannel != null">
        goods_pur_channel = #{record.goodsPurChannel,jdbcType=VARCHAR},
      </if>
      <if test="record.warehouseStock != null">
        warehouse_stock = #{record.warehouseStock,jdbcType=DECIMAL},
      </if>
      <if test="record.warehousePurchaseNonClearStock != null">
        warehouse_purchase_non_clear_stock = #{record.warehousePurchaseNonClearStock,jdbcType=DECIMAL},
      </if>
      <if test="record.warehouseReturnNonClearStock != null">
        warehouse_return_non_clear_stock = #{record.warehouseReturnNonClearStock,jdbcType=DECIMAL},
      </if>
      <if test="record.warehouseReturnNonSendStock != null">
        warehouse_return_non_send_stock = #{record.warehouseReturnNonSendStock,jdbcType=DECIMAL},
      </if>
      <if test="record.warehouseValidityDaysMin != null">
        warehouse_validity_days_min = #{record.warehouseValidityDaysMin,jdbcType=INTEGER},
      </if>
      <if test="record.warehouseConsumeDaysMax != null">
        warehouse_consume_days_max = #{record.warehouseConsumeDaysMax,jdbcType=INTEGER},
      </if>
      <if test="record.warehouseSuggestReceiveReturnQuantity != null">
        warehouse_suggest_receive_return_quantity = #{record.warehouseSuggestReceiveReturnQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.warehousePlanReceiveReturnQuantity != null">
        warehouse_plan_receive_return_quantity = #{record.warehousePlanReceiveReturnQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.returnDistrType != null">
        return_distr_type = #{record.returnDistrType,jdbcType=VARCHAR},
      </if>
      <if test="record.registerMonth != null">
        register_month = #{record.registerMonth,jdbcType=INTEGER},
      </if>
      <if test="record.storeQuantity != null">
        store_quantity = #{record.storeQuantity,jdbcType=INTEGER},
      </if>
      <if test="record.detailLines != null">
        detail_lines = #{record.detailLines,jdbcType=INTEGER},
      </if>
      <if test="record.returnAmount != null">
        return_amount = #{record.returnAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.singleGoodsReturnAmount != null">
        single_goods_return_amount = #{record.singleGoodsReturnAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.singleGoodsReturnQty != null">
        single_goods_return_qty = #{record.singleGoodsReturnQty,jdbcType=DECIMAL},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedName != null">
        updated_name = #{record.updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update iscm_store_return_confirm_order
    set id = #{record.id,jdbcType=BIGINT},
      return_confirm_order_no = #{record.returnConfirmOrderNo,jdbcType=VARCHAR},
      return_business_type = #{record.returnBusinessType,jdbcType=TINYINT},
      platform_org_id = #{record.platformOrgId,jdbcType=BIGINT},
      platform_org_name = #{record.platformOrgName,jdbcType=VARCHAR},
      warehouse_code = #{record.warehouseCode,jdbcType=VARCHAR},
      warehouse_name = #{record.warehouseName,jdbcType=VARCHAR},
      goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      goods_name = #{record.goodsName,jdbcType=VARCHAR},
      forecast_sales = #{record.forecastSales,jdbcType=DECIMAL},
      bar_code = #{record.barCode,jdbcType=VARCHAR},
      goods_common_name = #{record.goodsCommonName,jdbcType=VARCHAR},
      goods_unit = #{record.goodsUnit,jdbcType=VARCHAR},
      description = #{record.description,jdbcType=VARCHAR},
      specifications = #{record.specifications,jdbcType=VARCHAR},
      dosage_form = #{record.dosageForm,jdbcType=VARCHAR},
      manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      approval_number = #{record.approvalNumber,jdbcType=VARCHAR},
      goods_class_id = #{record.goodsClassId,jdbcType=BIGINT},
      goods_class_name = #{record.goodsClassName,jdbcType=VARCHAR},
      goods_pur_channel = #{record.goodsPurChannel,jdbcType=VARCHAR},
      warehouse_stock = #{record.warehouseStock,jdbcType=DECIMAL},
      warehouse_purchase_non_clear_stock = #{record.warehousePurchaseNonClearStock,jdbcType=DECIMAL},
      warehouse_return_non_clear_stock = #{record.warehouseReturnNonClearStock,jdbcType=DECIMAL},
      warehouse_return_non_send_stock = #{record.warehouseReturnNonSendStock,jdbcType=DECIMAL},
      warehouse_validity_days_min = #{record.warehouseValidityDaysMin,jdbcType=INTEGER},
      warehouse_consume_days_max = #{record.warehouseConsumeDaysMax,jdbcType=INTEGER},
      warehouse_suggest_receive_return_quantity = #{record.warehouseSuggestReceiveReturnQuantity,jdbcType=DECIMAL},
      warehouse_plan_receive_return_quantity = #{record.warehousePlanReceiveReturnQuantity,jdbcType=DECIMAL},
      return_distr_type = #{record.returnDistrType,jdbcType=VARCHAR},
      register_month = #{record.registerMonth,jdbcType=INTEGER},
      store_quantity = #{record.storeQuantity,jdbcType=INTEGER},
      detail_lines = #{record.detailLines,jdbcType=INTEGER},
      return_amount = #{record.returnAmount,jdbcType=DECIMAL},
      single_goods_return_amount = #{record.singleGoodsReturnAmount,jdbcType=DECIMAL},
      single_goods_return_qty = #{record.singleGoodsReturnQty,jdbcType=DECIMAL},
      `status` = #{record.status,jdbcType=TINYINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_name = #{record.createdName,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_name = #{record.updatedName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.iscm.entity.IscmStoreReturnConfirmOrder">
    update iscm_store_return_confirm_order
    <set>
      <if test="returnConfirmOrderNo != null">
        return_confirm_order_no = #{returnConfirmOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="returnBusinessType != null">
        return_business_type = #{returnBusinessType,jdbcType=TINYINT},
      </if>
      <if test="platformOrgId != null">
        platform_org_id = #{platformOrgId,jdbcType=BIGINT},
      </if>
      <if test="platformOrgName != null">
        platform_org_name = #{platformOrgName,jdbcType=VARCHAR},
      </if>
      <if test="warehouseCode != null">
        warehouse_code = #{warehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="warehouseName != null">
        warehouse_name = #{warehouseName,jdbcType=VARCHAR},
      </if>
      <if test="goodsNo != null">
        goods_no = #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        goods_name = #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="forecastSales != null">
        forecast_sales = #{forecastSales,jdbcType=DECIMAL},
      </if>
      <if test="barCode != null">
        bar_code = #{barCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsCommonName != null">
        goods_common_name = #{goodsCommonName,jdbcType=VARCHAR},
      </if>
      <if test="goodsUnit != null">
        goods_unit = #{goodsUnit,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="specifications != null">
        specifications = #{specifications,jdbcType=VARCHAR},
      </if>
      <if test="dosageForm != null">
        dosage_form = #{dosageForm,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        manufacturer = #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="approvalNumber != null">
        approval_number = #{approvalNumber,jdbcType=VARCHAR},
      </if>
      <if test="goodsClassId != null">
        goods_class_id = #{goodsClassId,jdbcType=BIGINT},
      </if>
      <if test="goodsClassName != null">
        goods_class_name = #{goodsClassName,jdbcType=VARCHAR},
      </if>
      <if test="goodsPurChannel != null">
        goods_pur_channel = #{goodsPurChannel,jdbcType=VARCHAR},
      </if>
      <if test="warehouseStock != null">
        warehouse_stock = #{warehouseStock,jdbcType=DECIMAL},
      </if>
      <if test="warehousePurchaseNonClearStock != null">
        warehouse_purchase_non_clear_stock = #{warehousePurchaseNonClearStock,jdbcType=DECIMAL},
      </if>
      <if test="warehouseReturnNonClearStock != null">
        warehouse_return_non_clear_stock = #{warehouseReturnNonClearStock,jdbcType=DECIMAL},
      </if>
      <if test="warehouseReturnNonSendStock != null">
        warehouse_return_non_send_stock = #{warehouseReturnNonSendStock,jdbcType=DECIMAL},
      </if>
      <if test="warehouseValidityDaysMin != null">
        warehouse_validity_days_min = #{warehouseValidityDaysMin,jdbcType=INTEGER},
      </if>
      <if test="warehouseConsumeDaysMax != null">
        warehouse_consume_days_max = #{warehouseConsumeDaysMax,jdbcType=INTEGER},
      </if>
      <if test="warehouseSuggestReceiveReturnQuantity != null">
        warehouse_suggest_receive_return_quantity = #{warehouseSuggestReceiveReturnQuantity,jdbcType=DECIMAL},
      </if>
      <if test="warehousePlanReceiveReturnQuantity != null">
        warehouse_plan_receive_return_quantity = #{warehousePlanReceiveReturnQuantity,jdbcType=DECIMAL},
      </if>
      <if test="returnDistrType != null">
        return_distr_type = #{returnDistrType,jdbcType=VARCHAR},
      </if>
      <if test="registerMonth != null">
        register_month = #{registerMonth,jdbcType=INTEGER},
      </if>
      <if test="storeQuantity != null">
        store_quantity = #{storeQuantity,jdbcType=INTEGER},
      </if>
      <if test="detailLines != null">
        detail_lines = #{detailLines,jdbcType=INTEGER},
      </if>
      <if test="returnAmount != null">
        return_amount = #{returnAmount,jdbcType=DECIMAL},
      </if>
      <if test="singleGoodsReturnAmount != null">
        single_goods_return_amount = #{singleGoodsReturnAmount,jdbcType=DECIMAL},
      </if>
      <if test="singleGoodsReturnQty != null">
        single_goods_return_qty = #{singleGoodsReturnQty,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        updated_name = #{updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.iscm.entity.IscmStoreReturnConfirmOrder">
    update iscm_store_return_confirm_order
    set return_confirm_order_no = #{returnConfirmOrderNo,jdbcType=VARCHAR},
      return_business_type = #{returnBusinessType,jdbcType=TINYINT},
      platform_org_id = #{platformOrgId,jdbcType=BIGINT},
      platform_org_name = #{platformOrgName,jdbcType=VARCHAR},
      warehouse_code = #{warehouseCode,jdbcType=VARCHAR},
      warehouse_name = #{warehouseName,jdbcType=VARCHAR},
      goods_no = #{goodsNo,jdbcType=VARCHAR},
      goods_name = #{goodsName,jdbcType=VARCHAR},
      forecast_sales = #{forecastSales,jdbcType=DECIMAL},
      bar_code = #{barCode,jdbcType=VARCHAR},
      goods_common_name = #{goodsCommonName,jdbcType=VARCHAR},
      goods_unit = #{goodsUnit,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      specifications = #{specifications,jdbcType=VARCHAR},
      dosage_form = #{dosageForm,jdbcType=VARCHAR},
      manufacturer = #{manufacturer,jdbcType=VARCHAR},
      approval_number = #{approvalNumber,jdbcType=VARCHAR},
      goods_class_id = #{goodsClassId,jdbcType=BIGINT},
      goods_class_name = #{goodsClassName,jdbcType=VARCHAR},
      goods_pur_channel = #{goodsPurChannel,jdbcType=VARCHAR},
      warehouse_stock = #{warehouseStock,jdbcType=DECIMAL},
      warehouse_purchase_non_clear_stock = #{warehousePurchaseNonClearStock,jdbcType=DECIMAL},
      warehouse_return_non_clear_stock = #{warehouseReturnNonClearStock,jdbcType=DECIMAL},
      warehouse_return_non_send_stock = #{warehouseReturnNonSendStock,jdbcType=DECIMAL},
      warehouse_validity_days_min = #{warehouseValidityDaysMin,jdbcType=INTEGER},
      warehouse_consume_days_max = #{warehouseConsumeDaysMax,jdbcType=INTEGER},
      warehouse_suggest_receive_return_quantity = #{warehouseSuggestReceiveReturnQuantity,jdbcType=DECIMAL},
      warehouse_plan_receive_return_quantity = #{warehousePlanReceiveReturnQuantity,jdbcType=DECIMAL},
      return_distr_type = #{returnDistrType,jdbcType=VARCHAR},
      register_month = #{registerMonth,jdbcType=INTEGER},
      store_quantity = #{storeQuantity,jdbcType=INTEGER},
      detail_lines = #{detailLines,jdbcType=INTEGER},
      return_amount = #{returnAmount,jdbcType=DECIMAL},
      single_goods_return_amount = #{singleGoodsReturnAmount,jdbcType=DECIMAL},
      single_goods_return_qty = #{singleGoodsReturnQty,jdbcType=DECIMAL},
      `status` = #{status,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_name = #{createdName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_name = #{updatedName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>