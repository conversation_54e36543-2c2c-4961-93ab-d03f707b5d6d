<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.SapZmmt0288Mapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.SapZmmt0288">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="MANDT" jdbcType="VARCHAR" property="mandt" />
    <result column="ZGUID" jdbcType="VARCHAR" property="zguid" />
    <result column="WERKS" jdbcType="VARCHAR" property="werks" />
    <result column="MATNR" jdbcType="VARCHAR" property="matnr" />
    <result column="EKORG" jdbcType="VARCHAR" property="ekorg" />
    <result column="BSART" jdbcType="VARCHAR" property="bsart" />
    <result column="BUKRS" jdbcType="VARCHAR" property="bukrs" />
    <result column="EKGRP" jdbcType="VARCHAR" property="ekgrp" />
    <result column="NAME1_DC" jdbcType="VARCHAR" property="name1Dc" />
    <result column="LIFNR_XD" jdbcType="VARCHAR" property="lifnrXd" />
    <result column="NAME1_XD" jdbcType="VARCHAR" property="name1Xd" />
    <result column="MENGE_XD" jdbcType="DECIMAL" property="mengeXd" />
    <result column="MEINS_XD" jdbcType="VARCHAR" property="meinsXd" />
    <result column="NETPR_XD" jdbcType="DECIMAL" property="netprXd" />
    <result column="BRTWR_XD" jdbcType="DECIMAL" property="brtwrXd" />
    <result column="NETSUM_XD" jdbcType="DECIMAL" property="netsumXd" />
    <result column="BRTSUM_XD" jdbcType="DECIMAL" property="brtsumXd" />
    <result column="PEINH_XD" jdbcType="DECIMAL" property="peinhXd" />
    <result column="TAX_XD" jdbcType="VARCHAR" property="taxXd" />
    <result column="TAX_XD_T" jdbcType="VARCHAR" property="taxXdT" />
    <result column="KONNR" jdbcType="VARCHAR" property="konnr" />
    <result column="KTPNR" jdbcType="VARCHAR" property="ktpnr" />
    <result column="ZPLCODE" jdbcType="VARCHAR" property="zplcode" />
    <result column="ZPLSUG" jdbcType="VARCHAR" property="zplsug" />
    <result column="ZCJMS" jdbcType="VARCHAR" property="zcjms" />
    <result column="ZTERM" jdbcType="VARCHAR" property="zterm" />
    <result column="ZTERM_T" jdbcType="VARCHAR" property="ztermT" />
    <result column="ZCGBZSZ" jdbcType="VARCHAR" property="zcgbzsz" />
    <result column="MENGE_SUG" jdbcType="DECIMAL" property="mengeSug" />
    <result column="MEINS_SUG" jdbcType="VARCHAR" property="meinsSug" />
    <result column="MENGE_ZBZ" jdbcType="DECIMAL" property="mengeZbz" />
    <result column="MENGE_XBZ" jdbcType="DECIMAL" property="mengeXbz" />
    <result column="MENGE_ZZ" jdbcType="DECIMAL" property="mengeZz" />
    <result column="MEINS_ZZ" jdbcType="VARCHAR" property="meinsZz" />
    <result column="ZZBZL" jdbcType="VARCHAR" property="zzbzl" />
    <result column="ZZCQTY" jdbcType="DECIMAL" property="zzcqty" />
    <result column="ZXSJWXS" jdbcType="DECIMAL" property="zxsjwxs" />
    <result column="ZXXJWXS" jdbcType="DECIMAL" property="zxxjwxs" />
    <result column="KNTTP" jdbcType="VARCHAR" property="knttp" />
    <result column="KOSTL" jdbcType="VARCHAR" property="kostl" />
    <result column="ZUSERID" jdbcType="VARCHAR" property="zuserid" />
    <result column="ZYWXM" jdbcType="VARCHAR" property="zywxm" />
    <result column="ZZHXS" jdbcType="VARCHAR" property="zzhxs" />
    <result column="ZBCDHL" jdbcType="DECIMAL" property="zbcdhl" />
    <result column="ZMDZ" jdbcType="VARCHAR" property="zmdz" />
    <result column="ZMDZ_T" jdbcType="VARCHAR" property="zmdzT" />
    <result column="ZSPJB" jdbcType="DECIMAL" property="zspjb" />
    <result column="EKORG_02" jdbcType="VARCHAR" property="ekorg02" />
    <result column="ZSPCKDDLX" jdbcType="VARCHAR" property="zspckddlx" />
    <result column="ZGHDC" jdbcType="VARCHAR" property="zghdc" />
    <result column="ZZCQTY_BDP" jdbcType="DECIMAL" property="zzcqtyBdp" />
    <result column="ZZBZL_BDP" jdbcType="DECIMAL" property="zzbzlBdp" />
    <result column="ZJHJH" jdbcType="VARCHAR" property="zjhjh" />
    <result column="LGORT" jdbcType="VARCHAR" property="lgort" />
    <result column="ZBZGG" jdbcType="VARCHAR" property="zbzgg" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="ZCRDATE" jdbcType="VARCHAR" property="zcrdate" />
    <result column="ZCRTIME" jdbcType="VARCHAR" property="zcrtime" />
    <result column="ZCRNAME" jdbcType="VARCHAR" property="zcrname" />
    <result column="ZCHDATE" jdbcType="VARCHAR" property="zchdate" />
    <result column="ZCHTIME" jdbcType="VARCHAR" property="zchtime" />
    <result column="ZCHNAME" jdbcType="VARCHAR" property="zchname" />
    <result column="EBELN" jdbcType="VARCHAR" property="ebeln" />
    <result column="EBELP" jdbcType="VARCHAR" property="ebelp" />
    <result column="PURREQNO" jdbcType="VARCHAR" property="purreqno" />
    <result column="STORELINENO" jdbcType="VARCHAR" property="storelineno" />
    <result column="ZBYZD2" jdbcType="VARCHAR" property="zbyzd2" />
    <result column="ZSTOCK_USE" jdbcType="DECIMAL" property="zstockUse" />
    <result column="LABST_DC_JS" jdbcType="DECIMAL" property="labstDcJs" />
    <result column="MENGE_WQ_JS" jdbcType="DECIMAL" property="mengeWqJs" />
    <result column="ZZXL_30_JS" jdbcType="DECIMAL" property="zzxl30Js" />
    <result column="ZMDQTY_JS" jdbcType="DECIMAL" property="zmdqtyJs" />
    <result column="ZTSDDBS" jdbcType="VARCHAR" property="ztsddbs" />
    <result column="ZTSDDYDH" jdbcType="VARCHAR" property="ztsddydh" />
    <result column="ZZQXDJH" jdbcType="VARCHAR" property="zzqxdjh" />
    <result column="ZZQXDHH" jdbcType="VARCHAR" property="zzqxdhh" />
    <result column="ZJTNDJTJ" jdbcType="VARCHAR" property="zjtndjtj" />
    <result column="ZCYJSWQ" jdbcType="DECIMAL" property="zcyjswq" />
    <result column="ZTCWQ" jdbcType="DECIMAL" property="ztcwq" />
    <result column="ZDCKYKC" jdbcType="DECIMAL" property="zdckykc" />
    <result column="MENGE_WQ" jdbcType="DECIMAL" property="mengeWq" />
    <result column="TZTS" jdbcType="INTEGER" property="tzts" />
    <result column="TZTS_YXQZ" jdbcType="VARCHAR" property="tztsYxqz" />
    <result column="ZGYSQZQ" jdbcType="INTEGER" property="zgysqzq" />
    <result column="ZSPQZQ" jdbcType="DECIMAL" property="zspqzq" />
    <result column="ZSPQZQQSRQ" jdbcType="VARCHAR" property="zspqzqqsrq" />
    <result column="ZSPQZQZZRQ" jdbcType="VARCHAR" property="zspqzqzzrq" />
    <result column="ZBCKCTS" jdbcType="DECIMAL" property="zbckcts" />
    <result column="ZBCKCTSQSRQ" jdbcType="VARCHAR" property="zbckctsqsrq" />
    <result column="ZBCKCTSZZRQ" jdbcType="VARCHAR" property="zbckctszzrq" />
    <result column="ZBCDHLQSRQ" jdbcType="VARCHAR" property="zbcdhlqsrq" />
    <result column="ZBCDHLZZRQ" jdbcType="VARCHAR" property="zbcdhlzzrq" />
    <result column="ZSFJS" jdbcType="VARCHAR" property="zsfjs" />
    <result column="ZXQHBS" jdbcType="DECIMAL" property="zxqhbs" />
    <result column="ZAQKCBCTS" jdbcType="DECIMAL" property="zaqkcbcts" />
    <result column="ZSFQZ" jdbcType="VARCHAR" property="zsfqz" />
    <result column="ZTSXSQSRQ" jdbcType="VARCHAR" property="ztsxsqsrq" />
    <result column="ZTSXSJSRQ" jdbcType="VARCHAR" property="ztsxsjsrq" />
    <result column="ZTSXSQZZB" jdbcType="DECIMAL" property="ztsxsqzzb" />
    <result column="ZKCSX" jdbcType="DECIMAL" property="zkcsx" />
    <result column="ZKCXX" jdbcType="DECIMAL" property="zkcxx" />
    <result column="ZCGTPJL" jdbcType="VARCHAR" property="zcgtpjl" />
    <result column="ZCGTPJLXM" jdbcType="VARCHAR" property="zcgtpjlxm" />
    <result column="ZYJYWXDTS" jdbcType="INTEGER" property="zyjywxdts" />
    <result column="BRTWR_LAST" jdbcType="DECIMAL" property="brtwrLast" />
    <result column="PEINH_LAST" jdbcType="DECIMAL" property="peinhLast" />
    <result column="ZTZDCKC" jdbcType="DECIMAL" property="ztzdckc" />
    <result column="ZSTOCK_DC" jdbcType="DECIMAL" property="zstockDc" />
    <result column="ZSTOCK_STORE" jdbcType="DECIMAL" property="zstockStore" />
    <result column="ZZC_INACTIVE_DC_STOCK" jdbcType="DECIMAL" property="zzcInactiveDcStock" />
    <result column="ZSTOCK_TOTAL" jdbcType="DECIMAL" property="zstockTotal" />
    <result column="ZFIX_TBS" jdbcType="DECIMAL" property="zfixTbs" />
    <result column="ZSUM_R_V" jdbcType="DECIMAL" property="zsumRV" />
    <result column="ZDC_INV_UPPER" jdbcType="DECIMAL" property="zdcInvUpper" />
    <result column="ZTZDCDKC1" jdbcType="DECIMAL" property="ztzdcdkc1" />
    <result column="ZTZDCDKC2" jdbcType="DECIMAL" property="ztzdcdkc2" />
    <result column="ZMENGE_WQ7" jdbcType="DECIMAL" property="zmengeWq7" />
    <result column="ZMAX_APPLY" jdbcType="DECIMAL" property="zmaxApply" />
    <result column="ZINV_UPPER" jdbcType="DECIMAL" property="zinvUpper" />
    <result column="ZMENGE_SUG" jdbcType="DECIMAL" property="zmengeSug" />
    <result column="ZDHD_B" jdbcType="DECIMAL" property="zdhdB" />
    <result column="ZDC_L" jdbcType="DECIMAL" property="zdcL" />
    <result column="ZGOODS_L" jdbcType="VARCHAR" property="zgoodsL" />
    <result column="ZS_STOCK" jdbcType="DECIMAL" property="zsStock" />
    <result column="ZRAW" jdbcType="DECIMAL" property="zraw" />
    <result column="ZNONCXB" jdbcType="VARCHAR" property="znoncxb" />
    <result column="ZJM_STORE_STOCK" jdbcType="DECIMAL" property="zjmStoreStock" />
    <result column="ZDHD" jdbcType="DECIMAL" property="zdhd" />
    <result column="ZHTLXBS" jdbcType="VARCHAR" property="zhtlxbs" />
    <result column="REASON_TYPE" jdbcType="VARCHAR" property="reasonType" />
    <result column="ZPYYY" jdbcType="VARCHAR" property="zpyyy" />
    <result column="BRTWR_JY" jdbcType="DECIMAL" property="brtwrJy" />
    <result column="ZHTLXBS_JY" jdbcType="VARCHAR" property="zhtlxbsJy" />
    <result column="ZNCSQWQ" jdbcType="DECIMAL" property="zncsqwq" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, MANDT, ZGUID, WERKS, MATNR, EKORG, BSART, BUKRS, EKGRP, NAME1_DC, LIFNR_XD, NAME1_XD, 
    MENGE_XD, MEINS_XD, NETPR_XD, BRTWR_XD, NETSUM_XD, BRTSUM_XD, PEINH_XD, TAX_XD, TAX_XD_T, 
    KONNR, KTPNR, ZPLCODE, ZPLSUG, ZCJMS, ZTERM, ZTERM_T, ZCGBZSZ, MENGE_SUG, MEINS_SUG, 
    MENGE_ZBZ, MENGE_XBZ, MENGE_ZZ, MEINS_ZZ, ZZBZL, ZZCQTY, ZXSJWXS, ZXXJWXS, KNTTP, 
    KOSTL, ZUSERID, ZYWXM, ZZHXS, ZBCDHL, ZMDZ, ZMDZ_T, ZSPJB, EKORG_02, ZSPCKDDLX, ZGHDC, 
    ZZCQTY_BDP, ZZBZL_BDP, ZJHJH, LGORT, ZBZGG, REMARK, ZCRDATE, ZCRTIME, ZCRNAME, ZCHDATE, 
    ZCHTIME, ZCHNAME, EBELN, EBELP, PURREQNO, STORELINENO, ZBYZD2, ZSTOCK_USE, LABST_DC_JS, 
    MENGE_WQ_JS, ZZXL_30_JS, ZMDQTY_JS, ZTSDDBS, ZTSDDYDH, ZZQXDJH, ZZQXDHH, ZJTNDJTJ, 
    ZCYJSWQ, ZTCWQ, ZDCKYKC, MENGE_WQ, TZTS, TZTS_YXQZ, ZGYSQZQ, ZSPQZQ, ZSPQZQQSRQ, 
    ZSPQZQZZRQ, ZBCKCTS, ZBCKCTSQSRQ, ZBCKCTSZZRQ, ZBCDHLQSRQ, ZBCDHLZZRQ, ZSFJS, ZXQHBS, 
    ZAQKCBCTS, ZSFQZ, ZTSXSQSRQ, ZTSXSJSRQ, ZTSXSQZZB, ZKCSX, ZKCXX, ZCGTPJL, ZCGTPJLXM, 
    ZYJYWXDTS, BRTWR_LAST, PEINH_LAST, ZTZDCKC, ZSTOCK_DC, ZSTOCK_STORE, ZZC_INACTIVE_DC_STOCK, 
    ZSTOCK_TOTAL, ZFIX_TBS, ZSUM_R_V, ZDC_INV_UPPER, ZTZDCDKC1, ZTZDCDKC2, ZMENGE_WQ7, 
    ZMAX_APPLY, ZINV_UPPER, ZMENGE_SUG, ZDHD_B, ZDC_L, ZGOODS_L, ZS_STOCK, ZRAW, ZNONCXB, 
    ZJM_STORE_STOCK, ZDHD, ZHTLXBS, REASON_TYPE, ZPYYY, BRTWR_JY, ZHTLXBS_JY, ZNCSQWQ
  </sql>
  <select id="selectByExample" parameterType="com.cowell.iscm.entity.SapZmmt0288Example" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from SAP_ZMMT0288
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from SAP_ZMMT0288
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from SAP_ZMMT0288
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.iscm.entity.SapZmmt0288Example">
    delete from SAP_ZMMT0288
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entity.SapZmmt0288" useGeneratedKeys="true">
    insert into SAP_ZMMT0288 (MANDT, ZGUID, WERKS, 
      MATNR, EKORG, BSART, 
      BUKRS, EKGRP, NAME1_DC, 
      LIFNR_XD, NAME1_XD, MENGE_XD, 
      MEINS_XD, NETPR_XD, BRTWR_XD, 
      NETSUM_XD, BRTSUM_XD, PEINH_XD, 
      TAX_XD, TAX_XD_T, KONNR, 
      KTPNR, ZPLCODE, ZPLSUG, 
      ZCJMS, ZTERM, ZTERM_T, 
      ZCGBZSZ, MENGE_SUG, MEINS_SUG, 
      MENGE_ZBZ, MENGE_XBZ, MENGE_ZZ, 
      MEINS_ZZ, ZZBZL, ZZCQTY, 
      ZXSJWXS, ZXXJWXS, KNTTP, 
      KOSTL, ZUSERID, ZYWXM, 
      ZZHXS, ZBCDHL, ZMDZ, 
      ZMDZ_T, ZSPJB, EKORG_02, 
      ZSPCKDDLX, ZGHDC, ZZCQTY_BDP, 
      ZZBZL_BDP, ZJHJH, LGORT, 
      ZBZGG, REMARK, ZCRDATE, 
      ZCRTIME, ZCRNAME, ZCHDATE, 
      ZCHTIME, ZCHNAME, EBELN, 
      EBELP, PURREQNO, STORELINENO, 
      ZBYZD2, ZSTOCK_USE, LABST_DC_JS, 
      MENGE_WQ_JS, ZZXL_30_JS, ZMDQTY_JS, 
      ZTSDDBS, ZTSDDYDH, ZZQXDJH, 
      ZZQXDHH, ZJTNDJTJ, ZCYJSWQ, 
      ZTCWQ, ZDCKYKC, MENGE_WQ, 
      TZTS, TZTS_YXQZ, ZGYSQZQ, 
      ZSPQZQ, ZSPQZQQSRQ, ZSPQZQZZRQ, 
      ZBCKCTS, ZBCKCTSQSRQ, ZBCKCTSZZRQ, 
      ZBCDHLQSRQ, ZBCDHLZZRQ, ZSFJS, 
      ZXQHBS, ZAQKCBCTS, ZSFQZ, 
      ZTSXSQSRQ, ZTSXSJSRQ, ZTSXSQZZB, 
      ZKCSX, ZKCXX, ZCGTPJL, 
      ZCGTPJLXM, ZYJYWXDTS, BRTWR_LAST, 
      PEINH_LAST, ZTZDCKC, ZSTOCK_DC, 
      ZSTOCK_STORE, ZZC_INACTIVE_DC_STOCK, ZSTOCK_TOTAL, 
      ZFIX_TBS, ZSUM_R_V, ZDC_INV_UPPER, 
      ZTZDCDKC1, ZTZDCDKC2, ZMENGE_WQ7, 
      ZMAX_APPLY, ZINV_UPPER, ZMENGE_SUG, 
      ZDHD_B, ZDC_L, ZGOODS_L, 
      ZS_STOCK, ZRAW, ZNONCXB, 
      ZJM_STORE_STOCK, ZDHD, ZHTLXBS, 
      REASON_TYPE, ZPYYY, BRTWR_JY, 
      ZHTLXBS_JY, ZNCSQWQ)
    values (#{mandt,jdbcType=VARCHAR}, #{zguid,jdbcType=VARCHAR}, #{werks,jdbcType=VARCHAR}, 
      #{matnr,jdbcType=VARCHAR}, #{ekorg,jdbcType=VARCHAR}, #{bsart,jdbcType=VARCHAR}, 
      #{bukrs,jdbcType=VARCHAR}, #{ekgrp,jdbcType=VARCHAR}, #{name1Dc,jdbcType=VARCHAR}, 
      #{lifnrXd,jdbcType=VARCHAR}, #{name1Xd,jdbcType=VARCHAR}, #{mengeXd,jdbcType=DECIMAL}, 
      #{meinsXd,jdbcType=VARCHAR}, #{netprXd,jdbcType=DECIMAL}, #{brtwrXd,jdbcType=DECIMAL}, 
      #{netsumXd,jdbcType=DECIMAL}, #{brtsumXd,jdbcType=DECIMAL}, #{peinhXd,jdbcType=DECIMAL}, 
      #{taxXd,jdbcType=VARCHAR}, #{taxXdT,jdbcType=VARCHAR}, #{konnr,jdbcType=VARCHAR}, 
      #{ktpnr,jdbcType=VARCHAR}, #{zplcode,jdbcType=VARCHAR}, #{zplsug,jdbcType=VARCHAR}, 
      #{zcjms,jdbcType=VARCHAR}, #{zterm,jdbcType=VARCHAR}, #{ztermT,jdbcType=VARCHAR}, 
      #{zcgbzsz,jdbcType=VARCHAR}, #{mengeSug,jdbcType=DECIMAL}, #{meinsSug,jdbcType=VARCHAR}, 
      #{mengeZbz,jdbcType=DECIMAL}, #{mengeXbz,jdbcType=DECIMAL}, #{mengeZz,jdbcType=DECIMAL}, 
      #{meinsZz,jdbcType=VARCHAR}, #{zzbzl,jdbcType=VARCHAR}, #{zzcqty,jdbcType=DECIMAL}, 
      #{zxsjwxs,jdbcType=DECIMAL}, #{zxxjwxs,jdbcType=DECIMAL}, #{knttp,jdbcType=VARCHAR}, 
      #{kostl,jdbcType=VARCHAR}, #{zuserid,jdbcType=VARCHAR}, #{zywxm,jdbcType=VARCHAR}, 
      #{zzhxs,jdbcType=VARCHAR}, #{zbcdhl,jdbcType=DECIMAL}, #{zmdz,jdbcType=VARCHAR}, 
      #{zmdzT,jdbcType=VARCHAR}, #{zspjb,jdbcType=DECIMAL}, #{ekorg02,jdbcType=VARCHAR}, 
      #{zspckddlx,jdbcType=VARCHAR}, #{zghdc,jdbcType=VARCHAR}, #{zzcqtyBdp,jdbcType=DECIMAL}, 
      #{zzbzlBdp,jdbcType=DECIMAL}, #{zjhjh,jdbcType=VARCHAR}, #{lgort,jdbcType=VARCHAR}, 
      #{zbzgg,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{zcrdate,jdbcType=VARCHAR}, 
      #{zcrtime,jdbcType=VARCHAR}, #{zcrname,jdbcType=VARCHAR}, #{zchdate,jdbcType=VARCHAR}, 
      #{zchtime,jdbcType=VARCHAR}, #{zchname,jdbcType=VARCHAR}, #{ebeln,jdbcType=VARCHAR}, 
      #{ebelp,jdbcType=VARCHAR}, #{purreqno,jdbcType=VARCHAR}, #{storelineno,jdbcType=VARCHAR}, 
      #{zbyzd2,jdbcType=VARCHAR}, #{zstockUse,jdbcType=DECIMAL}, #{labstDcJs,jdbcType=DECIMAL}, 
      #{mengeWqJs,jdbcType=DECIMAL}, #{zzxl30Js,jdbcType=DECIMAL}, #{zmdqtyJs,jdbcType=DECIMAL}, 
      #{ztsddbs,jdbcType=VARCHAR}, #{ztsddydh,jdbcType=VARCHAR}, #{zzqxdjh,jdbcType=VARCHAR}, 
      #{zzqxdhh,jdbcType=VARCHAR}, #{zjtndjtj,jdbcType=VARCHAR}, #{zcyjswq,jdbcType=DECIMAL}, 
      #{ztcwq,jdbcType=DECIMAL}, #{zdckykc,jdbcType=DECIMAL}, #{mengeWq,jdbcType=DECIMAL}, 
      #{tzts,jdbcType=INTEGER}, #{tztsYxqz,jdbcType=VARCHAR}, #{zgysqzq,jdbcType=INTEGER}, 
      #{zspqzq,jdbcType=DECIMAL}, #{zspqzqqsrq,jdbcType=VARCHAR}, #{zspqzqzzrq,jdbcType=VARCHAR}, 
      #{zbckcts,jdbcType=DECIMAL}, #{zbckctsqsrq,jdbcType=VARCHAR}, #{zbckctszzrq,jdbcType=VARCHAR}, 
      #{zbcdhlqsrq,jdbcType=VARCHAR}, #{zbcdhlzzrq,jdbcType=VARCHAR}, #{zsfjs,jdbcType=VARCHAR}, 
      #{zxqhbs,jdbcType=DECIMAL}, #{zaqkcbcts,jdbcType=DECIMAL}, #{zsfqz,jdbcType=VARCHAR}, 
      #{ztsxsqsrq,jdbcType=VARCHAR}, #{ztsxsjsrq,jdbcType=VARCHAR}, #{ztsxsqzzb,jdbcType=DECIMAL}, 
      #{zkcsx,jdbcType=DECIMAL}, #{zkcxx,jdbcType=DECIMAL}, #{zcgtpjl,jdbcType=VARCHAR}, 
      #{zcgtpjlxm,jdbcType=VARCHAR}, #{zyjywxdts,jdbcType=INTEGER}, #{brtwrLast,jdbcType=DECIMAL}, 
      #{peinhLast,jdbcType=DECIMAL}, #{ztzdckc,jdbcType=DECIMAL}, #{zstockDc,jdbcType=DECIMAL}, 
      #{zstockStore,jdbcType=DECIMAL}, #{zzcInactiveDcStock,jdbcType=DECIMAL}, #{zstockTotal,jdbcType=DECIMAL}, 
      #{zfixTbs,jdbcType=DECIMAL}, #{zsumRV,jdbcType=DECIMAL}, #{zdcInvUpper,jdbcType=DECIMAL}, 
      #{ztzdcdkc1,jdbcType=DECIMAL}, #{ztzdcdkc2,jdbcType=DECIMAL}, #{zmengeWq7,jdbcType=DECIMAL}, 
      #{zmaxApply,jdbcType=DECIMAL}, #{zinvUpper,jdbcType=DECIMAL}, #{zmengeSug,jdbcType=DECIMAL}, 
      #{zdhdB,jdbcType=DECIMAL}, #{zdcL,jdbcType=DECIMAL}, #{zgoodsL,jdbcType=VARCHAR}, 
      #{zsStock,jdbcType=DECIMAL}, #{zraw,jdbcType=DECIMAL}, #{znoncxb,jdbcType=VARCHAR}, 
      #{zjmStoreStock,jdbcType=DECIMAL}, #{zdhd,jdbcType=DECIMAL}, #{zhtlxbs,jdbcType=VARCHAR}, 
      #{reasonType,jdbcType=VARCHAR}, #{zpyyy,jdbcType=VARCHAR}, #{brtwrJy,jdbcType=DECIMAL}, 
      #{zhtlxbsJy,jdbcType=VARCHAR}, #{zncsqwq,jdbcType=DECIMAL})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entity.SapZmmt0288" useGeneratedKeys="true">
    insert into SAP_ZMMT0288
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="mandt != null">
        MANDT,
      </if>
      <if test="zguid != null">
        ZGUID,
      </if>
      <if test="werks != null">
        WERKS,
      </if>
      <if test="matnr != null">
        MATNR,
      </if>
      <if test="ekorg != null">
        EKORG,
      </if>
      <if test="bsart != null">
        BSART,
      </if>
      <if test="bukrs != null">
        BUKRS,
      </if>
      <if test="ekgrp != null">
        EKGRP,
      </if>
      <if test="name1Dc != null">
        NAME1_DC,
      </if>
      <if test="lifnrXd != null">
        LIFNR_XD,
      </if>
      <if test="name1Xd != null">
        NAME1_XD,
      </if>
      <if test="mengeXd != null">
        MENGE_XD,
      </if>
      <if test="meinsXd != null">
        MEINS_XD,
      </if>
      <if test="netprXd != null">
        NETPR_XD,
      </if>
      <if test="brtwrXd != null">
        BRTWR_XD,
      </if>
      <if test="netsumXd != null">
        NETSUM_XD,
      </if>
      <if test="brtsumXd != null">
        BRTSUM_XD,
      </if>
      <if test="peinhXd != null">
        PEINH_XD,
      </if>
      <if test="taxXd != null">
        TAX_XD,
      </if>
      <if test="taxXdT != null">
        TAX_XD_T,
      </if>
      <if test="konnr != null">
        KONNR,
      </if>
      <if test="ktpnr != null">
        KTPNR,
      </if>
      <if test="zplcode != null">
        ZPLCODE,
      </if>
      <if test="zplsug != null">
        ZPLSUG,
      </if>
      <if test="zcjms != null">
        ZCJMS,
      </if>
      <if test="zterm != null">
        ZTERM,
      </if>
      <if test="ztermT != null">
        ZTERM_T,
      </if>
      <if test="zcgbzsz != null">
        ZCGBZSZ,
      </if>
      <if test="mengeSug != null">
        MENGE_SUG,
      </if>
      <if test="meinsSug != null">
        MEINS_SUG,
      </if>
      <if test="mengeZbz != null">
        MENGE_ZBZ,
      </if>
      <if test="mengeXbz != null">
        MENGE_XBZ,
      </if>
      <if test="mengeZz != null">
        MENGE_ZZ,
      </if>
      <if test="meinsZz != null">
        MEINS_ZZ,
      </if>
      <if test="zzbzl != null">
        ZZBZL,
      </if>
      <if test="zzcqty != null">
        ZZCQTY,
      </if>
      <if test="zxsjwxs != null">
        ZXSJWXS,
      </if>
      <if test="zxxjwxs != null">
        ZXXJWXS,
      </if>
      <if test="knttp != null">
        KNTTP,
      </if>
      <if test="kostl != null">
        KOSTL,
      </if>
      <if test="zuserid != null">
        ZUSERID,
      </if>
      <if test="zywxm != null">
        ZYWXM,
      </if>
      <if test="zzhxs != null">
        ZZHXS,
      </if>
      <if test="zbcdhl != null">
        ZBCDHL,
      </if>
      <if test="zmdz != null">
        ZMDZ,
      </if>
      <if test="zmdzT != null">
        ZMDZ_T,
      </if>
      <if test="zspjb != null">
        ZSPJB,
      </if>
      <if test="ekorg02 != null">
        EKORG_02,
      </if>
      <if test="zspckddlx != null">
        ZSPCKDDLX,
      </if>
      <if test="zghdc != null">
        ZGHDC,
      </if>
      <if test="zzcqtyBdp != null">
        ZZCQTY_BDP,
      </if>
      <if test="zzbzlBdp != null">
        ZZBZL_BDP,
      </if>
      <if test="zjhjh != null">
        ZJHJH,
      </if>
      <if test="lgort != null">
        LGORT,
      </if>
      <if test="zbzgg != null">
        ZBZGG,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="zcrdate != null">
        ZCRDATE,
      </if>
      <if test="zcrtime != null">
        ZCRTIME,
      </if>
      <if test="zcrname != null">
        ZCRNAME,
      </if>
      <if test="zchdate != null">
        ZCHDATE,
      </if>
      <if test="zchtime != null">
        ZCHTIME,
      </if>
      <if test="zchname != null">
        ZCHNAME,
      </if>
      <if test="ebeln != null">
        EBELN,
      </if>
      <if test="ebelp != null">
        EBELP,
      </if>
      <if test="purreqno != null">
        PURREQNO,
      </if>
      <if test="storelineno != null">
        STORELINENO,
      </if>
      <if test="zbyzd2 != null">
        ZBYZD2,
      </if>
      <if test="zstockUse != null">
        ZSTOCK_USE,
      </if>
      <if test="labstDcJs != null">
        LABST_DC_JS,
      </if>
      <if test="mengeWqJs != null">
        MENGE_WQ_JS,
      </if>
      <if test="zzxl30Js != null">
        ZZXL_30_JS,
      </if>
      <if test="zmdqtyJs != null">
        ZMDQTY_JS,
      </if>
      <if test="ztsddbs != null">
        ZTSDDBS,
      </if>
      <if test="ztsddydh != null">
        ZTSDDYDH,
      </if>
      <if test="zzqxdjh != null">
        ZZQXDJH,
      </if>
      <if test="zzqxdhh != null">
        ZZQXDHH,
      </if>
      <if test="zjtndjtj != null">
        ZJTNDJTJ,
      </if>
      <if test="zcyjswq != null">
        ZCYJSWQ,
      </if>
      <if test="ztcwq != null">
        ZTCWQ,
      </if>
      <if test="zdckykc != null">
        ZDCKYKC,
      </if>
      <if test="mengeWq != null">
        MENGE_WQ,
      </if>
      <if test="tzts != null">
        TZTS,
      </if>
      <if test="tztsYxqz != null">
        TZTS_YXQZ,
      </if>
      <if test="zgysqzq != null">
        ZGYSQZQ,
      </if>
      <if test="zspqzq != null">
        ZSPQZQ,
      </if>
      <if test="zspqzqqsrq != null">
        ZSPQZQQSRQ,
      </if>
      <if test="zspqzqzzrq != null">
        ZSPQZQZZRQ,
      </if>
      <if test="zbckcts != null">
        ZBCKCTS,
      </if>
      <if test="zbckctsqsrq != null">
        ZBCKCTSQSRQ,
      </if>
      <if test="zbckctszzrq != null">
        ZBCKCTSZZRQ,
      </if>
      <if test="zbcdhlqsrq != null">
        ZBCDHLQSRQ,
      </if>
      <if test="zbcdhlzzrq != null">
        ZBCDHLZZRQ,
      </if>
      <if test="zsfjs != null">
        ZSFJS,
      </if>
      <if test="zxqhbs != null">
        ZXQHBS,
      </if>
      <if test="zaqkcbcts != null">
        ZAQKCBCTS,
      </if>
      <if test="zsfqz != null">
        ZSFQZ,
      </if>
      <if test="ztsxsqsrq != null">
        ZTSXSQSRQ,
      </if>
      <if test="ztsxsjsrq != null">
        ZTSXSJSRQ,
      </if>
      <if test="ztsxsqzzb != null">
        ZTSXSQZZB,
      </if>
      <if test="zkcsx != null">
        ZKCSX,
      </if>
      <if test="zkcxx != null">
        ZKCXX,
      </if>
      <if test="zcgtpjl != null">
        ZCGTPJL,
      </if>
      <if test="zcgtpjlxm != null">
        ZCGTPJLXM,
      </if>
      <if test="zyjywxdts != null">
        ZYJYWXDTS,
      </if>
      <if test="brtwrLast != null">
        BRTWR_LAST,
      </if>
      <if test="peinhLast != null">
        PEINH_LAST,
      </if>
      <if test="ztzdckc != null">
        ZTZDCKC,
      </if>
      <if test="zstockDc != null">
        ZSTOCK_DC,
      </if>
      <if test="zstockStore != null">
        ZSTOCK_STORE,
      </if>
      <if test="zzcInactiveDcStock != null">
        ZZC_INACTIVE_DC_STOCK,
      </if>
      <if test="zstockTotal != null">
        ZSTOCK_TOTAL,
      </if>
      <if test="zfixTbs != null">
        ZFIX_TBS,
      </if>
      <if test="zsumRV != null">
        ZSUM_R_V,
      </if>
      <if test="zdcInvUpper != null">
        ZDC_INV_UPPER,
      </if>
      <if test="ztzdcdkc1 != null">
        ZTZDCDKC1,
      </if>
      <if test="ztzdcdkc2 != null">
        ZTZDCDKC2,
      </if>
      <if test="zmengeWq7 != null">
        ZMENGE_WQ7,
      </if>
      <if test="zmaxApply != null">
        ZMAX_APPLY,
      </if>
      <if test="zinvUpper != null">
        ZINV_UPPER,
      </if>
      <if test="zmengeSug != null">
        ZMENGE_SUG,
      </if>
      <if test="zdhdB != null">
        ZDHD_B,
      </if>
      <if test="zdcL != null">
        ZDC_L,
      </if>
      <if test="zgoodsL != null">
        ZGOODS_L,
      </if>
      <if test="zsStock != null">
        ZS_STOCK,
      </if>
      <if test="zraw != null">
        ZRAW,
      </if>
      <if test="znoncxb != null">
        ZNONCXB,
      </if>
      <if test="zjmStoreStock != null">
        ZJM_STORE_STOCK,
      </if>
      <if test="zdhd != null">
        ZDHD,
      </if>
      <if test="zhtlxbs != null">
        ZHTLXBS,
      </if>
      <if test="reasonType != null">
        REASON_TYPE,
      </if>
      <if test="zpyyy != null">
        ZPYYY,
      </if>
      <if test="brtwrJy != null">
        BRTWR_JY,
      </if>
      <if test="zhtlxbsJy != null">
        ZHTLXBS_JY,
      </if>
      <if test="zncsqwq != null">
        ZNCSQWQ,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="mandt != null">
        #{mandt,jdbcType=VARCHAR},
      </if>
      <if test="zguid != null">
        #{zguid,jdbcType=VARCHAR},
      </if>
      <if test="werks != null">
        #{werks,jdbcType=VARCHAR},
      </if>
      <if test="matnr != null">
        #{matnr,jdbcType=VARCHAR},
      </if>
      <if test="ekorg != null">
        #{ekorg,jdbcType=VARCHAR},
      </if>
      <if test="bsart != null">
        #{bsart,jdbcType=VARCHAR},
      </if>
      <if test="bukrs != null">
        #{bukrs,jdbcType=VARCHAR},
      </if>
      <if test="ekgrp != null">
        #{ekgrp,jdbcType=VARCHAR},
      </if>
      <if test="name1Dc != null">
        #{name1Dc,jdbcType=VARCHAR},
      </if>
      <if test="lifnrXd != null">
        #{lifnrXd,jdbcType=VARCHAR},
      </if>
      <if test="name1Xd != null">
        #{name1Xd,jdbcType=VARCHAR},
      </if>
      <if test="mengeXd != null">
        #{mengeXd,jdbcType=DECIMAL},
      </if>
      <if test="meinsXd != null">
        #{meinsXd,jdbcType=VARCHAR},
      </if>
      <if test="netprXd != null">
        #{netprXd,jdbcType=DECIMAL},
      </if>
      <if test="brtwrXd != null">
        #{brtwrXd,jdbcType=DECIMAL},
      </if>
      <if test="netsumXd != null">
        #{netsumXd,jdbcType=DECIMAL},
      </if>
      <if test="brtsumXd != null">
        #{brtsumXd,jdbcType=DECIMAL},
      </if>
      <if test="peinhXd != null">
        #{peinhXd,jdbcType=DECIMAL},
      </if>
      <if test="taxXd != null">
        #{taxXd,jdbcType=VARCHAR},
      </if>
      <if test="taxXdT != null">
        #{taxXdT,jdbcType=VARCHAR},
      </if>
      <if test="konnr != null">
        #{konnr,jdbcType=VARCHAR},
      </if>
      <if test="ktpnr != null">
        #{ktpnr,jdbcType=VARCHAR},
      </if>
      <if test="zplcode != null">
        #{zplcode,jdbcType=VARCHAR},
      </if>
      <if test="zplsug != null">
        #{zplsug,jdbcType=VARCHAR},
      </if>
      <if test="zcjms != null">
        #{zcjms,jdbcType=VARCHAR},
      </if>
      <if test="zterm != null">
        #{zterm,jdbcType=VARCHAR},
      </if>
      <if test="ztermT != null">
        #{ztermT,jdbcType=VARCHAR},
      </if>
      <if test="zcgbzsz != null">
        #{zcgbzsz,jdbcType=VARCHAR},
      </if>
      <if test="mengeSug != null">
        #{mengeSug,jdbcType=DECIMAL},
      </if>
      <if test="meinsSug != null">
        #{meinsSug,jdbcType=VARCHAR},
      </if>
      <if test="mengeZbz != null">
        #{mengeZbz,jdbcType=DECIMAL},
      </if>
      <if test="mengeXbz != null">
        #{mengeXbz,jdbcType=DECIMAL},
      </if>
      <if test="mengeZz != null">
        #{mengeZz,jdbcType=DECIMAL},
      </if>
      <if test="meinsZz != null">
        #{meinsZz,jdbcType=VARCHAR},
      </if>
      <if test="zzbzl != null">
        #{zzbzl,jdbcType=VARCHAR},
      </if>
      <if test="zzcqty != null">
        #{zzcqty,jdbcType=DECIMAL},
      </if>
      <if test="zxsjwxs != null">
        #{zxsjwxs,jdbcType=DECIMAL},
      </if>
      <if test="zxxjwxs != null">
        #{zxxjwxs,jdbcType=DECIMAL},
      </if>
      <if test="knttp != null">
        #{knttp,jdbcType=VARCHAR},
      </if>
      <if test="kostl != null">
        #{kostl,jdbcType=VARCHAR},
      </if>
      <if test="zuserid != null">
        #{zuserid,jdbcType=VARCHAR},
      </if>
      <if test="zywxm != null">
        #{zywxm,jdbcType=VARCHAR},
      </if>
      <if test="zzhxs != null">
        #{zzhxs,jdbcType=VARCHAR},
      </if>
      <if test="zbcdhl != null">
        #{zbcdhl,jdbcType=DECIMAL},
      </if>
      <if test="zmdz != null">
        #{zmdz,jdbcType=VARCHAR},
      </if>
      <if test="zmdzT != null">
        #{zmdzT,jdbcType=VARCHAR},
      </if>
      <if test="zspjb != null">
        #{zspjb,jdbcType=DECIMAL},
      </if>
      <if test="ekorg02 != null">
        #{ekorg02,jdbcType=VARCHAR},
      </if>
      <if test="zspckddlx != null">
        #{zspckddlx,jdbcType=VARCHAR},
      </if>
      <if test="zghdc != null">
        #{zghdc,jdbcType=VARCHAR},
      </if>
      <if test="zzcqtyBdp != null">
        #{zzcqtyBdp,jdbcType=DECIMAL},
      </if>
      <if test="zzbzlBdp != null">
        #{zzbzlBdp,jdbcType=DECIMAL},
      </if>
      <if test="zjhjh != null">
        #{zjhjh,jdbcType=VARCHAR},
      </if>
      <if test="lgort != null">
        #{lgort,jdbcType=VARCHAR},
      </if>
      <if test="zbzgg != null">
        #{zbzgg,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="zcrdate != null">
        #{zcrdate,jdbcType=VARCHAR},
      </if>
      <if test="zcrtime != null">
        #{zcrtime,jdbcType=VARCHAR},
      </if>
      <if test="zcrname != null">
        #{zcrname,jdbcType=VARCHAR},
      </if>
      <if test="zchdate != null">
        #{zchdate,jdbcType=VARCHAR},
      </if>
      <if test="zchtime != null">
        #{zchtime,jdbcType=VARCHAR},
      </if>
      <if test="zchname != null">
        #{zchname,jdbcType=VARCHAR},
      </if>
      <if test="ebeln != null">
        #{ebeln,jdbcType=VARCHAR},
      </if>
      <if test="ebelp != null">
        #{ebelp,jdbcType=VARCHAR},
      </if>
      <if test="purreqno != null">
        #{purreqno,jdbcType=VARCHAR},
      </if>
      <if test="storelineno != null">
        #{storelineno,jdbcType=VARCHAR},
      </if>
      <if test="zbyzd2 != null">
        #{zbyzd2,jdbcType=VARCHAR},
      </if>
      <if test="zstockUse != null">
        #{zstockUse,jdbcType=DECIMAL},
      </if>
      <if test="labstDcJs != null">
        #{labstDcJs,jdbcType=DECIMAL},
      </if>
      <if test="mengeWqJs != null">
        #{mengeWqJs,jdbcType=DECIMAL},
      </if>
      <if test="zzxl30Js != null">
        #{zzxl30Js,jdbcType=DECIMAL},
      </if>
      <if test="zmdqtyJs != null">
        #{zmdqtyJs,jdbcType=DECIMAL},
      </if>
      <if test="ztsddbs != null">
        #{ztsddbs,jdbcType=VARCHAR},
      </if>
      <if test="ztsddydh != null">
        #{ztsddydh,jdbcType=VARCHAR},
      </if>
      <if test="zzqxdjh != null">
        #{zzqxdjh,jdbcType=VARCHAR},
      </if>
      <if test="zzqxdhh != null">
        #{zzqxdhh,jdbcType=VARCHAR},
      </if>
      <if test="zjtndjtj != null">
        #{zjtndjtj,jdbcType=VARCHAR},
      </if>
      <if test="zcyjswq != null">
        #{zcyjswq,jdbcType=DECIMAL},
      </if>
      <if test="ztcwq != null">
        #{ztcwq,jdbcType=DECIMAL},
      </if>
      <if test="zdckykc != null">
        #{zdckykc,jdbcType=DECIMAL},
      </if>
      <if test="mengeWq != null">
        #{mengeWq,jdbcType=DECIMAL},
      </if>
      <if test="tzts != null">
        #{tzts,jdbcType=INTEGER},
      </if>
      <if test="tztsYxqz != null">
        #{tztsYxqz,jdbcType=VARCHAR},
      </if>
      <if test="zgysqzq != null">
        #{zgysqzq,jdbcType=INTEGER},
      </if>
      <if test="zspqzq != null">
        #{zspqzq,jdbcType=DECIMAL},
      </if>
      <if test="zspqzqqsrq != null">
        #{zspqzqqsrq,jdbcType=VARCHAR},
      </if>
      <if test="zspqzqzzrq != null">
        #{zspqzqzzrq,jdbcType=VARCHAR},
      </if>
      <if test="zbckcts != null">
        #{zbckcts,jdbcType=DECIMAL},
      </if>
      <if test="zbckctsqsrq != null">
        #{zbckctsqsrq,jdbcType=VARCHAR},
      </if>
      <if test="zbckctszzrq != null">
        #{zbckctszzrq,jdbcType=VARCHAR},
      </if>
      <if test="zbcdhlqsrq != null">
        #{zbcdhlqsrq,jdbcType=VARCHAR},
      </if>
      <if test="zbcdhlzzrq != null">
        #{zbcdhlzzrq,jdbcType=VARCHAR},
      </if>
      <if test="zsfjs != null">
        #{zsfjs,jdbcType=VARCHAR},
      </if>
      <if test="zxqhbs != null">
        #{zxqhbs,jdbcType=DECIMAL},
      </if>
      <if test="zaqkcbcts != null">
        #{zaqkcbcts,jdbcType=DECIMAL},
      </if>
      <if test="zsfqz != null">
        #{zsfqz,jdbcType=VARCHAR},
      </if>
      <if test="ztsxsqsrq != null">
        #{ztsxsqsrq,jdbcType=VARCHAR},
      </if>
      <if test="ztsxsjsrq != null">
        #{ztsxsjsrq,jdbcType=VARCHAR},
      </if>
      <if test="ztsxsqzzb != null">
        #{ztsxsqzzb,jdbcType=DECIMAL},
      </if>
      <if test="zkcsx != null">
        #{zkcsx,jdbcType=DECIMAL},
      </if>
      <if test="zkcxx != null">
        #{zkcxx,jdbcType=DECIMAL},
      </if>
      <if test="zcgtpjl != null">
        #{zcgtpjl,jdbcType=VARCHAR},
      </if>
      <if test="zcgtpjlxm != null">
        #{zcgtpjlxm,jdbcType=VARCHAR},
      </if>
      <if test="zyjywxdts != null">
        #{zyjywxdts,jdbcType=INTEGER},
      </if>
      <if test="brtwrLast != null">
        #{brtwrLast,jdbcType=DECIMAL},
      </if>
      <if test="peinhLast != null">
        #{peinhLast,jdbcType=DECIMAL},
      </if>
      <if test="ztzdckc != null">
        #{ztzdckc,jdbcType=DECIMAL},
      </if>
      <if test="zstockDc != null">
        #{zstockDc,jdbcType=DECIMAL},
      </if>
      <if test="zstockStore != null">
        #{zstockStore,jdbcType=DECIMAL},
      </if>
      <if test="zzcInactiveDcStock != null">
        #{zzcInactiveDcStock,jdbcType=DECIMAL},
      </if>
      <if test="zstockTotal != null">
        #{zstockTotal,jdbcType=DECIMAL},
      </if>
      <if test="zfixTbs != null">
        #{zfixTbs,jdbcType=DECIMAL},
      </if>
      <if test="zsumRV != null">
        #{zsumRV,jdbcType=DECIMAL},
      </if>
      <if test="zdcInvUpper != null">
        #{zdcInvUpper,jdbcType=DECIMAL},
      </if>
      <if test="ztzdcdkc1 != null">
        #{ztzdcdkc1,jdbcType=DECIMAL},
      </if>
      <if test="ztzdcdkc2 != null">
        #{ztzdcdkc2,jdbcType=DECIMAL},
      </if>
      <if test="zmengeWq7 != null">
        #{zmengeWq7,jdbcType=DECIMAL},
      </if>
      <if test="zmaxApply != null">
        #{zmaxApply,jdbcType=DECIMAL},
      </if>
      <if test="zinvUpper != null">
        #{zinvUpper,jdbcType=DECIMAL},
      </if>
      <if test="zmengeSug != null">
        #{zmengeSug,jdbcType=DECIMAL},
      </if>
      <if test="zdhdB != null">
        #{zdhdB,jdbcType=DECIMAL},
      </if>
      <if test="zdcL != null">
        #{zdcL,jdbcType=DECIMAL},
      </if>
      <if test="zgoodsL != null">
        #{zgoodsL,jdbcType=VARCHAR},
      </if>
      <if test="zsStock != null">
        #{zsStock,jdbcType=DECIMAL},
      </if>
      <if test="zraw != null">
        #{zraw,jdbcType=DECIMAL},
      </if>
      <if test="znoncxb != null">
        #{znoncxb,jdbcType=VARCHAR},
      </if>
      <if test="zjmStoreStock != null">
        #{zjmStoreStock,jdbcType=DECIMAL},
      </if>
      <if test="zdhd != null">
        #{zdhd,jdbcType=DECIMAL},
      </if>
      <if test="zhtlxbs != null">
        #{zhtlxbs,jdbcType=VARCHAR},
      </if>
      <if test="reasonType != null">
        #{reasonType,jdbcType=VARCHAR},
      </if>
      <if test="zpyyy != null">
        #{zpyyy,jdbcType=VARCHAR},
      </if>
      <if test="brtwrJy != null">
        #{brtwrJy,jdbcType=DECIMAL},
      </if>
      <if test="zhtlxbsJy != null">
        #{zhtlxbsJy,jdbcType=VARCHAR},
      </if>
      <if test="zncsqwq != null">
        #{zncsqwq,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.iscm.entity.SapZmmt0288Example" resultType="java.lang.Long">
    select count(*) from SAP_ZMMT0288
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update SAP_ZMMT0288
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.mandt != null">
        MANDT = #{record.mandt,jdbcType=VARCHAR},
      </if>
      <if test="record.zguid != null">
        ZGUID = #{record.zguid,jdbcType=VARCHAR},
      </if>
      <if test="record.werks != null">
        WERKS = #{record.werks,jdbcType=VARCHAR},
      </if>
      <if test="record.matnr != null">
        MATNR = #{record.matnr,jdbcType=VARCHAR},
      </if>
      <if test="record.ekorg != null">
        EKORG = #{record.ekorg,jdbcType=VARCHAR},
      </if>
      <if test="record.bsart != null">
        BSART = #{record.bsart,jdbcType=VARCHAR},
      </if>
      <if test="record.bukrs != null">
        BUKRS = #{record.bukrs,jdbcType=VARCHAR},
      </if>
      <if test="record.ekgrp != null">
        EKGRP = #{record.ekgrp,jdbcType=VARCHAR},
      </if>
      <if test="record.name1Dc != null">
        NAME1_DC = #{record.name1Dc,jdbcType=VARCHAR},
      </if>
      <if test="record.lifnrXd != null">
        LIFNR_XD = #{record.lifnrXd,jdbcType=VARCHAR},
      </if>
      <if test="record.name1Xd != null">
        NAME1_XD = #{record.name1Xd,jdbcType=VARCHAR},
      </if>
      <if test="record.mengeXd != null">
        MENGE_XD = #{record.mengeXd,jdbcType=DECIMAL},
      </if>
      <if test="record.meinsXd != null">
        MEINS_XD = #{record.meinsXd,jdbcType=VARCHAR},
      </if>
      <if test="record.netprXd != null">
        NETPR_XD = #{record.netprXd,jdbcType=DECIMAL},
      </if>
      <if test="record.brtwrXd != null">
        BRTWR_XD = #{record.brtwrXd,jdbcType=DECIMAL},
      </if>
      <if test="record.netsumXd != null">
        NETSUM_XD = #{record.netsumXd,jdbcType=DECIMAL},
      </if>
      <if test="record.brtsumXd != null">
        BRTSUM_XD = #{record.brtsumXd,jdbcType=DECIMAL},
      </if>
      <if test="record.peinhXd != null">
        PEINH_XD = #{record.peinhXd,jdbcType=DECIMAL},
      </if>
      <if test="record.taxXd != null">
        TAX_XD = #{record.taxXd,jdbcType=VARCHAR},
      </if>
      <if test="record.taxXdT != null">
        TAX_XD_T = #{record.taxXdT,jdbcType=VARCHAR},
      </if>
      <if test="record.konnr != null">
        KONNR = #{record.konnr,jdbcType=VARCHAR},
      </if>
      <if test="record.ktpnr != null">
        KTPNR = #{record.ktpnr,jdbcType=VARCHAR},
      </if>
      <if test="record.zplcode != null">
        ZPLCODE = #{record.zplcode,jdbcType=VARCHAR},
      </if>
      <if test="record.zplsug != null">
        ZPLSUG = #{record.zplsug,jdbcType=VARCHAR},
      </if>
      <if test="record.zcjms != null">
        ZCJMS = #{record.zcjms,jdbcType=VARCHAR},
      </if>
      <if test="record.zterm != null">
        ZTERM = #{record.zterm,jdbcType=VARCHAR},
      </if>
      <if test="record.ztermT != null">
        ZTERM_T = #{record.ztermT,jdbcType=VARCHAR},
      </if>
      <if test="record.zcgbzsz != null">
        ZCGBZSZ = #{record.zcgbzsz,jdbcType=VARCHAR},
      </if>
      <if test="record.mengeSug != null">
        MENGE_SUG = #{record.mengeSug,jdbcType=DECIMAL},
      </if>
      <if test="record.meinsSug != null">
        MEINS_SUG = #{record.meinsSug,jdbcType=VARCHAR},
      </if>
      <if test="record.mengeZbz != null">
        MENGE_ZBZ = #{record.mengeZbz,jdbcType=DECIMAL},
      </if>
      <if test="record.mengeXbz != null">
        MENGE_XBZ = #{record.mengeXbz,jdbcType=DECIMAL},
      </if>
      <if test="record.mengeZz != null">
        MENGE_ZZ = #{record.mengeZz,jdbcType=DECIMAL},
      </if>
      <if test="record.meinsZz != null">
        MEINS_ZZ = #{record.meinsZz,jdbcType=VARCHAR},
      </if>
      <if test="record.zzbzl != null">
        ZZBZL = #{record.zzbzl,jdbcType=VARCHAR},
      </if>
      <if test="record.zzcqty != null">
        ZZCQTY = #{record.zzcqty,jdbcType=DECIMAL},
      </if>
      <if test="record.zxsjwxs != null">
        ZXSJWXS = #{record.zxsjwxs,jdbcType=DECIMAL},
      </if>
      <if test="record.zxxjwxs != null">
        ZXXJWXS = #{record.zxxjwxs,jdbcType=DECIMAL},
      </if>
      <if test="record.knttp != null">
        KNTTP = #{record.knttp,jdbcType=VARCHAR},
      </if>
      <if test="record.kostl != null">
        KOSTL = #{record.kostl,jdbcType=VARCHAR},
      </if>
      <if test="record.zuserid != null">
        ZUSERID = #{record.zuserid,jdbcType=VARCHAR},
      </if>
      <if test="record.zywxm != null">
        ZYWXM = #{record.zywxm,jdbcType=VARCHAR},
      </if>
      <if test="record.zzhxs != null">
        ZZHXS = #{record.zzhxs,jdbcType=VARCHAR},
      </if>
      <if test="record.zbcdhl != null">
        ZBCDHL = #{record.zbcdhl,jdbcType=DECIMAL},
      </if>
      <if test="record.zmdz != null">
        ZMDZ = #{record.zmdz,jdbcType=VARCHAR},
      </if>
      <if test="record.zmdzT != null">
        ZMDZ_T = #{record.zmdzT,jdbcType=VARCHAR},
      </if>
      <if test="record.zspjb != null">
        ZSPJB = #{record.zspjb,jdbcType=DECIMAL},
      </if>
      <if test="record.ekorg02 != null">
        EKORG_02 = #{record.ekorg02,jdbcType=VARCHAR},
      </if>
      <if test="record.zspckddlx != null">
        ZSPCKDDLX = #{record.zspckddlx,jdbcType=VARCHAR},
      </if>
      <if test="record.zghdc != null">
        ZGHDC = #{record.zghdc,jdbcType=VARCHAR},
      </if>
      <if test="record.zzcqtyBdp != null">
        ZZCQTY_BDP = #{record.zzcqtyBdp,jdbcType=DECIMAL},
      </if>
      <if test="record.zzbzlBdp != null">
        ZZBZL_BDP = #{record.zzbzlBdp,jdbcType=DECIMAL},
      </if>
      <if test="record.zjhjh != null">
        ZJHJH = #{record.zjhjh,jdbcType=VARCHAR},
      </if>
      <if test="record.lgort != null">
        LGORT = #{record.lgort,jdbcType=VARCHAR},
      </if>
      <if test="record.zbzgg != null">
        ZBZGG = #{record.zbzgg,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        REMARK = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.zcrdate != null">
        ZCRDATE = #{record.zcrdate,jdbcType=VARCHAR},
      </if>
      <if test="record.zcrtime != null">
        ZCRTIME = #{record.zcrtime,jdbcType=VARCHAR},
      </if>
      <if test="record.zcrname != null">
        ZCRNAME = #{record.zcrname,jdbcType=VARCHAR},
      </if>
      <if test="record.zchdate != null">
        ZCHDATE = #{record.zchdate,jdbcType=VARCHAR},
      </if>
      <if test="record.zchtime != null">
        ZCHTIME = #{record.zchtime,jdbcType=VARCHAR},
      </if>
      <if test="record.zchname != null">
        ZCHNAME = #{record.zchname,jdbcType=VARCHAR},
      </if>
      <if test="record.ebeln != null">
        EBELN = #{record.ebeln,jdbcType=VARCHAR},
      </if>
      <if test="record.ebelp != null">
        EBELP = #{record.ebelp,jdbcType=VARCHAR},
      </if>
      <if test="record.purreqno != null">
        PURREQNO = #{record.purreqno,jdbcType=VARCHAR},
      </if>
      <if test="record.storelineno != null">
        STORELINENO = #{record.storelineno,jdbcType=VARCHAR},
      </if>
      <if test="record.zbyzd2 != null">
        ZBYZD2 = #{record.zbyzd2,jdbcType=VARCHAR},
      </if>
      <if test="record.zstockUse != null">
        ZSTOCK_USE = #{record.zstockUse,jdbcType=DECIMAL},
      </if>
      <if test="record.labstDcJs != null">
        LABST_DC_JS = #{record.labstDcJs,jdbcType=DECIMAL},
      </if>
      <if test="record.mengeWqJs != null">
        MENGE_WQ_JS = #{record.mengeWqJs,jdbcType=DECIMAL},
      </if>
      <if test="record.zzxl30Js != null">
        ZZXL_30_JS = #{record.zzxl30Js,jdbcType=DECIMAL},
      </if>
      <if test="record.zmdqtyJs != null">
        ZMDQTY_JS = #{record.zmdqtyJs,jdbcType=DECIMAL},
      </if>
      <if test="record.ztsddbs != null">
        ZTSDDBS = #{record.ztsddbs,jdbcType=VARCHAR},
      </if>
      <if test="record.ztsddydh != null">
        ZTSDDYDH = #{record.ztsddydh,jdbcType=VARCHAR},
      </if>
      <if test="record.zzqxdjh != null">
        ZZQXDJH = #{record.zzqxdjh,jdbcType=VARCHAR},
      </if>
      <if test="record.zzqxdhh != null">
        ZZQXDHH = #{record.zzqxdhh,jdbcType=VARCHAR},
      </if>
      <if test="record.zjtndjtj != null">
        ZJTNDJTJ = #{record.zjtndjtj,jdbcType=VARCHAR},
      </if>
      <if test="record.zcyjswq != null">
        ZCYJSWQ = #{record.zcyjswq,jdbcType=DECIMAL},
      </if>
      <if test="record.ztcwq != null">
        ZTCWQ = #{record.ztcwq,jdbcType=DECIMAL},
      </if>
      <if test="record.zdckykc != null">
        ZDCKYKC = #{record.zdckykc,jdbcType=DECIMAL},
      </if>
      <if test="record.mengeWq != null">
        MENGE_WQ = #{record.mengeWq,jdbcType=DECIMAL},
      </if>
      <if test="record.tzts != null">
        TZTS = #{record.tzts,jdbcType=INTEGER},
      </if>
      <if test="record.tztsYxqz != null">
        TZTS_YXQZ = #{record.tztsYxqz,jdbcType=VARCHAR},
      </if>
      <if test="record.zgysqzq != null">
        ZGYSQZQ = #{record.zgysqzq,jdbcType=INTEGER},
      </if>
      <if test="record.zspqzq != null">
        ZSPQZQ = #{record.zspqzq,jdbcType=DECIMAL},
      </if>
      <if test="record.zspqzqqsrq != null">
        ZSPQZQQSRQ = #{record.zspqzqqsrq,jdbcType=VARCHAR},
      </if>
      <if test="record.zspqzqzzrq != null">
        ZSPQZQZZRQ = #{record.zspqzqzzrq,jdbcType=VARCHAR},
      </if>
      <if test="record.zbckcts != null">
        ZBCKCTS = #{record.zbckcts,jdbcType=DECIMAL},
      </if>
      <if test="record.zbckctsqsrq != null">
        ZBCKCTSQSRQ = #{record.zbckctsqsrq,jdbcType=VARCHAR},
      </if>
      <if test="record.zbckctszzrq != null">
        ZBCKCTSZZRQ = #{record.zbckctszzrq,jdbcType=VARCHAR},
      </if>
      <if test="record.zbcdhlqsrq != null">
        ZBCDHLQSRQ = #{record.zbcdhlqsrq,jdbcType=VARCHAR},
      </if>
      <if test="record.zbcdhlzzrq != null">
        ZBCDHLZZRQ = #{record.zbcdhlzzrq,jdbcType=VARCHAR},
      </if>
      <if test="record.zsfjs != null">
        ZSFJS = #{record.zsfjs,jdbcType=VARCHAR},
      </if>
      <if test="record.zxqhbs != null">
        ZXQHBS = #{record.zxqhbs,jdbcType=DECIMAL},
      </if>
      <if test="record.zaqkcbcts != null">
        ZAQKCBCTS = #{record.zaqkcbcts,jdbcType=DECIMAL},
      </if>
      <if test="record.zsfqz != null">
        ZSFQZ = #{record.zsfqz,jdbcType=VARCHAR},
      </if>
      <if test="record.ztsxsqsrq != null">
        ZTSXSQSRQ = #{record.ztsxsqsrq,jdbcType=VARCHAR},
      </if>
      <if test="record.ztsxsjsrq != null">
        ZTSXSJSRQ = #{record.ztsxsjsrq,jdbcType=VARCHAR},
      </if>
      <if test="record.ztsxsqzzb != null">
        ZTSXSQZZB = #{record.ztsxsqzzb,jdbcType=DECIMAL},
      </if>
      <if test="record.zkcsx != null">
        ZKCSX = #{record.zkcsx,jdbcType=DECIMAL},
      </if>
      <if test="record.zkcxx != null">
        ZKCXX = #{record.zkcxx,jdbcType=DECIMAL},
      </if>
      <if test="record.zcgtpjl != null">
        ZCGTPJL = #{record.zcgtpjl,jdbcType=VARCHAR},
      </if>
      <if test="record.zcgtpjlxm != null">
        ZCGTPJLXM = #{record.zcgtpjlxm,jdbcType=VARCHAR},
      </if>
      <if test="record.zyjywxdts != null">
        ZYJYWXDTS = #{record.zyjywxdts,jdbcType=INTEGER},
      </if>
      <if test="record.brtwrLast != null">
        BRTWR_LAST = #{record.brtwrLast,jdbcType=DECIMAL},
      </if>
      <if test="record.peinhLast != null">
        PEINH_LAST = #{record.peinhLast,jdbcType=DECIMAL},
      </if>
      <if test="record.ztzdckc != null">
        ZTZDCKC = #{record.ztzdckc,jdbcType=DECIMAL},
      </if>
      <if test="record.zstockDc != null">
        ZSTOCK_DC = #{record.zstockDc,jdbcType=DECIMAL},
      </if>
      <if test="record.zstockStore != null">
        ZSTOCK_STORE = #{record.zstockStore,jdbcType=DECIMAL},
      </if>
      <if test="record.zzcInactiveDcStock != null">
        ZZC_INACTIVE_DC_STOCK = #{record.zzcInactiveDcStock,jdbcType=DECIMAL},
      </if>
      <if test="record.zstockTotal != null">
        ZSTOCK_TOTAL = #{record.zstockTotal,jdbcType=DECIMAL},
      </if>
      <if test="record.zfixTbs != null">
        ZFIX_TBS = #{record.zfixTbs,jdbcType=DECIMAL},
      </if>
      <if test="record.zsumRV != null">
        ZSUM_R_V = #{record.zsumRV,jdbcType=DECIMAL},
      </if>
      <if test="record.zdcInvUpper != null">
        ZDC_INV_UPPER = #{record.zdcInvUpper,jdbcType=DECIMAL},
      </if>
      <if test="record.ztzdcdkc1 != null">
        ZTZDCDKC1 = #{record.ztzdcdkc1,jdbcType=DECIMAL},
      </if>
      <if test="record.ztzdcdkc2 != null">
        ZTZDCDKC2 = #{record.ztzdcdkc2,jdbcType=DECIMAL},
      </if>
      <if test="record.zmengeWq7 != null">
        ZMENGE_WQ7 = #{record.zmengeWq7,jdbcType=DECIMAL},
      </if>
      <if test="record.zmaxApply != null">
        ZMAX_APPLY = #{record.zmaxApply,jdbcType=DECIMAL},
      </if>
      <if test="record.zinvUpper != null">
        ZINV_UPPER = #{record.zinvUpper,jdbcType=DECIMAL},
      </if>
      <if test="record.zmengeSug != null">
        ZMENGE_SUG = #{record.zmengeSug,jdbcType=DECIMAL},
      </if>
      <if test="record.zdhdB != null">
        ZDHD_B = #{record.zdhdB,jdbcType=DECIMAL},
      </if>
      <if test="record.zdcL != null">
        ZDC_L = #{record.zdcL,jdbcType=DECIMAL},
      </if>
      <if test="record.zgoodsL != null">
        ZGOODS_L = #{record.zgoodsL,jdbcType=VARCHAR},
      </if>
      <if test="record.zsStock != null">
        ZS_STOCK = #{record.zsStock,jdbcType=DECIMAL},
      </if>
      <if test="record.zraw != null">
        ZRAW = #{record.zraw,jdbcType=DECIMAL},
      </if>
      <if test="record.znoncxb != null">
        ZNONCXB = #{record.znoncxb,jdbcType=VARCHAR},
      </if>
      <if test="record.zjmStoreStock != null">
        ZJM_STORE_STOCK = #{record.zjmStoreStock,jdbcType=DECIMAL},
      </if>
      <if test="record.zdhd != null">
        ZDHD = #{record.zdhd,jdbcType=DECIMAL},
      </if>
      <if test="record.zhtlxbs != null">
        ZHTLXBS = #{record.zhtlxbs,jdbcType=VARCHAR},
      </if>
      <if test="record.reasonType != null">
        REASON_TYPE = #{record.reasonType,jdbcType=VARCHAR},
      </if>
      <if test="record.zpyyy != null">
        ZPYYY = #{record.zpyyy,jdbcType=VARCHAR},
      </if>
      <if test="record.brtwrJy != null">
        BRTWR_JY = #{record.brtwrJy,jdbcType=DECIMAL},
      </if>
      <if test="record.zhtlxbsJy != null">
        ZHTLXBS_JY = #{record.zhtlxbsJy,jdbcType=VARCHAR},
      </if>
      <if test="record.zncsqwq != null">
        ZNCSQWQ = #{record.zncsqwq,jdbcType=DECIMAL},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update SAP_ZMMT0288
    set id = #{record.id,jdbcType=BIGINT},
      MANDT = #{record.mandt,jdbcType=VARCHAR},
      ZGUID = #{record.zguid,jdbcType=VARCHAR},
      WERKS = #{record.werks,jdbcType=VARCHAR},
      MATNR = #{record.matnr,jdbcType=VARCHAR},
      EKORG = #{record.ekorg,jdbcType=VARCHAR},
      BSART = #{record.bsart,jdbcType=VARCHAR},
      BUKRS = #{record.bukrs,jdbcType=VARCHAR},
      EKGRP = #{record.ekgrp,jdbcType=VARCHAR},
      NAME1_DC = #{record.name1Dc,jdbcType=VARCHAR},
      LIFNR_XD = #{record.lifnrXd,jdbcType=VARCHAR},
      NAME1_XD = #{record.name1Xd,jdbcType=VARCHAR},
      MENGE_XD = #{record.mengeXd,jdbcType=DECIMAL},
      MEINS_XD = #{record.meinsXd,jdbcType=VARCHAR},
      NETPR_XD = #{record.netprXd,jdbcType=DECIMAL},
      BRTWR_XD = #{record.brtwrXd,jdbcType=DECIMAL},
      NETSUM_XD = #{record.netsumXd,jdbcType=DECIMAL},
      BRTSUM_XD = #{record.brtsumXd,jdbcType=DECIMAL},
      PEINH_XD = #{record.peinhXd,jdbcType=DECIMAL},
      TAX_XD = #{record.taxXd,jdbcType=VARCHAR},
      TAX_XD_T = #{record.taxXdT,jdbcType=VARCHAR},
      KONNR = #{record.konnr,jdbcType=VARCHAR},
      KTPNR = #{record.ktpnr,jdbcType=VARCHAR},
      ZPLCODE = #{record.zplcode,jdbcType=VARCHAR},
      ZPLSUG = #{record.zplsug,jdbcType=VARCHAR},
      ZCJMS = #{record.zcjms,jdbcType=VARCHAR},
      ZTERM = #{record.zterm,jdbcType=VARCHAR},
      ZTERM_T = #{record.ztermT,jdbcType=VARCHAR},
      ZCGBZSZ = #{record.zcgbzsz,jdbcType=VARCHAR},
      MENGE_SUG = #{record.mengeSug,jdbcType=DECIMAL},
      MEINS_SUG = #{record.meinsSug,jdbcType=VARCHAR},
      MENGE_ZBZ = #{record.mengeZbz,jdbcType=DECIMAL},
      MENGE_XBZ = #{record.mengeXbz,jdbcType=DECIMAL},
      MENGE_ZZ = #{record.mengeZz,jdbcType=DECIMAL},
      MEINS_ZZ = #{record.meinsZz,jdbcType=VARCHAR},
      ZZBZL = #{record.zzbzl,jdbcType=VARCHAR},
      ZZCQTY = #{record.zzcqty,jdbcType=DECIMAL},
      ZXSJWXS = #{record.zxsjwxs,jdbcType=DECIMAL},
      ZXXJWXS = #{record.zxxjwxs,jdbcType=DECIMAL},
      KNTTP = #{record.knttp,jdbcType=VARCHAR},
      KOSTL = #{record.kostl,jdbcType=VARCHAR},
      ZUSERID = #{record.zuserid,jdbcType=VARCHAR},
      ZYWXM = #{record.zywxm,jdbcType=VARCHAR},
      ZZHXS = #{record.zzhxs,jdbcType=VARCHAR},
      ZBCDHL = #{record.zbcdhl,jdbcType=DECIMAL},
      ZMDZ = #{record.zmdz,jdbcType=VARCHAR},
      ZMDZ_T = #{record.zmdzT,jdbcType=VARCHAR},
      ZSPJB = #{record.zspjb,jdbcType=DECIMAL},
      EKORG_02 = #{record.ekorg02,jdbcType=VARCHAR},
      ZSPCKDDLX = #{record.zspckddlx,jdbcType=VARCHAR},
      ZGHDC = #{record.zghdc,jdbcType=VARCHAR},
      ZZCQTY_BDP = #{record.zzcqtyBdp,jdbcType=DECIMAL},
      ZZBZL_BDP = #{record.zzbzlBdp,jdbcType=DECIMAL},
      ZJHJH = #{record.zjhjh,jdbcType=VARCHAR},
      LGORT = #{record.lgort,jdbcType=VARCHAR},
      ZBZGG = #{record.zbzgg,jdbcType=VARCHAR},
      REMARK = #{record.remark,jdbcType=VARCHAR},
      ZCRDATE = #{record.zcrdate,jdbcType=VARCHAR},
      ZCRTIME = #{record.zcrtime,jdbcType=VARCHAR},
      ZCRNAME = #{record.zcrname,jdbcType=VARCHAR},
      ZCHDATE = #{record.zchdate,jdbcType=VARCHAR},
      ZCHTIME = #{record.zchtime,jdbcType=VARCHAR},
      ZCHNAME = #{record.zchname,jdbcType=VARCHAR},
      EBELN = #{record.ebeln,jdbcType=VARCHAR},
      EBELP = #{record.ebelp,jdbcType=VARCHAR},
      PURREQNO = #{record.purreqno,jdbcType=VARCHAR},
      STORELINENO = #{record.storelineno,jdbcType=VARCHAR},
      ZBYZD2 = #{record.zbyzd2,jdbcType=VARCHAR},
      ZSTOCK_USE = #{record.zstockUse,jdbcType=DECIMAL},
      LABST_DC_JS = #{record.labstDcJs,jdbcType=DECIMAL},
      MENGE_WQ_JS = #{record.mengeWqJs,jdbcType=DECIMAL},
      ZZXL_30_JS = #{record.zzxl30Js,jdbcType=DECIMAL},
      ZMDQTY_JS = #{record.zmdqtyJs,jdbcType=DECIMAL},
      ZTSDDBS = #{record.ztsddbs,jdbcType=VARCHAR},
      ZTSDDYDH = #{record.ztsddydh,jdbcType=VARCHAR},
      ZZQXDJH = #{record.zzqxdjh,jdbcType=VARCHAR},
      ZZQXDHH = #{record.zzqxdhh,jdbcType=VARCHAR},
      ZJTNDJTJ = #{record.zjtndjtj,jdbcType=VARCHAR},
      ZCYJSWQ = #{record.zcyjswq,jdbcType=DECIMAL},
      ZTCWQ = #{record.ztcwq,jdbcType=DECIMAL},
      ZDCKYKC = #{record.zdckykc,jdbcType=DECIMAL},
      MENGE_WQ = #{record.mengeWq,jdbcType=DECIMAL},
      TZTS = #{record.tzts,jdbcType=INTEGER},
      TZTS_YXQZ = #{record.tztsYxqz,jdbcType=VARCHAR},
      ZGYSQZQ = #{record.zgysqzq,jdbcType=INTEGER},
      ZSPQZQ = #{record.zspqzq,jdbcType=DECIMAL},
      ZSPQZQQSRQ = #{record.zspqzqqsrq,jdbcType=VARCHAR},
      ZSPQZQZZRQ = #{record.zspqzqzzrq,jdbcType=VARCHAR},
      ZBCKCTS = #{record.zbckcts,jdbcType=DECIMAL},
      ZBCKCTSQSRQ = #{record.zbckctsqsrq,jdbcType=VARCHAR},
      ZBCKCTSZZRQ = #{record.zbckctszzrq,jdbcType=VARCHAR},
      ZBCDHLQSRQ = #{record.zbcdhlqsrq,jdbcType=VARCHAR},
      ZBCDHLZZRQ = #{record.zbcdhlzzrq,jdbcType=VARCHAR},
      ZSFJS = #{record.zsfjs,jdbcType=VARCHAR},
      ZXQHBS = #{record.zxqhbs,jdbcType=DECIMAL},
      ZAQKCBCTS = #{record.zaqkcbcts,jdbcType=DECIMAL},
      ZSFQZ = #{record.zsfqz,jdbcType=VARCHAR},
      ZTSXSQSRQ = #{record.ztsxsqsrq,jdbcType=VARCHAR},
      ZTSXSJSRQ = #{record.ztsxsjsrq,jdbcType=VARCHAR},
      ZTSXSQZZB = #{record.ztsxsqzzb,jdbcType=DECIMAL},
      ZKCSX = #{record.zkcsx,jdbcType=DECIMAL},
      ZKCXX = #{record.zkcxx,jdbcType=DECIMAL},
      ZCGTPJL = #{record.zcgtpjl,jdbcType=VARCHAR},
      ZCGTPJLXM = #{record.zcgtpjlxm,jdbcType=VARCHAR},
      ZYJYWXDTS = #{record.zyjywxdts,jdbcType=INTEGER},
      BRTWR_LAST = #{record.brtwrLast,jdbcType=DECIMAL},
      PEINH_LAST = #{record.peinhLast,jdbcType=DECIMAL},
      ZTZDCKC = #{record.ztzdckc,jdbcType=DECIMAL},
      ZSTOCK_DC = #{record.zstockDc,jdbcType=DECIMAL},
      ZSTOCK_STORE = #{record.zstockStore,jdbcType=DECIMAL},
      ZZC_INACTIVE_DC_STOCK = #{record.zzcInactiveDcStock,jdbcType=DECIMAL},
      ZSTOCK_TOTAL = #{record.zstockTotal,jdbcType=DECIMAL},
      ZFIX_TBS = #{record.zfixTbs,jdbcType=DECIMAL},
      ZSUM_R_V = #{record.zsumRV,jdbcType=DECIMAL},
      ZDC_INV_UPPER = #{record.zdcInvUpper,jdbcType=DECIMAL},
      ZTZDCDKC1 = #{record.ztzdcdkc1,jdbcType=DECIMAL},
      ZTZDCDKC2 = #{record.ztzdcdkc2,jdbcType=DECIMAL},
      ZMENGE_WQ7 = #{record.zmengeWq7,jdbcType=DECIMAL},
      ZMAX_APPLY = #{record.zmaxApply,jdbcType=DECIMAL},
      ZINV_UPPER = #{record.zinvUpper,jdbcType=DECIMAL},
      ZMENGE_SUG = #{record.zmengeSug,jdbcType=DECIMAL},
      ZDHD_B = #{record.zdhdB,jdbcType=DECIMAL},
      ZDC_L = #{record.zdcL,jdbcType=DECIMAL},
      ZGOODS_L = #{record.zgoodsL,jdbcType=VARCHAR},
      ZS_STOCK = #{record.zsStock,jdbcType=DECIMAL},
      ZRAW = #{record.zraw,jdbcType=DECIMAL},
      ZNONCXB = #{record.znoncxb,jdbcType=VARCHAR},
      ZJM_STORE_STOCK = #{record.zjmStoreStock,jdbcType=DECIMAL},
      ZDHD = #{record.zdhd,jdbcType=DECIMAL},
      ZHTLXBS = #{record.zhtlxbs,jdbcType=VARCHAR},
      REASON_TYPE = #{record.reasonType,jdbcType=VARCHAR},
      ZPYYY = #{record.zpyyy,jdbcType=VARCHAR},
      BRTWR_JY = #{record.brtwrJy,jdbcType=DECIMAL},
      ZHTLXBS_JY = #{record.zhtlxbsJy,jdbcType=VARCHAR},
      ZNCSQWQ = #{record.zncsqwq,jdbcType=DECIMAL}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.iscm.entity.SapZmmt0288">
    update SAP_ZMMT0288
    <set>
      <if test="mandt != null">
        MANDT = #{mandt,jdbcType=VARCHAR},
      </if>
      <if test="zguid != null">
        ZGUID = #{zguid,jdbcType=VARCHAR},
      </if>
      <if test="werks != null">
        WERKS = #{werks,jdbcType=VARCHAR},
      </if>
      <if test="matnr != null">
        MATNR = #{matnr,jdbcType=VARCHAR},
      </if>
      <if test="ekorg != null">
        EKORG = #{ekorg,jdbcType=VARCHAR},
      </if>
      <if test="bsart != null">
        BSART = #{bsart,jdbcType=VARCHAR},
      </if>
      <if test="bukrs != null">
        BUKRS = #{bukrs,jdbcType=VARCHAR},
      </if>
      <if test="ekgrp != null">
        EKGRP = #{ekgrp,jdbcType=VARCHAR},
      </if>
      <if test="name1Dc != null">
        NAME1_DC = #{name1Dc,jdbcType=VARCHAR},
      </if>
      <if test="lifnrXd != null">
        LIFNR_XD = #{lifnrXd,jdbcType=VARCHAR},
      </if>
      <if test="name1Xd != null">
        NAME1_XD = #{name1Xd,jdbcType=VARCHAR},
      </if>
      <if test="mengeXd != null">
        MENGE_XD = #{mengeXd,jdbcType=DECIMAL},
      </if>
      <if test="meinsXd != null">
        MEINS_XD = #{meinsXd,jdbcType=VARCHAR},
      </if>
      <if test="netprXd != null">
        NETPR_XD = #{netprXd,jdbcType=DECIMAL},
      </if>
      <if test="brtwrXd != null">
        BRTWR_XD = #{brtwrXd,jdbcType=DECIMAL},
      </if>
      <if test="netsumXd != null">
        NETSUM_XD = #{netsumXd,jdbcType=DECIMAL},
      </if>
      <if test="brtsumXd != null">
        BRTSUM_XD = #{brtsumXd,jdbcType=DECIMAL},
      </if>
      <if test="peinhXd != null">
        PEINH_XD = #{peinhXd,jdbcType=DECIMAL},
      </if>
      <if test="taxXd != null">
        TAX_XD = #{taxXd,jdbcType=VARCHAR},
      </if>
      <if test="taxXdT != null">
        TAX_XD_T = #{taxXdT,jdbcType=VARCHAR},
      </if>
      <if test="konnr != null">
        KONNR = #{konnr,jdbcType=VARCHAR},
      </if>
      <if test="ktpnr != null">
        KTPNR = #{ktpnr,jdbcType=VARCHAR},
      </if>
      <if test="zplcode != null">
        ZPLCODE = #{zplcode,jdbcType=VARCHAR},
      </if>
      <if test="zplsug != null">
        ZPLSUG = #{zplsug,jdbcType=VARCHAR},
      </if>
      <if test="zcjms != null">
        ZCJMS = #{zcjms,jdbcType=VARCHAR},
      </if>
      <if test="zterm != null">
        ZTERM = #{zterm,jdbcType=VARCHAR},
      </if>
      <if test="ztermT != null">
        ZTERM_T = #{ztermT,jdbcType=VARCHAR},
      </if>
      <if test="zcgbzsz != null">
        ZCGBZSZ = #{zcgbzsz,jdbcType=VARCHAR},
      </if>
      <if test="mengeSug != null">
        MENGE_SUG = #{mengeSug,jdbcType=DECIMAL},
      </if>
      <if test="meinsSug != null">
        MEINS_SUG = #{meinsSug,jdbcType=VARCHAR},
      </if>
      <if test="mengeZbz != null">
        MENGE_ZBZ = #{mengeZbz,jdbcType=DECIMAL},
      </if>
      <if test="mengeXbz != null">
        MENGE_XBZ = #{mengeXbz,jdbcType=DECIMAL},
      </if>
      <if test="mengeZz != null">
        MENGE_ZZ = #{mengeZz,jdbcType=DECIMAL},
      </if>
      <if test="meinsZz != null">
        MEINS_ZZ = #{meinsZz,jdbcType=VARCHAR},
      </if>
      <if test="zzbzl != null">
        ZZBZL = #{zzbzl,jdbcType=VARCHAR},
      </if>
      <if test="zzcqty != null">
        ZZCQTY = #{zzcqty,jdbcType=DECIMAL},
      </if>
      <if test="zxsjwxs != null">
        ZXSJWXS = #{zxsjwxs,jdbcType=DECIMAL},
      </if>
      <if test="zxxjwxs != null">
        ZXXJWXS = #{zxxjwxs,jdbcType=DECIMAL},
      </if>
      <if test="knttp != null">
        KNTTP = #{knttp,jdbcType=VARCHAR},
      </if>
      <if test="kostl != null">
        KOSTL = #{kostl,jdbcType=VARCHAR},
      </if>
      <if test="zuserid != null">
        ZUSERID = #{zuserid,jdbcType=VARCHAR},
      </if>
      <if test="zywxm != null">
        ZYWXM = #{zywxm,jdbcType=VARCHAR},
      </if>
      <if test="zzhxs != null">
        ZZHXS = #{zzhxs,jdbcType=VARCHAR},
      </if>
      <if test="zbcdhl != null">
        ZBCDHL = #{zbcdhl,jdbcType=DECIMAL},
      </if>
      <if test="zmdz != null">
        ZMDZ = #{zmdz,jdbcType=VARCHAR},
      </if>
      <if test="zmdzT != null">
        ZMDZ_T = #{zmdzT,jdbcType=VARCHAR},
      </if>
      <if test="zspjb != null">
        ZSPJB = #{zspjb,jdbcType=DECIMAL},
      </if>
      <if test="ekorg02 != null">
        EKORG_02 = #{ekorg02,jdbcType=VARCHAR},
      </if>
      <if test="zspckddlx != null">
        ZSPCKDDLX = #{zspckddlx,jdbcType=VARCHAR},
      </if>
      <if test="zghdc != null">
        ZGHDC = #{zghdc,jdbcType=VARCHAR},
      </if>
      <if test="zzcqtyBdp != null">
        ZZCQTY_BDP = #{zzcqtyBdp,jdbcType=DECIMAL},
      </if>
      <if test="zzbzlBdp != null">
        ZZBZL_BDP = #{zzbzlBdp,jdbcType=DECIMAL},
      </if>
      <if test="zjhjh != null">
        ZJHJH = #{zjhjh,jdbcType=VARCHAR},
      </if>
      <if test="lgort != null">
        LGORT = #{lgort,jdbcType=VARCHAR},
      </if>
      <if test="zbzgg != null">
        ZBZGG = #{zbzgg,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="zcrdate != null">
        ZCRDATE = #{zcrdate,jdbcType=VARCHAR},
      </if>
      <if test="zcrtime != null">
        ZCRTIME = #{zcrtime,jdbcType=VARCHAR},
      </if>
      <if test="zcrname != null">
        ZCRNAME = #{zcrname,jdbcType=VARCHAR},
      </if>
      <if test="zchdate != null">
        ZCHDATE = #{zchdate,jdbcType=VARCHAR},
      </if>
      <if test="zchtime != null">
        ZCHTIME = #{zchtime,jdbcType=VARCHAR},
      </if>
      <if test="zchname != null">
        ZCHNAME = #{zchname,jdbcType=VARCHAR},
      </if>
      <if test="ebeln != null">
        EBELN = #{ebeln,jdbcType=VARCHAR},
      </if>
      <if test="ebelp != null">
        EBELP = #{ebelp,jdbcType=VARCHAR},
      </if>
      <if test="purreqno != null">
        PURREQNO = #{purreqno,jdbcType=VARCHAR},
      </if>
      <if test="storelineno != null">
        STORELINENO = #{storelineno,jdbcType=VARCHAR},
      </if>
      <if test="zbyzd2 != null">
        ZBYZD2 = #{zbyzd2,jdbcType=VARCHAR},
      </if>
      <if test="zstockUse != null">
        ZSTOCK_USE = #{zstockUse,jdbcType=DECIMAL},
      </if>
      <if test="labstDcJs != null">
        LABST_DC_JS = #{labstDcJs,jdbcType=DECIMAL},
      </if>
      <if test="mengeWqJs != null">
        MENGE_WQ_JS = #{mengeWqJs,jdbcType=DECIMAL},
      </if>
      <if test="zzxl30Js != null">
        ZZXL_30_JS = #{zzxl30Js,jdbcType=DECIMAL},
      </if>
      <if test="zmdqtyJs != null">
        ZMDQTY_JS = #{zmdqtyJs,jdbcType=DECIMAL},
      </if>
      <if test="ztsddbs != null">
        ZTSDDBS = #{ztsddbs,jdbcType=VARCHAR},
      </if>
      <if test="ztsddydh != null">
        ZTSDDYDH = #{ztsddydh,jdbcType=VARCHAR},
      </if>
      <if test="zzqxdjh != null">
        ZZQXDJH = #{zzqxdjh,jdbcType=VARCHAR},
      </if>
      <if test="zzqxdhh != null">
        ZZQXDHH = #{zzqxdhh,jdbcType=VARCHAR},
      </if>
      <if test="zjtndjtj != null">
        ZJTNDJTJ = #{zjtndjtj,jdbcType=VARCHAR},
      </if>
      <if test="zcyjswq != null">
        ZCYJSWQ = #{zcyjswq,jdbcType=DECIMAL},
      </if>
      <if test="ztcwq != null">
        ZTCWQ = #{ztcwq,jdbcType=DECIMAL},
      </if>
      <if test="zdckykc != null">
        ZDCKYKC = #{zdckykc,jdbcType=DECIMAL},
      </if>
      <if test="mengeWq != null">
        MENGE_WQ = #{mengeWq,jdbcType=DECIMAL},
      </if>
      <if test="tzts != null">
        TZTS = #{tzts,jdbcType=INTEGER},
      </if>
      <if test="tztsYxqz != null">
        TZTS_YXQZ = #{tztsYxqz,jdbcType=VARCHAR},
      </if>
      <if test="zgysqzq != null">
        ZGYSQZQ = #{zgysqzq,jdbcType=INTEGER},
      </if>
      <if test="zspqzq != null">
        ZSPQZQ = #{zspqzq,jdbcType=DECIMAL},
      </if>
      <if test="zspqzqqsrq != null">
        ZSPQZQQSRQ = #{zspqzqqsrq,jdbcType=VARCHAR},
      </if>
      <if test="zspqzqzzrq != null">
        ZSPQZQZZRQ = #{zspqzqzzrq,jdbcType=VARCHAR},
      </if>
      <if test="zbckcts != null">
        ZBCKCTS = #{zbckcts,jdbcType=DECIMAL},
      </if>
      <if test="zbckctsqsrq != null">
        ZBCKCTSQSRQ = #{zbckctsqsrq,jdbcType=VARCHAR},
      </if>
      <if test="zbckctszzrq != null">
        ZBCKCTSZZRQ = #{zbckctszzrq,jdbcType=VARCHAR},
      </if>
      <if test="zbcdhlqsrq != null">
        ZBCDHLQSRQ = #{zbcdhlqsrq,jdbcType=VARCHAR},
      </if>
      <if test="zbcdhlzzrq != null">
        ZBCDHLZZRQ = #{zbcdhlzzrq,jdbcType=VARCHAR},
      </if>
      <if test="zsfjs != null">
        ZSFJS = #{zsfjs,jdbcType=VARCHAR},
      </if>
      <if test="zxqhbs != null">
        ZXQHBS = #{zxqhbs,jdbcType=DECIMAL},
      </if>
      <if test="zaqkcbcts != null">
        ZAQKCBCTS = #{zaqkcbcts,jdbcType=DECIMAL},
      </if>
      <if test="zsfqz != null">
        ZSFQZ = #{zsfqz,jdbcType=VARCHAR},
      </if>
      <if test="ztsxsqsrq != null">
        ZTSXSQSRQ = #{ztsxsqsrq,jdbcType=VARCHAR},
      </if>
      <if test="ztsxsjsrq != null">
        ZTSXSJSRQ = #{ztsxsjsrq,jdbcType=VARCHAR},
      </if>
      <if test="ztsxsqzzb != null">
        ZTSXSQZZB = #{ztsxsqzzb,jdbcType=DECIMAL},
      </if>
      <if test="zkcsx != null">
        ZKCSX = #{zkcsx,jdbcType=DECIMAL},
      </if>
      <if test="zkcxx != null">
        ZKCXX = #{zkcxx,jdbcType=DECIMAL},
      </if>
      <if test="zcgtpjl != null">
        ZCGTPJL = #{zcgtpjl,jdbcType=VARCHAR},
      </if>
      <if test="zcgtpjlxm != null">
        ZCGTPJLXM = #{zcgtpjlxm,jdbcType=VARCHAR},
      </if>
      <if test="zyjywxdts != null">
        ZYJYWXDTS = #{zyjywxdts,jdbcType=INTEGER},
      </if>
      <if test="brtwrLast != null">
        BRTWR_LAST = #{brtwrLast,jdbcType=DECIMAL},
      </if>
      <if test="peinhLast != null">
        PEINH_LAST = #{peinhLast,jdbcType=DECIMAL},
      </if>
      <if test="ztzdckc != null">
        ZTZDCKC = #{ztzdckc,jdbcType=DECIMAL},
      </if>
      <if test="zstockDc != null">
        ZSTOCK_DC = #{zstockDc,jdbcType=DECIMAL},
      </if>
      <if test="zstockStore != null">
        ZSTOCK_STORE = #{zstockStore,jdbcType=DECIMAL},
      </if>
      <if test="zzcInactiveDcStock != null">
        ZZC_INACTIVE_DC_STOCK = #{zzcInactiveDcStock,jdbcType=DECIMAL},
      </if>
      <if test="zstockTotal != null">
        ZSTOCK_TOTAL = #{zstockTotal,jdbcType=DECIMAL},
      </if>
      <if test="zfixTbs != null">
        ZFIX_TBS = #{zfixTbs,jdbcType=DECIMAL},
      </if>
      <if test="zsumRV != null">
        ZSUM_R_V = #{zsumRV,jdbcType=DECIMAL},
      </if>
      <if test="zdcInvUpper != null">
        ZDC_INV_UPPER = #{zdcInvUpper,jdbcType=DECIMAL},
      </if>
      <if test="ztzdcdkc1 != null">
        ZTZDCDKC1 = #{ztzdcdkc1,jdbcType=DECIMAL},
      </if>
      <if test="ztzdcdkc2 != null">
        ZTZDCDKC2 = #{ztzdcdkc2,jdbcType=DECIMAL},
      </if>
      <if test="zmengeWq7 != null">
        ZMENGE_WQ7 = #{zmengeWq7,jdbcType=DECIMAL},
      </if>
      <if test="zmaxApply != null">
        ZMAX_APPLY = #{zmaxApply,jdbcType=DECIMAL},
      </if>
      <if test="zinvUpper != null">
        ZINV_UPPER = #{zinvUpper,jdbcType=DECIMAL},
      </if>
      <if test="zmengeSug != null">
        ZMENGE_SUG = #{zmengeSug,jdbcType=DECIMAL},
      </if>
      <if test="zdhdB != null">
        ZDHD_B = #{zdhdB,jdbcType=DECIMAL},
      </if>
      <if test="zdcL != null">
        ZDC_L = #{zdcL,jdbcType=DECIMAL},
      </if>
      <if test="zgoodsL != null">
        ZGOODS_L = #{zgoodsL,jdbcType=VARCHAR},
      </if>
      <if test="zsStock != null">
        ZS_STOCK = #{zsStock,jdbcType=DECIMAL},
      </if>
      <if test="zraw != null">
        ZRAW = #{zraw,jdbcType=DECIMAL},
      </if>
      <if test="znoncxb != null">
        ZNONCXB = #{znoncxb,jdbcType=VARCHAR},
      </if>
      <if test="zjmStoreStock != null">
        ZJM_STORE_STOCK = #{zjmStoreStock,jdbcType=DECIMAL},
      </if>
      <if test="zdhd != null">
        ZDHD = #{zdhd,jdbcType=DECIMAL},
      </if>
      <if test="zhtlxbs != null">
        ZHTLXBS = #{zhtlxbs,jdbcType=VARCHAR},
      </if>
      <if test="reasonType != null">
        REASON_TYPE = #{reasonType,jdbcType=VARCHAR},
      </if>
      <if test="zpyyy != null">
        ZPYYY = #{zpyyy,jdbcType=VARCHAR},
      </if>
      <if test="brtwrJy != null">
        BRTWR_JY = #{brtwrJy,jdbcType=DECIMAL},
      </if>
      <if test="zhtlxbsJy != null">
        ZHTLXBS_JY = #{zhtlxbsJy,jdbcType=VARCHAR},
      </if>
      <if test="zncsqwq != null">
        ZNCSQWQ = #{zncsqwq,jdbcType=DECIMAL},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.iscm.entity.SapZmmt0288">
    update SAP_ZMMT0288
    set MANDT = #{mandt,jdbcType=VARCHAR},
      ZGUID = #{zguid,jdbcType=VARCHAR},
      WERKS = #{werks,jdbcType=VARCHAR},
      MATNR = #{matnr,jdbcType=VARCHAR},
      EKORG = #{ekorg,jdbcType=VARCHAR},
      BSART = #{bsart,jdbcType=VARCHAR},
      BUKRS = #{bukrs,jdbcType=VARCHAR},
      EKGRP = #{ekgrp,jdbcType=VARCHAR},
      NAME1_DC = #{name1Dc,jdbcType=VARCHAR},
      LIFNR_XD = #{lifnrXd,jdbcType=VARCHAR},
      NAME1_XD = #{name1Xd,jdbcType=VARCHAR},
      MENGE_XD = #{mengeXd,jdbcType=DECIMAL},
      MEINS_XD = #{meinsXd,jdbcType=VARCHAR},
      NETPR_XD = #{netprXd,jdbcType=DECIMAL},
      BRTWR_XD = #{brtwrXd,jdbcType=DECIMAL},
      NETSUM_XD = #{netsumXd,jdbcType=DECIMAL},
      BRTSUM_XD = #{brtsumXd,jdbcType=DECIMAL},
      PEINH_XD = #{peinhXd,jdbcType=DECIMAL},
      TAX_XD = #{taxXd,jdbcType=VARCHAR},
      TAX_XD_T = #{taxXdT,jdbcType=VARCHAR},
      KONNR = #{konnr,jdbcType=VARCHAR},
      KTPNR = #{ktpnr,jdbcType=VARCHAR},
      ZPLCODE = #{zplcode,jdbcType=VARCHAR},
      ZPLSUG = #{zplsug,jdbcType=VARCHAR},
      ZCJMS = #{zcjms,jdbcType=VARCHAR},
      ZTERM = #{zterm,jdbcType=VARCHAR},
      ZTERM_T = #{ztermT,jdbcType=VARCHAR},
      ZCGBZSZ = #{zcgbzsz,jdbcType=VARCHAR},
      MENGE_SUG = #{mengeSug,jdbcType=DECIMAL},
      MEINS_SUG = #{meinsSug,jdbcType=VARCHAR},
      MENGE_ZBZ = #{mengeZbz,jdbcType=DECIMAL},
      MENGE_XBZ = #{mengeXbz,jdbcType=DECIMAL},
      MENGE_ZZ = #{mengeZz,jdbcType=DECIMAL},
      MEINS_ZZ = #{meinsZz,jdbcType=VARCHAR},
      ZZBZL = #{zzbzl,jdbcType=VARCHAR},
      ZZCQTY = #{zzcqty,jdbcType=DECIMAL},
      ZXSJWXS = #{zxsjwxs,jdbcType=DECIMAL},
      ZXXJWXS = #{zxxjwxs,jdbcType=DECIMAL},
      KNTTP = #{knttp,jdbcType=VARCHAR},
      KOSTL = #{kostl,jdbcType=VARCHAR},
      ZUSERID = #{zuserid,jdbcType=VARCHAR},
      ZYWXM = #{zywxm,jdbcType=VARCHAR},
      ZZHXS = #{zzhxs,jdbcType=VARCHAR},
      ZBCDHL = #{zbcdhl,jdbcType=DECIMAL},
      ZMDZ = #{zmdz,jdbcType=VARCHAR},
      ZMDZ_T = #{zmdzT,jdbcType=VARCHAR},
      ZSPJB = #{zspjb,jdbcType=DECIMAL},
      EKORG_02 = #{ekorg02,jdbcType=VARCHAR},
      ZSPCKDDLX = #{zspckddlx,jdbcType=VARCHAR},
      ZGHDC = #{zghdc,jdbcType=VARCHAR},
      ZZCQTY_BDP = #{zzcqtyBdp,jdbcType=DECIMAL},
      ZZBZL_BDP = #{zzbzlBdp,jdbcType=DECIMAL},
      ZJHJH = #{zjhjh,jdbcType=VARCHAR},
      LGORT = #{lgort,jdbcType=VARCHAR},
      ZBZGG = #{zbzgg,jdbcType=VARCHAR},
      REMARK = #{remark,jdbcType=VARCHAR},
      ZCRDATE = #{zcrdate,jdbcType=VARCHAR},
      ZCRTIME = #{zcrtime,jdbcType=VARCHAR},
      ZCRNAME = #{zcrname,jdbcType=VARCHAR},
      ZCHDATE = #{zchdate,jdbcType=VARCHAR},
      ZCHTIME = #{zchtime,jdbcType=VARCHAR},
      ZCHNAME = #{zchname,jdbcType=VARCHAR},
      EBELN = #{ebeln,jdbcType=VARCHAR},
      EBELP = #{ebelp,jdbcType=VARCHAR},
      PURREQNO = #{purreqno,jdbcType=VARCHAR},
      STORELINENO = #{storelineno,jdbcType=VARCHAR},
      ZBYZD2 = #{zbyzd2,jdbcType=VARCHAR},
      ZSTOCK_USE = #{zstockUse,jdbcType=DECIMAL},
      LABST_DC_JS = #{labstDcJs,jdbcType=DECIMAL},
      MENGE_WQ_JS = #{mengeWqJs,jdbcType=DECIMAL},
      ZZXL_30_JS = #{zzxl30Js,jdbcType=DECIMAL},
      ZMDQTY_JS = #{zmdqtyJs,jdbcType=DECIMAL},
      ZTSDDBS = #{ztsddbs,jdbcType=VARCHAR},
      ZTSDDYDH = #{ztsddydh,jdbcType=VARCHAR},
      ZZQXDJH = #{zzqxdjh,jdbcType=VARCHAR},
      ZZQXDHH = #{zzqxdhh,jdbcType=VARCHAR},
      ZJTNDJTJ = #{zjtndjtj,jdbcType=VARCHAR},
      ZCYJSWQ = #{zcyjswq,jdbcType=DECIMAL},
      ZTCWQ = #{ztcwq,jdbcType=DECIMAL},
      ZDCKYKC = #{zdckykc,jdbcType=DECIMAL},
      MENGE_WQ = #{mengeWq,jdbcType=DECIMAL},
      TZTS = #{tzts,jdbcType=INTEGER},
      TZTS_YXQZ = #{tztsYxqz,jdbcType=VARCHAR},
      ZGYSQZQ = #{zgysqzq,jdbcType=INTEGER},
      ZSPQZQ = #{zspqzq,jdbcType=DECIMAL},
      ZSPQZQQSRQ = #{zspqzqqsrq,jdbcType=VARCHAR},
      ZSPQZQZZRQ = #{zspqzqzzrq,jdbcType=VARCHAR},
      ZBCKCTS = #{zbckcts,jdbcType=DECIMAL},
      ZBCKCTSQSRQ = #{zbckctsqsrq,jdbcType=VARCHAR},
      ZBCKCTSZZRQ = #{zbckctszzrq,jdbcType=VARCHAR},
      ZBCDHLQSRQ = #{zbcdhlqsrq,jdbcType=VARCHAR},
      ZBCDHLZZRQ = #{zbcdhlzzrq,jdbcType=VARCHAR},
      ZSFJS = #{zsfjs,jdbcType=VARCHAR},
      ZXQHBS = #{zxqhbs,jdbcType=DECIMAL},
      ZAQKCBCTS = #{zaqkcbcts,jdbcType=DECIMAL},
      ZSFQZ = #{zsfqz,jdbcType=VARCHAR},
      ZTSXSQSRQ = #{ztsxsqsrq,jdbcType=VARCHAR},
      ZTSXSJSRQ = #{ztsxsjsrq,jdbcType=VARCHAR},
      ZTSXSQZZB = #{ztsxsqzzb,jdbcType=DECIMAL},
      ZKCSX = #{zkcsx,jdbcType=DECIMAL},
      ZKCXX = #{zkcxx,jdbcType=DECIMAL},
      ZCGTPJL = #{zcgtpjl,jdbcType=VARCHAR},
      ZCGTPJLXM = #{zcgtpjlxm,jdbcType=VARCHAR},
      ZYJYWXDTS = #{zyjywxdts,jdbcType=INTEGER},
      BRTWR_LAST = #{brtwrLast,jdbcType=DECIMAL},
      PEINH_LAST = #{peinhLast,jdbcType=DECIMAL},
      ZTZDCKC = #{ztzdckc,jdbcType=DECIMAL},
      ZSTOCK_DC = #{zstockDc,jdbcType=DECIMAL},
      ZSTOCK_STORE = #{zstockStore,jdbcType=DECIMAL},
      ZZC_INACTIVE_DC_STOCK = #{zzcInactiveDcStock,jdbcType=DECIMAL},
      ZSTOCK_TOTAL = #{zstockTotal,jdbcType=DECIMAL},
      ZFIX_TBS = #{zfixTbs,jdbcType=DECIMAL},
      ZSUM_R_V = #{zsumRV,jdbcType=DECIMAL},
      ZDC_INV_UPPER = #{zdcInvUpper,jdbcType=DECIMAL},
      ZTZDCDKC1 = #{ztzdcdkc1,jdbcType=DECIMAL},
      ZTZDCDKC2 = #{ztzdcdkc2,jdbcType=DECIMAL},
      ZMENGE_WQ7 = #{zmengeWq7,jdbcType=DECIMAL},
      ZMAX_APPLY = #{zmaxApply,jdbcType=DECIMAL},
      ZINV_UPPER = #{zinvUpper,jdbcType=DECIMAL},
      ZMENGE_SUG = #{zmengeSug,jdbcType=DECIMAL},
      ZDHD_B = #{zdhdB,jdbcType=DECIMAL},
      ZDC_L = #{zdcL,jdbcType=DECIMAL},
      ZGOODS_L = #{zgoodsL,jdbcType=VARCHAR},
      ZS_STOCK = #{zsStock,jdbcType=DECIMAL},
      ZRAW = #{zraw,jdbcType=DECIMAL},
      ZNONCXB = #{znoncxb,jdbcType=VARCHAR},
      ZJM_STORE_STOCK = #{zjmStoreStock,jdbcType=DECIMAL},
      ZDHD = #{zdhd,jdbcType=DECIMAL},
      ZHTLXBS = #{zhtlxbs,jdbcType=VARCHAR},
      REASON_TYPE = #{reasonType,jdbcType=VARCHAR},
      ZPYYY = #{zpyyy,jdbcType=VARCHAR},
      BRTWR_JY = #{brtwrJy,jdbcType=DECIMAL},
      ZHTLXBS_JY = #{zhtlxbsJy,jdbcType=VARCHAR},
      ZNCSQWQ = #{zncsqwq,jdbcType=DECIMAL}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>