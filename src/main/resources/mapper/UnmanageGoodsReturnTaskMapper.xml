<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.UnmanageGoodsReturnTaskMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.UnmanageGoodsReturnTask">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="task_code" jdbcType="VARCHAR" property="taskCode" />
    <result column="task_name" jdbcType="VARCHAR" property="taskName" />
    <result column="platform_org_id" jdbcType="BIGINT" property="platformOrgId" />
    <result column="platform_name" jdbcType="VARCHAR" property="platformName" />
    <result column="org_type" jdbcType="TINYINT" property="orgType" />
    <result column="org_ids" jdbcType="VARCHAR" property="orgIds" />
    <result column="goods_type" jdbcType="TINYINT" property="goodsType" />
    <result column="goods_nos" jdbcType="VARCHAR" property="goodsNos" />
    <result column="store_count" jdbcType="INTEGER" property="storeCount" />
    <result column="goods_count" jdbcType="INTEGER" property="goodsCount" />
    <result column="total_return_qty" jdbcType="DECIMAL" property="totalReturnQty" />
    <result column="total_return_amount" jdbcType="DECIMAL" property="totalReturnAmount" />
    <result column="task_status" jdbcType="TINYINT" property="taskStatus" />
    <result column="process_count" jdbcType="INTEGER" property="processCount" />
    <result column="success_count" jdbcType="INTEGER" property="successCount" />
    <result column="error_count" jdbcType="INTEGER" property="errorCount" />
    <result column="error_msg" jdbcType="VARCHAR" property="errorMsg" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, task_code, task_name, platform_org_id, platform_name, org_type, org_ids, goods_type, 
    goods_nos, store_count, goods_count, total_return_qty, total_return_amount, task_status, 
    process_count, success_count, error_count, error_msg, start_time, end_time, `status`, 
    gmt_create, gmt_update, extend, version, created_by, created_name, updated_by, updated_name
  </sql>
  <select id="selectByExample" parameterType="com.cowell.iscm.entity.UnmanageGoodsReturnTaskExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from unmanage_goods_return_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from unmanage_goods_return_task
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from unmanage_goods_return_task
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.iscm.entity.UnmanageGoodsReturnTaskExample">
    delete from unmanage_goods_return_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.iscm.entity.UnmanageGoodsReturnTask">
    insert into unmanage_goods_return_task (id, task_code, task_name, 
      platform_org_id, platform_name, org_type, 
      org_ids, goods_type, goods_nos, 
      store_count, goods_count, total_return_qty, 
      total_return_amount, task_status, process_count, 
      success_count, error_count, error_msg, 
      start_time, end_time, `status`, 
      gmt_create, gmt_update, extend, 
      version, created_by, created_name, 
      updated_by, updated_name)
    values (#{id,jdbcType=BIGINT}, #{taskCode,jdbcType=VARCHAR}, #{taskName,jdbcType=VARCHAR}, 
      #{platformOrgId,jdbcType=BIGINT}, #{platformName,jdbcType=VARCHAR}, #{orgType,jdbcType=TINYINT}, 
      #{orgIds,jdbcType=VARCHAR}, #{goodsType,jdbcType=TINYINT}, #{goodsNos,jdbcType=VARCHAR}, 
      #{storeCount,jdbcType=INTEGER}, #{goodsCount,jdbcType=INTEGER}, #{totalReturnQty,jdbcType=DECIMAL}, 
      #{totalReturnAmount,jdbcType=DECIMAL}, #{taskStatus,jdbcType=TINYINT}, #{processCount,jdbcType=INTEGER}, 
      #{successCount,jdbcType=INTEGER}, #{errorCount,jdbcType=INTEGER}, #{errorMsg,jdbcType=VARCHAR}, 
      #{startTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}, #{status,jdbcType=TINYINT}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}, #{extend,jdbcType=VARCHAR}, 
      #{version,jdbcType=INTEGER}, #{createdBy,jdbcType=BIGINT}, #{createdName,jdbcType=VARCHAR}, 
      #{updatedBy,jdbcType=BIGINT}, #{updatedName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.iscm.entity.UnmanageGoodsReturnTask"   useGeneratedKeys="true" keyProperty="id">
    insert into unmanage_goods_return_task
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="taskCode != null">
        task_code,
      </if>
      <if test="taskName != null">
        task_name,
      </if>
      <if test="platformOrgId != null">
        platform_org_id,
      </if>
      <if test="platformName != null">
        platform_name,
      </if>
      <if test="orgType != null">
        org_type,
      </if>
      <if test="orgIds != null">
        org_ids,
      </if>
      <if test="goodsType != null">
        goods_type,
      </if>
      <if test="goodsNos != null">
        goods_nos,
      </if>
      <if test="storeCount != null">
        store_count,
      </if>
      <if test="goodsCount != null">
        goods_count,
      </if>
      <if test="totalReturnQty != null">
        total_return_qty,
      </if>
      <if test="totalReturnAmount != null">
        total_return_amount,
      </if>
      <if test="taskStatus != null">
        task_status,
      </if>
      <if test="processCount != null">
        process_count,
      </if>
      <if test="successCount != null">
        success_count,
      </if>
      <if test="errorCount != null">
        error_count,
      </if>
      <if test="errorMsg != null">
        error_msg,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedName != null">
        updated_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="taskCode != null">
        #{taskCode,jdbcType=VARCHAR},
      </if>
      <if test="taskName != null">
        #{taskName,jdbcType=VARCHAR},
      </if>
      <if test="platformOrgId != null">
        #{platformOrgId,jdbcType=BIGINT},
      </if>
      <if test="platformName != null">
        #{platformName,jdbcType=VARCHAR},
      </if>
      <if test="orgType != null">
        #{orgType,jdbcType=TINYINT},
      </if>
      <if test="orgIds != null">
        #{orgIds,jdbcType=VARCHAR},
      </if>
      <if test="goodsType != null">
        #{goodsType,jdbcType=TINYINT},
      </if>
      <if test="goodsNos != null">
        #{goodsNos,jdbcType=VARCHAR},
      </if>
      <if test="storeCount != null">
        #{storeCount,jdbcType=INTEGER},
      </if>
      <if test="goodsCount != null">
        #{goodsCount,jdbcType=INTEGER},
      </if>
      <if test="totalReturnQty != null">
        #{totalReturnQty,jdbcType=DECIMAL},
      </if>
      <if test="totalReturnAmount != null">
        #{totalReturnAmount,jdbcType=DECIMAL},
      </if>
      <if test="taskStatus != null">
        #{taskStatus,jdbcType=TINYINT},
      </if>
      <if test="processCount != null">
        #{processCount,jdbcType=INTEGER},
      </if>
      <if test="successCount != null">
        #{successCount,jdbcType=INTEGER},
      </if>
      <if test="errorCount != null">
        #{errorCount,jdbcType=INTEGER},
      </if>
      <if test="errorMsg != null">
        #{errorMsg,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        #{updatedName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.iscm.entity.UnmanageGoodsReturnTaskExample" resultType="java.lang.Long">
    select count(*) from unmanage_goods_return_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update unmanage_goods_return_task
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.taskCode != null">
        task_code = #{record.taskCode,jdbcType=VARCHAR},
      </if>
      <if test="record.taskName != null">
        task_name = #{record.taskName,jdbcType=VARCHAR},
      </if>
      <if test="record.platformOrgId != null">
        platform_org_id = #{record.platformOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.platformName != null">
        platform_name = #{record.platformName,jdbcType=VARCHAR},
      </if>
      <if test="record.orgType != null">
        org_type = #{record.orgType,jdbcType=TINYINT},
      </if>
      <if test="record.orgIds != null">
        org_ids = #{record.orgIds,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsType != null">
        goods_type = #{record.goodsType,jdbcType=TINYINT},
      </if>
      <if test="record.goodsNos != null">
        goods_nos = #{record.goodsNos,jdbcType=VARCHAR},
      </if>
      <if test="record.storeCount != null">
        store_count = #{record.storeCount,jdbcType=INTEGER},
      </if>
      <if test="record.goodsCount != null">
        goods_count = #{record.goodsCount,jdbcType=INTEGER},
      </if>
      <if test="record.totalReturnQty != null">
        total_return_qty = #{record.totalReturnQty,jdbcType=DECIMAL},
      </if>
      <if test="record.totalReturnAmount != null">
        total_return_amount = #{record.totalReturnAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.taskStatus != null">
        task_status = #{record.taskStatus,jdbcType=TINYINT},
      </if>
      <if test="record.processCount != null">
        process_count = #{record.processCount,jdbcType=INTEGER},
      </if>
      <if test="record.successCount != null">
        success_count = #{record.successCount,jdbcType=INTEGER},
      </if>
      <if test="record.errorCount != null">
        error_count = #{record.errorCount,jdbcType=INTEGER},
      </if>
      <if test="record.errorMsg != null">
        error_msg = #{record.errorMsg,jdbcType=VARCHAR},
      </if>
      <if test="record.startTime != null">
        start_time = #{record.startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.endTime != null">
        end_time = #{record.endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedName != null">
        updated_name = #{record.updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update unmanage_goods_return_task
    set id = #{record.id,jdbcType=BIGINT},
      task_code = #{record.taskCode,jdbcType=VARCHAR},
      task_name = #{record.taskName,jdbcType=VARCHAR},
      platform_org_id = #{record.platformOrgId,jdbcType=BIGINT},
      platform_name = #{record.platformName,jdbcType=VARCHAR},
      org_type = #{record.orgType,jdbcType=TINYINT},
      org_ids = #{record.orgIds,jdbcType=VARCHAR},
      goods_type = #{record.goodsType,jdbcType=TINYINT},
      goods_nos = #{record.goodsNos,jdbcType=VARCHAR},
      store_count = #{record.storeCount,jdbcType=INTEGER},
      goods_count = #{record.goodsCount,jdbcType=INTEGER},
      total_return_qty = #{record.totalReturnQty,jdbcType=DECIMAL},
      total_return_amount = #{record.totalReturnAmount,jdbcType=DECIMAL},
      task_status = #{record.taskStatus,jdbcType=TINYINT},
      process_count = #{record.processCount,jdbcType=INTEGER},
      success_count = #{record.successCount,jdbcType=INTEGER},
      error_count = #{record.errorCount,jdbcType=INTEGER},
      error_msg = #{record.errorMsg,jdbcType=VARCHAR},
      start_time = #{record.startTime,jdbcType=TIMESTAMP},
      end_time = #{record.endTime,jdbcType=TIMESTAMP},
      `status` = #{record.status,jdbcType=TINYINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_name = #{record.createdName,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_name = #{record.updatedName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.iscm.entity.UnmanageGoodsReturnTask">
    update unmanage_goods_return_task
    <set>
      <if test="taskCode != null">
        task_code = #{taskCode,jdbcType=VARCHAR},
      </if>
      <if test="taskName != null">
        task_name = #{taskName,jdbcType=VARCHAR},
      </if>
      <if test="platformOrgId != null">
        platform_org_id = #{platformOrgId,jdbcType=BIGINT},
      </if>
      <if test="platformName != null">
        platform_name = #{platformName,jdbcType=VARCHAR},
      </if>
      <if test="orgType != null">
        org_type = #{orgType,jdbcType=TINYINT},
      </if>
      <if test="orgIds != null">
        org_ids = #{orgIds,jdbcType=VARCHAR},
      </if>
      <if test="goodsType != null">
        goods_type = #{goodsType,jdbcType=TINYINT},
      </if>
      <if test="goodsNos != null">
        goods_nos = #{goodsNos,jdbcType=VARCHAR},
      </if>
      <if test="storeCount != null">
        store_count = #{storeCount,jdbcType=INTEGER},
      </if>
      <if test="goodsCount != null">
        goods_count = #{goodsCount,jdbcType=INTEGER},
      </if>
      <if test="totalReturnQty != null">
        total_return_qty = #{totalReturnQty,jdbcType=DECIMAL},
      </if>
      <if test="totalReturnAmount != null">
        total_return_amount = #{totalReturnAmount,jdbcType=DECIMAL},
      </if>
      <if test="taskStatus != null">
        task_status = #{taskStatus,jdbcType=TINYINT},
      </if>
      <if test="processCount != null">
        process_count = #{processCount,jdbcType=INTEGER},
      </if>
      <if test="successCount != null">
        success_count = #{successCount,jdbcType=INTEGER},
      </if>
      <if test="errorCount != null">
        error_count = #{errorCount,jdbcType=INTEGER},
      </if>
      <if test="errorMsg != null">
        error_msg = #{errorMsg,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        updated_name = #{updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.iscm.entity.UnmanageGoodsReturnTask">
    update unmanage_goods_return_task
    set task_code = #{taskCode,jdbcType=VARCHAR},
      task_name = #{taskName,jdbcType=VARCHAR},
      platform_org_id = #{platformOrgId,jdbcType=BIGINT},
      platform_name = #{platformName,jdbcType=VARCHAR},
      org_type = #{orgType,jdbcType=TINYINT},
      org_ids = #{orgIds,jdbcType=VARCHAR},
      goods_type = #{goodsType,jdbcType=TINYINT},
      goods_nos = #{goodsNos,jdbcType=VARCHAR},
      store_count = #{storeCount,jdbcType=INTEGER},
      goods_count = #{goodsCount,jdbcType=INTEGER},
      total_return_qty = #{totalReturnQty,jdbcType=DECIMAL},
      total_return_amount = #{totalReturnAmount,jdbcType=DECIMAL},
      task_status = #{taskStatus,jdbcType=TINYINT},
      process_count = #{processCount,jdbcType=INTEGER},
      success_count = #{successCount,jdbcType=INTEGER},
      error_count = #{errorCount,jdbcType=INTEGER},
      error_msg = #{errorMsg,jdbcType=VARCHAR},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      `status` = #{status,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_name = #{createdName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_name = #{updatedName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <!-- 根据任务编码查询任务 -->
  <select id="selectByTaskCode" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from unmanage_goods_return_task
    where task_code = #{taskCode,jdbcType=VARCHAR}
      and `status` = 0
  </select>
</mapper>