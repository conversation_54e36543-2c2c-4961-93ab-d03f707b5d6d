<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.SapZmmt0287Mapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.SapZmmt0287">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="MANDT" jdbcType="VARCHAR" property="mandt" />
    <result column="PURREQNO" jdbcType="VARCHAR" property="purreqno" />
    <result column="STORELINENO" jdbcType="VARCHAR" property="storelineno" />
    <result column="BUKRS" jdbcType="VARCHAR" property="bukrs" />
    <result column="EKORG" jdbcType="VARCHAR" property="ekorg" />
    <result column="WERKS" jdbcType="VARCHAR" property="werks" />
    <result column="ZZZZSHD" jdbcType="VARCHAR" property="zzzzshd" />
    <result column="BSART" jdbcType="VARCHAR" property="bsart" />
    <result column="MATNR" jdbcType="VARCHAR" property="matnr" />
    <result column="MENGE" jdbcType="DECIMAL" property="menge" />
    <result column="ZSPSL" jdbcType="DECIMAL" property="zspsl" />
    <result column="MEINS" jdbcType="VARCHAR" property="meins" />
    <result column="ZZCXBJS" jdbcType="VARCHAR" property="zzcxbjs" />
    <result column="ZJHBHSL" jdbcType="DECIMAL" property="zjhbhsl" />
    <result column="ZYYBZ" jdbcType="VARCHAR" property="zyybz" />
    <result column="BADAT" jdbcType="VARCHAR" property="badat" />
    <result column="ZSPZT" jdbcType="VARCHAR" property="zspzt" />
    <result column="ZCLZT" jdbcType="VARCHAR" property="zclzt" />
    <result column="LOEKZ" jdbcType="VARCHAR" property="loekz" />
    <result column="ERDAT" jdbcType="VARCHAR" property="erdat" />
    <result column="ERZET" jdbcType="VARCHAR" property="erzet" />
    <result column="ERNAM" jdbcType="VARCHAR" property="ernam" />
    <result column="AEDAT" jdbcType="VARCHAR" property="aedat" />
    <result column="AEZET" jdbcType="VARCHAR" property="aezet" />
    <result column="AENAM" jdbcType="VARCHAR" property="aenam" />
    <result column="ZSHCK" jdbcType="VARCHAR" property="zshck" />
    <result column="ZCGYGH" jdbcType="VARCHAR" property="zcgygh" />
    <result column="ZPURAGT" jdbcType="VARCHAR" property="zpuragt" />
    <result column="CHARG" jdbcType="VARCHAR" property="charg" />
    <result column="LGORT" jdbcType="VARCHAR" property="lgort" />
    <result column="RESLO" jdbcType="VARCHAR" property="reslo" />
    <result column="MSG" jdbcType="VARCHAR" property="msg" />
    <result column="EBELN" jdbcType="VARCHAR" property="ebeln" />
    <result column="EBELP" jdbcType="VARCHAR" property="ebelp" />
    <result column="VBELN" jdbcType="VARCHAR" property="vbeln" />
    <result column="POSNR" jdbcType="VARCHAR" property="posnr" />
    <result column="BRTWR" jdbcType="DECIMAL" property="brtwr" />
    <result column="KPEIN2" jdbcType="DECIMAL" property="kpein2" />
    <result column="ZCGZB" jdbcType="VARCHAR" property="zcgzb" />
    <result column="ZYPURREQNO" jdbcType="VARCHAR" property="zypurreqno" />
    <result column="ZYSTORELINENO" jdbcType="VARCHAR" property="zystorelineno" />
    <result column="ZSFCF" jdbcType="VARCHAR" property="zsfcf" />
    <result column="ZMAIN" jdbcType="VARCHAR" property="zmain" />
    <result column="ZZXL_30_JS" jdbcType="DECIMAL" property="zzxl30Js" />
    <result column="ZMDQTY_JS" jdbcType="DECIMAL" property="zmdqtyJs" />
    <result column="ZDCKYKC" jdbcType="DECIMAL" property="zdckykc" />
    <result column="MENGE_SUG" jdbcType="DECIMAL" property="mengeSug" />
    <result column="ZPLSUG" jdbcType="VARCHAR" property="zplsug" />
    <result column="ZCYJSWQ" jdbcType="DECIMAL" property="zcyjswq" />
    <result column="LABST_DC_JS" jdbcType="DECIMAL" property="labstDcJs" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, MANDT, PURREQNO, STORELINENO, BUKRS, EKORG, WERKS, ZZZZSHD, BSART, MATNR, MENGE, 
    ZSPSL, MEINS, ZZCXBJS, ZJHBHSL, ZYYBZ, BADAT, ZSPZT, ZCLZT, LOEKZ, ERDAT, ERZET, 
    ERNAM, AEDAT, AEZET, AENAM, ZSHCK, ZCGYGH, ZPURAGT, CHARG, LGORT, RESLO, MSG, EBELN, 
    EBELP, VBELN, POSNR, BRTWR, KPEIN2, ZCGZB, ZYPURREQNO, ZYSTORELINENO, ZSFCF, ZMAIN, 
    ZZXL_30_JS, ZMDQTY_JS, ZDCKYKC, MENGE_SUG, ZPLSUG, ZCYJSWQ, LABST_DC_JS
  </sql>
  <select id="selectByExample" parameterType="com.cowell.iscm.entity.SapZmmt0287Example" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from SAP_ZMMT0287
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from SAP_ZMMT0287
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from SAP_ZMMT0287
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.iscm.entity.SapZmmt0287Example">
    delete from SAP_ZMMT0287
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entity.SapZmmt0287" useGeneratedKeys="true">
    insert into SAP_ZMMT0287 (MANDT, PURREQNO, STORELINENO, 
      BUKRS, EKORG, WERKS, 
      ZZZZSHD, BSART, MATNR, 
      MENGE, ZSPSL, MEINS, 
      ZZCXBJS, ZJHBHSL, ZYYBZ, 
      BADAT, ZSPZT, ZCLZT, 
      LOEKZ, ERDAT, ERZET, 
      ERNAM, AEDAT, AEZET, 
      AENAM, ZSHCK, ZCGYGH, 
      ZPURAGT, CHARG, LGORT, 
      RESLO, MSG, EBELN, 
      EBELP, VBELN, POSNR, 
      BRTWR, KPEIN2, ZCGZB, 
      ZYPURREQNO, ZYSTORELINENO, ZSFCF, 
      ZMAIN, ZZXL_30_JS, ZMDQTY_JS, 
      ZDCKYKC, MENGE_SUG, ZPLSUG, 
      ZCYJSWQ, LABST_DC_JS)
    values (#{mandt,jdbcType=VARCHAR}, #{purreqno,jdbcType=VARCHAR}, #{storelineno,jdbcType=VARCHAR}, 
      #{bukrs,jdbcType=VARCHAR}, #{ekorg,jdbcType=VARCHAR}, #{werks,jdbcType=VARCHAR}, 
      #{zzzzshd,jdbcType=VARCHAR}, #{bsart,jdbcType=VARCHAR}, #{matnr,jdbcType=VARCHAR}, 
      #{menge,jdbcType=DECIMAL}, #{zspsl,jdbcType=DECIMAL}, #{meins,jdbcType=VARCHAR}, 
      #{zzcxbjs,jdbcType=VARCHAR}, #{zjhbhsl,jdbcType=DECIMAL}, #{zyybz,jdbcType=VARCHAR}, 
      #{badat,jdbcType=VARCHAR}, #{zspzt,jdbcType=VARCHAR}, #{zclzt,jdbcType=VARCHAR}, 
      #{loekz,jdbcType=VARCHAR}, #{erdat,jdbcType=VARCHAR}, #{erzet,jdbcType=VARCHAR}, 
      #{ernam,jdbcType=VARCHAR}, #{aedat,jdbcType=VARCHAR}, #{aezet,jdbcType=VARCHAR}, 
      #{aenam,jdbcType=VARCHAR}, #{zshck,jdbcType=VARCHAR}, #{zcgygh,jdbcType=VARCHAR}, 
      #{zpuragt,jdbcType=VARCHAR}, #{charg,jdbcType=VARCHAR}, #{lgort,jdbcType=VARCHAR}, 
      #{reslo,jdbcType=VARCHAR}, #{msg,jdbcType=VARCHAR}, #{ebeln,jdbcType=VARCHAR}, 
      #{ebelp,jdbcType=VARCHAR}, #{vbeln,jdbcType=VARCHAR}, #{posnr,jdbcType=VARCHAR}, 
      #{brtwr,jdbcType=DECIMAL}, #{kpein2,jdbcType=DECIMAL}, #{zcgzb,jdbcType=VARCHAR}, 
      #{zypurreqno,jdbcType=VARCHAR}, #{zystorelineno,jdbcType=VARCHAR}, #{zsfcf,jdbcType=VARCHAR}, 
      #{zmain,jdbcType=VARCHAR}, #{zzxl30Js,jdbcType=DECIMAL}, #{zmdqtyJs,jdbcType=DECIMAL}, 
      #{zdckykc,jdbcType=DECIMAL}, #{mengeSug,jdbcType=DECIMAL}, #{zplsug,jdbcType=VARCHAR}, 
      #{zcyjswq,jdbcType=DECIMAL}, #{labstDcJs,jdbcType=DECIMAL})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entity.SapZmmt0287" useGeneratedKeys="true">
    insert into SAP_ZMMT0287
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="mandt != null">
        MANDT,
      </if>
      <if test="purreqno != null">
        PURREQNO,
      </if>
      <if test="storelineno != null">
        STORELINENO,
      </if>
      <if test="bukrs != null">
        BUKRS,
      </if>
      <if test="ekorg != null">
        EKORG,
      </if>
      <if test="werks != null">
        WERKS,
      </if>
      <if test="zzzzshd != null">
        ZZZZSHD,
      </if>
      <if test="bsart != null">
        BSART,
      </if>
      <if test="matnr != null">
        MATNR,
      </if>
      <if test="menge != null">
        MENGE,
      </if>
      <if test="zspsl != null">
        ZSPSL,
      </if>
      <if test="meins != null">
        MEINS,
      </if>
      <if test="zzcxbjs != null">
        ZZCXBJS,
      </if>
      <if test="zjhbhsl != null">
        ZJHBHSL,
      </if>
      <if test="zyybz != null">
        ZYYBZ,
      </if>
      <if test="badat != null">
        BADAT,
      </if>
      <if test="zspzt != null">
        ZSPZT,
      </if>
      <if test="zclzt != null">
        ZCLZT,
      </if>
      <if test="loekz != null">
        LOEKZ,
      </if>
      <if test="erdat != null">
        ERDAT,
      </if>
      <if test="erzet != null">
        ERZET,
      </if>
      <if test="ernam != null">
        ERNAM,
      </if>
      <if test="aedat != null">
        AEDAT,
      </if>
      <if test="aezet != null">
        AEZET,
      </if>
      <if test="aenam != null">
        AENAM,
      </if>
      <if test="zshck != null">
        ZSHCK,
      </if>
      <if test="zcgygh != null">
        ZCGYGH,
      </if>
      <if test="zpuragt != null">
        ZPURAGT,
      </if>
      <if test="charg != null">
        CHARG,
      </if>
      <if test="lgort != null">
        LGORT,
      </if>
      <if test="reslo != null">
        RESLO,
      </if>
      <if test="msg != null">
        MSG,
      </if>
      <if test="ebeln != null">
        EBELN,
      </if>
      <if test="ebelp != null">
        EBELP,
      </if>
      <if test="vbeln != null">
        VBELN,
      </if>
      <if test="posnr != null">
        POSNR,
      </if>
      <if test="brtwr != null">
        BRTWR,
      </if>
      <if test="kpein2 != null">
        KPEIN2,
      </if>
      <if test="zcgzb != null">
        ZCGZB,
      </if>
      <if test="zypurreqno != null">
        ZYPURREQNO,
      </if>
      <if test="zystorelineno != null">
        ZYSTORELINENO,
      </if>
      <if test="zsfcf != null">
        ZSFCF,
      </if>
      <if test="zmain != null">
        ZMAIN,
      </if>
      <if test="zzxl30Js != null">
        ZZXL_30_JS,
      </if>
      <if test="zmdqtyJs != null">
        ZMDQTY_JS,
      </if>
      <if test="zdckykc != null">
        ZDCKYKC,
      </if>
      <if test="mengeSug != null">
        MENGE_SUG,
      </if>
      <if test="zplsug != null">
        ZPLSUG,
      </if>
      <if test="zcyjswq != null">
        ZCYJSWQ,
      </if>
      <if test="labstDcJs != null">
        LABST_DC_JS,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="mandt != null">
        #{mandt,jdbcType=VARCHAR},
      </if>
      <if test="purreqno != null">
        #{purreqno,jdbcType=VARCHAR},
      </if>
      <if test="storelineno != null">
        #{storelineno,jdbcType=VARCHAR},
      </if>
      <if test="bukrs != null">
        #{bukrs,jdbcType=VARCHAR},
      </if>
      <if test="ekorg != null">
        #{ekorg,jdbcType=VARCHAR},
      </if>
      <if test="werks != null">
        #{werks,jdbcType=VARCHAR},
      </if>
      <if test="zzzzshd != null">
        #{zzzzshd,jdbcType=VARCHAR},
      </if>
      <if test="bsart != null">
        #{bsart,jdbcType=VARCHAR},
      </if>
      <if test="matnr != null">
        #{matnr,jdbcType=VARCHAR},
      </if>
      <if test="menge != null">
        #{menge,jdbcType=DECIMAL},
      </if>
      <if test="zspsl != null">
        #{zspsl,jdbcType=DECIMAL},
      </if>
      <if test="meins != null">
        #{meins,jdbcType=VARCHAR},
      </if>
      <if test="zzcxbjs != null">
        #{zzcxbjs,jdbcType=VARCHAR},
      </if>
      <if test="zjhbhsl != null">
        #{zjhbhsl,jdbcType=DECIMAL},
      </if>
      <if test="zyybz != null">
        #{zyybz,jdbcType=VARCHAR},
      </if>
      <if test="badat != null">
        #{badat,jdbcType=VARCHAR},
      </if>
      <if test="zspzt != null">
        #{zspzt,jdbcType=VARCHAR},
      </if>
      <if test="zclzt != null">
        #{zclzt,jdbcType=VARCHAR},
      </if>
      <if test="loekz != null">
        #{loekz,jdbcType=VARCHAR},
      </if>
      <if test="erdat != null">
        #{erdat,jdbcType=VARCHAR},
      </if>
      <if test="erzet != null">
        #{erzet,jdbcType=VARCHAR},
      </if>
      <if test="ernam != null">
        #{ernam,jdbcType=VARCHAR},
      </if>
      <if test="aedat != null">
        #{aedat,jdbcType=VARCHAR},
      </if>
      <if test="aezet != null">
        #{aezet,jdbcType=VARCHAR},
      </if>
      <if test="aenam != null">
        #{aenam,jdbcType=VARCHAR},
      </if>
      <if test="zshck != null">
        #{zshck,jdbcType=VARCHAR},
      </if>
      <if test="zcgygh != null">
        #{zcgygh,jdbcType=VARCHAR},
      </if>
      <if test="zpuragt != null">
        #{zpuragt,jdbcType=VARCHAR},
      </if>
      <if test="charg != null">
        #{charg,jdbcType=VARCHAR},
      </if>
      <if test="lgort != null">
        #{lgort,jdbcType=VARCHAR},
      </if>
      <if test="reslo != null">
        #{reslo,jdbcType=VARCHAR},
      </if>
      <if test="msg != null">
        #{msg,jdbcType=VARCHAR},
      </if>
      <if test="ebeln != null">
        #{ebeln,jdbcType=VARCHAR},
      </if>
      <if test="ebelp != null">
        #{ebelp,jdbcType=VARCHAR},
      </if>
      <if test="vbeln != null">
        #{vbeln,jdbcType=VARCHAR},
      </if>
      <if test="posnr != null">
        #{posnr,jdbcType=VARCHAR},
      </if>
      <if test="brtwr != null">
        #{brtwr,jdbcType=DECIMAL},
      </if>
      <if test="kpein2 != null">
        #{kpein2,jdbcType=DECIMAL},
      </if>
      <if test="zcgzb != null">
        #{zcgzb,jdbcType=VARCHAR},
      </if>
      <if test="zypurreqno != null">
        #{zypurreqno,jdbcType=VARCHAR},
      </if>
      <if test="zystorelineno != null">
        #{zystorelineno,jdbcType=VARCHAR},
      </if>
      <if test="zsfcf != null">
        #{zsfcf,jdbcType=VARCHAR},
      </if>
      <if test="zmain != null">
        #{zmain,jdbcType=VARCHAR},
      </if>
      <if test="zzxl30Js != null">
        #{zzxl30Js,jdbcType=DECIMAL},
      </if>
      <if test="zmdqtyJs != null">
        #{zmdqtyJs,jdbcType=DECIMAL},
      </if>
      <if test="zdckykc != null">
        #{zdckykc,jdbcType=DECIMAL},
      </if>
      <if test="mengeSug != null">
        #{mengeSug,jdbcType=DECIMAL},
      </if>
      <if test="zplsug != null">
        #{zplsug,jdbcType=VARCHAR},
      </if>
      <if test="zcyjswq != null">
        #{zcyjswq,jdbcType=DECIMAL},
      </if>
      <if test="labstDcJs != null">
        #{labstDcJs,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.iscm.entity.SapZmmt0287Example" resultType="java.lang.Long">
    select count(*) from SAP_ZMMT0287
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update SAP_ZMMT0287
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.mandt != null">
        MANDT = #{record.mandt,jdbcType=VARCHAR},
      </if>
      <if test="record.purreqno != null">
        PURREQNO = #{record.purreqno,jdbcType=VARCHAR},
      </if>
      <if test="record.storelineno != null">
        STORELINENO = #{record.storelineno,jdbcType=VARCHAR},
      </if>
      <if test="record.bukrs != null">
        BUKRS = #{record.bukrs,jdbcType=VARCHAR},
      </if>
      <if test="record.ekorg != null">
        EKORG = #{record.ekorg,jdbcType=VARCHAR},
      </if>
      <if test="record.werks != null">
        WERKS = #{record.werks,jdbcType=VARCHAR},
      </if>
      <if test="record.zzzzshd != null">
        ZZZZSHD = #{record.zzzzshd,jdbcType=VARCHAR},
      </if>
      <if test="record.bsart != null">
        BSART = #{record.bsart,jdbcType=VARCHAR},
      </if>
      <if test="record.matnr != null">
        MATNR = #{record.matnr,jdbcType=VARCHAR},
      </if>
      <if test="record.menge != null">
        MENGE = #{record.menge,jdbcType=DECIMAL},
      </if>
      <if test="record.zspsl != null">
        ZSPSL = #{record.zspsl,jdbcType=DECIMAL},
      </if>
      <if test="record.meins != null">
        MEINS = #{record.meins,jdbcType=VARCHAR},
      </if>
      <if test="record.zzcxbjs != null">
        ZZCXBJS = #{record.zzcxbjs,jdbcType=VARCHAR},
      </if>
      <if test="record.zjhbhsl != null">
        ZJHBHSL = #{record.zjhbhsl,jdbcType=DECIMAL},
      </if>
      <if test="record.zyybz != null">
        ZYYBZ = #{record.zyybz,jdbcType=VARCHAR},
      </if>
      <if test="record.badat != null">
        BADAT = #{record.badat,jdbcType=VARCHAR},
      </if>
      <if test="record.zspzt != null">
        ZSPZT = #{record.zspzt,jdbcType=VARCHAR},
      </if>
      <if test="record.zclzt != null">
        ZCLZT = #{record.zclzt,jdbcType=VARCHAR},
      </if>
      <if test="record.loekz != null">
        LOEKZ = #{record.loekz,jdbcType=VARCHAR},
      </if>
      <if test="record.erdat != null">
        ERDAT = #{record.erdat,jdbcType=VARCHAR},
      </if>
      <if test="record.erzet != null">
        ERZET = #{record.erzet,jdbcType=VARCHAR},
      </if>
      <if test="record.ernam != null">
        ERNAM = #{record.ernam,jdbcType=VARCHAR},
      </if>
      <if test="record.aedat != null">
        AEDAT = #{record.aedat,jdbcType=VARCHAR},
      </if>
      <if test="record.aezet != null">
        AEZET = #{record.aezet,jdbcType=VARCHAR},
      </if>
      <if test="record.aenam != null">
        AENAM = #{record.aenam,jdbcType=VARCHAR},
      </if>
      <if test="record.zshck != null">
        ZSHCK = #{record.zshck,jdbcType=VARCHAR},
      </if>
      <if test="record.zcgygh != null">
        ZCGYGH = #{record.zcgygh,jdbcType=VARCHAR},
      </if>
      <if test="record.zpuragt != null">
        ZPURAGT = #{record.zpuragt,jdbcType=VARCHAR},
      </if>
      <if test="record.charg != null">
        CHARG = #{record.charg,jdbcType=VARCHAR},
      </if>
      <if test="record.lgort != null">
        LGORT = #{record.lgort,jdbcType=VARCHAR},
      </if>
      <if test="record.reslo != null">
        RESLO = #{record.reslo,jdbcType=VARCHAR},
      </if>
      <if test="record.msg != null">
        MSG = #{record.msg,jdbcType=VARCHAR},
      </if>
      <if test="record.ebeln != null">
        EBELN = #{record.ebeln,jdbcType=VARCHAR},
      </if>
      <if test="record.ebelp != null">
        EBELP = #{record.ebelp,jdbcType=VARCHAR},
      </if>
      <if test="record.vbeln != null">
        VBELN = #{record.vbeln,jdbcType=VARCHAR},
      </if>
      <if test="record.posnr != null">
        POSNR = #{record.posnr,jdbcType=VARCHAR},
      </if>
      <if test="record.brtwr != null">
        BRTWR = #{record.brtwr,jdbcType=DECIMAL},
      </if>
      <if test="record.kpein2 != null">
        KPEIN2 = #{record.kpein2,jdbcType=DECIMAL},
      </if>
      <if test="record.zcgzb != null">
        ZCGZB = #{record.zcgzb,jdbcType=VARCHAR},
      </if>
      <if test="record.zypurreqno != null">
        ZYPURREQNO = #{record.zypurreqno,jdbcType=VARCHAR},
      </if>
      <if test="record.zystorelineno != null">
        ZYSTORELINENO = #{record.zystorelineno,jdbcType=VARCHAR},
      </if>
      <if test="record.zsfcf != null">
        ZSFCF = #{record.zsfcf,jdbcType=VARCHAR},
      </if>
      <if test="record.zmain != null">
        ZMAIN = #{record.zmain,jdbcType=VARCHAR},
      </if>
      <if test="record.zzxl30Js != null">
        ZZXL_30_JS = #{record.zzxl30Js,jdbcType=DECIMAL},
      </if>
      <if test="record.zmdqtyJs != null">
        ZMDQTY_JS = #{record.zmdqtyJs,jdbcType=DECIMAL},
      </if>
      <if test="record.zdckykc != null">
        ZDCKYKC = #{record.zdckykc,jdbcType=DECIMAL},
      </if>
      <if test="record.mengeSug != null">
        MENGE_SUG = #{record.mengeSug,jdbcType=DECIMAL},
      </if>
      <if test="record.zplsug != null">
        ZPLSUG = #{record.zplsug,jdbcType=VARCHAR},
      </if>
      <if test="record.zcyjswq != null">
        ZCYJSWQ = #{record.zcyjswq,jdbcType=DECIMAL},
      </if>
      <if test="record.labstDcJs != null">
        LABST_DC_JS = #{record.labstDcJs,jdbcType=DECIMAL},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update SAP_ZMMT0287
    set id = #{record.id,jdbcType=BIGINT},
      MANDT = #{record.mandt,jdbcType=VARCHAR},
      PURREQNO = #{record.purreqno,jdbcType=VARCHAR},
      STORELINENO = #{record.storelineno,jdbcType=VARCHAR},
      BUKRS = #{record.bukrs,jdbcType=VARCHAR},
      EKORG = #{record.ekorg,jdbcType=VARCHAR},
      WERKS = #{record.werks,jdbcType=VARCHAR},
      ZZZZSHD = #{record.zzzzshd,jdbcType=VARCHAR},
      BSART = #{record.bsart,jdbcType=VARCHAR},
      MATNR = #{record.matnr,jdbcType=VARCHAR},
      MENGE = #{record.menge,jdbcType=DECIMAL},
      ZSPSL = #{record.zspsl,jdbcType=DECIMAL},
      MEINS = #{record.meins,jdbcType=VARCHAR},
      ZZCXBJS = #{record.zzcxbjs,jdbcType=VARCHAR},
      ZJHBHSL = #{record.zjhbhsl,jdbcType=DECIMAL},
      ZYYBZ = #{record.zyybz,jdbcType=VARCHAR},
      BADAT = #{record.badat,jdbcType=VARCHAR},
      ZSPZT = #{record.zspzt,jdbcType=VARCHAR},
      ZCLZT = #{record.zclzt,jdbcType=VARCHAR},
      LOEKZ = #{record.loekz,jdbcType=VARCHAR},
      ERDAT = #{record.erdat,jdbcType=VARCHAR},
      ERZET = #{record.erzet,jdbcType=VARCHAR},
      ERNAM = #{record.ernam,jdbcType=VARCHAR},
      AEDAT = #{record.aedat,jdbcType=VARCHAR},
      AEZET = #{record.aezet,jdbcType=VARCHAR},
      AENAM = #{record.aenam,jdbcType=VARCHAR},
      ZSHCK = #{record.zshck,jdbcType=VARCHAR},
      ZCGYGH = #{record.zcgygh,jdbcType=VARCHAR},
      ZPURAGT = #{record.zpuragt,jdbcType=VARCHAR},
      CHARG = #{record.charg,jdbcType=VARCHAR},
      LGORT = #{record.lgort,jdbcType=VARCHAR},
      RESLO = #{record.reslo,jdbcType=VARCHAR},
      MSG = #{record.msg,jdbcType=VARCHAR},
      EBELN = #{record.ebeln,jdbcType=VARCHAR},
      EBELP = #{record.ebelp,jdbcType=VARCHAR},
      VBELN = #{record.vbeln,jdbcType=VARCHAR},
      POSNR = #{record.posnr,jdbcType=VARCHAR},
      BRTWR = #{record.brtwr,jdbcType=DECIMAL},
      KPEIN2 = #{record.kpein2,jdbcType=DECIMAL},
      ZCGZB = #{record.zcgzb,jdbcType=VARCHAR},
      ZYPURREQNO = #{record.zypurreqno,jdbcType=VARCHAR},
      ZYSTORELINENO = #{record.zystorelineno,jdbcType=VARCHAR},
      ZSFCF = #{record.zsfcf,jdbcType=VARCHAR},
      ZMAIN = #{record.zmain,jdbcType=VARCHAR},
      ZZXL_30_JS = #{record.zzxl30Js,jdbcType=DECIMAL},
      ZMDQTY_JS = #{record.zmdqtyJs,jdbcType=DECIMAL},
      ZDCKYKC = #{record.zdckykc,jdbcType=DECIMAL},
      MENGE_SUG = #{record.mengeSug,jdbcType=DECIMAL},
      ZPLSUG = #{record.zplsug,jdbcType=VARCHAR},
      ZCYJSWQ = #{record.zcyjswq,jdbcType=DECIMAL},
      LABST_DC_JS = #{record.labstDcJs,jdbcType=DECIMAL}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.iscm.entity.SapZmmt0287">
    update SAP_ZMMT0287
    <set>
      <if test="mandt != null">
        MANDT = #{mandt,jdbcType=VARCHAR},
      </if>
      <if test="purreqno != null">
        PURREQNO = #{purreqno,jdbcType=VARCHAR},
      </if>
      <if test="storelineno != null">
        STORELINENO = #{storelineno,jdbcType=VARCHAR},
      </if>
      <if test="bukrs != null">
        BUKRS = #{bukrs,jdbcType=VARCHAR},
      </if>
      <if test="ekorg != null">
        EKORG = #{ekorg,jdbcType=VARCHAR},
      </if>
      <if test="werks != null">
        WERKS = #{werks,jdbcType=VARCHAR},
      </if>
      <if test="zzzzshd != null">
        ZZZZSHD = #{zzzzshd,jdbcType=VARCHAR},
      </if>
      <if test="bsart != null">
        BSART = #{bsart,jdbcType=VARCHAR},
      </if>
      <if test="matnr != null">
        MATNR = #{matnr,jdbcType=VARCHAR},
      </if>
      <if test="menge != null">
        MENGE = #{menge,jdbcType=DECIMAL},
      </if>
      <if test="zspsl != null">
        ZSPSL = #{zspsl,jdbcType=DECIMAL},
      </if>
      <if test="meins != null">
        MEINS = #{meins,jdbcType=VARCHAR},
      </if>
      <if test="zzcxbjs != null">
        ZZCXBJS = #{zzcxbjs,jdbcType=VARCHAR},
      </if>
      <if test="zjhbhsl != null">
        ZJHBHSL = #{zjhbhsl,jdbcType=DECIMAL},
      </if>
      <if test="zyybz != null">
        ZYYBZ = #{zyybz,jdbcType=VARCHAR},
      </if>
      <if test="badat != null">
        BADAT = #{badat,jdbcType=VARCHAR},
      </if>
      <if test="zspzt != null">
        ZSPZT = #{zspzt,jdbcType=VARCHAR},
      </if>
      <if test="zclzt != null">
        ZCLZT = #{zclzt,jdbcType=VARCHAR},
      </if>
      <if test="loekz != null">
        LOEKZ = #{loekz,jdbcType=VARCHAR},
      </if>
      <if test="erdat != null">
        ERDAT = #{erdat,jdbcType=VARCHAR},
      </if>
      <if test="erzet != null">
        ERZET = #{erzet,jdbcType=VARCHAR},
      </if>
      <if test="ernam != null">
        ERNAM = #{ernam,jdbcType=VARCHAR},
      </if>
      <if test="aedat != null">
        AEDAT = #{aedat,jdbcType=VARCHAR},
      </if>
      <if test="aezet != null">
        AEZET = #{aezet,jdbcType=VARCHAR},
      </if>
      <if test="aenam != null">
        AENAM = #{aenam,jdbcType=VARCHAR},
      </if>
      <if test="zshck != null">
        ZSHCK = #{zshck,jdbcType=VARCHAR},
      </if>
      <if test="zcgygh != null">
        ZCGYGH = #{zcgygh,jdbcType=VARCHAR},
      </if>
      <if test="zpuragt != null">
        ZPURAGT = #{zpuragt,jdbcType=VARCHAR},
      </if>
      <if test="charg != null">
        CHARG = #{charg,jdbcType=VARCHAR},
      </if>
      <if test="lgort != null">
        LGORT = #{lgort,jdbcType=VARCHAR},
      </if>
      <if test="reslo != null">
        RESLO = #{reslo,jdbcType=VARCHAR},
      </if>
      <if test="msg != null">
        MSG = #{msg,jdbcType=VARCHAR},
      </if>
      <if test="ebeln != null">
        EBELN = #{ebeln,jdbcType=VARCHAR},
      </if>
      <if test="ebelp != null">
        EBELP = #{ebelp,jdbcType=VARCHAR},
      </if>
      <if test="vbeln != null">
        VBELN = #{vbeln,jdbcType=VARCHAR},
      </if>
      <if test="posnr != null">
        POSNR = #{posnr,jdbcType=VARCHAR},
      </if>
      <if test="brtwr != null">
        BRTWR = #{brtwr,jdbcType=DECIMAL},
      </if>
      <if test="kpein2 != null">
        KPEIN2 = #{kpein2,jdbcType=DECIMAL},
      </if>
      <if test="zcgzb != null">
        ZCGZB = #{zcgzb,jdbcType=VARCHAR},
      </if>
      <if test="zypurreqno != null">
        ZYPURREQNO = #{zypurreqno,jdbcType=VARCHAR},
      </if>
      <if test="zystorelineno != null">
        ZYSTORELINENO = #{zystorelineno,jdbcType=VARCHAR},
      </if>
      <if test="zsfcf != null">
        ZSFCF = #{zsfcf,jdbcType=VARCHAR},
      </if>
      <if test="zmain != null">
        ZMAIN = #{zmain,jdbcType=VARCHAR},
      </if>
      <if test="zzxl30Js != null">
        ZZXL_30_JS = #{zzxl30Js,jdbcType=DECIMAL},
      </if>
      <if test="zmdqtyJs != null">
        ZMDQTY_JS = #{zmdqtyJs,jdbcType=DECIMAL},
      </if>
      <if test="zdckykc != null">
        ZDCKYKC = #{zdckykc,jdbcType=DECIMAL},
      </if>
      <if test="mengeSug != null">
        MENGE_SUG = #{mengeSug,jdbcType=DECIMAL},
      </if>
      <if test="zplsug != null">
        ZPLSUG = #{zplsug,jdbcType=VARCHAR},
      </if>
      <if test="zcyjswq != null">
        ZCYJSWQ = #{zcyjswq,jdbcType=DECIMAL},
      </if>
      <if test="labstDcJs != null">
        LABST_DC_JS = #{labstDcJs,jdbcType=DECIMAL},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.iscm.entity.SapZmmt0287">
    update SAP_ZMMT0287
    set MANDT = #{mandt,jdbcType=VARCHAR},
      PURREQNO = #{purreqno,jdbcType=VARCHAR},
      STORELINENO = #{storelineno,jdbcType=VARCHAR},
      BUKRS = #{bukrs,jdbcType=VARCHAR},
      EKORG = #{ekorg,jdbcType=VARCHAR},
      WERKS = #{werks,jdbcType=VARCHAR},
      ZZZZSHD = #{zzzzshd,jdbcType=VARCHAR},
      BSART = #{bsart,jdbcType=VARCHAR},
      MATNR = #{matnr,jdbcType=VARCHAR},
      MENGE = #{menge,jdbcType=DECIMAL},
      ZSPSL = #{zspsl,jdbcType=DECIMAL},
      MEINS = #{meins,jdbcType=VARCHAR},
      ZZCXBJS = #{zzcxbjs,jdbcType=VARCHAR},
      ZJHBHSL = #{zjhbhsl,jdbcType=DECIMAL},
      ZYYBZ = #{zyybz,jdbcType=VARCHAR},
      BADAT = #{badat,jdbcType=VARCHAR},
      ZSPZT = #{zspzt,jdbcType=VARCHAR},
      ZCLZT = #{zclzt,jdbcType=VARCHAR},
      LOEKZ = #{loekz,jdbcType=VARCHAR},
      ERDAT = #{erdat,jdbcType=VARCHAR},
      ERZET = #{erzet,jdbcType=VARCHAR},
      ERNAM = #{ernam,jdbcType=VARCHAR},
      AEDAT = #{aedat,jdbcType=VARCHAR},
      AEZET = #{aezet,jdbcType=VARCHAR},
      AENAM = #{aenam,jdbcType=VARCHAR},
      ZSHCK = #{zshck,jdbcType=VARCHAR},
      ZCGYGH = #{zcgygh,jdbcType=VARCHAR},
      ZPURAGT = #{zpuragt,jdbcType=VARCHAR},
      CHARG = #{charg,jdbcType=VARCHAR},
      LGORT = #{lgort,jdbcType=VARCHAR},
      RESLO = #{reslo,jdbcType=VARCHAR},
      MSG = #{msg,jdbcType=VARCHAR},
      EBELN = #{ebeln,jdbcType=VARCHAR},
      EBELP = #{ebelp,jdbcType=VARCHAR},
      VBELN = #{vbeln,jdbcType=VARCHAR},
      POSNR = #{posnr,jdbcType=VARCHAR},
      BRTWR = #{brtwr,jdbcType=DECIMAL},
      KPEIN2 = #{kpein2,jdbcType=DECIMAL},
      ZCGZB = #{zcgzb,jdbcType=VARCHAR},
      ZYPURREQNO = #{zypurreqno,jdbcType=VARCHAR},
      ZYSTORELINENO = #{zystorelineno,jdbcType=VARCHAR},
      ZSFCF = #{zsfcf,jdbcType=VARCHAR},
      ZMAIN = #{zmain,jdbcType=VARCHAR},
      ZZXL_30_JS = #{zzxl30Js,jdbcType=DECIMAL},
      ZMDQTY_JS = #{zmdqtyJs,jdbcType=DECIMAL},
      ZDCKYKC = #{zdckykc,jdbcType=DECIMAL},
      MENGE_SUG = #{mengeSug,jdbcType=DECIMAL},
      ZPLSUG = #{zplsug,jdbcType=VARCHAR},
      ZCYJSWQ = #{zcyjswq,jdbcType=DECIMAL},
      LABST_DC_JS = #{labstDcJs,jdbcType=DECIMAL}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>