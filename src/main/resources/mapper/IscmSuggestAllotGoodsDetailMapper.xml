<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.IscmSuggestAllotGoodsDetailMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.IscmSuggestAllotGoodsDetail">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="business_date" jdbcType="TIMESTAMP" property="businessDate" />
    <result column="record_id" jdbcType="BIGINT" property="recordId" />
    <result column="allot_type" jdbcType="TINYINT" property="allotType" />
    <result column="register_no" jdbcType="VARCHAR" property="registerNo" />
    <result column="register_source" jdbcType="TINYINT" property="registerSource" />
    <result column="register_by" jdbcType="BIGINT" property="registerBy" />
    <result column="register_name" jdbcType="VARCHAR" property="registerName" />
    <result column="register_time" jdbcType="TIMESTAMP" property="registerTime" />
    <result column="platform_org_id" jdbcType="BIGINT" property="platformOrgId" />
    <result column="platform_org_name" jdbcType="VARCHAR" property="platformOrgName" />
    <result column="out_company_id" jdbcType="BIGINT" property="outCompanyId" />
    <result column="out_company_code" jdbcType="VARCHAR" property="outCompanyCode" />
    <result column="out_company_name" jdbcType="VARCHAR" property="outCompanyName" />
    <result column="in_company_id" jdbcType="BIGINT" property="inCompanyId" />
    <result column="in_company_code" jdbcType="VARCHAR" property="inCompanyCode" />
    <result column="in_company_name" jdbcType="VARCHAR" property="inCompanyName" />
    <result column="out_store_id" jdbcType="BIGINT" property="outStoreId" />
    <result column="out_store_code" jdbcType="VARCHAR" property="outStoreCode" />
    <result column="out_store_name" jdbcType="VARCHAR" property="outStoreName" />
    <result column="out_store_attr" jdbcType="VARCHAR" property="outStoreAttr" />
    <result column="out_store_sales_level" jdbcType="VARCHAR" property="outStoreSalesLevel" />
    <result column="in_store_id" jdbcType="BIGINT" property="inStoreId" />
    <result column="in_store_code" jdbcType="VARCHAR" property="inStoreCode" />
    <result column="in_store_name" jdbcType="VARCHAR" property="inStoreName" />
    <result column="in_store_attr" jdbcType="VARCHAR" property="inStoreAttr" />
    <result column="in_store_sales_level" jdbcType="VARCHAR" property="inStoreSalesLevel" />
    <result column="allot_group_code" jdbcType="VARCHAR" property="allotGroupCode" />
    <result column="allot_group_name" jdbcType="VARCHAR" property="allotGroupName" />
    <result column="allot_no" jdbcType="VARCHAR" property="allotNo" />
    <result column="pos_allot_no" jdbcType="VARCHAR" property="posAllotNo" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="goods_desc" jdbcType="VARCHAR" property="goodsDesc" />
    <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
    <result column="expiry_time" jdbcType="TIMESTAMP" property="expiryTime" />
    <result column="goods_common_name" jdbcType="VARCHAR" property="goodsCommonName" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="dosage_form" jdbcType="VARCHAR" property="dosageForm" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="season" jdbcType="TINYINT" property="season" />
    <result column="return_condition" jdbcType="VARCHAR" property="returnCondition" />
    <result column="suggest_allot_quantity" jdbcType="DECIMAL" property="suggestAllotQuantity" />
    <result column="real_allot_quantity" jdbcType="DECIMAL" property="realAllotQuantity" />
    <result column="transfer_cost_amount" jdbcType="DECIMAL" property="transferCostAmount" />
    <result column="in_store_thirty_days_sales" jdbcType="DECIMAL" property="inStoreThirtyDaysSales" />
    <result column="out_store_thirty_days_sales" jdbcType="DECIMAL" property="outStoreThirtyDaysSales" />
    <result column="in_store_thirty_days_sale_times" jdbcType="DECIMAL" property="inStoreThirtyDaysSaleTimes" />
    <result column="out_store_thirty_days_sale_times" jdbcType="DECIMAL" property="outStoreThirtyDaysSaleTimes" />
    <result column="in_store_stock" jdbcType="DECIMAL" property="inStoreStock" />
    <result column="out_store_stock" jdbcType="DECIMAL" property="outStoreStock" />
    <result column="in_store_stock_min" jdbcType="DECIMAL" property="inStoreStockMin" />
    <result column="out_store_stock_min" jdbcType="DECIMAL" property="outStoreStockMin" />
    <result column="in_store_stock_max" jdbcType="DECIMAL" property="inStoreStockMax" />
    <result column="out_store_stock_max" jdbcType="DECIMAL" property="outStoreStockMax" />
    <result column="in_store_stock_onway" jdbcType="DECIMAL" property="inStoreStockOnway" />
    <result column="out_store_stock_onway" jdbcType="DECIMAL" property="outStoreStockOnway" />
    <result column="in_store_pos_daily_sales" jdbcType="DECIMAL" property="inStorePosDailySales" />
    <result column="out_store_pos_daily_sales" jdbcType="DECIMAL" property="outStorePosDailySales" />
    <result column="in_store_estimate_sales_days" jdbcType="DECIMAL" property="inStoreEstimateSalesDays" />
    <result column="out_store_estimate_sales_days" jdbcType="DECIMAL" property="outStoreEstimateSalesDays" />
    <result column="approve_status" jdbcType="TINYINT" property="approveStatus" />
    <result column="approve_result" jdbcType="VARCHAR" property="approveResult" />
    <result column="approve_by" jdbcType="BIGINT" property="approveBy" />
    <result column="approve_name" jdbcType="VARCHAR" property="approveName" />
    <result column="approve_time" jdbcType="TIMESTAMP" property="approveTime" />
    <result column="goodsline" jdbcType="VARCHAR" property="goodsline" />
    <result column="pushlevel" jdbcType="VARCHAR" property="pushlevel" />
    <result column="goods_marks" jdbcType="VARCHAR" property="goodsMarks" />
    <result column="short_manufacturer" jdbcType="VARCHAR" property="shortManufacturer" />
    <result column="specifications" jdbcType="VARCHAR" property="specifications" />
    <result column="model_code" jdbcType="VARCHAR" property="modelCode" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, business_date, record_id, allot_type, register_no, register_source, register_by, 
    register_name, register_time, platform_org_id, platform_org_name, out_company_id, 
    out_company_code, out_company_name, in_company_id, in_company_code, in_company_name, 
    out_store_id, out_store_code, out_store_name, out_store_attr, out_store_sales_level, 
    in_store_id, in_store_code, in_store_name, in_store_attr, in_store_sales_level, allot_group_code, 
    allot_group_name, allot_no, pos_allot_no, goods_no, goods_desc, batch_no, expiry_time, 
    goods_common_name, manufacturer, dosage_form, unit, season, return_condition, suggest_allot_quantity, 
    real_allot_quantity, transfer_cost_amount, in_store_thirty_days_sales, out_store_thirty_days_sales, 
    in_store_thirty_days_sale_times, out_store_thirty_days_sale_times, in_store_stock, 
    out_store_stock, in_store_stock_min, out_store_stock_min, in_store_stock_max, out_store_stock_max, 
    in_store_stock_onway, out_store_stock_onway, in_store_pos_daily_sales, out_store_pos_daily_sales, 
    in_store_estimate_sales_days, out_store_estimate_sales_days, approve_status, approve_result, 
    approve_by, approve_name, approve_time, goodsline, pushlevel, goods_marks, short_manufacturer, 
    specifications, model_code, `status`, gmt_create, gmt_update, extend, version, created_by, 
    created_name, updated_by, updated_name
  </sql>
  <select id="selectByExample" parameterType="com.cowell.iscm.entity.IscmSuggestAllotGoodsDetailExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from iscm_suggest_allot_goods_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from iscm_suggest_allot_goods_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from iscm_suggest_allot_goods_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.iscm.entity.IscmSuggestAllotGoodsDetailExample">
    delete from iscm_suggest_allot_goods_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entity.IscmSuggestAllotGoodsDetail" useGeneratedKeys="true">
    insert into iscm_suggest_allot_goods_detail (business_date, record_id, allot_type, 
      register_no, register_source, register_by, 
      register_name, register_time, platform_org_id, 
      platform_org_name, out_company_id, out_company_code, 
      out_company_name, in_company_id, in_company_code, 
      in_company_name, out_store_id, out_store_code, 
      out_store_name, out_store_attr, out_store_sales_level, 
      in_store_id, in_store_code, in_store_name, 
      in_store_attr, in_store_sales_level, allot_group_code, 
      allot_group_name, allot_no, pos_allot_no, 
      goods_no, goods_desc, batch_no, 
      expiry_time, goods_common_name, manufacturer, 
      dosage_form, unit, season, 
      return_condition, suggest_allot_quantity, 
      real_allot_quantity, transfer_cost_amount, 
      in_store_thirty_days_sales, out_store_thirty_days_sales, 
      in_store_thirty_days_sale_times, out_store_thirty_days_sale_times, 
      in_store_stock, out_store_stock, in_store_stock_min, 
      out_store_stock_min, in_store_stock_max, out_store_stock_max, 
      in_store_stock_onway, out_store_stock_onway, 
      in_store_pos_daily_sales, out_store_pos_daily_sales, 
      in_store_estimate_sales_days, out_store_estimate_sales_days, 
      approve_status, approve_result, approve_by, 
      approve_name, approve_time, goodsline, 
      pushlevel, goods_marks, short_manufacturer, 
      specifications, model_code, `status`, 
      gmt_create, gmt_update, extend, 
      version, created_by, created_name, 
      updated_by, updated_name)
    values (#{businessDate,jdbcType=TIMESTAMP}, #{recordId,jdbcType=BIGINT}, #{allotType,jdbcType=TINYINT}, 
      #{registerNo,jdbcType=VARCHAR}, #{registerSource,jdbcType=TINYINT}, #{registerBy,jdbcType=BIGINT}, 
      #{registerName,jdbcType=VARCHAR}, #{registerTime,jdbcType=TIMESTAMP}, #{platformOrgId,jdbcType=BIGINT}, 
      #{platformOrgName,jdbcType=VARCHAR}, #{outCompanyId,jdbcType=BIGINT}, #{outCompanyCode,jdbcType=VARCHAR}, 
      #{outCompanyName,jdbcType=VARCHAR}, #{inCompanyId,jdbcType=BIGINT}, #{inCompanyCode,jdbcType=VARCHAR}, 
      #{inCompanyName,jdbcType=VARCHAR}, #{outStoreId,jdbcType=BIGINT}, #{outStoreCode,jdbcType=VARCHAR}, 
      #{outStoreName,jdbcType=VARCHAR}, #{outStoreAttr,jdbcType=VARCHAR}, #{outStoreSalesLevel,jdbcType=VARCHAR}, 
      #{inStoreId,jdbcType=BIGINT}, #{inStoreCode,jdbcType=VARCHAR}, #{inStoreName,jdbcType=VARCHAR}, 
      #{inStoreAttr,jdbcType=VARCHAR}, #{inStoreSalesLevel,jdbcType=VARCHAR}, #{allotGroupCode,jdbcType=VARCHAR}, 
      #{allotGroupName,jdbcType=VARCHAR}, #{allotNo,jdbcType=VARCHAR}, #{posAllotNo,jdbcType=VARCHAR}, 
      #{goodsNo,jdbcType=VARCHAR}, #{goodsDesc,jdbcType=VARCHAR}, #{batchNo,jdbcType=VARCHAR}, 
      #{expiryTime,jdbcType=TIMESTAMP}, #{goodsCommonName,jdbcType=VARCHAR}, #{manufacturer,jdbcType=VARCHAR}, 
      #{dosageForm,jdbcType=VARCHAR}, #{unit,jdbcType=VARCHAR}, #{season,jdbcType=TINYINT}, 
      #{returnCondition,jdbcType=VARCHAR}, #{suggestAllotQuantity,jdbcType=DECIMAL}, 
      #{realAllotQuantity,jdbcType=DECIMAL}, #{transferCostAmount,jdbcType=DECIMAL}, 
      #{inStoreThirtyDaysSales,jdbcType=DECIMAL}, #{outStoreThirtyDaysSales,jdbcType=DECIMAL}, 
      #{inStoreThirtyDaysSaleTimes,jdbcType=DECIMAL}, #{outStoreThirtyDaysSaleTimes,jdbcType=DECIMAL}, 
      #{inStoreStock,jdbcType=DECIMAL}, #{outStoreStock,jdbcType=DECIMAL}, #{inStoreStockMin,jdbcType=DECIMAL}, 
      #{outStoreStockMin,jdbcType=DECIMAL}, #{inStoreStockMax,jdbcType=DECIMAL}, #{outStoreStockMax,jdbcType=DECIMAL}, 
      #{inStoreStockOnway,jdbcType=DECIMAL}, #{outStoreStockOnway,jdbcType=DECIMAL}, 
      #{inStorePosDailySales,jdbcType=DECIMAL}, #{outStorePosDailySales,jdbcType=DECIMAL}, 
      #{inStoreEstimateSalesDays,jdbcType=DECIMAL}, #{outStoreEstimateSalesDays,jdbcType=DECIMAL}, 
      #{approveStatus,jdbcType=TINYINT}, #{approveResult,jdbcType=VARCHAR}, #{approveBy,jdbcType=BIGINT}, 
      #{approveName,jdbcType=VARCHAR}, #{approveTime,jdbcType=TIMESTAMP}, #{goodsline,jdbcType=VARCHAR}, 
      #{pushlevel,jdbcType=VARCHAR}, #{goodsMarks,jdbcType=VARCHAR}, #{shortManufacturer,jdbcType=VARCHAR}, 
      #{specifications,jdbcType=VARCHAR}, #{modelCode,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}, #{extend,jdbcType=VARCHAR}, 
      #{version,jdbcType=INTEGER}, #{createdBy,jdbcType=BIGINT}, #{createdName,jdbcType=VARCHAR}, 
      #{updatedBy,jdbcType=BIGINT}, #{updatedName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entity.IscmSuggestAllotGoodsDetail" useGeneratedKeys="true">
    insert into iscm_suggest_allot_goods_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessDate != null">
        business_date,
      </if>
      <if test="recordId != null">
        record_id,
      </if>
      <if test="allotType != null">
        allot_type,
      </if>
      <if test="registerNo != null">
        register_no,
      </if>
      <if test="registerSource != null">
        register_source,
      </if>
      <if test="registerBy != null">
        register_by,
      </if>
      <if test="registerName != null">
        register_name,
      </if>
      <if test="registerTime != null">
        register_time,
      </if>
      <if test="platformOrgId != null">
        platform_org_id,
      </if>
      <if test="platformOrgName != null">
        platform_org_name,
      </if>
      <if test="outCompanyId != null">
        out_company_id,
      </if>
      <if test="outCompanyCode != null">
        out_company_code,
      </if>
      <if test="outCompanyName != null">
        out_company_name,
      </if>
      <if test="inCompanyId != null">
        in_company_id,
      </if>
      <if test="inCompanyCode != null">
        in_company_code,
      </if>
      <if test="inCompanyName != null">
        in_company_name,
      </if>
      <if test="outStoreId != null">
        out_store_id,
      </if>
      <if test="outStoreCode != null">
        out_store_code,
      </if>
      <if test="outStoreName != null">
        out_store_name,
      </if>
      <if test="outStoreAttr != null">
        out_store_attr,
      </if>
      <if test="outStoreSalesLevel != null">
        out_store_sales_level,
      </if>
      <if test="inStoreId != null">
        in_store_id,
      </if>
      <if test="inStoreCode != null">
        in_store_code,
      </if>
      <if test="inStoreName != null">
        in_store_name,
      </if>
      <if test="inStoreAttr != null">
        in_store_attr,
      </if>
      <if test="inStoreSalesLevel != null">
        in_store_sales_level,
      </if>
      <if test="allotGroupCode != null">
        allot_group_code,
      </if>
      <if test="allotGroupName != null">
        allot_group_name,
      </if>
      <if test="allotNo != null">
        allot_no,
      </if>
      <if test="posAllotNo != null">
        pos_allot_no,
      </if>
      <if test="goodsNo != null">
        goods_no,
      </if>
      <if test="goodsDesc != null">
        goods_desc,
      </if>
      <if test="batchNo != null">
        batch_no,
      </if>
      <if test="expiryTime != null">
        expiry_time,
      </if>
      <if test="goodsCommonName != null">
        goods_common_name,
      </if>
      <if test="manufacturer != null">
        manufacturer,
      </if>
      <if test="dosageForm != null">
        dosage_form,
      </if>
      <if test="unit != null">
        unit,
      </if>
      <if test="season != null">
        season,
      </if>
      <if test="returnCondition != null">
        return_condition,
      </if>
      <if test="suggestAllotQuantity != null">
        suggest_allot_quantity,
      </if>
      <if test="realAllotQuantity != null">
        real_allot_quantity,
      </if>
      <if test="transferCostAmount != null">
        transfer_cost_amount,
      </if>
      <if test="inStoreThirtyDaysSales != null">
        in_store_thirty_days_sales,
      </if>
      <if test="outStoreThirtyDaysSales != null">
        out_store_thirty_days_sales,
      </if>
      <if test="inStoreThirtyDaysSaleTimes != null">
        in_store_thirty_days_sale_times,
      </if>
      <if test="outStoreThirtyDaysSaleTimes != null">
        out_store_thirty_days_sale_times,
      </if>
      <if test="inStoreStock != null">
        in_store_stock,
      </if>
      <if test="outStoreStock != null">
        out_store_stock,
      </if>
      <if test="inStoreStockMin != null">
        in_store_stock_min,
      </if>
      <if test="outStoreStockMin != null">
        out_store_stock_min,
      </if>
      <if test="inStoreStockMax != null">
        in_store_stock_max,
      </if>
      <if test="outStoreStockMax != null">
        out_store_stock_max,
      </if>
      <if test="inStoreStockOnway != null">
        in_store_stock_onway,
      </if>
      <if test="outStoreStockOnway != null">
        out_store_stock_onway,
      </if>
      <if test="inStorePosDailySales != null">
        in_store_pos_daily_sales,
      </if>
      <if test="outStorePosDailySales != null">
        out_store_pos_daily_sales,
      </if>
      <if test="inStoreEstimateSalesDays != null">
        in_store_estimate_sales_days,
      </if>
      <if test="outStoreEstimateSalesDays != null">
        out_store_estimate_sales_days,
      </if>
      <if test="approveStatus != null">
        approve_status,
      </if>
      <if test="approveResult != null">
        approve_result,
      </if>
      <if test="approveBy != null">
        approve_by,
      </if>
      <if test="approveName != null">
        approve_name,
      </if>
      <if test="approveTime != null">
        approve_time,
      </if>
      <if test="goodsline != null">
        goodsline,
      </if>
      <if test="pushlevel != null">
        pushlevel,
      </if>
      <if test="goodsMarks != null">
        goods_marks,
      </if>
      <if test="shortManufacturer != null">
        short_manufacturer,
      </if>
      <if test="specifications != null">
        specifications,
      </if>
      <if test="modelCode != null">
        model_code,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedName != null">
        updated_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessDate != null">
        #{businessDate,jdbcType=TIMESTAMP},
      </if>
      <if test="recordId != null">
        #{recordId,jdbcType=BIGINT},
      </if>
      <if test="allotType != null">
        #{allotType,jdbcType=TINYINT},
      </if>
      <if test="registerNo != null">
        #{registerNo,jdbcType=VARCHAR},
      </if>
      <if test="registerSource != null">
        #{registerSource,jdbcType=TINYINT},
      </if>
      <if test="registerBy != null">
        #{registerBy,jdbcType=BIGINT},
      </if>
      <if test="registerName != null">
        #{registerName,jdbcType=VARCHAR},
      </if>
      <if test="registerTime != null">
        #{registerTime,jdbcType=TIMESTAMP},
      </if>
      <if test="platformOrgId != null">
        #{platformOrgId,jdbcType=BIGINT},
      </if>
      <if test="platformOrgName != null">
        #{platformOrgName,jdbcType=VARCHAR},
      </if>
      <if test="outCompanyId != null">
        #{outCompanyId,jdbcType=BIGINT},
      </if>
      <if test="outCompanyCode != null">
        #{outCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="outCompanyName != null">
        #{outCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="inCompanyId != null">
        #{inCompanyId,jdbcType=BIGINT},
      </if>
      <if test="inCompanyCode != null">
        #{inCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="inCompanyName != null">
        #{inCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="outStoreId != null">
        #{outStoreId,jdbcType=BIGINT},
      </if>
      <if test="outStoreCode != null">
        #{outStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="outStoreName != null">
        #{outStoreName,jdbcType=VARCHAR},
      </if>
      <if test="outStoreAttr != null">
        #{outStoreAttr,jdbcType=VARCHAR},
      </if>
      <if test="outStoreSalesLevel != null">
        #{outStoreSalesLevel,jdbcType=VARCHAR},
      </if>
      <if test="inStoreId != null">
        #{inStoreId,jdbcType=BIGINT},
      </if>
      <if test="inStoreCode != null">
        #{inStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="inStoreName != null">
        #{inStoreName,jdbcType=VARCHAR},
      </if>
      <if test="inStoreAttr != null">
        #{inStoreAttr,jdbcType=VARCHAR},
      </if>
      <if test="inStoreSalesLevel != null">
        #{inStoreSalesLevel,jdbcType=VARCHAR},
      </if>
      <if test="allotGroupCode != null">
        #{allotGroupCode,jdbcType=VARCHAR},
      </if>
      <if test="allotGroupName != null">
        #{allotGroupName,jdbcType=VARCHAR},
      </if>
      <if test="allotNo != null">
        #{allotNo,jdbcType=VARCHAR},
      </if>
      <if test="posAllotNo != null">
        #{posAllotNo,jdbcType=VARCHAR},
      </if>
      <if test="goodsNo != null">
        #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="goodsDesc != null">
        #{goodsDesc,jdbcType=VARCHAR},
      </if>
      <if test="batchNo != null">
        #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="expiryTime != null">
        #{expiryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="goodsCommonName != null">
        #{goodsCommonName,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="dosageForm != null">
        #{dosageForm,jdbcType=VARCHAR},
      </if>
      <if test="unit != null">
        #{unit,jdbcType=VARCHAR},
      </if>
      <if test="season != null">
        #{season,jdbcType=TINYINT},
      </if>
      <if test="returnCondition != null">
        #{returnCondition,jdbcType=VARCHAR},
      </if>
      <if test="suggestAllotQuantity != null">
        #{suggestAllotQuantity,jdbcType=DECIMAL},
      </if>
      <if test="realAllotQuantity != null">
        #{realAllotQuantity,jdbcType=DECIMAL},
      </if>
      <if test="transferCostAmount != null">
        #{transferCostAmount,jdbcType=DECIMAL},
      </if>
      <if test="inStoreThirtyDaysSales != null">
        #{inStoreThirtyDaysSales,jdbcType=DECIMAL},
      </if>
      <if test="outStoreThirtyDaysSales != null">
        #{outStoreThirtyDaysSales,jdbcType=DECIMAL},
      </if>
      <if test="inStoreThirtyDaysSaleTimes != null">
        #{inStoreThirtyDaysSaleTimes,jdbcType=DECIMAL},
      </if>
      <if test="outStoreThirtyDaysSaleTimes != null">
        #{outStoreThirtyDaysSaleTimes,jdbcType=DECIMAL},
      </if>
      <if test="inStoreStock != null">
        #{inStoreStock,jdbcType=DECIMAL},
      </if>
      <if test="outStoreStock != null">
        #{outStoreStock,jdbcType=DECIMAL},
      </if>
      <if test="inStoreStockMin != null">
        #{inStoreStockMin,jdbcType=DECIMAL},
      </if>
      <if test="outStoreStockMin != null">
        #{outStoreStockMin,jdbcType=DECIMAL},
      </if>
      <if test="inStoreStockMax != null">
        #{inStoreStockMax,jdbcType=DECIMAL},
      </if>
      <if test="outStoreStockMax != null">
        #{outStoreStockMax,jdbcType=DECIMAL},
      </if>
      <if test="inStoreStockOnway != null">
        #{inStoreStockOnway,jdbcType=DECIMAL},
      </if>
      <if test="outStoreStockOnway != null">
        #{outStoreStockOnway,jdbcType=DECIMAL},
      </if>
      <if test="inStorePosDailySales != null">
        #{inStorePosDailySales,jdbcType=DECIMAL},
      </if>
      <if test="outStorePosDailySales != null">
        #{outStorePosDailySales,jdbcType=DECIMAL},
      </if>
      <if test="inStoreEstimateSalesDays != null">
        #{inStoreEstimateSalesDays,jdbcType=DECIMAL},
      </if>
      <if test="outStoreEstimateSalesDays != null">
        #{outStoreEstimateSalesDays,jdbcType=DECIMAL},
      </if>
      <if test="approveStatus != null">
        #{approveStatus,jdbcType=TINYINT},
      </if>
      <if test="approveResult != null">
        #{approveResult,jdbcType=VARCHAR},
      </if>
      <if test="approveBy != null">
        #{approveBy,jdbcType=BIGINT},
      </if>
      <if test="approveName != null">
        #{approveName,jdbcType=VARCHAR},
      </if>
      <if test="approveTime != null">
        #{approveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="goodsline != null">
        #{goodsline,jdbcType=VARCHAR},
      </if>
      <if test="pushlevel != null">
        #{pushlevel,jdbcType=VARCHAR},
      </if>
      <if test="goodsMarks != null">
        #{goodsMarks,jdbcType=VARCHAR},
      </if>
      <if test="shortManufacturer != null">
        #{shortManufacturer,jdbcType=VARCHAR},
      </if>
      <if test="specifications != null">
        #{specifications,jdbcType=VARCHAR},
      </if>
      <if test="modelCode != null">
        #{modelCode,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        #{updatedName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.iscm.entity.IscmSuggestAllotGoodsDetailExample" resultType="java.lang.Long">
    select count(*) from iscm_suggest_allot_goods_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update iscm_suggest_allot_goods_detail
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.businessDate != null">
        business_date = #{record.businessDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.recordId != null">
        record_id = #{record.recordId,jdbcType=BIGINT},
      </if>
      <if test="record.allotType != null">
        allot_type = #{record.allotType,jdbcType=TINYINT},
      </if>
      <if test="record.registerNo != null">
        register_no = #{record.registerNo,jdbcType=VARCHAR},
      </if>
      <if test="record.registerSource != null">
        register_source = #{record.registerSource,jdbcType=TINYINT},
      </if>
      <if test="record.registerBy != null">
        register_by = #{record.registerBy,jdbcType=BIGINT},
      </if>
      <if test="record.registerName != null">
        register_name = #{record.registerName,jdbcType=VARCHAR},
      </if>
      <if test="record.registerTime != null">
        register_time = #{record.registerTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.platformOrgId != null">
        platform_org_id = #{record.platformOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.platformOrgName != null">
        platform_org_name = #{record.platformOrgName,jdbcType=VARCHAR},
      </if>
      <if test="record.outCompanyId != null">
        out_company_id = #{record.outCompanyId,jdbcType=BIGINT},
      </if>
      <if test="record.outCompanyCode != null">
        out_company_code = #{record.outCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.outCompanyName != null">
        out_company_name = #{record.outCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="record.inCompanyId != null">
        in_company_id = #{record.inCompanyId,jdbcType=BIGINT},
      </if>
      <if test="record.inCompanyCode != null">
        in_company_code = #{record.inCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.inCompanyName != null">
        in_company_name = #{record.inCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="record.outStoreId != null">
        out_store_id = #{record.outStoreId,jdbcType=BIGINT},
      </if>
      <if test="record.outStoreCode != null">
        out_store_code = #{record.outStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="record.outStoreName != null">
        out_store_name = #{record.outStoreName,jdbcType=VARCHAR},
      </if>
      <if test="record.outStoreAttr != null">
        out_store_attr = #{record.outStoreAttr,jdbcType=VARCHAR},
      </if>
      <if test="record.outStoreSalesLevel != null">
        out_store_sales_level = #{record.outStoreSalesLevel,jdbcType=VARCHAR},
      </if>
      <if test="record.inStoreId != null">
        in_store_id = #{record.inStoreId,jdbcType=BIGINT},
      </if>
      <if test="record.inStoreCode != null">
        in_store_code = #{record.inStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="record.inStoreName != null">
        in_store_name = #{record.inStoreName,jdbcType=VARCHAR},
      </if>
      <if test="record.inStoreAttr != null">
        in_store_attr = #{record.inStoreAttr,jdbcType=VARCHAR},
      </if>
      <if test="record.inStoreSalesLevel != null">
        in_store_sales_level = #{record.inStoreSalesLevel,jdbcType=VARCHAR},
      </if>
      <if test="record.allotGroupCode != null">
        allot_group_code = #{record.allotGroupCode,jdbcType=VARCHAR},
      </if>
      <if test="record.allotGroupName != null">
        allot_group_name = #{record.allotGroupName,jdbcType=VARCHAR},
      </if>
      <if test="record.allotNo != null">
        allot_no = #{record.allotNo,jdbcType=VARCHAR},
      </if>
      <if test="record.posAllotNo != null">
        pos_allot_no = #{record.posAllotNo,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsNo != null">
        goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsDesc != null">
        goods_desc = #{record.goodsDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.batchNo != null">
        batch_no = #{record.batchNo,jdbcType=VARCHAR},
      </if>
      <if test="record.expiryTime != null">
        expiry_time = #{record.expiryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.goodsCommonName != null">
        goods_common_name = #{record.goodsCommonName,jdbcType=VARCHAR},
      </if>
      <if test="record.manufacturer != null">
        manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="record.dosageForm != null">
        dosage_form = #{record.dosageForm,jdbcType=VARCHAR},
      </if>
      <if test="record.unit != null">
        unit = #{record.unit,jdbcType=VARCHAR},
      </if>
      <if test="record.season != null">
        season = #{record.season,jdbcType=TINYINT},
      </if>
      <if test="record.returnCondition != null">
        return_condition = #{record.returnCondition,jdbcType=VARCHAR},
      </if>
      <if test="record.suggestAllotQuantity != null">
        suggest_allot_quantity = #{record.suggestAllotQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.realAllotQuantity != null">
        real_allot_quantity = #{record.realAllotQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.transferCostAmount != null">
        transfer_cost_amount = #{record.transferCostAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.inStoreThirtyDaysSales != null">
        in_store_thirty_days_sales = #{record.inStoreThirtyDaysSales,jdbcType=DECIMAL},
      </if>
      <if test="record.outStoreThirtyDaysSales != null">
        out_store_thirty_days_sales = #{record.outStoreThirtyDaysSales,jdbcType=DECIMAL},
      </if>
      <if test="record.inStoreThirtyDaysSaleTimes != null">
        in_store_thirty_days_sale_times = #{record.inStoreThirtyDaysSaleTimes,jdbcType=DECIMAL},
      </if>
      <if test="record.outStoreThirtyDaysSaleTimes != null">
        out_store_thirty_days_sale_times = #{record.outStoreThirtyDaysSaleTimes,jdbcType=DECIMAL},
      </if>
      <if test="record.inStoreStock != null">
        in_store_stock = #{record.inStoreStock,jdbcType=DECIMAL},
      </if>
      <if test="record.outStoreStock != null">
        out_store_stock = #{record.outStoreStock,jdbcType=DECIMAL},
      </if>
      <if test="record.inStoreStockMin != null">
        in_store_stock_min = #{record.inStoreStockMin,jdbcType=DECIMAL},
      </if>
      <if test="record.outStoreStockMin != null">
        out_store_stock_min = #{record.outStoreStockMin,jdbcType=DECIMAL},
      </if>
      <if test="record.inStoreStockMax != null">
        in_store_stock_max = #{record.inStoreStockMax,jdbcType=DECIMAL},
      </if>
      <if test="record.outStoreStockMax != null">
        out_store_stock_max = #{record.outStoreStockMax,jdbcType=DECIMAL},
      </if>
      <if test="record.inStoreStockOnway != null">
        in_store_stock_onway = #{record.inStoreStockOnway,jdbcType=DECIMAL},
      </if>
      <if test="record.outStoreStockOnway != null">
        out_store_stock_onway = #{record.outStoreStockOnway,jdbcType=DECIMAL},
      </if>
      <if test="record.inStorePosDailySales != null">
        in_store_pos_daily_sales = #{record.inStorePosDailySales,jdbcType=DECIMAL},
      </if>
      <if test="record.outStorePosDailySales != null">
        out_store_pos_daily_sales = #{record.outStorePosDailySales,jdbcType=DECIMAL},
      </if>
      <if test="record.inStoreEstimateSalesDays != null">
        in_store_estimate_sales_days = #{record.inStoreEstimateSalesDays,jdbcType=DECIMAL},
      </if>
      <if test="record.outStoreEstimateSalesDays != null">
        out_store_estimate_sales_days = #{record.outStoreEstimateSalesDays,jdbcType=DECIMAL},
      </if>
      <if test="record.approveStatus != null">
        approve_status = #{record.approveStatus,jdbcType=TINYINT},
      </if>
      <if test="record.approveResult != null">
        approve_result = #{record.approveResult,jdbcType=VARCHAR},
      </if>
      <if test="record.approveBy != null">
        approve_by = #{record.approveBy,jdbcType=BIGINT},
      </if>
      <if test="record.approveName != null">
        approve_name = #{record.approveName,jdbcType=VARCHAR},
      </if>
      <if test="record.approveTime != null">
        approve_time = #{record.approveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.goodsline != null">
        goodsline = #{record.goodsline,jdbcType=VARCHAR},
      </if>
      <if test="record.pushlevel != null">
        pushlevel = #{record.pushlevel,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsMarks != null">
        goods_marks = #{record.goodsMarks,jdbcType=VARCHAR},
      </if>
      <if test="record.shortManufacturer != null">
        short_manufacturer = #{record.shortManufacturer,jdbcType=VARCHAR},
      </if>
      <if test="record.specifications != null">
        specifications = #{record.specifications,jdbcType=VARCHAR},
      </if>
      <if test="record.modelCode != null">
        model_code = #{record.modelCode,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedName != null">
        updated_name = #{record.updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update iscm_suggest_allot_goods_detail
    set id = #{record.id,jdbcType=BIGINT},
      business_date = #{record.businessDate,jdbcType=TIMESTAMP},
      record_id = #{record.recordId,jdbcType=BIGINT},
      allot_type = #{record.allotType,jdbcType=TINYINT},
      register_no = #{record.registerNo,jdbcType=VARCHAR},
      register_source = #{record.registerSource,jdbcType=TINYINT},
      register_by = #{record.registerBy,jdbcType=BIGINT},
      register_name = #{record.registerName,jdbcType=VARCHAR},
      register_time = #{record.registerTime,jdbcType=TIMESTAMP},
      platform_org_id = #{record.platformOrgId,jdbcType=BIGINT},
      platform_org_name = #{record.platformOrgName,jdbcType=VARCHAR},
      out_company_id = #{record.outCompanyId,jdbcType=BIGINT},
      out_company_code = #{record.outCompanyCode,jdbcType=VARCHAR},
      out_company_name = #{record.outCompanyName,jdbcType=VARCHAR},
      in_company_id = #{record.inCompanyId,jdbcType=BIGINT},
      in_company_code = #{record.inCompanyCode,jdbcType=VARCHAR},
      in_company_name = #{record.inCompanyName,jdbcType=VARCHAR},
      out_store_id = #{record.outStoreId,jdbcType=BIGINT},
      out_store_code = #{record.outStoreCode,jdbcType=VARCHAR},
      out_store_name = #{record.outStoreName,jdbcType=VARCHAR},
      out_store_attr = #{record.outStoreAttr,jdbcType=VARCHAR},
      out_store_sales_level = #{record.outStoreSalesLevel,jdbcType=VARCHAR},
      in_store_id = #{record.inStoreId,jdbcType=BIGINT},
      in_store_code = #{record.inStoreCode,jdbcType=VARCHAR},
      in_store_name = #{record.inStoreName,jdbcType=VARCHAR},
      in_store_attr = #{record.inStoreAttr,jdbcType=VARCHAR},
      in_store_sales_level = #{record.inStoreSalesLevel,jdbcType=VARCHAR},
      allot_group_code = #{record.allotGroupCode,jdbcType=VARCHAR},
      allot_group_name = #{record.allotGroupName,jdbcType=VARCHAR},
      allot_no = #{record.allotNo,jdbcType=VARCHAR},
      pos_allot_no = #{record.posAllotNo,jdbcType=VARCHAR},
      goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      goods_desc = #{record.goodsDesc,jdbcType=VARCHAR},
      batch_no = #{record.batchNo,jdbcType=VARCHAR},
      expiry_time = #{record.expiryTime,jdbcType=TIMESTAMP},
      goods_common_name = #{record.goodsCommonName,jdbcType=VARCHAR},
      manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      dosage_form = #{record.dosageForm,jdbcType=VARCHAR},
      unit = #{record.unit,jdbcType=VARCHAR},
      season = #{record.season,jdbcType=TINYINT},
      return_condition = #{record.returnCondition,jdbcType=VARCHAR},
      suggest_allot_quantity = #{record.suggestAllotQuantity,jdbcType=DECIMAL},
      real_allot_quantity = #{record.realAllotQuantity,jdbcType=DECIMAL},
      transfer_cost_amount = #{record.transferCostAmount,jdbcType=DECIMAL},
      in_store_thirty_days_sales = #{record.inStoreThirtyDaysSales,jdbcType=DECIMAL},
      out_store_thirty_days_sales = #{record.outStoreThirtyDaysSales,jdbcType=DECIMAL},
      in_store_thirty_days_sale_times = #{record.inStoreThirtyDaysSaleTimes,jdbcType=DECIMAL},
      out_store_thirty_days_sale_times = #{record.outStoreThirtyDaysSaleTimes,jdbcType=DECIMAL},
      in_store_stock = #{record.inStoreStock,jdbcType=DECIMAL},
      out_store_stock = #{record.outStoreStock,jdbcType=DECIMAL},
      in_store_stock_min = #{record.inStoreStockMin,jdbcType=DECIMAL},
      out_store_stock_min = #{record.outStoreStockMin,jdbcType=DECIMAL},
      in_store_stock_max = #{record.inStoreStockMax,jdbcType=DECIMAL},
      out_store_stock_max = #{record.outStoreStockMax,jdbcType=DECIMAL},
      in_store_stock_onway = #{record.inStoreStockOnway,jdbcType=DECIMAL},
      out_store_stock_onway = #{record.outStoreStockOnway,jdbcType=DECIMAL},
      in_store_pos_daily_sales = #{record.inStorePosDailySales,jdbcType=DECIMAL},
      out_store_pos_daily_sales = #{record.outStorePosDailySales,jdbcType=DECIMAL},
      in_store_estimate_sales_days = #{record.inStoreEstimateSalesDays,jdbcType=DECIMAL},
      out_store_estimate_sales_days = #{record.outStoreEstimateSalesDays,jdbcType=DECIMAL},
      approve_status = #{record.approveStatus,jdbcType=TINYINT},
      approve_result = #{record.approveResult,jdbcType=VARCHAR},
      approve_by = #{record.approveBy,jdbcType=BIGINT},
      approve_name = #{record.approveName,jdbcType=VARCHAR},
      approve_time = #{record.approveTime,jdbcType=TIMESTAMP},
      goodsline = #{record.goodsline,jdbcType=VARCHAR},
      pushlevel = #{record.pushlevel,jdbcType=VARCHAR},
      goods_marks = #{record.goodsMarks,jdbcType=VARCHAR},
      short_manufacturer = #{record.shortManufacturer,jdbcType=VARCHAR},
      specifications = #{record.specifications,jdbcType=VARCHAR},
      model_code = #{record.modelCode,jdbcType=VARCHAR},
      `status` = #{record.status,jdbcType=TINYINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_name = #{record.createdName,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_name = #{record.updatedName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.iscm.entity.IscmSuggestAllotGoodsDetail">
    update iscm_suggest_allot_goods_detail
    <set>
      <if test="businessDate != null">
        business_date = #{businessDate,jdbcType=TIMESTAMP},
      </if>
      <if test="recordId != null">
        record_id = #{recordId,jdbcType=BIGINT},
      </if>
      <if test="allotType != null">
        allot_type = #{allotType,jdbcType=TINYINT},
      </if>
      <if test="registerNo != null">
        register_no = #{registerNo,jdbcType=VARCHAR},
      </if>
      <if test="registerSource != null">
        register_source = #{registerSource,jdbcType=TINYINT},
      </if>
      <if test="registerBy != null">
        register_by = #{registerBy,jdbcType=BIGINT},
      </if>
      <if test="registerName != null">
        register_name = #{registerName,jdbcType=VARCHAR},
      </if>
      <if test="registerTime != null">
        register_time = #{registerTime,jdbcType=TIMESTAMP},
      </if>
      <if test="platformOrgId != null">
        platform_org_id = #{platformOrgId,jdbcType=BIGINT},
      </if>
      <if test="platformOrgName != null">
        platform_org_name = #{platformOrgName,jdbcType=VARCHAR},
      </if>
      <if test="outCompanyId != null">
        out_company_id = #{outCompanyId,jdbcType=BIGINT},
      </if>
      <if test="outCompanyCode != null">
        out_company_code = #{outCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="outCompanyName != null">
        out_company_name = #{outCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="inCompanyId != null">
        in_company_id = #{inCompanyId,jdbcType=BIGINT},
      </if>
      <if test="inCompanyCode != null">
        in_company_code = #{inCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="inCompanyName != null">
        in_company_name = #{inCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="outStoreId != null">
        out_store_id = #{outStoreId,jdbcType=BIGINT},
      </if>
      <if test="outStoreCode != null">
        out_store_code = #{outStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="outStoreName != null">
        out_store_name = #{outStoreName,jdbcType=VARCHAR},
      </if>
      <if test="outStoreAttr != null">
        out_store_attr = #{outStoreAttr,jdbcType=VARCHAR},
      </if>
      <if test="outStoreSalesLevel != null">
        out_store_sales_level = #{outStoreSalesLevel,jdbcType=VARCHAR},
      </if>
      <if test="inStoreId != null">
        in_store_id = #{inStoreId,jdbcType=BIGINT},
      </if>
      <if test="inStoreCode != null">
        in_store_code = #{inStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="inStoreName != null">
        in_store_name = #{inStoreName,jdbcType=VARCHAR},
      </if>
      <if test="inStoreAttr != null">
        in_store_attr = #{inStoreAttr,jdbcType=VARCHAR},
      </if>
      <if test="inStoreSalesLevel != null">
        in_store_sales_level = #{inStoreSalesLevel,jdbcType=VARCHAR},
      </if>
      <if test="allotGroupCode != null">
        allot_group_code = #{allotGroupCode,jdbcType=VARCHAR},
      </if>
      <if test="allotGroupName != null">
        allot_group_name = #{allotGroupName,jdbcType=VARCHAR},
      </if>
      <if test="allotNo != null">
        allot_no = #{allotNo,jdbcType=VARCHAR},
      </if>
      <if test="posAllotNo != null">
        pos_allot_no = #{posAllotNo,jdbcType=VARCHAR},
      </if>
      <if test="goodsNo != null">
        goods_no = #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="goodsDesc != null">
        goods_desc = #{goodsDesc,jdbcType=VARCHAR},
      </if>
      <if test="batchNo != null">
        batch_no = #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="expiryTime != null">
        expiry_time = #{expiryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="goodsCommonName != null">
        goods_common_name = #{goodsCommonName,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        manufacturer = #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="dosageForm != null">
        dosage_form = #{dosageForm,jdbcType=VARCHAR},
      </if>
      <if test="unit != null">
        unit = #{unit,jdbcType=VARCHAR},
      </if>
      <if test="season != null">
        season = #{season,jdbcType=TINYINT},
      </if>
      <if test="returnCondition != null">
        return_condition = #{returnCondition,jdbcType=VARCHAR},
      </if>
      <if test="suggestAllotQuantity != null">
        suggest_allot_quantity = #{suggestAllotQuantity,jdbcType=DECIMAL},
      </if>
      <if test="realAllotQuantity != null">
        real_allot_quantity = #{realAllotQuantity,jdbcType=DECIMAL},
      </if>
      <if test="transferCostAmount != null">
        transfer_cost_amount = #{transferCostAmount,jdbcType=DECIMAL},
      </if>
      <if test="inStoreThirtyDaysSales != null">
        in_store_thirty_days_sales = #{inStoreThirtyDaysSales,jdbcType=DECIMAL},
      </if>
      <if test="outStoreThirtyDaysSales != null">
        out_store_thirty_days_sales = #{outStoreThirtyDaysSales,jdbcType=DECIMAL},
      </if>
      <if test="inStoreThirtyDaysSaleTimes != null">
        in_store_thirty_days_sale_times = #{inStoreThirtyDaysSaleTimes,jdbcType=DECIMAL},
      </if>
      <if test="outStoreThirtyDaysSaleTimes != null">
        out_store_thirty_days_sale_times = #{outStoreThirtyDaysSaleTimes,jdbcType=DECIMAL},
      </if>
      <if test="inStoreStock != null">
        in_store_stock = #{inStoreStock,jdbcType=DECIMAL},
      </if>
      <if test="outStoreStock != null">
        out_store_stock = #{outStoreStock,jdbcType=DECIMAL},
      </if>
      <if test="inStoreStockMin != null">
        in_store_stock_min = #{inStoreStockMin,jdbcType=DECIMAL},
      </if>
      <if test="outStoreStockMin != null">
        out_store_stock_min = #{outStoreStockMin,jdbcType=DECIMAL},
      </if>
      <if test="inStoreStockMax != null">
        in_store_stock_max = #{inStoreStockMax,jdbcType=DECIMAL},
      </if>
      <if test="outStoreStockMax != null">
        out_store_stock_max = #{outStoreStockMax,jdbcType=DECIMAL},
      </if>
      <if test="inStoreStockOnway != null">
        in_store_stock_onway = #{inStoreStockOnway,jdbcType=DECIMAL},
      </if>
      <if test="outStoreStockOnway != null">
        out_store_stock_onway = #{outStoreStockOnway,jdbcType=DECIMAL},
      </if>
      <if test="inStorePosDailySales != null">
        in_store_pos_daily_sales = #{inStorePosDailySales,jdbcType=DECIMAL},
      </if>
      <if test="outStorePosDailySales != null">
        out_store_pos_daily_sales = #{outStorePosDailySales,jdbcType=DECIMAL},
      </if>
      <if test="inStoreEstimateSalesDays != null">
        in_store_estimate_sales_days = #{inStoreEstimateSalesDays,jdbcType=DECIMAL},
      </if>
      <if test="outStoreEstimateSalesDays != null">
        out_store_estimate_sales_days = #{outStoreEstimateSalesDays,jdbcType=DECIMAL},
      </if>
      <if test="approveStatus != null">
        approve_status = #{approveStatus,jdbcType=TINYINT},
      </if>
      <if test="approveResult != null">
        approve_result = #{approveResult,jdbcType=VARCHAR},
      </if>
      <if test="approveBy != null">
        approve_by = #{approveBy,jdbcType=BIGINT},
      </if>
      <if test="approveName != null">
        approve_name = #{approveName,jdbcType=VARCHAR},
      </if>
      <if test="approveTime != null">
        approve_time = #{approveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="goodsline != null">
        goodsline = #{goodsline,jdbcType=VARCHAR},
      </if>
      <if test="pushlevel != null">
        pushlevel = #{pushlevel,jdbcType=VARCHAR},
      </if>
      <if test="goodsMarks != null">
        goods_marks = #{goodsMarks,jdbcType=VARCHAR},
      </if>
      <if test="shortManufacturer != null">
        short_manufacturer = #{shortManufacturer,jdbcType=VARCHAR},
      </if>
      <if test="specifications != null">
        specifications = #{specifications,jdbcType=VARCHAR},
      </if>
      <if test="modelCode != null">
        model_code = #{modelCode,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        updated_name = #{updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.iscm.entity.IscmSuggestAllotGoodsDetail">
    update iscm_suggest_allot_goods_detail
    set business_date = #{businessDate,jdbcType=TIMESTAMP},
      record_id = #{recordId,jdbcType=BIGINT},
      allot_type = #{allotType,jdbcType=TINYINT},
      register_no = #{registerNo,jdbcType=VARCHAR},
      register_source = #{registerSource,jdbcType=TINYINT},
      register_by = #{registerBy,jdbcType=BIGINT},
      register_name = #{registerName,jdbcType=VARCHAR},
      register_time = #{registerTime,jdbcType=TIMESTAMP},
      platform_org_id = #{platformOrgId,jdbcType=BIGINT},
      platform_org_name = #{platformOrgName,jdbcType=VARCHAR},
      out_company_id = #{outCompanyId,jdbcType=BIGINT},
      out_company_code = #{outCompanyCode,jdbcType=VARCHAR},
      out_company_name = #{outCompanyName,jdbcType=VARCHAR},
      in_company_id = #{inCompanyId,jdbcType=BIGINT},
      in_company_code = #{inCompanyCode,jdbcType=VARCHAR},
      in_company_name = #{inCompanyName,jdbcType=VARCHAR},
      out_store_id = #{outStoreId,jdbcType=BIGINT},
      out_store_code = #{outStoreCode,jdbcType=VARCHAR},
      out_store_name = #{outStoreName,jdbcType=VARCHAR},
      out_store_attr = #{outStoreAttr,jdbcType=VARCHAR},
      out_store_sales_level = #{outStoreSalesLevel,jdbcType=VARCHAR},
      in_store_id = #{inStoreId,jdbcType=BIGINT},
      in_store_code = #{inStoreCode,jdbcType=VARCHAR},
      in_store_name = #{inStoreName,jdbcType=VARCHAR},
      in_store_attr = #{inStoreAttr,jdbcType=VARCHAR},
      in_store_sales_level = #{inStoreSalesLevel,jdbcType=VARCHAR},
      allot_group_code = #{allotGroupCode,jdbcType=VARCHAR},
      allot_group_name = #{allotGroupName,jdbcType=VARCHAR},
      allot_no = #{allotNo,jdbcType=VARCHAR},
      pos_allot_no = #{posAllotNo,jdbcType=VARCHAR},
      goods_no = #{goodsNo,jdbcType=VARCHAR},
      goods_desc = #{goodsDesc,jdbcType=VARCHAR},
      batch_no = #{batchNo,jdbcType=VARCHAR},
      expiry_time = #{expiryTime,jdbcType=TIMESTAMP},
      goods_common_name = #{goodsCommonName,jdbcType=VARCHAR},
      manufacturer = #{manufacturer,jdbcType=VARCHAR},
      dosage_form = #{dosageForm,jdbcType=VARCHAR},
      unit = #{unit,jdbcType=VARCHAR},
      season = #{season,jdbcType=TINYINT},
      return_condition = #{returnCondition,jdbcType=VARCHAR},
      suggest_allot_quantity = #{suggestAllotQuantity,jdbcType=DECIMAL},
      real_allot_quantity = #{realAllotQuantity,jdbcType=DECIMAL},
      transfer_cost_amount = #{transferCostAmount,jdbcType=DECIMAL},
      in_store_thirty_days_sales = #{inStoreThirtyDaysSales,jdbcType=DECIMAL},
      out_store_thirty_days_sales = #{outStoreThirtyDaysSales,jdbcType=DECIMAL},
      in_store_thirty_days_sale_times = #{inStoreThirtyDaysSaleTimes,jdbcType=DECIMAL},
      out_store_thirty_days_sale_times = #{outStoreThirtyDaysSaleTimes,jdbcType=DECIMAL},
      in_store_stock = #{inStoreStock,jdbcType=DECIMAL},
      out_store_stock = #{outStoreStock,jdbcType=DECIMAL},
      in_store_stock_min = #{inStoreStockMin,jdbcType=DECIMAL},
      out_store_stock_min = #{outStoreStockMin,jdbcType=DECIMAL},
      in_store_stock_max = #{inStoreStockMax,jdbcType=DECIMAL},
      out_store_stock_max = #{outStoreStockMax,jdbcType=DECIMAL},
      in_store_stock_onway = #{inStoreStockOnway,jdbcType=DECIMAL},
      out_store_stock_onway = #{outStoreStockOnway,jdbcType=DECIMAL},
      in_store_pos_daily_sales = #{inStorePosDailySales,jdbcType=DECIMAL},
      out_store_pos_daily_sales = #{outStorePosDailySales,jdbcType=DECIMAL},
      in_store_estimate_sales_days = #{inStoreEstimateSalesDays,jdbcType=DECIMAL},
      out_store_estimate_sales_days = #{outStoreEstimateSalesDays,jdbcType=DECIMAL},
      approve_status = #{approveStatus,jdbcType=TINYINT},
      approve_result = #{approveResult,jdbcType=VARCHAR},
      approve_by = #{approveBy,jdbcType=BIGINT},
      approve_name = #{approveName,jdbcType=VARCHAR},
      approve_time = #{approveTime,jdbcType=TIMESTAMP},
      goodsline = #{goodsline,jdbcType=VARCHAR},
      pushlevel = #{pushlevel,jdbcType=VARCHAR},
      goods_marks = #{goodsMarks,jdbcType=VARCHAR},
      short_manufacturer = #{shortManufacturer,jdbcType=VARCHAR},
      specifications = #{specifications,jdbcType=VARCHAR},
      model_code = #{modelCode,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_name = #{createdName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_name = #{updatedName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>