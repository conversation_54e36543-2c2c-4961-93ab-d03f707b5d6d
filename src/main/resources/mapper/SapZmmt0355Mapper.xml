<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.SapZmmt0355Mapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.SapZmmt0355">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="MANDT" jdbcType="VARCHAR" property="mandt" />
    <result column="ZGUID" jdbcType="VARCHAR" property="zguid" />
    <result column="ZCGJHD" jdbcType="VARCHAR" property="zcgjhd" />
    <result column="ZCGJHDHH" jdbcType="VARCHAR" property="zcgjhdhh" />
    <result column="BSART" jdbcType="VARCHAR" property="bsart" />
    <result column="LOEKZ" jdbcType="VARCHAR" property="loekz" />
    <result column="ZPOFLAG" jdbcType="VARCHAR" property="zpoflag" />
    <result column="ZOAFLAG" jdbcType="VARCHAR" property="zoaflag" />
    <result column="EBELN" jdbcType="VARCHAR" property="ebeln" />
    <result column="EBELP" jdbcType="VARCHAR" property="ebelp" />
    <result column="EKORG" jdbcType="VARCHAR" property="ekorg" />
    <result column="BUKRS" jdbcType="VARCHAR" property="bukrs" />
    <result column="EKGRP" jdbcType="VARCHAR" property="ekgrp" />
    <result column="LIFNR" jdbcType="VARCHAR" property="lifnr" />
    <result column="ZYWCJ" jdbcType="VARCHAR" property="zywcj" />
    <result column="ZZZWLMS" jdbcType="VARCHAR" property="zzzwlms" />
    <result column="ERNAM" jdbcType="VARCHAR" property="ernam" />
    <result column="ERDAT" jdbcType="VARCHAR" property="erdat" />
    <result column="ERZET" jdbcType="VARCHAR" property="erzet" />
    <result column="WERKS" jdbcType="VARCHAR" property="werks" />
    <result column="MATNR" jdbcType="VARCHAR" property="matnr" />
    <result column="MENGE" jdbcType="DECIMAL" property="menge" />
    <result column="ZMENGE" jdbcType="DECIMAL" property="zmenge" />
    <result column="EINDT" jdbcType="VARCHAR" property="eindt" />
    <result column="ZEINDT" jdbcType="VARCHAR" property="zeindt" />
    <result column="MEINS" jdbcType="VARCHAR" property="meins" />
    <result column="ZHSDJ" jdbcType="DECIMAL" property="zhsdj" />
    <result column="ZQRHSDJ" jdbcType="DECIMAL" property="zqrhsdj" />
    <result column="PEINH" jdbcType="INTEGER" property="peinh" />
    <result column="ZPEINH" jdbcType="INTEGER" property="zpeinh" />
    <result column="MWSKZ" jdbcType="VARCHAR" property="mwskz" />
    <result column="ZMWSKZ" jdbcType="VARCHAR" property="zmwskz" />
    <result column="ZCFLAG" jdbcType="VARCHAR" property="zcflag" />
    <result column="ZTERM" jdbcType="VARCHAR" property="zterm" />
    <result column="ZZZUSERID" jdbcType="VARCHAR" property="zzzuserid" />
    <result column="ZSHCK" jdbcType="VARCHAR" property="zshck" />
    <result column="ZSPJB" jdbcType="INTEGER" property="zspjb" />
    <result column="KONNR" jdbcType="VARCHAR" property="konnr" />
    <result column="KTPNR" jdbcType="VARCHAR" property="ktpnr" />
    <result column="ZTZHTBH" jdbcType="VARCHAR" property="ztzhtbh" />
    <result column="ZTZHTHH" jdbcType="VARCHAR" property="ztzhthh" />
    <result column="ZPLCODE" jdbcType="VARCHAR" property="zplcode" />
    <result column="ZPLSUG" jdbcType="VARCHAR" property="zplsug" />
    <result column="ZCJBM" jdbcType="VARCHAR" property="zcjbm" />
    <result column="MENGE_ZZ" jdbcType="DECIMAL" property="mengeZz" />
    <result column="ZSTOCK_USE" jdbcType="DECIMAL" property="zstockUse" />
    <result column="ZSJFD" jdbcType="DECIMAL" property="zsjfd" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="USERNAME" jdbcType="VARCHAR" property="username" />
    <result column="UDATE" jdbcType="VARCHAR" property="udate" />
    <result column="UTIME" jdbcType="VARCHAR" property="utime" />
    <result column="ZMDZ" jdbcType="VARCHAR" property="zmdz" />
    <result column="ZSRMFLAG" jdbcType="VARCHAR" property="zsrmflag" />
    <result column="BRTWR_LAST" jdbcType="DECIMAL" property="brtwrLast" />
    <result column="PEINH_LAST" jdbcType="INTEGER" property="peinhLast" />
    <result column="LABST_DC_JS" jdbcType="DECIMAL" property="labstDcJs" />
    <result column="MENGE_WQ_JS" jdbcType="DECIMAL" property="mengeWqJs" />
    <result column="ZZXL_30_JS" jdbcType="DECIMAL" property="zzxl30Js" />
    <result column="ZMDQTY_JS" jdbcType="DECIMAL" property="zmdqtyJs" />
    <result column="ZZZMDSQ" jdbcType="VARCHAR" property="zzzmdsq" />
    <result column="ZZZMDHH" jdbcType="VARCHAR" property="zzzmdhh" />
    <result column="ZJHDAT" jdbcType="VARCHAR" property="zjhdat" />
    <result column="ZJHTIME" jdbcType="VARCHAR" property="zjhtime" />
    <result column="ZSRMDAT" jdbcType="VARCHAR" property="zsrmdat" />
    <result column="ZSRMTIME" jdbcType="VARCHAR" property="zsrmtime" />
    <result column="ZJHZDAT" jdbcType="VARCHAR" property="zjhzdat" />
    <result column="ZJHZTIME" jdbcType="VARCHAR" property="zjhztime" />
    <result column="FRGGR" jdbcType="VARCHAR" property="frggr" />
    <result column="FRGSX" jdbcType="VARCHAR" property="frgsx" />
    <result column="FRGKX" jdbcType="VARCHAR" property="frgkx" />
    <result column="FRGCO" jdbcType="VARCHAR" property="frgco" />
    <result column="SUBMI" jdbcType="VARCHAR" property="submi" />
    <result column="ZRESULT" jdbcType="VARCHAR" property="zresult" />
    <result column="ZLEVEL" jdbcType="VARCHAR" property="zlevel" />
    <result column="ZSPBS" jdbcType="VARCHAR" property="zspbs" />
    <result column="ZMESS" jdbcType="VARCHAR" property="zmess" />
    <result column="LGORT" jdbcType="VARCHAR" property="lgort" />
    <result column="ZDCKYKC" jdbcType="DECIMAL" property="zdckykc" />
    <result column="ZCYJSWQ" jdbcType="DECIMAL" property="zcyjswq" />
    <result column="ZZDGDSC" jdbcType="DECIMAL" property="zzdgdsc" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.cowell.iscm.entity.SapZmmt0355">
    <result column="extend" jdbcType="LONGVARCHAR" property="extend" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, MANDT, ZGUID, ZCGJHD, ZCGJHDHH, BSART, LOEKZ, ZPOFLAG, ZOAFLAG, EBELN, EBELP, 
    EKORG, BUKRS, EKGRP, LIFNR, ZYWCJ, ZZZWLMS, ERNAM, ERDAT, ERZET, WERKS, MATNR, MENGE, 
    ZMENGE, EINDT, ZEINDT, MEINS, ZHSDJ, ZQRHSDJ, PEINH, ZPEINH, MWSKZ, ZMWSKZ, ZCFLAG, 
    ZTERM, ZZZUSERID, ZSHCK, ZSPJB, KONNR, KTPNR, ZTZHTBH, ZTZHTHH, ZPLCODE, ZPLSUG, 
    ZCJBM, MENGE_ZZ, ZSTOCK_USE, ZSJFD, REMARK, USERNAME, UDATE, UTIME, ZMDZ, ZSRMFLAG, 
    BRTWR_LAST, PEINH_LAST, LABST_DC_JS, MENGE_WQ_JS, ZZXL_30_JS, ZMDQTY_JS, ZZZMDSQ, 
    ZZZMDHH, ZJHDAT, ZJHTIME, ZSRMDAT, ZSRMTIME, ZJHZDAT, ZJHZTIME, FRGGR, FRGSX, FRGKX, 
    FRGCO, SUBMI, ZRESULT, ZLEVEL, ZSPBS, ZMESS, LGORT, ZDCKYKC, ZCYJSWQ, ZZDGDSC
  </sql>
  <sql id="Blob_Column_List">
    extend
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.cowell.iscm.entity.SapZmmt0355Example" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from SAP_ZMMT0355
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByExample" parameterType="com.cowell.iscm.entity.SapZmmt0355Example" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from SAP_ZMMT0355
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from SAP_ZMMT0355
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from SAP_ZMMT0355
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.iscm.entity.SapZmmt0355Example">
    delete from SAP_ZMMT0355
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entity.SapZmmt0355" useGeneratedKeys="true">
    insert into SAP_ZMMT0355 (MANDT, ZGUID, ZCGJHD, 
      ZCGJHDHH, BSART, LOEKZ, 
      ZPOFLAG, ZOAFLAG, EBELN, 
      EBELP, EKORG, BUKRS, 
      EKGRP, LIFNR, ZYWCJ, 
      ZZZWLMS, ERNAM, ERDAT, 
      ERZET, WERKS, MATNR, 
      MENGE, ZMENGE, EINDT, 
      ZEINDT, MEINS, ZHSDJ, 
      ZQRHSDJ, PEINH, ZPEINH, 
      MWSKZ, ZMWSKZ, ZCFLAG, 
      ZTERM, ZZZUSERID, ZSHCK, 
      ZSPJB, KONNR, KTPNR, 
      ZTZHTBH, ZTZHTHH, ZPLCODE, 
      ZPLSUG, ZCJBM, MENGE_ZZ, 
      ZSTOCK_USE, ZSJFD, REMARK, 
      USERNAME, UDATE, UTIME, 
      ZMDZ, ZSRMFLAG, BRTWR_LAST, 
      PEINH_LAST, LABST_DC_JS, MENGE_WQ_JS, 
      ZZXL_30_JS, ZMDQTY_JS, ZZZMDSQ, 
      ZZZMDHH, ZJHDAT, ZJHTIME, 
      ZSRMDAT, ZSRMTIME, ZJHZDAT, 
      ZJHZTIME, FRGGR, FRGSX, 
      FRGKX, FRGCO, SUBMI, 
      ZRESULT, ZLEVEL, ZSPBS, 
      ZMESS, LGORT, ZDCKYKC, 
      ZCYJSWQ, ZZDGDSC, extend
      )
    values (#{mandt,jdbcType=VARCHAR}, #{zguid,jdbcType=VARCHAR}, #{zcgjhd,jdbcType=VARCHAR}, 
      #{zcgjhdhh,jdbcType=VARCHAR}, #{bsart,jdbcType=VARCHAR}, #{loekz,jdbcType=VARCHAR}, 
      #{zpoflag,jdbcType=VARCHAR}, #{zoaflag,jdbcType=VARCHAR}, #{ebeln,jdbcType=VARCHAR}, 
      #{ebelp,jdbcType=VARCHAR}, #{ekorg,jdbcType=VARCHAR}, #{bukrs,jdbcType=VARCHAR}, 
      #{ekgrp,jdbcType=VARCHAR}, #{lifnr,jdbcType=VARCHAR}, #{zywcj,jdbcType=VARCHAR}, 
      #{zzzwlms,jdbcType=VARCHAR}, #{ernam,jdbcType=VARCHAR}, #{erdat,jdbcType=VARCHAR}, 
      #{erzet,jdbcType=VARCHAR}, #{werks,jdbcType=VARCHAR}, #{matnr,jdbcType=VARCHAR}, 
      #{menge,jdbcType=DECIMAL}, #{zmenge,jdbcType=DECIMAL}, #{eindt,jdbcType=VARCHAR}, 
      #{zeindt,jdbcType=VARCHAR}, #{meins,jdbcType=VARCHAR}, #{zhsdj,jdbcType=DECIMAL}, 
      #{zqrhsdj,jdbcType=DECIMAL}, #{peinh,jdbcType=INTEGER}, #{zpeinh,jdbcType=INTEGER}, 
      #{mwskz,jdbcType=VARCHAR}, #{zmwskz,jdbcType=VARCHAR}, #{zcflag,jdbcType=VARCHAR}, 
      #{zterm,jdbcType=VARCHAR}, #{zzzuserid,jdbcType=VARCHAR}, #{zshck,jdbcType=VARCHAR}, 
      #{zspjb,jdbcType=INTEGER}, #{konnr,jdbcType=VARCHAR}, #{ktpnr,jdbcType=VARCHAR}, 
      #{ztzhtbh,jdbcType=VARCHAR}, #{ztzhthh,jdbcType=VARCHAR}, #{zplcode,jdbcType=VARCHAR}, 
      #{zplsug,jdbcType=VARCHAR}, #{zcjbm,jdbcType=VARCHAR}, #{mengeZz,jdbcType=DECIMAL}, 
      #{zstockUse,jdbcType=DECIMAL}, #{zsjfd,jdbcType=DECIMAL}, #{remark,jdbcType=VARCHAR}, 
      #{username,jdbcType=VARCHAR}, #{udate,jdbcType=VARCHAR}, #{utime,jdbcType=VARCHAR}, 
      #{zmdz,jdbcType=VARCHAR}, #{zsrmflag,jdbcType=VARCHAR}, #{brtwrLast,jdbcType=DECIMAL}, 
      #{peinhLast,jdbcType=INTEGER}, #{labstDcJs,jdbcType=DECIMAL}, #{mengeWqJs,jdbcType=DECIMAL}, 
      #{zzxl30Js,jdbcType=DECIMAL}, #{zmdqtyJs,jdbcType=DECIMAL}, #{zzzmdsq,jdbcType=VARCHAR}, 
      #{zzzmdhh,jdbcType=VARCHAR}, #{zjhdat,jdbcType=VARCHAR}, #{zjhtime,jdbcType=VARCHAR}, 
      #{zsrmdat,jdbcType=VARCHAR}, #{zsrmtime,jdbcType=VARCHAR}, #{zjhzdat,jdbcType=VARCHAR}, 
      #{zjhztime,jdbcType=VARCHAR}, #{frggr,jdbcType=VARCHAR}, #{frgsx,jdbcType=VARCHAR}, 
      #{frgkx,jdbcType=VARCHAR}, #{frgco,jdbcType=VARCHAR}, #{submi,jdbcType=VARCHAR}, 
      #{zresult,jdbcType=VARCHAR}, #{zlevel,jdbcType=VARCHAR}, #{zspbs,jdbcType=VARCHAR}, 
      #{zmess,jdbcType=VARCHAR}, #{lgort,jdbcType=VARCHAR}, #{zdckykc,jdbcType=DECIMAL}, 
      #{zcyjswq,jdbcType=DECIMAL}, #{zzdgdsc,jdbcType=DECIMAL}, #{extend,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entity.SapZmmt0355" useGeneratedKeys="true">
    insert into SAP_ZMMT0355
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="mandt != null">
        MANDT,
      </if>
      <if test="zguid != null">
        ZGUID,
      </if>
      <if test="zcgjhd != null">
        ZCGJHD,
      </if>
      <if test="zcgjhdhh != null">
        ZCGJHDHH,
      </if>
      <if test="bsart != null">
        BSART,
      </if>
      <if test="loekz != null">
        LOEKZ,
      </if>
      <if test="zpoflag != null">
        ZPOFLAG,
      </if>
      <if test="zoaflag != null">
        ZOAFLAG,
      </if>
      <if test="ebeln != null">
        EBELN,
      </if>
      <if test="ebelp != null">
        EBELP,
      </if>
      <if test="ekorg != null">
        EKORG,
      </if>
      <if test="bukrs != null">
        BUKRS,
      </if>
      <if test="ekgrp != null">
        EKGRP,
      </if>
      <if test="lifnr != null">
        LIFNR,
      </if>
      <if test="zywcj != null">
        ZYWCJ,
      </if>
      <if test="zzzwlms != null">
        ZZZWLMS,
      </if>
      <if test="ernam != null">
        ERNAM,
      </if>
      <if test="erdat != null">
        ERDAT,
      </if>
      <if test="erzet != null">
        ERZET,
      </if>
      <if test="werks != null">
        WERKS,
      </if>
      <if test="matnr != null">
        MATNR,
      </if>
      <if test="menge != null">
        MENGE,
      </if>
      <if test="zmenge != null">
        ZMENGE,
      </if>
      <if test="eindt != null">
        EINDT,
      </if>
      <if test="zeindt != null">
        ZEINDT,
      </if>
      <if test="meins != null">
        MEINS,
      </if>
      <if test="zhsdj != null">
        ZHSDJ,
      </if>
      <if test="zqrhsdj != null">
        ZQRHSDJ,
      </if>
      <if test="peinh != null">
        PEINH,
      </if>
      <if test="zpeinh != null">
        ZPEINH,
      </if>
      <if test="mwskz != null">
        MWSKZ,
      </if>
      <if test="zmwskz != null">
        ZMWSKZ,
      </if>
      <if test="zcflag != null">
        ZCFLAG,
      </if>
      <if test="zterm != null">
        ZTERM,
      </if>
      <if test="zzzuserid != null">
        ZZZUSERID,
      </if>
      <if test="zshck != null">
        ZSHCK,
      </if>
      <if test="zspjb != null">
        ZSPJB,
      </if>
      <if test="konnr != null">
        KONNR,
      </if>
      <if test="ktpnr != null">
        KTPNR,
      </if>
      <if test="ztzhtbh != null">
        ZTZHTBH,
      </if>
      <if test="ztzhthh != null">
        ZTZHTHH,
      </if>
      <if test="zplcode != null">
        ZPLCODE,
      </if>
      <if test="zplsug != null">
        ZPLSUG,
      </if>
      <if test="zcjbm != null">
        ZCJBM,
      </if>
      <if test="mengeZz != null">
        MENGE_ZZ,
      </if>
      <if test="zstockUse != null">
        ZSTOCK_USE,
      </if>
      <if test="zsjfd != null">
        ZSJFD,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="username != null">
        USERNAME,
      </if>
      <if test="udate != null">
        UDATE,
      </if>
      <if test="utime != null">
        UTIME,
      </if>
      <if test="zmdz != null">
        ZMDZ,
      </if>
      <if test="zsrmflag != null">
        ZSRMFLAG,
      </if>
      <if test="brtwrLast != null">
        BRTWR_LAST,
      </if>
      <if test="peinhLast != null">
        PEINH_LAST,
      </if>
      <if test="labstDcJs != null">
        LABST_DC_JS,
      </if>
      <if test="mengeWqJs != null">
        MENGE_WQ_JS,
      </if>
      <if test="zzxl30Js != null">
        ZZXL_30_JS,
      </if>
      <if test="zmdqtyJs != null">
        ZMDQTY_JS,
      </if>
      <if test="zzzmdsq != null">
        ZZZMDSQ,
      </if>
      <if test="zzzmdhh != null">
        ZZZMDHH,
      </if>
      <if test="zjhdat != null">
        ZJHDAT,
      </if>
      <if test="zjhtime != null">
        ZJHTIME,
      </if>
      <if test="zsrmdat != null">
        ZSRMDAT,
      </if>
      <if test="zsrmtime != null">
        ZSRMTIME,
      </if>
      <if test="zjhzdat != null">
        ZJHZDAT,
      </if>
      <if test="zjhztime != null">
        ZJHZTIME,
      </if>
      <if test="frggr != null">
        FRGGR,
      </if>
      <if test="frgsx != null">
        FRGSX,
      </if>
      <if test="frgkx != null">
        FRGKX,
      </if>
      <if test="frgco != null">
        FRGCO,
      </if>
      <if test="submi != null">
        SUBMI,
      </if>
      <if test="zresult != null">
        ZRESULT,
      </if>
      <if test="zlevel != null">
        ZLEVEL,
      </if>
      <if test="zspbs != null">
        ZSPBS,
      </if>
      <if test="zmess != null">
        ZMESS,
      </if>
      <if test="lgort != null">
        LGORT,
      </if>
      <if test="zdckykc != null">
        ZDCKYKC,
      </if>
      <if test="zcyjswq != null">
        ZCYJSWQ,
      </if>
      <if test="zzdgdsc != null">
        ZZDGDSC,
      </if>
      <if test="extend != null">
        extend,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="mandt != null">
        #{mandt,jdbcType=VARCHAR},
      </if>
      <if test="zguid != null">
        #{zguid,jdbcType=VARCHAR},
      </if>
      <if test="zcgjhd != null">
        #{zcgjhd,jdbcType=VARCHAR},
      </if>
      <if test="zcgjhdhh != null">
        #{zcgjhdhh,jdbcType=VARCHAR},
      </if>
      <if test="bsart != null">
        #{bsart,jdbcType=VARCHAR},
      </if>
      <if test="loekz != null">
        #{loekz,jdbcType=VARCHAR},
      </if>
      <if test="zpoflag != null">
        #{zpoflag,jdbcType=VARCHAR},
      </if>
      <if test="zoaflag != null">
        #{zoaflag,jdbcType=VARCHAR},
      </if>
      <if test="ebeln != null">
        #{ebeln,jdbcType=VARCHAR},
      </if>
      <if test="ebelp != null">
        #{ebelp,jdbcType=VARCHAR},
      </if>
      <if test="ekorg != null">
        #{ekorg,jdbcType=VARCHAR},
      </if>
      <if test="bukrs != null">
        #{bukrs,jdbcType=VARCHAR},
      </if>
      <if test="ekgrp != null">
        #{ekgrp,jdbcType=VARCHAR},
      </if>
      <if test="lifnr != null">
        #{lifnr,jdbcType=VARCHAR},
      </if>
      <if test="zywcj != null">
        #{zywcj,jdbcType=VARCHAR},
      </if>
      <if test="zzzwlms != null">
        #{zzzwlms,jdbcType=VARCHAR},
      </if>
      <if test="ernam != null">
        #{ernam,jdbcType=VARCHAR},
      </if>
      <if test="erdat != null">
        #{erdat,jdbcType=VARCHAR},
      </if>
      <if test="erzet != null">
        #{erzet,jdbcType=VARCHAR},
      </if>
      <if test="werks != null">
        #{werks,jdbcType=VARCHAR},
      </if>
      <if test="matnr != null">
        #{matnr,jdbcType=VARCHAR},
      </if>
      <if test="menge != null">
        #{menge,jdbcType=DECIMAL},
      </if>
      <if test="zmenge != null">
        #{zmenge,jdbcType=DECIMAL},
      </if>
      <if test="eindt != null">
        #{eindt,jdbcType=VARCHAR},
      </if>
      <if test="zeindt != null">
        #{zeindt,jdbcType=VARCHAR},
      </if>
      <if test="meins != null">
        #{meins,jdbcType=VARCHAR},
      </if>
      <if test="zhsdj != null">
        #{zhsdj,jdbcType=DECIMAL},
      </if>
      <if test="zqrhsdj != null">
        #{zqrhsdj,jdbcType=DECIMAL},
      </if>
      <if test="peinh != null">
        #{peinh,jdbcType=INTEGER},
      </if>
      <if test="zpeinh != null">
        #{zpeinh,jdbcType=INTEGER},
      </if>
      <if test="mwskz != null">
        #{mwskz,jdbcType=VARCHAR},
      </if>
      <if test="zmwskz != null">
        #{zmwskz,jdbcType=VARCHAR},
      </if>
      <if test="zcflag != null">
        #{zcflag,jdbcType=VARCHAR},
      </if>
      <if test="zterm != null">
        #{zterm,jdbcType=VARCHAR},
      </if>
      <if test="zzzuserid != null">
        #{zzzuserid,jdbcType=VARCHAR},
      </if>
      <if test="zshck != null">
        #{zshck,jdbcType=VARCHAR},
      </if>
      <if test="zspjb != null">
        #{zspjb,jdbcType=INTEGER},
      </if>
      <if test="konnr != null">
        #{konnr,jdbcType=VARCHAR},
      </if>
      <if test="ktpnr != null">
        #{ktpnr,jdbcType=VARCHAR},
      </if>
      <if test="ztzhtbh != null">
        #{ztzhtbh,jdbcType=VARCHAR},
      </if>
      <if test="ztzhthh != null">
        #{ztzhthh,jdbcType=VARCHAR},
      </if>
      <if test="zplcode != null">
        #{zplcode,jdbcType=VARCHAR},
      </if>
      <if test="zplsug != null">
        #{zplsug,jdbcType=VARCHAR},
      </if>
      <if test="zcjbm != null">
        #{zcjbm,jdbcType=VARCHAR},
      </if>
      <if test="mengeZz != null">
        #{mengeZz,jdbcType=DECIMAL},
      </if>
      <if test="zstockUse != null">
        #{zstockUse,jdbcType=DECIMAL},
      </if>
      <if test="zsjfd != null">
        #{zsjfd,jdbcType=DECIMAL},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="username != null">
        #{username,jdbcType=VARCHAR},
      </if>
      <if test="udate != null">
        #{udate,jdbcType=VARCHAR},
      </if>
      <if test="utime != null">
        #{utime,jdbcType=VARCHAR},
      </if>
      <if test="zmdz != null">
        #{zmdz,jdbcType=VARCHAR},
      </if>
      <if test="zsrmflag != null">
        #{zsrmflag,jdbcType=VARCHAR},
      </if>
      <if test="brtwrLast != null">
        #{brtwrLast,jdbcType=DECIMAL},
      </if>
      <if test="peinhLast != null">
        #{peinhLast,jdbcType=INTEGER},
      </if>
      <if test="labstDcJs != null">
        #{labstDcJs,jdbcType=DECIMAL},
      </if>
      <if test="mengeWqJs != null">
        #{mengeWqJs,jdbcType=DECIMAL},
      </if>
      <if test="zzxl30Js != null">
        #{zzxl30Js,jdbcType=DECIMAL},
      </if>
      <if test="zmdqtyJs != null">
        #{zmdqtyJs,jdbcType=DECIMAL},
      </if>
      <if test="zzzmdsq != null">
        #{zzzmdsq,jdbcType=VARCHAR},
      </if>
      <if test="zzzmdhh != null">
        #{zzzmdhh,jdbcType=VARCHAR},
      </if>
      <if test="zjhdat != null">
        #{zjhdat,jdbcType=VARCHAR},
      </if>
      <if test="zjhtime != null">
        #{zjhtime,jdbcType=VARCHAR},
      </if>
      <if test="zsrmdat != null">
        #{zsrmdat,jdbcType=VARCHAR},
      </if>
      <if test="zsrmtime != null">
        #{zsrmtime,jdbcType=VARCHAR},
      </if>
      <if test="zjhzdat != null">
        #{zjhzdat,jdbcType=VARCHAR},
      </if>
      <if test="zjhztime != null">
        #{zjhztime,jdbcType=VARCHAR},
      </if>
      <if test="frggr != null">
        #{frggr,jdbcType=VARCHAR},
      </if>
      <if test="frgsx != null">
        #{frgsx,jdbcType=VARCHAR},
      </if>
      <if test="frgkx != null">
        #{frgkx,jdbcType=VARCHAR},
      </if>
      <if test="frgco != null">
        #{frgco,jdbcType=VARCHAR},
      </if>
      <if test="submi != null">
        #{submi,jdbcType=VARCHAR},
      </if>
      <if test="zresult != null">
        #{zresult,jdbcType=VARCHAR},
      </if>
      <if test="zlevel != null">
        #{zlevel,jdbcType=VARCHAR},
      </if>
      <if test="zspbs != null">
        #{zspbs,jdbcType=VARCHAR},
      </if>
      <if test="zmess != null">
        #{zmess,jdbcType=VARCHAR},
      </if>
      <if test="lgort != null">
        #{lgort,jdbcType=VARCHAR},
      </if>
      <if test="zdckykc != null">
        #{zdckykc,jdbcType=DECIMAL},
      </if>
      <if test="zcyjswq != null">
        #{zcyjswq,jdbcType=DECIMAL},
      </if>
      <if test="zzdgdsc != null">
        #{zzdgdsc,jdbcType=DECIMAL},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.iscm.entity.SapZmmt0355Example" resultType="java.lang.Long">
    select count(*) from SAP_ZMMT0355
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update SAP_ZMMT0355
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.mandt != null">
        MANDT = #{record.mandt,jdbcType=VARCHAR},
      </if>
      <if test="record.zguid != null">
        ZGUID = #{record.zguid,jdbcType=VARCHAR},
      </if>
      <if test="record.zcgjhd != null">
        ZCGJHD = #{record.zcgjhd,jdbcType=VARCHAR},
      </if>
      <if test="record.zcgjhdhh != null">
        ZCGJHDHH = #{record.zcgjhdhh,jdbcType=VARCHAR},
      </if>
      <if test="record.bsart != null">
        BSART = #{record.bsart,jdbcType=VARCHAR},
      </if>
      <if test="record.loekz != null">
        LOEKZ = #{record.loekz,jdbcType=VARCHAR},
      </if>
      <if test="record.zpoflag != null">
        ZPOFLAG = #{record.zpoflag,jdbcType=VARCHAR},
      </if>
      <if test="record.zoaflag != null">
        ZOAFLAG = #{record.zoaflag,jdbcType=VARCHAR},
      </if>
      <if test="record.ebeln != null">
        EBELN = #{record.ebeln,jdbcType=VARCHAR},
      </if>
      <if test="record.ebelp != null">
        EBELP = #{record.ebelp,jdbcType=VARCHAR},
      </if>
      <if test="record.ekorg != null">
        EKORG = #{record.ekorg,jdbcType=VARCHAR},
      </if>
      <if test="record.bukrs != null">
        BUKRS = #{record.bukrs,jdbcType=VARCHAR},
      </if>
      <if test="record.ekgrp != null">
        EKGRP = #{record.ekgrp,jdbcType=VARCHAR},
      </if>
      <if test="record.lifnr != null">
        LIFNR = #{record.lifnr,jdbcType=VARCHAR},
      </if>
      <if test="record.zywcj != null">
        ZYWCJ = #{record.zywcj,jdbcType=VARCHAR},
      </if>
      <if test="record.zzzwlms != null">
        ZZZWLMS = #{record.zzzwlms,jdbcType=VARCHAR},
      </if>
      <if test="record.ernam != null">
        ERNAM = #{record.ernam,jdbcType=VARCHAR},
      </if>
      <if test="record.erdat != null">
        ERDAT = #{record.erdat,jdbcType=VARCHAR},
      </if>
      <if test="record.erzet != null">
        ERZET = #{record.erzet,jdbcType=VARCHAR},
      </if>
      <if test="record.werks != null">
        WERKS = #{record.werks,jdbcType=VARCHAR},
      </if>
      <if test="record.matnr != null">
        MATNR = #{record.matnr,jdbcType=VARCHAR},
      </if>
      <if test="record.menge != null">
        MENGE = #{record.menge,jdbcType=DECIMAL},
      </if>
      <if test="record.zmenge != null">
        ZMENGE = #{record.zmenge,jdbcType=DECIMAL},
      </if>
      <if test="record.eindt != null">
        EINDT = #{record.eindt,jdbcType=VARCHAR},
      </if>
      <if test="record.zeindt != null">
        ZEINDT = #{record.zeindt,jdbcType=VARCHAR},
      </if>
      <if test="record.meins != null">
        MEINS = #{record.meins,jdbcType=VARCHAR},
      </if>
      <if test="record.zhsdj != null">
        ZHSDJ = #{record.zhsdj,jdbcType=DECIMAL},
      </if>
      <if test="record.zqrhsdj != null">
        ZQRHSDJ = #{record.zqrhsdj,jdbcType=DECIMAL},
      </if>
      <if test="record.peinh != null">
        PEINH = #{record.peinh,jdbcType=INTEGER},
      </if>
      <if test="record.zpeinh != null">
        ZPEINH = #{record.zpeinh,jdbcType=INTEGER},
      </if>
      <if test="record.mwskz != null">
        MWSKZ = #{record.mwskz,jdbcType=VARCHAR},
      </if>
      <if test="record.zmwskz != null">
        ZMWSKZ = #{record.zmwskz,jdbcType=VARCHAR},
      </if>
      <if test="record.zcflag != null">
        ZCFLAG = #{record.zcflag,jdbcType=VARCHAR},
      </if>
      <if test="record.zterm != null">
        ZTERM = #{record.zterm,jdbcType=VARCHAR},
      </if>
      <if test="record.zzzuserid != null">
        ZZZUSERID = #{record.zzzuserid,jdbcType=VARCHAR},
      </if>
      <if test="record.zshck != null">
        ZSHCK = #{record.zshck,jdbcType=VARCHAR},
      </if>
      <if test="record.zspjb != null">
        ZSPJB = #{record.zspjb,jdbcType=INTEGER},
      </if>
      <if test="record.konnr != null">
        KONNR = #{record.konnr,jdbcType=VARCHAR},
      </if>
      <if test="record.ktpnr != null">
        KTPNR = #{record.ktpnr,jdbcType=VARCHAR},
      </if>
      <if test="record.ztzhtbh != null">
        ZTZHTBH = #{record.ztzhtbh,jdbcType=VARCHAR},
      </if>
      <if test="record.ztzhthh != null">
        ZTZHTHH = #{record.ztzhthh,jdbcType=VARCHAR},
      </if>
      <if test="record.zplcode != null">
        ZPLCODE = #{record.zplcode,jdbcType=VARCHAR},
      </if>
      <if test="record.zplsug != null">
        ZPLSUG = #{record.zplsug,jdbcType=VARCHAR},
      </if>
      <if test="record.zcjbm != null">
        ZCJBM = #{record.zcjbm,jdbcType=VARCHAR},
      </if>
      <if test="record.mengeZz != null">
        MENGE_ZZ = #{record.mengeZz,jdbcType=DECIMAL},
      </if>
      <if test="record.zstockUse != null">
        ZSTOCK_USE = #{record.zstockUse,jdbcType=DECIMAL},
      </if>
      <if test="record.zsjfd != null">
        ZSJFD = #{record.zsjfd,jdbcType=DECIMAL},
      </if>
      <if test="record.remark != null">
        REMARK = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.username != null">
        USERNAME = #{record.username,jdbcType=VARCHAR},
      </if>
      <if test="record.udate != null">
        UDATE = #{record.udate,jdbcType=VARCHAR},
      </if>
      <if test="record.utime != null">
        UTIME = #{record.utime,jdbcType=VARCHAR},
      </if>
      <if test="record.zmdz != null">
        ZMDZ = #{record.zmdz,jdbcType=VARCHAR},
      </if>
      <if test="record.zsrmflag != null">
        ZSRMFLAG = #{record.zsrmflag,jdbcType=VARCHAR},
      </if>
      <if test="record.brtwrLast != null">
        BRTWR_LAST = #{record.brtwrLast,jdbcType=DECIMAL},
      </if>
      <if test="record.peinhLast != null">
        PEINH_LAST = #{record.peinhLast,jdbcType=INTEGER},
      </if>
      <if test="record.labstDcJs != null">
        LABST_DC_JS = #{record.labstDcJs,jdbcType=DECIMAL},
      </if>
      <if test="record.mengeWqJs != null">
        MENGE_WQ_JS = #{record.mengeWqJs,jdbcType=DECIMAL},
      </if>
      <if test="record.zzxl30Js != null">
        ZZXL_30_JS = #{record.zzxl30Js,jdbcType=DECIMAL},
      </if>
      <if test="record.zmdqtyJs != null">
        ZMDQTY_JS = #{record.zmdqtyJs,jdbcType=DECIMAL},
      </if>
      <if test="record.zzzmdsq != null">
        ZZZMDSQ = #{record.zzzmdsq,jdbcType=VARCHAR},
      </if>
      <if test="record.zzzmdhh != null">
        ZZZMDHH = #{record.zzzmdhh,jdbcType=VARCHAR},
      </if>
      <if test="record.zjhdat != null">
        ZJHDAT = #{record.zjhdat,jdbcType=VARCHAR},
      </if>
      <if test="record.zjhtime != null">
        ZJHTIME = #{record.zjhtime,jdbcType=VARCHAR},
      </if>
      <if test="record.zsrmdat != null">
        ZSRMDAT = #{record.zsrmdat,jdbcType=VARCHAR},
      </if>
      <if test="record.zsrmtime != null">
        ZSRMTIME = #{record.zsrmtime,jdbcType=VARCHAR},
      </if>
      <if test="record.zjhzdat != null">
        ZJHZDAT = #{record.zjhzdat,jdbcType=VARCHAR},
      </if>
      <if test="record.zjhztime != null">
        ZJHZTIME = #{record.zjhztime,jdbcType=VARCHAR},
      </if>
      <if test="record.frggr != null">
        FRGGR = #{record.frggr,jdbcType=VARCHAR},
      </if>
      <if test="record.frgsx != null">
        FRGSX = #{record.frgsx,jdbcType=VARCHAR},
      </if>
      <if test="record.frgkx != null">
        FRGKX = #{record.frgkx,jdbcType=VARCHAR},
      </if>
      <if test="record.frgco != null">
        FRGCO = #{record.frgco,jdbcType=VARCHAR},
      </if>
      <if test="record.submi != null">
        SUBMI = #{record.submi,jdbcType=VARCHAR},
      </if>
      <if test="record.zresult != null">
        ZRESULT = #{record.zresult,jdbcType=VARCHAR},
      </if>
      <if test="record.zlevel != null">
        ZLEVEL = #{record.zlevel,jdbcType=VARCHAR},
      </if>
      <if test="record.zspbs != null">
        ZSPBS = #{record.zspbs,jdbcType=VARCHAR},
      </if>
      <if test="record.zmess != null">
        ZMESS = #{record.zmess,jdbcType=VARCHAR},
      </if>
      <if test="record.lgort != null">
        LGORT = #{record.lgort,jdbcType=VARCHAR},
      </if>
      <if test="record.zdckykc != null">
        ZDCKYKC = #{record.zdckykc,jdbcType=DECIMAL},
      </if>
      <if test="record.zcyjswq != null">
        ZCYJSWQ = #{record.zcyjswq,jdbcType=DECIMAL},
      </if>
      <if test="record.zzdgdsc != null">
        ZZDGDSC = #{record.zzdgdsc,jdbcType=DECIMAL},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update SAP_ZMMT0355
    set id = #{record.id,jdbcType=BIGINT},
      MANDT = #{record.mandt,jdbcType=VARCHAR},
      ZGUID = #{record.zguid,jdbcType=VARCHAR},
      ZCGJHD = #{record.zcgjhd,jdbcType=VARCHAR},
      ZCGJHDHH = #{record.zcgjhdhh,jdbcType=VARCHAR},
      BSART = #{record.bsart,jdbcType=VARCHAR},
      LOEKZ = #{record.loekz,jdbcType=VARCHAR},
      ZPOFLAG = #{record.zpoflag,jdbcType=VARCHAR},
      ZOAFLAG = #{record.zoaflag,jdbcType=VARCHAR},
      EBELN = #{record.ebeln,jdbcType=VARCHAR},
      EBELP = #{record.ebelp,jdbcType=VARCHAR},
      EKORG = #{record.ekorg,jdbcType=VARCHAR},
      BUKRS = #{record.bukrs,jdbcType=VARCHAR},
      EKGRP = #{record.ekgrp,jdbcType=VARCHAR},
      LIFNR = #{record.lifnr,jdbcType=VARCHAR},
      ZYWCJ = #{record.zywcj,jdbcType=VARCHAR},
      ZZZWLMS = #{record.zzzwlms,jdbcType=VARCHAR},
      ERNAM = #{record.ernam,jdbcType=VARCHAR},
      ERDAT = #{record.erdat,jdbcType=VARCHAR},
      ERZET = #{record.erzet,jdbcType=VARCHAR},
      WERKS = #{record.werks,jdbcType=VARCHAR},
      MATNR = #{record.matnr,jdbcType=VARCHAR},
      MENGE = #{record.menge,jdbcType=DECIMAL},
      ZMENGE = #{record.zmenge,jdbcType=DECIMAL},
      EINDT = #{record.eindt,jdbcType=VARCHAR},
      ZEINDT = #{record.zeindt,jdbcType=VARCHAR},
      MEINS = #{record.meins,jdbcType=VARCHAR},
      ZHSDJ = #{record.zhsdj,jdbcType=DECIMAL},
      ZQRHSDJ = #{record.zqrhsdj,jdbcType=DECIMAL},
      PEINH = #{record.peinh,jdbcType=INTEGER},
      ZPEINH = #{record.zpeinh,jdbcType=INTEGER},
      MWSKZ = #{record.mwskz,jdbcType=VARCHAR},
      ZMWSKZ = #{record.zmwskz,jdbcType=VARCHAR},
      ZCFLAG = #{record.zcflag,jdbcType=VARCHAR},
      ZTERM = #{record.zterm,jdbcType=VARCHAR},
      ZZZUSERID = #{record.zzzuserid,jdbcType=VARCHAR},
      ZSHCK = #{record.zshck,jdbcType=VARCHAR},
      ZSPJB = #{record.zspjb,jdbcType=INTEGER},
      KONNR = #{record.konnr,jdbcType=VARCHAR},
      KTPNR = #{record.ktpnr,jdbcType=VARCHAR},
      ZTZHTBH = #{record.ztzhtbh,jdbcType=VARCHAR},
      ZTZHTHH = #{record.ztzhthh,jdbcType=VARCHAR},
      ZPLCODE = #{record.zplcode,jdbcType=VARCHAR},
      ZPLSUG = #{record.zplsug,jdbcType=VARCHAR},
      ZCJBM = #{record.zcjbm,jdbcType=VARCHAR},
      MENGE_ZZ = #{record.mengeZz,jdbcType=DECIMAL},
      ZSTOCK_USE = #{record.zstockUse,jdbcType=DECIMAL},
      ZSJFD = #{record.zsjfd,jdbcType=DECIMAL},
      REMARK = #{record.remark,jdbcType=VARCHAR},
      USERNAME = #{record.username,jdbcType=VARCHAR},
      UDATE = #{record.udate,jdbcType=VARCHAR},
      UTIME = #{record.utime,jdbcType=VARCHAR},
      ZMDZ = #{record.zmdz,jdbcType=VARCHAR},
      ZSRMFLAG = #{record.zsrmflag,jdbcType=VARCHAR},
      BRTWR_LAST = #{record.brtwrLast,jdbcType=DECIMAL},
      PEINH_LAST = #{record.peinhLast,jdbcType=INTEGER},
      LABST_DC_JS = #{record.labstDcJs,jdbcType=DECIMAL},
      MENGE_WQ_JS = #{record.mengeWqJs,jdbcType=DECIMAL},
      ZZXL_30_JS = #{record.zzxl30Js,jdbcType=DECIMAL},
      ZMDQTY_JS = #{record.zmdqtyJs,jdbcType=DECIMAL},
      ZZZMDSQ = #{record.zzzmdsq,jdbcType=VARCHAR},
      ZZZMDHH = #{record.zzzmdhh,jdbcType=VARCHAR},
      ZJHDAT = #{record.zjhdat,jdbcType=VARCHAR},
      ZJHTIME = #{record.zjhtime,jdbcType=VARCHAR},
      ZSRMDAT = #{record.zsrmdat,jdbcType=VARCHAR},
      ZSRMTIME = #{record.zsrmtime,jdbcType=VARCHAR},
      ZJHZDAT = #{record.zjhzdat,jdbcType=VARCHAR},
      ZJHZTIME = #{record.zjhztime,jdbcType=VARCHAR},
      FRGGR = #{record.frggr,jdbcType=VARCHAR},
      FRGSX = #{record.frgsx,jdbcType=VARCHAR},
      FRGKX = #{record.frgkx,jdbcType=VARCHAR},
      FRGCO = #{record.frgco,jdbcType=VARCHAR},
      SUBMI = #{record.submi,jdbcType=VARCHAR},
      ZRESULT = #{record.zresult,jdbcType=VARCHAR},
      ZLEVEL = #{record.zlevel,jdbcType=VARCHAR},
      ZSPBS = #{record.zspbs,jdbcType=VARCHAR},
      ZMESS = #{record.zmess,jdbcType=VARCHAR},
      LGORT = #{record.lgort,jdbcType=VARCHAR},
      ZDCKYKC = #{record.zdckykc,jdbcType=DECIMAL},
      ZCYJSWQ = #{record.zcyjswq,jdbcType=DECIMAL},
      ZZDGDSC = #{record.zzdgdsc,jdbcType=DECIMAL},
      extend = #{record.extend,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update SAP_ZMMT0355
    set id = #{record.id,jdbcType=BIGINT},
      MANDT = #{record.mandt,jdbcType=VARCHAR},
      ZGUID = #{record.zguid,jdbcType=VARCHAR},
      ZCGJHD = #{record.zcgjhd,jdbcType=VARCHAR},
      ZCGJHDHH = #{record.zcgjhdhh,jdbcType=VARCHAR},
      BSART = #{record.bsart,jdbcType=VARCHAR},
      LOEKZ = #{record.loekz,jdbcType=VARCHAR},
      ZPOFLAG = #{record.zpoflag,jdbcType=VARCHAR},
      ZOAFLAG = #{record.zoaflag,jdbcType=VARCHAR},
      EBELN = #{record.ebeln,jdbcType=VARCHAR},
      EBELP = #{record.ebelp,jdbcType=VARCHAR},
      EKORG = #{record.ekorg,jdbcType=VARCHAR},
      BUKRS = #{record.bukrs,jdbcType=VARCHAR},
      EKGRP = #{record.ekgrp,jdbcType=VARCHAR},
      LIFNR = #{record.lifnr,jdbcType=VARCHAR},
      ZYWCJ = #{record.zywcj,jdbcType=VARCHAR},
      ZZZWLMS = #{record.zzzwlms,jdbcType=VARCHAR},
      ERNAM = #{record.ernam,jdbcType=VARCHAR},
      ERDAT = #{record.erdat,jdbcType=VARCHAR},
      ERZET = #{record.erzet,jdbcType=VARCHAR},
      WERKS = #{record.werks,jdbcType=VARCHAR},
      MATNR = #{record.matnr,jdbcType=VARCHAR},
      MENGE = #{record.menge,jdbcType=DECIMAL},
      ZMENGE = #{record.zmenge,jdbcType=DECIMAL},
      EINDT = #{record.eindt,jdbcType=VARCHAR},
      ZEINDT = #{record.zeindt,jdbcType=VARCHAR},
      MEINS = #{record.meins,jdbcType=VARCHAR},
      ZHSDJ = #{record.zhsdj,jdbcType=DECIMAL},
      ZQRHSDJ = #{record.zqrhsdj,jdbcType=DECIMAL},
      PEINH = #{record.peinh,jdbcType=INTEGER},
      ZPEINH = #{record.zpeinh,jdbcType=INTEGER},
      MWSKZ = #{record.mwskz,jdbcType=VARCHAR},
      ZMWSKZ = #{record.zmwskz,jdbcType=VARCHAR},
      ZCFLAG = #{record.zcflag,jdbcType=VARCHAR},
      ZTERM = #{record.zterm,jdbcType=VARCHAR},
      ZZZUSERID = #{record.zzzuserid,jdbcType=VARCHAR},
      ZSHCK = #{record.zshck,jdbcType=VARCHAR},
      ZSPJB = #{record.zspjb,jdbcType=INTEGER},
      KONNR = #{record.konnr,jdbcType=VARCHAR},
      KTPNR = #{record.ktpnr,jdbcType=VARCHAR},
      ZTZHTBH = #{record.ztzhtbh,jdbcType=VARCHAR},
      ZTZHTHH = #{record.ztzhthh,jdbcType=VARCHAR},
      ZPLCODE = #{record.zplcode,jdbcType=VARCHAR},
      ZPLSUG = #{record.zplsug,jdbcType=VARCHAR},
      ZCJBM = #{record.zcjbm,jdbcType=VARCHAR},
      MENGE_ZZ = #{record.mengeZz,jdbcType=DECIMAL},
      ZSTOCK_USE = #{record.zstockUse,jdbcType=DECIMAL},
      ZSJFD = #{record.zsjfd,jdbcType=DECIMAL},
      REMARK = #{record.remark,jdbcType=VARCHAR},
      USERNAME = #{record.username,jdbcType=VARCHAR},
      UDATE = #{record.udate,jdbcType=VARCHAR},
      UTIME = #{record.utime,jdbcType=VARCHAR},
      ZMDZ = #{record.zmdz,jdbcType=VARCHAR},
      ZSRMFLAG = #{record.zsrmflag,jdbcType=VARCHAR},
      BRTWR_LAST = #{record.brtwrLast,jdbcType=DECIMAL},
      PEINH_LAST = #{record.peinhLast,jdbcType=INTEGER},
      LABST_DC_JS = #{record.labstDcJs,jdbcType=DECIMAL},
      MENGE_WQ_JS = #{record.mengeWqJs,jdbcType=DECIMAL},
      ZZXL_30_JS = #{record.zzxl30Js,jdbcType=DECIMAL},
      ZMDQTY_JS = #{record.zmdqtyJs,jdbcType=DECIMAL},
      ZZZMDSQ = #{record.zzzmdsq,jdbcType=VARCHAR},
      ZZZMDHH = #{record.zzzmdhh,jdbcType=VARCHAR},
      ZJHDAT = #{record.zjhdat,jdbcType=VARCHAR},
      ZJHTIME = #{record.zjhtime,jdbcType=VARCHAR},
      ZSRMDAT = #{record.zsrmdat,jdbcType=VARCHAR},
      ZSRMTIME = #{record.zsrmtime,jdbcType=VARCHAR},
      ZJHZDAT = #{record.zjhzdat,jdbcType=VARCHAR},
      ZJHZTIME = #{record.zjhztime,jdbcType=VARCHAR},
      FRGGR = #{record.frggr,jdbcType=VARCHAR},
      FRGSX = #{record.frgsx,jdbcType=VARCHAR},
      FRGKX = #{record.frgkx,jdbcType=VARCHAR},
      FRGCO = #{record.frgco,jdbcType=VARCHAR},
      SUBMI = #{record.submi,jdbcType=VARCHAR},
      ZRESULT = #{record.zresult,jdbcType=VARCHAR},
      ZLEVEL = #{record.zlevel,jdbcType=VARCHAR},
      ZSPBS = #{record.zspbs,jdbcType=VARCHAR},
      ZMESS = #{record.zmess,jdbcType=VARCHAR},
      LGORT = #{record.lgort,jdbcType=VARCHAR},
      ZDCKYKC = #{record.zdckykc,jdbcType=DECIMAL},
      ZCYJSWQ = #{record.zcyjswq,jdbcType=DECIMAL},
      ZZDGDSC = #{record.zzdgdsc,jdbcType=DECIMAL}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.iscm.entity.SapZmmt0355">
    update SAP_ZMMT0355
    <set>
      <if test="mandt != null">
        MANDT = #{mandt,jdbcType=VARCHAR},
      </if>
      <if test="zguid != null">
        ZGUID = #{zguid,jdbcType=VARCHAR},
      </if>
      <if test="zcgjhd != null">
        ZCGJHD = #{zcgjhd,jdbcType=VARCHAR},
      </if>
      <if test="zcgjhdhh != null">
        ZCGJHDHH = #{zcgjhdhh,jdbcType=VARCHAR},
      </if>
      <if test="bsart != null">
        BSART = #{bsart,jdbcType=VARCHAR},
      </if>
      <if test="loekz != null">
        LOEKZ = #{loekz,jdbcType=VARCHAR},
      </if>
      <if test="zpoflag != null">
        ZPOFLAG = #{zpoflag,jdbcType=VARCHAR},
      </if>
      <if test="zoaflag != null">
        ZOAFLAG = #{zoaflag,jdbcType=VARCHAR},
      </if>
      <if test="ebeln != null">
        EBELN = #{ebeln,jdbcType=VARCHAR},
      </if>
      <if test="ebelp != null">
        EBELP = #{ebelp,jdbcType=VARCHAR},
      </if>
      <if test="ekorg != null">
        EKORG = #{ekorg,jdbcType=VARCHAR},
      </if>
      <if test="bukrs != null">
        BUKRS = #{bukrs,jdbcType=VARCHAR},
      </if>
      <if test="ekgrp != null">
        EKGRP = #{ekgrp,jdbcType=VARCHAR},
      </if>
      <if test="lifnr != null">
        LIFNR = #{lifnr,jdbcType=VARCHAR},
      </if>
      <if test="zywcj != null">
        ZYWCJ = #{zywcj,jdbcType=VARCHAR},
      </if>
      <if test="zzzwlms != null">
        ZZZWLMS = #{zzzwlms,jdbcType=VARCHAR},
      </if>
      <if test="ernam != null">
        ERNAM = #{ernam,jdbcType=VARCHAR},
      </if>
      <if test="erdat != null">
        ERDAT = #{erdat,jdbcType=VARCHAR},
      </if>
      <if test="erzet != null">
        ERZET = #{erzet,jdbcType=VARCHAR},
      </if>
      <if test="werks != null">
        WERKS = #{werks,jdbcType=VARCHAR},
      </if>
      <if test="matnr != null">
        MATNR = #{matnr,jdbcType=VARCHAR},
      </if>
      <if test="menge != null">
        MENGE = #{menge,jdbcType=DECIMAL},
      </if>
      <if test="zmenge != null">
        ZMENGE = #{zmenge,jdbcType=DECIMAL},
      </if>
      <if test="eindt != null">
        EINDT = #{eindt,jdbcType=VARCHAR},
      </if>
      <if test="zeindt != null">
        ZEINDT = #{zeindt,jdbcType=VARCHAR},
      </if>
      <if test="meins != null">
        MEINS = #{meins,jdbcType=VARCHAR},
      </if>
      <if test="zhsdj != null">
        ZHSDJ = #{zhsdj,jdbcType=DECIMAL},
      </if>
      <if test="zqrhsdj != null">
        ZQRHSDJ = #{zqrhsdj,jdbcType=DECIMAL},
      </if>
      <if test="peinh != null">
        PEINH = #{peinh,jdbcType=INTEGER},
      </if>
      <if test="zpeinh != null">
        ZPEINH = #{zpeinh,jdbcType=INTEGER},
      </if>
      <if test="mwskz != null">
        MWSKZ = #{mwskz,jdbcType=VARCHAR},
      </if>
      <if test="zmwskz != null">
        ZMWSKZ = #{zmwskz,jdbcType=VARCHAR},
      </if>
      <if test="zcflag != null">
        ZCFLAG = #{zcflag,jdbcType=VARCHAR},
      </if>
      <if test="zterm != null">
        ZTERM = #{zterm,jdbcType=VARCHAR},
      </if>
      <if test="zzzuserid != null">
        ZZZUSERID = #{zzzuserid,jdbcType=VARCHAR},
      </if>
      <if test="zshck != null">
        ZSHCK = #{zshck,jdbcType=VARCHAR},
      </if>
      <if test="zspjb != null">
        ZSPJB = #{zspjb,jdbcType=INTEGER},
      </if>
      <if test="konnr != null">
        KONNR = #{konnr,jdbcType=VARCHAR},
      </if>
      <if test="ktpnr != null">
        KTPNR = #{ktpnr,jdbcType=VARCHAR},
      </if>
      <if test="ztzhtbh != null">
        ZTZHTBH = #{ztzhtbh,jdbcType=VARCHAR},
      </if>
      <if test="ztzhthh != null">
        ZTZHTHH = #{ztzhthh,jdbcType=VARCHAR},
      </if>
      <if test="zplcode != null">
        ZPLCODE = #{zplcode,jdbcType=VARCHAR},
      </if>
      <if test="zplsug != null">
        ZPLSUG = #{zplsug,jdbcType=VARCHAR},
      </if>
      <if test="zcjbm != null">
        ZCJBM = #{zcjbm,jdbcType=VARCHAR},
      </if>
      <if test="mengeZz != null">
        MENGE_ZZ = #{mengeZz,jdbcType=DECIMAL},
      </if>
      <if test="zstockUse != null">
        ZSTOCK_USE = #{zstockUse,jdbcType=DECIMAL},
      </if>
      <if test="zsjfd != null">
        ZSJFD = #{zsjfd,jdbcType=DECIMAL},
      </if>
      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="username != null">
        USERNAME = #{username,jdbcType=VARCHAR},
      </if>
      <if test="udate != null">
        UDATE = #{udate,jdbcType=VARCHAR},
      </if>
      <if test="utime != null">
        UTIME = #{utime,jdbcType=VARCHAR},
      </if>
      <if test="zmdz != null">
        ZMDZ = #{zmdz,jdbcType=VARCHAR},
      </if>
      <if test="zsrmflag != null">
        ZSRMFLAG = #{zsrmflag,jdbcType=VARCHAR},
      </if>
      <if test="brtwrLast != null">
        BRTWR_LAST = #{brtwrLast,jdbcType=DECIMAL},
      </if>
      <if test="peinhLast != null">
        PEINH_LAST = #{peinhLast,jdbcType=INTEGER},
      </if>
      <if test="labstDcJs != null">
        LABST_DC_JS = #{labstDcJs,jdbcType=DECIMAL},
      </if>
      <if test="mengeWqJs != null">
        MENGE_WQ_JS = #{mengeWqJs,jdbcType=DECIMAL},
      </if>
      <if test="zzxl30Js != null">
        ZZXL_30_JS = #{zzxl30Js,jdbcType=DECIMAL},
      </if>
      <if test="zmdqtyJs != null">
        ZMDQTY_JS = #{zmdqtyJs,jdbcType=DECIMAL},
      </if>
      <if test="zzzmdsq != null">
        ZZZMDSQ = #{zzzmdsq,jdbcType=VARCHAR},
      </if>
      <if test="zzzmdhh != null">
        ZZZMDHH = #{zzzmdhh,jdbcType=VARCHAR},
      </if>
      <if test="zjhdat != null">
        ZJHDAT = #{zjhdat,jdbcType=VARCHAR},
      </if>
      <if test="zjhtime != null">
        ZJHTIME = #{zjhtime,jdbcType=VARCHAR},
      </if>
      <if test="zsrmdat != null">
        ZSRMDAT = #{zsrmdat,jdbcType=VARCHAR},
      </if>
      <if test="zsrmtime != null">
        ZSRMTIME = #{zsrmtime,jdbcType=VARCHAR},
      </if>
      <if test="zjhzdat != null">
        ZJHZDAT = #{zjhzdat,jdbcType=VARCHAR},
      </if>
      <if test="zjhztime != null">
        ZJHZTIME = #{zjhztime,jdbcType=VARCHAR},
      </if>
      <if test="frggr != null">
        FRGGR = #{frggr,jdbcType=VARCHAR},
      </if>
      <if test="frgsx != null">
        FRGSX = #{frgsx,jdbcType=VARCHAR},
      </if>
      <if test="frgkx != null">
        FRGKX = #{frgkx,jdbcType=VARCHAR},
      </if>
      <if test="frgco != null">
        FRGCO = #{frgco,jdbcType=VARCHAR},
      </if>
      <if test="submi != null">
        SUBMI = #{submi,jdbcType=VARCHAR},
      </if>
      <if test="zresult != null">
        ZRESULT = #{zresult,jdbcType=VARCHAR},
      </if>
      <if test="zlevel != null">
        ZLEVEL = #{zlevel,jdbcType=VARCHAR},
      </if>
      <if test="zspbs != null">
        ZSPBS = #{zspbs,jdbcType=VARCHAR},
      </if>
      <if test="zmess != null">
        ZMESS = #{zmess,jdbcType=VARCHAR},
      </if>
      <if test="lgort != null">
        LGORT = #{lgort,jdbcType=VARCHAR},
      </if>
      <if test="zdckykc != null">
        ZDCKYKC = #{zdckykc,jdbcType=DECIMAL},
      </if>
      <if test="zcyjswq != null">
        ZCYJSWQ = #{zcyjswq,jdbcType=DECIMAL},
      </if>
      <if test="zzdgdsc != null">
        ZZDGDSC = #{zzdgdsc,jdbcType=DECIMAL},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.cowell.iscm.entity.SapZmmt0355">
    update SAP_ZMMT0355
    set MANDT = #{mandt,jdbcType=VARCHAR},
      ZGUID = #{zguid,jdbcType=VARCHAR},
      ZCGJHD = #{zcgjhd,jdbcType=VARCHAR},
      ZCGJHDHH = #{zcgjhdhh,jdbcType=VARCHAR},
      BSART = #{bsart,jdbcType=VARCHAR},
      LOEKZ = #{loekz,jdbcType=VARCHAR},
      ZPOFLAG = #{zpoflag,jdbcType=VARCHAR},
      ZOAFLAG = #{zoaflag,jdbcType=VARCHAR},
      EBELN = #{ebeln,jdbcType=VARCHAR},
      EBELP = #{ebelp,jdbcType=VARCHAR},
      EKORG = #{ekorg,jdbcType=VARCHAR},
      BUKRS = #{bukrs,jdbcType=VARCHAR},
      EKGRP = #{ekgrp,jdbcType=VARCHAR},
      LIFNR = #{lifnr,jdbcType=VARCHAR},
      ZYWCJ = #{zywcj,jdbcType=VARCHAR},
      ZZZWLMS = #{zzzwlms,jdbcType=VARCHAR},
      ERNAM = #{ernam,jdbcType=VARCHAR},
      ERDAT = #{erdat,jdbcType=VARCHAR},
      ERZET = #{erzet,jdbcType=VARCHAR},
      WERKS = #{werks,jdbcType=VARCHAR},
      MATNR = #{matnr,jdbcType=VARCHAR},
      MENGE = #{menge,jdbcType=DECIMAL},
      ZMENGE = #{zmenge,jdbcType=DECIMAL},
      EINDT = #{eindt,jdbcType=VARCHAR},
      ZEINDT = #{zeindt,jdbcType=VARCHAR},
      MEINS = #{meins,jdbcType=VARCHAR},
      ZHSDJ = #{zhsdj,jdbcType=DECIMAL},
      ZQRHSDJ = #{zqrhsdj,jdbcType=DECIMAL},
      PEINH = #{peinh,jdbcType=INTEGER},
      ZPEINH = #{zpeinh,jdbcType=INTEGER},
      MWSKZ = #{mwskz,jdbcType=VARCHAR},
      ZMWSKZ = #{zmwskz,jdbcType=VARCHAR},
      ZCFLAG = #{zcflag,jdbcType=VARCHAR},
      ZTERM = #{zterm,jdbcType=VARCHAR},
      ZZZUSERID = #{zzzuserid,jdbcType=VARCHAR},
      ZSHCK = #{zshck,jdbcType=VARCHAR},
      ZSPJB = #{zspjb,jdbcType=INTEGER},
      KONNR = #{konnr,jdbcType=VARCHAR},
      KTPNR = #{ktpnr,jdbcType=VARCHAR},
      ZTZHTBH = #{ztzhtbh,jdbcType=VARCHAR},
      ZTZHTHH = #{ztzhthh,jdbcType=VARCHAR},
      ZPLCODE = #{zplcode,jdbcType=VARCHAR},
      ZPLSUG = #{zplsug,jdbcType=VARCHAR},
      ZCJBM = #{zcjbm,jdbcType=VARCHAR},
      MENGE_ZZ = #{mengeZz,jdbcType=DECIMAL},
      ZSTOCK_USE = #{zstockUse,jdbcType=DECIMAL},
      ZSJFD = #{zsjfd,jdbcType=DECIMAL},
      REMARK = #{remark,jdbcType=VARCHAR},
      USERNAME = #{username,jdbcType=VARCHAR},
      UDATE = #{udate,jdbcType=VARCHAR},
      UTIME = #{utime,jdbcType=VARCHAR},
      ZMDZ = #{zmdz,jdbcType=VARCHAR},
      ZSRMFLAG = #{zsrmflag,jdbcType=VARCHAR},
      BRTWR_LAST = #{brtwrLast,jdbcType=DECIMAL},
      PEINH_LAST = #{peinhLast,jdbcType=INTEGER},
      LABST_DC_JS = #{labstDcJs,jdbcType=DECIMAL},
      MENGE_WQ_JS = #{mengeWqJs,jdbcType=DECIMAL},
      ZZXL_30_JS = #{zzxl30Js,jdbcType=DECIMAL},
      ZMDQTY_JS = #{zmdqtyJs,jdbcType=DECIMAL},
      ZZZMDSQ = #{zzzmdsq,jdbcType=VARCHAR},
      ZZZMDHH = #{zzzmdhh,jdbcType=VARCHAR},
      ZJHDAT = #{zjhdat,jdbcType=VARCHAR},
      ZJHTIME = #{zjhtime,jdbcType=VARCHAR},
      ZSRMDAT = #{zsrmdat,jdbcType=VARCHAR},
      ZSRMTIME = #{zsrmtime,jdbcType=VARCHAR},
      ZJHZDAT = #{zjhzdat,jdbcType=VARCHAR},
      ZJHZTIME = #{zjhztime,jdbcType=VARCHAR},
      FRGGR = #{frggr,jdbcType=VARCHAR},
      FRGSX = #{frgsx,jdbcType=VARCHAR},
      FRGKX = #{frgkx,jdbcType=VARCHAR},
      FRGCO = #{frgco,jdbcType=VARCHAR},
      SUBMI = #{submi,jdbcType=VARCHAR},
      ZRESULT = #{zresult,jdbcType=VARCHAR},
      ZLEVEL = #{zlevel,jdbcType=VARCHAR},
      ZSPBS = #{zspbs,jdbcType=VARCHAR},
      ZMESS = #{zmess,jdbcType=VARCHAR},
      LGORT = #{lgort,jdbcType=VARCHAR},
      ZDCKYKC = #{zdckykc,jdbcType=DECIMAL},
      ZCYJSWQ = #{zcyjswq,jdbcType=DECIMAL},
      ZZDGDSC = #{zzdgdsc,jdbcType=DECIMAL},
      extend = #{extend,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.iscm.entity.SapZmmt0355">
    update SAP_ZMMT0355
    set MANDT = #{mandt,jdbcType=VARCHAR},
      ZGUID = #{zguid,jdbcType=VARCHAR},
      ZCGJHD = #{zcgjhd,jdbcType=VARCHAR},
      ZCGJHDHH = #{zcgjhdhh,jdbcType=VARCHAR},
      BSART = #{bsart,jdbcType=VARCHAR},
      LOEKZ = #{loekz,jdbcType=VARCHAR},
      ZPOFLAG = #{zpoflag,jdbcType=VARCHAR},
      ZOAFLAG = #{zoaflag,jdbcType=VARCHAR},
      EBELN = #{ebeln,jdbcType=VARCHAR},
      EBELP = #{ebelp,jdbcType=VARCHAR},
      EKORG = #{ekorg,jdbcType=VARCHAR},
      BUKRS = #{bukrs,jdbcType=VARCHAR},
      EKGRP = #{ekgrp,jdbcType=VARCHAR},
      LIFNR = #{lifnr,jdbcType=VARCHAR},
      ZYWCJ = #{zywcj,jdbcType=VARCHAR},
      ZZZWLMS = #{zzzwlms,jdbcType=VARCHAR},
      ERNAM = #{ernam,jdbcType=VARCHAR},
      ERDAT = #{erdat,jdbcType=VARCHAR},
      ERZET = #{erzet,jdbcType=VARCHAR},
      WERKS = #{werks,jdbcType=VARCHAR},
      MATNR = #{matnr,jdbcType=VARCHAR},
      MENGE = #{menge,jdbcType=DECIMAL},
      ZMENGE = #{zmenge,jdbcType=DECIMAL},
      EINDT = #{eindt,jdbcType=VARCHAR},
      ZEINDT = #{zeindt,jdbcType=VARCHAR},
      MEINS = #{meins,jdbcType=VARCHAR},
      ZHSDJ = #{zhsdj,jdbcType=DECIMAL},
      ZQRHSDJ = #{zqrhsdj,jdbcType=DECIMAL},
      PEINH = #{peinh,jdbcType=INTEGER},
      ZPEINH = #{zpeinh,jdbcType=INTEGER},
      MWSKZ = #{mwskz,jdbcType=VARCHAR},
      ZMWSKZ = #{zmwskz,jdbcType=VARCHAR},
      ZCFLAG = #{zcflag,jdbcType=VARCHAR},
      ZTERM = #{zterm,jdbcType=VARCHAR},
      ZZZUSERID = #{zzzuserid,jdbcType=VARCHAR},
      ZSHCK = #{zshck,jdbcType=VARCHAR},
      ZSPJB = #{zspjb,jdbcType=INTEGER},
      KONNR = #{konnr,jdbcType=VARCHAR},
      KTPNR = #{ktpnr,jdbcType=VARCHAR},
      ZTZHTBH = #{ztzhtbh,jdbcType=VARCHAR},
      ZTZHTHH = #{ztzhthh,jdbcType=VARCHAR},
      ZPLCODE = #{zplcode,jdbcType=VARCHAR},
      ZPLSUG = #{zplsug,jdbcType=VARCHAR},
      ZCJBM = #{zcjbm,jdbcType=VARCHAR},
      MENGE_ZZ = #{mengeZz,jdbcType=DECIMAL},
      ZSTOCK_USE = #{zstockUse,jdbcType=DECIMAL},
      ZSJFD = #{zsjfd,jdbcType=DECIMAL},
      REMARK = #{remark,jdbcType=VARCHAR},
      USERNAME = #{username,jdbcType=VARCHAR},
      UDATE = #{udate,jdbcType=VARCHAR},
      UTIME = #{utime,jdbcType=VARCHAR},
      ZMDZ = #{zmdz,jdbcType=VARCHAR},
      ZSRMFLAG = #{zsrmflag,jdbcType=VARCHAR},
      BRTWR_LAST = #{brtwrLast,jdbcType=DECIMAL},
      PEINH_LAST = #{peinhLast,jdbcType=INTEGER},
      LABST_DC_JS = #{labstDcJs,jdbcType=DECIMAL},
      MENGE_WQ_JS = #{mengeWqJs,jdbcType=DECIMAL},
      ZZXL_30_JS = #{zzxl30Js,jdbcType=DECIMAL},
      ZMDQTY_JS = #{zmdqtyJs,jdbcType=DECIMAL},
      ZZZMDSQ = #{zzzmdsq,jdbcType=VARCHAR},
      ZZZMDHH = #{zzzmdhh,jdbcType=VARCHAR},
      ZJHDAT = #{zjhdat,jdbcType=VARCHAR},
      ZJHTIME = #{zjhtime,jdbcType=VARCHAR},
      ZSRMDAT = #{zsrmdat,jdbcType=VARCHAR},
      ZSRMTIME = #{zsrmtime,jdbcType=VARCHAR},
      ZJHZDAT = #{zjhzdat,jdbcType=VARCHAR},
      ZJHZTIME = #{zjhztime,jdbcType=VARCHAR},
      FRGGR = #{frggr,jdbcType=VARCHAR},
      FRGSX = #{frgsx,jdbcType=VARCHAR},
      FRGKX = #{frgkx,jdbcType=VARCHAR},
      FRGCO = #{frgco,jdbcType=VARCHAR},
      SUBMI = #{submi,jdbcType=VARCHAR},
      ZRESULT = #{zresult,jdbcType=VARCHAR},
      ZLEVEL = #{zlevel,jdbcType=VARCHAR},
      ZSPBS = #{zspbs,jdbcType=VARCHAR},
      ZMESS = #{zmess,jdbcType=VARCHAR},
      LGORT = #{lgort,jdbcType=VARCHAR},
      ZDCKYKC = #{zdckykc,jdbcType=DECIMAL},
      ZCYJSWQ = #{zcyjswq,jdbcType=DECIMAL},
      ZZDGDSC = #{zzdgdsc,jdbcType=DECIMAL}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>