<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.BdpAvgDailySalesMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.BdpAvgDailySales">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="store_org_id" jdbcType="BIGINT" property="storeOrgId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="store_attr" jdbcType="TINYINT" property="storeAttr" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="special_ctrl" jdbcType="TINYINT" property="specialCtrl" />
    <result column="distr_forbid" jdbcType="TINYINT" property="distrForbid" />
    <result column="apply_forbid" jdbcType="TINYINT" property="applyForbid" />
    <result column="valuable" jdbcType="TINYINT" property="valuable" />
    <result column="dtp_goods" jdbcType="TINYINT" property="dtpGoods" />
    <result column="coldchainind" jdbcType="TINYINT" property="coldchainind" />
    <result column="special_attr" jdbcType="VARCHAR" property="specialAttr" />
    <result column="purchase_type" jdbcType="VARCHAR" property="purchaseType" />
    <result column="goods_level" jdbcType="TINYINT" property="goodsLevel" />
    <result column="goods_status" jdbcType="TINYINT" property="goodsStatus" />
    <result column="lawful" jdbcType="TINYINT" property="lawful" />
    <result column="newable" jdbcType="TINYINT" property="newable" />
    <result column="category_id" jdbcType="BIGINT" property="categoryId" />
    <result column="middle_category_id" jdbcType="BIGINT" property="middleCategoryId" />
    <result column="small_category_id" jdbcType="BIGINT" property="smallCategoryId" />
    <result column="sub_category_id" jdbcType="BIGINT" property="subCategoryId" />
    <result column="push_level" jdbcType="VARCHAR" property="pushLevel" />
    <result column="goods_line" jdbcType="TINYINT" property="goodsLine" />
    <result column="middle_package_qty" jdbcType="DECIMAL" property="middlePackageQty" />
    <result column="middle_package_switch_biz" jdbcType="VARCHAR" property="middlePackageSwitchBiz" />
    <result column="middle_package_switch_store" jdbcType="VARCHAR" property="middlePackageSwitchStore" />
    <result column="apply_ratio" jdbcType="DECIMAL" property="applyRatio" />
    <result column="special_thirty_days_qty" jdbcType="DECIMAL" property="specialThirtyDaysQty" />
    <result column="stock_upper_limit_days" jdbcType="INTEGER" property="stockUpperLimitDays" />
    <result column="stock_lower_limit_days" jdbcType="INTEGER" property="stockLowerLimitDays" />
    <result column="average_daily_sales" jdbcType="DECIMAL" property="averageDailySales" />
    <result column="min_display_qty" jdbcType="DECIMAL" property="minDisplayQty" />
    <result column="stock_upper_limit" jdbcType="DECIMAL" property="stockUpperLimit" />
    <result column="stock_lower_limit" jdbcType="DECIMAL" property="stockLowerLimit" />
    <result column="thirty_days_sales" jdbcType="DECIMAL" property="thirtyDaysSales" />
    <result column="sixty_days_sales" jdbcType="DECIMAL" property="sixtyDaysSales" />
    <result column="ninety_days_sales" jdbcType="DECIMAL" property="ninetyDaysSales" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, company_code, store_org_id, store_id, store_code, store_attr, goods_no, special_ctrl, 
    distr_forbid, apply_forbid, valuable, dtp_goods, coldchainind, special_attr, purchase_type, 
    goods_level, goods_status, lawful, newable, category_id, middle_category_id, small_category_id, 
    sub_category_id, push_level, goods_line, middle_package_qty, middle_package_switch_biz, 
    middle_package_switch_store, apply_ratio, special_thirty_days_qty, stock_upper_limit_days, 
    stock_lower_limit_days, average_daily_sales, min_display_qty, stock_upper_limit, 
    stock_lower_limit, thirty_days_sales, sixty_days_sales, ninety_days_sales, gmt_create, 
    gmt_update
  </sql>
  <select id="selectByExample" parameterType="com.cowell.iscm.entity.BdpAvgDailySalesExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from bdp_avg_daily_sales
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from bdp_avg_daily_sales
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from bdp_avg_daily_sales
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.iscm.entity.BdpAvgDailySalesExample">
    delete from bdp_avg_daily_sales
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entity.BdpAvgDailySales" useGeneratedKeys="true">
    insert into bdp_avg_daily_sales (company_code, store_org_id, store_id, 
      store_code, store_attr, goods_no, 
      special_ctrl, distr_forbid, apply_forbid, 
      valuable, dtp_goods, coldchainind, 
      special_attr, purchase_type, goods_level, 
      goods_status, lawful, newable, 
      category_id, middle_category_id, small_category_id, 
      sub_category_id, push_level, goods_line, 
      middle_package_qty, middle_package_switch_biz, 
      middle_package_switch_store, apply_ratio, special_thirty_days_qty, 
      stock_upper_limit_days, stock_lower_limit_days, 
      average_daily_sales, min_display_qty, stock_upper_limit, 
      stock_lower_limit, thirty_days_sales, sixty_days_sales, 
      ninety_days_sales, gmt_create, gmt_update
      )
    values (#{companyCode,jdbcType=VARCHAR}, #{storeOrgId,jdbcType=BIGINT}, #{storeId,jdbcType=BIGINT}, 
      #{storeCode,jdbcType=VARCHAR}, #{storeAttr,jdbcType=TINYINT}, #{goodsNo,jdbcType=VARCHAR}, 
      #{specialCtrl,jdbcType=TINYINT}, #{distrForbid,jdbcType=TINYINT}, #{applyForbid,jdbcType=TINYINT}, 
      #{valuable,jdbcType=TINYINT}, #{dtpGoods,jdbcType=TINYINT}, #{coldchainind,jdbcType=TINYINT}, 
      #{specialAttr,jdbcType=VARCHAR}, #{purchaseType,jdbcType=VARCHAR}, #{goodsLevel,jdbcType=TINYINT}, 
      #{goodsStatus,jdbcType=TINYINT}, #{lawful,jdbcType=TINYINT}, #{newable,jdbcType=TINYINT}, 
      #{categoryId,jdbcType=BIGINT}, #{middleCategoryId,jdbcType=BIGINT}, #{smallCategoryId,jdbcType=BIGINT}, 
      #{subCategoryId,jdbcType=BIGINT}, #{pushLevel,jdbcType=VARCHAR}, #{goodsLine,jdbcType=TINYINT}, 
      #{middlePackageQty,jdbcType=DECIMAL}, #{middlePackageSwitchBiz,jdbcType=VARCHAR}, 
      #{middlePackageSwitchStore,jdbcType=VARCHAR}, #{applyRatio,jdbcType=DECIMAL}, #{specialThirtyDaysQty,jdbcType=DECIMAL}, 
      #{stockUpperLimitDays,jdbcType=INTEGER}, #{stockLowerLimitDays,jdbcType=INTEGER}, 
      #{averageDailySales,jdbcType=DECIMAL}, #{minDisplayQty,jdbcType=DECIMAL}, #{stockUpperLimit,jdbcType=DECIMAL}, 
      #{stockLowerLimit,jdbcType=DECIMAL}, #{thirtyDaysSales,jdbcType=DECIMAL}, #{sixtyDaysSales,jdbcType=DECIMAL}, 
      #{ninetyDaysSales,jdbcType=DECIMAL}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entity.BdpAvgDailySales" useGeneratedKeys="true">
    insert into bdp_avg_daily_sales
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="storeOrgId != null">
        store_org_id,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="storeCode != null">
        store_code,
      </if>
      <if test="storeAttr != null">
        store_attr,
      </if>
      <if test="goodsNo != null">
        goods_no,
      </if>
      <if test="specialCtrl != null">
        special_ctrl,
      </if>
      <if test="distrForbid != null">
        distr_forbid,
      </if>
      <if test="applyForbid != null">
        apply_forbid,
      </if>
      <if test="valuable != null">
        valuable,
      </if>
      <if test="dtpGoods != null">
        dtp_goods,
      </if>
      <if test="coldchainind != null">
        coldchainind,
      </if>
      <if test="specialAttr != null">
        special_attr,
      </if>
      <if test="purchaseType != null">
        purchase_type,
      </if>
      <if test="goodsLevel != null">
        goods_level,
      </if>
      <if test="goodsStatus != null">
        goods_status,
      </if>
      <if test="lawful != null">
        lawful,
      </if>
      <if test="newable != null">
        newable,
      </if>
      <if test="categoryId != null">
        category_id,
      </if>
      <if test="middleCategoryId != null">
        middle_category_id,
      </if>
      <if test="smallCategoryId != null">
        small_category_id,
      </if>
      <if test="subCategoryId != null">
        sub_category_id,
      </if>
      <if test="pushLevel != null">
        push_level,
      </if>
      <if test="goodsLine != null">
        goods_line,
      </if>
      <if test="middlePackageQty != null">
        middle_package_qty,
      </if>
      <if test="middlePackageSwitchBiz != null">
        middle_package_switch_biz,
      </if>
      <if test="middlePackageSwitchStore != null">
        middle_package_switch_store,
      </if>
      <if test="applyRatio != null">
        apply_ratio,
      </if>
      <if test="specialThirtyDaysQty != null">
        special_thirty_days_qty,
      </if>
      <if test="stockUpperLimitDays != null">
        stock_upper_limit_days,
      </if>
      <if test="stockLowerLimitDays != null">
        stock_lower_limit_days,
      </if>
      <if test="averageDailySales != null">
        average_daily_sales,
      </if>
      <if test="minDisplayQty != null">
        min_display_qty,
      </if>
      <if test="stockUpperLimit != null">
        stock_upper_limit,
      </if>
      <if test="stockLowerLimit != null">
        stock_lower_limit,
      </if>
      <if test="thirtyDaysSales != null">
        thirty_days_sales,
      </if>
      <if test="sixtyDaysSales != null">
        sixty_days_sales,
      </if>
      <if test="ninetyDaysSales != null">
        ninety_days_sales,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="storeOrgId != null">
        #{storeOrgId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="storeCode != null">
        #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="storeAttr != null">
        #{storeAttr,jdbcType=TINYINT},
      </if>
      <if test="goodsNo != null">
        #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="specialCtrl != null">
        #{specialCtrl,jdbcType=TINYINT},
      </if>
      <if test="distrForbid != null">
        #{distrForbid,jdbcType=TINYINT},
      </if>
      <if test="applyForbid != null">
        #{applyForbid,jdbcType=TINYINT},
      </if>
      <if test="valuable != null">
        #{valuable,jdbcType=TINYINT},
      </if>
      <if test="dtpGoods != null">
        #{dtpGoods,jdbcType=TINYINT},
      </if>
      <if test="coldchainind != null">
        #{coldchainind,jdbcType=TINYINT},
      </if>
      <if test="specialAttr != null">
        #{specialAttr,jdbcType=VARCHAR},
      </if>
      <if test="purchaseType != null">
        #{purchaseType,jdbcType=VARCHAR},
      </if>
      <if test="goodsLevel != null">
        #{goodsLevel,jdbcType=TINYINT},
      </if>
      <if test="goodsStatus != null">
        #{goodsStatus,jdbcType=TINYINT},
      </if>
      <if test="lawful != null">
        #{lawful,jdbcType=TINYINT},
      </if>
      <if test="newable != null">
        #{newable,jdbcType=TINYINT},
      </if>
      <if test="categoryId != null">
        #{categoryId,jdbcType=BIGINT},
      </if>
      <if test="middleCategoryId != null">
        #{middleCategoryId,jdbcType=BIGINT},
      </if>
      <if test="smallCategoryId != null">
        #{smallCategoryId,jdbcType=BIGINT},
      </if>
      <if test="subCategoryId != null">
        #{subCategoryId,jdbcType=BIGINT},
      </if>
      <if test="pushLevel != null">
        #{pushLevel,jdbcType=VARCHAR},
      </if>
      <if test="goodsLine != null">
        #{goodsLine,jdbcType=TINYINT},
      </if>
      <if test="middlePackageQty != null">
        #{middlePackageQty,jdbcType=DECIMAL},
      </if>
      <if test="middlePackageSwitchBiz != null">
        #{middlePackageSwitchBiz,jdbcType=VARCHAR},
      </if>
      <if test="middlePackageSwitchStore != null">
        #{middlePackageSwitchStore,jdbcType=VARCHAR},
      </if>
      <if test="applyRatio != null">
        #{applyRatio,jdbcType=DECIMAL},
      </if>
      <if test="specialThirtyDaysQty != null">
        #{specialThirtyDaysQty,jdbcType=DECIMAL},
      </if>
      <if test="stockUpperLimitDays != null">
        #{stockUpperLimitDays,jdbcType=INTEGER},
      </if>
      <if test="stockLowerLimitDays != null">
        #{stockLowerLimitDays,jdbcType=INTEGER},
      </if>
      <if test="averageDailySales != null">
        #{averageDailySales,jdbcType=DECIMAL},
      </if>
      <if test="minDisplayQty != null">
        #{minDisplayQty,jdbcType=DECIMAL},
      </if>
      <if test="stockUpperLimit != null">
        #{stockUpperLimit,jdbcType=DECIMAL},
      </if>
      <if test="stockLowerLimit != null">
        #{stockLowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="thirtyDaysSales != null">
        #{thirtyDaysSales,jdbcType=DECIMAL},
      </if>
      <if test="sixtyDaysSales != null">
        #{sixtyDaysSales,jdbcType=DECIMAL},
      </if>
      <if test="ninetyDaysSales != null">
        #{ninetyDaysSales,jdbcType=DECIMAL},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.iscm.entity.BdpAvgDailySalesExample" resultType="java.lang.Long">
    select count(*) from bdp_avg_daily_sales
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update bdp_avg_daily_sales
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.companyCode != null">
        company_code = #{record.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.storeOrgId != null">
        store_org_id = #{record.storeOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.storeCode != null">
        store_code = #{record.storeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.storeAttr != null">
        store_attr = #{record.storeAttr,jdbcType=TINYINT},
      </if>
      <if test="record.goodsNo != null">
        goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="record.specialCtrl != null">
        special_ctrl = #{record.specialCtrl,jdbcType=TINYINT},
      </if>
      <if test="record.distrForbid != null">
        distr_forbid = #{record.distrForbid,jdbcType=TINYINT},
      </if>
      <if test="record.applyForbid != null">
        apply_forbid = #{record.applyForbid,jdbcType=TINYINT},
      </if>
      <if test="record.valuable != null">
        valuable = #{record.valuable,jdbcType=TINYINT},
      </if>
      <if test="record.dtpGoods != null">
        dtp_goods = #{record.dtpGoods,jdbcType=TINYINT},
      </if>
      <if test="record.coldchainind != null">
        coldchainind = #{record.coldchainind,jdbcType=TINYINT},
      </if>
      <if test="record.specialAttr != null">
        special_attr = #{record.specialAttr,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaseType != null">
        purchase_type = #{record.purchaseType,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsLevel != null">
        goods_level = #{record.goodsLevel,jdbcType=TINYINT},
      </if>
      <if test="record.goodsStatus != null">
        goods_status = #{record.goodsStatus,jdbcType=TINYINT},
      </if>
      <if test="record.lawful != null">
        lawful = #{record.lawful,jdbcType=TINYINT},
      </if>
      <if test="record.newable != null">
        newable = #{record.newable,jdbcType=TINYINT},
      </if>
      <if test="record.categoryId != null">
        category_id = #{record.categoryId,jdbcType=BIGINT},
      </if>
      <if test="record.middleCategoryId != null">
        middle_category_id = #{record.middleCategoryId,jdbcType=BIGINT},
      </if>
      <if test="record.smallCategoryId != null">
        small_category_id = #{record.smallCategoryId,jdbcType=BIGINT},
      </if>
      <if test="record.subCategoryId != null">
        sub_category_id = #{record.subCategoryId,jdbcType=BIGINT},
      </if>
      <if test="record.pushLevel != null">
        push_level = #{record.pushLevel,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsLine != null">
        goods_line = #{record.goodsLine,jdbcType=TINYINT},
      </if>
      <if test="record.middlePackageQty != null">
        middle_package_qty = #{record.middlePackageQty,jdbcType=DECIMAL},
      </if>
      <if test="record.middlePackageSwitchBiz != null">
        middle_package_switch_biz = #{record.middlePackageSwitchBiz,jdbcType=VARCHAR},
      </if>
      <if test="record.middlePackageSwitchStore != null">
        middle_package_switch_store = #{record.middlePackageSwitchStore,jdbcType=VARCHAR},
      </if>
      <if test="record.applyRatio != null">
        apply_ratio = #{record.applyRatio,jdbcType=DECIMAL},
      </if>
      <if test="record.specialThirtyDaysQty != null">
        special_thirty_days_qty = #{record.specialThirtyDaysQty,jdbcType=DECIMAL},
      </if>
      <if test="record.stockUpperLimitDays != null">
        stock_upper_limit_days = #{record.stockUpperLimitDays,jdbcType=INTEGER},
      </if>
      <if test="record.stockLowerLimitDays != null">
        stock_lower_limit_days = #{record.stockLowerLimitDays,jdbcType=INTEGER},
      </if>
      <if test="record.averageDailySales != null">
        average_daily_sales = #{record.averageDailySales,jdbcType=DECIMAL},
      </if>
      <if test="record.minDisplayQty != null">
        min_display_qty = #{record.minDisplayQty,jdbcType=DECIMAL},
      </if>
      <if test="record.stockUpperLimit != null">
        stock_upper_limit = #{record.stockUpperLimit,jdbcType=DECIMAL},
      </if>
      <if test="record.stockLowerLimit != null">
        stock_lower_limit = #{record.stockLowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="record.thirtyDaysSales != null">
        thirty_days_sales = #{record.thirtyDaysSales,jdbcType=DECIMAL},
      </if>
      <if test="record.sixtyDaysSales != null">
        sixty_days_sales = #{record.sixtyDaysSales,jdbcType=DECIMAL},
      </if>
      <if test="record.ninetyDaysSales != null">
        ninety_days_sales = #{record.ninetyDaysSales,jdbcType=DECIMAL},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update bdp_avg_daily_sales
    set id = #{record.id,jdbcType=BIGINT},
      company_code = #{record.companyCode,jdbcType=VARCHAR},
      store_org_id = #{record.storeOrgId,jdbcType=BIGINT},
      store_id = #{record.storeId,jdbcType=BIGINT},
      store_code = #{record.storeCode,jdbcType=VARCHAR},
      store_attr = #{record.storeAttr,jdbcType=TINYINT},
      goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      special_ctrl = #{record.specialCtrl,jdbcType=TINYINT},
      distr_forbid = #{record.distrForbid,jdbcType=TINYINT},
      apply_forbid = #{record.applyForbid,jdbcType=TINYINT},
      valuable = #{record.valuable,jdbcType=TINYINT},
      dtp_goods = #{record.dtpGoods,jdbcType=TINYINT},
      coldchainind = #{record.coldchainind,jdbcType=TINYINT},
      special_attr = #{record.specialAttr,jdbcType=VARCHAR},
      purchase_type = #{record.purchaseType,jdbcType=VARCHAR},
      goods_level = #{record.goodsLevel,jdbcType=TINYINT},
      goods_status = #{record.goodsStatus,jdbcType=TINYINT},
      lawful = #{record.lawful,jdbcType=TINYINT},
      newable = #{record.newable,jdbcType=TINYINT},
      category_id = #{record.categoryId,jdbcType=BIGINT},
      middle_category_id = #{record.middleCategoryId,jdbcType=BIGINT},
      small_category_id = #{record.smallCategoryId,jdbcType=BIGINT},
      sub_category_id = #{record.subCategoryId,jdbcType=BIGINT},
      push_level = #{record.pushLevel,jdbcType=VARCHAR},
      goods_line = #{record.goodsLine,jdbcType=TINYINT},
      middle_package_qty = #{record.middlePackageQty,jdbcType=DECIMAL},
      middle_package_switch_biz = #{record.middlePackageSwitchBiz,jdbcType=VARCHAR},
      middle_package_switch_store = #{record.middlePackageSwitchStore,jdbcType=VARCHAR},
      apply_ratio = #{record.applyRatio,jdbcType=DECIMAL},
      special_thirty_days_qty = #{record.specialThirtyDaysQty,jdbcType=DECIMAL},
      stock_upper_limit_days = #{record.stockUpperLimitDays,jdbcType=INTEGER},
      stock_lower_limit_days = #{record.stockLowerLimitDays,jdbcType=INTEGER},
      average_daily_sales = #{record.averageDailySales,jdbcType=DECIMAL},
      min_display_qty = #{record.minDisplayQty,jdbcType=DECIMAL},
      stock_upper_limit = #{record.stockUpperLimit,jdbcType=DECIMAL},
      stock_lower_limit = #{record.stockLowerLimit,jdbcType=DECIMAL},
      thirty_days_sales = #{record.thirtyDaysSales,jdbcType=DECIMAL},
      sixty_days_sales = #{record.sixtyDaysSales,jdbcType=DECIMAL},
      ninety_days_sales = #{record.ninetyDaysSales,jdbcType=DECIMAL},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.iscm.entity.BdpAvgDailySales">
    update bdp_avg_daily_sales
    <set>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="storeOrgId != null">
        store_org_id = #{storeOrgId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="storeCode != null">
        store_code = #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="storeAttr != null">
        store_attr = #{storeAttr,jdbcType=TINYINT},
      </if>
      <if test="goodsNo != null">
        goods_no = #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="specialCtrl != null">
        special_ctrl = #{specialCtrl,jdbcType=TINYINT},
      </if>
      <if test="distrForbid != null">
        distr_forbid = #{distrForbid,jdbcType=TINYINT},
      </if>
      <if test="applyForbid != null">
        apply_forbid = #{applyForbid,jdbcType=TINYINT},
      </if>
      <if test="valuable != null">
        valuable = #{valuable,jdbcType=TINYINT},
      </if>
      <if test="dtpGoods != null">
        dtp_goods = #{dtpGoods,jdbcType=TINYINT},
      </if>
      <if test="coldchainind != null">
        coldchainind = #{coldchainind,jdbcType=TINYINT},
      </if>
      <if test="specialAttr != null">
        special_attr = #{specialAttr,jdbcType=VARCHAR},
      </if>
      <if test="purchaseType != null">
        purchase_type = #{purchaseType,jdbcType=VARCHAR},
      </if>
      <if test="goodsLevel != null">
        goods_level = #{goodsLevel,jdbcType=TINYINT},
      </if>
      <if test="goodsStatus != null">
        goods_status = #{goodsStatus,jdbcType=TINYINT},
      </if>
      <if test="lawful != null">
        lawful = #{lawful,jdbcType=TINYINT},
      </if>
      <if test="newable != null">
        newable = #{newable,jdbcType=TINYINT},
      </if>
      <if test="categoryId != null">
        category_id = #{categoryId,jdbcType=BIGINT},
      </if>
      <if test="middleCategoryId != null">
        middle_category_id = #{middleCategoryId,jdbcType=BIGINT},
      </if>
      <if test="smallCategoryId != null">
        small_category_id = #{smallCategoryId,jdbcType=BIGINT},
      </if>
      <if test="subCategoryId != null">
        sub_category_id = #{subCategoryId,jdbcType=BIGINT},
      </if>
      <if test="pushLevel != null">
        push_level = #{pushLevel,jdbcType=VARCHAR},
      </if>
      <if test="goodsLine != null">
        goods_line = #{goodsLine,jdbcType=TINYINT},
      </if>
      <if test="middlePackageQty != null">
        middle_package_qty = #{middlePackageQty,jdbcType=DECIMAL},
      </if>
      <if test="middlePackageSwitchBiz != null">
        middle_package_switch_biz = #{middlePackageSwitchBiz,jdbcType=VARCHAR},
      </if>
      <if test="middlePackageSwitchStore != null">
        middle_package_switch_store = #{middlePackageSwitchStore,jdbcType=VARCHAR},
      </if>
      <if test="applyRatio != null">
        apply_ratio = #{applyRatio,jdbcType=DECIMAL},
      </if>
      <if test="specialThirtyDaysQty != null">
        special_thirty_days_qty = #{specialThirtyDaysQty,jdbcType=DECIMAL},
      </if>
      <if test="stockUpperLimitDays != null">
        stock_upper_limit_days = #{stockUpperLimitDays,jdbcType=INTEGER},
      </if>
      <if test="stockLowerLimitDays != null">
        stock_lower_limit_days = #{stockLowerLimitDays,jdbcType=INTEGER},
      </if>
      <if test="averageDailySales != null">
        average_daily_sales = #{averageDailySales,jdbcType=DECIMAL},
      </if>
      <if test="minDisplayQty != null">
        min_display_qty = #{minDisplayQty,jdbcType=DECIMAL},
      </if>
      <if test="stockUpperLimit != null">
        stock_upper_limit = #{stockUpperLimit,jdbcType=DECIMAL},
      </if>
      <if test="stockLowerLimit != null">
        stock_lower_limit = #{stockLowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="thirtyDaysSales != null">
        thirty_days_sales = #{thirtyDaysSales,jdbcType=DECIMAL},
      </if>
      <if test="sixtyDaysSales != null">
        sixty_days_sales = #{sixtyDaysSales,jdbcType=DECIMAL},
      </if>
      <if test="ninetyDaysSales != null">
        ninety_days_sales = #{ninetyDaysSales,jdbcType=DECIMAL},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.iscm.entity.BdpAvgDailySales">
    update bdp_avg_daily_sales
    set company_code = #{companyCode,jdbcType=VARCHAR},
      store_org_id = #{storeOrgId,jdbcType=BIGINT},
      store_id = #{storeId,jdbcType=BIGINT},
      store_code = #{storeCode,jdbcType=VARCHAR},
      store_attr = #{storeAttr,jdbcType=TINYINT},
      goods_no = #{goodsNo,jdbcType=VARCHAR},
      special_ctrl = #{specialCtrl,jdbcType=TINYINT},
      distr_forbid = #{distrForbid,jdbcType=TINYINT},
      apply_forbid = #{applyForbid,jdbcType=TINYINT},
      valuable = #{valuable,jdbcType=TINYINT},
      dtp_goods = #{dtpGoods,jdbcType=TINYINT},
      coldchainind = #{coldchainind,jdbcType=TINYINT},
      special_attr = #{specialAttr,jdbcType=VARCHAR},
      purchase_type = #{purchaseType,jdbcType=VARCHAR},
      goods_level = #{goodsLevel,jdbcType=TINYINT},
      goods_status = #{goodsStatus,jdbcType=TINYINT},
      lawful = #{lawful,jdbcType=TINYINT},
      newable = #{newable,jdbcType=TINYINT},
      category_id = #{categoryId,jdbcType=BIGINT},
      middle_category_id = #{middleCategoryId,jdbcType=BIGINT},
      small_category_id = #{smallCategoryId,jdbcType=BIGINT},
      sub_category_id = #{subCategoryId,jdbcType=BIGINT},
      push_level = #{pushLevel,jdbcType=VARCHAR},
      goods_line = #{goodsLine,jdbcType=TINYINT},
      middle_package_qty = #{middlePackageQty,jdbcType=DECIMAL},
      middle_package_switch_biz = #{middlePackageSwitchBiz,jdbcType=VARCHAR},
      middle_package_switch_store = #{middlePackageSwitchStore,jdbcType=VARCHAR},
      apply_ratio = #{applyRatio,jdbcType=DECIMAL},
      special_thirty_days_qty = #{specialThirtyDaysQty,jdbcType=DECIMAL},
      stock_upper_limit_days = #{stockUpperLimitDays,jdbcType=INTEGER},
      stock_lower_limit_days = #{stockLowerLimitDays,jdbcType=INTEGER},
      average_daily_sales = #{averageDailySales,jdbcType=DECIMAL},
      min_display_qty = #{minDisplayQty,jdbcType=DECIMAL},
      stock_upper_limit = #{stockUpperLimit,jdbcType=DECIMAL},
      stock_lower_limit = #{stockLowerLimit,jdbcType=DECIMAL},
      thirty_days_sales = #{thirtyDaysSales,jdbcType=DECIMAL},
      sixty_days_sales = #{sixtyDaysSales,jdbcType=DECIMAL},
      ninety_days_sales = #{ninetyDaysSales,jdbcType=DECIMAL},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>