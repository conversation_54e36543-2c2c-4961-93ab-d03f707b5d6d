<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.IscmParamBusinessComidMappingMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.IscmParamBusinessComidMapping">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="business_id" jdbcType="BIGINT" property="businessId" />
    <result column="business_org_id" jdbcType="BIGINT" property="businessOrgId" />
    <result column="business_name" jdbcType="VARCHAR" property="businessName" />
    <result column="sap_code" jdbcType="VARCHAR" property="sapCode" />
    <result column="comids" jdbcType="VARCHAR" property="comids" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="BIGINT" property="version" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, business_id, business_org_id, business_name, sap_code, comids, `status`, extend, 
    version
  </sql>
  <select id="selectByExample" parameterType="com.cowell.iscm.entity.IscmParamBusinessComidMappingExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from iscm_param_business_comid_mapping
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from iscm_param_business_comid_mapping
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from iscm_param_business_comid_mapping
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.iscm.entity.IscmParamBusinessComidMappingExample">
    delete from iscm_param_business_comid_mapping
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.iscm.entity.IscmParamBusinessComidMapping">
    insert into iscm_param_business_comid_mapping (id, business_id, business_org_id, 
      business_name, sap_code, comids, 
      `status`, extend, version
      )
    values (#{id,jdbcType=BIGINT}, #{businessId,jdbcType=BIGINT}, #{businessOrgId,jdbcType=BIGINT}, 
      #{businessName,jdbcType=VARCHAR}, #{sapCode,jdbcType=VARCHAR}, #{comids,jdbcType=VARCHAR}, 
      #{status,jdbcType=TINYINT}, #{extend,jdbcType=VARCHAR}, #{version,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.iscm.entity.IscmParamBusinessComidMapping">
    insert into iscm_param_business_comid_mapping
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="businessId != null">
        business_id,
      </if>
      <if test="businessOrgId != null">
        business_org_id,
      </if>
      <if test="businessName != null">
        business_name,
      </if>
      <if test="sapCode != null">
        sap_code,
      </if>
      <if test="comids != null">
        comids,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="businessId != null">
        #{businessId,jdbcType=BIGINT},
      </if>
      <if test="businessOrgId != null">
        #{businessOrgId,jdbcType=BIGINT},
      </if>
      <if test="businessName != null">
        #{businessName,jdbcType=VARCHAR},
      </if>
      <if test="sapCode != null">
        #{sapCode,jdbcType=VARCHAR},
      </if>
      <if test="comids != null">
        #{comids,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.iscm.entity.IscmParamBusinessComidMappingExample" resultType="java.lang.Long">
    select count(*) from iscm_param_business_comid_mapping
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update iscm_param_business_comid_mapping
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.businessId != null">
        business_id = #{record.businessId,jdbcType=BIGINT},
      </if>
      <if test="record.businessOrgId != null">
        business_org_id = #{record.businessOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.businessName != null">
        business_name = #{record.businessName,jdbcType=VARCHAR},
      </if>
      <if test="record.sapCode != null">
        sap_code = #{record.sapCode,jdbcType=VARCHAR},
      </if>
      <if test="record.comids != null">
        comids = #{record.comids,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update iscm_param_business_comid_mapping
    set id = #{record.id,jdbcType=BIGINT},
      business_id = #{record.businessId,jdbcType=BIGINT},
      business_org_id = #{record.businessOrgId,jdbcType=BIGINT},
      business_name = #{record.businessName,jdbcType=VARCHAR},
      sap_code = #{record.sapCode,jdbcType=VARCHAR},
      comids = #{record.comids,jdbcType=VARCHAR},
      `status` = #{record.status,jdbcType=TINYINT},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.iscm.entity.IscmParamBusinessComidMapping">
    update iscm_param_business_comid_mapping
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=BIGINT},
      </if>
      <if test="businessOrgId != null">
        business_org_id = #{businessOrgId,jdbcType=BIGINT},
      </if>
      <if test="businessName != null">
        business_name = #{businessName,jdbcType=VARCHAR},
      </if>
      <if test="sapCode != null">
        sap_code = #{sapCode,jdbcType=VARCHAR},
      </if>
      <if test="comids != null">
        comids = #{comids,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.iscm.entity.IscmParamBusinessComidMapping">
    update iscm_param_business_comid_mapping
    set business_id = #{businessId,jdbcType=BIGINT},
      business_org_id = #{businessOrgId,jdbcType=BIGINT},
      business_name = #{businessName,jdbcType=VARCHAR},
      sap_code = #{sapCode,jdbcType=VARCHAR},
      comids = #{comids,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=TINYINT},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>