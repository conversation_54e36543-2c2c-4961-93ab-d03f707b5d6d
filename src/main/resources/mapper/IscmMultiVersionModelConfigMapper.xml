<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.IscmMultiVersionModelConfigMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.IscmMultiVersionModelConfig">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="model_code" jdbcType="VARCHAR" property="modelCode" />
    <result column="model_name" jdbcType="VARCHAR" property="modelName" />
    <result column="model_desc" jdbcType="VARCHAR" property="modelDesc" />
    <result column="business_scope" jdbcType="TINYINT" property="businessScope" />
    <result column="bdp_task" jdbcType="VARCHAR" property="bdpTask" />
    <result column="model_status" jdbcType="TINYINT" property="modelStatus" />
    <result column="effect_date" jdbcType="TIMESTAMP" property="effectDate" />
    <result column="invalid_date" jdbcType="TIMESTAMP" property="invalidDate" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, model_code, model_name, model_desc, business_scope, bdp_task, model_status, effect_date, 
    invalid_date, status, extend, version, created_by, created_name, updated_by, updated_name, 
    gmt_create, gmt_update
  </sql>
  <select id="selectByExample" parameterType="com.cowell.iscm.entity.IscmMultiVersionModelConfigExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from iscm_multi_version_model_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from iscm_multi_version_model_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from iscm_multi_version_model_config
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.iscm.entity.IscmMultiVersionModelConfigExample">
    delete from iscm_multi_version_model_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.iscm.entity.IscmMultiVersionModelConfig">
    insert into iscm_multi_version_model_config (id, model_code, model_name, 
      model_desc, business_scope, bdp_task, 
      model_status, effect_date, invalid_date, 
      status, extend, version, 
      created_by, created_name, updated_by, 
      updated_name, gmt_create, gmt_update
      )
    values (#{id,jdbcType=BIGINT}, #{modelCode,jdbcType=VARCHAR}, #{modelName,jdbcType=VARCHAR}, 
      #{modelDesc,jdbcType=VARCHAR}, #{businessScope,jdbcType=TINYINT}, #{bdpTask,jdbcType=VARCHAR}, 
      #{modelStatus,jdbcType=TINYINT}, #{effectDate,jdbcType=TIMESTAMP}, #{invalidDate,jdbcType=TIMESTAMP}, 
      #{status,jdbcType=TINYINT}, #{extend,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER}, 
      #{createdBy,jdbcType=BIGINT}, #{createdName,jdbcType=VARCHAR}, #{updatedBy,jdbcType=BIGINT}, 
      #{updatedName,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.iscm.entity.IscmMultiVersionModelConfig">
    insert into iscm_multi_version_model_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="modelCode != null">
        model_code,
      </if>
      <if test="modelName != null">
        model_name,
      </if>
      <if test="modelDesc != null">
        model_desc,
      </if>
      <if test="businessScope != null">
        business_scope,
      </if>
      <if test="bdpTask != null">
        bdp_task,
      </if>
      <if test="modelStatus != null">
        model_status,
      </if>
      <if test="effectDate != null">
        effect_date,
      </if>
      <if test="invalidDate != null">
        invalid_date,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedName != null">
        updated_name,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="modelCode != null">
        #{modelCode,jdbcType=VARCHAR},
      </if>
      <if test="modelName != null">
        #{modelName,jdbcType=VARCHAR},
      </if>
      <if test="modelDesc != null">
        #{modelDesc,jdbcType=VARCHAR},
      </if>
      <if test="businessScope != null">
        #{businessScope,jdbcType=TINYINT},
      </if>
      <if test="bdpTask != null">
        #{bdpTask,jdbcType=VARCHAR},
      </if>
      <if test="modelStatus != null">
        #{modelStatus,jdbcType=TINYINT},
      </if>
      <if test="effectDate != null">
        #{effectDate,jdbcType=TIMESTAMP},
      </if>
      <if test="invalidDate != null">
        #{invalidDate,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        #{updatedName,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.iscm.entity.IscmMultiVersionModelConfigExample" resultType="java.lang.Long">
    select count(*) from iscm_multi_version_model_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update iscm_multi_version_model_config
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.modelCode != null">
        model_code = #{record.modelCode,jdbcType=VARCHAR},
      </if>
      <if test="record.modelName != null">
        model_name = #{record.modelName,jdbcType=VARCHAR},
      </if>
      <if test="record.modelDesc != null">
        model_desc = #{record.modelDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.businessScope != null">
        business_scope = #{record.businessScope,jdbcType=TINYINT},
      </if>
      <if test="record.bdpTask != null">
        bdp_task = #{record.bdpTask,jdbcType=VARCHAR},
      </if>
      <if test="record.modelStatus != null">
        model_status = #{record.modelStatus,jdbcType=TINYINT},
      </if>
      <if test="record.effectDate != null">
        effect_date = #{record.effectDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.invalidDate != null">
        invalid_date = #{record.invalidDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedName != null">
        updated_name = #{record.updatedName,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update iscm_multi_version_model_config
    set id = #{record.id,jdbcType=BIGINT},
      model_code = #{record.modelCode,jdbcType=VARCHAR},
      model_name = #{record.modelName,jdbcType=VARCHAR},
      model_desc = #{record.modelDesc,jdbcType=VARCHAR},
      business_scope = #{record.businessScope,jdbcType=TINYINT},
      bdp_task = #{record.bdpTask,jdbcType=VARCHAR},
      model_status = #{record.modelStatus,jdbcType=TINYINT},
      effect_date = #{record.effectDate,jdbcType=TIMESTAMP},
      invalid_date = #{record.invalidDate,jdbcType=TIMESTAMP},
      status = #{record.status,jdbcType=TINYINT},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_name = #{record.createdName,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_name = #{record.updatedName,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.iscm.entity.IscmMultiVersionModelConfig">
    update iscm_multi_version_model_config
    <set>
      <if test="modelCode != null">
        model_code = #{modelCode,jdbcType=VARCHAR},
      </if>
      <if test="modelName != null">
        model_name = #{modelName,jdbcType=VARCHAR},
      </if>
      <if test="modelDesc != null">
        model_desc = #{modelDesc,jdbcType=VARCHAR},
      </if>
      <if test="businessScope != null">
        business_scope = #{businessScope,jdbcType=TINYINT},
      </if>
      <if test="bdpTask != null">
        bdp_task = #{bdpTask,jdbcType=VARCHAR},
      </if>
      <if test="modelStatus != null">
        model_status = #{modelStatus,jdbcType=TINYINT},
      </if>
      <if test="effectDate != null">
        effect_date = #{effectDate,jdbcType=TIMESTAMP},
      </if>
      <if test="invalidDate != null">
        invalid_date = #{invalidDate,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        updated_name = #{updatedName,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.iscm.entity.IscmMultiVersionModelConfig">
    update iscm_multi_version_model_config
    set model_code = #{modelCode,jdbcType=VARCHAR},
      model_name = #{modelName,jdbcType=VARCHAR},
      model_desc = #{modelDesc,jdbcType=VARCHAR},
      business_scope = #{businessScope,jdbcType=TINYINT},
      bdp_task = #{bdpTask,jdbcType=VARCHAR},
      model_status = #{modelStatus,jdbcType=TINYINT},
      effect_date = #{effectDate,jdbcType=TIMESTAMP},
      invalid_date = #{invalidDate,jdbcType=TIMESTAMP},
      status = #{status,jdbcType=TINYINT},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_name = #{createdName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_name = #{updatedName,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>