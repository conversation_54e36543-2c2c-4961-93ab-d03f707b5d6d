<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.BdpGoodsGiftDomainDetailMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.BdpGoodsGiftDomainDetail">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="domain_id" jdbcType="BIGINT" property="domainId" />
    <result column="gift_type" jdbcType="TINYINT" property="giftType" />
    <result column="gift_goods_no" jdbcType="VARCHAR" property="giftGoodsNo" />
    <result column="gift_name" jdbcType="VARCHAR" property="giftName" />
    <result column="gift_manufacturer" jdbcType="VARCHAR" property="giftManufacturer" />
    <result column="gift_specifications" jdbcType="VARCHAR" property="giftSpecifications" />
    <result column="gift_remark" jdbcType="VARCHAR" property="giftRemark" />
    <result column="gift_quantity" jdbcType="DECIMAL" property="giftQuantity" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, domain_id, gift_type, gift_goods_no, gift_name, gift_manufacturer, gift_specifications, 
    gift_remark, gift_quantity, `status`, gmt_create, gmt_update, extend, version, created_by, 
    created_name, updated_by, updated_name
  </sql>
  <select id="selectByExample" parameterType="com.cowell.iscm.entity.BdpGoodsGiftDomainDetailExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from bdp_goods_gift_domain_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from bdp_goods_gift_domain_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from bdp_goods_gift_domain_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.iscm.entity.BdpGoodsGiftDomainDetailExample">
    delete from bdp_goods_gift_domain_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entity.BdpGoodsGiftDomainDetail" useGeneratedKeys="true">
    insert into bdp_goods_gift_domain_detail (domain_id, gift_type, gift_goods_no, 
      gift_name, gift_manufacturer, gift_specifications, 
      gift_remark, gift_quantity, `status`, 
      gmt_create, gmt_update, extend, 
      version, created_by, created_name, 
      updated_by, updated_name)
    values (#{domainId,jdbcType=BIGINT}, #{giftType,jdbcType=TINYINT}, #{giftGoodsNo,jdbcType=VARCHAR}, 
      #{giftName,jdbcType=VARCHAR}, #{giftManufacturer,jdbcType=VARCHAR}, #{giftSpecifications,jdbcType=VARCHAR}, 
      #{giftRemark,jdbcType=VARCHAR}, #{giftQuantity,jdbcType=DECIMAL}, #{status,jdbcType=TINYINT}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}, #{extend,jdbcType=VARCHAR}, 
      #{version,jdbcType=INTEGER}, #{createdBy,jdbcType=BIGINT}, #{createdName,jdbcType=VARCHAR}, 
      #{updatedBy,jdbcType=BIGINT}, #{updatedName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entity.BdpGoodsGiftDomainDetail" useGeneratedKeys="true">
    insert into bdp_goods_gift_domain_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="domainId != null">
        domain_id,
      </if>
      <if test="giftType != null">
        gift_type,
      </if>
      <if test="giftGoodsNo != null">
        gift_goods_no,
      </if>
      <if test="giftName != null">
        gift_name,
      </if>
      <if test="giftManufacturer != null">
        gift_manufacturer,
      </if>
      <if test="giftSpecifications != null">
        gift_specifications,
      </if>
      <if test="giftRemark != null">
        gift_remark,
      </if>
      <if test="giftQuantity != null">
        gift_quantity,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedName != null">
        updated_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="domainId != null">
        #{domainId,jdbcType=BIGINT},
      </if>
      <if test="giftType != null">
        #{giftType,jdbcType=TINYINT},
      </if>
      <if test="giftGoodsNo != null">
        #{giftGoodsNo,jdbcType=VARCHAR},
      </if>
      <if test="giftName != null">
        #{giftName,jdbcType=VARCHAR},
      </if>
      <if test="giftManufacturer != null">
        #{giftManufacturer,jdbcType=VARCHAR},
      </if>
      <if test="giftSpecifications != null">
        #{giftSpecifications,jdbcType=VARCHAR},
      </if>
      <if test="giftRemark != null">
        #{giftRemark,jdbcType=VARCHAR},
      </if>
      <if test="giftQuantity != null">
        #{giftQuantity,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        #{updatedName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.iscm.entity.BdpGoodsGiftDomainDetailExample" resultType="java.lang.Long">
    select count(*) from bdp_goods_gift_domain_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update bdp_goods_gift_domain_detail
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.domainId != null">
        domain_id = #{record.domainId,jdbcType=BIGINT},
      </if>
      <if test="record.giftType != null">
        gift_type = #{record.giftType,jdbcType=TINYINT},
      </if>
      <if test="record.giftGoodsNo != null">
        gift_goods_no = #{record.giftGoodsNo,jdbcType=VARCHAR},
      </if>
      <if test="record.giftName != null">
        gift_name = #{record.giftName,jdbcType=VARCHAR},
      </if>
      <if test="record.giftManufacturer != null">
        gift_manufacturer = #{record.giftManufacturer,jdbcType=VARCHAR},
      </if>
      <if test="record.giftSpecifications != null">
        gift_specifications = #{record.giftSpecifications,jdbcType=VARCHAR},
      </if>
      <if test="record.giftRemark != null">
        gift_remark = #{record.giftRemark,jdbcType=VARCHAR},
      </if>
      <if test="record.giftQuantity != null">
        gift_quantity = #{record.giftQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedName != null">
        updated_name = #{record.updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update bdp_goods_gift_domain_detail
    set id = #{record.id,jdbcType=BIGINT},
      domain_id = #{record.domainId,jdbcType=BIGINT},
      gift_type = #{record.giftType,jdbcType=TINYINT},
      gift_goods_no = #{record.giftGoodsNo,jdbcType=VARCHAR},
      gift_name = #{record.giftName,jdbcType=VARCHAR},
      gift_manufacturer = #{record.giftManufacturer,jdbcType=VARCHAR},
      gift_specifications = #{record.giftSpecifications,jdbcType=VARCHAR},
      gift_remark = #{record.giftRemark,jdbcType=VARCHAR},
      gift_quantity = #{record.giftQuantity,jdbcType=DECIMAL},
      `status` = #{record.status,jdbcType=TINYINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_name = #{record.createdName,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_name = #{record.updatedName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.iscm.entity.BdpGoodsGiftDomainDetail">
    update bdp_goods_gift_domain_detail
    <set>
      <if test="domainId != null">
        domain_id = #{domainId,jdbcType=BIGINT},
      </if>
      <if test="giftType != null">
        gift_type = #{giftType,jdbcType=TINYINT},
      </if>
      <if test="giftGoodsNo != null">
        gift_goods_no = #{giftGoodsNo,jdbcType=VARCHAR},
      </if>
      <if test="giftName != null">
        gift_name = #{giftName,jdbcType=VARCHAR},
      </if>
      <if test="giftManufacturer != null">
        gift_manufacturer = #{giftManufacturer,jdbcType=VARCHAR},
      </if>
      <if test="giftSpecifications != null">
        gift_specifications = #{giftSpecifications,jdbcType=VARCHAR},
      </if>
      <if test="giftRemark != null">
        gift_remark = #{giftRemark,jdbcType=VARCHAR},
      </if>
      <if test="giftQuantity != null">
        gift_quantity = #{giftQuantity,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        updated_name = #{updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.iscm.entity.BdpGoodsGiftDomainDetail">
    update bdp_goods_gift_domain_detail
    set domain_id = #{domainId,jdbcType=BIGINT},
      gift_type = #{giftType,jdbcType=TINYINT},
      gift_goods_no = #{giftGoodsNo,jdbcType=VARCHAR},
      gift_name = #{giftName,jdbcType=VARCHAR},
      gift_manufacturer = #{giftManufacturer,jdbcType=VARCHAR},
      gift_specifications = #{giftSpecifications,jdbcType=VARCHAR},
      gift_remark = #{giftRemark,jdbcType=VARCHAR},
      gift_quantity = #{giftQuantity,jdbcType=DECIMAL},
      `status` = #{status,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_name = #{createdName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_name = #{updatedName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>