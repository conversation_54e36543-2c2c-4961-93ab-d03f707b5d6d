<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.IscmStoreReturnExecuteOrderDetailBatchMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.IscmStoreReturnExecuteOrderDetailBatch">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="return_order_no" jdbcType="VARCHAR" property="returnOrderNo" />
    <result column="platform_org_id" jdbcType="BIGINT" property="platformOrgId" />
    <result column="platform_org_name" jdbcType="VARCHAR" property="platformOrgName" />
    <result column="company_org_id" jdbcType="BIGINT" property="companyOrgId" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="store_org_id" jdbcType="BIGINT" property="storeOrgId" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="store_name" jdbcType="VARCHAR" property="storeName" />
    <result column="warehouse_code" jdbcType="VARCHAR" property="warehouseCode" />
    <result column="warehouse_name" jdbcType="VARCHAR" property="warehouseName" />
    <result column="register_order_no" jdbcType="VARCHAR" property="registerOrderNo" />
    <result column="register_quantity" jdbcType="DECIMAL" property="registerQuantity" />
    <result column="cost_amount" jdbcType="DECIMAL" property="costAmount" />
    <result column="data_type" jdbcType="TINYINT" property="dataType" />
    <result column="register_date" jdbcType="DATE" property="registerDate" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="forecast_sales" jdbcType="DECIMAL" property="forecastSales" />
    <result column="bar_code" jdbcType="VARCHAR" property="barCode" />
    <result column="goods_common_name" jdbcType="VARCHAR" property="goodsCommonName" />
    <result column="goods_unit" jdbcType="VARCHAR" property="goodsUnit" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="specifications" jdbcType="VARCHAR" property="specifications" />
    <result column="dosage_form" jdbcType="VARCHAR" property="dosageForm" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="approval_number" jdbcType="VARCHAR" property="approvalNumber" />
    <result column="goods_class_id" jdbcType="BIGINT" property="goodsClassId" />
    <result column="goods_class_name" jdbcType="VARCHAR" property="goodsClassName" />
    <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
    <result column="validity_date" jdbcType="TIMESTAMP" property="validityDate" />
    <result column="produce_date" jdbcType="TIMESTAMP" property="produceDate" />
    <result column="validity_days" jdbcType="INTEGER" property="validityDays" />
    <result column="stock_upper_limit_days" jdbcType="INTEGER" property="stockUpperLimitDays" />
    <result column="stock_lower_limit_days" jdbcType="INTEGER" property="stockLowerLimitDays" />
    <result column="hd_synthesize_average_daily_sales" jdbcType="DECIMAL" property="hdSynthesizeAverageDailySales" />
    <result column="stock_upper_limit" jdbcType="DECIMAL" property="stockUpperLimit" />
    <result column="stock_lower_limit" jdbcType="DECIMAL" property="stockLowerLimit" />
    <result column="bdp_synthesize_average_daily_sales" jdbcType="DECIMAL" property="bdpSynthesizeAverageDailySales" />
    <result column="storage_days" jdbcType="INTEGER" property="storageDays" />
    <result column="non_sales_days" jdbcType="INTEGER" property="nonSalesDays" />
    <result column="should_return_quantity" jdbcType="DECIMAL" property="shouldReturnQuantity" />
    <result column="return_quantity" jdbcType="DECIMAL" property="returnQuantity" />
    <result column="goods_stock" jdbcType="DECIMAL" property="goodsStock" />
    <result column="batch_stock" jdbcType="DECIMAL" property="batchStock" />
    <result column="thirty_sales_quantity" jdbcType="DECIMAL" property="thirtySalesQuantity" />
    <result column="thirty_sales_count" jdbcType="INTEGER" property="thirtySalesCount" />
    <result column="min_display_quantity" jdbcType="DECIMAL" property="minDisplayQuantity" />
    <result column="goodsline" jdbcType="VARCHAR" property="goodsline" />
    <result column="pushlevel" jdbcType="VARCHAR" property="pushlevel" />
    <result column="forbid_distribute" jdbcType="VARCHAR" property="forbidDistribute" />
    <result column="forbid_return_warehouse" jdbcType="VARCHAR" property="forbidReturnWarehouse" />
    <result column="forbid_apply" jdbcType="VARCHAR" property="forbidApply" />
    <result column="forbid_allot" jdbcType="VARCHAR" property="forbidAllot" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
    <result column="issue_by" jdbcType="BIGINT" property="issueBy" />
    <result column="issue_name" jdbcType="VARCHAR" property="issueName" />
    <result column="gmt_issue" jdbcType="TIMESTAMP" property="gmtIssue" />
    <result column="issue_return_quantity" jdbcType="DECIMAL" property="issueReturnQuantity" />
    <result column="issue_return_amount" jdbcType="DECIMAL" property="issueReturnAmount" />
    <result column="real_return_quantity" jdbcType="DECIMAL" property="realReturnQuantity" />
    <result column="pos_return_order_no" jdbcType="VARCHAR" property="posReturnOrderNo" />
    <result column="row_no" jdbcType="VARCHAR" property="rowNo" />
    <result column="sap_batch_code" jdbcType="VARCHAR" property="sapBatchCode" />
    <result column="process_status" jdbcType="TINYINT" property="processStatus" />
    <result column="created_month" jdbcType="INTEGER" property="createdMonth" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, return_order_no, platform_org_id, platform_org_name, company_org_id, company_code, 
    company_name, store_org_id, store_code, store_name, warehouse_code, warehouse_name, 
    register_order_no, register_quantity, cost_amount, data_type, register_date, goods_no, 
    goods_name, forecast_sales, bar_code, goods_common_name, goods_unit, description, 
    specifications, dosage_form, manufacturer, approval_number, goods_class_id, goods_class_name, 
    batch_no, validity_date, produce_date, validity_days, stock_upper_limit_days, stock_lower_limit_days, 
    hd_synthesize_average_daily_sales, stock_upper_limit, stock_lower_limit, bdp_synthesize_average_daily_sales, 
    storage_days, non_sales_days, should_return_quantity, return_quantity, goods_stock, 
    batch_stock, thirty_sales_quantity, thirty_sales_count, min_display_quantity, goodsline, 
    pushlevel, forbid_distribute, forbid_return_warehouse, forbid_apply, forbid_allot, 
    status, gmt_create, gmt_update, extend, version, created_by, created_name, updated_by, 
    updated_name, issue_by, issue_name, gmt_issue, issue_return_quantity, issue_return_amount, 
    real_return_quantity, pos_return_order_no, row_no, sap_batch_code, process_status, 
    created_month
  </sql>
  <select id="selectByExample" parameterType="com.cowell.iscm.entity.IscmStoreReturnExecuteOrderDetailBatchExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from iscm_store_return_execute_order_detail_batch
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from iscm_store_return_execute_order_detail_batch
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from iscm_store_return_execute_order_detail_batch
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.iscm.entity.IscmStoreReturnExecuteOrderDetailBatchExample">
    delete from iscm_store_return_execute_order_detail_batch
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.iscm.entity.IscmStoreReturnExecuteOrderDetailBatch">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into iscm_store_return_execute_order_detail_batch (return_order_no, platform_org_id, platform_org_name, 
      company_org_id, company_code, company_name, 
      store_org_id, store_code, store_name, 
      warehouse_code, warehouse_name, register_order_no, 
      register_quantity, cost_amount, data_type, 
      register_date, goods_no, goods_name, 
      forecast_sales, bar_code, goods_common_name, 
      goods_unit, description, specifications, 
      dosage_form, manufacturer, approval_number, 
      goods_class_id, goods_class_name, batch_no, 
      validity_date, produce_date, validity_days, 
      stock_upper_limit_days, stock_lower_limit_days, 
      hd_synthesize_average_daily_sales, stock_upper_limit, 
      stock_lower_limit, bdp_synthesize_average_daily_sales, 
      storage_days, non_sales_days, should_return_quantity, 
      return_quantity, goods_stock, batch_stock, 
      thirty_sales_quantity, thirty_sales_count, 
      min_display_quantity, goodsline, pushlevel, 
      forbid_distribute, forbid_return_warehouse, 
      forbid_apply, forbid_allot, status, 
      gmt_create, gmt_update, extend, 
      version, created_by, created_name, 
      updated_by, updated_name, issue_by, 
      issue_name, gmt_issue, issue_return_quantity, 
      issue_return_amount, real_return_quantity, 
      pos_return_order_no, row_no, sap_batch_code, 
      process_status, created_month)
    values (#{returnOrderNo,jdbcType=VARCHAR}, #{platformOrgId,jdbcType=BIGINT}, #{platformOrgName,jdbcType=VARCHAR}, 
      #{companyOrgId,jdbcType=BIGINT}, #{companyCode,jdbcType=VARCHAR}, #{companyName,jdbcType=VARCHAR}, 
      #{storeOrgId,jdbcType=BIGINT}, #{storeCode,jdbcType=VARCHAR}, #{storeName,jdbcType=VARCHAR}, 
      #{warehouseCode,jdbcType=VARCHAR}, #{warehouseName,jdbcType=VARCHAR}, #{registerOrderNo,jdbcType=VARCHAR}, 
      #{registerQuantity,jdbcType=DECIMAL}, #{costAmount,jdbcType=DECIMAL}, #{dataType,jdbcType=TINYINT}, 
      #{registerDate,jdbcType=DATE}, #{goodsNo,jdbcType=VARCHAR}, #{goodsName,jdbcType=VARCHAR}, 
      #{forecastSales,jdbcType=DECIMAL}, #{barCode,jdbcType=VARCHAR}, #{goodsCommonName,jdbcType=VARCHAR}, 
      #{goodsUnit,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, #{specifications,jdbcType=VARCHAR}, 
      #{dosageForm,jdbcType=VARCHAR}, #{manufacturer,jdbcType=VARCHAR}, #{approvalNumber,jdbcType=VARCHAR}, 
      #{goodsClassId,jdbcType=BIGINT}, #{goodsClassName,jdbcType=VARCHAR}, #{batchNo,jdbcType=VARCHAR}, 
      #{validityDate,jdbcType=TIMESTAMP}, #{produceDate,jdbcType=TIMESTAMP}, #{validityDays,jdbcType=INTEGER}, 
      #{stockUpperLimitDays,jdbcType=INTEGER}, #{stockLowerLimitDays,jdbcType=INTEGER}, 
      #{hdSynthesizeAverageDailySales,jdbcType=DECIMAL}, #{stockUpperLimit,jdbcType=DECIMAL}, 
      #{stockLowerLimit,jdbcType=DECIMAL}, #{bdpSynthesizeAverageDailySales,jdbcType=DECIMAL}, 
      #{storageDays,jdbcType=INTEGER}, #{nonSalesDays,jdbcType=INTEGER}, #{shouldReturnQuantity,jdbcType=DECIMAL}, 
      #{returnQuantity,jdbcType=DECIMAL}, #{goodsStock,jdbcType=DECIMAL}, #{batchStock,jdbcType=DECIMAL}, 
      #{thirtySalesQuantity,jdbcType=DECIMAL}, #{thirtySalesCount,jdbcType=INTEGER}, 
      #{minDisplayQuantity,jdbcType=DECIMAL}, #{goodsline,jdbcType=VARCHAR}, #{pushlevel,jdbcType=VARCHAR}, 
      #{forbidDistribute,jdbcType=VARCHAR}, #{forbidReturnWarehouse,jdbcType=VARCHAR}, 
      #{forbidApply,jdbcType=VARCHAR}, #{forbidAllot,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}, #{extend,jdbcType=VARCHAR}, 
      #{version,jdbcType=INTEGER}, #{createdBy,jdbcType=BIGINT}, #{createdName,jdbcType=VARCHAR}, 
      #{updatedBy,jdbcType=BIGINT}, #{updatedName,jdbcType=VARCHAR}, #{issueBy,jdbcType=BIGINT}, 
      #{issueName,jdbcType=VARCHAR}, #{gmtIssue,jdbcType=TIMESTAMP}, #{issueReturnQuantity,jdbcType=DECIMAL}, 
      #{issueReturnAmount,jdbcType=DECIMAL}, #{realReturnQuantity,jdbcType=DECIMAL}, 
      #{posReturnOrderNo,jdbcType=VARCHAR}, #{rowNo,jdbcType=VARCHAR}, #{sapBatchCode,jdbcType=VARCHAR}, 
      #{processStatus,jdbcType=TINYINT}, #{createdMonth,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.iscm.entity.IscmStoreReturnExecuteOrderDetailBatch">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into iscm_store_return_execute_order_detail_batch
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="returnOrderNo != null">
        return_order_no,
      </if>
      <if test="platformOrgId != null">
        platform_org_id,
      </if>
      <if test="platformOrgName != null">
        platform_org_name,
      </if>
      <if test="companyOrgId != null">
        company_org_id,
      </if>
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="companyName != null">
        company_name,
      </if>
      <if test="storeOrgId != null">
        store_org_id,
      </if>
      <if test="storeCode != null">
        store_code,
      </if>
      <if test="storeName != null">
        store_name,
      </if>
      <if test="warehouseCode != null">
        warehouse_code,
      </if>
      <if test="warehouseName != null">
        warehouse_name,
      </if>
      <if test="registerOrderNo != null">
        register_order_no,
      </if>
      <if test="registerQuantity != null">
        register_quantity,
      </if>
      <if test="costAmount != null">
        cost_amount,
      </if>
      <if test="dataType != null">
        data_type,
      </if>
      <if test="registerDate != null">
        register_date,
      </if>
      <if test="goodsNo != null">
        goods_no,
      </if>
      <if test="goodsName != null">
        goods_name,
      </if>
      <if test="forecastSales != null">
        forecast_sales,
      </if>
      <if test="barCode != null">
        bar_code,
      </if>
      <if test="goodsCommonName != null">
        goods_common_name,
      </if>
      <if test="goodsUnit != null">
        goods_unit,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="specifications != null">
        specifications,
      </if>
      <if test="dosageForm != null">
        dosage_form,
      </if>
      <if test="manufacturer != null">
        manufacturer,
      </if>
      <if test="approvalNumber != null">
        approval_number,
      </if>
      <if test="goodsClassId != null">
        goods_class_id,
      </if>
      <if test="goodsClassName != null">
        goods_class_name,
      </if>
      <if test="batchNo != null">
        batch_no,
      </if>
      <if test="validityDate != null">
        validity_date,
      </if>
      <if test="produceDate != null">
        produce_date,
      </if>
      <if test="validityDays != null">
        validity_days,
      </if>
      <if test="stockUpperLimitDays != null">
        stock_upper_limit_days,
      </if>
      <if test="stockLowerLimitDays != null">
        stock_lower_limit_days,
      </if>
      <if test="hdSynthesizeAverageDailySales != null">
        hd_synthesize_average_daily_sales,
      </if>
      <if test="stockUpperLimit != null">
        stock_upper_limit,
      </if>
      <if test="stockLowerLimit != null">
        stock_lower_limit,
      </if>
      <if test="bdpSynthesizeAverageDailySales != null">
        bdp_synthesize_average_daily_sales,
      </if>
      <if test="storageDays != null">
        storage_days,
      </if>
      <if test="nonSalesDays != null">
        non_sales_days,
      </if>
      <if test="shouldReturnQuantity != null">
        should_return_quantity,
      </if>
      <if test="returnQuantity != null">
        return_quantity,
      </if>
      <if test="goodsStock != null">
        goods_stock,
      </if>
      <if test="batchStock != null">
        batch_stock,
      </if>
      <if test="thirtySalesQuantity != null">
        thirty_sales_quantity,
      </if>
      <if test="thirtySalesCount != null">
        thirty_sales_count,
      </if>
      <if test="minDisplayQuantity != null">
        min_display_quantity,
      </if>
      <if test="goodsline != null">
        goodsline,
      </if>
      <if test="pushlevel != null">
        pushlevel,
      </if>
      <if test="forbidDistribute != null">
        forbid_distribute,
      </if>
      <if test="forbidReturnWarehouse != null">
        forbid_return_warehouse,
      </if>
      <if test="forbidApply != null">
        forbid_apply,
      </if>
      <if test="forbidAllot != null">
        forbid_allot,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedName != null">
        updated_name,
      </if>
      <if test="issueBy != null">
        issue_by,
      </if>
      <if test="issueName != null">
        issue_name,
      </if>
      <if test="gmtIssue != null">
        gmt_issue,
      </if>
      <if test="issueReturnQuantity != null">
        issue_return_quantity,
      </if>
      <if test="issueReturnAmount != null">
        issue_return_amount,
      </if>
      <if test="realReturnQuantity != null">
        real_return_quantity,
      </if>
      <if test="posReturnOrderNo != null">
        pos_return_order_no,
      </if>
      <if test="rowNo != null">
        row_no,
      </if>
      <if test="sapBatchCode != null">
        sap_batch_code,
      </if>
      <if test="processStatus != null">
        process_status,
      </if>
      <if test="createdMonth != null">
        created_month,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="returnOrderNo != null">
        #{returnOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="platformOrgId != null">
        #{platformOrgId,jdbcType=BIGINT},
      </if>
      <if test="platformOrgName != null">
        #{platformOrgName,jdbcType=VARCHAR},
      </if>
      <if test="companyOrgId != null">
        #{companyOrgId,jdbcType=BIGINT},
      </if>
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="storeOrgId != null">
        #{storeOrgId,jdbcType=BIGINT},
      </if>
      <if test="storeCode != null">
        #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="storeName != null">
        #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="warehouseCode != null">
        #{warehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="warehouseName != null">
        #{warehouseName,jdbcType=VARCHAR},
      </if>
      <if test="registerOrderNo != null">
        #{registerOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="registerQuantity != null">
        #{registerQuantity,jdbcType=DECIMAL},
      </if>
      <if test="costAmount != null">
        #{costAmount,jdbcType=DECIMAL},
      </if>
      <if test="dataType != null">
        #{dataType,jdbcType=TINYINT},
      </if>
      <if test="registerDate != null">
        #{registerDate,jdbcType=DATE},
      </if>
      <if test="goodsNo != null">
        #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="forecastSales != null">
        #{forecastSales,jdbcType=DECIMAL},
      </if>
      <if test="barCode != null">
        #{barCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsCommonName != null">
        #{goodsCommonName,jdbcType=VARCHAR},
      </if>
      <if test="goodsUnit != null">
        #{goodsUnit,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="specifications != null">
        #{specifications,jdbcType=VARCHAR},
      </if>
      <if test="dosageForm != null">
        #{dosageForm,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="approvalNumber != null">
        #{approvalNumber,jdbcType=VARCHAR},
      </if>
      <if test="goodsClassId != null">
        #{goodsClassId,jdbcType=BIGINT},
      </if>
      <if test="goodsClassName != null">
        #{goodsClassName,jdbcType=VARCHAR},
      </if>
      <if test="batchNo != null">
        #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="validityDate != null">
        #{validityDate,jdbcType=TIMESTAMP},
      </if>
      <if test="produceDate != null">
        #{produceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="validityDays != null">
        #{validityDays,jdbcType=INTEGER},
      </if>
      <if test="stockUpperLimitDays != null">
        #{stockUpperLimitDays,jdbcType=INTEGER},
      </if>
      <if test="stockLowerLimitDays != null">
        #{stockLowerLimitDays,jdbcType=INTEGER},
      </if>
      <if test="hdSynthesizeAverageDailySales != null">
        #{hdSynthesizeAverageDailySales,jdbcType=DECIMAL},
      </if>
      <if test="stockUpperLimit != null">
        #{stockUpperLimit,jdbcType=DECIMAL},
      </if>
      <if test="stockLowerLimit != null">
        #{stockLowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="bdpSynthesizeAverageDailySales != null">
        #{bdpSynthesizeAverageDailySales,jdbcType=DECIMAL},
      </if>
      <if test="storageDays != null">
        #{storageDays,jdbcType=INTEGER},
      </if>
      <if test="nonSalesDays != null">
        #{nonSalesDays,jdbcType=INTEGER},
      </if>
      <if test="shouldReturnQuantity != null">
        #{shouldReturnQuantity,jdbcType=DECIMAL},
      </if>
      <if test="returnQuantity != null">
        #{returnQuantity,jdbcType=DECIMAL},
      </if>
      <if test="goodsStock != null">
        #{goodsStock,jdbcType=DECIMAL},
      </if>
      <if test="batchStock != null">
        #{batchStock,jdbcType=DECIMAL},
      </if>
      <if test="thirtySalesQuantity != null">
        #{thirtySalesQuantity,jdbcType=DECIMAL},
      </if>
      <if test="thirtySalesCount != null">
        #{thirtySalesCount,jdbcType=INTEGER},
      </if>
      <if test="minDisplayQuantity != null">
        #{minDisplayQuantity,jdbcType=DECIMAL},
      </if>
      <if test="goodsline != null">
        #{goodsline,jdbcType=VARCHAR},
      </if>
      <if test="pushlevel != null">
        #{pushlevel,jdbcType=VARCHAR},
      </if>
      <if test="forbidDistribute != null">
        #{forbidDistribute,jdbcType=VARCHAR},
      </if>
      <if test="forbidReturnWarehouse != null">
        #{forbidReturnWarehouse,jdbcType=VARCHAR},
      </if>
      <if test="forbidApply != null">
        #{forbidApply,jdbcType=VARCHAR},
      </if>
      <if test="forbidAllot != null">
        #{forbidAllot,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        #{updatedName,jdbcType=VARCHAR},
      </if>
      <if test="issueBy != null">
        #{issueBy,jdbcType=BIGINT},
      </if>
      <if test="issueName != null">
        #{issueName,jdbcType=VARCHAR},
      </if>
      <if test="gmtIssue != null">
        #{gmtIssue,jdbcType=TIMESTAMP},
      </if>
      <if test="issueReturnQuantity != null">
        #{issueReturnQuantity,jdbcType=DECIMAL},
      </if>
      <if test="issueReturnAmount != null">
        #{issueReturnAmount,jdbcType=DECIMAL},
      </if>
      <if test="realReturnQuantity != null">
        #{realReturnQuantity,jdbcType=DECIMAL},
      </if>
      <if test="posReturnOrderNo != null">
        #{posReturnOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="rowNo != null">
        #{rowNo,jdbcType=VARCHAR},
      </if>
      <if test="sapBatchCode != null">
        #{sapBatchCode,jdbcType=VARCHAR},
      </if>
      <if test="processStatus != null">
        #{processStatus,jdbcType=TINYINT},
      </if>
      <if test="createdMonth != null">
        #{createdMonth,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.iscm.entity.IscmStoreReturnExecuteOrderDetailBatchExample" resultType="java.lang.Long">
    select count(*) from iscm_store_return_execute_order_detail_batch
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update iscm_store_return_execute_order_detail_batch
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.returnOrderNo != null">
        return_order_no = #{record.returnOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.platformOrgId != null">
        platform_org_id = #{record.platformOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.platformOrgName != null">
        platform_org_name = #{record.platformOrgName,jdbcType=VARCHAR},
      </if>
      <if test="record.companyOrgId != null">
        company_org_id = #{record.companyOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.companyCode != null">
        company_code = #{record.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.companyName != null">
        company_name = #{record.companyName,jdbcType=VARCHAR},
      </if>
      <if test="record.storeOrgId != null">
        store_org_id = #{record.storeOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.storeCode != null">
        store_code = #{record.storeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.storeName != null">
        store_name = #{record.storeName,jdbcType=VARCHAR},
      </if>
      <if test="record.warehouseCode != null">
        warehouse_code = #{record.warehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="record.warehouseName != null">
        warehouse_name = #{record.warehouseName,jdbcType=VARCHAR},
      </if>
      <if test="record.registerOrderNo != null">
        register_order_no = #{record.registerOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.registerQuantity != null">
        register_quantity = #{record.registerQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.costAmount != null">
        cost_amount = #{record.costAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.dataType != null">
        data_type = #{record.dataType,jdbcType=TINYINT},
      </if>
      <if test="record.registerDate != null">
        register_date = #{record.registerDate,jdbcType=DATE},
      </if>
      <if test="record.goodsNo != null">
        goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsName != null">
        goods_name = #{record.goodsName,jdbcType=VARCHAR},
      </if>
      <if test="record.forecastSales != null">
        forecast_sales = #{record.forecastSales,jdbcType=DECIMAL},
      </if>
      <if test="record.barCode != null">
        bar_code = #{record.barCode,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsCommonName != null">
        goods_common_name = #{record.goodsCommonName,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsUnit != null">
        goods_unit = #{record.goodsUnit,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.specifications != null">
        specifications = #{record.specifications,jdbcType=VARCHAR},
      </if>
      <if test="record.dosageForm != null">
        dosage_form = #{record.dosageForm,jdbcType=VARCHAR},
      </if>
      <if test="record.manufacturer != null">
        manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="record.approvalNumber != null">
        approval_number = #{record.approvalNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsClassId != null">
        goods_class_id = #{record.goodsClassId,jdbcType=BIGINT},
      </if>
      <if test="record.goodsClassName != null">
        goods_class_name = #{record.goodsClassName,jdbcType=VARCHAR},
      </if>
      <if test="record.batchNo != null">
        batch_no = #{record.batchNo,jdbcType=VARCHAR},
      </if>
      <if test="record.validityDate != null">
        validity_date = #{record.validityDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.produceDate != null">
        produce_date = #{record.produceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.validityDays != null">
        validity_days = #{record.validityDays,jdbcType=INTEGER},
      </if>
      <if test="record.stockUpperLimitDays != null">
        stock_upper_limit_days = #{record.stockUpperLimitDays,jdbcType=INTEGER},
      </if>
      <if test="record.stockLowerLimitDays != null">
        stock_lower_limit_days = #{record.stockLowerLimitDays,jdbcType=INTEGER},
      </if>
      <if test="record.hdSynthesizeAverageDailySales != null">
        hd_synthesize_average_daily_sales = #{record.hdSynthesizeAverageDailySales,jdbcType=DECIMAL},
      </if>
      <if test="record.stockUpperLimit != null">
        stock_upper_limit = #{record.stockUpperLimit,jdbcType=DECIMAL},
      </if>
      <if test="record.stockLowerLimit != null">
        stock_lower_limit = #{record.stockLowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="record.bdpSynthesizeAverageDailySales != null">
        bdp_synthesize_average_daily_sales = #{record.bdpSynthesizeAverageDailySales,jdbcType=DECIMAL},
      </if>
      <if test="record.storageDays != null">
        storage_days = #{record.storageDays,jdbcType=INTEGER},
      </if>
      <if test="record.nonSalesDays != null">
        non_sales_days = #{record.nonSalesDays,jdbcType=INTEGER},
      </if>
      <if test="record.shouldReturnQuantity != null">
        should_return_quantity = #{record.shouldReturnQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.returnQuantity != null">
        return_quantity = #{record.returnQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.goodsStock != null">
        goods_stock = #{record.goodsStock,jdbcType=DECIMAL},
      </if>
      <if test="record.batchStock != null">
        batch_stock = #{record.batchStock,jdbcType=DECIMAL},
      </if>
      <if test="record.thirtySalesQuantity != null">
        thirty_sales_quantity = #{record.thirtySalesQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.thirtySalesCount != null">
        thirty_sales_count = #{record.thirtySalesCount,jdbcType=INTEGER},
      </if>
      <if test="record.minDisplayQuantity != null">
        min_display_quantity = #{record.minDisplayQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.goodsline != null">
        goodsline = #{record.goodsline,jdbcType=VARCHAR},
      </if>
      <if test="record.pushlevel != null">
        pushlevel = #{record.pushlevel,jdbcType=VARCHAR},
      </if>
      <if test="record.forbidDistribute != null">
        forbid_distribute = #{record.forbidDistribute,jdbcType=VARCHAR},
      </if>
      <if test="record.forbidReturnWarehouse != null">
        forbid_return_warehouse = #{record.forbidReturnWarehouse,jdbcType=VARCHAR},
      </if>
      <if test="record.forbidApply != null">
        forbid_apply = #{record.forbidApply,jdbcType=VARCHAR},
      </if>
      <if test="record.forbidAllot != null">
        forbid_allot = #{record.forbidAllot,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedName != null">
        updated_name = #{record.updatedName,jdbcType=VARCHAR},
      </if>
      <if test="record.issueBy != null">
        issue_by = #{record.issueBy,jdbcType=BIGINT},
      </if>
      <if test="record.issueName != null">
        issue_name = #{record.issueName,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtIssue != null">
        gmt_issue = #{record.gmtIssue,jdbcType=TIMESTAMP},
      </if>
      <if test="record.issueReturnQuantity != null">
        issue_return_quantity = #{record.issueReturnQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.issueReturnAmount != null">
        issue_return_amount = #{record.issueReturnAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.realReturnQuantity != null">
        real_return_quantity = #{record.realReturnQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.posReturnOrderNo != null">
        pos_return_order_no = #{record.posReturnOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.rowNo != null">
        row_no = #{record.rowNo,jdbcType=VARCHAR},
      </if>
      <if test="record.sapBatchCode != null">
        sap_batch_code = #{record.sapBatchCode,jdbcType=VARCHAR},
      </if>
      <if test="record.processStatus != null">
        process_status = #{record.processStatus,jdbcType=TINYINT},
      </if>
      <if test="record.createdMonth != null">
        created_month = #{record.createdMonth,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update iscm_store_return_execute_order_detail_batch
    set id = #{record.id,jdbcType=BIGINT},
      return_order_no = #{record.returnOrderNo,jdbcType=VARCHAR},
      platform_org_id = #{record.platformOrgId,jdbcType=BIGINT},
      platform_org_name = #{record.platformOrgName,jdbcType=VARCHAR},
      company_org_id = #{record.companyOrgId,jdbcType=BIGINT},
      company_code = #{record.companyCode,jdbcType=VARCHAR},
      company_name = #{record.companyName,jdbcType=VARCHAR},
      store_org_id = #{record.storeOrgId,jdbcType=BIGINT},
      store_code = #{record.storeCode,jdbcType=VARCHAR},
      store_name = #{record.storeName,jdbcType=VARCHAR},
      warehouse_code = #{record.warehouseCode,jdbcType=VARCHAR},
      warehouse_name = #{record.warehouseName,jdbcType=VARCHAR},
      register_order_no = #{record.registerOrderNo,jdbcType=VARCHAR},
      register_quantity = #{record.registerQuantity,jdbcType=DECIMAL},
      cost_amount = #{record.costAmount,jdbcType=DECIMAL},
      data_type = #{record.dataType,jdbcType=TINYINT},
      register_date = #{record.registerDate,jdbcType=DATE},
      goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      goods_name = #{record.goodsName,jdbcType=VARCHAR},
      forecast_sales = #{record.forecastSales,jdbcType=DECIMAL},
      bar_code = #{record.barCode,jdbcType=VARCHAR},
      goods_common_name = #{record.goodsCommonName,jdbcType=VARCHAR},
      goods_unit = #{record.goodsUnit,jdbcType=VARCHAR},
      description = #{record.description,jdbcType=VARCHAR},
      specifications = #{record.specifications,jdbcType=VARCHAR},
      dosage_form = #{record.dosageForm,jdbcType=VARCHAR},
      manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      approval_number = #{record.approvalNumber,jdbcType=VARCHAR},
      goods_class_id = #{record.goodsClassId,jdbcType=BIGINT},
      goods_class_name = #{record.goodsClassName,jdbcType=VARCHAR},
      batch_no = #{record.batchNo,jdbcType=VARCHAR},
      validity_date = #{record.validityDate,jdbcType=TIMESTAMP},
      produce_date = #{record.produceDate,jdbcType=TIMESTAMP},
      validity_days = #{record.validityDays,jdbcType=INTEGER},
      stock_upper_limit_days = #{record.stockUpperLimitDays,jdbcType=INTEGER},
      stock_lower_limit_days = #{record.stockLowerLimitDays,jdbcType=INTEGER},
      hd_synthesize_average_daily_sales = #{record.hdSynthesizeAverageDailySales,jdbcType=DECIMAL},
      stock_upper_limit = #{record.stockUpperLimit,jdbcType=DECIMAL},
      stock_lower_limit = #{record.stockLowerLimit,jdbcType=DECIMAL},
      bdp_synthesize_average_daily_sales = #{record.bdpSynthesizeAverageDailySales,jdbcType=DECIMAL},
      storage_days = #{record.storageDays,jdbcType=INTEGER},
      non_sales_days = #{record.nonSalesDays,jdbcType=INTEGER},
      should_return_quantity = #{record.shouldReturnQuantity,jdbcType=DECIMAL},
      return_quantity = #{record.returnQuantity,jdbcType=DECIMAL},
      goods_stock = #{record.goodsStock,jdbcType=DECIMAL},
      batch_stock = #{record.batchStock,jdbcType=DECIMAL},
      thirty_sales_quantity = #{record.thirtySalesQuantity,jdbcType=DECIMAL},
      thirty_sales_count = #{record.thirtySalesCount,jdbcType=INTEGER},
      min_display_quantity = #{record.minDisplayQuantity,jdbcType=DECIMAL},
      goodsline = #{record.goodsline,jdbcType=VARCHAR},
      pushlevel = #{record.pushlevel,jdbcType=VARCHAR},
      forbid_distribute = #{record.forbidDistribute,jdbcType=VARCHAR},
      forbid_return_warehouse = #{record.forbidReturnWarehouse,jdbcType=VARCHAR},
      forbid_apply = #{record.forbidApply,jdbcType=VARCHAR},
      forbid_allot = #{record.forbidAllot,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=TINYINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_name = #{record.createdName,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_name = #{record.updatedName,jdbcType=VARCHAR},
      issue_by = #{record.issueBy,jdbcType=BIGINT},
      issue_name = #{record.issueName,jdbcType=VARCHAR},
      gmt_issue = #{record.gmtIssue,jdbcType=TIMESTAMP},
      issue_return_quantity = #{record.issueReturnQuantity,jdbcType=DECIMAL},
      issue_return_amount = #{record.issueReturnAmount,jdbcType=DECIMAL},
      real_return_quantity = #{record.realReturnQuantity,jdbcType=DECIMAL},
      pos_return_order_no = #{record.posReturnOrderNo,jdbcType=VARCHAR},
      row_no = #{record.rowNo,jdbcType=VARCHAR},
      sap_batch_code = #{record.sapBatchCode,jdbcType=VARCHAR},
      process_status = #{record.processStatus,jdbcType=TINYINT},
      created_month = #{record.createdMonth,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.iscm.entity.IscmStoreReturnExecuteOrderDetailBatch">
    update iscm_store_return_execute_order_detail_batch
    <set>
      <if test="returnOrderNo != null">
        return_order_no = #{returnOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="platformOrgId != null">
        platform_org_id = #{platformOrgId,jdbcType=BIGINT},
      </if>
      <if test="platformOrgName != null">
        platform_org_name = #{platformOrgName,jdbcType=VARCHAR},
      </if>
      <if test="companyOrgId != null">
        company_org_id = #{companyOrgId,jdbcType=BIGINT},
      </if>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="storeOrgId != null">
        store_org_id = #{storeOrgId,jdbcType=BIGINT},
      </if>
      <if test="storeCode != null">
        store_code = #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="storeName != null">
        store_name = #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="warehouseCode != null">
        warehouse_code = #{warehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="warehouseName != null">
        warehouse_name = #{warehouseName,jdbcType=VARCHAR},
      </if>
      <if test="registerOrderNo != null">
        register_order_no = #{registerOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="registerQuantity != null">
        register_quantity = #{registerQuantity,jdbcType=DECIMAL},
      </if>
      <if test="costAmount != null">
        cost_amount = #{costAmount,jdbcType=DECIMAL},
      </if>
      <if test="dataType != null">
        data_type = #{dataType,jdbcType=TINYINT},
      </if>
      <if test="registerDate != null">
        register_date = #{registerDate,jdbcType=DATE},
      </if>
      <if test="goodsNo != null">
        goods_no = #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        goods_name = #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="forecastSales != null">
        forecast_sales = #{forecastSales,jdbcType=DECIMAL},
      </if>
      <if test="barCode != null">
        bar_code = #{barCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsCommonName != null">
        goods_common_name = #{goodsCommonName,jdbcType=VARCHAR},
      </if>
      <if test="goodsUnit != null">
        goods_unit = #{goodsUnit,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="specifications != null">
        specifications = #{specifications,jdbcType=VARCHAR},
      </if>
      <if test="dosageForm != null">
        dosage_form = #{dosageForm,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        manufacturer = #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="approvalNumber != null">
        approval_number = #{approvalNumber,jdbcType=VARCHAR},
      </if>
      <if test="goodsClassId != null">
        goods_class_id = #{goodsClassId,jdbcType=BIGINT},
      </if>
      <if test="goodsClassName != null">
        goods_class_name = #{goodsClassName,jdbcType=VARCHAR},
      </if>
      <if test="batchNo != null">
        batch_no = #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="validityDate != null">
        validity_date = #{validityDate,jdbcType=TIMESTAMP},
      </if>
      <if test="produceDate != null">
        produce_date = #{produceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="validityDays != null">
        validity_days = #{validityDays,jdbcType=INTEGER},
      </if>
      <if test="stockUpperLimitDays != null">
        stock_upper_limit_days = #{stockUpperLimitDays,jdbcType=INTEGER},
      </if>
      <if test="stockLowerLimitDays != null">
        stock_lower_limit_days = #{stockLowerLimitDays,jdbcType=INTEGER},
      </if>
      <if test="hdSynthesizeAverageDailySales != null">
        hd_synthesize_average_daily_sales = #{hdSynthesizeAverageDailySales,jdbcType=DECIMAL},
      </if>
      <if test="stockUpperLimit != null">
        stock_upper_limit = #{stockUpperLimit,jdbcType=DECIMAL},
      </if>
      <if test="stockLowerLimit != null">
        stock_lower_limit = #{stockLowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="bdpSynthesizeAverageDailySales != null">
        bdp_synthesize_average_daily_sales = #{bdpSynthesizeAverageDailySales,jdbcType=DECIMAL},
      </if>
      <if test="storageDays != null">
        storage_days = #{storageDays,jdbcType=INTEGER},
      </if>
      <if test="nonSalesDays != null">
        non_sales_days = #{nonSalesDays,jdbcType=INTEGER},
      </if>
      <if test="shouldReturnQuantity != null">
        should_return_quantity = #{shouldReturnQuantity,jdbcType=DECIMAL},
      </if>
      <if test="returnQuantity != null">
        return_quantity = #{returnQuantity,jdbcType=DECIMAL},
      </if>
      <if test="goodsStock != null">
        goods_stock = #{goodsStock,jdbcType=DECIMAL},
      </if>
      <if test="batchStock != null">
        batch_stock = #{batchStock,jdbcType=DECIMAL},
      </if>
      <if test="thirtySalesQuantity != null">
        thirty_sales_quantity = #{thirtySalesQuantity,jdbcType=DECIMAL},
      </if>
      <if test="thirtySalesCount != null">
        thirty_sales_count = #{thirtySalesCount,jdbcType=INTEGER},
      </if>
      <if test="minDisplayQuantity != null">
        min_display_quantity = #{minDisplayQuantity,jdbcType=DECIMAL},
      </if>
      <if test="goodsline != null">
        goodsline = #{goodsline,jdbcType=VARCHAR},
      </if>
      <if test="pushlevel != null">
        pushlevel = #{pushlevel,jdbcType=VARCHAR},
      </if>
      <if test="forbidDistribute != null">
        forbid_distribute = #{forbidDistribute,jdbcType=VARCHAR},
      </if>
      <if test="forbidReturnWarehouse != null">
        forbid_return_warehouse = #{forbidReturnWarehouse,jdbcType=VARCHAR},
      </if>
      <if test="forbidApply != null">
        forbid_apply = #{forbidApply,jdbcType=VARCHAR},
      </if>
      <if test="forbidAllot != null">
        forbid_allot = #{forbidAllot,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        updated_name = #{updatedName,jdbcType=VARCHAR},
      </if>
      <if test="issueBy != null">
        issue_by = #{issueBy,jdbcType=BIGINT},
      </if>
      <if test="issueName != null">
        issue_name = #{issueName,jdbcType=VARCHAR},
      </if>
      <if test="gmtIssue != null">
        gmt_issue = #{gmtIssue,jdbcType=TIMESTAMP},
      </if>
      <if test="issueReturnQuantity != null">
        issue_return_quantity = #{issueReturnQuantity,jdbcType=DECIMAL},
      </if>
      <if test="issueReturnAmount != null">
        issue_return_amount = #{issueReturnAmount,jdbcType=DECIMAL},
      </if>
      <if test="realReturnQuantity != null">
        real_return_quantity = #{realReturnQuantity,jdbcType=DECIMAL},
      </if>
      <if test="posReturnOrderNo != null">
        pos_return_order_no = #{posReturnOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="rowNo != null">
        row_no = #{rowNo,jdbcType=VARCHAR},
      </if>
      <if test="sapBatchCode != null">
        sap_batch_code = #{sapBatchCode,jdbcType=VARCHAR},
      </if>
      <if test="processStatus != null">
        process_status = #{processStatus,jdbcType=TINYINT},
      </if>
      <if test="createdMonth != null">
        created_month = #{createdMonth,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.iscm.entity.IscmStoreReturnExecuteOrderDetailBatch">
    update iscm_store_return_execute_order_detail_batch
    set return_order_no = #{returnOrderNo,jdbcType=VARCHAR},
      platform_org_id = #{platformOrgId,jdbcType=BIGINT},
      platform_org_name = #{platformOrgName,jdbcType=VARCHAR},
      company_org_id = #{companyOrgId,jdbcType=BIGINT},
      company_code = #{companyCode,jdbcType=VARCHAR},
      company_name = #{companyName,jdbcType=VARCHAR},
      store_org_id = #{storeOrgId,jdbcType=BIGINT},
      store_code = #{storeCode,jdbcType=VARCHAR},
      store_name = #{storeName,jdbcType=VARCHAR},
      warehouse_code = #{warehouseCode,jdbcType=VARCHAR},
      warehouse_name = #{warehouseName,jdbcType=VARCHAR},
      register_order_no = #{registerOrderNo,jdbcType=VARCHAR},
      register_quantity = #{registerQuantity,jdbcType=DECIMAL},
      cost_amount = #{costAmount,jdbcType=DECIMAL},
      data_type = #{dataType,jdbcType=TINYINT},
      register_date = #{registerDate,jdbcType=DATE},
      goods_no = #{goodsNo,jdbcType=VARCHAR},
      goods_name = #{goodsName,jdbcType=VARCHAR},
      forecast_sales = #{forecastSales,jdbcType=DECIMAL},
      bar_code = #{barCode,jdbcType=VARCHAR},
      goods_common_name = #{goodsCommonName,jdbcType=VARCHAR},
      goods_unit = #{goodsUnit,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      specifications = #{specifications,jdbcType=VARCHAR},
      dosage_form = #{dosageForm,jdbcType=VARCHAR},
      manufacturer = #{manufacturer,jdbcType=VARCHAR},
      approval_number = #{approvalNumber,jdbcType=VARCHAR},
      goods_class_id = #{goodsClassId,jdbcType=BIGINT},
      goods_class_name = #{goodsClassName,jdbcType=VARCHAR},
      batch_no = #{batchNo,jdbcType=VARCHAR},
      validity_date = #{validityDate,jdbcType=TIMESTAMP},
      produce_date = #{produceDate,jdbcType=TIMESTAMP},
      validity_days = #{validityDays,jdbcType=INTEGER},
      stock_upper_limit_days = #{stockUpperLimitDays,jdbcType=INTEGER},
      stock_lower_limit_days = #{stockLowerLimitDays,jdbcType=INTEGER},
      hd_synthesize_average_daily_sales = #{hdSynthesizeAverageDailySales,jdbcType=DECIMAL},
      stock_upper_limit = #{stockUpperLimit,jdbcType=DECIMAL},
      stock_lower_limit = #{stockLowerLimit,jdbcType=DECIMAL},
      bdp_synthesize_average_daily_sales = #{bdpSynthesizeAverageDailySales,jdbcType=DECIMAL},
      storage_days = #{storageDays,jdbcType=INTEGER},
      non_sales_days = #{nonSalesDays,jdbcType=INTEGER},
      should_return_quantity = #{shouldReturnQuantity,jdbcType=DECIMAL},
      return_quantity = #{returnQuantity,jdbcType=DECIMAL},
      goods_stock = #{goodsStock,jdbcType=DECIMAL},
      batch_stock = #{batchStock,jdbcType=DECIMAL},
      thirty_sales_quantity = #{thirtySalesQuantity,jdbcType=DECIMAL},
      thirty_sales_count = #{thirtySalesCount,jdbcType=INTEGER},
      min_display_quantity = #{minDisplayQuantity,jdbcType=DECIMAL},
      goodsline = #{goodsline,jdbcType=VARCHAR},
      pushlevel = #{pushlevel,jdbcType=VARCHAR},
      forbid_distribute = #{forbidDistribute,jdbcType=VARCHAR},
      forbid_return_warehouse = #{forbidReturnWarehouse,jdbcType=VARCHAR},
      forbid_apply = #{forbidApply,jdbcType=VARCHAR},
      forbid_allot = #{forbidAllot,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_name = #{createdName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_name = #{updatedName,jdbcType=VARCHAR},
      issue_by = #{issueBy,jdbcType=BIGINT},
      issue_name = #{issueName,jdbcType=VARCHAR},
      gmt_issue = #{gmtIssue,jdbcType=TIMESTAMP},
      issue_return_quantity = #{issueReturnQuantity,jdbcType=DECIMAL},
      issue_return_amount = #{issueReturnAmount,jdbcType=DECIMAL},
      real_return_quantity = #{realReturnQuantity,jdbcType=DECIMAL},
      pos_return_order_no = #{posReturnOrderNo,jdbcType=VARCHAR},
      row_no = #{rowNo,jdbcType=VARCHAR},
      sap_batch_code = #{sapBatchCode,jdbcType=VARCHAR},
      process_status = #{processStatus,jdbcType=TINYINT},
      created_month = #{createdMonth,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>