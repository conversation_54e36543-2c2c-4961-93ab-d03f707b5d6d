<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.IscmZdtTaskDomainMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.IscmZdtTaskDomain">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="domain_key" jdbcType="VARCHAR" property="domainKey" />
    <result column="domain_key_type" jdbcType="TINYINT" property="domainKeyType" />
    <result column="domain_key_type_desc" jdbcType="VARCHAR" property="domainKeyTypeDesc" />
    <result column="type_id" jdbcType="BIGINT" property="typeId" />
    <result column="type_code" jdbcType="VARCHAR" property="typeCode" />
    <result column="task_name" jdbcType="VARCHAR" property="taskName" />
    <result column="domain_mark" jdbcType="VARCHAR" property="domainMark" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.cowell.iscm.entity.IscmZdtTaskDomain">
    <result column="domain_info" jdbcType="LONGVARCHAR" property="domainInfo" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, domain_key, domain_key_type, domain_key_type_desc, type_id, type_code, task_name, 
    domain_mark, status, gmt_create, gmt_update, extend, version, created_by, created_name, 
    updated_by, updated_name
  </sql>
  <sql id="Blob_Column_List">
    domain_info
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.cowell.iscm.entity.IscmZdtTaskDomainExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from iscm_zdt_task_domain
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.cowell.iscm.entity.IscmZdtTaskDomainExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from iscm_zdt_task_domain
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from iscm_zdt_task_domain
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from iscm_zdt_task_domain
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.iscm.entity.IscmZdtTaskDomainExample">
    delete from iscm_zdt_task_domain
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.iscm.entity.IscmZdtTaskDomain">
    insert into iscm_zdt_task_domain (id, domain_key, domain_key_type, 
      domain_key_type_desc, type_id, type_code, 
      task_name, domain_mark, status, 
      gmt_create, gmt_update, extend, 
      version, created_by, created_name, 
      updated_by, updated_name, domain_info
      )
    values (#{id,jdbcType=BIGINT}, #{domainKey,jdbcType=VARCHAR}, #{domainKeyType,jdbcType=TINYINT}, 
      #{domainKeyTypeDesc,jdbcType=VARCHAR}, #{typeId,jdbcType=BIGINT}, #{typeCode,jdbcType=VARCHAR}, 
      #{taskName,jdbcType=VARCHAR}, #{domainMark,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}, #{extend,jdbcType=VARCHAR}, 
      #{version,jdbcType=INTEGER}, #{createdBy,jdbcType=BIGINT}, #{createdName,jdbcType=VARCHAR}, 
      #{updatedBy,jdbcType=BIGINT}, #{updatedName,jdbcType=VARCHAR}, #{domainInfo,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.iscm.entity.IscmZdtTaskDomain">
    insert into iscm_zdt_task_domain
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="domainKey != null">
        domain_key,
      </if>
      <if test="domainKeyType != null">
        domain_key_type,
      </if>
      <if test="domainKeyTypeDesc != null">
        domain_key_type_desc,
      </if>
      <if test="typeId != null">
        type_id,
      </if>
      <if test="typeCode != null">
        type_code,
      </if>
      <if test="taskName != null">
        task_name,
      </if>
      <if test="domainMark != null">
        domain_mark,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedName != null">
        updated_name,
      </if>
      <if test="domainInfo != null">
        domain_info,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="domainKey != null">
        #{domainKey,jdbcType=VARCHAR},
      </if>
      <if test="domainKeyType != null">
        #{domainKeyType,jdbcType=TINYINT},
      </if>
      <if test="domainKeyTypeDesc != null">
        #{domainKeyTypeDesc,jdbcType=VARCHAR},
      </if>
      <if test="typeId != null">
        #{typeId,jdbcType=BIGINT},
      </if>
      <if test="typeCode != null">
        #{typeCode,jdbcType=VARCHAR},
      </if>
      <if test="taskName != null">
        #{taskName,jdbcType=VARCHAR},
      </if>
      <if test="domainMark != null">
        #{domainMark,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        #{updatedName,jdbcType=VARCHAR},
      </if>
      <if test="domainInfo != null">
        #{domainInfo,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.iscm.entity.IscmZdtTaskDomainExample" resultType="java.lang.Long">
    select count(*) from iscm_zdt_task_domain
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update iscm_zdt_task_domain
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.domainKey != null">
        domain_key = #{record.domainKey,jdbcType=VARCHAR},
      </if>
      <if test="record.domainKeyType != null">
        domain_key_type = #{record.domainKeyType,jdbcType=TINYINT},
      </if>
      <if test="record.domainKeyTypeDesc != null">
        domain_key_type_desc = #{record.domainKeyTypeDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.typeId != null">
        type_id = #{record.typeId,jdbcType=BIGINT},
      </if>
      <if test="record.typeCode != null">
        type_code = #{record.typeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.taskName != null">
        task_name = #{record.taskName,jdbcType=VARCHAR},
      </if>
      <if test="record.domainMark != null">
        domain_mark = #{record.domainMark,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedName != null">
        updated_name = #{record.updatedName,jdbcType=VARCHAR},
      </if>
      <if test="record.domainInfo != null">
        domain_info = #{record.domainInfo,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update iscm_zdt_task_domain
    set id = #{record.id,jdbcType=BIGINT},
      domain_key = #{record.domainKey,jdbcType=VARCHAR},
      domain_key_type = #{record.domainKeyType,jdbcType=TINYINT},
      domain_key_type_desc = #{record.domainKeyTypeDesc,jdbcType=VARCHAR},
      type_id = #{record.typeId,jdbcType=BIGINT},
      type_code = #{record.typeCode,jdbcType=VARCHAR},
      task_name = #{record.taskName,jdbcType=VARCHAR},
      domain_mark = #{record.domainMark,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=TINYINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_name = #{record.createdName,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_name = #{record.updatedName,jdbcType=VARCHAR},
      domain_info = #{record.domainInfo,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update iscm_zdt_task_domain
    set id = #{record.id,jdbcType=BIGINT},
      domain_key = #{record.domainKey,jdbcType=VARCHAR},
      domain_key_type = #{record.domainKeyType,jdbcType=TINYINT},
      domain_key_type_desc = #{record.domainKeyTypeDesc,jdbcType=VARCHAR},
      type_id = #{record.typeId,jdbcType=BIGINT},
      type_code = #{record.typeCode,jdbcType=VARCHAR},
      task_name = #{record.taskName,jdbcType=VARCHAR},
      domain_mark = #{record.domainMark,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=TINYINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_name = #{record.createdName,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_name = #{record.updatedName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.iscm.entity.IscmZdtTaskDomain">
    update iscm_zdt_task_domain
    <set>
      <if test="domainKey != null">
        domain_key = #{domainKey,jdbcType=VARCHAR},
      </if>
      <if test="domainKeyType != null">
        domain_key_type = #{domainKeyType,jdbcType=TINYINT},
      </if>
      <if test="domainKeyTypeDesc != null">
        domain_key_type_desc = #{domainKeyTypeDesc,jdbcType=VARCHAR},
      </if>
      <if test="typeId != null">
        type_id = #{typeId,jdbcType=BIGINT},
      </if>
      <if test="typeCode != null">
        type_code = #{typeCode,jdbcType=VARCHAR},
      </if>
      <if test="taskName != null">
        task_name = #{taskName,jdbcType=VARCHAR},
      </if>
      <if test="domainMark != null">
        domain_mark = #{domainMark,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        updated_name = #{updatedName,jdbcType=VARCHAR},
      </if>
      <if test="domainInfo != null">
        domain_info = #{domainInfo,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.cowell.iscm.entity.IscmZdtTaskDomain">
    update iscm_zdt_task_domain
    set domain_key = #{domainKey,jdbcType=VARCHAR},
      domain_key_type = #{domainKeyType,jdbcType=TINYINT},
      domain_key_type_desc = #{domainKeyTypeDesc,jdbcType=VARCHAR},
      type_id = #{typeId,jdbcType=BIGINT},
      type_code = #{typeCode,jdbcType=VARCHAR},
      task_name = #{taskName,jdbcType=VARCHAR},
      domain_mark = #{domainMark,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_name = #{createdName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_name = #{updatedName,jdbcType=VARCHAR},
      domain_info = #{domainInfo,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.iscm.entity.IscmZdtTaskDomain">
    update iscm_zdt_task_domain
    set domain_key = #{domainKey,jdbcType=VARCHAR},
      domain_key_type = #{domainKeyType,jdbcType=TINYINT},
      domain_key_type_desc = #{domainKeyTypeDesc,jdbcType=VARCHAR},
      type_id = #{typeId,jdbcType=BIGINT},
      type_code = #{typeCode,jdbcType=VARCHAR},
      task_name = #{taskName,jdbcType=VARCHAR},
      domain_mark = #{domainMark,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_name = #{createdName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_name = #{updatedName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>