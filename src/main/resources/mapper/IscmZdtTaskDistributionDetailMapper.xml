<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.IscmZdtTaskDistributionDetailMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.IscmZdtTaskDistributionDetail">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="task_id" jdbcType="BIGINT" property="taskId" />
    <result column="business_id" jdbcType="BIGINT" property="businessId" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="purchase_no" jdbcType="VARCHAR" property="purchaseNo" />
    <result column="purchase_sn" jdbcType="VARCHAR" property="purchaseSn" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="sales_attrs" jdbcType="VARCHAR" property="salesAttrs" />
    <result column="goodsline" jdbcType="VARCHAR" property="goodsline" />
    <result column="pushlevel" jdbcType="VARCHAR" property="pushlevel" />
    <result column="specifications" jdbcType="VARCHAR" property="specifications" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="stock" jdbcType="DECIMAL" property="stock" />
    <result column="stock_upper_limit" jdbcType="DECIMAL" property="stockUpperLimit" />
    <result column="stock_lower_limit" jdbcType="DECIMAL" property="stockLowerLimit" />
    <result column="nearly_thirty_days_XL" jdbcType="DECIMAL" property="nearlyThirtyDaysXl" />
    <result column="nearly_thirty_days_KL" jdbcType="DECIMAL" property="nearlyThirtyDaysKl" />
    <result column="purchase_quantity" jdbcType="DECIMAL" property="purchaseQuantity" />
    <result column="approved_quantity" jdbcType="DECIMAL" property="approvedQuantity" />
    <result column="distributed_quantity" jdbcType="DECIMAL" property="distributedQuantity" />
    <result column="subtract_quantity" jdbcType="DECIMAL" property="subtractQuantity" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, task_id, business_id, company_code, store_id, store_code, purchase_no, purchase_sn, 
    goods_no, goods_name, sales_attrs, goodsline, pushlevel, specifications, manufacturer, 
    stock, stock_upper_limit, stock_lower_limit, nearly_thirty_days_XL, nearly_thirty_days_KL, 
    purchase_quantity, approved_quantity, distributed_quantity, subtract_quantity, status, 
    gmt_create, gmt_update, extend, version, created_by, created_name, updated_by, updated_name
  </sql>
  <select id="selectByExample" parameterType="com.cowell.iscm.entity.IscmZdtTaskDistributionDetailExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from iscm_zdt_task_distribution_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from iscm_zdt_task_distribution_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from iscm_zdt_task_distribution_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.iscm.entity.IscmZdtTaskDistributionDetailExample">
    delete from iscm_zdt_task_distribution_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.iscm.entity.IscmZdtTaskDistributionDetail">
    insert into iscm_zdt_task_distribution_detail (id, task_id, business_id, 
      company_code, store_id, store_code, 
      purchase_no, purchase_sn, goods_no, 
      goods_name, sales_attrs, goodsline, 
      pushlevel, specifications, manufacturer, 
      stock, stock_upper_limit, stock_lower_limit, 
      nearly_thirty_days_XL, nearly_thirty_days_KL, 
      purchase_quantity, approved_quantity, distributed_quantity, 
      subtract_quantity, status, gmt_create, 
      gmt_update, extend, version, 
      created_by, created_name, updated_by, 
      updated_name)
    values (#{id,jdbcType=BIGINT}, #{taskId,jdbcType=BIGINT}, #{businessId,jdbcType=BIGINT}, 
      #{companyCode,jdbcType=VARCHAR}, #{storeId,jdbcType=BIGINT}, #{storeCode,jdbcType=VARCHAR}, 
      #{purchaseNo,jdbcType=VARCHAR}, #{purchaseSn,jdbcType=VARCHAR}, #{goodsNo,jdbcType=VARCHAR}, 
      #{goodsName,jdbcType=VARCHAR}, #{salesAttrs,jdbcType=VARCHAR}, #{goodsline,jdbcType=VARCHAR}, 
      #{pushlevel,jdbcType=VARCHAR}, #{specifications,jdbcType=VARCHAR}, #{manufacturer,jdbcType=VARCHAR}, 
      #{stock,jdbcType=DECIMAL}, #{stockUpperLimit,jdbcType=DECIMAL}, #{stockLowerLimit,jdbcType=DECIMAL}, 
      #{nearlyThirtyDaysXl,jdbcType=DECIMAL}, #{nearlyThirtyDaysKl,jdbcType=DECIMAL}, 
      #{purchaseQuantity,jdbcType=DECIMAL}, #{approvedQuantity,jdbcType=DECIMAL}, #{distributedQuantity,jdbcType=DECIMAL}, 
      #{subtractQuantity,jdbcType=DECIMAL}, #{status,jdbcType=TINYINT}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtUpdate,jdbcType=TIMESTAMP}, #{extend,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER}, 
      #{createdBy,jdbcType=BIGINT}, #{createdName,jdbcType=VARCHAR}, #{updatedBy,jdbcType=BIGINT}, 
      #{updatedName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.iscm.entity.IscmZdtTaskDistributionDetail">
    insert into iscm_zdt_task_distribution_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="taskId != null">
        task_id,
      </if>
      <if test="businessId != null">
        business_id,
      </if>
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="storeCode != null">
        store_code,
      </if>
      <if test="purchaseNo != null">
        purchase_no,
      </if>
      <if test="purchaseSn != null">
        purchase_sn,
      </if>
      <if test="goodsNo != null">
        goods_no,
      </if>
      <if test="goodsName != null">
        goods_name,
      </if>
      <if test="salesAttrs != null">
        sales_attrs,
      </if>
      <if test="goodsline != null">
        goodsline,
      </if>
      <if test="pushlevel != null">
        pushlevel,
      </if>
      <if test="specifications != null">
        specifications,
      </if>
      <if test="manufacturer != null">
        manufacturer,
      </if>
      <if test="stock != null">
        stock,
      </if>
      <if test="stockUpperLimit != null">
        stock_upper_limit,
      </if>
      <if test="stockLowerLimit != null">
        stock_lower_limit,
      </if>
      <if test="nearlyThirtyDaysXl != null">
        nearly_thirty_days_XL,
      </if>
      <if test="nearlyThirtyDaysKl != null">
        nearly_thirty_days_KL,
      </if>
      <if test="purchaseQuantity != null">
        purchase_quantity,
      </if>
      <if test="approvedQuantity != null">
        approved_quantity,
      </if>
      <if test="distributedQuantity != null">
        distributed_quantity,
      </if>
      <if test="subtractQuantity != null">
        subtract_quantity,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedName != null">
        updated_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="taskId != null">
        #{taskId,jdbcType=BIGINT},
      </if>
      <if test="businessId != null">
        #{businessId,jdbcType=BIGINT},
      </if>
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="storeCode != null">
        #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="purchaseNo != null">
        #{purchaseNo,jdbcType=VARCHAR},
      </if>
      <if test="purchaseSn != null">
        #{purchaseSn,jdbcType=VARCHAR},
      </if>
      <if test="goodsNo != null">
        #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="salesAttrs != null">
        #{salesAttrs,jdbcType=VARCHAR},
      </if>
      <if test="goodsline != null">
        #{goodsline,jdbcType=VARCHAR},
      </if>
      <if test="pushlevel != null">
        #{pushlevel,jdbcType=VARCHAR},
      </if>
      <if test="specifications != null">
        #{specifications,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="stock != null">
        #{stock,jdbcType=DECIMAL},
      </if>
      <if test="stockUpperLimit != null">
        #{stockUpperLimit,jdbcType=DECIMAL},
      </if>
      <if test="stockLowerLimit != null">
        #{stockLowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="nearlyThirtyDaysXl != null">
        #{nearlyThirtyDaysXl,jdbcType=DECIMAL},
      </if>
      <if test="nearlyThirtyDaysKl != null">
        #{nearlyThirtyDaysKl,jdbcType=DECIMAL},
      </if>
      <if test="purchaseQuantity != null">
        #{purchaseQuantity,jdbcType=DECIMAL},
      </if>
      <if test="approvedQuantity != null">
        #{approvedQuantity,jdbcType=DECIMAL},
      </if>
      <if test="distributedQuantity != null">
        #{distributedQuantity,jdbcType=DECIMAL},
      </if>
      <if test="subtractQuantity != null">
        #{subtractQuantity,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        #{updatedName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.iscm.entity.IscmZdtTaskDistributionDetailExample" resultType="java.lang.Long">
    select count(*) from iscm_zdt_task_distribution_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update iscm_zdt_task_distribution_detail
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.taskId != null">
        task_id = #{record.taskId,jdbcType=BIGINT},
      </if>
      <if test="record.businessId != null">
        business_id = #{record.businessId,jdbcType=BIGINT},
      </if>
      <if test="record.companyCode != null">
        company_code = #{record.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.storeCode != null">
        store_code = #{record.storeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaseNo != null">
        purchase_no = #{record.purchaseNo,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaseSn != null">
        purchase_sn = #{record.purchaseSn,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsNo != null">
        goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsName != null">
        goods_name = #{record.goodsName,jdbcType=VARCHAR},
      </if>
      <if test="record.salesAttrs != null">
        sales_attrs = #{record.salesAttrs,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsline != null">
        goodsline = #{record.goodsline,jdbcType=VARCHAR},
      </if>
      <if test="record.pushlevel != null">
        pushlevel = #{record.pushlevel,jdbcType=VARCHAR},
      </if>
      <if test="record.specifications != null">
        specifications = #{record.specifications,jdbcType=VARCHAR},
      </if>
      <if test="record.manufacturer != null">
        manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="record.stock != null">
        stock = #{record.stock,jdbcType=DECIMAL},
      </if>
      <if test="record.stockUpperLimit != null">
        stock_upper_limit = #{record.stockUpperLimit,jdbcType=DECIMAL},
      </if>
      <if test="record.stockLowerLimit != null">
        stock_lower_limit = #{record.stockLowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="record.nearlyThirtyDaysXl != null">
        nearly_thirty_days_XL = #{record.nearlyThirtyDaysXl,jdbcType=DECIMAL},
      </if>
      <if test="record.nearlyThirtyDaysKl != null">
        nearly_thirty_days_KL = #{record.nearlyThirtyDaysKl,jdbcType=DECIMAL},
      </if>
      <if test="record.purchaseQuantity != null">
        purchase_quantity = #{record.purchaseQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.approvedQuantity != null">
        approved_quantity = #{record.approvedQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.distributedQuantity != null">
        distributed_quantity = #{record.distributedQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.subtractQuantity != null">
        subtract_quantity = #{record.subtractQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedName != null">
        updated_name = #{record.updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update iscm_zdt_task_distribution_detail
    set id = #{record.id,jdbcType=BIGINT},
      task_id = #{record.taskId,jdbcType=BIGINT},
      business_id = #{record.businessId,jdbcType=BIGINT},
      company_code = #{record.companyCode,jdbcType=VARCHAR},
      store_id = #{record.storeId,jdbcType=BIGINT},
      store_code = #{record.storeCode,jdbcType=VARCHAR},
      purchase_no = #{record.purchaseNo,jdbcType=VARCHAR},
      purchase_sn = #{record.purchaseSn,jdbcType=VARCHAR},
      goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      goods_name = #{record.goodsName,jdbcType=VARCHAR},
      sales_attrs = #{record.salesAttrs,jdbcType=VARCHAR},
      goodsline = #{record.goodsline,jdbcType=VARCHAR},
      pushlevel = #{record.pushlevel,jdbcType=VARCHAR},
      specifications = #{record.specifications,jdbcType=VARCHAR},
      manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      stock = #{record.stock,jdbcType=DECIMAL},
      stock_upper_limit = #{record.stockUpperLimit,jdbcType=DECIMAL},
      stock_lower_limit = #{record.stockLowerLimit,jdbcType=DECIMAL},
      nearly_thirty_days_XL = #{record.nearlyThirtyDaysXl,jdbcType=DECIMAL},
      nearly_thirty_days_KL = #{record.nearlyThirtyDaysKl,jdbcType=DECIMAL},
      purchase_quantity = #{record.purchaseQuantity,jdbcType=DECIMAL},
      approved_quantity = #{record.approvedQuantity,jdbcType=DECIMAL},
      distributed_quantity = #{record.distributedQuantity,jdbcType=DECIMAL},
      subtract_quantity = #{record.subtractQuantity,jdbcType=DECIMAL},
      status = #{record.status,jdbcType=TINYINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_name = #{record.createdName,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_name = #{record.updatedName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.iscm.entity.IscmZdtTaskDistributionDetail">
    update iscm_zdt_task_distribution_detail
    <set>
      <if test="taskId != null">
        task_id = #{taskId,jdbcType=BIGINT},
      </if>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=BIGINT},
      </if>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="storeCode != null">
        store_code = #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="purchaseNo != null">
        purchase_no = #{purchaseNo,jdbcType=VARCHAR},
      </if>
      <if test="purchaseSn != null">
        purchase_sn = #{purchaseSn,jdbcType=VARCHAR},
      </if>
      <if test="goodsNo != null">
        goods_no = #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        goods_name = #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="salesAttrs != null">
        sales_attrs = #{salesAttrs,jdbcType=VARCHAR},
      </if>
      <if test="goodsline != null">
        goodsline = #{goodsline,jdbcType=VARCHAR},
      </if>
      <if test="pushlevel != null">
        pushlevel = #{pushlevel,jdbcType=VARCHAR},
      </if>
      <if test="specifications != null">
        specifications = #{specifications,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        manufacturer = #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="stock != null">
        stock = #{stock,jdbcType=DECIMAL},
      </if>
      <if test="stockUpperLimit != null">
        stock_upper_limit = #{stockUpperLimit,jdbcType=DECIMAL},
      </if>
      <if test="stockLowerLimit != null">
        stock_lower_limit = #{stockLowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="nearlyThirtyDaysXl != null">
        nearly_thirty_days_XL = #{nearlyThirtyDaysXl,jdbcType=DECIMAL},
      </if>
      <if test="nearlyThirtyDaysKl != null">
        nearly_thirty_days_KL = #{nearlyThirtyDaysKl,jdbcType=DECIMAL},
      </if>
      <if test="purchaseQuantity != null">
        purchase_quantity = #{purchaseQuantity,jdbcType=DECIMAL},
      </if>
      <if test="approvedQuantity != null">
        approved_quantity = #{approvedQuantity,jdbcType=DECIMAL},
      </if>
      <if test="distributedQuantity != null">
        distributed_quantity = #{distributedQuantity,jdbcType=DECIMAL},
      </if>
      <if test="subtractQuantity != null">
        subtract_quantity = #{subtractQuantity,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        updated_name = #{updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.iscm.entity.IscmZdtTaskDistributionDetail">
    update iscm_zdt_task_distribution_detail
    set task_id = #{taskId,jdbcType=BIGINT},
      business_id = #{businessId,jdbcType=BIGINT},
      company_code = #{companyCode,jdbcType=VARCHAR},
      store_id = #{storeId,jdbcType=BIGINT},
      store_code = #{storeCode,jdbcType=VARCHAR},
      purchase_no = #{purchaseNo,jdbcType=VARCHAR},
      purchase_sn = #{purchaseSn,jdbcType=VARCHAR},
      goods_no = #{goodsNo,jdbcType=VARCHAR},
      goods_name = #{goodsName,jdbcType=VARCHAR},
      sales_attrs = #{salesAttrs,jdbcType=VARCHAR},
      goodsline = #{goodsline,jdbcType=VARCHAR},
      pushlevel = #{pushlevel,jdbcType=VARCHAR},
      specifications = #{specifications,jdbcType=VARCHAR},
      manufacturer = #{manufacturer,jdbcType=VARCHAR},
      stock = #{stock,jdbcType=DECIMAL},
      stock_upper_limit = #{stockUpperLimit,jdbcType=DECIMAL},
      stock_lower_limit = #{stockLowerLimit,jdbcType=DECIMAL},
      nearly_thirty_days_XL = #{nearlyThirtyDaysXl,jdbcType=DECIMAL},
      nearly_thirty_days_KL = #{nearlyThirtyDaysKl,jdbcType=DECIMAL},
      purchase_quantity = #{purchaseQuantity,jdbcType=DECIMAL},
      approved_quantity = #{approvedQuantity,jdbcType=DECIMAL},
      distributed_quantity = #{distributedQuantity,jdbcType=DECIMAL},
      subtract_quantity = #{subtractQuantity,jdbcType=DECIMAL},
      status = #{status,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_name = #{createdName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_name = #{updatedName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>