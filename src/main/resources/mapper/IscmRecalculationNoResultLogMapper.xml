<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.IscmRecalculationNoResultLogMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.IscmRecalculationNoResultLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="apply_no" jdbcType="VARCHAR" property="applyNo" />
    <result column="apply_line" jdbcType="VARCHAR" property="applyLine" />
    <result column="apply_date" jdbcType="DATE" property="applyDate" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="apply_stock" jdbcType="DECIMAL" property="applyStock" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, apply_no, apply_line, apply_date, reason, apply_stock
  </sql>
  <select id="selectByExample" parameterType="com.cowell.iscm.entity.IscmRecalculationNoResultLogExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from iscm_recalculation_no_result_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from iscm_recalculation_no_result_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from iscm_recalculation_no_result_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.iscm.entity.IscmRecalculationNoResultLogExample">
    delete from iscm_recalculation_no_result_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entity.IscmRecalculationNoResultLog" useGeneratedKeys="true">
    insert into iscm_recalculation_no_result_log (apply_no, apply_line, apply_date, 
      reason, apply_stock)
    values (#{applyNo,jdbcType=VARCHAR}, #{applyLine,jdbcType=VARCHAR}, #{applyDate,jdbcType=DATE}, 
      #{reason,jdbcType=VARCHAR}, #{applyStock,jdbcType=DECIMAL})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entity.IscmRecalculationNoResultLog" useGeneratedKeys="true">
    insert into iscm_recalculation_no_result_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="applyNo != null">
        apply_no,
      </if>
      <if test="applyLine != null">
        apply_line,
      </if>
      <if test="applyDate != null">
        apply_date,
      </if>
      <if test="reason != null">
        reason,
      </if>
      <if test="applyStock != null">
        apply_stock,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="applyNo != null">
        #{applyNo,jdbcType=VARCHAR},
      </if>
      <if test="applyLine != null">
        #{applyLine,jdbcType=VARCHAR},
      </if>
      <if test="applyDate != null">
        #{applyDate,jdbcType=DATE},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="applyStock != null">
        #{applyStock,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.iscm.entity.IscmRecalculationNoResultLogExample" resultType="java.lang.Long">
    select count(*) from iscm_recalculation_no_result_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update iscm_recalculation_no_result_log
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.applyNo != null">
        apply_no = #{record.applyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.applyLine != null">
        apply_line = #{record.applyLine,jdbcType=VARCHAR},
      </if>
      <if test="record.applyDate != null">
        apply_date = #{record.applyDate,jdbcType=DATE},
      </if>
      <if test="record.reason != null">
        reason = #{record.reason,jdbcType=VARCHAR},
      </if>
      <if test="record.applyStock != null">
        apply_stock = #{record.applyStock,jdbcType=DECIMAL},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update iscm_recalculation_no_result_log
    set id = #{record.id,jdbcType=BIGINT},
      apply_no = #{record.applyNo,jdbcType=VARCHAR},
      apply_line = #{record.applyLine,jdbcType=VARCHAR},
      apply_date = #{record.applyDate,jdbcType=DATE},
      reason = #{record.reason,jdbcType=VARCHAR},
      apply_stock = #{record.applyStock,jdbcType=DECIMAL}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.iscm.entity.IscmRecalculationNoResultLog">
    update iscm_recalculation_no_result_log
    <set>
      <if test="applyNo != null">
        apply_no = #{applyNo,jdbcType=VARCHAR},
      </if>
      <if test="applyLine != null">
        apply_line = #{applyLine,jdbcType=VARCHAR},
      </if>
      <if test="applyDate != null">
        apply_date = #{applyDate,jdbcType=DATE},
      </if>
      <if test="reason != null">
        reason = #{reason,jdbcType=VARCHAR},
      </if>
      <if test="applyStock != null">
        apply_stock = #{applyStock,jdbcType=DECIMAL},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.iscm.entity.IscmRecalculationNoResultLog">
    update iscm_recalculation_no_result_log
    set apply_no = #{applyNo,jdbcType=VARCHAR},
      apply_line = #{applyLine,jdbcType=VARCHAR},
      apply_date = #{applyDate,jdbcType=DATE},
      reason = #{reason,jdbcType=VARCHAR},
      apply_stock = #{applyStock,jdbcType=DECIMAL}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>