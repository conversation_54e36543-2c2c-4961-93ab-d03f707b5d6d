<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.IscmSeasonalGoodsPlatformMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.IscmSeasonalGoodsPlatform">
    <id column="Id" jdbcType="BIGINT" property="id" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
    <result column="season_name" jdbcType="VARCHAR" property="seasonName" />
    <result column="season_start_md" jdbcType="VARCHAR" property="seasonStartMd" />
    <result column="season_end_md" jdbcType="VARCHAR" property="seasonEndMd" />
    <result column="stock_start" jdbcType="VARCHAR" property="stockStart" />
    <result column="stock_end" jdbcType="VARCHAR" property="stockEnd" />
    <result column="start_date" jdbcType="DATE" property="startDate" />
    <result column="end_date" jdbcType="DATE" property="endDate" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="goods_unit" jdbcType="VARCHAR" property="goodsUnit" />
    <result column="specifications" jdbcType="VARCHAR" property="specifications" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="ingredients" jdbcType="VARCHAR" property="ingredients" />
    <result column="platform_org_id" jdbcType="BIGINT" property="platformOrgId" />
    <result column="platform_name" jdbcType="VARCHAR" property="platformName" />
    <result column="platform_sapcode" jdbcType="VARCHAR" property="platformSapcode" />
    <result column="goods_class_max_id" jdbcType="VARCHAR" property="goodsClassMaxId" />
    <result column="goods_class_mid_id" jdbcType="VARCHAR" property="goodsClassMidId" />
    <result column="goods_class_min_id" jdbcType="VARCHAR" property="goodsClassMinId" />
    <result column="goods_class_max_name" jdbcType="VARCHAR" property="goodsClassMaxName" />
    <result column="goods_class_mid_name" jdbcType="VARCHAR" property="goodsClassMidName" />
    <result column="goods_class_min_name" jdbcType="VARCHAR" property="goodsClassMinName" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="core_flag" jdbcType="TINYINT" property="coreFlag" />
    <result column="display_flag" jdbcType="TINYINT" property="displayFlag" />
    <result column="span_year_flag" jdbcType="TINYINT" property="spanYearFlag" />
    <result column="void_date" jdbcType="DATE" property="voidDate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    Id, `status`, gmt_create, gmt_update, extend, version, created_by, created_name,
    updated_by, updated_name, season_name, season_start_md, season_end_md, stock_start,
    stock_end, start_date, end_date, goods_no, goods_name, goods_unit, specifications,
    manufacturer, ingredients, platform_org_id, platform_name,
    platform_sapcode, goods_class_max_id, goods_class_mid_id, goods_class_min_id, goods_class_max_name,
    goods_class_mid_name, goods_class_min_name, description, core_flag, display_flag, span_year_flag, void_date
  </sql>
  <select id="selectByExample" parameterType="com.cowell.iscm.entity.IscmSeasonalGoodsPlatformExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from iscm_seasonal_goods_platform
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from iscm_seasonal_goods_platform
    where Id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from iscm_seasonal_goods_platform
    where Id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.iscm.entity.IscmSeasonalGoodsPlatformExample">
    delete from iscm_seasonal_goods_platform
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="Id" keyProperty="id" parameterType="com.cowell.iscm.entity.IscmSeasonalGoodsPlatform" useGeneratedKeys="true">
    insert into iscm_seasonal_goods_platform (
      gmt_create, gmt_update, extend, created_by,
      created_name, updated_by, updated_name, season_name,
      season_start_md, season_end_md, stock_start, stock_end, start_date,
      end_date, goods_no, goods_name, goods_unit, specifications, manufacturer,
      ingredients, platform_org_id, platform_name,
      platform_sapcode, goods_class_max_id, goods_class_mid_id,
      goods_class_min_id, goods_class_max_name, goods_class_mid_name,
      goods_class_min_name, core_flag, display_flag,
      span_year_flag, void_date)
    values (
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}, #{extend,jdbcType=VARCHAR}, #{createdBy,jdbcType=BIGINT},
      #{createdName,jdbcType=VARCHAR}, #{updatedBy,jdbcType=BIGINT}, #{updatedName,jdbcType=VARCHAR}, #{seasonName,jdbcType=VARCHAR},
      #{seasonStartMd,jdbcType=VARCHAR}, #{seasonEndMd,jdbcType=VARCHAR}, #{stockStart,jdbcType=VARCHAR}, #{stockEnd,jdbcType=VARCHAR}, #{startDate,jdbcType=DATE},
      #{endDate,jdbcType=DATE}, #{goodsNo,jdbcType=VARCHAR}, #{goodsName,jdbcType=VARCHAR}, #{goodsUnit,jdbcType=VARCHAR}, #{specifications,jdbcType=VARCHAR}, #{manufacturer,jdbcType=VARCHAR},
      #{ingredients,jdbcType=VARCHAR}, #{platformOrgId,jdbcType=BIGINT}, #{platformName,jdbcType=VARCHAR},
      #{platformSapcode,jdbcType=VARCHAR}, #{goodsClassMaxId,jdbcType=BIGINT}, #{goodsClassMidId,jdbcType=BIGINT},
      #{goodsClassMinId,jdbcType=BIGINT}, #{goodsClassMaxName,jdbcType=VARCHAR}, #{goodsClassMidName,jdbcType=VARCHAR},
      #{goodsClassMinName,jdbcType=VARCHAR}, #{coreFlag,jdbcType=TINYINT}, #{displayFlag,jdbcType=TINYINT},
      #{spanYearFlag,jdbcType=TINYINT}, #{voidDate,jdbcType=DATE}
      )
  </insert>

  <insert id="batchInsert" parameterType="list" >
    insert into iscm_seasonal_goods_platform (created_by,
          created_name, updated_by, updated_name,
          season_name, season_start_md, season_end_md,
          stock_start, stock_end, start_date,
          end_date, goods_no, goods_name,
          goods_unit, specifications, manufacturer,
          ingredients, platform_org_id, platform_name,
          platform_sapcode, goods_class_max_id, goods_class_mid_id,
          goods_class_min_id, goods_class_max_name, goods_class_mid_name,
          goods_class_min_name, description, core_flag, display_flag,
          span_year_flag, void_date)
    values
    <foreach collection="list" item="item" index="index" separator="," >
      (
        #{item.createdBy,jdbcType=BIGINT},
        #{item.createdName,jdbcType=VARCHAR}, #{item.updatedBy,jdbcType=BIGINT}, #{item.updatedName,jdbcType=VARCHAR},
        #{item.seasonName,jdbcType=VARCHAR}, #{item.seasonStartMd,jdbcType=VARCHAR}, #{item.seasonEndMd,jdbcType=VARCHAR},
        #{item.stockStart,jdbcType=VARCHAR}, #{item.stockEnd,jdbcType=VARCHAR}, #{item.startDate,jdbcType=DATE},
        #{item.endDate,jdbcType=DATE}, #{item.goodsNo,jdbcType=VARCHAR}, #{item.goodsName,jdbcType=VARCHAR},
        #{item.goodsUnit,jdbcType=VARCHAR}, #{item.specifications,jdbcType=VARCHAR}, #{item.manufacturer,jdbcType=VARCHAR},
        #{item.ingredients,jdbcType=VARCHAR}, #{item.platformOrgId,jdbcType=BIGINT}, #{item.platformName,jdbcType=VARCHAR},
        #{item.platformSapcode,jdbcType=VARCHAR}, #{item.goodsClassMaxId,jdbcType=BIGINT}, #{item.goodsClassMidId,jdbcType=BIGINT},
        #{item.goodsClassMinId,jdbcType=BIGINT}, #{item.goodsClassMaxName,jdbcType=VARCHAR}, #{item.goodsClassMidName,jdbcType=VARCHAR},
        #{item.goodsClassMinName,jdbcType=VARCHAR},#{item.description,jdbcType=VARCHAR}, #{item.coreFlag,jdbcType=TINYINT}, #{item.displayFlag,jdbcType=TINYINT},
        #{item.spanYearFlag,jdbcType=TINYINT}, #{item.voidDate,jdbcType=DATE}
      )
    </foreach>
  </insert>

  <insert id="insertSelective" keyColumn="Id" keyProperty="id" parameterType="com.cowell.iscm.entity.IscmSeasonalGoodsPlatform" useGeneratedKeys="true">
    insert into iscm_seasonal_goods_platform
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="status != null">
        `status`,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedName != null">
        updated_name,
      </if>
      <if test="seasonName != null">
        season_name,
      </if>
      <if test="seasonStartMd != null">
        season_start_md,
      </if>
      <if test="seasonEndMd != null">
        season_end_md,
      </if>
      <if test="stockStart != null">
        stock_start,
      </if>
      <if test="stockEnd != null">
        stock_end,
      </if>
      <if test="startDate != null">
        start_date,
      </if>
      <if test="endDate != null">
        end_date,
      </if>
      <if test="goodsNo != null">
        goods_no,
      </if>
      <if test="goodsName != null">
        goods_name,
      </if>
      <if test="goodsUnit != null">
        goods_unit,
      </if>
      <if test="specifications != null">
        specifications,
      </if>
      <if test="manufacturer != null">
        manufacturer,
      </if>
      <if test="ingredients != null">
        ingredients,
      </if>
      <if test="platformOrgId != null">
        platform_org_id,
      </if>
      <if test="platformName != null">
        platform_name,
      </if>
      <if test="platformSapcode != null">
        platform_sapcode,
      </if>
      <if test="goodsClassMaxId != null">
        goods_class_max_id,
      </if>
      <if test="goodsClassMidId != null">
        goods_class_mid_id,
      </if>
      <if test="goodsClassMinId != null">
        goods_class_min_id,
      </if>
      <if test="goodsClassMaxName != null">
        goods_class_max_name,
      </if>
      <if test="goodsClassMidName != null">
        goods_class_mid_name,
      </if>
      <if test="goodsClassMinName != null">
        goods_class_min_name,
      </if>
      <if test="coreFlag != null">
        core_flag,
      </if>
      <if test="displayFlag != null">
        display_flag,
      </if>
      <if test="spanYearFlag != null">
        span_year_flag,
      </if>
      <if test="voidDate != null">
        void_date,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        #{updatedName,jdbcType=VARCHAR},
      </if>
      <if test="seasonName != null">
        #{seasonName,jdbcType=VARCHAR},
      </if>
      <if test="seasonStartMd != null">
        #{seasonStartMd,jdbcType=VARCHAR},
      </if>
      <if test="seasonEndMd != null">
        #{seasonEndMd,jdbcType=VARCHAR},
      </if>
      <if test="stockStart != null">
        #{stockStart,jdbcType=VARCHAR},
      </if>
      <if test="stockEnd != null">
        #{stockEnd,jdbcType=VARCHAR},
      </if>
      <if test="startDate != null">
        #{startDate,jdbcType=DATE},
      </if>
      <if test="endDate != null">
        #{endDate,jdbcType=DATE},
      </if>
      <if test="goodsNo != null">
        #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="goodsUnit != null">
        #{goodsUnit,jdbcType=VARCHAR},
      </if>
      <if test="specifications != null">
        #{specifications,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="ingredients != null">
        #{ingredients,jdbcType=VARCHAR},
      </if>
      <if test="addFlag != null">
        #{addFlag,jdbcType=TINYINT},
      </if>
      <if test="endFlag != null">
        #{endFlag,jdbcType=TINYINT},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=TINYINT},
      </if>
      <if test="platformOrgId != null">
        #{platformOrgId,jdbcType=BIGINT},
      </if>
      <if test="platformName != null">
        #{platformName,jdbcType=VARCHAR},
      </if>
      <if test="platformSapcode != null">
        #{platformSapcode,jdbcType=VARCHAR},
      </if>
      <if test="goodsClassMaxId != null">
        #{goodsClassMaxId,jdbcType=BIGINT},
      </if>
      <if test="goodsClassMidId != null">
        #{goodsClassMidId,jdbcType=BIGINT},
      </if>
      <if test="goodsClassMinId != null">
        #{goodsClassMinId,jdbcType=BIGINT},
      </if>
      <if test="goodsClassMaxName != null">
        #{goodsClassMaxName,jdbcType=VARCHAR},
      </if>
      <if test="goodsClassMidName != null">
        #{goodsClassMidName,jdbcType=VARCHAR},
      </if>
      <if test="goodsClassMinName != null">
        #{goodsClassMinName,jdbcType=VARCHAR},
      </if>
      <if test="coreFlag != null">
        #{coreFlag,jdbcType=TINYINT},
      </if>
      <if test="displayFlag != null">
        #{displayFlag,jdbcType=TINYINT},
      </if>
      <if test="spanYearFlag != null">
        #{spanYearFlag,jdbcType=TINYINT},
      </if>
      <if test="voidDate != null">
        #{voidDate,jdbcType=DATE}
      </if>

    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.iscm.entity.IscmSeasonalGoodsPlatformExample" resultType="java.lang.Long">
    select count(*) from iscm_seasonal_goods_platform
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update iscm_seasonal_goods_platform
    <set>
      <if test="record.id != null">
        Id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedName != null">
        updated_name = #{record.updatedName,jdbcType=VARCHAR},
      </if>
      <if test="record.seasonName != null">
        season_name = #{record.seasonName,jdbcType=VARCHAR},
      </if>
      <if test="record.seasonStartMd != null">
        season_start_md = #{record.seasonStartMd,jdbcType=VARCHAR},
      </if>
      <if test="record.seasonEndMd != null">
        season_end_md = #{record.seasonEndMd,jdbcType=VARCHAR},
      </if>
      <if test="record.stockStart != null">
        stock_start = #{record.stockStart,jdbcType=VARCHAR},
      </if>
      <if test="record.stockEnd != null">
        stock_end = #{record.stockEnd,jdbcType=VARCHAR},
      </if>
      <if test="record.startDate != null">
        start_date = #{record.startDate,jdbcType=DATE},
      </if>
      <if test="record.endDate != null">
        end_date = #{record.endDate,jdbcType=DATE},
      </if>
      <if test="record.goodsNo != null">
        goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsName != null">
        goods_name = #{record.goodsName,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsUnit != null">
        goods_unit = #{record.goodsUnit,jdbcType=VARCHAR},
      </if>
      <if test="record.specifications != null">
        specifications = #{record.specifications,jdbcType=VARCHAR},
      </if>
      <if test="record.manufacturer != null">
        manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="record.ingredients != null">
        ingredients = #{record.ingredients,jdbcType=VARCHAR},
      </if>
      <if test="record.platformOrgId != null">
        platform_org_id = #{record.platformOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.platformName != null">
        platform_name = #{record.platformName,jdbcType=VARCHAR},
      </if>
      <if test="record.platformSapcode != null">
        platform_sapcode = #{record.platformSapcode,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsClassMaxId != null">
        goods_class_max_id = #{record.goodsClassMaxId,jdbcType=BIGINT},
      </if>
      <if test="record.goodsClassMidId != null">
        goods_class_mid_id = #{record.goodsClassMidId,jdbcType=BIGINT},
      </if>
      <if test="record.goodsClassMinId != null">
        goods_class_min_id = #{record.goodsClassMinId,jdbcType=BIGINT},
      </if>
      <if test="record.goodsClassMaxName != null">
        goods_class_max_name = #{record.goodsClassMaxName,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsClassMidName != null">
        goods_class_mid_name = #{record.goodsClassMidName,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsClassMinName != null">
        goods_class_min_name = #{record.goodsClassMinName,jdbcType=VARCHAR},
      </if>
      <if test="coreFlag != null">
        core_flag = #{record.coreFlag,jdbcType=TINYINT},
      </if>
      <if test="displayFlag != null">
        display_flag = #{record.displayFlag,jdbcType=TINYINT},
      </if>
      <if test="spanYearFlag != null">
        span_year_flag = #{record.spanYearFlag,jdbcType=TINYINT},
      </if>
      <if test="voidDate != null">
        void_date = #{record.voidDate,jdbcType=DATE}
      </if>

    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update iscm_seasonal_goods_platform
    set Id = #{record.id,jdbcType=BIGINT},
      `status` = #{record.status,jdbcType=TINYINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_name = #{record.createdName,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_name = #{record.updatedName,jdbcType=VARCHAR},
      season_name = #{record.seasonName,jdbcType=VARCHAR},
      season_start_md = #{record.seasonStartMd,jdbcType=VARCHAR},
      season_end_md = #{record.seasonEndMd,jdbcType=VARCHAR},
      stock_start = #{record.stockStart,jdbcType=VARCHAR},
      stock_end = #{record.stockEnd,jdbcType=VARCHAR},
      start_date = #{record.startDate,jdbcType=DATE},
      end_date = #{record.endDate,jdbcType=DATE},
      goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      goods_name = #{record.goodsName,jdbcType=VARCHAR},
      goods_unit = #{record.goodsUnit,jdbcType=VARCHAR},
      specifications = #{record.specifications,jdbcType=VARCHAR},
      manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      ingredients = #{record.ingredients,jdbcType=VARCHAR},
      platform_org_id = #{record.platformOrgId,jdbcType=BIGINT},
      platform_name = #{record.platformName,jdbcType=VARCHAR},
      platform_sapcode = #{record.platformSapcode,jdbcType=VARCHAR},
      goods_class_max_id = #{record.goodsClassMaxId,jdbcType=BIGINT},
      goods_class_mid_id = #{record.goodsClassMidId,jdbcType=BIGINT},
      goods_class_min_id = #{record.goodsClassMinId,jdbcType=BIGINT},
      goods_class_max_name = #{record.goodsClassMaxName,jdbcType=VARCHAR},
      goods_class_mid_name = #{record.goodsClassMidName,jdbcType=VARCHAR},
      goods_class_min_name = #{record.goodsClassMinName,jdbcType=VARCHAR},
      core_flag = #{record.coreFlag,jdbcType=TINYINT},
      display_flag = #{record.displayFlag,jdbcType=TINYINT},
      span_year_flag = #{record.spanYearFlag,jdbcType=TINYINT},
      void_date = #{record.voidDate,jdbcType=DATE}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.iscm.entity.IscmSeasonalGoodsPlatform">
    update iscm_seasonal_goods_platform
    <set>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        updated_name = #{updatedName,jdbcType=VARCHAR},
      </if>
      <if test="seasonName != null">
        season_name = #{seasonName,jdbcType=VARCHAR},
      </if>
      <if test="seasonStartMd != null">
        season_start_md = #{seasonStartMd,jdbcType=VARCHAR},
      </if>
      <if test="seasonEndMd != null">
        season_end_md = #{seasonEndMd,jdbcType=VARCHAR},
      </if>
      <if test="stockStart != null">
        stock_start = #{stockStart,jdbcType=VARCHAR},
      </if>
      <if test="stockEnd != null">
        stock_end = #{stockEnd,jdbcType=VARCHAR},
      </if>
      <if test="startDate != null">
        start_date = #{startDate,jdbcType=DATE},
      </if>
      <if test="endDate != null">
        end_date = #{endDate,jdbcType=DATE},
      </if>
      <if test="goodsNo != null">
        goods_no = #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        goods_name = #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="goodsUnit != null">
        goods_unit = #{goodsUnit,jdbcType=VARCHAR},
      </if>
      <if test="specifications != null">
        specifications = #{specifications,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        manufacturer = #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="ingredients != null">
        ingredients = #{ingredients,jdbcType=VARCHAR},
      </if>
      <if test="platformOrgId != null">
        platform_org_id = #{platformOrgId,jdbcType=BIGINT},
      </if>
      <if test="platformName != null">
        platform_name = #{platformName,jdbcType=VARCHAR},
      </if>
      <if test="platformSapcode != null">
        platform_sapcode = #{platformSapcode,jdbcType=VARCHAR},
      </if>
      <if test="goodsClassMaxId != null">
        goods_class_max_id = #{goodsClassMaxId,jdbcType=BIGINT},
      </if>
      <if test="goodsClassMidId != null">
        goods_class_mid_id = #{goodsClassMidId,jdbcType=BIGINT},
      </if>
      <if test="goodsClassMinId != null">
        goods_class_min_id = #{goodsClassMinId,jdbcType=BIGINT},
      </if>
      <if test="goodsClassMaxName != null">
        goods_class_max_name = #{goodsClassMaxName,jdbcType=VARCHAR},
      </if>
      <if test="goodsClassMidName != null">
        goods_class_mid_name = #{goodsClassMidName,jdbcType=VARCHAR},
      </if>
      <if test="goodsClassMinName != null">
        goods_class_min_name = #{goodsClassMinName,jdbcType=VARCHAR},
      </if>
      <if test="coreFlag != null">
        core_flag = #{coreFlag,jdbcType=TINYINT},
      </if>
      <if test="displayFlag != null">
        display_flag = #{displayFlag,jdbcType=TINYINT},
      </if>

      <if test="spanYearFlag != null">
        span_year_flag = #{spanYearFlag,jdbcType=TINYINT},
      </if>
      <if test="voidDate != null">
        void_date = #{voidDate,jdbcType=DATE},
      </if>
    </set>
    where Id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.iscm.entity.IscmSeasonalGoodsPlatform">
    update iscm_seasonal_goods_platform
    set `status` = #{status,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_name = #{createdName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_name = #{updatedName,jdbcType=VARCHAR},
      season_name = #{seasonName,jdbcType=VARCHAR},
      season_start_md = #{seasonStartMd,jdbcType=VARCHAR},
      season_end_md = #{seasonEndMd,jdbcType=VARCHAR},
      stock_start = #{stockStart,jdbcType=VARCHAR},
      stock_end = #{stockEnd,jdbcType=VARCHAR},
      start_date = #{startDate,jdbcType=DATE},
      end_date = #{endDate,jdbcType=DATE},
      goods_no = #{goodsNo,jdbcType=VARCHAR},
      goods_name = #{goodsName,jdbcType=VARCHAR},
      goods_unit = #{goodsUnit,jdbcType=VARCHAR},
      specifications = #{specifications,jdbcType=VARCHAR},
      manufacturer = #{manufacturer,jdbcType=VARCHAR},
      ingredients = #{ingredients,jdbcType=VARCHAR},
      platform_org_id = #{platformOrgId,jdbcType=BIGINT},
      platform_name = #{platformName,jdbcType=VARCHAR},
      platform_sapcode = #{platformSapcode,jdbcType=VARCHAR},
      goods_class_max_id = #{goodsClassMaxId,jdbcType=BIGINT},
      goods_class_mid_id = #{goodsClassMidId,jdbcType=BIGINT},
      goods_class_min_id = #{goodsClassMinId,jdbcType=BIGINT},
      goods_class_max_name = #{goodsClassMaxName,jdbcType=VARCHAR},
      goods_class_mid_name = #{goodsClassMidName,jdbcType=VARCHAR},
      goods_class_min_name = #{goodsClassMinName,jdbcType=VARCHAR},
      core_flag = #{coreFlag,jdbcType=TINYINT},
      display_flag = #{displayFlag,jdbcType=TINYINT},
      span_year_flag = #{spanYearFlag,jdbcType=TINYINT},
      void_date = #{voidDate,jdbcType=DATE}
    where Id = #{id,jdbcType=BIGINT}
  </update>

  <update id="updateDelFlagByIds" >
    update iscm_seasonal_goods_platform set status = -1, void_date = NOW() where id in
    <foreach collection="array" index="index" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
  </update>

  <update id="updateDelFlagByOrgId" >
      update iscm_seasonal_goods_platform set status = -1, void_date = NOW()
       where platform_org_id = #{orgId}
         and
         <trim prefix="(" prefixOverrides="and" suffix=")">
                 status = 0
           <if test="seasonName != null and seasonName !=''">
             and season_name like #{seasonName,jdbcType=VARCHAR}
           </if>
           <if test="goodsNo != null and goodsNo != ''">
             and goods_no = #{goodsNo,jdbcType=VARCHAR}
           </if>
           <if test="ingredients != null and ingredients != ''">
             and ingredients like #{ingredients,jdbcType=VARCHAR}
           </if>
           <if test="seasonStartMD != null and seasonStartMD != ''">
             and STR_TO_DATE(CONCAT('1900-', season_start_md), '%Y-%m-%d') between #{seasonStartMD} and #{seasonEndMD}
           </if>
         </trim>
  </update>

  <select id="countByQuertParam" resultType="java.lang.Long">
    select count(*) from iscm_seasonal_goods_platform
    where platform_org_id = #{orgId}
      and
    <trim prefix="(" prefixOverrides="and" suffix=")">
      status = 0
      <if test="seasonName != null and seasonName !=''">
        and season_name like #{seasonName,jdbcType=VARCHAR}
      </if>
      <if test="goodsNo != null and goodsNo != ''">
        and goods_no = #{goodsNo,jdbcType=VARCHAR}
      </if>
      <if test="ingredients != null and ingredients != ''">
        and ingredients like #{ingredients,jdbcType=VARCHAR}
      </if>
      <if test="seasonStartMD != null and seasonStartMD != ''">
        and STR_TO_DATE(CONCAT('1900-', season_start_md), '%Y-%m-%d') between #{seasonStartMD} and #{seasonEndMD}
      </if>
    </trim>
  </select>

  <select id="selectByQueryParam" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from iscm_seasonal_goods_platform
    where platform_org_id = #{orgId}
    and
    <trim prefix="(" prefixOverrides="and" suffix=")">
      status = 0
      <if test="seasonName != null and seasonName !=''">
        and season_name like #{seasonName,jdbcType=VARCHAR}
      </if>
      <if test="goodsNo != null and goodsNo != ''">
        and goods_no = #{goodsNo,jdbcType=VARCHAR}
      </if>
      <if test="ingredients != null and ingredients != ''">
        and ingredients like #{ingredients,jdbcType=VARCHAR}
      </if>
      <if test="seasonStartMD != null and seasonStartMD != ''">
        and STR_TO_DATE(CONCAT('1900-', season_start_md), '%Y-%m-%d') between #{seasonStartMD} and #{seasonEndMD}
      </if>
    </trim>
    <if test="pageNo != null and pageSize != null">
        limit #{pageNo}, #{pageSize}
    </if>
  </select>
</mapper>
