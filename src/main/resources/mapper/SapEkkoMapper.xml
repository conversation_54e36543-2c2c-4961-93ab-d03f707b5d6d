<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.SapEkkoMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.SapEkko">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="MANDT" jdbcType="VARCHAR" property="mandt" />
    <result column="EBELN" jdbcType="VARCHAR" property="ebeln" />
    <result column="BUKRS" jdbcType="VARCHAR" property="bukrs" />
    <result column="BSTYP" jdbcType="VARCHAR" property="bstyp" />
    <result column="BSART" jdbcType="VARCHAR" property="bsart" />
    <result column="BSAKZ" jdbcType="VARCHAR" property="bsakz" />
    <result column="LOEKZ" jdbcType="VARCHAR" property="loekz" />
    <result column="STATU" jdbcType="VARCHAR" property="statu" />
    <result column="AEDAT" jdbcType="VARCHAR" property="aedat" />
    <result column="ERNAM" jdbcType="VARCHAR" property="ernam" />
    <result column="LASTCHANGEDATETIME" jdbcType="DECIMAL" property="lastchangedatetime" />
    <result column="PINCR" jdbcType="VARCHAR" property="pincr" />
    <result column="LPONR" jdbcType="VARCHAR" property="lponr" />
    <result column="LIFNR" jdbcType="VARCHAR" property="lifnr" />
    <result column="SPRAS" jdbcType="VARCHAR" property="spras" />
    <result column="ZTERM" jdbcType="VARCHAR" property="zterm" />
    <result column="ZBD1T" jdbcType="DECIMAL" property="zbd1t" />
    <result column="ZBD2T" jdbcType="DECIMAL" property="zbd2t" />
    <result column="ZBD3T" jdbcType="DECIMAL" property="zbd3t" />
    <result column="ZBD1P" jdbcType="DECIMAL" property="zbd1p" />
    <result column="ZBD2P" jdbcType="DECIMAL" property="zbd2p" />
    <result column="EKORG" jdbcType="VARCHAR" property="ekorg" />
    <result column="EKGRP" jdbcType="VARCHAR" property="ekgrp" />
    <result column="WAERS" jdbcType="VARCHAR" property="waers" />
    <result column="WKURS" jdbcType="DECIMAL" property="wkurs" />
    <result column="KUFIX" jdbcType="VARCHAR" property="kufix" />
    <result column="BEDAT" jdbcType="VARCHAR" property="bedat" />
    <result column="KDATB" jdbcType="VARCHAR" property="kdatb" />
    <result column="KDATE" jdbcType="VARCHAR" property="kdate" />
    <result column="BWBDT" jdbcType="VARCHAR" property="bwbdt" />
    <result column="ANGDT" jdbcType="VARCHAR" property="angdt" />
    <result column="BNDDT" jdbcType="VARCHAR" property="bnddt" />
    <result column="GWLDT" jdbcType="VARCHAR" property="gwldt" />
    <result column="AUSNR" jdbcType="VARCHAR" property="ausnr" />
    <result column="ANGNR" jdbcType="VARCHAR" property="angnr" />
    <result column="IHRAN" jdbcType="VARCHAR" property="ihran" />
    <result column="IHREZ" jdbcType="VARCHAR" property="ihrez" />
    <result column="VERKF" jdbcType="VARCHAR" property="verkf" />
    <result column="TELF1" jdbcType="VARCHAR" property="telf1" />
    <result column="LLIEF" jdbcType="VARCHAR" property="llief" />
    <result column="KUNNR" jdbcType="VARCHAR" property="kunnr" />
    <result column="KONNR" jdbcType="VARCHAR" property="konnr" />
    <result column="ABGRU" jdbcType="VARCHAR" property="abgru" />
    <result column="AUTLF" jdbcType="VARCHAR" property="autlf" />
    <result column="WEAKT" jdbcType="VARCHAR" property="weakt" />
    <result column="RESWK" jdbcType="VARCHAR" property="reswk" />
    <result column="LBLIF" jdbcType="VARCHAR" property="lblif" />
    <result column="INCO1" jdbcType="VARCHAR" property="inco1" />
    <result column="INCO2" jdbcType="VARCHAR" property="inco2" />
    <result column="KTWRT" jdbcType="DECIMAL" property="ktwrt" />
    <result column="SUBMI" jdbcType="VARCHAR" property="submi" />
    <result column="KNUMV" jdbcType="VARCHAR" property="knumv" />
    <result column="KALSM" jdbcType="VARCHAR" property="kalsm" />
    <result column="STAFO" jdbcType="VARCHAR" property="stafo" />
    <result column="LIFRE" jdbcType="VARCHAR" property="lifre" />
    <result column="EXNUM" jdbcType="VARCHAR" property="exnum" />
    <result column="UNSEZ" jdbcType="VARCHAR" property="unsez" />
    <result column="LOGSY" jdbcType="VARCHAR" property="logsy" />
    <result column="UPINC" jdbcType="VARCHAR" property="upinc" />
    <result column="STAKO" jdbcType="VARCHAR" property="stako" />
    <result column="FRGGR" jdbcType="VARCHAR" property="frggr" />
    <result column="FRGSX" jdbcType="VARCHAR" property="frgsx" />
    <result column="FRGKE" jdbcType="VARCHAR" property="frgke" />
    <result column="FRGZU" jdbcType="VARCHAR" property="frgzu" />
    <result column="FRGRL" jdbcType="VARCHAR" property="frgrl" />
    <result column="LANDS" jdbcType="VARCHAR" property="lands" />
    <result column="LPHIS" jdbcType="VARCHAR" property="lphis" />
    <result column="ADRNR" jdbcType="VARCHAR" property="adrnr" />
    <result column="STCEG_L" jdbcType="VARCHAR" property="stcegL" />
    <result column="STCEG" jdbcType="VARCHAR" property="stceg" />
    <result column="ABSGR" jdbcType="VARCHAR" property="absgr" />
    <result column="ADDNR" jdbcType="VARCHAR" property="addnr" />
    <result column="KORNR" jdbcType="VARCHAR" property="kornr" />
    <result column="MEMORY" jdbcType="VARCHAR" property="memory" />
    <result column="PROCSTAT" jdbcType="VARCHAR" property="procstat" />
    <result column="RLWRT" jdbcType="DECIMAL" property="rlwrt" />
    <result column="REVNO" jdbcType="VARCHAR" property="revno" />
    <result column="SCMPROC" jdbcType="VARCHAR" property="scmproc" />
    <result column="REASON_CODE" jdbcType="VARCHAR" property="reasonCode" />
    <result column="MEMORYTYPE" jdbcType="VARCHAR" property="memorytype" />
    <result column="RETTP" jdbcType="VARCHAR" property="rettp" />
    <result column="RETPC" jdbcType="DECIMAL" property="retpc" />
    <result column="DPTYP" jdbcType="VARCHAR" property="dptyp" />
    <result column="DPPCT" jdbcType="DECIMAL" property="dppct" />
    <result column="DPAMT" jdbcType="DECIMAL" property="dpamt" />
    <result column="DPDAT" jdbcType="VARCHAR" property="dpdat" />
    <result column="MSR_ID" jdbcType="VARCHAR" property="msrId" />
    <result column="HIERARCHY_EXISTS" jdbcType="VARCHAR" property="hierarchyExists" />
    <result column="THRESHOLD_EXISTS" jdbcType="VARCHAR" property="thresholdExists" />
    <result column="LEGAL_CONTRACT" jdbcType="VARCHAR" property="legalContract" />
    <result column="DESCRIPTION" jdbcType="VARCHAR" property="description" />
    <result column="RELEASE_DATE" jdbcType="VARCHAR" property="releaseDate" />
    <result column="VSART" jdbcType="VARCHAR" property="vsart" />
    <result column="HANDOVERLOC" jdbcType="VARCHAR" property="handoverloc" />
    <result column="SHIPCOND" jdbcType="VARCHAR" property="shipcond" />
    <result column="INCOV" jdbcType="VARCHAR" property="incov" />
    <result column="INCO2_L" jdbcType="VARCHAR" property="inco2L" />
    <result column="INCO3_L" jdbcType="VARCHAR" property="inco3L" />
    <result column="GRWCU" jdbcType="VARCHAR" property="grwcu" />
    <result column="INTRA_REL" jdbcType="VARCHAR" property="intraRel" />
    <result column="INTRA_EXCL" jdbcType="VARCHAR" property="intraExcl" />
    <result column="QTN_ERLST_SUBMSN_DATE" jdbcType="VARCHAR" property="qtnErlstSubmsnDate" />
    <result column="FOLLOWON_DOC_CAT" jdbcType="VARCHAR" property="followonDocCat" />
    <result column="FOLLOWON_DOC_TYPE" jdbcType="VARCHAR" property="followonDocType" />
    <result column="DUMMY_EKKO_INCL_EEW_PS" jdbcType="VARCHAR" property="dummyEkkoInclEewPs" />
    <result column="EXTERNALSYSTEM" jdbcType="VARCHAR" property="externalsystem" />
    <result column="EXTERNALREFERENCEID" jdbcType="VARCHAR" property="externalreferenceid" />
    <result column="EXT_REV_TMSTMP" jdbcType="DECIMAL" property="extRevTmstmp" />
    <result column="ISEOPBLOCKED" jdbcType="VARCHAR" property="iseopblocked" />
    <result column="ISAGED" jdbcType="VARCHAR" property="isaged" />
    <result column="FORCE_ID" jdbcType="VARCHAR" property="forceId" />
    <result column="FORCE_CNT" jdbcType="VARCHAR" property="forceCnt" />
    <result column="RELOC_ID" jdbcType="VARCHAR" property="relocId" />
    <result column="RELOC_SEQ_ID" jdbcType="VARCHAR" property="relocSeqId" />
    <result column="SOURCE_LOGSYS" jdbcType="VARCHAR" property="sourceLogsys" />
    <result column="FSH_TRANSACTION" jdbcType="VARCHAR" property="fshTransaction" />
    <result column="FSH_ITEM_GROUP" jdbcType="VARCHAR" property="fshItemGroup" />
    <result column="FSH_VAS_LAST_ITEM" jdbcType="VARCHAR" property="fshVasLastItem" />
    <result column="FSH_OS_STG_CHANGE" jdbcType="VARCHAR" property="fshOsStgChange" />
    <result column="TMS_REF_UUID" jdbcType="VARCHAR" property="tmsRefUuid" />
    <result column="ZZZWLMS" jdbcType="VARCHAR" property="zzzwlms" />
    <result column="ZZZZSHD" jdbcType="VARCHAR" property="zzzzshd" />
    <result column="ZZZSHKC" jdbcType="VARCHAR" property="zzzshkc" />
    <result column="ZZZMDSQ" jdbcType="VARCHAR" property="zzzmdsq" />
    <result column="ZZZBEIZ" jdbcType="VARCHAR" property="zzzbeiz" />
    <result column="ZZZHGZT" jdbcType="VARCHAR" property="zzzhgzt" />
    <result column="ZZQXDJH" jdbcType="VARCHAR" property="zzqxdjh" />
    <result column="ZZZQHBS" jdbcType="VARCHAR" property="zzzqhbs" />
    <result column="ZZZUSERID" jdbcType="VARCHAR" property="zzzuserid" />
    <result column="ZZCGY" jdbcType="VARCHAR" property="zzcgy" />
    <result column="ZZDHY" jdbcType="VARCHAR" property="zzdhy" />
    <result column="ZZCOER" jdbcType="VARCHAR" property="zzcoer" />
    <result column="ZZCOCA" jdbcType="VARCHAR" property="zzcoca" />
    <result column="ZZYCGDD" jdbcType="VARCHAR" property="zzycgdd" />
    <result column="ZAPCGK" jdbcType="VARCHAR" property="zapcgk" />
    <result column="APCGK_EXTEND" jdbcType="VARCHAR" property="apcgkExtend" />
    <result column="ZBAS_DATE" jdbcType="VARCHAR" property="zbasDate" />
    <result column="ZADATTYP" jdbcType="VARCHAR" property="zadattyp" />
    <result column="ZSTART_DAT" jdbcType="VARCHAR" property="zstartDat" />
    <result column="Z_DEV" jdbcType="DECIMAL" property="zDev" />
    <result column="ZINDANX" jdbcType="VARCHAR" property="zindanx" />
    <result column="ZLIMIT_DAT" jdbcType="VARCHAR" property="zlimitDat" />
    <result column="NUMERATOR" jdbcType="VARCHAR" property="numerator" />
    <result column="HASHCAL_BDAT" jdbcType="VARCHAR" property="hashcalBdat" />
    <result column="HASHCAL" jdbcType="VARCHAR" property="hashcal" />
    <result column="NEGATIVE" jdbcType="VARCHAR" property="negative" />
    <result column="HASHCAL_EXISTS" jdbcType="VARCHAR" property="hashcalExists" />
    <result column="KNOWN_INDEX" jdbcType="VARCHAR" property="knownIndex" />
    <result column="POSTAT" jdbcType="VARCHAR" property="postat" />
    <result column="VZSKZ" jdbcType="VARCHAR" property="vzskz" />
    <result column="FSH_SNST_STATUS" jdbcType="VARCHAR" property="fshSnstStatus" />
    <result column="PROCE" jdbcType="VARCHAR" property="proce" />
    <result column="CONC" jdbcType="VARCHAR" property="conc" />
    <result column="CONT" jdbcType="VARCHAR" property="cont" />
    <result column="COMP" jdbcType="VARCHAR" property="comp" />
    <result column="OUTR" jdbcType="VARCHAR" property="outr" />
    <result column="DESP" jdbcType="VARCHAR" property="desp" />
    <result column="DESP_DAT" jdbcType="VARCHAR" property="despDat" />
    <result column="DESP_CARGO" jdbcType="VARCHAR" property="despCargo" />
    <result column="PARE" jdbcType="VARCHAR" property="pare" />
    <result column="PARE_DAT" jdbcType="VARCHAR" property="pareDat" />
    <result column="PARE_CARGO" jdbcType="VARCHAR" property="pareCargo" />
    <result column="PFM_CONTRACT" jdbcType="VARCHAR" property="pfmContract" />
    <result column="POHF_TYPE" jdbcType="VARCHAR" property="pohfType" />
    <result column="EQ_EINDT" jdbcType="VARCHAR" property="eqEindt" />
    <result column="EQ_WERKS" jdbcType="VARCHAR" property="eqWerks" />
    <result column="FIXPO" jdbcType="VARCHAR" property="fixpo" />
    <result column="EKGRP_ALLOW" jdbcType="VARCHAR" property="ekgrpAllow" />
    <result column="WERKS_ALLOW" jdbcType="VARCHAR" property="werksAllow" />
    <result column="CONTRACT_ALLOW" jdbcType="VARCHAR" property="contractAllow" />
    <result column="PSTYP_ALLOW" jdbcType="VARCHAR" property="pstypAllow" />
    <result column="FIXPO_ALLOW" jdbcType="VARCHAR" property="fixpoAllow" />
    <result column="KEY_ID_ALLOW" jdbcType="VARCHAR" property="keyIdAllow" />
    <result column="AUREL_ALLOW" jdbcType="VARCHAR" property="aurelAllow" />
    <result column="DELPER_ALLOW" jdbcType="VARCHAR" property="delperAllow" />
    <result column="EINDT_ALLOW" jdbcType="VARCHAR" property="eindtAllow" />
    <result column="LTSNR_ALLOW" jdbcType="VARCHAR" property="ltsnrAllow" />
    <result column="OTB_LEVEL" jdbcType="VARCHAR" property="otbLevel" />
    <result column="OTB_COND_TYPE" jdbcType="VARCHAR" property="otbCondType" />
    <result column="KEY_ID" jdbcType="VARCHAR" property="keyId" />
    <result column="OTB_VALUE" jdbcType="DECIMAL" property="otbValue" />
    <result column="OTB_CURR" jdbcType="VARCHAR" property="otbCurr" />
    <result column="OTB_RES_VALUE" jdbcType="DECIMAL" property="otbResValue" />
    <result column="OTB_SPEC_VALUE" jdbcType="DECIMAL" property="otbSpecValue" />
    <result column="SPR_RSN_PROFILE" jdbcType="VARCHAR" property="sprRsnProfile" />
    <result column="BUDG_TYPE" jdbcType="VARCHAR" property="budgType" />
    <result column="OTB_STATUS" jdbcType="VARCHAR" property="otbStatus" />
    <result column="OTB_REASON" jdbcType="VARCHAR" property="otbReason" />
    <result column="CHECK_TYPE" jdbcType="VARCHAR" property="checkType" />
    <result column="CON_OTB_REQ" jdbcType="VARCHAR" property="conOtbReq" />
    <result column="CON_PREBOOK_LEV" jdbcType="VARCHAR" property="conPrebookLev" />
    <result column="CON_DISTR_LEV" jdbcType="VARCHAR" property="conDistrLev" />
    <result column="ZZYFYE" jdbcType="VARCHAR" property="zzyfye" />
    <result column="ZSQHZH" jdbcType="VARCHAR" property="zsqhzh" />
    <result column="ZZSRMZT" jdbcType="VARCHAR" property="zzsrmzt" />
    <result column="ZZPAID" jdbcType="VARCHAR" property="zzpaid" />
    <result column="ZZPAMT" jdbcType="DECIMAL" property="zzpamt" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, MANDT, EBELN, BUKRS, BSTYP, BSART, BSAKZ, LOEKZ, STATU, AEDAT, ERNAM, LASTCHANGEDATETIME, 
    PINCR, LPONR, LIFNR, SPRAS, ZTERM, ZBD1T, ZBD2T, ZBD3T, ZBD1P, ZBD2P, EKORG, EKGRP, 
    WAERS, WKURS, KUFIX, BEDAT, KDATB, KDATE, BWBDT, ANGDT, BNDDT, GWLDT, AUSNR, ANGNR, 
    IHRAN, IHREZ, VERKF, TELF1, LLIEF, KUNNR, KONNR, ABGRU, AUTLF, WEAKT, RESWK, LBLIF, 
    INCO1, INCO2, KTWRT, SUBMI, KNUMV, KALSM, STAFO, LIFRE, EXNUM, UNSEZ, LOGSY, UPINC, 
    STAKO, FRGGR, FRGSX, FRGKE, FRGZU, FRGRL, LANDS, LPHIS, ADRNR, STCEG_L, STCEG, ABSGR, 
    ADDNR, KORNR, MEMORY, PROCSTAT, RLWRT, REVNO, SCMPROC, REASON_CODE, MEMORYTYPE, RETTP, 
    RETPC, DPTYP, DPPCT, DPAMT, DPDAT, MSR_ID, HIERARCHY_EXISTS, THRESHOLD_EXISTS, LEGAL_CONTRACT, 
    DESCRIPTION, RELEASE_DATE, VSART, HANDOVERLOC, SHIPCOND, INCOV, INCO2_L, INCO3_L, 
    GRWCU, INTRA_REL, INTRA_EXCL, QTN_ERLST_SUBMSN_DATE, FOLLOWON_DOC_CAT, FOLLOWON_DOC_TYPE, 
    DUMMY_EKKO_INCL_EEW_PS, EXTERNALSYSTEM, EXTERNALREFERENCEID, EXT_REV_TMSTMP, ISEOPBLOCKED, 
    ISAGED, FORCE_ID, FORCE_CNT, RELOC_ID, RELOC_SEQ_ID, SOURCE_LOGSYS, FSH_TRANSACTION, 
    FSH_ITEM_GROUP, FSH_VAS_LAST_ITEM, FSH_OS_STG_CHANGE, TMS_REF_UUID, ZZZWLMS, ZZZZSHD, 
    ZZZSHKC, ZZZMDSQ, ZZZBEIZ, ZZZHGZT, ZZQXDJH, ZZZQHBS, ZZZUSERID, ZZCGY, ZZDHY, ZZCOER, 
    ZZCOCA, ZZYCGDD, ZAPCGK, APCGK_EXTEND, ZBAS_DATE, ZADATTYP, ZSTART_DAT, Z_DEV, ZINDANX, 
    ZLIMIT_DAT, NUMERATOR, HASHCAL_BDAT, HASHCAL, NEGATIVE, HASHCAL_EXISTS, KNOWN_INDEX, 
    POSTAT, VZSKZ, FSH_SNST_STATUS, PROCE, CONC, CONT, COMP, OUTR, DESP, DESP_DAT, DESP_CARGO, 
    PARE, PARE_DAT, PARE_CARGO, PFM_CONTRACT, POHF_TYPE, EQ_EINDT, EQ_WERKS, FIXPO, EKGRP_ALLOW, 
    WERKS_ALLOW, CONTRACT_ALLOW, PSTYP_ALLOW, FIXPO_ALLOW, KEY_ID_ALLOW, AUREL_ALLOW, 
    DELPER_ALLOW, EINDT_ALLOW, LTSNR_ALLOW, OTB_LEVEL, OTB_COND_TYPE, KEY_ID, OTB_VALUE, 
    OTB_CURR, OTB_RES_VALUE, OTB_SPEC_VALUE, SPR_RSN_PROFILE, BUDG_TYPE, OTB_STATUS, 
    OTB_REASON, CHECK_TYPE, CON_OTB_REQ, CON_PREBOOK_LEV, CON_DISTR_LEV, ZZYFYE, ZSQHZH, 
    ZZSRMZT, ZZPAID, ZZPAMT
  </sql>
  <select id="selectByExample" parameterType="com.cowell.iscm.entity.SapEkkoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from SAP_EKKO
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from SAP_EKKO
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from SAP_EKKO
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.iscm.entity.SapEkkoExample">
    delete from SAP_EKKO
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entity.SapEkko" useGeneratedKeys="true">
    insert into SAP_EKKO (MANDT, EBELN, BUKRS, 
      BSTYP, BSART, BSAKZ, 
      LOEKZ, STATU, AEDAT, 
      ERNAM, LASTCHANGEDATETIME, PINCR, 
      LPONR, LIFNR, SPRAS, 
      ZTERM, ZBD1T, ZBD2T, 
      ZBD3T, ZBD1P, ZBD2P, 
      EKORG, EKGRP, WAERS, 
      WKURS, KUFIX, BEDAT, 
      KDATB, KDATE, BWBDT, 
      ANGDT, BNDDT, GWLDT, 
      AUSNR, ANGNR, IHRAN, 
      IHREZ, VERKF, TELF1, 
      LLIEF, KUNNR, KONNR, 
      ABGRU, AUTLF, WEAKT, 
      RESWK, LBLIF, INCO1, 
      INCO2, KTWRT, SUBMI, 
      KNUMV, KALSM, STAFO, 
      LIFRE, EXNUM, UNSEZ, 
      LOGSY, UPINC, STAKO, 
      FRGGR, FRGSX, FRGKE, 
      FRGZU, FRGRL, LANDS, 
      LPHIS, ADRNR, STCEG_L, 
      STCEG, ABSGR, ADDNR, 
      KORNR, MEMORY, PROCSTAT, 
      RLWRT, REVNO, SCMPROC, 
      REASON_CODE, MEMORYTYPE, RETTP, 
      RETPC, DPTYP, DPPCT, 
      DPAMT, DPDAT, MSR_ID, 
      HIERARCHY_EXISTS, THRESHOLD_EXISTS, LEGAL_CONTRACT, 
      DESCRIPTION, RELEASE_DATE, VSART, 
      HANDOVERLOC, SHIPCOND, INCOV, 
      INCO2_L, INCO3_L, GRWCU, 
      INTRA_REL, INTRA_EXCL, QTN_ERLST_SUBMSN_DATE, 
      FOLLOWON_DOC_CAT, FOLLOWON_DOC_TYPE, DUMMY_EKKO_INCL_EEW_PS, 
      EXTERNALSYSTEM, EXTERNALREFERENCEID, EXT_REV_TMSTMP, 
      ISEOPBLOCKED, ISAGED, FORCE_ID, 
      FORCE_CNT, RELOC_ID, RELOC_SEQ_ID, 
      SOURCE_LOGSYS, FSH_TRANSACTION, FSH_ITEM_GROUP, 
      FSH_VAS_LAST_ITEM, FSH_OS_STG_CHANGE, TMS_REF_UUID, 
      ZZZWLMS, ZZZZSHD, ZZZSHKC, 
      ZZZMDSQ, ZZZBEIZ, ZZZHGZT, 
      ZZQXDJH, ZZZQHBS, ZZZUSERID, 
      ZZCGY, ZZDHY, ZZCOER, 
      ZZCOCA, ZZYCGDD, ZAPCGK, 
      APCGK_EXTEND, ZBAS_DATE, ZADATTYP, 
      ZSTART_DAT, Z_DEV, ZINDANX, 
      ZLIMIT_DAT, NUMERATOR, HASHCAL_BDAT, 
      HASHCAL, NEGATIVE, HASHCAL_EXISTS, 
      KNOWN_INDEX, POSTAT, VZSKZ, 
      FSH_SNST_STATUS, PROCE, CONC, 
      CONT, COMP, OUTR, DESP, 
      DESP_DAT, DESP_CARGO, PARE, 
      PARE_DAT, PARE_CARGO, PFM_CONTRACT, 
      POHF_TYPE, EQ_EINDT, EQ_WERKS, 
      FIXPO, EKGRP_ALLOW, WERKS_ALLOW, 
      CONTRACT_ALLOW, PSTYP_ALLOW, FIXPO_ALLOW, 
      KEY_ID_ALLOW, AUREL_ALLOW, DELPER_ALLOW, 
      EINDT_ALLOW, LTSNR_ALLOW, OTB_LEVEL, 
      OTB_COND_TYPE, KEY_ID, OTB_VALUE, 
      OTB_CURR, OTB_RES_VALUE, OTB_SPEC_VALUE, 
      SPR_RSN_PROFILE, BUDG_TYPE, OTB_STATUS, 
      OTB_REASON, CHECK_TYPE, CON_OTB_REQ, 
      CON_PREBOOK_LEV, CON_DISTR_LEV, ZZYFYE, 
      ZSQHZH, ZZSRMZT, ZZPAID, 
      ZZPAMT)
    values (#{mandt,jdbcType=VARCHAR}, #{ebeln,jdbcType=VARCHAR}, #{bukrs,jdbcType=VARCHAR}, 
      #{bstyp,jdbcType=VARCHAR}, #{bsart,jdbcType=VARCHAR}, #{bsakz,jdbcType=VARCHAR}, 
      #{loekz,jdbcType=VARCHAR}, #{statu,jdbcType=VARCHAR}, #{aedat,jdbcType=VARCHAR}, 
      #{ernam,jdbcType=VARCHAR}, #{lastchangedatetime,jdbcType=DECIMAL}, #{pincr,jdbcType=VARCHAR}, 
      #{lponr,jdbcType=VARCHAR}, #{lifnr,jdbcType=VARCHAR}, #{spras,jdbcType=VARCHAR}, 
      #{zterm,jdbcType=VARCHAR}, #{zbd1t,jdbcType=DECIMAL}, #{zbd2t,jdbcType=DECIMAL}, 
      #{zbd3t,jdbcType=DECIMAL}, #{zbd1p,jdbcType=DECIMAL}, #{zbd2p,jdbcType=DECIMAL}, 
      #{ekorg,jdbcType=VARCHAR}, #{ekgrp,jdbcType=VARCHAR}, #{waers,jdbcType=VARCHAR}, 
      #{wkurs,jdbcType=DECIMAL}, #{kufix,jdbcType=VARCHAR}, #{bedat,jdbcType=VARCHAR}, 
      #{kdatb,jdbcType=VARCHAR}, #{kdate,jdbcType=VARCHAR}, #{bwbdt,jdbcType=VARCHAR}, 
      #{angdt,jdbcType=VARCHAR}, #{bnddt,jdbcType=VARCHAR}, #{gwldt,jdbcType=VARCHAR}, 
      #{ausnr,jdbcType=VARCHAR}, #{angnr,jdbcType=VARCHAR}, #{ihran,jdbcType=VARCHAR}, 
      #{ihrez,jdbcType=VARCHAR}, #{verkf,jdbcType=VARCHAR}, #{telf1,jdbcType=VARCHAR}, 
      #{llief,jdbcType=VARCHAR}, #{kunnr,jdbcType=VARCHAR}, #{konnr,jdbcType=VARCHAR}, 
      #{abgru,jdbcType=VARCHAR}, #{autlf,jdbcType=VARCHAR}, #{weakt,jdbcType=VARCHAR}, 
      #{reswk,jdbcType=VARCHAR}, #{lblif,jdbcType=VARCHAR}, #{inco1,jdbcType=VARCHAR}, 
      #{inco2,jdbcType=VARCHAR}, #{ktwrt,jdbcType=DECIMAL}, #{submi,jdbcType=VARCHAR}, 
      #{knumv,jdbcType=VARCHAR}, #{kalsm,jdbcType=VARCHAR}, #{stafo,jdbcType=VARCHAR}, 
      #{lifre,jdbcType=VARCHAR}, #{exnum,jdbcType=VARCHAR}, #{unsez,jdbcType=VARCHAR}, 
      #{logsy,jdbcType=VARCHAR}, #{upinc,jdbcType=VARCHAR}, #{stako,jdbcType=VARCHAR}, 
      #{frggr,jdbcType=VARCHAR}, #{frgsx,jdbcType=VARCHAR}, #{frgke,jdbcType=VARCHAR}, 
      #{frgzu,jdbcType=VARCHAR}, #{frgrl,jdbcType=VARCHAR}, #{lands,jdbcType=VARCHAR}, 
      #{lphis,jdbcType=VARCHAR}, #{adrnr,jdbcType=VARCHAR}, #{stcegL,jdbcType=VARCHAR}, 
      #{stceg,jdbcType=VARCHAR}, #{absgr,jdbcType=VARCHAR}, #{addnr,jdbcType=VARCHAR}, 
      #{kornr,jdbcType=VARCHAR}, #{memory,jdbcType=VARCHAR}, #{procstat,jdbcType=VARCHAR}, 
      #{rlwrt,jdbcType=DECIMAL}, #{revno,jdbcType=VARCHAR}, #{scmproc,jdbcType=VARCHAR}, 
      #{reasonCode,jdbcType=VARCHAR}, #{memorytype,jdbcType=VARCHAR}, #{rettp,jdbcType=VARCHAR}, 
      #{retpc,jdbcType=DECIMAL}, #{dptyp,jdbcType=VARCHAR}, #{dppct,jdbcType=DECIMAL}, 
      #{dpamt,jdbcType=DECIMAL}, #{dpdat,jdbcType=VARCHAR}, #{msrId,jdbcType=VARCHAR}, 
      #{hierarchyExists,jdbcType=VARCHAR}, #{thresholdExists,jdbcType=VARCHAR}, #{legalContract,jdbcType=VARCHAR}, 
      #{description,jdbcType=VARCHAR}, #{releaseDate,jdbcType=VARCHAR}, #{vsart,jdbcType=VARCHAR}, 
      #{handoverloc,jdbcType=VARCHAR}, #{shipcond,jdbcType=VARCHAR}, #{incov,jdbcType=VARCHAR}, 
      #{inco2L,jdbcType=VARCHAR}, #{inco3L,jdbcType=VARCHAR}, #{grwcu,jdbcType=VARCHAR}, 
      #{intraRel,jdbcType=VARCHAR}, #{intraExcl,jdbcType=VARCHAR}, #{qtnErlstSubmsnDate,jdbcType=VARCHAR}, 
      #{followonDocCat,jdbcType=VARCHAR}, #{followonDocType,jdbcType=VARCHAR}, #{dummyEkkoInclEewPs,jdbcType=VARCHAR}, 
      #{externalsystem,jdbcType=VARCHAR}, #{externalreferenceid,jdbcType=VARCHAR}, #{extRevTmstmp,jdbcType=DECIMAL}, 
      #{iseopblocked,jdbcType=VARCHAR}, #{isaged,jdbcType=VARCHAR}, #{forceId,jdbcType=VARCHAR}, 
      #{forceCnt,jdbcType=VARCHAR}, #{relocId,jdbcType=VARCHAR}, #{relocSeqId,jdbcType=VARCHAR}, 
      #{sourceLogsys,jdbcType=VARCHAR}, #{fshTransaction,jdbcType=VARCHAR}, #{fshItemGroup,jdbcType=VARCHAR}, 
      #{fshVasLastItem,jdbcType=VARCHAR}, #{fshOsStgChange,jdbcType=VARCHAR}, #{tmsRefUuid,jdbcType=VARCHAR}, 
      #{zzzwlms,jdbcType=VARCHAR}, #{zzzzshd,jdbcType=VARCHAR}, #{zzzshkc,jdbcType=VARCHAR}, 
      #{zzzmdsq,jdbcType=VARCHAR}, #{zzzbeiz,jdbcType=VARCHAR}, #{zzzhgzt,jdbcType=VARCHAR}, 
      #{zzqxdjh,jdbcType=VARCHAR}, #{zzzqhbs,jdbcType=VARCHAR}, #{zzzuserid,jdbcType=VARCHAR}, 
      #{zzcgy,jdbcType=VARCHAR}, #{zzdhy,jdbcType=VARCHAR}, #{zzcoer,jdbcType=VARCHAR}, 
      #{zzcoca,jdbcType=VARCHAR}, #{zzycgdd,jdbcType=VARCHAR}, #{zapcgk,jdbcType=VARCHAR}, 
      #{apcgkExtend,jdbcType=VARCHAR}, #{zbasDate,jdbcType=VARCHAR}, #{zadattyp,jdbcType=VARCHAR}, 
      #{zstartDat,jdbcType=VARCHAR}, #{zDev,jdbcType=DECIMAL}, #{zindanx,jdbcType=VARCHAR}, 
      #{zlimitDat,jdbcType=VARCHAR}, #{numerator,jdbcType=VARCHAR}, #{hashcalBdat,jdbcType=VARCHAR}, 
      #{hashcal,jdbcType=VARCHAR}, #{negative,jdbcType=VARCHAR}, #{hashcalExists,jdbcType=VARCHAR}, 
      #{knownIndex,jdbcType=VARCHAR}, #{postat,jdbcType=VARCHAR}, #{vzskz,jdbcType=VARCHAR}, 
      #{fshSnstStatus,jdbcType=VARCHAR}, #{proce,jdbcType=VARCHAR}, #{conc,jdbcType=VARCHAR}, 
      #{cont,jdbcType=VARCHAR}, #{comp,jdbcType=VARCHAR}, #{outr,jdbcType=VARCHAR}, #{desp,jdbcType=VARCHAR}, 
      #{despDat,jdbcType=VARCHAR}, #{despCargo,jdbcType=VARCHAR}, #{pare,jdbcType=VARCHAR}, 
      #{pareDat,jdbcType=VARCHAR}, #{pareCargo,jdbcType=VARCHAR}, #{pfmContract,jdbcType=VARCHAR}, 
      #{pohfType,jdbcType=VARCHAR}, #{eqEindt,jdbcType=VARCHAR}, #{eqWerks,jdbcType=VARCHAR}, 
      #{fixpo,jdbcType=VARCHAR}, #{ekgrpAllow,jdbcType=VARCHAR}, #{werksAllow,jdbcType=VARCHAR}, 
      #{contractAllow,jdbcType=VARCHAR}, #{pstypAllow,jdbcType=VARCHAR}, #{fixpoAllow,jdbcType=VARCHAR}, 
      #{keyIdAllow,jdbcType=VARCHAR}, #{aurelAllow,jdbcType=VARCHAR}, #{delperAllow,jdbcType=VARCHAR}, 
      #{eindtAllow,jdbcType=VARCHAR}, #{ltsnrAllow,jdbcType=VARCHAR}, #{otbLevel,jdbcType=VARCHAR}, 
      #{otbCondType,jdbcType=VARCHAR}, #{keyId,jdbcType=VARCHAR}, #{otbValue,jdbcType=DECIMAL}, 
      #{otbCurr,jdbcType=VARCHAR}, #{otbResValue,jdbcType=DECIMAL}, #{otbSpecValue,jdbcType=DECIMAL}, 
      #{sprRsnProfile,jdbcType=VARCHAR}, #{budgType,jdbcType=VARCHAR}, #{otbStatus,jdbcType=VARCHAR}, 
      #{otbReason,jdbcType=VARCHAR}, #{checkType,jdbcType=VARCHAR}, #{conOtbReq,jdbcType=VARCHAR}, 
      #{conPrebookLev,jdbcType=VARCHAR}, #{conDistrLev,jdbcType=VARCHAR}, #{zzyfye,jdbcType=VARCHAR}, 
      #{zsqhzh,jdbcType=VARCHAR}, #{zzsrmzt,jdbcType=VARCHAR}, #{zzpaid,jdbcType=VARCHAR}, 
      #{zzpamt,jdbcType=DECIMAL})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entity.SapEkko" useGeneratedKeys="true">
    insert into SAP_EKKO
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="mandt != null">
        MANDT,
      </if>
      <if test="ebeln != null">
        EBELN,
      </if>
      <if test="bukrs != null">
        BUKRS,
      </if>
      <if test="bstyp != null">
        BSTYP,
      </if>
      <if test="bsart != null">
        BSART,
      </if>
      <if test="bsakz != null">
        BSAKZ,
      </if>
      <if test="loekz != null">
        LOEKZ,
      </if>
      <if test="statu != null">
        STATU,
      </if>
      <if test="aedat != null">
        AEDAT,
      </if>
      <if test="ernam != null">
        ERNAM,
      </if>
      <if test="lastchangedatetime != null">
        LASTCHANGEDATETIME,
      </if>
      <if test="pincr != null">
        PINCR,
      </if>
      <if test="lponr != null">
        LPONR,
      </if>
      <if test="lifnr != null">
        LIFNR,
      </if>
      <if test="spras != null">
        SPRAS,
      </if>
      <if test="zterm != null">
        ZTERM,
      </if>
      <if test="zbd1t != null">
        ZBD1T,
      </if>
      <if test="zbd2t != null">
        ZBD2T,
      </if>
      <if test="zbd3t != null">
        ZBD3T,
      </if>
      <if test="zbd1p != null">
        ZBD1P,
      </if>
      <if test="zbd2p != null">
        ZBD2P,
      </if>
      <if test="ekorg != null">
        EKORG,
      </if>
      <if test="ekgrp != null">
        EKGRP,
      </if>
      <if test="waers != null">
        WAERS,
      </if>
      <if test="wkurs != null">
        WKURS,
      </if>
      <if test="kufix != null">
        KUFIX,
      </if>
      <if test="bedat != null">
        BEDAT,
      </if>
      <if test="kdatb != null">
        KDATB,
      </if>
      <if test="kdate != null">
        KDATE,
      </if>
      <if test="bwbdt != null">
        BWBDT,
      </if>
      <if test="angdt != null">
        ANGDT,
      </if>
      <if test="bnddt != null">
        BNDDT,
      </if>
      <if test="gwldt != null">
        GWLDT,
      </if>
      <if test="ausnr != null">
        AUSNR,
      </if>
      <if test="angnr != null">
        ANGNR,
      </if>
      <if test="ihran != null">
        IHRAN,
      </if>
      <if test="ihrez != null">
        IHREZ,
      </if>
      <if test="verkf != null">
        VERKF,
      </if>
      <if test="telf1 != null">
        TELF1,
      </if>
      <if test="llief != null">
        LLIEF,
      </if>
      <if test="kunnr != null">
        KUNNR,
      </if>
      <if test="konnr != null">
        KONNR,
      </if>
      <if test="abgru != null">
        ABGRU,
      </if>
      <if test="autlf != null">
        AUTLF,
      </if>
      <if test="weakt != null">
        WEAKT,
      </if>
      <if test="reswk != null">
        RESWK,
      </if>
      <if test="lblif != null">
        LBLIF,
      </if>
      <if test="inco1 != null">
        INCO1,
      </if>
      <if test="inco2 != null">
        INCO2,
      </if>
      <if test="ktwrt != null">
        KTWRT,
      </if>
      <if test="submi != null">
        SUBMI,
      </if>
      <if test="knumv != null">
        KNUMV,
      </if>
      <if test="kalsm != null">
        KALSM,
      </if>
      <if test="stafo != null">
        STAFO,
      </if>
      <if test="lifre != null">
        LIFRE,
      </if>
      <if test="exnum != null">
        EXNUM,
      </if>
      <if test="unsez != null">
        UNSEZ,
      </if>
      <if test="logsy != null">
        LOGSY,
      </if>
      <if test="upinc != null">
        UPINC,
      </if>
      <if test="stako != null">
        STAKO,
      </if>
      <if test="frggr != null">
        FRGGR,
      </if>
      <if test="frgsx != null">
        FRGSX,
      </if>
      <if test="frgke != null">
        FRGKE,
      </if>
      <if test="frgzu != null">
        FRGZU,
      </if>
      <if test="frgrl != null">
        FRGRL,
      </if>
      <if test="lands != null">
        LANDS,
      </if>
      <if test="lphis != null">
        LPHIS,
      </if>
      <if test="adrnr != null">
        ADRNR,
      </if>
      <if test="stcegL != null">
        STCEG_L,
      </if>
      <if test="stceg != null">
        STCEG,
      </if>
      <if test="absgr != null">
        ABSGR,
      </if>
      <if test="addnr != null">
        ADDNR,
      </if>
      <if test="kornr != null">
        KORNR,
      </if>
      <if test="memory != null">
        MEMORY,
      </if>
      <if test="procstat != null">
        PROCSTAT,
      </if>
      <if test="rlwrt != null">
        RLWRT,
      </if>
      <if test="revno != null">
        REVNO,
      </if>
      <if test="scmproc != null">
        SCMPROC,
      </if>
      <if test="reasonCode != null">
        REASON_CODE,
      </if>
      <if test="memorytype != null">
        MEMORYTYPE,
      </if>
      <if test="rettp != null">
        RETTP,
      </if>
      <if test="retpc != null">
        RETPC,
      </if>
      <if test="dptyp != null">
        DPTYP,
      </if>
      <if test="dppct != null">
        DPPCT,
      </if>
      <if test="dpamt != null">
        DPAMT,
      </if>
      <if test="dpdat != null">
        DPDAT,
      </if>
      <if test="msrId != null">
        MSR_ID,
      </if>
      <if test="hierarchyExists != null">
        HIERARCHY_EXISTS,
      </if>
      <if test="thresholdExists != null">
        THRESHOLD_EXISTS,
      </if>
      <if test="legalContract != null">
        LEGAL_CONTRACT,
      </if>
      <if test="description != null">
        DESCRIPTION,
      </if>
      <if test="releaseDate != null">
        RELEASE_DATE,
      </if>
      <if test="vsart != null">
        VSART,
      </if>
      <if test="handoverloc != null">
        HANDOVERLOC,
      </if>
      <if test="shipcond != null">
        SHIPCOND,
      </if>
      <if test="incov != null">
        INCOV,
      </if>
      <if test="inco2L != null">
        INCO2_L,
      </if>
      <if test="inco3L != null">
        INCO3_L,
      </if>
      <if test="grwcu != null">
        GRWCU,
      </if>
      <if test="intraRel != null">
        INTRA_REL,
      </if>
      <if test="intraExcl != null">
        INTRA_EXCL,
      </if>
      <if test="qtnErlstSubmsnDate != null">
        QTN_ERLST_SUBMSN_DATE,
      </if>
      <if test="followonDocCat != null">
        FOLLOWON_DOC_CAT,
      </if>
      <if test="followonDocType != null">
        FOLLOWON_DOC_TYPE,
      </if>
      <if test="dummyEkkoInclEewPs != null">
        DUMMY_EKKO_INCL_EEW_PS,
      </if>
      <if test="externalsystem != null">
        EXTERNALSYSTEM,
      </if>
      <if test="externalreferenceid != null">
        EXTERNALREFERENCEID,
      </if>
      <if test="extRevTmstmp != null">
        EXT_REV_TMSTMP,
      </if>
      <if test="iseopblocked != null">
        ISEOPBLOCKED,
      </if>
      <if test="isaged != null">
        ISAGED,
      </if>
      <if test="forceId != null">
        FORCE_ID,
      </if>
      <if test="forceCnt != null">
        FORCE_CNT,
      </if>
      <if test="relocId != null">
        RELOC_ID,
      </if>
      <if test="relocSeqId != null">
        RELOC_SEQ_ID,
      </if>
      <if test="sourceLogsys != null">
        SOURCE_LOGSYS,
      </if>
      <if test="fshTransaction != null">
        FSH_TRANSACTION,
      </if>
      <if test="fshItemGroup != null">
        FSH_ITEM_GROUP,
      </if>
      <if test="fshVasLastItem != null">
        FSH_VAS_LAST_ITEM,
      </if>
      <if test="fshOsStgChange != null">
        FSH_OS_STG_CHANGE,
      </if>
      <if test="tmsRefUuid != null">
        TMS_REF_UUID,
      </if>
      <if test="zzzwlms != null">
        ZZZWLMS,
      </if>
      <if test="zzzzshd != null">
        ZZZZSHD,
      </if>
      <if test="zzzshkc != null">
        ZZZSHKC,
      </if>
      <if test="zzzmdsq != null">
        ZZZMDSQ,
      </if>
      <if test="zzzbeiz != null">
        ZZZBEIZ,
      </if>
      <if test="zzzhgzt != null">
        ZZZHGZT,
      </if>
      <if test="zzqxdjh != null">
        ZZQXDJH,
      </if>
      <if test="zzzqhbs != null">
        ZZZQHBS,
      </if>
      <if test="zzzuserid != null">
        ZZZUSERID,
      </if>
      <if test="zzcgy != null">
        ZZCGY,
      </if>
      <if test="zzdhy != null">
        ZZDHY,
      </if>
      <if test="zzcoer != null">
        ZZCOER,
      </if>
      <if test="zzcoca != null">
        ZZCOCA,
      </if>
      <if test="zzycgdd != null">
        ZZYCGDD,
      </if>
      <if test="zapcgk != null">
        ZAPCGK,
      </if>
      <if test="apcgkExtend != null">
        APCGK_EXTEND,
      </if>
      <if test="zbasDate != null">
        ZBAS_DATE,
      </if>
      <if test="zadattyp != null">
        ZADATTYP,
      </if>
      <if test="zstartDat != null">
        ZSTART_DAT,
      </if>
      <if test="zDev != null">
        Z_DEV,
      </if>
      <if test="zindanx != null">
        ZINDANX,
      </if>
      <if test="zlimitDat != null">
        ZLIMIT_DAT,
      </if>
      <if test="numerator != null">
        NUMERATOR,
      </if>
      <if test="hashcalBdat != null">
        HASHCAL_BDAT,
      </if>
      <if test="hashcal != null">
        HASHCAL,
      </if>
      <if test="negative != null">
        NEGATIVE,
      </if>
      <if test="hashcalExists != null">
        HASHCAL_EXISTS,
      </if>
      <if test="knownIndex != null">
        KNOWN_INDEX,
      </if>
      <if test="postat != null">
        POSTAT,
      </if>
      <if test="vzskz != null">
        VZSKZ,
      </if>
      <if test="fshSnstStatus != null">
        FSH_SNST_STATUS,
      </if>
      <if test="proce != null">
        PROCE,
      </if>
      <if test="conc != null">
        CONC,
      </if>
      <if test="cont != null">
        CONT,
      </if>
      <if test="comp != null">
        COMP,
      </if>
      <if test="outr != null">
        OUTR,
      </if>
      <if test="desp != null">
        DESP,
      </if>
      <if test="despDat != null">
        DESP_DAT,
      </if>
      <if test="despCargo != null">
        DESP_CARGO,
      </if>
      <if test="pare != null">
        PARE,
      </if>
      <if test="pareDat != null">
        PARE_DAT,
      </if>
      <if test="pareCargo != null">
        PARE_CARGO,
      </if>
      <if test="pfmContract != null">
        PFM_CONTRACT,
      </if>
      <if test="pohfType != null">
        POHF_TYPE,
      </if>
      <if test="eqEindt != null">
        EQ_EINDT,
      </if>
      <if test="eqWerks != null">
        EQ_WERKS,
      </if>
      <if test="fixpo != null">
        FIXPO,
      </if>
      <if test="ekgrpAllow != null">
        EKGRP_ALLOW,
      </if>
      <if test="werksAllow != null">
        WERKS_ALLOW,
      </if>
      <if test="contractAllow != null">
        CONTRACT_ALLOW,
      </if>
      <if test="pstypAllow != null">
        PSTYP_ALLOW,
      </if>
      <if test="fixpoAllow != null">
        FIXPO_ALLOW,
      </if>
      <if test="keyIdAllow != null">
        KEY_ID_ALLOW,
      </if>
      <if test="aurelAllow != null">
        AUREL_ALLOW,
      </if>
      <if test="delperAllow != null">
        DELPER_ALLOW,
      </if>
      <if test="eindtAllow != null">
        EINDT_ALLOW,
      </if>
      <if test="ltsnrAllow != null">
        LTSNR_ALLOW,
      </if>
      <if test="otbLevel != null">
        OTB_LEVEL,
      </if>
      <if test="otbCondType != null">
        OTB_COND_TYPE,
      </if>
      <if test="keyId != null">
        KEY_ID,
      </if>
      <if test="otbValue != null">
        OTB_VALUE,
      </if>
      <if test="otbCurr != null">
        OTB_CURR,
      </if>
      <if test="otbResValue != null">
        OTB_RES_VALUE,
      </if>
      <if test="otbSpecValue != null">
        OTB_SPEC_VALUE,
      </if>
      <if test="sprRsnProfile != null">
        SPR_RSN_PROFILE,
      </if>
      <if test="budgType != null">
        BUDG_TYPE,
      </if>
      <if test="otbStatus != null">
        OTB_STATUS,
      </if>
      <if test="otbReason != null">
        OTB_REASON,
      </if>
      <if test="checkType != null">
        CHECK_TYPE,
      </if>
      <if test="conOtbReq != null">
        CON_OTB_REQ,
      </if>
      <if test="conPrebookLev != null">
        CON_PREBOOK_LEV,
      </if>
      <if test="conDistrLev != null">
        CON_DISTR_LEV,
      </if>
      <if test="zzyfye != null">
        ZZYFYE,
      </if>
      <if test="zsqhzh != null">
        ZSQHZH,
      </if>
      <if test="zzsrmzt != null">
        ZZSRMZT,
      </if>
      <if test="zzpaid != null">
        ZZPAID,
      </if>
      <if test="zzpamt != null">
        ZZPAMT,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="mandt != null">
        #{mandt,jdbcType=VARCHAR},
      </if>
      <if test="ebeln != null">
        #{ebeln,jdbcType=VARCHAR},
      </if>
      <if test="bukrs != null">
        #{bukrs,jdbcType=VARCHAR},
      </if>
      <if test="bstyp != null">
        #{bstyp,jdbcType=VARCHAR},
      </if>
      <if test="bsart != null">
        #{bsart,jdbcType=VARCHAR},
      </if>
      <if test="bsakz != null">
        #{bsakz,jdbcType=VARCHAR},
      </if>
      <if test="loekz != null">
        #{loekz,jdbcType=VARCHAR},
      </if>
      <if test="statu != null">
        #{statu,jdbcType=VARCHAR},
      </if>
      <if test="aedat != null">
        #{aedat,jdbcType=VARCHAR},
      </if>
      <if test="ernam != null">
        #{ernam,jdbcType=VARCHAR},
      </if>
      <if test="lastchangedatetime != null">
        #{lastchangedatetime,jdbcType=DECIMAL},
      </if>
      <if test="pincr != null">
        #{pincr,jdbcType=VARCHAR},
      </if>
      <if test="lponr != null">
        #{lponr,jdbcType=VARCHAR},
      </if>
      <if test="lifnr != null">
        #{lifnr,jdbcType=VARCHAR},
      </if>
      <if test="spras != null">
        #{spras,jdbcType=VARCHAR},
      </if>
      <if test="zterm != null">
        #{zterm,jdbcType=VARCHAR},
      </if>
      <if test="zbd1t != null">
        #{zbd1t,jdbcType=DECIMAL},
      </if>
      <if test="zbd2t != null">
        #{zbd2t,jdbcType=DECIMAL},
      </if>
      <if test="zbd3t != null">
        #{zbd3t,jdbcType=DECIMAL},
      </if>
      <if test="zbd1p != null">
        #{zbd1p,jdbcType=DECIMAL},
      </if>
      <if test="zbd2p != null">
        #{zbd2p,jdbcType=DECIMAL},
      </if>
      <if test="ekorg != null">
        #{ekorg,jdbcType=VARCHAR},
      </if>
      <if test="ekgrp != null">
        #{ekgrp,jdbcType=VARCHAR},
      </if>
      <if test="waers != null">
        #{waers,jdbcType=VARCHAR},
      </if>
      <if test="wkurs != null">
        #{wkurs,jdbcType=DECIMAL},
      </if>
      <if test="kufix != null">
        #{kufix,jdbcType=VARCHAR},
      </if>
      <if test="bedat != null">
        #{bedat,jdbcType=VARCHAR},
      </if>
      <if test="kdatb != null">
        #{kdatb,jdbcType=VARCHAR},
      </if>
      <if test="kdate != null">
        #{kdate,jdbcType=VARCHAR},
      </if>
      <if test="bwbdt != null">
        #{bwbdt,jdbcType=VARCHAR},
      </if>
      <if test="angdt != null">
        #{angdt,jdbcType=VARCHAR},
      </if>
      <if test="bnddt != null">
        #{bnddt,jdbcType=VARCHAR},
      </if>
      <if test="gwldt != null">
        #{gwldt,jdbcType=VARCHAR},
      </if>
      <if test="ausnr != null">
        #{ausnr,jdbcType=VARCHAR},
      </if>
      <if test="angnr != null">
        #{angnr,jdbcType=VARCHAR},
      </if>
      <if test="ihran != null">
        #{ihran,jdbcType=VARCHAR},
      </if>
      <if test="ihrez != null">
        #{ihrez,jdbcType=VARCHAR},
      </if>
      <if test="verkf != null">
        #{verkf,jdbcType=VARCHAR},
      </if>
      <if test="telf1 != null">
        #{telf1,jdbcType=VARCHAR},
      </if>
      <if test="llief != null">
        #{llief,jdbcType=VARCHAR},
      </if>
      <if test="kunnr != null">
        #{kunnr,jdbcType=VARCHAR},
      </if>
      <if test="konnr != null">
        #{konnr,jdbcType=VARCHAR},
      </if>
      <if test="abgru != null">
        #{abgru,jdbcType=VARCHAR},
      </if>
      <if test="autlf != null">
        #{autlf,jdbcType=VARCHAR},
      </if>
      <if test="weakt != null">
        #{weakt,jdbcType=VARCHAR},
      </if>
      <if test="reswk != null">
        #{reswk,jdbcType=VARCHAR},
      </if>
      <if test="lblif != null">
        #{lblif,jdbcType=VARCHAR},
      </if>
      <if test="inco1 != null">
        #{inco1,jdbcType=VARCHAR},
      </if>
      <if test="inco2 != null">
        #{inco2,jdbcType=VARCHAR},
      </if>
      <if test="ktwrt != null">
        #{ktwrt,jdbcType=DECIMAL},
      </if>
      <if test="submi != null">
        #{submi,jdbcType=VARCHAR},
      </if>
      <if test="knumv != null">
        #{knumv,jdbcType=VARCHAR},
      </if>
      <if test="kalsm != null">
        #{kalsm,jdbcType=VARCHAR},
      </if>
      <if test="stafo != null">
        #{stafo,jdbcType=VARCHAR},
      </if>
      <if test="lifre != null">
        #{lifre,jdbcType=VARCHAR},
      </if>
      <if test="exnum != null">
        #{exnum,jdbcType=VARCHAR},
      </if>
      <if test="unsez != null">
        #{unsez,jdbcType=VARCHAR},
      </if>
      <if test="logsy != null">
        #{logsy,jdbcType=VARCHAR},
      </if>
      <if test="upinc != null">
        #{upinc,jdbcType=VARCHAR},
      </if>
      <if test="stako != null">
        #{stako,jdbcType=VARCHAR},
      </if>
      <if test="frggr != null">
        #{frggr,jdbcType=VARCHAR},
      </if>
      <if test="frgsx != null">
        #{frgsx,jdbcType=VARCHAR},
      </if>
      <if test="frgke != null">
        #{frgke,jdbcType=VARCHAR},
      </if>
      <if test="frgzu != null">
        #{frgzu,jdbcType=VARCHAR},
      </if>
      <if test="frgrl != null">
        #{frgrl,jdbcType=VARCHAR},
      </if>
      <if test="lands != null">
        #{lands,jdbcType=VARCHAR},
      </if>
      <if test="lphis != null">
        #{lphis,jdbcType=VARCHAR},
      </if>
      <if test="adrnr != null">
        #{adrnr,jdbcType=VARCHAR},
      </if>
      <if test="stcegL != null">
        #{stcegL,jdbcType=VARCHAR},
      </if>
      <if test="stceg != null">
        #{stceg,jdbcType=VARCHAR},
      </if>
      <if test="absgr != null">
        #{absgr,jdbcType=VARCHAR},
      </if>
      <if test="addnr != null">
        #{addnr,jdbcType=VARCHAR},
      </if>
      <if test="kornr != null">
        #{kornr,jdbcType=VARCHAR},
      </if>
      <if test="memory != null">
        #{memory,jdbcType=VARCHAR},
      </if>
      <if test="procstat != null">
        #{procstat,jdbcType=VARCHAR},
      </if>
      <if test="rlwrt != null">
        #{rlwrt,jdbcType=DECIMAL},
      </if>
      <if test="revno != null">
        #{revno,jdbcType=VARCHAR},
      </if>
      <if test="scmproc != null">
        #{scmproc,jdbcType=VARCHAR},
      </if>
      <if test="reasonCode != null">
        #{reasonCode,jdbcType=VARCHAR},
      </if>
      <if test="memorytype != null">
        #{memorytype,jdbcType=VARCHAR},
      </if>
      <if test="rettp != null">
        #{rettp,jdbcType=VARCHAR},
      </if>
      <if test="retpc != null">
        #{retpc,jdbcType=DECIMAL},
      </if>
      <if test="dptyp != null">
        #{dptyp,jdbcType=VARCHAR},
      </if>
      <if test="dppct != null">
        #{dppct,jdbcType=DECIMAL},
      </if>
      <if test="dpamt != null">
        #{dpamt,jdbcType=DECIMAL},
      </if>
      <if test="dpdat != null">
        #{dpdat,jdbcType=VARCHAR},
      </if>
      <if test="msrId != null">
        #{msrId,jdbcType=VARCHAR},
      </if>
      <if test="hierarchyExists != null">
        #{hierarchyExists,jdbcType=VARCHAR},
      </if>
      <if test="thresholdExists != null">
        #{thresholdExists,jdbcType=VARCHAR},
      </if>
      <if test="legalContract != null">
        #{legalContract,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="releaseDate != null">
        #{releaseDate,jdbcType=VARCHAR},
      </if>
      <if test="vsart != null">
        #{vsart,jdbcType=VARCHAR},
      </if>
      <if test="handoverloc != null">
        #{handoverloc,jdbcType=VARCHAR},
      </if>
      <if test="shipcond != null">
        #{shipcond,jdbcType=VARCHAR},
      </if>
      <if test="incov != null">
        #{incov,jdbcType=VARCHAR},
      </if>
      <if test="inco2L != null">
        #{inco2L,jdbcType=VARCHAR},
      </if>
      <if test="inco3L != null">
        #{inco3L,jdbcType=VARCHAR},
      </if>
      <if test="grwcu != null">
        #{grwcu,jdbcType=VARCHAR},
      </if>
      <if test="intraRel != null">
        #{intraRel,jdbcType=VARCHAR},
      </if>
      <if test="intraExcl != null">
        #{intraExcl,jdbcType=VARCHAR},
      </if>
      <if test="qtnErlstSubmsnDate != null">
        #{qtnErlstSubmsnDate,jdbcType=VARCHAR},
      </if>
      <if test="followonDocCat != null">
        #{followonDocCat,jdbcType=VARCHAR},
      </if>
      <if test="followonDocType != null">
        #{followonDocType,jdbcType=VARCHAR},
      </if>
      <if test="dummyEkkoInclEewPs != null">
        #{dummyEkkoInclEewPs,jdbcType=VARCHAR},
      </if>
      <if test="externalsystem != null">
        #{externalsystem,jdbcType=VARCHAR},
      </if>
      <if test="externalreferenceid != null">
        #{externalreferenceid,jdbcType=VARCHAR},
      </if>
      <if test="extRevTmstmp != null">
        #{extRevTmstmp,jdbcType=DECIMAL},
      </if>
      <if test="iseopblocked != null">
        #{iseopblocked,jdbcType=VARCHAR},
      </if>
      <if test="isaged != null">
        #{isaged,jdbcType=VARCHAR},
      </if>
      <if test="forceId != null">
        #{forceId,jdbcType=VARCHAR},
      </if>
      <if test="forceCnt != null">
        #{forceCnt,jdbcType=VARCHAR},
      </if>
      <if test="relocId != null">
        #{relocId,jdbcType=VARCHAR},
      </if>
      <if test="relocSeqId != null">
        #{relocSeqId,jdbcType=VARCHAR},
      </if>
      <if test="sourceLogsys != null">
        #{sourceLogsys,jdbcType=VARCHAR},
      </if>
      <if test="fshTransaction != null">
        #{fshTransaction,jdbcType=VARCHAR},
      </if>
      <if test="fshItemGroup != null">
        #{fshItemGroup,jdbcType=VARCHAR},
      </if>
      <if test="fshVasLastItem != null">
        #{fshVasLastItem,jdbcType=VARCHAR},
      </if>
      <if test="fshOsStgChange != null">
        #{fshOsStgChange,jdbcType=VARCHAR},
      </if>
      <if test="tmsRefUuid != null">
        #{tmsRefUuid,jdbcType=VARCHAR},
      </if>
      <if test="zzzwlms != null">
        #{zzzwlms,jdbcType=VARCHAR},
      </if>
      <if test="zzzzshd != null">
        #{zzzzshd,jdbcType=VARCHAR},
      </if>
      <if test="zzzshkc != null">
        #{zzzshkc,jdbcType=VARCHAR},
      </if>
      <if test="zzzmdsq != null">
        #{zzzmdsq,jdbcType=VARCHAR},
      </if>
      <if test="zzzbeiz != null">
        #{zzzbeiz,jdbcType=VARCHAR},
      </if>
      <if test="zzzhgzt != null">
        #{zzzhgzt,jdbcType=VARCHAR},
      </if>
      <if test="zzqxdjh != null">
        #{zzqxdjh,jdbcType=VARCHAR},
      </if>
      <if test="zzzqhbs != null">
        #{zzzqhbs,jdbcType=VARCHAR},
      </if>
      <if test="zzzuserid != null">
        #{zzzuserid,jdbcType=VARCHAR},
      </if>
      <if test="zzcgy != null">
        #{zzcgy,jdbcType=VARCHAR},
      </if>
      <if test="zzdhy != null">
        #{zzdhy,jdbcType=VARCHAR},
      </if>
      <if test="zzcoer != null">
        #{zzcoer,jdbcType=VARCHAR},
      </if>
      <if test="zzcoca != null">
        #{zzcoca,jdbcType=VARCHAR},
      </if>
      <if test="zzycgdd != null">
        #{zzycgdd,jdbcType=VARCHAR},
      </if>
      <if test="zapcgk != null">
        #{zapcgk,jdbcType=VARCHAR},
      </if>
      <if test="apcgkExtend != null">
        #{apcgkExtend,jdbcType=VARCHAR},
      </if>
      <if test="zbasDate != null">
        #{zbasDate,jdbcType=VARCHAR},
      </if>
      <if test="zadattyp != null">
        #{zadattyp,jdbcType=VARCHAR},
      </if>
      <if test="zstartDat != null">
        #{zstartDat,jdbcType=VARCHAR},
      </if>
      <if test="zDev != null">
        #{zDev,jdbcType=DECIMAL},
      </if>
      <if test="zindanx != null">
        #{zindanx,jdbcType=VARCHAR},
      </if>
      <if test="zlimitDat != null">
        #{zlimitDat,jdbcType=VARCHAR},
      </if>
      <if test="numerator != null">
        #{numerator,jdbcType=VARCHAR},
      </if>
      <if test="hashcalBdat != null">
        #{hashcalBdat,jdbcType=VARCHAR},
      </if>
      <if test="hashcal != null">
        #{hashcal,jdbcType=VARCHAR},
      </if>
      <if test="negative != null">
        #{negative,jdbcType=VARCHAR},
      </if>
      <if test="hashcalExists != null">
        #{hashcalExists,jdbcType=VARCHAR},
      </if>
      <if test="knownIndex != null">
        #{knownIndex,jdbcType=VARCHAR},
      </if>
      <if test="postat != null">
        #{postat,jdbcType=VARCHAR},
      </if>
      <if test="vzskz != null">
        #{vzskz,jdbcType=VARCHAR},
      </if>
      <if test="fshSnstStatus != null">
        #{fshSnstStatus,jdbcType=VARCHAR},
      </if>
      <if test="proce != null">
        #{proce,jdbcType=VARCHAR},
      </if>
      <if test="conc != null">
        #{conc,jdbcType=VARCHAR},
      </if>
      <if test="cont != null">
        #{cont,jdbcType=VARCHAR},
      </if>
      <if test="comp != null">
        #{comp,jdbcType=VARCHAR},
      </if>
      <if test="outr != null">
        #{outr,jdbcType=VARCHAR},
      </if>
      <if test="desp != null">
        #{desp,jdbcType=VARCHAR},
      </if>
      <if test="despDat != null">
        #{despDat,jdbcType=VARCHAR},
      </if>
      <if test="despCargo != null">
        #{despCargo,jdbcType=VARCHAR},
      </if>
      <if test="pare != null">
        #{pare,jdbcType=VARCHAR},
      </if>
      <if test="pareDat != null">
        #{pareDat,jdbcType=VARCHAR},
      </if>
      <if test="pareCargo != null">
        #{pareCargo,jdbcType=VARCHAR},
      </if>
      <if test="pfmContract != null">
        #{pfmContract,jdbcType=VARCHAR},
      </if>
      <if test="pohfType != null">
        #{pohfType,jdbcType=VARCHAR},
      </if>
      <if test="eqEindt != null">
        #{eqEindt,jdbcType=VARCHAR},
      </if>
      <if test="eqWerks != null">
        #{eqWerks,jdbcType=VARCHAR},
      </if>
      <if test="fixpo != null">
        #{fixpo,jdbcType=VARCHAR},
      </if>
      <if test="ekgrpAllow != null">
        #{ekgrpAllow,jdbcType=VARCHAR},
      </if>
      <if test="werksAllow != null">
        #{werksAllow,jdbcType=VARCHAR},
      </if>
      <if test="contractAllow != null">
        #{contractAllow,jdbcType=VARCHAR},
      </if>
      <if test="pstypAllow != null">
        #{pstypAllow,jdbcType=VARCHAR},
      </if>
      <if test="fixpoAllow != null">
        #{fixpoAllow,jdbcType=VARCHAR},
      </if>
      <if test="keyIdAllow != null">
        #{keyIdAllow,jdbcType=VARCHAR},
      </if>
      <if test="aurelAllow != null">
        #{aurelAllow,jdbcType=VARCHAR},
      </if>
      <if test="delperAllow != null">
        #{delperAllow,jdbcType=VARCHAR},
      </if>
      <if test="eindtAllow != null">
        #{eindtAllow,jdbcType=VARCHAR},
      </if>
      <if test="ltsnrAllow != null">
        #{ltsnrAllow,jdbcType=VARCHAR},
      </if>
      <if test="otbLevel != null">
        #{otbLevel,jdbcType=VARCHAR},
      </if>
      <if test="otbCondType != null">
        #{otbCondType,jdbcType=VARCHAR},
      </if>
      <if test="keyId != null">
        #{keyId,jdbcType=VARCHAR},
      </if>
      <if test="otbValue != null">
        #{otbValue,jdbcType=DECIMAL},
      </if>
      <if test="otbCurr != null">
        #{otbCurr,jdbcType=VARCHAR},
      </if>
      <if test="otbResValue != null">
        #{otbResValue,jdbcType=DECIMAL},
      </if>
      <if test="otbSpecValue != null">
        #{otbSpecValue,jdbcType=DECIMAL},
      </if>
      <if test="sprRsnProfile != null">
        #{sprRsnProfile,jdbcType=VARCHAR},
      </if>
      <if test="budgType != null">
        #{budgType,jdbcType=VARCHAR},
      </if>
      <if test="otbStatus != null">
        #{otbStatus,jdbcType=VARCHAR},
      </if>
      <if test="otbReason != null">
        #{otbReason,jdbcType=VARCHAR},
      </if>
      <if test="checkType != null">
        #{checkType,jdbcType=VARCHAR},
      </if>
      <if test="conOtbReq != null">
        #{conOtbReq,jdbcType=VARCHAR},
      </if>
      <if test="conPrebookLev != null">
        #{conPrebookLev,jdbcType=VARCHAR},
      </if>
      <if test="conDistrLev != null">
        #{conDistrLev,jdbcType=VARCHAR},
      </if>
      <if test="zzyfye != null">
        #{zzyfye,jdbcType=VARCHAR},
      </if>
      <if test="zsqhzh != null">
        #{zsqhzh,jdbcType=VARCHAR},
      </if>
      <if test="zzsrmzt != null">
        #{zzsrmzt,jdbcType=VARCHAR},
      </if>
      <if test="zzpaid != null">
        #{zzpaid,jdbcType=VARCHAR},
      </if>
      <if test="zzpamt != null">
        #{zzpamt,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.iscm.entity.SapEkkoExample" resultType="java.lang.Long">
    select count(*) from SAP_EKKO
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update SAP_EKKO
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.mandt != null">
        MANDT = #{record.mandt,jdbcType=VARCHAR},
      </if>
      <if test="record.ebeln != null">
        EBELN = #{record.ebeln,jdbcType=VARCHAR},
      </if>
      <if test="record.bukrs != null">
        BUKRS = #{record.bukrs,jdbcType=VARCHAR},
      </if>
      <if test="record.bstyp != null">
        BSTYP = #{record.bstyp,jdbcType=VARCHAR},
      </if>
      <if test="record.bsart != null">
        BSART = #{record.bsart,jdbcType=VARCHAR},
      </if>
      <if test="record.bsakz != null">
        BSAKZ = #{record.bsakz,jdbcType=VARCHAR},
      </if>
      <if test="record.loekz != null">
        LOEKZ = #{record.loekz,jdbcType=VARCHAR},
      </if>
      <if test="record.statu != null">
        STATU = #{record.statu,jdbcType=VARCHAR},
      </if>
      <if test="record.aedat != null">
        AEDAT = #{record.aedat,jdbcType=VARCHAR},
      </if>
      <if test="record.ernam != null">
        ERNAM = #{record.ernam,jdbcType=VARCHAR},
      </if>
      <if test="record.lastchangedatetime != null">
        LASTCHANGEDATETIME = #{record.lastchangedatetime,jdbcType=DECIMAL},
      </if>
      <if test="record.pincr != null">
        PINCR = #{record.pincr,jdbcType=VARCHAR},
      </if>
      <if test="record.lponr != null">
        LPONR = #{record.lponr,jdbcType=VARCHAR},
      </if>
      <if test="record.lifnr != null">
        LIFNR = #{record.lifnr,jdbcType=VARCHAR},
      </if>
      <if test="record.spras != null">
        SPRAS = #{record.spras,jdbcType=VARCHAR},
      </if>
      <if test="record.zterm != null">
        ZTERM = #{record.zterm,jdbcType=VARCHAR},
      </if>
      <if test="record.zbd1t != null">
        ZBD1T = #{record.zbd1t,jdbcType=DECIMAL},
      </if>
      <if test="record.zbd2t != null">
        ZBD2T = #{record.zbd2t,jdbcType=DECIMAL},
      </if>
      <if test="record.zbd3t != null">
        ZBD3T = #{record.zbd3t,jdbcType=DECIMAL},
      </if>
      <if test="record.zbd1p != null">
        ZBD1P = #{record.zbd1p,jdbcType=DECIMAL},
      </if>
      <if test="record.zbd2p != null">
        ZBD2P = #{record.zbd2p,jdbcType=DECIMAL},
      </if>
      <if test="record.ekorg != null">
        EKORG = #{record.ekorg,jdbcType=VARCHAR},
      </if>
      <if test="record.ekgrp != null">
        EKGRP = #{record.ekgrp,jdbcType=VARCHAR},
      </if>
      <if test="record.waers != null">
        WAERS = #{record.waers,jdbcType=VARCHAR},
      </if>
      <if test="record.wkurs != null">
        WKURS = #{record.wkurs,jdbcType=DECIMAL},
      </if>
      <if test="record.kufix != null">
        KUFIX = #{record.kufix,jdbcType=VARCHAR},
      </if>
      <if test="record.bedat != null">
        BEDAT = #{record.bedat,jdbcType=VARCHAR},
      </if>
      <if test="record.kdatb != null">
        KDATB = #{record.kdatb,jdbcType=VARCHAR},
      </if>
      <if test="record.kdate != null">
        KDATE = #{record.kdate,jdbcType=VARCHAR},
      </if>
      <if test="record.bwbdt != null">
        BWBDT = #{record.bwbdt,jdbcType=VARCHAR},
      </if>
      <if test="record.angdt != null">
        ANGDT = #{record.angdt,jdbcType=VARCHAR},
      </if>
      <if test="record.bnddt != null">
        BNDDT = #{record.bnddt,jdbcType=VARCHAR},
      </if>
      <if test="record.gwldt != null">
        GWLDT = #{record.gwldt,jdbcType=VARCHAR},
      </if>
      <if test="record.ausnr != null">
        AUSNR = #{record.ausnr,jdbcType=VARCHAR},
      </if>
      <if test="record.angnr != null">
        ANGNR = #{record.angnr,jdbcType=VARCHAR},
      </if>
      <if test="record.ihran != null">
        IHRAN = #{record.ihran,jdbcType=VARCHAR},
      </if>
      <if test="record.ihrez != null">
        IHREZ = #{record.ihrez,jdbcType=VARCHAR},
      </if>
      <if test="record.verkf != null">
        VERKF = #{record.verkf,jdbcType=VARCHAR},
      </if>
      <if test="record.telf1 != null">
        TELF1 = #{record.telf1,jdbcType=VARCHAR},
      </if>
      <if test="record.llief != null">
        LLIEF = #{record.llief,jdbcType=VARCHAR},
      </if>
      <if test="record.kunnr != null">
        KUNNR = #{record.kunnr,jdbcType=VARCHAR},
      </if>
      <if test="record.konnr != null">
        KONNR = #{record.konnr,jdbcType=VARCHAR},
      </if>
      <if test="record.abgru != null">
        ABGRU = #{record.abgru,jdbcType=VARCHAR},
      </if>
      <if test="record.autlf != null">
        AUTLF = #{record.autlf,jdbcType=VARCHAR},
      </if>
      <if test="record.weakt != null">
        WEAKT = #{record.weakt,jdbcType=VARCHAR},
      </if>
      <if test="record.reswk != null">
        RESWK = #{record.reswk,jdbcType=VARCHAR},
      </if>
      <if test="record.lblif != null">
        LBLIF = #{record.lblif,jdbcType=VARCHAR},
      </if>
      <if test="record.inco1 != null">
        INCO1 = #{record.inco1,jdbcType=VARCHAR},
      </if>
      <if test="record.inco2 != null">
        INCO2 = #{record.inco2,jdbcType=VARCHAR},
      </if>
      <if test="record.ktwrt != null">
        KTWRT = #{record.ktwrt,jdbcType=DECIMAL},
      </if>
      <if test="record.submi != null">
        SUBMI = #{record.submi,jdbcType=VARCHAR},
      </if>
      <if test="record.knumv != null">
        KNUMV = #{record.knumv,jdbcType=VARCHAR},
      </if>
      <if test="record.kalsm != null">
        KALSM = #{record.kalsm,jdbcType=VARCHAR},
      </if>
      <if test="record.stafo != null">
        STAFO = #{record.stafo,jdbcType=VARCHAR},
      </if>
      <if test="record.lifre != null">
        LIFRE = #{record.lifre,jdbcType=VARCHAR},
      </if>
      <if test="record.exnum != null">
        EXNUM = #{record.exnum,jdbcType=VARCHAR},
      </if>
      <if test="record.unsez != null">
        UNSEZ = #{record.unsez,jdbcType=VARCHAR},
      </if>
      <if test="record.logsy != null">
        LOGSY = #{record.logsy,jdbcType=VARCHAR},
      </if>
      <if test="record.upinc != null">
        UPINC = #{record.upinc,jdbcType=VARCHAR},
      </if>
      <if test="record.stako != null">
        STAKO = #{record.stako,jdbcType=VARCHAR},
      </if>
      <if test="record.frggr != null">
        FRGGR = #{record.frggr,jdbcType=VARCHAR},
      </if>
      <if test="record.frgsx != null">
        FRGSX = #{record.frgsx,jdbcType=VARCHAR},
      </if>
      <if test="record.frgke != null">
        FRGKE = #{record.frgke,jdbcType=VARCHAR},
      </if>
      <if test="record.frgzu != null">
        FRGZU = #{record.frgzu,jdbcType=VARCHAR},
      </if>
      <if test="record.frgrl != null">
        FRGRL = #{record.frgrl,jdbcType=VARCHAR},
      </if>
      <if test="record.lands != null">
        LANDS = #{record.lands,jdbcType=VARCHAR},
      </if>
      <if test="record.lphis != null">
        LPHIS = #{record.lphis,jdbcType=VARCHAR},
      </if>
      <if test="record.adrnr != null">
        ADRNR = #{record.adrnr,jdbcType=VARCHAR},
      </if>
      <if test="record.stcegL != null">
        STCEG_L = #{record.stcegL,jdbcType=VARCHAR},
      </if>
      <if test="record.stceg != null">
        STCEG = #{record.stceg,jdbcType=VARCHAR},
      </if>
      <if test="record.absgr != null">
        ABSGR = #{record.absgr,jdbcType=VARCHAR},
      </if>
      <if test="record.addnr != null">
        ADDNR = #{record.addnr,jdbcType=VARCHAR},
      </if>
      <if test="record.kornr != null">
        KORNR = #{record.kornr,jdbcType=VARCHAR},
      </if>
      <if test="record.memory != null">
        MEMORY = #{record.memory,jdbcType=VARCHAR},
      </if>
      <if test="record.procstat != null">
        PROCSTAT = #{record.procstat,jdbcType=VARCHAR},
      </if>
      <if test="record.rlwrt != null">
        RLWRT = #{record.rlwrt,jdbcType=DECIMAL},
      </if>
      <if test="record.revno != null">
        REVNO = #{record.revno,jdbcType=VARCHAR},
      </if>
      <if test="record.scmproc != null">
        SCMPROC = #{record.scmproc,jdbcType=VARCHAR},
      </if>
      <if test="record.reasonCode != null">
        REASON_CODE = #{record.reasonCode,jdbcType=VARCHAR},
      </if>
      <if test="record.memorytype != null">
        MEMORYTYPE = #{record.memorytype,jdbcType=VARCHAR},
      </if>
      <if test="record.rettp != null">
        RETTP = #{record.rettp,jdbcType=VARCHAR},
      </if>
      <if test="record.retpc != null">
        RETPC = #{record.retpc,jdbcType=DECIMAL},
      </if>
      <if test="record.dptyp != null">
        DPTYP = #{record.dptyp,jdbcType=VARCHAR},
      </if>
      <if test="record.dppct != null">
        DPPCT = #{record.dppct,jdbcType=DECIMAL},
      </if>
      <if test="record.dpamt != null">
        DPAMT = #{record.dpamt,jdbcType=DECIMAL},
      </if>
      <if test="record.dpdat != null">
        DPDAT = #{record.dpdat,jdbcType=VARCHAR},
      </if>
      <if test="record.msrId != null">
        MSR_ID = #{record.msrId,jdbcType=VARCHAR},
      </if>
      <if test="record.hierarchyExists != null">
        HIERARCHY_EXISTS = #{record.hierarchyExists,jdbcType=VARCHAR},
      </if>
      <if test="record.thresholdExists != null">
        THRESHOLD_EXISTS = #{record.thresholdExists,jdbcType=VARCHAR},
      </if>
      <if test="record.legalContract != null">
        LEGAL_CONTRACT = #{record.legalContract,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        DESCRIPTION = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.releaseDate != null">
        RELEASE_DATE = #{record.releaseDate,jdbcType=VARCHAR},
      </if>
      <if test="record.vsart != null">
        VSART = #{record.vsart,jdbcType=VARCHAR},
      </if>
      <if test="record.handoverloc != null">
        HANDOVERLOC = #{record.handoverloc,jdbcType=VARCHAR},
      </if>
      <if test="record.shipcond != null">
        SHIPCOND = #{record.shipcond,jdbcType=VARCHAR},
      </if>
      <if test="record.incov != null">
        INCOV = #{record.incov,jdbcType=VARCHAR},
      </if>
      <if test="record.inco2L != null">
        INCO2_L = #{record.inco2L,jdbcType=VARCHAR},
      </if>
      <if test="record.inco3L != null">
        INCO3_L = #{record.inco3L,jdbcType=VARCHAR},
      </if>
      <if test="record.grwcu != null">
        GRWCU = #{record.grwcu,jdbcType=VARCHAR},
      </if>
      <if test="record.intraRel != null">
        INTRA_REL = #{record.intraRel,jdbcType=VARCHAR},
      </if>
      <if test="record.intraExcl != null">
        INTRA_EXCL = #{record.intraExcl,jdbcType=VARCHAR},
      </if>
      <if test="record.qtnErlstSubmsnDate != null">
        QTN_ERLST_SUBMSN_DATE = #{record.qtnErlstSubmsnDate,jdbcType=VARCHAR},
      </if>
      <if test="record.followonDocCat != null">
        FOLLOWON_DOC_CAT = #{record.followonDocCat,jdbcType=VARCHAR},
      </if>
      <if test="record.followonDocType != null">
        FOLLOWON_DOC_TYPE = #{record.followonDocType,jdbcType=VARCHAR},
      </if>
      <if test="record.dummyEkkoInclEewPs != null">
        DUMMY_EKKO_INCL_EEW_PS = #{record.dummyEkkoInclEewPs,jdbcType=VARCHAR},
      </if>
      <if test="record.externalsystem != null">
        EXTERNALSYSTEM = #{record.externalsystem,jdbcType=VARCHAR},
      </if>
      <if test="record.externalreferenceid != null">
        EXTERNALREFERENCEID = #{record.externalreferenceid,jdbcType=VARCHAR},
      </if>
      <if test="record.extRevTmstmp != null">
        EXT_REV_TMSTMP = #{record.extRevTmstmp,jdbcType=DECIMAL},
      </if>
      <if test="record.iseopblocked != null">
        ISEOPBLOCKED = #{record.iseopblocked,jdbcType=VARCHAR},
      </if>
      <if test="record.isaged != null">
        ISAGED = #{record.isaged,jdbcType=VARCHAR},
      </if>
      <if test="record.forceId != null">
        FORCE_ID = #{record.forceId,jdbcType=VARCHAR},
      </if>
      <if test="record.forceCnt != null">
        FORCE_CNT = #{record.forceCnt,jdbcType=VARCHAR},
      </if>
      <if test="record.relocId != null">
        RELOC_ID = #{record.relocId,jdbcType=VARCHAR},
      </if>
      <if test="record.relocSeqId != null">
        RELOC_SEQ_ID = #{record.relocSeqId,jdbcType=VARCHAR},
      </if>
      <if test="record.sourceLogsys != null">
        SOURCE_LOGSYS = #{record.sourceLogsys,jdbcType=VARCHAR},
      </if>
      <if test="record.fshTransaction != null">
        FSH_TRANSACTION = #{record.fshTransaction,jdbcType=VARCHAR},
      </if>
      <if test="record.fshItemGroup != null">
        FSH_ITEM_GROUP = #{record.fshItemGroup,jdbcType=VARCHAR},
      </if>
      <if test="record.fshVasLastItem != null">
        FSH_VAS_LAST_ITEM = #{record.fshVasLastItem,jdbcType=VARCHAR},
      </if>
      <if test="record.fshOsStgChange != null">
        FSH_OS_STG_CHANGE = #{record.fshOsStgChange,jdbcType=VARCHAR},
      </if>
      <if test="record.tmsRefUuid != null">
        TMS_REF_UUID = #{record.tmsRefUuid,jdbcType=VARCHAR},
      </if>
      <if test="record.zzzwlms != null">
        ZZZWLMS = #{record.zzzwlms,jdbcType=VARCHAR},
      </if>
      <if test="record.zzzzshd != null">
        ZZZZSHD = #{record.zzzzshd,jdbcType=VARCHAR},
      </if>
      <if test="record.zzzshkc != null">
        ZZZSHKC = #{record.zzzshkc,jdbcType=VARCHAR},
      </if>
      <if test="record.zzzmdsq != null">
        ZZZMDSQ = #{record.zzzmdsq,jdbcType=VARCHAR},
      </if>
      <if test="record.zzzbeiz != null">
        ZZZBEIZ = #{record.zzzbeiz,jdbcType=VARCHAR},
      </if>
      <if test="record.zzzhgzt != null">
        ZZZHGZT = #{record.zzzhgzt,jdbcType=VARCHAR},
      </if>
      <if test="record.zzqxdjh != null">
        ZZQXDJH = #{record.zzqxdjh,jdbcType=VARCHAR},
      </if>
      <if test="record.zzzqhbs != null">
        ZZZQHBS = #{record.zzzqhbs,jdbcType=VARCHAR},
      </if>
      <if test="record.zzzuserid != null">
        ZZZUSERID = #{record.zzzuserid,jdbcType=VARCHAR},
      </if>
      <if test="record.zzcgy != null">
        ZZCGY = #{record.zzcgy,jdbcType=VARCHAR},
      </if>
      <if test="record.zzdhy != null">
        ZZDHY = #{record.zzdhy,jdbcType=VARCHAR},
      </if>
      <if test="record.zzcoer != null">
        ZZCOER = #{record.zzcoer,jdbcType=VARCHAR},
      </if>
      <if test="record.zzcoca != null">
        ZZCOCA = #{record.zzcoca,jdbcType=VARCHAR},
      </if>
      <if test="record.zzycgdd != null">
        ZZYCGDD = #{record.zzycgdd,jdbcType=VARCHAR},
      </if>
      <if test="record.zapcgk != null">
        ZAPCGK = #{record.zapcgk,jdbcType=VARCHAR},
      </if>
      <if test="record.apcgkExtend != null">
        APCGK_EXTEND = #{record.apcgkExtend,jdbcType=VARCHAR},
      </if>
      <if test="record.zbasDate != null">
        ZBAS_DATE = #{record.zbasDate,jdbcType=VARCHAR},
      </if>
      <if test="record.zadattyp != null">
        ZADATTYP = #{record.zadattyp,jdbcType=VARCHAR},
      </if>
      <if test="record.zstartDat != null">
        ZSTART_DAT = #{record.zstartDat,jdbcType=VARCHAR},
      </if>
      <if test="record.zDev != null">
        Z_DEV = #{record.zDev,jdbcType=DECIMAL},
      </if>
      <if test="record.zindanx != null">
        ZINDANX = #{record.zindanx,jdbcType=VARCHAR},
      </if>
      <if test="record.zlimitDat != null">
        ZLIMIT_DAT = #{record.zlimitDat,jdbcType=VARCHAR},
      </if>
      <if test="record.numerator != null">
        NUMERATOR = #{record.numerator,jdbcType=VARCHAR},
      </if>
      <if test="record.hashcalBdat != null">
        HASHCAL_BDAT = #{record.hashcalBdat,jdbcType=VARCHAR},
      </if>
      <if test="record.hashcal != null">
        HASHCAL = #{record.hashcal,jdbcType=VARCHAR},
      </if>
      <if test="record.negative != null">
        NEGATIVE = #{record.negative,jdbcType=VARCHAR},
      </if>
      <if test="record.hashcalExists != null">
        HASHCAL_EXISTS = #{record.hashcalExists,jdbcType=VARCHAR},
      </if>
      <if test="record.knownIndex != null">
        KNOWN_INDEX = #{record.knownIndex,jdbcType=VARCHAR},
      </if>
      <if test="record.postat != null">
        POSTAT = #{record.postat,jdbcType=VARCHAR},
      </if>
      <if test="record.vzskz != null">
        VZSKZ = #{record.vzskz,jdbcType=VARCHAR},
      </if>
      <if test="record.fshSnstStatus != null">
        FSH_SNST_STATUS = #{record.fshSnstStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.proce != null">
        PROCE = #{record.proce,jdbcType=VARCHAR},
      </if>
      <if test="record.conc != null">
        CONC = #{record.conc,jdbcType=VARCHAR},
      </if>
      <if test="record.cont != null">
        CONT = #{record.cont,jdbcType=VARCHAR},
      </if>
      <if test="record.comp != null">
        COMP = #{record.comp,jdbcType=VARCHAR},
      </if>
      <if test="record.outr != null">
        OUTR = #{record.outr,jdbcType=VARCHAR},
      </if>
      <if test="record.desp != null">
        DESP = #{record.desp,jdbcType=VARCHAR},
      </if>
      <if test="record.despDat != null">
        DESP_DAT = #{record.despDat,jdbcType=VARCHAR},
      </if>
      <if test="record.despCargo != null">
        DESP_CARGO = #{record.despCargo,jdbcType=VARCHAR},
      </if>
      <if test="record.pare != null">
        PARE = #{record.pare,jdbcType=VARCHAR},
      </if>
      <if test="record.pareDat != null">
        PARE_DAT = #{record.pareDat,jdbcType=VARCHAR},
      </if>
      <if test="record.pareCargo != null">
        PARE_CARGO = #{record.pareCargo,jdbcType=VARCHAR},
      </if>
      <if test="record.pfmContract != null">
        PFM_CONTRACT = #{record.pfmContract,jdbcType=VARCHAR},
      </if>
      <if test="record.pohfType != null">
        POHF_TYPE = #{record.pohfType,jdbcType=VARCHAR},
      </if>
      <if test="record.eqEindt != null">
        EQ_EINDT = #{record.eqEindt,jdbcType=VARCHAR},
      </if>
      <if test="record.eqWerks != null">
        EQ_WERKS = #{record.eqWerks,jdbcType=VARCHAR},
      </if>
      <if test="record.fixpo != null">
        FIXPO = #{record.fixpo,jdbcType=VARCHAR},
      </if>
      <if test="record.ekgrpAllow != null">
        EKGRP_ALLOW = #{record.ekgrpAllow,jdbcType=VARCHAR},
      </if>
      <if test="record.werksAllow != null">
        WERKS_ALLOW = #{record.werksAllow,jdbcType=VARCHAR},
      </if>
      <if test="record.contractAllow != null">
        CONTRACT_ALLOW = #{record.contractAllow,jdbcType=VARCHAR},
      </if>
      <if test="record.pstypAllow != null">
        PSTYP_ALLOW = #{record.pstypAllow,jdbcType=VARCHAR},
      </if>
      <if test="record.fixpoAllow != null">
        FIXPO_ALLOW = #{record.fixpoAllow,jdbcType=VARCHAR},
      </if>
      <if test="record.keyIdAllow != null">
        KEY_ID_ALLOW = #{record.keyIdAllow,jdbcType=VARCHAR},
      </if>
      <if test="record.aurelAllow != null">
        AUREL_ALLOW = #{record.aurelAllow,jdbcType=VARCHAR},
      </if>
      <if test="record.delperAllow != null">
        DELPER_ALLOW = #{record.delperAllow,jdbcType=VARCHAR},
      </if>
      <if test="record.eindtAllow != null">
        EINDT_ALLOW = #{record.eindtAllow,jdbcType=VARCHAR},
      </if>
      <if test="record.ltsnrAllow != null">
        LTSNR_ALLOW = #{record.ltsnrAllow,jdbcType=VARCHAR},
      </if>
      <if test="record.otbLevel != null">
        OTB_LEVEL = #{record.otbLevel,jdbcType=VARCHAR},
      </if>
      <if test="record.otbCondType != null">
        OTB_COND_TYPE = #{record.otbCondType,jdbcType=VARCHAR},
      </if>
      <if test="record.keyId != null">
        KEY_ID = #{record.keyId,jdbcType=VARCHAR},
      </if>
      <if test="record.otbValue != null">
        OTB_VALUE = #{record.otbValue,jdbcType=DECIMAL},
      </if>
      <if test="record.otbCurr != null">
        OTB_CURR = #{record.otbCurr,jdbcType=VARCHAR},
      </if>
      <if test="record.otbResValue != null">
        OTB_RES_VALUE = #{record.otbResValue,jdbcType=DECIMAL},
      </if>
      <if test="record.otbSpecValue != null">
        OTB_SPEC_VALUE = #{record.otbSpecValue,jdbcType=DECIMAL},
      </if>
      <if test="record.sprRsnProfile != null">
        SPR_RSN_PROFILE = #{record.sprRsnProfile,jdbcType=VARCHAR},
      </if>
      <if test="record.budgType != null">
        BUDG_TYPE = #{record.budgType,jdbcType=VARCHAR},
      </if>
      <if test="record.otbStatus != null">
        OTB_STATUS = #{record.otbStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.otbReason != null">
        OTB_REASON = #{record.otbReason,jdbcType=VARCHAR},
      </if>
      <if test="record.checkType != null">
        CHECK_TYPE = #{record.checkType,jdbcType=VARCHAR},
      </if>
      <if test="record.conOtbReq != null">
        CON_OTB_REQ = #{record.conOtbReq,jdbcType=VARCHAR},
      </if>
      <if test="record.conPrebookLev != null">
        CON_PREBOOK_LEV = #{record.conPrebookLev,jdbcType=VARCHAR},
      </if>
      <if test="record.conDistrLev != null">
        CON_DISTR_LEV = #{record.conDistrLev,jdbcType=VARCHAR},
      </if>
      <if test="record.zzyfye != null">
        ZZYFYE = #{record.zzyfye,jdbcType=VARCHAR},
      </if>
      <if test="record.zsqhzh != null">
        ZSQHZH = #{record.zsqhzh,jdbcType=VARCHAR},
      </if>
      <if test="record.zzsrmzt != null">
        ZZSRMZT = #{record.zzsrmzt,jdbcType=VARCHAR},
      </if>
      <if test="record.zzpaid != null">
        ZZPAID = #{record.zzpaid,jdbcType=VARCHAR},
      </if>
      <if test="record.zzpamt != null">
        ZZPAMT = #{record.zzpamt,jdbcType=DECIMAL},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update SAP_EKKO
    set id = #{record.id,jdbcType=BIGINT},
      MANDT = #{record.mandt,jdbcType=VARCHAR},
      EBELN = #{record.ebeln,jdbcType=VARCHAR},
      BUKRS = #{record.bukrs,jdbcType=VARCHAR},
      BSTYP = #{record.bstyp,jdbcType=VARCHAR},
      BSART = #{record.bsart,jdbcType=VARCHAR},
      BSAKZ = #{record.bsakz,jdbcType=VARCHAR},
      LOEKZ = #{record.loekz,jdbcType=VARCHAR},
      STATU = #{record.statu,jdbcType=VARCHAR},
      AEDAT = #{record.aedat,jdbcType=VARCHAR},
      ERNAM = #{record.ernam,jdbcType=VARCHAR},
      LASTCHANGEDATETIME = #{record.lastchangedatetime,jdbcType=DECIMAL},
      PINCR = #{record.pincr,jdbcType=VARCHAR},
      LPONR = #{record.lponr,jdbcType=VARCHAR},
      LIFNR = #{record.lifnr,jdbcType=VARCHAR},
      SPRAS = #{record.spras,jdbcType=VARCHAR},
      ZTERM = #{record.zterm,jdbcType=VARCHAR},
      ZBD1T = #{record.zbd1t,jdbcType=DECIMAL},
      ZBD2T = #{record.zbd2t,jdbcType=DECIMAL},
      ZBD3T = #{record.zbd3t,jdbcType=DECIMAL},
      ZBD1P = #{record.zbd1p,jdbcType=DECIMAL},
      ZBD2P = #{record.zbd2p,jdbcType=DECIMAL},
      EKORG = #{record.ekorg,jdbcType=VARCHAR},
      EKGRP = #{record.ekgrp,jdbcType=VARCHAR},
      WAERS = #{record.waers,jdbcType=VARCHAR},
      WKURS = #{record.wkurs,jdbcType=DECIMAL},
      KUFIX = #{record.kufix,jdbcType=VARCHAR},
      BEDAT = #{record.bedat,jdbcType=VARCHAR},
      KDATB = #{record.kdatb,jdbcType=VARCHAR},
      KDATE = #{record.kdate,jdbcType=VARCHAR},
      BWBDT = #{record.bwbdt,jdbcType=VARCHAR},
      ANGDT = #{record.angdt,jdbcType=VARCHAR},
      BNDDT = #{record.bnddt,jdbcType=VARCHAR},
      GWLDT = #{record.gwldt,jdbcType=VARCHAR},
      AUSNR = #{record.ausnr,jdbcType=VARCHAR},
      ANGNR = #{record.angnr,jdbcType=VARCHAR},
      IHRAN = #{record.ihran,jdbcType=VARCHAR},
      IHREZ = #{record.ihrez,jdbcType=VARCHAR},
      VERKF = #{record.verkf,jdbcType=VARCHAR},
      TELF1 = #{record.telf1,jdbcType=VARCHAR},
      LLIEF = #{record.llief,jdbcType=VARCHAR},
      KUNNR = #{record.kunnr,jdbcType=VARCHAR},
      KONNR = #{record.konnr,jdbcType=VARCHAR},
      ABGRU = #{record.abgru,jdbcType=VARCHAR},
      AUTLF = #{record.autlf,jdbcType=VARCHAR},
      WEAKT = #{record.weakt,jdbcType=VARCHAR},
      RESWK = #{record.reswk,jdbcType=VARCHAR},
      LBLIF = #{record.lblif,jdbcType=VARCHAR},
      INCO1 = #{record.inco1,jdbcType=VARCHAR},
      INCO2 = #{record.inco2,jdbcType=VARCHAR},
      KTWRT = #{record.ktwrt,jdbcType=DECIMAL},
      SUBMI = #{record.submi,jdbcType=VARCHAR},
      KNUMV = #{record.knumv,jdbcType=VARCHAR},
      KALSM = #{record.kalsm,jdbcType=VARCHAR},
      STAFO = #{record.stafo,jdbcType=VARCHAR},
      LIFRE = #{record.lifre,jdbcType=VARCHAR},
      EXNUM = #{record.exnum,jdbcType=VARCHAR},
      UNSEZ = #{record.unsez,jdbcType=VARCHAR},
      LOGSY = #{record.logsy,jdbcType=VARCHAR},
      UPINC = #{record.upinc,jdbcType=VARCHAR},
      STAKO = #{record.stako,jdbcType=VARCHAR},
      FRGGR = #{record.frggr,jdbcType=VARCHAR},
      FRGSX = #{record.frgsx,jdbcType=VARCHAR},
      FRGKE = #{record.frgke,jdbcType=VARCHAR},
      FRGZU = #{record.frgzu,jdbcType=VARCHAR},
      FRGRL = #{record.frgrl,jdbcType=VARCHAR},
      LANDS = #{record.lands,jdbcType=VARCHAR},
      LPHIS = #{record.lphis,jdbcType=VARCHAR},
      ADRNR = #{record.adrnr,jdbcType=VARCHAR},
      STCEG_L = #{record.stcegL,jdbcType=VARCHAR},
      STCEG = #{record.stceg,jdbcType=VARCHAR},
      ABSGR = #{record.absgr,jdbcType=VARCHAR},
      ADDNR = #{record.addnr,jdbcType=VARCHAR},
      KORNR = #{record.kornr,jdbcType=VARCHAR},
      MEMORY = #{record.memory,jdbcType=VARCHAR},
      PROCSTAT = #{record.procstat,jdbcType=VARCHAR},
      RLWRT = #{record.rlwrt,jdbcType=DECIMAL},
      REVNO = #{record.revno,jdbcType=VARCHAR},
      SCMPROC = #{record.scmproc,jdbcType=VARCHAR},
      REASON_CODE = #{record.reasonCode,jdbcType=VARCHAR},
      MEMORYTYPE = #{record.memorytype,jdbcType=VARCHAR},
      RETTP = #{record.rettp,jdbcType=VARCHAR},
      RETPC = #{record.retpc,jdbcType=DECIMAL},
      DPTYP = #{record.dptyp,jdbcType=VARCHAR},
      DPPCT = #{record.dppct,jdbcType=DECIMAL},
      DPAMT = #{record.dpamt,jdbcType=DECIMAL},
      DPDAT = #{record.dpdat,jdbcType=VARCHAR},
      MSR_ID = #{record.msrId,jdbcType=VARCHAR},
      HIERARCHY_EXISTS = #{record.hierarchyExists,jdbcType=VARCHAR},
      THRESHOLD_EXISTS = #{record.thresholdExists,jdbcType=VARCHAR},
      LEGAL_CONTRACT = #{record.legalContract,jdbcType=VARCHAR},
      DESCRIPTION = #{record.description,jdbcType=VARCHAR},
      RELEASE_DATE = #{record.releaseDate,jdbcType=VARCHAR},
      VSART = #{record.vsart,jdbcType=VARCHAR},
      HANDOVERLOC = #{record.handoverloc,jdbcType=VARCHAR},
      SHIPCOND = #{record.shipcond,jdbcType=VARCHAR},
      INCOV = #{record.incov,jdbcType=VARCHAR},
      INCO2_L = #{record.inco2L,jdbcType=VARCHAR},
      INCO3_L = #{record.inco3L,jdbcType=VARCHAR},
      GRWCU = #{record.grwcu,jdbcType=VARCHAR},
      INTRA_REL = #{record.intraRel,jdbcType=VARCHAR},
      INTRA_EXCL = #{record.intraExcl,jdbcType=VARCHAR},
      QTN_ERLST_SUBMSN_DATE = #{record.qtnErlstSubmsnDate,jdbcType=VARCHAR},
      FOLLOWON_DOC_CAT = #{record.followonDocCat,jdbcType=VARCHAR},
      FOLLOWON_DOC_TYPE = #{record.followonDocType,jdbcType=VARCHAR},
      DUMMY_EKKO_INCL_EEW_PS = #{record.dummyEkkoInclEewPs,jdbcType=VARCHAR},
      EXTERNALSYSTEM = #{record.externalsystem,jdbcType=VARCHAR},
      EXTERNALREFERENCEID = #{record.externalreferenceid,jdbcType=VARCHAR},
      EXT_REV_TMSTMP = #{record.extRevTmstmp,jdbcType=DECIMAL},
      ISEOPBLOCKED = #{record.iseopblocked,jdbcType=VARCHAR},
      ISAGED = #{record.isaged,jdbcType=VARCHAR},
      FORCE_ID = #{record.forceId,jdbcType=VARCHAR},
      FORCE_CNT = #{record.forceCnt,jdbcType=VARCHAR},
      RELOC_ID = #{record.relocId,jdbcType=VARCHAR},
      RELOC_SEQ_ID = #{record.relocSeqId,jdbcType=VARCHAR},
      SOURCE_LOGSYS = #{record.sourceLogsys,jdbcType=VARCHAR},
      FSH_TRANSACTION = #{record.fshTransaction,jdbcType=VARCHAR},
      FSH_ITEM_GROUP = #{record.fshItemGroup,jdbcType=VARCHAR},
      FSH_VAS_LAST_ITEM = #{record.fshVasLastItem,jdbcType=VARCHAR},
      FSH_OS_STG_CHANGE = #{record.fshOsStgChange,jdbcType=VARCHAR},
      TMS_REF_UUID = #{record.tmsRefUuid,jdbcType=VARCHAR},
      ZZZWLMS = #{record.zzzwlms,jdbcType=VARCHAR},
      ZZZZSHD = #{record.zzzzshd,jdbcType=VARCHAR},
      ZZZSHKC = #{record.zzzshkc,jdbcType=VARCHAR},
      ZZZMDSQ = #{record.zzzmdsq,jdbcType=VARCHAR},
      ZZZBEIZ = #{record.zzzbeiz,jdbcType=VARCHAR},
      ZZZHGZT = #{record.zzzhgzt,jdbcType=VARCHAR},
      ZZQXDJH = #{record.zzqxdjh,jdbcType=VARCHAR},
      ZZZQHBS = #{record.zzzqhbs,jdbcType=VARCHAR},
      ZZZUSERID = #{record.zzzuserid,jdbcType=VARCHAR},
      ZZCGY = #{record.zzcgy,jdbcType=VARCHAR},
      ZZDHY = #{record.zzdhy,jdbcType=VARCHAR},
      ZZCOER = #{record.zzcoer,jdbcType=VARCHAR},
      ZZCOCA = #{record.zzcoca,jdbcType=VARCHAR},
      ZZYCGDD = #{record.zzycgdd,jdbcType=VARCHAR},
      ZAPCGK = #{record.zapcgk,jdbcType=VARCHAR},
      APCGK_EXTEND = #{record.apcgkExtend,jdbcType=VARCHAR},
      ZBAS_DATE = #{record.zbasDate,jdbcType=VARCHAR},
      ZADATTYP = #{record.zadattyp,jdbcType=VARCHAR},
      ZSTART_DAT = #{record.zstartDat,jdbcType=VARCHAR},
      Z_DEV = #{record.zDev,jdbcType=DECIMAL},
      ZINDANX = #{record.zindanx,jdbcType=VARCHAR},
      ZLIMIT_DAT = #{record.zlimitDat,jdbcType=VARCHAR},
      NUMERATOR = #{record.numerator,jdbcType=VARCHAR},
      HASHCAL_BDAT = #{record.hashcalBdat,jdbcType=VARCHAR},
      HASHCAL = #{record.hashcal,jdbcType=VARCHAR},
      NEGATIVE = #{record.negative,jdbcType=VARCHAR},
      HASHCAL_EXISTS = #{record.hashcalExists,jdbcType=VARCHAR},
      KNOWN_INDEX = #{record.knownIndex,jdbcType=VARCHAR},
      POSTAT = #{record.postat,jdbcType=VARCHAR},
      VZSKZ = #{record.vzskz,jdbcType=VARCHAR},
      FSH_SNST_STATUS = #{record.fshSnstStatus,jdbcType=VARCHAR},
      PROCE = #{record.proce,jdbcType=VARCHAR},
      CONC = #{record.conc,jdbcType=VARCHAR},
      CONT = #{record.cont,jdbcType=VARCHAR},
      COMP = #{record.comp,jdbcType=VARCHAR},
      OUTR = #{record.outr,jdbcType=VARCHAR},
      DESP = #{record.desp,jdbcType=VARCHAR},
      DESP_DAT = #{record.despDat,jdbcType=VARCHAR},
      DESP_CARGO = #{record.despCargo,jdbcType=VARCHAR},
      PARE = #{record.pare,jdbcType=VARCHAR},
      PARE_DAT = #{record.pareDat,jdbcType=VARCHAR},
      PARE_CARGO = #{record.pareCargo,jdbcType=VARCHAR},
      PFM_CONTRACT = #{record.pfmContract,jdbcType=VARCHAR},
      POHF_TYPE = #{record.pohfType,jdbcType=VARCHAR},
      EQ_EINDT = #{record.eqEindt,jdbcType=VARCHAR},
      EQ_WERKS = #{record.eqWerks,jdbcType=VARCHAR},
      FIXPO = #{record.fixpo,jdbcType=VARCHAR},
      EKGRP_ALLOW = #{record.ekgrpAllow,jdbcType=VARCHAR},
      WERKS_ALLOW = #{record.werksAllow,jdbcType=VARCHAR},
      CONTRACT_ALLOW = #{record.contractAllow,jdbcType=VARCHAR},
      PSTYP_ALLOW = #{record.pstypAllow,jdbcType=VARCHAR},
      FIXPO_ALLOW = #{record.fixpoAllow,jdbcType=VARCHAR},
      KEY_ID_ALLOW = #{record.keyIdAllow,jdbcType=VARCHAR},
      AUREL_ALLOW = #{record.aurelAllow,jdbcType=VARCHAR},
      DELPER_ALLOW = #{record.delperAllow,jdbcType=VARCHAR},
      EINDT_ALLOW = #{record.eindtAllow,jdbcType=VARCHAR},
      LTSNR_ALLOW = #{record.ltsnrAllow,jdbcType=VARCHAR},
      OTB_LEVEL = #{record.otbLevel,jdbcType=VARCHAR},
      OTB_COND_TYPE = #{record.otbCondType,jdbcType=VARCHAR},
      KEY_ID = #{record.keyId,jdbcType=VARCHAR},
      OTB_VALUE = #{record.otbValue,jdbcType=DECIMAL},
      OTB_CURR = #{record.otbCurr,jdbcType=VARCHAR},
      OTB_RES_VALUE = #{record.otbResValue,jdbcType=DECIMAL},
      OTB_SPEC_VALUE = #{record.otbSpecValue,jdbcType=DECIMAL},
      SPR_RSN_PROFILE = #{record.sprRsnProfile,jdbcType=VARCHAR},
      BUDG_TYPE = #{record.budgType,jdbcType=VARCHAR},
      OTB_STATUS = #{record.otbStatus,jdbcType=VARCHAR},
      OTB_REASON = #{record.otbReason,jdbcType=VARCHAR},
      CHECK_TYPE = #{record.checkType,jdbcType=VARCHAR},
      CON_OTB_REQ = #{record.conOtbReq,jdbcType=VARCHAR},
      CON_PREBOOK_LEV = #{record.conPrebookLev,jdbcType=VARCHAR},
      CON_DISTR_LEV = #{record.conDistrLev,jdbcType=VARCHAR},
      ZZYFYE = #{record.zzyfye,jdbcType=VARCHAR},
      ZSQHZH = #{record.zsqhzh,jdbcType=VARCHAR},
      ZZSRMZT = #{record.zzsrmzt,jdbcType=VARCHAR},
      ZZPAID = #{record.zzpaid,jdbcType=VARCHAR},
      ZZPAMT = #{record.zzpamt,jdbcType=DECIMAL}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.iscm.entity.SapEkko">
    update SAP_EKKO
    <set>
      <if test="mandt != null">
        MANDT = #{mandt,jdbcType=VARCHAR},
      </if>
      <if test="ebeln != null">
        EBELN = #{ebeln,jdbcType=VARCHAR},
      </if>
      <if test="bukrs != null">
        BUKRS = #{bukrs,jdbcType=VARCHAR},
      </if>
      <if test="bstyp != null">
        BSTYP = #{bstyp,jdbcType=VARCHAR},
      </if>
      <if test="bsart != null">
        BSART = #{bsart,jdbcType=VARCHAR},
      </if>
      <if test="bsakz != null">
        BSAKZ = #{bsakz,jdbcType=VARCHAR},
      </if>
      <if test="loekz != null">
        LOEKZ = #{loekz,jdbcType=VARCHAR},
      </if>
      <if test="statu != null">
        STATU = #{statu,jdbcType=VARCHAR},
      </if>
      <if test="aedat != null">
        AEDAT = #{aedat,jdbcType=VARCHAR},
      </if>
      <if test="ernam != null">
        ERNAM = #{ernam,jdbcType=VARCHAR},
      </if>
      <if test="lastchangedatetime != null">
        LASTCHANGEDATETIME = #{lastchangedatetime,jdbcType=DECIMAL},
      </if>
      <if test="pincr != null">
        PINCR = #{pincr,jdbcType=VARCHAR},
      </if>
      <if test="lponr != null">
        LPONR = #{lponr,jdbcType=VARCHAR},
      </if>
      <if test="lifnr != null">
        LIFNR = #{lifnr,jdbcType=VARCHAR},
      </if>
      <if test="spras != null">
        SPRAS = #{spras,jdbcType=VARCHAR},
      </if>
      <if test="zterm != null">
        ZTERM = #{zterm,jdbcType=VARCHAR},
      </if>
      <if test="zbd1t != null">
        ZBD1T = #{zbd1t,jdbcType=DECIMAL},
      </if>
      <if test="zbd2t != null">
        ZBD2T = #{zbd2t,jdbcType=DECIMAL},
      </if>
      <if test="zbd3t != null">
        ZBD3T = #{zbd3t,jdbcType=DECIMAL},
      </if>
      <if test="zbd1p != null">
        ZBD1P = #{zbd1p,jdbcType=DECIMAL},
      </if>
      <if test="zbd2p != null">
        ZBD2P = #{zbd2p,jdbcType=DECIMAL},
      </if>
      <if test="ekorg != null">
        EKORG = #{ekorg,jdbcType=VARCHAR},
      </if>
      <if test="ekgrp != null">
        EKGRP = #{ekgrp,jdbcType=VARCHAR},
      </if>
      <if test="waers != null">
        WAERS = #{waers,jdbcType=VARCHAR},
      </if>
      <if test="wkurs != null">
        WKURS = #{wkurs,jdbcType=DECIMAL},
      </if>
      <if test="kufix != null">
        KUFIX = #{kufix,jdbcType=VARCHAR},
      </if>
      <if test="bedat != null">
        BEDAT = #{bedat,jdbcType=VARCHAR},
      </if>
      <if test="kdatb != null">
        KDATB = #{kdatb,jdbcType=VARCHAR},
      </if>
      <if test="kdate != null">
        KDATE = #{kdate,jdbcType=VARCHAR},
      </if>
      <if test="bwbdt != null">
        BWBDT = #{bwbdt,jdbcType=VARCHAR},
      </if>
      <if test="angdt != null">
        ANGDT = #{angdt,jdbcType=VARCHAR},
      </if>
      <if test="bnddt != null">
        BNDDT = #{bnddt,jdbcType=VARCHAR},
      </if>
      <if test="gwldt != null">
        GWLDT = #{gwldt,jdbcType=VARCHAR},
      </if>
      <if test="ausnr != null">
        AUSNR = #{ausnr,jdbcType=VARCHAR},
      </if>
      <if test="angnr != null">
        ANGNR = #{angnr,jdbcType=VARCHAR},
      </if>
      <if test="ihran != null">
        IHRAN = #{ihran,jdbcType=VARCHAR},
      </if>
      <if test="ihrez != null">
        IHREZ = #{ihrez,jdbcType=VARCHAR},
      </if>
      <if test="verkf != null">
        VERKF = #{verkf,jdbcType=VARCHAR},
      </if>
      <if test="telf1 != null">
        TELF1 = #{telf1,jdbcType=VARCHAR},
      </if>
      <if test="llief != null">
        LLIEF = #{llief,jdbcType=VARCHAR},
      </if>
      <if test="kunnr != null">
        KUNNR = #{kunnr,jdbcType=VARCHAR},
      </if>
      <if test="konnr != null">
        KONNR = #{konnr,jdbcType=VARCHAR},
      </if>
      <if test="abgru != null">
        ABGRU = #{abgru,jdbcType=VARCHAR},
      </if>
      <if test="autlf != null">
        AUTLF = #{autlf,jdbcType=VARCHAR},
      </if>
      <if test="weakt != null">
        WEAKT = #{weakt,jdbcType=VARCHAR},
      </if>
      <if test="reswk != null">
        RESWK = #{reswk,jdbcType=VARCHAR},
      </if>
      <if test="lblif != null">
        LBLIF = #{lblif,jdbcType=VARCHAR},
      </if>
      <if test="inco1 != null">
        INCO1 = #{inco1,jdbcType=VARCHAR},
      </if>
      <if test="inco2 != null">
        INCO2 = #{inco2,jdbcType=VARCHAR},
      </if>
      <if test="ktwrt != null">
        KTWRT = #{ktwrt,jdbcType=DECIMAL},
      </if>
      <if test="submi != null">
        SUBMI = #{submi,jdbcType=VARCHAR},
      </if>
      <if test="knumv != null">
        KNUMV = #{knumv,jdbcType=VARCHAR},
      </if>
      <if test="kalsm != null">
        KALSM = #{kalsm,jdbcType=VARCHAR},
      </if>
      <if test="stafo != null">
        STAFO = #{stafo,jdbcType=VARCHAR},
      </if>
      <if test="lifre != null">
        LIFRE = #{lifre,jdbcType=VARCHAR},
      </if>
      <if test="exnum != null">
        EXNUM = #{exnum,jdbcType=VARCHAR},
      </if>
      <if test="unsez != null">
        UNSEZ = #{unsez,jdbcType=VARCHAR},
      </if>
      <if test="logsy != null">
        LOGSY = #{logsy,jdbcType=VARCHAR},
      </if>
      <if test="upinc != null">
        UPINC = #{upinc,jdbcType=VARCHAR},
      </if>
      <if test="stako != null">
        STAKO = #{stako,jdbcType=VARCHAR},
      </if>
      <if test="frggr != null">
        FRGGR = #{frggr,jdbcType=VARCHAR},
      </if>
      <if test="frgsx != null">
        FRGSX = #{frgsx,jdbcType=VARCHAR},
      </if>
      <if test="frgke != null">
        FRGKE = #{frgke,jdbcType=VARCHAR},
      </if>
      <if test="frgzu != null">
        FRGZU = #{frgzu,jdbcType=VARCHAR},
      </if>
      <if test="frgrl != null">
        FRGRL = #{frgrl,jdbcType=VARCHAR},
      </if>
      <if test="lands != null">
        LANDS = #{lands,jdbcType=VARCHAR},
      </if>
      <if test="lphis != null">
        LPHIS = #{lphis,jdbcType=VARCHAR},
      </if>
      <if test="adrnr != null">
        ADRNR = #{adrnr,jdbcType=VARCHAR},
      </if>
      <if test="stcegL != null">
        STCEG_L = #{stcegL,jdbcType=VARCHAR},
      </if>
      <if test="stceg != null">
        STCEG = #{stceg,jdbcType=VARCHAR},
      </if>
      <if test="absgr != null">
        ABSGR = #{absgr,jdbcType=VARCHAR},
      </if>
      <if test="addnr != null">
        ADDNR = #{addnr,jdbcType=VARCHAR},
      </if>
      <if test="kornr != null">
        KORNR = #{kornr,jdbcType=VARCHAR},
      </if>
      <if test="memory != null">
        MEMORY = #{memory,jdbcType=VARCHAR},
      </if>
      <if test="procstat != null">
        PROCSTAT = #{procstat,jdbcType=VARCHAR},
      </if>
      <if test="rlwrt != null">
        RLWRT = #{rlwrt,jdbcType=DECIMAL},
      </if>
      <if test="revno != null">
        REVNO = #{revno,jdbcType=VARCHAR},
      </if>
      <if test="scmproc != null">
        SCMPROC = #{scmproc,jdbcType=VARCHAR},
      </if>
      <if test="reasonCode != null">
        REASON_CODE = #{reasonCode,jdbcType=VARCHAR},
      </if>
      <if test="memorytype != null">
        MEMORYTYPE = #{memorytype,jdbcType=VARCHAR},
      </if>
      <if test="rettp != null">
        RETTP = #{rettp,jdbcType=VARCHAR},
      </if>
      <if test="retpc != null">
        RETPC = #{retpc,jdbcType=DECIMAL},
      </if>
      <if test="dptyp != null">
        DPTYP = #{dptyp,jdbcType=VARCHAR},
      </if>
      <if test="dppct != null">
        DPPCT = #{dppct,jdbcType=DECIMAL},
      </if>
      <if test="dpamt != null">
        DPAMT = #{dpamt,jdbcType=DECIMAL},
      </if>
      <if test="dpdat != null">
        DPDAT = #{dpdat,jdbcType=VARCHAR},
      </if>
      <if test="msrId != null">
        MSR_ID = #{msrId,jdbcType=VARCHAR},
      </if>
      <if test="hierarchyExists != null">
        HIERARCHY_EXISTS = #{hierarchyExists,jdbcType=VARCHAR},
      </if>
      <if test="thresholdExists != null">
        THRESHOLD_EXISTS = #{thresholdExists,jdbcType=VARCHAR},
      </if>
      <if test="legalContract != null">
        LEGAL_CONTRACT = #{legalContract,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        DESCRIPTION = #{description,jdbcType=VARCHAR},
      </if>
      <if test="releaseDate != null">
        RELEASE_DATE = #{releaseDate,jdbcType=VARCHAR},
      </if>
      <if test="vsart != null">
        VSART = #{vsart,jdbcType=VARCHAR},
      </if>
      <if test="handoverloc != null">
        HANDOVERLOC = #{handoverloc,jdbcType=VARCHAR},
      </if>
      <if test="shipcond != null">
        SHIPCOND = #{shipcond,jdbcType=VARCHAR},
      </if>
      <if test="incov != null">
        INCOV = #{incov,jdbcType=VARCHAR},
      </if>
      <if test="inco2L != null">
        INCO2_L = #{inco2L,jdbcType=VARCHAR},
      </if>
      <if test="inco3L != null">
        INCO3_L = #{inco3L,jdbcType=VARCHAR},
      </if>
      <if test="grwcu != null">
        GRWCU = #{grwcu,jdbcType=VARCHAR},
      </if>
      <if test="intraRel != null">
        INTRA_REL = #{intraRel,jdbcType=VARCHAR},
      </if>
      <if test="intraExcl != null">
        INTRA_EXCL = #{intraExcl,jdbcType=VARCHAR},
      </if>
      <if test="qtnErlstSubmsnDate != null">
        QTN_ERLST_SUBMSN_DATE = #{qtnErlstSubmsnDate,jdbcType=VARCHAR},
      </if>
      <if test="followonDocCat != null">
        FOLLOWON_DOC_CAT = #{followonDocCat,jdbcType=VARCHAR},
      </if>
      <if test="followonDocType != null">
        FOLLOWON_DOC_TYPE = #{followonDocType,jdbcType=VARCHAR},
      </if>
      <if test="dummyEkkoInclEewPs != null">
        DUMMY_EKKO_INCL_EEW_PS = #{dummyEkkoInclEewPs,jdbcType=VARCHAR},
      </if>
      <if test="externalsystem != null">
        EXTERNALSYSTEM = #{externalsystem,jdbcType=VARCHAR},
      </if>
      <if test="externalreferenceid != null">
        EXTERNALREFERENCEID = #{externalreferenceid,jdbcType=VARCHAR},
      </if>
      <if test="extRevTmstmp != null">
        EXT_REV_TMSTMP = #{extRevTmstmp,jdbcType=DECIMAL},
      </if>
      <if test="iseopblocked != null">
        ISEOPBLOCKED = #{iseopblocked,jdbcType=VARCHAR},
      </if>
      <if test="isaged != null">
        ISAGED = #{isaged,jdbcType=VARCHAR},
      </if>
      <if test="forceId != null">
        FORCE_ID = #{forceId,jdbcType=VARCHAR},
      </if>
      <if test="forceCnt != null">
        FORCE_CNT = #{forceCnt,jdbcType=VARCHAR},
      </if>
      <if test="relocId != null">
        RELOC_ID = #{relocId,jdbcType=VARCHAR},
      </if>
      <if test="relocSeqId != null">
        RELOC_SEQ_ID = #{relocSeqId,jdbcType=VARCHAR},
      </if>
      <if test="sourceLogsys != null">
        SOURCE_LOGSYS = #{sourceLogsys,jdbcType=VARCHAR},
      </if>
      <if test="fshTransaction != null">
        FSH_TRANSACTION = #{fshTransaction,jdbcType=VARCHAR},
      </if>
      <if test="fshItemGroup != null">
        FSH_ITEM_GROUP = #{fshItemGroup,jdbcType=VARCHAR},
      </if>
      <if test="fshVasLastItem != null">
        FSH_VAS_LAST_ITEM = #{fshVasLastItem,jdbcType=VARCHAR},
      </if>
      <if test="fshOsStgChange != null">
        FSH_OS_STG_CHANGE = #{fshOsStgChange,jdbcType=VARCHAR},
      </if>
      <if test="tmsRefUuid != null">
        TMS_REF_UUID = #{tmsRefUuid,jdbcType=VARCHAR},
      </if>
      <if test="zzzwlms != null">
        ZZZWLMS = #{zzzwlms,jdbcType=VARCHAR},
      </if>
      <if test="zzzzshd != null">
        ZZZZSHD = #{zzzzshd,jdbcType=VARCHAR},
      </if>
      <if test="zzzshkc != null">
        ZZZSHKC = #{zzzshkc,jdbcType=VARCHAR},
      </if>
      <if test="zzzmdsq != null">
        ZZZMDSQ = #{zzzmdsq,jdbcType=VARCHAR},
      </if>
      <if test="zzzbeiz != null">
        ZZZBEIZ = #{zzzbeiz,jdbcType=VARCHAR},
      </if>
      <if test="zzzhgzt != null">
        ZZZHGZT = #{zzzhgzt,jdbcType=VARCHAR},
      </if>
      <if test="zzqxdjh != null">
        ZZQXDJH = #{zzqxdjh,jdbcType=VARCHAR},
      </if>
      <if test="zzzqhbs != null">
        ZZZQHBS = #{zzzqhbs,jdbcType=VARCHAR},
      </if>
      <if test="zzzuserid != null">
        ZZZUSERID = #{zzzuserid,jdbcType=VARCHAR},
      </if>
      <if test="zzcgy != null">
        ZZCGY = #{zzcgy,jdbcType=VARCHAR},
      </if>
      <if test="zzdhy != null">
        ZZDHY = #{zzdhy,jdbcType=VARCHAR},
      </if>
      <if test="zzcoer != null">
        ZZCOER = #{zzcoer,jdbcType=VARCHAR},
      </if>
      <if test="zzcoca != null">
        ZZCOCA = #{zzcoca,jdbcType=VARCHAR},
      </if>
      <if test="zzycgdd != null">
        ZZYCGDD = #{zzycgdd,jdbcType=VARCHAR},
      </if>
      <if test="zapcgk != null">
        ZAPCGK = #{zapcgk,jdbcType=VARCHAR},
      </if>
      <if test="apcgkExtend != null">
        APCGK_EXTEND = #{apcgkExtend,jdbcType=VARCHAR},
      </if>
      <if test="zbasDate != null">
        ZBAS_DATE = #{zbasDate,jdbcType=VARCHAR},
      </if>
      <if test="zadattyp != null">
        ZADATTYP = #{zadattyp,jdbcType=VARCHAR},
      </if>
      <if test="zstartDat != null">
        ZSTART_DAT = #{zstartDat,jdbcType=VARCHAR},
      </if>
      <if test="zDev != null">
        Z_DEV = #{zDev,jdbcType=DECIMAL},
      </if>
      <if test="zindanx != null">
        ZINDANX = #{zindanx,jdbcType=VARCHAR},
      </if>
      <if test="zlimitDat != null">
        ZLIMIT_DAT = #{zlimitDat,jdbcType=VARCHAR},
      </if>
      <if test="numerator != null">
        NUMERATOR = #{numerator,jdbcType=VARCHAR},
      </if>
      <if test="hashcalBdat != null">
        HASHCAL_BDAT = #{hashcalBdat,jdbcType=VARCHAR},
      </if>
      <if test="hashcal != null">
        HASHCAL = #{hashcal,jdbcType=VARCHAR},
      </if>
      <if test="negative != null">
        NEGATIVE = #{negative,jdbcType=VARCHAR},
      </if>
      <if test="hashcalExists != null">
        HASHCAL_EXISTS = #{hashcalExists,jdbcType=VARCHAR},
      </if>
      <if test="knownIndex != null">
        KNOWN_INDEX = #{knownIndex,jdbcType=VARCHAR},
      </if>
      <if test="postat != null">
        POSTAT = #{postat,jdbcType=VARCHAR},
      </if>
      <if test="vzskz != null">
        VZSKZ = #{vzskz,jdbcType=VARCHAR},
      </if>
      <if test="fshSnstStatus != null">
        FSH_SNST_STATUS = #{fshSnstStatus,jdbcType=VARCHAR},
      </if>
      <if test="proce != null">
        PROCE = #{proce,jdbcType=VARCHAR},
      </if>
      <if test="conc != null">
        CONC = #{conc,jdbcType=VARCHAR},
      </if>
      <if test="cont != null">
        CONT = #{cont,jdbcType=VARCHAR},
      </if>
      <if test="comp != null">
        COMP = #{comp,jdbcType=VARCHAR},
      </if>
      <if test="outr != null">
        OUTR = #{outr,jdbcType=VARCHAR},
      </if>
      <if test="desp != null">
        DESP = #{desp,jdbcType=VARCHAR},
      </if>
      <if test="despDat != null">
        DESP_DAT = #{despDat,jdbcType=VARCHAR},
      </if>
      <if test="despCargo != null">
        DESP_CARGO = #{despCargo,jdbcType=VARCHAR},
      </if>
      <if test="pare != null">
        PARE = #{pare,jdbcType=VARCHAR},
      </if>
      <if test="pareDat != null">
        PARE_DAT = #{pareDat,jdbcType=VARCHAR},
      </if>
      <if test="pareCargo != null">
        PARE_CARGO = #{pareCargo,jdbcType=VARCHAR},
      </if>
      <if test="pfmContract != null">
        PFM_CONTRACT = #{pfmContract,jdbcType=VARCHAR},
      </if>
      <if test="pohfType != null">
        POHF_TYPE = #{pohfType,jdbcType=VARCHAR},
      </if>
      <if test="eqEindt != null">
        EQ_EINDT = #{eqEindt,jdbcType=VARCHAR},
      </if>
      <if test="eqWerks != null">
        EQ_WERKS = #{eqWerks,jdbcType=VARCHAR},
      </if>
      <if test="fixpo != null">
        FIXPO = #{fixpo,jdbcType=VARCHAR},
      </if>
      <if test="ekgrpAllow != null">
        EKGRP_ALLOW = #{ekgrpAllow,jdbcType=VARCHAR},
      </if>
      <if test="werksAllow != null">
        WERKS_ALLOW = #{werksAllow,jdbcType=VARCHAR},
      </if>
      <if test="contractAllow != null">
        CONTRACT_ALLOW = #{contractAllow,jdbcType=VARCHAR},
      </if>
      <if test="pstypAllow != null">
        PSTYP_ALLOW = #{pstypAllow,jdbcType=VARCHAR},
      </if>
      <if test="fixpoAllow != null">
        FIXPO_ALLOW = #{fixpoAllow,jdbcType=VARCHAR},
      </if>
      <if test="keyIdAllow != null">
        KEY_ID_ALLOW = #{keyIdAllow,jdbcType=VARCHAR},
      </if>
      <if test="aurelAllow != null">
        AUREL_ALLOW = #{aurelAllow,jdbcType=VARCHAR},
      </if>
      <if test="delperAllow != null">
        DELPER_ALLOW = #{delperAllow,jdbcType=VARCHAR},
      </if>
      <if test="eindtAllow != null">
        EINDT_ALLOW = #{eindtAllow,jdbcType=VARCHAR},
      </if>
      <if test="ltsnrAllow != null">
        LTSNR_ALLOW = #{ltsnrAllow,jdbcType=VARCHAR},
      </if>
      <if test="otbLevel != null">
        OTB_LEVEL = #{otbLevel,jdbcType=VARCHAR},
      </if>
      <if test="otbCondType != null">
        OTB_COND_TYPE = #{otbCondType,jdbcType=VARCHAR},
      </if>
      <if test="keyId != null">
        KEY_ID = #{keyId,jdbcType=VARCHAR},
      </if>
      <if test="otbValue != null">
        OTB_VALUE = #{otbValue,jdbcType=DECIMAL},
      </if>
      <if test="otbCurr != null">
        OTB_CURR = #{otbCurr,jdbcType=VARCHAR},
      </if>
      <if test="otbResValue != null">
        OTB_RES_VALUE = #{otbResValue,jdbcType=DECIMAL},
      </if>
      <if test="otbSpecValue != null">
        OTB_SPEC_VALUE = #{otbSpecValue,jdbcType=DECIMAL},
      </if>
      <if test="sprRsnProfile != null">
        SPR_RSN_PROFILE = #{sprRsnProfile,jdbcType=VARCHAR},
      </if>
      <if test="budgType != null">
        BUDG_TYPE = #{budgType,jdbcType=VARCHAR},
      </if>
      <if test="otbStatus != null">
        OTB_STATUS = #{otbStatus,jdbcType=VARCHAR},
      </if>
      <if test="otbReason != null">
        OTB_REASON = #{otbReason,jdbcType=VARCHAR},
      </if>
      <if test="checkType != null">
        CHECK_TYPE = #{checkType,jdbcType=VARCHAR},
      </if>
      <if test="conOtbReq != null">
        CON_OTB_REQ = #{conOtbReq,jdbcType=VARCHAR},
      </if>
      <if test="conPrebookLev != null">
        CON_PREBOOK_LEV = #{conPrebookLev,jdbcType=VARCHAR},
      </if>
      <if test="conDistrLev != null">
        CON_DISTR_LEV = #{conDistrLev,jdbcType=VARCHAR},
      </if>
      <if test="zzyfye != null">
        ZZYFYE = #{zzyfye,jdbcType=VARCHAR},
      </if>
      <if test="zsqhzh != null">
        ZSQHZH = #{zsqhzh,jdbcType=VARCHAR},
      </if>
      <if test="zzsrmzt != null">
        ZZSRMZT = #{zzsrmzt,jdbcType=VARCHAR},
      </if>
      <if test="zzpaid != null">
        ZZPAID = #{zzpaid,jdbcType=VARCHAR},
      </if>
      <if test="zzpamt != null">
        ZZPAMT = #{zzpamt,jdbcType=DECIMAL},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.iscm.entity.SapEkko">
    update SAP_EKKO
    set MANDT = #{mandt,jdbcType=VARCHAR},
      EBELN = #{ebeln,jdbcType=VARCHAR},
      BUKRS = #{bukrs,jdbcType=VARCHAR},
      BSTYP = #{bstyp,jdbcType=VARCHAR},
      BSART = #{bsart,jdbcType=VARCHAR},
      BSAKZ = #{bsakz,jdbcType=VARCHAR},
      LOEKZ = #{loekz,jdbcType=VARCHAR},
      STATU = #{statu,jdbcType=VARCHAR},
      AEDAT = #{aedat,jdbcType=VARCHAR},
      ERNAM = #{ernam,jdbcType=VARCHAR},
      LASTCHANGEDATETIME = #{lastchangedatetime,jdbcType=DECIMAL},
      PINCR = #{pincr,jdbcType=VARCHAR},
      LPONR = #{lponr,jdbcType=VARCHAR},
      LIFNR = #{lifnr,jdbcType=VARCHAR},
      SPRAS = #{spras,jdbcType=VARCHAR},
      ZTERM = #{zterm,jdbcType=VARCHAR},
      ZBD1T = #{zbd1t,jdbcType=DECIMAL},
      ZBD2T = #{zbd2t,jdbcType=DECIMAL},
      ZBD3T = #{zbd3t,jdbcType=DECIMAL},
      ZBD1P = #{zbd1p,jdbcType=DECIMAL},
      ZBD2P = #{zbd2p,jdbcType=DECIMAL},
      EKORG = #{ekorg,jdbcType=VARCHAR},
      EKGRP = #{ekgrp,jdbcType=VARCHAR},
      WAERS = #{waers,jdbcType=VARCHAR},
      WKURS = #{wkurs,jdbcType=DECIMAL},
      KUFIX = #{kufix,jdbcType=VARCHAR},
      BEDAT = #{bedat,jdbcType=VARCHAR},
      KDATB = #{kdatb,jdbcType=VARCHAR},
      KDATE = #{kdate,jdbcType=VARCHAR},
      BWBDT = #{bwbdt,jdbcType=VARCHAR},
      ANGDT = #{angdt,jdbcType=VARCHAR},
      BNDDT = #{bnddt,jdbcType=VARCHAR},
      GWLDT = #{gwldt,jdbcType=VARCHAR},
      AUSNR = #{ausnr,jdbcType=VARCHAR},
      ANGNR = #{angnr,jdbcType=VARCHAR},
      IHRAN = #{ihran,jdbcType=VARCHAR},
      IHREZ = #{ihrez,jdbcType=VARCHAR},
      VERKF = #{verkf,jdbcType=VARCHAR},
      TELF1 = #{telf1,jdbcType=VARCHAR},
      LLIEF = #{llief,jdbcType=VARCHAR},
      KUNNR = #{kunnr,jdbcType=VARCHAR},
      KONNR = #{konnr,jdbcType=VARCHAR},
      ABGRU = #{abgru,jdbcType=VARCHAR},
      AUTLF = #{autlf,jdbcType=VARCHAR},
      WEAKT = #{weakt,jdbcType=VARCHAR},
      RESWK = #{reswk,jdbcType=VARCHAR},
      LBLIF = #{lblif,jdbcType=VARCHAR},
      INCO1 = #{inco1,jdbcType=VARCHAR},
      INCO2 = #{inco2,jdbcType=VARCHAR},
      KTWRT = #{ktwrt,jdbcType=DECIMAL},
      SUBMI = #{submi,jdbcType=VARCHAR},
      KNUMV = #{knumv,jdbcType=VARCHAR},
      KALSM = #{kalsm,jdbcType=VARCHAR},
      STAFO = #{stafo,jdbcType=VARCHAR},
      LIFRE = #{lifre,jdbcType=VARCHAR},
      EXNUM = #{exnum,jdbcType=VARCHAR},
      UNSEZ = #{unsez,jdbcType=VARCHAR},
      LOGSY = #{logsy,jdbcType=VARCHAR},
      UPINC = #{upinc,jdbcType=VARCHAR},
      STAKO = #{stako,jdbcType=VARCHAR},
      FRGGR = #{frggr,jdbcType=VARCHAR},
      FRGSX = #{frgsx,jdbcType=VARCHAR},
      FRGKE = #{frgke,jdbcType=VARCHAR},
      FRGZU = #{frgzu,jdbcType=VARCHAR},
      FRGRL = #{frgrl,jdbcType=VARCHAR},
      LANDS = #{lands,jdbcType=VARCHAR},
      LPHIS = #{lphis,jdbcType=VARCHAR},
      ADRNR = #{adrnr,jdbcType=VARCHAR},
      STCEG_L = #{stcegL,jdbcType=VARCHAR},
      STCEG = #{stceg,jdbcType=VARCHAR},
      ABSGR = #{absgr,jdbcType=VARCHAR},
      ADDNR = #{addnr,jdbcType=VARCHAR},
      KORNR = #{kornr,jdbcType=VARCHAR},
      MEMORY = #{memory,jdbcType=VARCHAR},
      PROCSTAT = #{procstat,jdbcType=VARCHAR},
      RLWRT = #{rlwrt,jdbcType=DECIMAL},
      REVNO = #{revno,jdbcType=VARCHAR},
      SCMPROC = #{scmproc,jdbcType=VARCHAR},
      REASON_CODE = #{reasonCode,jdbcType=VARCHAR},
      MEMORYTYPE = #{memorytype,jdbcType=VARCHAR},
      RETTP = #{rettp,jdbcType=VARCHAR},
      RETPC = #{retpc,jdbcType=DECIMAL},
      DPTYP = #{dptyp,jdbcType=VARCHAR},
      DPPCT = #{dppct,jdbcType=DECIMAL},
      DPAMT = #{dpamt,jdbcType=DECIMAL},
      DPDAT = #{dpdat,jdbcType=VARCHAR},
      MSR_ID = #{msrId,jdbcType=VARCHAR},
      HIERARCHY_EXISTS = #{hierarchyExists,jdbcType=VARCHAR},
      THRESHOLD_EXISTS = #{thresholdExists,jdbcType=VARCHAR},
      LEGAL_CONTRACT = #{legalContract,jdbcType=VARCHAR},
      DESCRIPTION = #{description,jdbcType=VARCHAR},
      RELEASE_DATE = #{releaseDate,jdbcType=VARCHAR},
      VSART = #{vsart,jdbcType=VARCHAR},
      HANDOVERLOC = #{handoverloc,jdbcType=VARCHAR},
      SHIPCOND = #{shipcond,jdbcType=VARCHAR},
      INCOV = #{incov,jdbcType=VARCHAR},
      INCO2_L = #{inco2L,jdbcType=VARCHAR},
      INCO3_L = #{inco3L,jdbcType=VARCHAR},
      GRWCU = #{grwcu,jdbcType=VARCHAR},
      INTRA_REL = #{intraRel,jdbcType=VARCHAR},
      INTRA_EXCL = #{intraExcl,jdbcType=VARCHAR},
      QTN_ERLST_SUBMSN_DATE = #{qtnErlstSubmsnDate,jdbcType=VARCHAR},
      FOLLOWON_DOC_CAT = #{followonDocCat,jdbcType=VARCHAR},
      FOLLOWON_DOC_TYPE = #{followonDocType,jdbcType=VARCHAR},
      DUMMY_EKKO_INCL_EEW_PS = #{dummyEkkoInclEewPs,jdbcType=VARCHAR},
      EXTERNALSYSTEM = #{externalsystem,jdbcType=VARCHAR},
      EXTERNALREFERENCEID = #{externalreferenceid,jdbcType=VARCHAR},
      EXT_REV_TMSTMP = #{extRevTmstmp,jdbcType=DECIMAL},
      ISEOPBLOCKED = #{iseopblocked,jdbcType=VARCHAR},
      ISAGED = #{isaged,jdbcType=VARCHAR},
      FORCE_ID = #{forceId,jdbcType=VARCHAR},
      FORCE_CNT = #{forceCnt,jdbcType=VARCHAR},
      RELOC_ID = #{relocId,jdbcType=VARCHAR},
      RELOC_SEQ_ID = #{relocSeqId,jdbcType=VARCHAR},
      SOURCE_LOGSYS = #{sourceLogsys,jdbcType=VARCHAR},
      FSH_TRANSACTION = #{fshTransaction,jdbcType=VARCHAR},
      FSH_ITEM_GROUP = #{fshItemGroup,jdbcType=VARCHAR},
      FSH_VAS_LAST_ITEM = #{fshVasLastItem,jdbcType=VARCHAR},
      FSH_OS_STG_CHANGE = #{fshOsStgChange,jdbcType=VARCHAR},
      TMS_REF_UUID = #{tmsRefUuid,jdbcType=VARCHAR},
      ZZZWLMS = #{zzzwlms,jdbcType=VARCHAR},
      ZZZZSHD = #{zzzzshd,jdbcType=VARCHAR},
      ZZZSHKC = #{zzzshkc,jdbcType=VARCHAR},
      ZZZMDSQ = #{zzzmdsq,jdbcType=VARCHAR},
      ZZZBEIZ = #{zzzbeiz,jdbcType=VARCHAR},
      ZZZHGZT = #{zzzhgzt,jdbcType=VARCHAR},
      ZZQXDJH = #{zzqxdjh,jdbcType=VARCHAR},
      ZZZQHBS = #{zzzqhbs,jdbcType=VARCHAR},
      ZZZUSERID = #{zzzuserid,jdbcType=VARCHAR},
      ZZCGY = #{zzcgy,jdbcType=VARCHAR},
      ZZDHY = #{zzdhy,jdbcType=VARCHAR},
      ZZCOER = #{zzcoer,jdbcType=VARCHAR},
      ZZCOCA = #{zzcoca,jdbcType=VARCHAR},
      ZZYCGDD = #{zzycgdd,jdbcType=VARCHAR},
      ZAPCGK = #{zapcgk,jdbcType=VARCHAR},
      APCGK_EXTEND = #{apcgkExtend,jdbcType=VARCHAR},
      ZBAS_DATE = #{zbasDate,jdbcType=VARCHAR},
      ZADATTYP = #{zadattyp,jdbcType=VARCHAR},
      ZSTART_DAT = #{zstartDat,jdbcType=VARCHAR},
      Z_DEV = #{zDev,jdbcType=DECIMAL},
      ZINDANX = #{zindanx,jdbcType=VARCHAR},
      ZLIMIT_DAT = #{zlimitDat,jdbcType=VARCHAR},
      NUMERATOR = #{numerator,jdbcType=VARCHAR},
      HASHCAL_BDAT = #{hashcalBdat,jdbcType=VARCHAR},
      HASHCAL = #{hashcal,jdbcType=VARCHAR},
      NEGATIVE = #{negative,jdbcType=VARCHAR},
      HASHCAL_EXISTS = #{hashcalExists,jdbcType=VARCHAR},
      KNOWN_INDEX = #{knownIndex,jdbcType=VARCHAR},
      POSTAT = #{postat,jdbcType=VARCHAR},
      VZSKZ = #{vzskz,jdbcType=VARCHAR},
      FSH_SNST_STATUS = #{fshSnstStatus,jdbcType=VARCHAR},
      PROCE = #{proce,jdbcType=VARCHAR},
      CONC = #{conc,jdbcType=VARCHAR},
      CONT = #{cont,jdbcType=VARCHAR},
      COMP = #{comp,jdbcType=VARCHAR},
      OUTR = #{outr,jdbcType=VARCHAR},
      DESP = #{desp,jdbcType=VARCHAR},
      DESP_DAT = #{despDat,jdbcType=VARCHAR},
      DESP_CARGO = #{despCargo,jdbcType=VARCHAR},
      PARE = #{pare,jdbcType=VARCHAR},
      PARE_DAT = #{pareDat,jdbcType=VARCHAR},
      PARE_CARGO = #{pareCargo,jdbcType=VARCHAR},
      PFM_CONTRACT = #{pfmContract,jdbcType=VARCHAR},
      POHF_TYPE = #{pohfType,jdbcType=VARCHAR},
      EQ_EINDT = #{eqEindt,jdbcType=VARCHAR},
      EQ_WERKS = #{eqWerks,jdbcType=VARCHAR},
      FIXPO = #{fixpo,jdbcType=VARCHAR},
      EKGRP_ALLOW = #{ekgrpAllow,jdbcType=VARCHAR},
      WERKS_ALLOW = #{werksAllow,jdbcType=VARCHAR},
      CONTRACT_ALLOW = #{contractAllow,jdbcType=VARCHAR},
      PSTYP_ALLOW = #{pstypAllow,jdbcType=VARCHAR},
      FIXPO_ALLOW = #{fixpoAllow,jdbcType=VARCHAR},
      KEY_ID_ALLOW = #{keyIdAllow,jdbcType=VARCHAR},
      AUREL_ALLOW = #{aurelAllow,jdbcType=VARCHAR},
      DELPER_ALLOW = #{delperAllow,jdbcType=VARCHAR},
      EINDT_ALLOW = #{eindtAllow,jdbcType=VARCHAR},
      LTSNR_ALLOW = #{ltsnrAllow,jdbcType=VARCHAR},
      OTB_LEVEL = #{otbLevel,jdbcType=VARCHAR},
      OTB_COND_TYPE = #{otbCondType,jdbcType=VARCHAR},
      KEY_ID = #{keyId,jdbcType=VARCHAR},
      OTB_VALUE = #{otbValue,jdbcType=DECIMAL},
      OTB_CURR = #{otbCurr,jdbcType=VARCHAR},
      OTB_RES_VALUE = #{otbResValue,jdbcType=DECIMAL},
      OTB_SPEC_VALUE = #{otbSpecValue,jdbcType=DECIMAL},
      SPR_RSN_PROFILE = #{sprRsnProfile,jdbcType=VARCHAR},
      BUDG_TYPE = #{budgType,jdbcType=VARCHAR},
      OTB_STATUS = #{otbStatus,jdbcType=VARCHAR},
      OTB_REASON = #{otbReason,jdbcType=VARCHAR},
      CHECK_TYPE = #{checkType,jdbcType=VARCHAR},
      CON_OTB_REQ = #{conOtbReq,jdbcType=VARCHAR},
      CON_PREBOOK_LEV = #{conPrebookLev,jdbcType=VARCHAR},
      CON_DISTR_LEV = #{conDistrLev,jdbcType=VARCHAR},
      ZZYFYE = #{zzyfye,jdbcType=VARCHAR},
      ZSQHZH = #{zsqhzh,jdbcType=VARCHAR},
      ZZSRMZT = #{zzsrmzt,jdbcType=VARCHAR},
      ZZPAID = #{zzpaid,jdbcType=VARCHAR},
      ZZPAMT = #{zzpamt,jdbcType=DECIMAL}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>