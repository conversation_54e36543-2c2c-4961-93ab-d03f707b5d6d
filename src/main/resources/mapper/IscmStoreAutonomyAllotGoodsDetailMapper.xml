<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.IscmStoreAutonomyAllotGoodsDetailMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.IscmStoreAutonomyAllotGoodsDetail">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="autonomy_allot_no" jdbcType="VARCHAR" property="autonomyAllotNo" />
    <result column="pos_allot_no" jdbcType="VARCHAR" property="posAllotNo" />
    <result column="record_id" jdbcType="BIGINT" property="recordId" />
    <result column="allot_type" jdbcType="TINYINT" property="allotType" />
    <result column="platform_org_id" jdbcType="BIGINT" property="platformOrgId" />
    <result column="platform_org_name" jdbcType="VARCHAR" property="platformOrgName" />
    <result column="out_company_id" jdbcType="BIGINT" property="outCompanyId" />
    <result column="out_company_code" jdbcType="VARCHAR" property="outCompanyCode" />
    <result column="out_company_name" jdbcType="VARCHAR" property="outCompanyName" />
    <result column="in_company_id" jdbcType="BIGINT" property="inCompanyId" />
    <result column="in_company_code" jdbcType="VARCHAR" property="inCompanyCode" />
    <result column="in_company_name" jdbcType="VARCHAR" property="inCompanyName" />
    <result column="out_store_id" jdbcType="BIGINT" property="outStoreId" />
    <result column="out_store_code" jdbcType="VARCHAR" property="outStoreCode" />
    <result column="out_store_name" jdbcType="VARCHAR" property="outStoreName" />
    <result column="in_store_id" jdbcType="BIGINT" property="inStoreId" />
    <result column="in_store_code" jdbcType="VARCHAR" property="inStoreCode" />
    <result column="in_store_name" jdbcType="VARCHAR" property="inStoreName" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="goods_desc" jdbcType="VARCHAR" property="goodsDesc" />
    <result column="allot_quantity" jdbcType="DECIMAL" property="allotQuantity" />
    <result column="transfer_cost_amount" jdbcType="DECIMAL" property="transferCostAmount" />
    <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
    <result column="expiry_date" jdbcType="DATE" property="expiryDate" />
    <result column="produce_date" jdbcType="DATE" property="produceDate" />
    <result column="import_sys_stock_quantity" jdbcType="DECIMAL" property="importSysStockQuantity" />
    <result column="send_sys_stock_quantity" jdbcType="DECIMAL" property="sendSysStockQuantity" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="goods_common_name" jdbcType="VARCHAR" property="goodsCommonName" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="dosage_form" jdbcType="VARCHAR" property="dosageForm" />
    <result column="send_status" jdbcType="TINYINT" property="sendStatus" />
    <result column="send_result" jdbcType="VARCHAR" property="sendResult" />
    <result column="send_time" jdbcType="TIMESTAMP" property="sendTime" />
    <result column="send_by" jdbcType="BIGINT" property="sendBy" />
    <result column="send_name" jdbcType="VARCHAR" property="sendName" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, autonomy_allot_no, pos_allot_no, record_id, allot_type, platform_org_id, platform_org_name, 
    out_company_id, out_company_code, out_company_name, in_company_id, in_company_code, 
    in_company_name, out_store_id, out_store_code, out_store_name, in_store_id, in_store_code, 
    in_store_name, goods_no, goods_name, goods_desc, allot_quantity, transfer_cost_amount, 
    batch_no, expiry_date, produce_date, import_sys_stock_quantity, send_sys_stock_quantity, 
    unit, goods_common_name, manufacturer, dosage_form, send_status, send_result, send_time, 
    send_by, send_name, `status`, extend, version, created_by, created_name, updated_by, 
    updated_name, gmt_create, gmt_update
  </sql>
  <select id="selectByExample" parameterType="com.cowell.iscm.entity.IscmStoreAutonomyAllotGoodsDetailExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from iscm_store_autonomy_allot_goods_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from iscm_store_autonomy_allot_goods_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from iscm_store_autonomy_allot_goods_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.iscm.entity.IscmStoreAutonomyAllotGoodsDetailExample">
    delete from iscm_store_autonomy_allot_goods_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entity.IscmStoreAutonomyAllotGoodsDetail" useGeneratedKeys="true">
    insert into iscm_store_autonomy_allot_goods_detail (autonomy_allot_no, pos_allot_no, record_id, 
      allot_type, platform_org_id, platform_org_name, 
      out_company_id, out_company_code, out_company_name, 
      in_company_id, in_company_code, in_company_name, 
      out_store_id, out_store_code, out_store_name, 
      in_store_id, in_store_code, in_store_name, 
      goods_no, goods_name, goods_desc, 
      allot_quantity, transfer_cost_amount, batch_no, 
      expiry_date, produce_date, import_sys_stock_quantity, 
      send_sys_stock_quantity, unit, goods_common_name, 
      manufacturer, dosage_form, send_status, 
      send_result, send_time, send_by, 
      send_name, `status`, extend, 
      version, created_by, created_name, 
      updated_by, updated_name, gmt_create, 
      gmt_update)
    values (#{autonomyAllotNo,jdbcType=VARCHAR}, #{posAllotNo,jdbcType=VARCHAR}, #{recordId,jdbcType=BIGINT}, 
      #{allotType,jdbcType=TINYINT}, #{platformOrgId,jdbcType=BIGINT}, #{platformOrgName,jdbcType=VARCHAR}, 
      #{outCompanyId,jdbcType=BIGINT}, #{outCompanyCode,jdbcType=VARCHAR}, #{outCompanyName,jdbcType=VARCHAR}, 
      #{inCompanyId,jdbcType=BIGINT}, #{inCompanyCode,jdbcType=VARCHAR}, #{inCompanyName,jdbcType=VARCHAR}, 
      #{outStoreId,jdbcType=BIGINT}, #{outStoreCode,jdbcType=VARCHAR}, #{outStoreName,jdbcType=VARCHAR}, 
      #{inStoreId,jdbcType=BIGINT}, #{inStoreCode,jdbcType=VARCHAR}, #{inStoreName,jdbcType=VARCHAR}, 
      #{goodsNo,jdbcType=VARCHAR}, #{goodsName,jdbcType=VARCHAR}, #{goodsDesc,jdbcType=VARCHAR}, 
      #{allotQuantity,jdbcType=DECIMAL}, #{transferCostAmount,jdbcType=DECIMAL}, #{batchNo,jdbcType=VARCHAR}, 
      #{expiryDate,jdbcType=DATE}, #{produceDate,jdbcType=DATE}, #{importSysStockQuantity,jdbcType=DECIMAL}, 
      #{sendSysStockQuantity,jdbcType=DECIMAL}, #{unit,jdbcType=VARCHAR}, #{goodsCommonName,jdbcType=VARCHAR}, 
      #{manufacturer,jdbcType=VARCHAR}, #{dosageForm,jdbcType=VARCHAR}, #{sendStatus,jdbcType=TINYINT}, 
      #{sendResult,jdbcType=VARCHAR}, #{sendTime,jdbcType=TIMESTAMP}, #{sendBy,jdbcType=BIGINT}, 
      #{sendName,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, #{extend,jdbcType=VARCHAR}, 
      #{version,jdbcType=INTEGER}, #{createdBy,jdbcType=BIGINT}, #{createdName,jdbcType=VARCHAR}, 
      #{updatedBy,jdbcType=BIGINT}, #{updatedName,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtUpdate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entity.IscmStoreAutonomyAllotGoodsDetail" useGeneratedKeys="true">
    insert into iscm_store_autonomy_allot_goods_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="autonomyAllotNo != null">
        autonomy_allot_no,
      </if>
      <if test="posAllotNo != null">
        pos_allot_no,
      </if>
      <if test="recordId != null">
        record_id,
      </if>
      <if test="allotType != null">
        allot_type,
      </if>
      <if test="platformOrgId != null">
        platform_org_id,
      </if>
      <if test="platformOrgName != null">
        platform_org_name,
      </if>
      <if test="outCompanyId != null">
        out_company_id,
      </if>
      <if test="outCompanyCode != null">
        out_company_code,
      </if>
      <if test="outCompanyName != null">
        out_company_name,
      </if>
      <if test="inCompanyId != null">
        in_company_id,
      </if>
      <if test="inCompanyCode != null">
        in_company_code,
      </if>
      <if test="inCompanyName != null">
        in_company_name,
      </if>
      <if test="outStoreId != null">
        out_store_id,
      </if>
      <if test="outStoreCode != null">
        out_store_code,
      </if>
      <if test="outStoreName != null">
        out_store_name,
      </if>
      <if test="inStoreId != null">
        in_store_id,
      </if>
      <if test="inStoreCode != null">
        in_store_code,
      </if>
      <if test="inStoreName != null">
        in_store_name,
      </if>
      <if test="goodsNo != null">
        goods_no,
      </if>
      <if test="goodsName != null">
        goods_name,
      </if>
      <if test="goodsDesc != null">
        goods_desc,
      </if>
      <if test="allotQuantity != null">
        allot_quantity,
      </if>
      <if test="transferCostAmount != null">
        transfer_cost_amount,
      </if>
      <if test="batchNo != null">
        batch_no,
      </if>
      <if test="expiryDate != null">
        expiry_date,
      </if>
      <if test="produceDate != null">
        produce_date,
      </if>
      <if test="importSysStockQuantity != null">
        import_sys_stock_quantity,
      </if>
      <if test="sendSysStockQuantity != null">
        send_sys_stock_quantity,
      </if>
      <if test="unit != null">
        unit,
      </if>
      <if test="goodsCommonName != null">
        goods_common_name,
      </if>
      <if test="manufacturer != null">
        manufacturer,
      </if>
      <if test="dosageForm != null">
        dosage_form,
      </if>
      <if test="sendStatus != null">
        send_status,
      </if>
      <if test="sendResult != null">
        send_result,
      </if>
      <if test="sendTime != null">
        send_time,
      </if>
      <if test="sendBy != null">
        send_by,
      </if>
      <if test="sendName != null">
        send_name,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedName != null">
        updated_name,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="autonomyAllotNo != null">
        #{autonomyAllotNo,jdbcType=VARCHAR},
      </if>
      <if test="posAllotNo != null">
        #{posAllotNo,jdbcType=VARCHAR},
      </if>
      <if test="recordId != null">
        #{recordId,jdbcType=BIGINT},
      </if>
      <if test="allotType != null">
        #{allotType,jdbcType=TINYINT},
      </if>
      <if test="platformOrgId != null">
        #{platformOrgId,jdbcType=BIGINT},
      </if>
      <if test="platformOrgName != null">
        #{platformOrgName,jdbcType=VARCHAR},
      </if>
      <if test="outCompanyId != null">
        #{outCompanyId,jdbcType=BIGINT},
      </if>
      <if test="outCompanyCode != null">
        #{outCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="outCompanyName != null">
        #{outCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="inCompanyId != null">
        #{inCompanyId,jdbcType=BIGINT},
      </if>
      <if test="inCompanyCode != null">
        #{inCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="inCompanyName != null">
        #{inCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="outStoreId != null">
        #{outStoreId,jdbcType=BIGINT},
      </if>
      <if test="outStoreCode != null">
        #{outStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="outStoreName != null">
        #{outStoreName,jdbcType=VARCHAR},
      </if>
      <if test="inStoreId != null">
        #{inStoreId,jdbcType=BIGINT},
      </if>
      <if test="inStoreCode != null">
        #{inStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="inStoreName != null">
        #{inStoreName,jdbcType=VARCHAR},
      </if>
      <if test="goodsNo != null">
        #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="goodsDesc != null">
        #{goodsDesc,jdbcType=VARCHAR},
      </if>
      <if test="allotQuantity != null">
        #{allotQuantity,jdbcType=DECIMAL},
      </if>
      <if test="transferCostAmount != null">
        #{transferCostAmount,jdbcType=DECIMAL},
      </if>
      <if test="batchNo != null">
        #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="expiryDate != null">
        #{expiryDate,jdbcType=DATE},
      </if>
      <if test="produceDate != null">
        #{produceDate,jdbcType=DATE},
      </if>
      <if test="importSysStockQuantity != null">
        #{importSysStockQuantity,jdbcType=DECIMAL},
      </if>
      <if test="sendSysStockQuantity != null">
        #{sendSysStockQuantity,jdbcType=DECIMAL},
      </if>
      <if test="unit != null">
        #{unit,jdbcType=VARCHAR},
      </if>
      <if test="goodsCommonName != null">
        #{goodsCommonName,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="dosageForm != null">
        #{dosageForm,jdbcType=VARCHAR},
      </if>
      <if test="sendStatus != null">
        #{sendStatus,jdbcType=TINYINT},
      </if>
      <if test="sendResult != null">
        #{sendResult,jdbcType=VARCHAR},
      </if>
      <if test="sendTime != null">
        #{sendTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sendBy != null">
        #{sendBy,jdbcType=BIGINT},
      </if>
      <if test="sendName != null">
        #{sendName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        #{updatedName,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.iscm.entity.IscmStoreAutonomyAllotGoodsDetailExample" resultType="java.lang.Long">
    select count(*) from iscm_store_autonomy_allot_goods_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update iscm_store_autonomy_allot_goods_detail
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.autonomyAllotNo != null">
        autonomy_allot_no = #{record.autonomyAllotNo,jdbcType=VARCHAR},
      </if>
      <if test="record.posAllotNo != null">
        pos_allot_no = #{record.posAllotNo,jdbcType=VARCHAR},
      </if>
      <if test="record.recordId != null">
        record_id = #{record.recordId,jdbcType=BIGINT},
      </if>
      <if test="record.allotType != null">
        allot_type = #{record.allotType,jdbcType=TINYINT},
      </if>
      <if test="record.platformOrgId != null">
        platform_org_id = #{record.platformOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.platformOrgName != null">
        platform_org_name = #{record.platformOrgName,jdbcType=VARCHAR},
      </if>
      <if test="record.outCompanyId != null">
        out_company_id = #{record.outCompanyId,jdbcType=BIGINT},
      </if>
      <if test="record.outCompanyCode != null">
        out_company_code = #{record.outCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.outCompanyName != null">
        out_company_name = #{record.outCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="record.inCompanyId != null">
        in_company_id = #{record.inCompanyId,jdbcType=BIGINT},
      </if>
      <if test="record.inCompanyCode != null">
        in_company_code = #{record.inCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.inCompanyName != null">
        in_company_name = #{record.inCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="record.outStoreId != null">
        out_store_id = #{record.outStoreId,jdbcType=BIGINT},
      </if>
      <if test="record.outStoreCode != null">
        out_store_code = #{record.outStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="record.outStoreName != null">
        out_store_name = #{record.outStoreName,jdbcType=VARCHAR},
      </if>
      <if test="record.inStoreId != null">
        in_store_id = #{record.inStoreId,jdbcType=BIGINT},
      </if>
      <if test="record.inStoreCode != null">
        in_store_code = #{record.inStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="record.inStoreName != null">
        in_store_name = #{record.inStoreName,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsNo != null">
        goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsName != null">
        goods_name = #{record.goodsName,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsDesc != null">
        goods_desc = #{record.goodsDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.allotQuantity != null">
        allot_quantity = #{record.allotQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.transferCostAmount != null">
        transfer_cost_amount = #{record.transferCostAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.batchNo != null">
        batch_no = #{record.batchNo,jdbcType=VARCHAR},
      </if>
      <if test="record.expiryDate != null">
        expiry_date = #{record.expiryDate,jdbcType=DATE},
      </if>
      <if test="record.produceDate != null">
        produce_date = #{record.produceDate,jdbcType=DATE},
      </if>
      <if test="record.importSysStockQuantity != null">
        import_sys_stock_quantity = #{record.importSysStockQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.sendSysStockQuantity != null">
        send_sys_stock_quantity = #{record.sendSysStockQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.unit != null">
        unit = #{record.unit,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsCommonName != null">
        goods_common_name = #{record.goodsCommonName,jdbcType=VARCHAR},
      </if>
      <if test="record.manufacturer != null">
        manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="record.dosageForm != null">
        dosage_form = #{record.dosageForm,jdbcType=VARCHAR},
      </if>
      <if test="record.sendStatus != null">
        send_status = #{record.sendStatus,jdbcType=TINYINT},
      </if>
      <if test="record.sendResult != null">
        send_result = #{record.sendResult,jdbcType=VARCHAR},
      </if>
      <if test="record.sendTime != null">
        send_time = #{record.sendTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.sendBy != null">
        send_by = #{record.sendBy,jdbcType=BIGINT},
      </if>
      <if test="record.sendName != null">
        send_name = #{record.sendName,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedName != null">
        updated_name = #{record.updatedName,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update iscm_store_autonomy_allot_goods_detail
    set id = #{record.id,jdbcType=BIGINT},
      autonomy_allot_no = #{record.autonomyAllotNo,jdbcType=VARCHAR},
      pos_allot_no = #{record.posAllotNo,jdbcType=VARCHAR},
      record_id = #{record.recordId,jdbcType=BIGINT},
      allot_type = #{record.allotType,jdbcType=TINYINT},
      platform_org_id = #{record.platformOrgId,jdbcType=BIGINT},
      platform_org_name = #{record.platformOrgName,jdbcType=VARCHAR},
      out_company_id = #{record.outCompanyId,jdbcType=BIGINT},
      out_company_code = #{record.outCompanyCode,jdbcType=VARCHAR},
      out_company_name = #{record.outCompanyName,jdbcType=VARCHAR},
      in_company_id = #{record.inCompanyId,jdbcType=BIGINT},
      in_company_code = #{record.inCompanyCode,jdbcType=VARCHAR},
      in_company_name = #{record.inCompanyName,jdbcType=VARCHAR},
      out_store_id = #{record.outStoreId,jdbcType=BIGINT},
      out_store_code = #{record.outStoreCode,jdbcType=VARCHAR},
      out_store_name = #{record.outStoreName,jdbcType=VARCHAR},
      in_store_id = #{record.inStoreId,jdbcType=BIGINT},
      in_store_code = #{record.inStoreCode,jdbcType=VARCHAR},
      in_store_name = #{record.inStoreName,jdbcType=VARCHAR},
      goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      goods_name = #{record.goodsName,jdbcType=VARCHAR},
      goods_desc = #{record.goodsDesc,jdbcType=VARCHAR},
      allot_quantity = #{record.allotQuantity,jdbcType=DECIMAL},
      transfer_cost_amount = #{record.transferCostAmount,jdbcType=DECIMAL},
      batch_no = #{record.batchNo,jdbcType=VARCHAR},
      expiry_date = #{record.expiryDate,jdbcType=DATE},
      produce_date = #{record.produceDate,jdbcType=DATE},
      import_sys_stock_quantity = #{record.importSysStockQuantity,jdbcType=DECIMAL},
      send_sys_stock_quantity = #{record.sendSysStockQuantity,jdbcType=DECIMAL},
      unit = #{record.unit,jdbcType=VARCHAR},
      goods_common_name = #{record.goodsCommonName,jdbcType=VARCHAR},
      manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      dosage_form = #{record.dosageForm,jdbcType=VARCHAR},
      send_status = #{record.sendStatus,jdbcType=TINYINT},
      send_result = #{record.sendResult,jdbcType=VARCHAR},
      send_time = #{record.sendTime,jdbcType=TIMESTAMP},
      send_by = #{record.sendBy,jdbcType=BIGINT},
      send_name = #{record.sendName,jdbcType=VARCHAR},
      `status` = #{record.status,jdbcType=TINYINT},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_name = #{record.createdName,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_name = #{record.updatedName,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.iscm.entity.IscmStoreAutonomyAllotGoodsDetail">
    update iscm_store_autonomy_allot_goods_detail
    <set>
      <if test="autonomyAllotNo != null">
        autonomy_allot_no = #{autonomyAllotNo,jdbcType=VARCHAR},
      </if>
      <if test="posAllotNo != null">
        pos_allot_no = #{posAllotNo,jdbcType=VARCHAR},
      </if>
      <if test="recordId != null">
        record_id = #{recordId,jdbcType=BIGINT},
      </if>
      <if test="allotType != null">
        allot_type = #{allotType,jdbcType=TINYINT},
      </if>
      <if test="platformOrgId != null">
        platform_org_id = #{platformOrgId,jdbcType=BIGINT},
      </if>
      <if test="platformOrgName != null">
        platform_org_name = #{platformOrgName,jdbcType=VARCHAR},
      </if>
      <if test="outCompanyId != null">
        out_company_id = #{outCompanyId,jdbcType=BIGINT},
      </if>
      <if test="outCompanyCode != null">
        out_company_code = #{outCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="outCompanyName != null">
        out_company_name = #{outCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="inCompanyId != null">
        in_company_id = #{inCompanyId,jdbcType=BIGINT},
      </if>
      <if test="inCompanyCode != null">
        in_company_code = #{inCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="inCompanyName != null">
        in_company_name = #{inCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="outStoreId != null">
        out_store_id = #{outStoreId,jdbcType=BIGINT},
      </if>
      <if test="outStoreCode != null">
        out_store_code = #{outStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="outStoreName != null">
        out_store_name = #{outStoreName,jdbcType=VARCHAR},
      </if>
      <if test="inStoreId != null">
        in_store_id = #{inStoreId,jdbcType=BIGINT},
      </if>
      <if test="inStoreCode != null">
        in_store_code = #{inStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="inStoreName != null">
        in_store_name = #{inStoreName,jdbcType=VARCHAR},
      </if>
      <if test="goodsNo != null">
        goods_no = #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        goods_name = #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="goodsDesc != null">
        goods_desc = #{goodsDesc,jdbcType=VARCHAR},
      </if>
      <if test="allotQuantity != null">
        allot_quantity = #{allotQuantity,jdbcType=DECIMAL},
      </if>
      <if test="transferCostAmount != null">
        transfer_cost_amount = #{transferCostAmount,jdbcType=DECIMAL},
      </if>
      <if test="batchNo != null">
        batch_no = #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="expiryDate != null">
        expiry_date = #{expiryDate,jdbcType=DATE},
      </if>
      <if test="produceDate != null">
        produce_date = #{produceDate,jdbcType=DATE},
      </if>
      <if test="importSysStockQuantity != null">
        import_sys_stock_quantity = #{importSysStockQuantity,jdbcType=DECIMAL},
      </if>
      <if test="sendSysStockQuantity != null">
        send_sys_stock_quantity = #{sendSysStockQuantity,jdbcType=DECIMAL},
      </if>
      <if test="unit != null">
        unit = #{unit,jdbcType=VARCHAR},
      </if>
      <if test="goodsCommonName != null">
        goods_common_name = #{goodsCommonName,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        manufacturer = #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="dosageForm != null">
        dosage_form = #{dosageForm,jdbcType=VARCHAR},
      </if>
      <if test="sendStatus != null">
        send_status = #{sendStatus,jdbcType=TINYINT},
      </if>
      <if test="sendResult != null">
        send_result = #{sendResult,jdbcType=VARCHAR},
      </if>
      <if test="sendTime != null">
        send_time = #{sendTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sendBy != null">
        send_by = #{sendBy,jdbcType=BIGINT},
      </if>
      <if test="sendName != null">
        send_name = #{sendName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        updated_name = #{updatedName,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.iscm.entity.IscmStoreAutonomyAllotGoodsDetail">
    update iscm_store_autonomy_allot_goods_detail
    set autonomy_allot_no = #{autonomyAllotNo,jdbcType=VARCHAR},
      pos_allot_no = #{posAllotNo,jdbcType=VARCHAR},
      record_id = #{recordId,jdbcType=BIGINT},
      allot_type = #{allotType,jdbcType=TINYINT},
      platform_org_id = #{platformOrgId,jdbcType=BIGINT},
      platform_org_name = #{platformOrgName,jdbcType=VARCHAR},
      out_company_id = #{outCompanyId,jdbcType=BIGINT},
      out_company_code = #{outCompanyCode,jdbcType=VARCHAR},
      out_company_name = #{outCompanyName,jdbcType=VARCHAR},
      in_company_id = #{inCompanyId,jdbcType=BIGINT},
      in_company_code = #{inCompanyCode,jdbcType=VARCHAR},
      in_company_name = #{inCompanyName,jdbcType=VARCHAR},
      out_store_id = #{outStoreId,jdbcType=BIGINT},
      out_store_code = #{outStoreCode,jdbcType=VARCHAR},
      out_store_name = #{outStoreName,jdbcType=VARCHAR},
      in_store_id = #{inStoreId,jdbcType=BIGINT},
      in_store_code = #{inStoreCode,jdbcType=VARCHAR},
      in_store_name = #{inStoreName,jdbcType=VARCHAR},
      goods_no = #{goodsNo,jdbcType=VARCHAR},
      goods_name = #{goodsName,jdbcType=VARCHAR},
      goods_desc = #{goodsDesc,jdbcType=VARCHAR},
      allot_quantity = #{allotQuantity,jdbcType=DECIMAL},
      transfer_cost_amount = #{transferCostAmount,jdbcType=DECIMAL},
      batch_no = #{batchNo,jdbcType=VARCHAR},
      expiry_date = #{expiryDate,jdbcType=DATE},
      produce_date = #{produceDate,jdbcType=DATE},
      import_sys_stock_quantity = #{importSysStockQuantity,jdbcType=DECIMAL},
      send_sys_stock_quantity = #{sendSysStockQuantity,jdbcType=DECIMAL},
      unit = #{unit,jdbcType=VARCHAR},
      goods_common_name = #{goodsCommonName,jdbcType=VARCHAR},
      manufacturer = #{manufacturer,jdbcType=VARCHAR},
      dosage_form = #{dosageForm,jdbcType=VARCHAR},
      send_status = #{sendStatus,jdbcType=TINYINT},
      send_result = #{sendResult,jdbcType=VARCHAR},
      send_time = #{sendTime,jdbcType=TIMESTAMP},
      send_by = #{sendBy,jdbcType=BIGINT},
      send_name = #{sendName,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=TINYINT},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_name = #{createdName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_name = #{updatedName,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>