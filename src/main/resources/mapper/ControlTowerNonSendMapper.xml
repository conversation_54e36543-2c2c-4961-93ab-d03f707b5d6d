<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.ControlTowerNonSendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.ControlTowerNonSend">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="param_unique_mark" jdbcType="VARCHAR" property="paramUniqueMark" />
    <result column="purreqno" jdbcType="VARCHAR" property="purreqno" />
    <result column="storelineno" jdbcType="VARCHAR" property="storelineno" />
    <result column="bukrs" jdbcType="VARCHAR" property="bukrs" />
    <result column="ekorg" jdbcType="VARCHAR" property="ekorg" />
    <result column="werks" jdbcType="VARCHAR" property="werks" />
    <result column="zzzzshd" jdbcType="VARCHAR" property="zzzzshd" />
    <result column="badat" jdbcType="VARCHAR" property="badat" />
    <result column="matnr" jdbcType="VARCHAR" property="matnr" />
    <result column="matnr_desc" jdbcType="VARCHAR" property="matnrDesc" />
    <result column="menge" jdbcType="DECIMAL" property="menge" />
    <result column="zspsl" jdbcType="DECIMAL" property="zspsl" />
    <result column="meins" jdbcType="VARCHAR" property="meins" />
    <result column="approve_status" jdbcType="VARCHAR" property="approveStatus" />
    <result column="approve_desc" jdbcType="VARCHAR" property="approveDesc" />
    <result column="erdat" jdbcType="VARCHAR" property="erdat" />
    <result column="erzet" jdbcType="VARCHAR" property="erzet" />
    <result column="ernam" jdbcType="VARCHAR" property="ernam" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_by_id" jdbcType="BIGINT" property="createById" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_by_id" jdbcType="BIGINT" property="updateById" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, param_unique_mark, purreqno, storelineno, bukrs, ekorg, werks, zzzzshd, badat, 
    matnr, matnr_desc, menge, zspsl, meins, approve_status, approve_desc, erdat, erzet, 
    ernam, `status`, gmt_create, gmt_update, create_by, create_by_id, update_by, update_by_id, 
    version, extend
  </sql>
  <select id="selectByExample" parameterType="com.cowell.iscm.entity.ControlTowerNonSendExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from control_tower_non_send
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from control_tower_non_send
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from control_tower_non_send
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.iscm.entity.ControlTowerNonSendExample">
    delete from control_tower_non_send
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entity.ControlTowerNonSend" useGeneratedKeys="true">
    insert into control_tower_non_send (param_unique_mark, purreqno, storelineno, 
      bukrs, ekorg, werks, 
      zzzzshd, badat, matnr, 
      matnr_desc, menge, zspsl, 
      meins, approve_status, approve_desc, 
      erdat, erzet, ernam, 
      `status`, gmt_create, gmt_update, 
      create_by, create_by_id, update_by, 
      update_by_id, version, extend
      )
    values (#{paramUniqueMark,jdbcType=VARCHAR}, #{purreqno,jdbcType=VARCHAR}, #{storelineno,jdbcType=VARCHAR}, 
      #{bukrs,jdbcType=VARCHAR}, #{ekorg,jdbcType=VARCHAR}, #{werks,jdbcType=VARCHAR}, 
      #{zzzzshd,jdbcType=VARCHAR}, #{badat,jdbcType=VARCHAR}, #{matnr,jdbcType=VARCHAR}, 
      #{matnrDesc,jdbcType=VARCHAR}, #{menge,jdbcType=DECIMAL}, #{zspsl,jdbcType=DECIMAL}, 
      #{meins,jdbcType=VARCHAR}, #{approveStatus,jdbcType=VARCHAR}, #{approveDesc,jdbcType=VARCHAR}, 
      #{erdat,jdbcType=VARCHAR}, #{erzet,jdbcType=VARCHAR}, #{ernam,jdbcType=VARCHAR}, 
      #{status,jdbcType=INTEGER}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}, 
      #{createBy,jdbcType=VARCHAR}, #{createById,jdbcType=BIGINT}, #{updateBy,jdbcType=VARCHAR}, 
      #{updateById,jdbcType=BIGINT}, #{version,jdbcType=INTEGER}, #{extend,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entity.ControlTowerNonSend" useGeneratedKeys="true">
    insert into control_tower_non_send
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="paramUniqueMark != null">
        param_unique_mark,
      </if>
      <if test="purreqno != null">
        purreqno,
      </if>
      <if test="storelineno != null">
        storelineno,
      </if>
      <if test="bukrs != null">
        bukrs,
      </if>
      <if test="ekorg != null">
        ekorg,
      </if>
      <if test="werks != null">
        werks,
      </if>
      <if test="zzzzshd != null">
        zzzzshd,
      </if>
      <if test="badat != null">
        badat,
      </if>
      <if test="matnr != null">
        matnr,
      </if>
      <if test="matnrDesc != null">
        matnr_desc,
      </if>
      <if test="menge != null">
        menge,
      </if>
      <if test="zspsl != null">
        zspsl,
      </if>
      <if test="meins != null">
        meins,
      </if>
      <if test="approveStatus != null">
        approve_status,
      </if>
      <if test="approveDesc != null">
        approve_desc,
      </if>
      <if test="erdat != null">
        erdat,
      </if>
      <if test="erzet != null">
        erzet,
      </if>
      <if test="ernam != null">
        ernam,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createById != null">
        create_by_id,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateById != null">
        update_by_id,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="extend != null">
        extend,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="paramUniqueMark != null">
        #{paramUniqueMark,jdbcType=VARCHAR},
      </if>
      <if test="purreqno != null">
        #{purreqno,jdbcType=VARCHAR},
      </if>
      <if test="storelineno != null">
        #{storelineno,jdbcType=VARCHAR},
      </if>
      <if test="bukrs != null">
        #{bukrs,jdbcType=VARCHAR},
      </if>
      <if test="ekorg != null">
        #{ekorg,jdbcType=VARCHAR},
      </if>
      <if test="werks != null">
        #{werks,jdbcType=VARCHAR},
      </if>
      <if test="zzzzshd != null">
        #{zzzzshd,jdbcType=VARCHAR},
      </if>
      <if test="badat != null">
        #{badat,jdbcType=VARCHAR},
      </if>
      <if test="matnr != null">
        #{matnr,jdbcType=VARCHAR},
      </if>
      <if test="matnrDesc != null">
        #{matnrDesc,jdbcType=VARCHAR},
      </if>
      <if test="menge != null">
        #{menge,jdbcType=DECIMAL},
      </if>
      <if test="zspsl != null">
        #{zspsl,jdbcType=DECIMAL},
      </if>
      <if test="meins != null">
        #{meins,jdbcType=VARCHAR},
      </if>
      <if test="approveStatus != null">
        #{approveStatus,jdbcType=VARCHAR},
      </if>
      <if test="approveDesc != null">
        #{approveDesc,jdbcType=VARCHAR},
      </if>
      <if test="erdat != null">
        #{erdat,jdbcType=VARCHAR},
      </if>
      <if test="erzet != null">
        #{erzet,jdbcType=VARCHAR},
      </if>
      <if test="ernam != null">
        #{ernam,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createById != null">
        #{createById,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateById != null">
        #{updateById,jdbcType=BIGINT},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.iscm.entity.ControlTowerNonSendExample" resultType="java.lang.Long">
    select count(*) from control_tower_non_send
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update control_tower_non_send
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.paramUniqueMark != null">
        param_unique_mark = #{record.paramUniqueMark,jdbcType=VARCHAR},
      </if>
      <if test="record.purreqno != null">
        purreqno = #{record.purreqno,jdbcType=VARCHAR},
      </if>
      <if test="record.storelineno != null">
        storelineno = #{record.storelineno,jdbcType=VARCHAR},
      </if>
      <if test="record.bukrs != null">
        bukrs = #{record.bukrs,jdbcType=VARCHAR},
      </if>
      <if test="record.ekorg != null">
        ekorg = #{record.ekorg,jdbcType=VARCHAR},
      </if>
      <if test="record.werks != null">
        werks = #{record.werks,jdbcType=VARCHAR},
      </if>
      <if test="record.zzzzshd != null">
        zzzzshd = #{record.zzzzshd,jdbcType=VARCHAR},
      </if>
      <if test="record.badat != null">
        badat = #{record.badat,jdbcType=VARCHAR},
      </if>
      <if test="record.matnr != null">
        matnr = #{record.matnr,jdbcType=VARCHAR},
      </if>
      <if test="record.matnrDesc != null">
        matnr_desc = #{record.matnrDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.menge != null">
        menge = #{record.menge,jdbcType=DECIMAL},
      </if>
      <if test="record.zspsl != null">
        zspsl = #{record.zspsl,jdbcType=DECIMAL},
      </if>
      <if test="record.meins != null">
        meins = #{record.meins,jdbcType=VARCHAR},
      </if>
      <if test="record.approveStatus != null">
        approve_status = #{record.approveStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.approveDesc != null">
        approve_desc = #{record.approveDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.erdat != null">
        erdat = #{record.erdat,jdbcType=VARCHAR},
      </if>
      <if test="record.erzet != null">
        erzet = #{record.erzet,jdbcType=VARCHAR},
      </if>
      <if test="record.ernam != null">
        ernam = #{record.ernam,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createBy != null">
        create_by = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createById != null">
        create_by_id = #{record.createById,jdbcType=BIGINT},
      </if>
      <if test="record.updateBy != null">
        update_by = #{record.updateBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updateById != null">
        update_by_id = #{record.updateById,jdbcType=BIGINT},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update control_tower_non_send
    set id = #{record.id,jdbcType=BIGINT},
      param_unique_mark = #{record.paramUniqueMark,jdbcType=VARCHAR},
      purreqno = #{record.purreqno,jdbcType=VARCHAR},
      storelineno = #{record.storelineno,jdbcType=VARCHAR},
      bukrs = #{record.bukrs,jdbcType=VARCHAR},
      ekorg = #{record.ekorg,jdbcType=VARCHAR},
      werks = #{record.werks,jdbcType=VARCHAR},
      zzzzshd = #{record.zzzzshd,jdbcType=VARCHAR},
      badat = #{record.badat,jdbcType=VARCHAR},
      matnr = #{record.matnr,jdbcType=VARCHAR},
      matnr_desc = #{record.matnrDesc,jdbcType=VARCHAR},
      menge = #{record.menge,jdbcType=DECIMAL},
      zspsl = #{record.zspsl,jdbcType=DECIMAL},
      meins = #{record.meins,jdbcType=VARCHAR},
      approve_status = #{record.approveStatus,jdbcType=VARCHAR},
      approve_desc = #{record.approveDesc,jdbcType=VARCHAR},
      erdat = #{record.erdat,jdbcType=VARCHAR},
      erzet = #{record.erzet,jdbcType=VARCHAR},
      ernam = #{record.ernam,jdbcType=VARCHAR},
      `status` = #{record.status,jdbcType=INTEGER},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      create_by = #{record.createBy,jdbcType=VARCHAR},
      create_by_id = #{record.createById,jdbcType=BIGINT},
      update_by = #{record.updateBy,jdbcType=VARCHAR},
      update_by_id = #{record.updateById,jdbcType=BIGINT},
      version = #{record.version,jdbcType=INTEGER},
      extend = #{record.extend,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.iscm.entity.ControlTowerNonSend">
    update control_tower_non_send
    <set>
      <if test="paramUniqueMark != null">
        param_unique_mark = #{paramUniqueMark,jdbcType=VARCHAR},
      </if>
      <if test="purreqno != null">
        purreqno = #{purreqno,jdbcType=VARCHAR},
      </if>
      <if test="storelineno != null">
        storelineno = #{storelineno,jdbcType=VARCHAR},
      </if>
      <if test="bukrs != null">
        bukrs = #{bukrs,jdbcType=VARCHAR},
      </if>
      <if test="ekorg != null">
        ekorg = #{ekorg,jdbcType=VARCHAR},
      </if>
      <if test="werks != null">
        werks = #{werks,jdbcType=VARCHAR},
      </if>
      <if test="zzzzshd != null">
        zzzzshd = #{zzzzshd,jdbcType=VARCHAR},
      </if>
      <if test="badat != null">
        badat = #{badat,jdbcType=VARCHAR},
      </if>
      <if test="matnr != null">
        matnr = #{matnr,jdbcType=VARCHAR},
      </if>
      <if test="matnrDesc != null">
        matnr_desc = #{matnrDesc,jdbcType=VARCHAR},
      </if>
      <if test="menge != null">
        menge = #{menge,jdbcType=DECIMAL},
      </if>
      <if test="zspsl != null">
        zspsl = #{zspsl,jdbcType=DECIMAL},
      </if>
      <if test="meins != null">
        meins = #{meins,jdbcType=VARCHAR},
      </if>
      <if test="approveStatus != null">
        approve_status = #{approveStatus,jdbcType=VARCHAR},
      </if>
      <if test="approveDesc != null">
        approve_desc = #{approveDesc,jdbcType=VARCHAR},
      </if>
      <if test="erdat != null">
        erdat = #{erdat,jdbcType=VARCHAR},
      </if>
      <if test="erzet != null">
        erzet = #{erzet,jdbcType=VARCHAR},
      </if>
      <if test="ernam != null">
        ernam = #{ernam,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createById != null">
        create_by_id = #{createById,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateById != null">
        update_by_id = #{updateById,jdbcType=BIGINT},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.iscm.entity.ControlTowerNonSend">
    update control_tower_non_send
    set param_unique_mark = #{paramUniqueMark,jdbcType=VARCHAR},
      purreqno = #{purreqno,jdbcType=VARCHAR},
      storelineno = #{storelineno,jdbcType=VARCHAR},
      bukrs = #{bukrs,jdbcType=VARCHAR},
      ekorg = #{ekorg,jdbcType=VARCHAR},
      werks = #{werks,jdbcType=VARCHAR},
      zzzzshd = #{zzzzshd,jdbcType=VARCHAR},
      badat = #{badat,jdbcType=VARCHAR},
      matnr = #{matnr,jdbcType=VARCHAR},
      matnr_desc = #{matnrDesc,jdbcType=VARCHAR},
      menge = #{menge,jdbcType=DECIMAL},
      zspsl = #{zspsl,jdbcType=DECIMAL},
      meins = #{meins,jdbcType=VARCHAR},
      approve_status = #{approveStatus,jdbcType=VARCHAR},
      approve_desc = #{approveDesc,jdbcType=VARCHAR},
      erdat = #{erdat,jdbcType=VARCHAR},
      erzet = #{erzet,jdbcType=VARCHAR},
      ernam = #{ernam,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=INTEGER},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      create_by = #{createBy,jdbcType=VARCHAR},
      create_by_id = #{createById,jdbcType=BIGINT},
      update_by = #{updateBy,jdbcType=VARCHAR},
      update_by_id = #{updateById,jdbcType=BIGINT},
      version = #{version,jdbcType=INTEGER},
      extend = #{extend,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>