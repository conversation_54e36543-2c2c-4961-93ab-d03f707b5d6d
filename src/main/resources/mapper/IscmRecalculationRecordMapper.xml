<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.IscmRecalculationRecordMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.IscmRecalculationRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="apply_no" jdbcType="VARCHAR" property="applyNo" />
    <result column="bdp_apply_no" jdbcType="VARCHAR" property="bdpApplyNo" />
    <result column="apply_line" jdbcType="VARCHAR" property="applyLine" />
    <result column="company_org_id" jdbcType="BIGINT" property="companyOrgId" />
    <result column="business_id" jdbcType="BIGINT" property="businessId" />
    <result column="company_org_name" jdbcType="VARCHAR" property="companyOrgName" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="store_org_id" jdbcType="BIGINT" property="storeOrgId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="store_name" jdbcType="VARCHAR" property="storeName" />
    <result column="apply_date" jdbcType="DATE" property="applyDate" />
    <result column="data_origin_type" jdbcType="TINYINT" property="dataOriginType" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="apply_goods_type" jdbcType="TINYINT" property="applyGoodsType" />
    <result column="bdp_apply_total" jdbcType="DECIMAL" property="bdpApplyTotal" />
    <result column="var_a" jdbcType="DECIMAL" property="varA" />
    <result column="var_b" jdbcType="DECIMAL" property="varB" />
    <result column="middle_package_switch" jdbcType="VARCHAR" property="middlePackageSwitch" />
    <result column="middle_package_qty" jdbcType="DECIMAL" property="middlePackageQty" />
    <result column="apply_ratio" jdbcType="DECIMAL" property="applyRatio" />
    <result column="category_id" jdbcType="BIGINT" property="categoryId" />
    <result column="apply_total" jdbcType="DECIMAL" property="applyTotal" />
    <result column="bdp_average_daily_sales" jdbcType="DECIMAL" property="bdpAverageDailySales" />
    <result column="min_display_qty" jdbcType="DECIMAL" property="minDisplayQty" />
    <result column="stock_upper_limit" jdbcType="DECIMAL" property="stockUpperLimit" />
    <result column="stock_lower_limit" jdbcType="DECIMAL" property="stockLowerLimit" />
    <result column="sale_days_before" jdbcType="DECIMAL" property="saleDaysBefore" />
    <result column="sale_days_after" jdbcType="DECIMAL" property="saleDaysAfter" />
    <result column="special_ctrl" jdbcType="VARCHAR" property="specialCtrl" />
    <result column="special_thirty_days_qty" jdbcType="DECIMAL" property="specialThirtyDaysQty" />
    <result column="apply_reason" jdbcType="VARCHAR" property="applyReason" />
    <result column="apply_stock" jdbcType="DECIMAL" property="applyStock" />
    <result column="scarce_limit" jdbcType="INTEGER" property="scarceLimit" />
    <result column="purchase_type" jdbcType="INTEGER" property="purchaseType" />
    <result column="purchase_channel" jdbcType="VARCHAR" property="purchaseChannel" />
    <result column="warehouse_code" jdbcType="VARCHAR" property="warehouseCode" />
    <result column="recommend_reason" jdbcType="VARCHAR" property="recommendReason" />
    <result column="promotion_desc" jdbcType="VARCHAR" property="promotionDesc" />
    <result column="composite_new" jdbcType="TINYINT" property="compositeNew" />
    <result column="thirty_sales_quantity" jdbcType="DECIMAL" property="thirtySalesQuantity" />
    <result column="promotion_title" jdbcType="VARCHAR" property="promotionTitle" />
    <result column="promotion_start_date" jdbcType="VARCHAR" property="promotionStartDate" />
    <result column="promotion_end_date" jdbcType="VARCHAR" property="promotionEndDate" />
    <result column="deal_suggest" jdbcType="TINYINT" property="dealSuggest" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, apply_no, bdp_apply_no, apply_line, company_org_id, business_id, company_org_name, 
    company_code, store_org_id, store_id, store_code, store_name, apply_date, data_origin_type, 
    goods_no, apply_goods_type, bdp_apply_total, var_a, var_b, middle_package_switch, 
    middle_package_qty, apply_ratio, category_id, apply_total, bdp_average_daily_sales,
    min_display_qty, stock_upper_limit, stock_lower_limit, sale_days_before, sale_days_after,
    special_ctrl, special_thirty_days_qty, apply_reason, apply_stock, scarce_limit, purchase_type,
    purchase_channel, warehouse_code, recommend_reason, promotion_desc, composite_new, thirty_sales_quantity,
    promotion_title, promotion_start_date, promotion_end_date, deal_suggest, gmt_create, gmt_update
  </sql>
  <select id="selectByExample" parameterType="com.cowell.iscm.entity.IscmRecalculationRecordExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from iscm_recalculation_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from iscm_recalculation_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from iscm_recalculation_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.iscm.entity.IscmRecalculationRecordExample">
    delete from iscm_recalculation_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entity.IscmRecalculationRecord" useGeneratedKeys="true">
    insert into iscm_recalculation_record (apply_no, bdp_apply_no, apply_line, 
      company_org_id, business_id, company_org_name, 
      company_code, store_org_id, store_id, 
      store_code, store_name, apply_date, 
      data_origin_type, goods_no, apply_goods_type, 
      bdp_apply_total, var_a, var_b, 
      middle_package_switch, middle_package_qty, 
      apply_ratio, category_id, apply_total,
      bdp_average_daily_sales, min_display_qty, stock_upper_limit,
      stock_lower_limit, sale_days_before, sale_days_after,
      special_ctrl, special_thirty_days_qty, apply_reason,
      apply_stock, scarce_limit, purchase_type, purchase_channel,
      warehouse_code, recommend_reason, promotion_desc,
      composite_new, thirty_sales_quantity, promotion_title,
      promotion_start_date, promotion_end_date, deal_suggest, gmt_create,
      gmt_update)
    values (#{applyNo,jdbcType=VARCHAR}, #{bdpApplyNo,jdbcType=VARCHAR}, #{applyLine,jdbcType=VARCHAR}, 
      #{companyOrgId,jdbcType=BIGINT}, #{businessId,jdbcType=BIGINT}, #{companyOrgName,jdbcType=VARCHAR}, 
      #{companyCode,jdbcType=VARCHAR}, #{storeOrgId,jdbcType=BIGINT}, #{storeId,jdbcType=BIGINT}, 
      #{storeCode,jdbcType=VARCHAR}, #{storeName,jdbcType=VARCHAR}, #{applyDate,jdbcType=DATE}, 
      #{dataOriginType,jdbcType=TINYINT}, #{goodsNo,jdbcType=VARCHAR}, #{applyGoodsType,jdbcType=TINYINT}, 
      #{bdpApplyTotal,jdbcType=DECIMAL}, #{varA,jdbcType=DECIMAL}, #{varB,jdbcType=DECIMAL}, 
      #{middlePackageSwitch,jdbcType=VARCHAR}, #{middlePackageQty,jdbcType=DECIMAL}, 
      #{applyRatio,jdbcType=DECIMAL}, #{categoryId,jdbcType=BIGINT}, #{applyTotal,jdbcType=DECIMAL},
      #{bdpAverageDailySales,jdbcType=DECIMAL}, #{minDisplayQty,jdbcType=DECIMAL}, #{stockUpperLimit,jdbcType=DECIMAL},
      #{stockLowerLimit,jdbcType=DECIMAL}, #{saleDaysBefore,jdbcType=DECIMAL}, #{saleDaysAfter,jdbcType=DECIMAL},
      #{specialCtrl,jdbcType=VARCHAR}, #{specialThirtyDaysQty,jdbcType=DECIMAL}, #{applyReason,jdbcType=VARCHAR},
      #{applyStock,jdbcType=DECIMAL}, #{scarceLimit,jdbcType=INTEGER}, #{purchaseType,jdbcType=INTEGER}, #{purchaseChannel,jdbcType=VARCHAR},
      #{warehouseCode,jdbcType=VARCHAR}, #{recommendReason,jdbcType=VARCHAR}, #{promotionDesc,jdbcType=VARCHAR},
      #{compositeNew,jdbcType=TINYINT}, #{thirtySalesQuantity,jdbcType=DECIMAL}, #{promotionTitle,jdbcType=VARCHAR},
      #{promotionStartDate,jdbcType=VARCHAR}, #{promotionEndDate,jdbcType=VARCHAR}, #{dealSuggest,jdbcType=TINYINT}, #{gmtCreate,jdbcType=TIMESTAMP},
      #{gmtUpdate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entity.IscmRecalculationRecord" useGeneratedKeys="true">
    insert into iscm_recalculation_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="applyNo != null">
        apply_no,
      </if>
      <if test="bdpApplyNo != null">
        bdp_apply_no,
      </if>
      <if test="applyLine != null">
        apply_line,
      </if>
      <if test="companyOrgId != null">
        company_org_id,
      </if>
      <if test="businessId != null">
        business_id,
      </if>
      <if test="companyOrgName != null">
        company_org_name,
      </if>
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="storeOrgId != null">
        store_org_id,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="storeCode != null">
        store_code,
      </if>
      <if test="storeName != null">
        store_name,
      </if>
      <if test="applyDate != null">
        apply_date,
      </if>
      <if test="dataOriginType != null">
        data_origin_type,
      </if>
      <if test="goodsNo != null">
        goods_no,
      </if>
      <if test="applyGoodsType != null">
        apply_goods_type,
      </if>
      <if test="bdpApplyTotal != null">
        bdp_apply_total,
      </if>
      <if test="varA != null">
        var_a,
      </if>
      <if test="varB != null">
        var_b,
      </if>
      <if test="middlePackageSwitch != null">
        middle_package_switch,
      </if>
      <if test="middlePackageQty != null">
        middle_package_qty,
      </if>
      <if test="applyRatio != null">
        apply_ratio,
      </if>
      <if test="categoryId != null">
        category_id,
      </if>
      <if test="applyTotal != null">
        apply_total,
      </if>
      <if test="bdpAverageDailySales != null">
        bdp_average_daily_sales,
      </if>
      <if test="minDisplayQty != null">
        min_display_qty,
      </if>
      <if test="stockUpperLimit != null">
        stock_upper_limit,
      </if>
      <if test="stockLowerLimit != null">
        stock_lower_limit,
      </if>
      <if test="saleDaysBefore != null">
        sale_days_before,
      </if>
      <if test="saleDaysAfter != null">
        sale_days_after,
      </if>
      <if test="specialCtrl != null">
        special_ctrl,
      </if>
      <if test="specialThirtyDaysQty != null">
        special_thirty_days_qty,
      </if>
      <if test="applyReason != null">
        apply_reason,
      </if>
      <if test="applyStock != null">
        apply_stock,
      </if>
      <if test="scarceLimit != null">
        scarce_limit,
      </if>
      <if test="purchaseType != null">
        purchase_type,
      </if>
      <if test="purchaseChannel != null">
        purchase_channel,
      </if>
      <if test="warehouseCode != null">
        warehouse_code,
      </if>
      <if test="recommendReason != null">
        recommend_reason,
      </if>
      <if test="promotionDesc != null">
        promotion_desc,
      </if>
      <if test="compositeNew != null">
        composite_new,
      </if>
      <if test="thirtySalesQuantity != null">
        thirty_sales_quantity,
      </if>
      <if test="promotionTitle != null">
        promotion_title,
      </if>
      <if test="promotionStartDate != null">
        promotion_start_date,
      </if>
      <if test="promotionEndDate != null">
        promotion_end_date,
      </if>
      <if test="dealSuggest != null">
        deal_suggest,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="applyNo != null">
        #{applyNo,jdbcType=VARCHAR},
      </if>
      <if test="bdpApplyNo != null">
        #{bdpApplyNo,jdbcType=VARCHAR},
      </if>
      <if test="applyLine != null">
        #{applyLine,jdbcType=VARCHAR},
      </if>
      <if test="companyOrgId != null">
        #{companyOrgId,jdbcType=BIGINT},
      </if>
      <if test="businessId != null">
        #{businessId,jdbcType=BIGINT},
      </if>
      <if test="companyOrgName != null">
        #{companyOrgName,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="storeOrgId != null">
        #{storeOrgId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="storeCode != null">
        #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="storeName != null">
        #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="applyDate != null">
        #{applyDate,jdbcType=DATE},
      </if>
      <if test="dataOriginType != null">
        #{dataOriginType,jdbcType=TINYINT},
      </if>
      <if test="goodsNo != null">
        #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="applyGoodsType != null">
        #{applyGoodsType,jdbcType=TINYINT},
      </if>
      <if test="bdpApplyTotal != null">
        #{bdpApplyTotal,jdbcType=DECIMAL},
      </if>
      <if test="varA != null">
        #{varA,jdbcType=DECIMAL},
      </if>
      <if test="varB != null">
        #{varB,jdbcType=DECIMAL},
      </if>
      <if test="middlePackageSwitch != null">
        #{middlePackageSwitch,jdbcType=VARCHAR},
      </if>
      <if test="middlePackageQty != null">
        #{middlePackageQty,jdbcType=DECIMAL},
      </if>
      <if test="applyRatio != null">
        #{applyRatio,jdbcType=DECIMAL},
      </if>
      <if test="categoryId != null">
        #{categoryId,jdbcType=BIGINT},
      </if>
      <if test="applyTotal != null">
        #{applyTotal,jdbcType=DECIMAL},
      </if>
      <if test="bdpAverageDailySales != null">
        #{bdpAverageDailySales,jdbcType=DECIMAL},
      </if>
      <if test="minDisplayQty != null">
        #{minDisplayQty,jdbcType=DECIMAL},
      </if>
      <if test="stockUpperLimit != null">
        #{stockUpperLimit,jdbcType=DECIMAL},
      </if>
      <if test="stockLowerLimit != null">
        #{stockLowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="saleDaysBefore != null">
        #{saleDaysBefore,jdbcType=DECIMAL},
      </if>
      <if test="saleDaysAfter != null">
        #{saleDaysAfter,jdbcType=DECIMAL},
      </if>
      <if test="specialCtrl != null">
        #{specialCtrl,jdbcType=VARCHAR},
      </if>
      <if test="specialThirtyDaysQty != null">
        #{specialThirtyDaysQty,jdbcType=DECIMAL},
      </if>
      <if test="applyReason != null">
        #{applyReason,jdbcType=VARCHAR},
      </if>
      <if test="applyStock != null">
        #{applyStock,jdbcType=DECIMAL},
      </if>
      <if test="scarceLimit != null">
        #{scarceLimit,jdbcType=INTEGER},
      </if>
      <if test="purchaseType != null">
        #{purchaseType,jdbcType=INTEGER},
      </if>
      <if test="purchaseChannel != null">
        #{purchaseChannel,jdbcType=VARCHAR},
      </if>
      <if test="warehouseCode != null">
        #{warehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="recommendReason != null">
        #{recommendReason,jdbcType=VARCHAR},
      </if>
      <if test="promotionDesc != null">
        #{promotionDesc,jdbcType=VARCHAR},
      </if>
      <if test="compositeNew != null">
        #{compositeNew,jdbcType=TINYINT},
      </if>
      <if test="thirtySalesQuantity != null">
        #{thirtySalesQuantity,jdbcType=DECIMAL},
      </if>
      <if test="promotionTitle != null">
        #{promotionTitle,jdbcType=VARCHAR},
      </if>
      <if test="promotionStartDate != null">
        #{promotionStartDate,jdbcType=VARCHAR},
      </if>
      <if test="promotionEndDate != null">
        #{promotionEndDate,jdbcType=VARCHAR},
      </if>
      <if test="dealSuggest != null">
        #{dealSuggest,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.iscm.entity.IscmRecalculationRecordExample" resultType="java.lang.Long">
    select count(*) from iscm_recalculation_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update iscm_recalculation_record
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.applyNo != null">
        apply_no = #{record.applyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.bdpApplyNo != null">
        bdp_apply_no = #{record.bdpApplyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.applyLine != null">
        apply_line = #{record.applyLine,jdbcType=VARCHAR},
      </if>
      <if test="record.companyOrgId != null">
        company_org_id = #{record.companyOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.businessId != null">
        business_id = #{record.businessId,jdbcType=BIGINT},
      </if>
      <if test="record.companyOrgName != null">
        company_org_name = #{record.companyOrgName,jdbcType=VARCHAR},
      </if>
      <if test="record.companyCode != null">
        company_code = #{record.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.storeOrgId != null">
        store_org_id = #{record.storeOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.storeCode != null">
        store_code = #{record.storeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.storeName != null">
        store_name = #{record.storeName,jdbcType=VARCHAR},
      </if>
      <if test="record.applyDate != null">
        apply_date = #{record.applyDate,jdbcType=DATE},
      </if>
      <if test="record.dataOriginType != null">
        data_origin_type = #{record.dataOriginType,jdbcType=TINYINT},
      </if>
      <if test="record.goodsNo != null">
        goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="record.applyGoodsType != null">
        apply_goods_type = #{record.applyGoodsType,jdbcType=TINYINT},
      </if>
      <if test="record.bdpApplyTotal != null">
        bdp_apply_total = #{record.bdpApplyTotal,jdbcType=DECIMAL},
      </if>
      <if test="record.varA != null">
        var_a = #{record.varA,jdbcType=DECIMAL},
      </if>
      <if test="record.varB != null">
        var_b = #{record.varB,jdbcType=DECIMAL},
      </if>
      <if test="record.middlePackageSwitch != null">
        middle_package_switch = #{record.middlePackageSwitch,jdbcType=VARCHAR},
      </if>
      <if test="record.middlePackageQty != null">
        middle_package_qty = #{record.middlePackageQty,jdbcType=DECIMAL},
      </if>
      <if test="record.applyRatio != null">
        apply_ratio = #{record.applyRatio,jdbcType=DECIMAL},
      </if>
      <if test="record.categoryId != null">
        category_id = #{record.categoryId,jdbcType=BIGINT},
      </if>
      <if test="record.applyTotal != null">
        apply_total = #{record.applyTotal,jdbcType=DECIMAL},
      </if>
      <if test="record.bdpAverageDailySales != null">
        bdp_average_daily_sales = #{record.bdpAverageDailySales,jdbcType=DECIMAL},
      </if>
      <if test="record.minDisplayQty != null">
        min_display_qty = #{record.minDisplayQty,jdbcType=DECIMAL},
      </if>
      <if test="record.stockUpperLimit != null">
        stock_upper_limit = #{record.stockUpperLimit,jdbcType=DECIMAL},
      </if>
      <if test="record.stockLowerLimit != null">
        stock_lower_limit = #{record.stockLowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="record.saleDaysBefore != null">
        sale_days_before = #{record.saleDaysBefore,jdbcType=DECIMAL},
      </if>
      <if test="record.saleDaysAfter != null">
        sale_days_after = #{record.saleDaysAfter,jdbcType=DECIMAL},
      </if>
      <if test="record.specialCtrl != null">
        special_ctrl = #{record.specialCtrl,jdbcType=VARCHAR},
      </if>
      <if test="record.specialThirtyDaysQty != null">
        special_thirty_days_qty = #{record.specialThirtyDaysQty,jdbcType=DECIMAL},
      </if>
      <if test="record.applyReason != null">
        apply_reason = #{record.applyReason,jdbcType=VARCHAR},
      </if>
      <if test="record.applyStock != null">
        apply_stock = #{record.applyStock,jdbcType=DECIMAL},
      </if>
      <if test="record.scarceLimit != null">
        scarce_limit = #{record.scarceLimit,jdbcType=INTEGER},
      </if>
      <if test="record.purchaseType != null">
        purchase_type = #{record.purchaseType,jdbcType=INTEGER},
      </if>
      <if test="record.purchaseChannel != null">
        purchase_channel = #{record.purchaseChannel,jdbcType=VARCHAR},
      </if>
      <if test="record.warehouseCode != null">
        warehouse_code = #{record.warehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="record.recommendReason != null">
        recommend_reason = #{record.recommendReason,jdbcType=VARCHAR},
      </if>
      <if test="record.promotionDesc != null">
        promotion_desc = #{record.promotionDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.compositeNew != null">
        composite_new = #{record.compositeNew,jdbcType=TINYINT},
      </if>
      <if test="record.thirtySalesQuantity != null">
        thirty_sales_quantity = #{record.thirtySalesQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.promotionTitle != null">
        promotion_title = #{record.promotionTitle,jdbcType=VARCHAR},
      </if>
      <if test="record.promotionStartDate != null">
        promotion_start_date = #{record.promotionStartDate,jdbcType=VARCHAR},
      </if>
      <if test="record.promotionEndDate != null">
        promotion_end_date = #{record.promotionEndDate,jdbcType=VARCHAR},
      </if>
      <if test="record.dealSuggest != null">
        deal_suggest = #{record.dealSuggest,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update iscm_recalculation_record
    set id = #{record.id,jdbcType=BIGINT},
      apply_no = #{record.applyNo,jdbcType=VARCHAR},
      bdp_apply_no = #{record.bdpApplyNo,jdbcType=VARCHAR},
      apply_line = #{record.applyLine,jdbcType=VARCHAR},
      company_org_id = #{record.companyOrgId,jdbcType=BIGINT},
      business_id = #{record.businessId,jdbcType=BIGINT},
      company_org_name = #{record.companyOrgName,jdbcType=VARCHAR},
      company_code = #{record.companyCode,jdbcType=VARCHAR},
      store_org_id = #{record.storeOrgId,jdbcType=BIGINT},
      store_id = #{record.storeId,jdbcType=BIGINT},
      store_code = #{record.storeCode,jdbcType=VARCHAR},
      store_name = #{record.storeName,jdbcType=VARCHAR},
      apply_date = #{record.applyDate,jdbcType=DATE},
      data_origin_type = #{record.dataOriginType,jdbcType=TINYINT},
      goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      apply_goods_type = #{record.applyGoodsType,jdbcType=TINYINT},
      bdp_apply_total = #{record.bdpApplyTotal,jdbcType=DECIMAL},
      var_a = #{record.varA,jdbcType=DECIMAL},
      var_b = #{record.varB,jdbcType=DECIMAL},
      middle_package_switch = #{record.middlePackageSwitch,jdbcType=VARCHAR},
      middle_package_qty = #{record.middlePackageQty,jdbcType=DECIMAL},
      apply_ratio = #{record.applyRatio,jdbcType=DECIMAL},
      category_id = #{record.categoryId,jdbcType=BIGINT},
      apply_total = #{record.applyTotal,jdbcType=DECIMAL},
      bdp_average_daily_sales = #{record.bdpAverageDailySales,jdbcType=DECIMAL},
      min_display_qty = #{record.minDisplayQty,jdbcType=DECIMAL},
      stock_upper_limit = #{record.stockUpperLimit,jdbcType=DECIMAL},
      stock_lower_limit = #{record.stockLowerLimit,jdbcType=DECIMAL},
      sale_days_before = #{record.saleDaysBefore,jdbcType=DECIMAL},
      sale_days_after = #{record.saleDaysAfter,jdbcType=DECIMAL},
      special_ctrl = #{record.specialCtrl,jdbcType=VARCHAR},
      special_thirty_days_qty = #{record.specialThirtyDaysQty,jdbcType=DECIMAL},
      apply_reason = #{record.applyReason,jdbcType=VARCHAR},
      apply_stock = #{record.applyStock,jdbcType=DECIMAL},
      scarce_limit = #{record.scarceLimit,jdbcType=INTEGER},
      purchase_type = #{record.purchaseType,jdbcType=INTEGER},
      purchase_channel = #{record.purchaseChannel,jdbcType=VARCHAR},
      warehouse_code = #{record.warehouseCode,jdbcType=VARCHAR},
      recommend_reason = #{record.recommendReason,jdbcType=VARCHAR},
      promotion_desc = #{record.promotionDesc,jdbcType=VARCHAR},
      composite_new = #{record.compositeNew,jdbcType=TINYINT},
      thirty_sales_quantity = #{record.thirtySalesQuantity,jdbcType=DECIMAL},
      promotion_title = #{record.promotionTitle,jdbcType=VARCHAR},
      promotion_start_date = #{record.promotionStartDate,jdbcType=VARCHAR},
      promotion_end_date = #{record.promotionEndDate,jdbcType=VARCHAR},
      deal_suggest = #{record.dealSuggest,jdbcType=TINYINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.iscm.entity.IscmRecalculationRecord">
    update iscm_recalculation_record
    <set>
      <if test="applyNo != null">
        apply_no = #{applyNo,jdbcType=VARCHAR},
      </if>
      <if test="bdpApplyNo != null">
        bdp_apply_no = #{bdpApplyNo,jdbcType=VARCHAR},
      </if>
      <if test="applyLine != null">
        apply_line = #{applyLine,jdbcType=VARCHAR},
      </if>
      <if test="companyOrgId != null">
        company_org_id = #{companyOrgId,jdbcType=BIGINT},
      </if>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=BIGINT},
      </if>
      <if test="companyOrgName != null">
        company_org_name = #{companyOrgName,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="storeOrgId != null">
        store_org_id = #{storeOrgId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="storeCode != null">
        store_code = #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="storeName != null">
        store_name = #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="applyDate != null">
        apply_date = #{applyDate,jdbcType=DATE},
      </if>
      <if test="dataOriginType != null">
        data_origin_type = #{dataOriginType,jdbcType=TINYINT},
      </if>
      <if test="goodsNo != null">
        goods_no = #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="applyGoodsType != null">
        apply_goods_type = #{applyGoodsType,jdbcType=TINYINT},
      </if>
      <if test="bdpApplyTotal != null">
        bdp_apply_total = #{bdpApplyTotal,jdbcType=DECIMAL},
      </if>
      <if test="varA != null">
        var_a = #{varA,jdbcType=DECIMAL},
      </if>
      <if test="varB != null">
        var_b = #{varB,jdbcType=DECIMAL},
      </if>
      <if test="middlePackageSwitch != null">
        middle_package_switch = #{middlePackageSwitch,jdbcType=VARCHAR},
      </if>
      <if test="middlePackageQty != null">
        middle_package_qty = #{middlePackageQty,jdbcType=DECIMAL},
      </if>
      <if test="applyRatio != null">
        apply_ratio = #{applyRatio,jdbcType=DECIMAL},
      </if>
      <if test="categoryId != null">
        category_id = #{categoryId,jdbcType=BIGINT},
      </if>
      <if test="applyTotal != null">
        apply_total = #{applyTotal,jdbcType=DECIMAL},
      </if>
      <if test="bdpAverageDailySales != null">
        bdp_average_daily_sales = #{bdpAverageDailySales,jdbcType=DECIMAL},
      </if>
      <if test="minDisplayQty != null">
        min_display_qty = #{minDisplayQty,jdbcType=DECIMAL},
      </if>
      <if test="stockUpperLimit != null">
        stock_upper_limit = #{stockUpperLimit,jdbcType=DECIMAL},
      </if>
      <if test="stockLowerLimit != null">
        stock_lower_limit = #{stockLowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="saleDaysBefore != null">
        sale_days_before = #{saleDaysBefore,jdbcType=DECIMAL},
      </if>
      <if test="saleDaysAfter != null">
        sale_days_after = #{saleDaysAfter,jdbcType=DECIMAL},
      </if>
      <if test="specialCtrl != null">
        special_ctrl = #{specialCtrl,jdbcType=VARCHAR},
      </if>
      <if test="specialThirtyDaysQty != null">
        special_thirty_days_qty = #{specialThirtyDaysQty,jdbcType=DECIMAL},
      </if>
      <if test="applyReason != null">
        apply_reason = #{applyReason,jdbcType=VARCHAR},
      </if>
      <if test="applyStock != null">
        apply_stock = #{applyStock,jdbcType=DECIMAL},
      </if>
      <if test="scarceLimit != null">
        scarce_limit = #{scarceLimit,jdbcType=INTEGER},
      </if>
      <if test="purchaseType != null">
        purchase_type = #{purchaseType,jdbcType=INTEGER},
      </if>
      <if test="purchaseChannel != null">
        purchase_channel = #{purchaseChannel,jdbcType=VARCHAR},
      </if>
      <if test="warehouseCode != null">
        warehouse_code = #{warehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="recommendReason != null">
        recommend_reason = #{recommendReason,jdbcType=VARCHAR},
      </if>
      <if test="promotionDesc != null">
        promotion_desc = #{promotionDesc,jdbcType=VARCHAR},
      </if>
      <if test="compositeNew != null">
        composite_new = #{compositeNew,jdbcType=TINYINT},
      </if>
      <if test="thirtySalesQuantity != null">
        thirty_sales_quantity = #{thirtySalesQuantity,jdbcType=DECIMAL},
      </if>
      <if test="promotionTitle != null">
        promotion_title = #{promotionTitle,jdbcType=VARCHAR},
      </if>
      <if test="promotionStartDate != null">
        promotion_start_date = #{promotionStartDate,jdbcType=VARCHAR},
      </if>
      <if test="promotionEndDate != null">
        promotion_end_date = #{promotionEndDate,jdbcType=VARCHAR},
      </if>
      <if test="dealSuggest != null">
        deal_suggest = #{dealSuggest,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.iscm.entity.IscmRecalculationRecord">
    update iscm_recalculation_record
    set apply_no = #{applyNo,jdbcType=VARCHAR},
      bdp_apply_no = #{bdpApplyNo,jdbcType=VARCHAR},
      apply_line = #{applyLine,jdbcType=VARCHAR},
      company_org_id = #{companyOrgId,jdbcType=BIGINT},
      business_id = #{businessId,jdbcType=BIGINT},
      company_org_name = #{companyOrgName,jdbcType=VARCHAR},
      company_code = #{companyCode,jdbcType=VARCHAR},
      store_org_id = #{storeOrgId,jdbcType=BIGINT},
      store_id = #{storeId,jdbcType=BIGINT},
      store_code = #{storeCode,jdbcType=VARCHAR},
      store_name = #{storeName,jdbcType=VARCHAR},
      apply_date = #{applyDate,jdbcType=DATE},
      data_origin_type = #{dataOriginType,jdbcType=TINYINT},
      goods_no = #{goodsNo,jdbcType=VARCHAR},
      apply_goods_type = #{applyGoodsType,jdbcType=TINYINT},
      bdp_apply_total = #{bdpApplyTotal,jdbcType=DECIMAL},
      var_a = #{varA,jdbcType=DECIMAL},
      var_b = #{varB,jdbcType=DECIMAL},
      middle_package_switch = #{middlePackageSwitch,jdbcType=VARCHAR},
      middle_package_qty = #{middlePackageQty,jdbcType=DECIMAL},
      apply_ratio = #{applyRatio,jdbcType=DECIMAL},
      category_id = #{categoryId,jdbcType=BIGINT},
      apply_total = #{applyTotal,jdbcType=DECIMAL},
      bdp_average_daily_sales = #{bdpAverageDailySales,jdbcType=DECIMAL},
      min_display_qty = #{minDisplayQty,jdbcType=DECIMAL},
      stock_upper_limit = #{stockUpperLimit,jdbcType=DECIMAL},
      stock_lower_limit = #{stockLowerLimit,jdbcType=DECIMAL},
      sale_days_before = #{saleDaysBefore,jdbcType=DECIMAL},
      sale_days_after = #{saleDaysAfter,jdbcType=DECIMAL},
      special_ctrl = #{specialCtrl,jdbcType=VARCHAR},
      special_thirty_days_qty = #{specialThirtyDaysQty,jdbcType=DECIMAL},
      apply_reason = #{applyReason,jdbcType=VARCHAR},
      apply_stock = #{applyStock,jdbcType=DECIMAL},
      scarce_limit = #{scarceLimit,jdbcType=INTEGER},
      purchase_type = #{purchaseType,jdbcType=INTEGER},
      purchase_channel = #{purchaseChannel,jdbcType=VARCHAR},
      warehouse_code = #{warehouseCode,jdbcType=VARCHAR},
      recommend_reason = #{recommendReason,jdbcType=VARCHAR},
      promotion_desc = #{promotionDesc,jdbcType=VARCHAR},
      composite_new = #{compositeNew,jdbcType=TINYINT},
      thirty_sales_quantity = #{thirtySalesQuantity,jdbcType=DECIMAL},
      promotion_title = #{promotionTitle,jdbcType=VARCHAR},
      promotion_start_date = #{promotionStartDate,jdbcType=VARCHAR},
      promotion_end_date = #{promotionEndDate,jdbcType=VARCHAR},
      deal_suggest = #{dealSuggest,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
