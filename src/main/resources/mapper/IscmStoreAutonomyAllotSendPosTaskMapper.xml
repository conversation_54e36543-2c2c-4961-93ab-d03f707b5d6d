<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.IscmStoreAutonomyAllotSendPosTaskMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.IscmStoreAutonomyAllotSendPosTask">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="send_by" jdbcType="BIGINT" property="sendBy" />
    <result column="send_name" jdbcType="VARCHAR" property="sendName" />
    <result column="send_time" jdbcType="TIMESTAMP" property="sendTime" />
    <result column="query_params" jdbcType="VARCHAR" property="queryParams" />
    <result column="send_count" jdbcType="BIGINT" property="sendCount" />
    <result column="send_type" jdbcType="TINYINT" property="sendType" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, send_by, send_name, send_time, query_params, send_count, send_type, `status`, 
    gmt_create, gmt_update, extend, version, created_by, created_name, updated_by, updated_name
  </sql>
  <select id="selectByExample" parameterType="com.cowell.iscm.entity.IscmStoreAutonomyAllotSendPosTaskExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from iscm_store_autonomy_allot_send_pos_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from iscm_store_autonomy_allot_send_pos_task
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from iscm_store_autonomy_allot_send_pos_task
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.iscm.entity.IscmStoreAutonomyAllotSendPosTaskExample">
    delete from iscm_store_autonomy_allot_send_pos_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entity.IscmStoreAutonomyAllotSendPosTask" useGeneratedKeys="true">
    insert into iscm_store_autonomy_allot_send_pos_task (send_by, send_name, send_time, 
      query_params, send_count, send_type, 
      `status`, gmt_create, gmt_update, 
      extend, version, created_by, 
      created_name, updated_by, updated_name
      )
    values (#{sendBy,jdbcType=BIGINT}, #{sendName,jdbcType=VARCHAR}, #{sendTime,jdbcType=TIMESTAMP}, 
      #{queryParams,jdbcType=VARCHAR}, #{sendCount,jdbcType=BIGINT}, #{sendType,jdbcType=TINYINT}, 
      #{status,jdbcType=TINYINT}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}, 
      #{extend,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER}, #{createdBy,jdbcType=BIGINT}, 
      #{createdName,jdbcType=VARCHAR}, #{updatedBy,jdbcType=BIGINT}, #{updatedName,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entity.IscmStoreAutonomyAllotSendPosTask" useGeneratedKeys="true">
    insert into iscm_store_autonomy_allot_send_pos_task
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="sendBy != null">
        send_by,
      </if>
      <if test="sendName != null">
        send_name,
      </if>
      <if test="sendTime != null">
        send_time,
      </if>
      <if test="queryParams != null">
        query_params,
      </if>
      <if test="sendCount != null">
        send_count,
      </if>
      <if test="sendType != null">
        send_type,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedName != null">
        updated_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="sendBy != null">
        #{sendBy,jdbcType=BIGINT},
      </if>
      <if test="sendName != null">
        #{sendName,jdbcType=VARCHAR},
      </if>
      <if test="sendTime != null">
        #{sendTime,jdbcType=TIMESTAMP},
      </if>
      <if test="queryParams != null">
        #{queryParams,jdbcType=VARCHAR},
      </if>
      <if test="sendCount != null">
        #{sendCount,jdbcType=BIGINT},
      </if>
      <if test="sendType != null">
        #{sendType,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        #{updatedName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.iscm.entity.IscmStoreAutonomyAllotSendPosTaskExample" resultType="java.lang.Long">
    select count(*) from iscm_store_autonomy_allot_send_pos_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update iscm_store_autonomy_allot_send_pos_task
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.sendBy != null">
        send_by = #{record.sendBy,jdbcType=BIGINT},
      </if>
      <if test="record.sendName != null">
        send_name = #{record.sendName,jdbcType=VARCHAR},
      </if>
      <if test="record.sendTime != null">
        send_time = #{record.sendTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.queryParams != null">
        query_params = #{record.queryParams,jdbcType=VARCHAR},
      </if>
      <if test="record.sendCount != null">
        send_count = #{record.sendCount,jdbcType=BIGINT},
      </if>
      <if test="record.sendType != null">
        send_type = #{record.sendType,jdbcType=TINYINT},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedName != null">
        updated_name = #{record.updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update iscm_store_autonomy_allot_send_pos_task
    set id = #{record.id,jdbcType=BIGINT},
      send_by = #{record.sendBy,jdbcType=BIGINT},
      send_name = #{record.sendName,jdbcType=VARCHAR},
      send_time = #{record.sendTime,jdbcType=TIMESTAMP},
      query_params = #{record.queryParams,jdbcType=VARCHAR},
      send_count = #{record.sendCount,jdbcType=BIGINT},
      send_type = #{record.sendType,jdbcType=TINYINT},
      `status` = #{record.status,jdbcType=TINYINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_name = #{record.createdName,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_name = #{record.updatedName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.iscm.entity.IscmStoreAutonomyAllotSendPosTask">
    update iscm_store_autonomy_allot_send_pos_task
    <set>
      <if test="sendBy != null">
        send_by = #{sendBy,jdbcType=BIGINT},
      </if>
      <if test="sendName != null">
        send_name = #{sendName,jdbcType=VARCHAR},
      </if>
      <if test="sendTime != null">
        send_time = #{sendTime,jdbcType=TIMESTAMP},
      </if>
      <if test="queryParams != null">
        query_params = #{queryParams,jdbcType=VARCHAR},
      </if>
      <if test="sendCount != null">
        send_count = #{sendCount,jdbcType=BIGINT},
      </if>
      <if test="sendType != null">
        send_type = #{sendType,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        updated_name = #{updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.iscm.entity.IscmStoreAutonomyAllotSendPosTask">
    update iscm_store_autonomy_allot_send_pos_task
    set send_by = #{sendBy,jdbcType=BIGINT},
      send_name = #{sendName,jdbcType=VARCHAR},
      send_time = #{sendTime,jdbcType=TIMESTAMP},
      query_params = #{queryParams,jdbcType=VARCHAR},
      send_count = #{sendCount,jdbcType=BIGINT},
      send_type = #{sendType,jdbcType=TINYINT},
      `status` = #{status,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_name = #{createdName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_name = #{updatedName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>