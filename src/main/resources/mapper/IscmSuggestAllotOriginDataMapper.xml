<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.IscmSuggestAllotOriginDataMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.IscmSuggestAllotOriginData">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="register_no" jdbcType="VARCHAR" property="registerNo" />
    <result column="allot_type" jdbcType="TINYINT" property="allotType" />
    <result column="register_source" jdbcType="TINYINT" property="registerSource" />
    <result column="business_date" jdbcType="TIMESTAMP" property="businessDate" />
    <result column="allot_no" jdbcType="VARCHAR" property="allotNo" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
    <result column="out_company_code" jdbcType="VARCHAR" property="outCompanyCode" />
    <result column="in_company_code" jdbcType="VARCHAR" property="inCompanyCode" />
    <result column="out_store_code" jdbcType="VARCHAR" property="outStoreCode" />
    <result column="in_store_code" jdbcType="VARCHAR" property="inStoreCode" />
    <result column="suggest_allot_quantity" jdbcType="DECIMAL" property="suggestAllotQuantity" />
    <result column="in_store_thirty_days_sales" jdbcType="DECIMAL" property="inStoreThirtyDaysSales" />
    <result column="out_store_thirty_days_sales" jdbcType="DECIMAL" property="outStoreThirtyDaysSales" />
    <result column="in_store_thirty_days_sale_times" jdbcType="DECIMAL" property="inStoreThirtyDaysSaleTimes" />
    <result column="out_store_thirty_days_sale_times" jdbcType="DECIMAL" property="outStoreThirtyDaysSaleTimes" />
    <result column="in_store_stock" jdbcType="DECIMAL" property="inStoreStock" />
    <result column="out_store_stock" jdbcType="DECIMAL" property="outStoreStock" />
    <result column="in_store_stock_min" jdbcType="DECIMAL" property="inStoreStockMin" />
    <result column="out_store_stock_min" jdbcType="DECIMAL" property="outStoreStockMin" />
    <result column="in_store_stock_max" jdbcType="DECIMAL" property="inStoreStockMax" />
    <result column="out_store_stock_max" jdbcType="DECIMAL" property="outStoreStockMax" />
    <result column="in_store_stock_onway" jdbcType="DECIMAL" property="inStoreStockOnway" />
    <result column="out_store_stock_onway" jdbcType="DECIMAL" property="outStoreStockOnway" />
    <result column="in_store_pos_daily_sales" jdbcType="DECIMAL" property="inStorePosDailySales" />
    <result column="out_store_pos_daily_sales" jdbcType="DECIMAL" property="outStorePosDailySales" />
    <result column="transfer_cost_amount" jdbcType="DECIMAL" property="transferCostAmount" />
    <result column="model_code" jdbcType="VARCHAR" property="modelCode" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, register_no, allot_type, register_source, business_date, allot_no, goods_no, 
    batch_no, out_company_code, in_company_code, out_store_code, in_store_code, suggest_allot_quantity, 
    in_store_thirty_days_sales, out_store_thirty_days_sales, in_store_thirty_days_sale_times, 
    out_store_thirty_days_sale_times, in_store_stock, out_store_stock, in_store_stock_min, 
    out_store_stock_min, in_store_stock_max, out_store_stock_max, in_store_stock_onway, 
    out_store_stock_onway, in_store_pos_daily_sales, out_store_pos_daily_sales, transfer_cost_amount, 
    model_code, `status`, gmt_create, gmt_update, extend, version, created_by, created_name, 
    updated_by, updated_name
  </sql>
  <select id="selectByExample" parameterType="com.cowell.iscm.entity.IscmSuggestAllotOriginDataExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from iscm_suggest_allot_origin_data_temp
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from iscm_suggest_allot_origin_data_temp
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from iscm_suggest_allot_origin_data_temp
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.iscm.entity.IscmSuggestAllotOriginDataExample">
    delete from iscm_suggest_allot_origin_data_temp
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.iscm.entity.IscmSuggestAllotOriginData">
    insert into iscm_suggest_allot_origin_data_temp (id, register_no, allot_type, 
      register_source, business_date, allot_no, 
      goods_no, batch_no, out_company_code, 
      in_company_code, out_store_code, in_store_code, 
      suggest_allot_quantity, in_store_thirty_days_sales, 
      out_store_thirty_days_sales, in_store_thirty_days_sale_times, 
      out_store_thirty_days_sale_times, in_store_stock, 
      out_store_stock, in_store_stock_min, out_store_stock_min, 
      in_store_stock_max, out_store_stock_max, in_store_stock_onway, 
      out_store_stock_onway, in_store_pos_daily_sales, 
      out_store_pos_daily_sales, transfer_cost_amount, 
      model_code, `status`, gmt_create, 
      gmt_update, extend, version, 
      created_by, created_name, updated_by, 
      updated_name)
    values (#{id,jdbcType=BIGINT}, #{registerNo,jdbcType=VARCHAR}, #{allotType,jdbcType=TINYINT}, 
      #{registerSource,jdbcType=TINYINT}, #{businessDate,jdbcType=TIMESTAMP}, #{allotNo,jdbcType=VARCHAR}, 
      #{goodsNo,jdbcType=VARCHAR}, #{batchNo,jdbcType=VARCHAR}, #{outCompanyCode,jdbcType=VARCHAR}, 
      #{inCompanyCode,jdbcType=VARCHAR}, #{outStoreCode,jdbcType=VARCHAR}, #{inStoreCode,jdbcType=VARCHAR}, 
      #{suggestAllotQuantity,jdbcType=DECIMAL}, #{inStoreThirtyDaysSales,jdbcType=DECIMAL}, 
      #{outStoreThirtyDaysSales,jdbcType=DECIMAL}, #{inStoreThirtyDaysSaleTimes,jdbcType=DECIMAL}, 
      #{outStoreThirtyDaysSaleTimes,jdbcType=DECIMAL}, #{inStoreStock,jdbcType=DECIMAL}, 
      #{outStoreStock,jdbcType=DECIMAL}, #{inStoreStockMin,jdbcType=DECIMAL}, #{outStoreStockMin,jdbcType=DECIMAL}, 
      #{inStoreStockMax,jdbcType=DECIMAL}, #{outStoreStockMax,jdbcType=DECIMAL}, #{inStoreStockOnway,jdbcType=DECIMAL}, 
      #{outStoreStockOnway,jdbcType=DECIMAL}, #{inStorePosDailySales,jdbcType=DECIMAL}, 
      #{outStorePosDailySales,jdbcType=DECIMAL}, #{transferCostAmount,jdbcType=DECIMAL}, 
      #{modelCode,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtUpdate,jdbcType=TIMESTAMP}, #{extend,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER}, 
      #{createdBy,jdbcType=BIGINT}, #{createdName,jdbcType=VARCHAR}, #{updatedBy,jdbcType=BIGINT}, 
      #{updatedName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.iscm.entity.IscmSuggestAllotOriginData">
    insert into iscm_suggest_allot_origin_data_temp
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="registerNo != null">
        register_no,
      </if>
      <if test="allotType != null">
        allot_type,
      </if>
      <if test="registerSource != null">
        register_source,
      </if>
      <if test="businessDate != null">
        business_date,
      </if>
      <if test="allotNo != null">
        allot_no,
      </if>
      <if test="goodsNo != null">
        goods_no,
      </if>
      <if test="batchNo != null">
        batch_no,
      </if>
      <if test="outCompanyCode != null">
        out_company_code,
      </if>
      <if test="inCompanyCode != null">
        in_company_code,
      </if>
      <if test="outStoreCode != null">
        out_store_code,
      </if>
      <if test="inStoreCode != null">
        in_store_code,
      </if>
      <if test="suggestAllotQuantity != null">
        suggest_allot_quantity,
      </if>
      <if test="inStoreThirtyDaysSales != null">
        in_store_thirty_days_sales,
      </if>
      <if test="outStoreThirtyDaysSales != null">
        out_store_thirty_days_sales,
      </if>
      <if test="inStoreThirtyDaysSaleTimes != null">
        in_store_thirty_days_sale_times,
      </if>
      <if test="outStoreThirtyDaysSaleTimes != null">
        out_store_thirty_days_sale_times,
      </if>
      <if test="inStoreStock != null">
        in_store_stock,
      </if>
      <if test="outStoreStock != null">
        out_store_stock,
      </if>
      <if test="inStoreStockMin != null">
        in_store_stock_min,
      </if>
      <if test="outStoreStockMin != null">
        out_store_stock_min,
      </if>
      <if test="inStoreStockMax != null">
        in_store_stock_max,
      </if>
      <if test="outStoreStockMax != null">
        out_store_stock_max,
      </if>
      <if test="inStoreStockOnway != null">
        in_store_stock_onway,
      </if>
      <if test="outStoreStockOnway != null">
        out_store_stock_onway,
      </if>
      <if test="inStorePosDailySales != null">
        in_store_pos_daily_sales,
      </if>
      <if test="outStorePosDailySales != null">
        out_store_pos_daily_sales,
      </if>
      <if test="transferCostAmount != null">
        transfer_cost_amount,
      </if>
      <if test="modelCode != null">
        model_code,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedName != null">
        updated_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="registerNo != null">
        #{registerNo,jdbcType=VARCHAR},
      </if>
      <if test="allotType != null">
        #{allotType,jdbcType=TINYINT},
      </if>
      <if test="registerSource != null">
        #{registerSource,jdbcType=TINYINT},
      </if>
      <if test="businessDate != null">
        #{businessDate,jdbcType=TIMESTAMP},
      </if>
      <if test="allotNo != null">
        #{allotNo,jdbcType=VARCHAR},
      </if>
      <if test="goodsNo != null">
        #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="batchNo != null">
        #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="outCompanyCode != null">
        #{outCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="inCompanyCode != null">
        #{inCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="outStoreCode != null">
        #{outStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="inStoreCode != null">
        #{inStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="suggestAllotQuantity != null">
        #{suggestAllotQuantity,jdbcType=DECIMAL},
      </if>
      <if test="inStoreThirtyDaysSales != null">
        #{inStoreThirtyDaysSales,jdbcType=DECIMAL},
      </if>
      <if test="outStoreThirtyDaysSales != null">
        #{outStoreThirtyDaysSales,jdbcType=DECIMAL},
      </if>
      <if test="inStoreThirtyDaysSaleTimes != null">
        #{inStoreThirtyDaysSaleTimes,jdbcType=DECIMAL},
      </if>
      <if test="outStoreThirtyDaysSaleTimes != null">
        #{outStoreThirtyDaysSaleTimes,jdbcType=DECIMAL},
      </if>
      <if test="inStoreStock != null">
        #{inStoreStock,jdbcType=DECIMAL},
      </if>
      <if test="outStoreStock != null">
        #{outStoreStock,jdbcType=DECIMAL},
      </if>
      <if test="inStoreStockMin != null">
        #{inStoreStockMin,jdbcType=DECIMAL},
      </if>
      <if test="outStoreStockMin != null">
        #{outStoreStockMin,jdbcType=DECIMAL},
      </if>
      <if test="inStoreStockMax != null">
        #{inStoreStockMax,jdbcType=DECIMAL},
      </if>
      <if test="outStoreStockMax != null">
        #{outStoreStockMax,jdbcType=DECIMAL},
      </if>
      <if test="inStoreStockOnway != null">
        #{inStoreStockOnway,jdbcType=DECIMAL},
      </if>
      <if test="outStoreStockOnway != null">
        #{outStoreStockOnway,jdbcType=DECIMAL},
      </if>
      <if test="inStorePosDailySales != null">
        #{inStorePosDailySales,jdbcType=DECIMAL},
      </if>
      <if test="outStorePosDailySales != null">
        #{outStorePosDailySales,jdbcType=DECIMAL},
      </if>
      <if test="transferCostAmount != null">
        #{transferCostAmount,jdbcType=DECIMAL},
      </if>
      <if test="modelCode != null">
        #{modelCode,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        #{updatedName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.iscm.entity.IscmSuggestAllotOriginDataExample" resultType="java.lang.Long">
    select count(*) from iscm_suggest_allot_origin_data_temp
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update iscm_suggest_allot_origin_data_temp
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.registerNo != null">
        register_no = #{record.registerNo,jdbcType=VARCHAR},
      </if>
      <if test="record.allotType != null">
        allot_type = #{record.allotType,jdbcType=TINYINT},
      </if>
      <if test="record.registerSource != null">
        register_source = #{record.registerSource,jdbcType=TINYINT},
      </if>
      <if test="record.businessDate != null">
        business_date = #{record.businessDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.allotNo != null">
        allot_no = #{record.allotNo,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsNo != null">
        goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="record.batchNo != null">
        batch_no = #{record.batchNo,jdbcType=VARCHAR},
      </if>
      <if test="record.outCompanyCode != null">
        out_company_code = #{record.outCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.inCompanyCode != null">
        in_company_code = #{record.inCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.outStoreCode != null">
        out_store_code = #{record.outStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="record.inStoreCode != null">
        in_store_code = #{record.inStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="record.suggestAllotQuantity != null">
        suggest_allot_quantity = #{record.suggestAllotQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.inStoreThirtyDaysSales != null">
        in_store_thirty_days_sales = #{record.inStoreThirtyDaysSales,jdbcType=DECIMAL},
      </if>
      <if test="record.outStoreThirtyDaysSales != null">
        out_store_thirty_days_sales = #{record.outStoreThirtyDaysSales,jdbcType=DECIMAL},
      </if>
      <if test="record.inStoreThirtyDaysSaleTimes != null">
        in_store_thirty_days_sale_times = #{record.inStoreThirtyDaysSaleTimes,jdbcType=DECIMAL},
      </if>
      <if test="record.outStoreThirtyDaysSaleTimes != null">
        out_store_thirty_days_sale_times = #{record.outStoreThirtyDaysSaleTimes,jdbcType=DECIMAL},
      </if>
      <if test="record.inStoreStock != null">
        in_store_stock = #{record.inStoreStock,jdbcType=DECIMAL},
      </if>
      <if test="record.outStoreStock != null">
        out_store_stock = #{record.outStoreStock,jdbcType=DECIMAL},
      </if>
      <if test="record.inStoreStockMin != null">
        in_store_stock_min = #{record.inStoreStockMin,jdbcType=DECIMAL},
      </if>
      <if test="record.outStoreStockMin != null">
        out_store_stock_min = #{record.outStoreStockMin,jdbcType=DECIMAL},
      </if>
      <if test="record.inStoreStockMax != null">
        in_store_stock_max = #{record.inStoreStockMax,jdbcType=DECIMAL},
      </if>
      <if test="record.outStoreStockMax != null">
        out_store_stock_max = #{record.outStoreStockMax,jdbcType=DECIMAL},
      </if>
      <if test="record.inStoreStockOnway != null">
        in_store_stock_onway = #{record.inStoreStockOnway,jdbcType=DECIMAL},
      </if>
      <if test="record.outStoreStockOnway != null">
        out_store_stock_onway = #{record.outStoreStockOnway,jdbcType=DECIMAL},
      </if>
      <if test="record.inStorePosDailySales != null">
        in_store_pos_daily_sales = #{record.inStorePosDailySales,jdbcType=DECIMAL},
      </if>
      <if test="record.outStorePosDailySales != null">
        out_store_pos_daily_sales = #{record.outStorePosDailySales,jdbcType=DECIMAL},
      </if>
      <if test="record.transferCostAmount != null">
        transfer_cost_amount = #{record.transferCostAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.modelCode != null">
        model_code = #{record.modelCode,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedName != null">
        updated_name = #{record.updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update iscm_suggest_allot_origin_data_temp
    set id = #{record.id,jdbcType=BIGINT},
      register_no = #{record.registerNo,jdbcType=VARCHAR},
      allot_type = #{record.allotType,jdbcType=TINYINT},
      register_source = #{record.registerSource,jdbcType=TINYINT},
      business_date = #{record.businessDate,jdbcType=TIMESTAMP},
      allot_no = #{record.allotNo,jdbcType=VARCHAR},
      goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      batch_no = #{record.batchNo,jdbcType=VARCHAR},
      out_company_code = #{record.outCompanyCode,jdbcType=VARCHAR},
      in_company_code = #{record.inCompanyCode,jdbcType=VARCHAR},
      out_store_code = #{record.outStoreCode,jdbcType=VARCHAR},
      in_store_code = #{record.inStoreCode,jdbcType=VARCHAR},
      suggest_allot_quantity = #{record.suggestAllotQuantity,jdbcType=DECIMAL},
      in_store_thirty_days_sales = #{record.inStoreThirtyDaysSales,jdbcType=DECIMAL},
      out_store_thirty_days_sales = #{record.outStoreThirtyDaysSales,jdbcType=DECIMAL},
      in_store_thirty_days_sale_times = #{record.inStoreThirtyDaysSaleTimes,jdbcType=DECIMAL},
      out_store_thirty_days_sale_times = #{record.outStoreThirtyDaysSaleTimes,jdbcType=DECIMAL},
      in_store_stock = #{record.inStoreStock,jdbcType=DECIMAL},
      out_store_stock = #{record.outStoreStock,jdbcType=DECIMAL},
      in_store_stock_min = #{record.inStoreStockMin,jdbcType=DECIMAL},
      out_store_stock_min = #{record.outStoreStockMin,jdbcType=DECIMAL},
      in_store_stock_max = #{record.inStoreStockMax,jdbcType=DECIMAL},
      out_store_stock_max = #{record.outStoreStockMax,jdbcType=DECIMAL},
      in_store_stock_onway = #{record.inStoreStockOnway,jdbcType=DECIMAL},
      out_store_stock_onway = #{record.outStoreStockOnway,jdbcType=DECIMAL},
      in_store_pos_daily_sales = #{record.inStorePosDailySales,jdbcType=DECIMAL},
      out_store_pos_daily_sales = #{record.outStorePosDailySales,jdbcType=DECIMAL},
      transfer_cost_amount = #{record.transferCostAmount,jdbcType=DECIMAL},
      model_code = #{record.modelCode,jdbcType=VARCHAR},
      `status` = #{record.status,jdbcType=TINYINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_name = #{record.createdName,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_name = #{record.updatedName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.iscm.entity.IscmSuggestAllotOriginData">
    update iscm_suggest_allot_origin_data_temp
    <set>
      <if test="registerNo != null">
        register_no = #{registerNo,jdbcType=VARCHAR},
      </if>
      <if test="allotType != null">
        allot_type = #{allotType,jdbcType=TINYINT},
      </if>
      <if test="registerSource != null">
        register_source = #{registerSource,jdbcType=TINYINT},
      </if>
      <if test="businessDate != null">
        business_date = #{businessDate,jdbcType=TIMESTAMP},
      </if>
      <if test="allotNo != null">
        allot_no = #{allotNo,jdbcType=VARCHAR},
      </if>
      <if test="goodsNo != null">
        goods_no = #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="batchNo != null">
        batch_no = #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="outCompanyCode != null">
        out_company_code = #{outCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="inCompanyCode != null">
        in_company_code = #{inCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="outStoreCode != null">
        out_store_code = #{outStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="inStoreCode != null">
        in_store_code = #{inStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="suggestAllotQuantity != null">
        suggest_allot_quantity = #{suggestAllotQuantity,jdbcType=DECIMAL},
      </if>
      <if test="inStoreThirtyDaysSales != null">
        in_store_thirty_days_sales = #{inStoreThirtyDaysSales,jdbcType=DECIMAL},
      </if>
      <if test="outStoreThirtyDaysSales != null">
        out_store_thirty_days_sales = #{outStoreThirtyDaysSales,jdbcType=DECIMAL},
      </if>
      <if test="inStoreThirtyDaysSaleTimes != null">
        in_store_thirty_days_sale_times = #{inStoreThirtyDaysSaleTimes,jdbcType=DECIMAL},
      </if>
      <if test="outStoreThirtyDaysSaleTimes != null">
        out_store_thirty_days_sale_times = #{outStoreThirtyDaysSaleTimes,jdbcType=DECIMAL},
      </if>
      <if test="inStoreStock != null">
        in_store_stock = #{inStoreStock,jdbcType=DECIMAL},
      </if>
      <if test="outStoreStock != null">
        out_store_stock = #{outStoreStock,jdbcType=DECIMAL},
      </if>
      <if test="inStoreStockMin != null">
        in_store_stock_min = #{inStoreStockMin,jdbcType=DECIMAL},
      </if>
      <if test="outStoreStockMin != null">
        out_store_stock_min = #{outStoreStockMin,jdbcType=DECIMAL},
      </if>
      <if test="inStoreStockMax != null">
        in_store_stock_max = #{inStoreStockMax,jdbcType=DECIMAL},
      </if>
      <if test="outStoreStockMax != null">
        out_store_stock_max = #{outStoreStockMax,jdbcType=DECIMAL},
      </if>
      <if test="inStoreStockOnway != null">
        in_store_stock_onway = #{inStoreStockOnway,jdbcType=DECIMAL},
      </if>
      <if test="outStoreStockOnway != null">
        out_store_stock_onway = #{outStoreStockOnway,jdbcType=DECIMAL},
      </if>
      <if test="inStorePosDailySales != null">
        in_store_pos_daily_sales = #{inStorePosDailySales,jdbcType=DECIMAL},
      </if>
      <if test="outStorePosDailySales != null">
        out_store_pos_daily_sales = #{outStorePosDailySales,jdbcType=DECIMAL},
      </if>
      <if test="transferCostAmount != null">
        transfer_cost_amount = #{transferCostAmount,jdbcType=DECIMAL},
      </if>
      <if test="modelCode != null">
        model_code = #{modelCode,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        updated_name = #{updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.iscm.entity.IscmSuggestAllotOriginData">
    update iscm_suggest_allot_origin_data_temp
    set register_no = #{registerNo,jdbcType=VARCHAR},
      allot_type = #{allotType,jdbcType=TINYINT},
      register_source = #{registerSource,jdbcType=TINYINT},
      business_date = #{businessDate,jdbcType=TIMESTAMP},
      allot_no = #{allotNo,jdbcType=VARCHAR},
      goods_no = #{goodsNo,jdbcType=VARCHAR},
      batch_no = #{batchNo,jdbcType=VARCHAR},
      out_company_code = #{outCompanyCode,jdbcType=VARCHAR},
      in_company_code = #{inCompanyCode,jdbcType=VARCHAR},
      out_store_code = #{outStoreCode,jdbcType=VARCHAR},
      in_store_code = #{inStoreCode,jdbcType=VARCHAR},
      suggest_allot_quantity = #{suggestAllotQuantity,jdbcType=DECIMAL},
      in_store_thirty_days_sales = #{inStoreThirtyDaysSales,jdbcType=DECIMAL},
      out_store_thirty_days_sales = #{outStoreThirtyDaysSales,jdbcType=DECIMAL},
      in_store_thirty_days_sale_times = #{inStoreThirtyDaysSaleTimes,jdbcType=DECIMAL},
      out_store_thirty_days_sale_times = #{outStoreThirtyDaysSaleTimes,jdbcType=DECIMAL},
      in_store_stock = #{inStoreStock,jdbcType=DECIMAL},
      out_store_stock = #{outStoreStock,jdbcType=DECIMAL},
      in_store_stock_min = #{inStoreStockMin,jdbcType=DECIMAL},
      out_store_stock_min = #{outStoreStockMin,jdbcType=DECIMAL},
      in_store_stock_max = #{inStoreStockMax,jdbcType=DECIMAL},
      out_store_stock_max = #{outStoreStockMax,jdbcType=DECIMAL},
      in_store_stock_onway = #{inStoreStockOnway,jdbcType=DECIMAL},
      out_store_stock_onway = #{outStoreStockOnway,jdbcType=DECIMAL},
      in_store_pos_daily_sales = #{inStorePosDailySales,jdbcType=DECIMAL},
      out_store_pos_daily_sales = #{outStorePosDailySales,jdbcType=DECIMAL},
      transfer_cost_amount = #{transferCostAmount,jdbcType=DECIMAL},
      model_code = #{modelCode,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_name = #{createdName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_name = #{updatedName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>