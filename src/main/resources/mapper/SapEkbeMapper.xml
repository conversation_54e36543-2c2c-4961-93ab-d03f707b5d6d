<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.SapEkbeMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.SapEkbe">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="MANDT" jdbcType="VARCHAR" property="mandt" />
    <result column="EBELN" jdbcType="VARCHAR" property="ebeln" />
    <result column="EBELP" jdbcType="VARCHAR" property="ebelp" />
    <result column="ZEKKN" jdbcType="VARCHAR" property="zekkn" />
    <result column="VGABE" jdbcType="VARCHAR" property="vgabe" />
    <result column="GJAHR" jdbcType="VARCHAR" property="gjahr" />
    <result column="BELNR" jdbcType="VARCHAR" property="belnr" />
    <result column="BUZEI" jdbcType="VARCHAR" property="buzei" />
    <result column="BEWTP" jdbcType="VARCHAR" property="bewtp" />
    <result column="BWART" jdbcType="VARCHAR" property="bwart" />
    <result column="BUDAT" jdbcType="VARCHAR" property="budat" />
    <result column="MENGE" jdbcType="DECIMAL" property="menge" />
    <result column="BPMNG" jdbcType="DECIMAL" property="bpmng" />
    <result column="DMBTR" jdbcType="DECIMAL" property="dmbtr" />
    <result column="WRBTR" jdbcType="DECIMAL" property="wrbtr" />
    <result column="WAERS" jdbcType="VARCHAR" property="waers" />
    <result column="AREWR" jdbcType="DECIMAL" property="arewr" />
    <result column="WESBS" jdbcType="DECIMAL" property="wesbs" />
    <result column="BPWES" jdbcType="DECIMAL" property="bpwes" />
    <result column="SHKZG" jdbcType="VARCHAR" property="shkzg" />
    <result column="BWTAR" jdbcType="VARCHAR" property="bwtar" />
    <result column="ELIKZ" jdbcType="VARCHAR" property="elikz" />
    <result column="XBLNR" jdbcType="VARCHAR" property="xblnr" />
    <result column="LFGJA" jdbcType="VARCHAR" property="lfgja" />
    <result column="LFBNR" jdbcType="VARCHAR" property="lfbnr" />
    <result column="LFPOS" jdbcType="VARCHAR" property="lfpos" />
    <result column="GRUND" jdbcType="VARCHAR" property="grund" />
    <result column="CPUDT" jdbcType="VARCHAR" property="cpudt" />
    <result column="CPUTM" jdbcType="VARCHAR" property="cputm" />
    <result column="REEWR" jdbcType="DECIMAL" property="reewr" />
    <result column="EVERE" jdbcType="VARCHAR" property="evere" />
    <result column="REFWR" jdbcType="DECIMAL" property="refwr" />
    <result column="MATNR" jdbcType="VARCHAR" property="matnr" />
    <result column="WERKS" jdbcType="VARCHAR" property="werks" />
    <result column="XWSBR" jdbcType="VARCHAR" property="xwsbr" />
    <result column="ETENS" jdbcType="VARCHAR" property="etens" />
    <result column="KNUMV" jdbcType="VARCHAR" property="knumv" />
    <result column="MWSKZ" jdbcType="VARCHAR" property="mwskz" />
    <result column="LSMNG" jdbcType="DECIMAL" property="lsmng" />
    <result column="LSMEH" jdbcType="VARCHAR" property="lsmeh" />
    <result column="EMATN" jdbcType="VARCHAR" property="ematn" />
    <result column="AREWW" jdbcType="DECIMAL" property="areww" />
    <result column="HSWAE" jdbcType="VARCHAR" property="hswae" />
    <result column="BAMNG" jdbcType="DECIMAL" property="bamng" />
    <result column="CHARG" jdbcType="VARCHAR" property="charg" />
    <result column="BLDAT" jdbcType="VARCHAR" property="bldat" />
    <result column="XWOFF" jdbcType="VARCHAR" property="xwoff" />
    <result column="XUNPL" jdbcType="VARCHAR" property="xunpl" />
    <result column="ERNAM" jdbcType="VARCHAR" property="ernam" />
    <result column="SRVPOS" jdbcType="VARCHAR" property="srvpos" />
    <result column="PACKNO" jdbcType="VARCHAR" property="packno" />
    <result column="INTROW" jdbcType="VARCHAR" property="introw" />
    <result column="BEKKN" jdbcType="VARCHAR" property="bekkn" />
    <result column="LEMIN" jdbcType="VARCHAR" property="lemin" />
    <result column="AREWB" jdbcType="DECIMAL" property="arewb" />
    <result column="REWRB" jdbcType="DECIMAL" property="rewrb" />
    <result column="SAPRL" jdbcType="VARCHAR" property="saprl" />
    <result column="MENGE_POP" jdbcType="DECIMAL" property="mengePop" />
    <result column="BPMNG_POP" jdbcType="DECIMAL" property="bpmngPop" />
    <result column="DMBTR_POP" jdbcType="DECIMAL" property="dmbtrPop" />
    <result column="WRBTR_POP" jdbcType="DECIMAL" property="wrbtrPop" />
    <result column="WESBB" jdbcType="DECIMAL" property="wesbb" />
    <result column="BPWEB" jdbcType="DECIMAL" property="bpweb" />
    <result column="WEORA" jdbcType="VARCHAR" property="weora" />
    <result column="AREWR_POP" jdbcType="DECIMAL" property="arewrPop" />
    <result column="KUDIF" jdbcType="DECIMAL" property="kudif" />
    <result column="RETAMT_FC" jdbcType="DECIMAL" property="retamtFc" />
    <result column="RETAMT_LC" jdbcType="DECIMAL" property="retamtLc" />
    <result column="RETAMTP_FC" jdbcType="DECIMAL" property="retamtpFc" />
    <result column="RETAMTP_LC" jdbcType="DECIMAL" property="retamtpLc" />
    <result column="XMACC" jdbcType="VARCHAR" property="xmacc" />
    <result column="WKURS" jdbcType="DECIMAL" property="wkurs" />
    <result column="INV_ITEM_ORIGIN" jdbcType="VARCHAR" property="invItemOrigin" />
    <result column="VBELN_ST" jdbcType="VARCHAR" property="vbelnSt" />
    <result column="VBELP_ST" jdbcType="VARCHAR" property="vbelpSt" />
    <result column="SGT_SCAT" jdbcType="VARCHAR" property="sgtScat" />
    <result column="DATAAGING" jdbcType="VARCHAR" property="dataaging" />
    <result column="SESUOM" jdbcType="VARCHAR" property="sesuom" />
    <result column="ET_UPD" jdbcType="VARCHAR" property="etUpd" />
    <result column="CWM_BAMNG" jdbcType="DECIMAL" property="cwmBamng" />
    <result column="CWM_WESBS" jdbcType="DECIMAL" property="cwmWesbs" />
    <result column="CWM_TY2TQ" jdbcType="VARCHAR" property="cwmTy2tq" />
    <result column="CWM_WESBB" jdbcType="DECIMAL" property="cwmWesbb" />
    <result column="J_SC_DIE_COMP_F" jdbcType="VARCHAR" property="jScDieCompF" />
    <result column="FSH_SEASON_YEAR" jdbcType="VARCHAR" property="fshSeasonYear" />
    <result column="FSH_SEASON" jdbcType="VARCHAR" property="fshSeason" />
    <result column="FSH_COLLECTION" jdbcType="VARCHAR" property="fshCollection" />
    <result column="FSH_THEME" jdbcType="VARCHAR" property="fshTheme" />
    <result column="QTY_DIFF" jdbcType="DECIMAL" property="qtyDiff" />
    <result column="WRF_CHARSTC1" jdbcType="VARCHAR" property="wrfCharstc1" />
    <result column="WRF_CHARSTC2" jdbcType="VARCHAR" property="wrfCharstc2" />
    <result column="WRF_CHARSTC3" jdbcType="VARCHAR" property="wrfCharstc3" />
    <result column="ZZDATE" jdbcType="VARCHAR" property="zzdate" />
    <result column="ZZTIME" jdbcType="VARCHAR" property="zztime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, MANDT, EBELN, EBELP, ZEKKN, VGABE, GJAHR, BELNR, BUZEI, BEWTP, BWART, BUDAT, 
    MENGE, BPMNG, DMBTR, WRBTR, WAERS, AREWR, WESBS, BPWES, SHKZG, BWTAR, ELIKZ, XBLNR, 
    LFGJA, LFBNR, LFPOS, GRUND, CPUDT, CPUTM, REEWR, EVERE, REFWR, MATNR, WERKS, XWSBR, 
    ETENS, KNUMV, MWSKZ, LSMNG, LSMEH, EMATN, AREWW, HSWAE, BAMNG, CHARG, BLDAT, XWOFF, 
    XUNPL, ERNAM, SRVPOS, PACKNO, INTROW, BEKKN, LEMIN, AREWB, REWRB, SAPRL, MENGE_POP, 
    BPMNG_POP, DMBTR_POP, WRBTR_POP, WESBB, BPWEB, WEORA, AREWR_POP, KUDIF, RETAMT_FC, 
    RETAMT_LC, RETAMTP_FC, RETAMTP_LC, XMACC, WKURS, INV_ITEM_ORIGIN, VBELN_ST, VBELP_ST, 
    SGT_SCAT, DATAAGING, SESUOM, ET_UPD, CWM_BAMNG, CWM_WESBS, CWM_TY2TQ, CWM_WESBB, 
    J_SC_DIE_COMP_F, FSH_SEASON_YEAR, FSH_SEASON, FSH_COLLECTION, FSH_THEME, QTY_DIFF, 
    WRF_CHARSTC1, WRF_CHARSTC2, WRF_CHARSTC3, ZZDATE, ZZTIME
  </sql>
  <select id="selectByExample" parameterType="com.cowell.iscm.entity.SapEkbeExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from SAP_EKBE
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from SAP_EKBE
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from SAP_EKBE
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.iscm.entity.SapEkbeExample">
    delete from SAP_EKBE
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entity.SapEkbe" useGeneratedKeys="true">
    insert into SAP_EKBE (MANDT, EBELN, EBELP, 
      ZEKKN, VGABE, GJAHR, 
      BELNR, BUZEI, BEWTP, 
      BWART, BUDAT, MENGE, 
      BPMNG, DMBTR, WRBTR, 
      WAERS, AREWR, WESBS, 
      BPWES, SHKZG, BWTAR, 
      ELIKZ, XBLNR, LFGJA, 
      LFBNR, LFPOS, GRUND, 
      CPUDT, CPUTM, REEWR, 
      EVERE, REFWR, MATNR, 
      WERKS, XWSBR, ETENS, 
      KNUMV, MWSKZ, LSMNG, 
      LSMEH, EMATN, AREWW, 
      HSWAE, BAMNG, CHARG, 
      BLDAT, XWOFF, XUNPL, 
      ERNAM, SRVPOS, PACKNO, 
      INTROW, BEKKN, LEMIN, 
      AREWB, REWRB, SAPRL, 
      MENGE_POP, BPMNG_POP, DMBTR_POP, 
      WRBTR_POP, WESBB, BPWEB, 
      WEORA, AREWR_POP, KUDIF, 
      RETAMT_FC, RETAMT_LC, RETAMTP_FC, 
      RETAMTP_LC, XMACC, WKURS, 
      INV_ITEM_ORIGIN, VBELN_ST, VBELP_ST, 
      SGT_SCAT, DATAAGING, SESUOM, 
      ET_UPD, CWM_BAMNG, CWM_WESBS, 
      CWM_TY2TQ, CWM_WESBB, J_SC_DIE_COMP_F, 
      FSH_SEASON_YEAR, FSH_SEASON, FSH_COLLECTION, 
      FSH_THEME, QTY_DIFF, WRF_CHARSTC1, 
      WRF_CHARSTC2, WRF_CHARSTC3, ZZDATE, 
      ZZTIME)
    values (#{mandt,jdbcType=VARCHAR}, #{ebeln,jdbcType=VARCHAR}, #{ebelp,jdbcType=VARCHAR}, 
      #{zekkn,jdbcType=VARCHAR}, #{vgabe,jdbcType=VARCHAR}, #{gjahr,jdbcType=VARCHAR}, 
      #{belnr,jdbcType=VARCHAR}, #{buzei,jdbcType=VARCHAR}, #{bewtp,jdbcType=VARCHAR}, 
      #{bwart,jdbcType=VARCHAR}, #{budat,jdbcType=VARCHAR}, #{menge,jdbcType=DECIMAL}, 
      #{bpmng,jdbcType=DECIMAL}, #{dmbtr,jdbcType=DECIMAL}, #{wrbtr,jdbcType=DECIMAL}, 
      #{waers,jdbcType=VARCHAR}, #{arewr,jdbcType=DECIMAL}, #{wesbs,jdbcType=DECIMAL}, 
      #{bpwes,jdbcType=DECIMAL}, #{shkzg,jdbcType=VARCHAR}, #{bwtar,jdbcType=VARCHAR}, 
      #{elikz,jdbcType=VARCHAR}, #{xblnr,jdbcType=VARCHAR}, #{lfgja,jdbcType=VARCHAR}, 
      #{lfbnr,jdbcType=VARCHAR}, #{lfpos,jdbcType=VARCHAR}, #{grund,jdbcType=VARCHAR}, 
      #{cpudt,jdbcType=VARCHAR}, #{cputm,jdbcType=VARCHAR}, #{reewr,jdbcType=DECIMAL}, 
      #{evere,jdbcType=VARCHAR}, #{refwr,jdbcType=DECIMAL}, #{matnr,jdbcType=VARCHAR}, 
      #{werks,jdbcType=VARCHAR}, #{xwsbr,jdbcType=VARCHAR}, #{etens,jdbcType=VARCHAR}, 
      #{knumv,jdbcType=VARCHAR}, #{mwskz,jdbcType=VARCHAR}, #{lsmng,jdbcType=DECIMAL}, 
      #{lsmeh,jdbcType=VARCHAR}, #{ematn,jdbcType=VARCHAR}, #{areww,jdbcType=DECIMAL}, 
      #{hswae,jdbcType=VARCHAR}, #{bamng,jdbcType=DECIMAL}, #{charg,jdbcType=VARCHAR}, 
      #{bldat,jdbcType=VARCHAR}, #{xwoff,jdbcType=VARCHAR}, #{xunpl,jdbcType=VARCHAR}, 
      #{ernam,jdbcType=VARCHAR}, #{srvpos,jdbcType=VARCHAR}, #{packno,jdbcType=VARCHAR}, 
      #{introw,jdbcType=VARCHAR}, #{bekkn,jdbcType=VARCHAR}, #{lemin,jdbcType=VARCHAR}, 
      #{arewb,jdbcType=DECIMAL}, #{rewrb,jdbcType=DECIMAL}, #{saprl,jdbcType=VARCHAR}, 
      #{mengePop,jdbcType=DECIMAL}, #{bpmngPop,jdbcType=DECIMAL}, #{dmbtrPop,jdbcType=DECIMAL}, 
      #{wrbtrPop,jdbcType=DECIMAL}, #{wesbb,jdbcType=DECIMAL}, #{bpweb,jdbcType=DECIMAL}, 
      #{weora,jdbcType=VARCHAR}, #{arewrPop,jdbcType=DECIMAL}, #{kudif,jdbcType=DECIMAL}, 
      #{retamtFc,jdbcType=DECIMAL}, #{retamtLc,jdbcType=DECIMAL}, #{retamtpFc,jdbcType=DECIMAL}, 
      #{retamtpLc,jdbcType=DECIMAL}, #{xmacc,jdbcType=VARCHAR}, #{wkurs,jdbcType=DECIMAL}, 
      #{invItemOrigin,jdbcType=VARCHAR}, #{vbelnSt,jdbcType=VARCHAR}, #{vbelpSt,jdbcType=VARCHAR}, 
      #{sgtScat,jdbcType=VARCHAR}, #{dataaging,jdbcType=VARCHAR}, #{sesuom,jdbcType=VARCHAR}, 
      #{etUpd,jdbcType=VARCHAR}, #{cwmBamng,jdbcType=DECIMAL}, #{cwmWesbs,jdbcType=DECIMAL}, 
      #{cwmTy2tq,jdbcType=VARCHAR}, #{cwmWesbb,jdbcType=DECIMAL}, #{jScDieCompF,jdbcType=VARCHAR}, 
      #{fshSeasonYear,jdbcType=VARCHAR}, #{fshSeason,jdbcType=VARCHAR}, #{fshCollection,jdbcType=VARCHAR}, 
      #{fshTheme,jdbcType=VARCHAR}, #{qtyDiff,jdbcType=DECIMAL}, #{wrfCharstc1,jdbcType=VARCHAR}, 
      #{wrfCharstc2,jdbcType=VARCHAR}, #{wrfCharstc3,jdbcType=VARCHAR}, #{zzdate,jdbcType=VARCHAR}, 
      #{zztime,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entity.SapEkbe" useGeneratedKeys="true">
    insert into SAP_EKBE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="mandt != null">
        MANDT,
      </if>
      <if test="ebeln != null">
        EBELN,
      </if>
      <if test="ebelp != null">
        EBELP,
      </if>
      <if test="zekkn != null">
        ZEKKN,
      </if>
      <if test="vgabe != null">
        VGABE,
      </if>
      <if test="gjahr != null">
        GJAHR,
      </if>
      <if test="belnr != null">
        BELNR,
      </if>
      <if test="buzei != null">
        BUZEI,
      </if>
      <if test="bewtp != null">
        BEWTP,
      </if>
      <if test="bwart != null">
        BWART,
      </if>
      <if test="budat != null">
        BUDAT,
      </if>
      <if test="menge != null">
        MENGE,
      </if>
      <if test="bpmng != null">
        BPMNG,
      </if>
      <if test="dmbtr != null">
        DMBTR,
      </if>
      <if test="wrbtr != null">
        WRBTR,
      </if>
      <if test="waers != null">
        WAERS,
      </if>
      <if test="arewr != null">
        AREWR,
      </if>
      <if test="wesbs != null">
        WESBS,
      </if>
      <if test="bpwes != null">
        BPWES,
      </if>
      <if test="shkzg != null">
        SHKZG,
      </if>
      <if test="bwtar != null">
        BWTAR,
      </if>
      <if test="elikz != null">
        ELIKZ,
      </if>
      <if test="xblnr != null">
        XBLNR,
      </if>
      <if test="lfgja != null">
        LFGJA,
      </if>
      <if test="lfbnr != null">
        LFBNR,
      </if>
      <if test="lfpos != null">
        LFPOS,
      </if>
      <if test="grund != null">
        GRUND,
      </if>
      <if test="cpudt != null">
        CPUDT,
      </if>
      <if test="cputm != null">
        CPUTM,
      </if>
      <if test="reewr != null">
        REEWR,
      </if>
      <if test="evere != null">
        EVERE,
      </if>
      <if test="refwr != null">
        REFWR,
      </if>
      <if test="matnr != null">
        MATNR,
      </if>
      <if test="werks != null">
        WERKS,
      </if>
      <if test="xwsbr != null">
        XWSBR,
      </if>
      <if test="etens != null">
        ETENS,
      </if>
      <if test="knumv != null">
        KNUMV,
      </if>
      <if test="mwskz != null">
        MWSKZ,
      </if>
      <if test="lsmng != null">
        LSMNG,
      </if>
      <if test="lsmeh != null">
        LSMEH,
      </if>
      <if test="ematn != null">
        EMATN,
      </if>
      <if test="areww != null">
        AREWW,
      </if>
      <if test="hswae != null">
        HSWAE,
      </if>
      <if test="bamng != null">
        BAMNG,
      </if>
      <if test="charg != null">
        CHARG,
      </if>
      <if test="bldat != null">
        BLDAT,
      </if>
      <if test="xwoff != null">
        XWOFF,
      </if>
      <if test="xunpl != null">
        XUNPL,
      </if>
      <if test="ernam != null">
        ERNAM,
      </if>
      <if test="srvpos != null">
        SRVPOS,
      </if>
      <if test="packno != null">
        PACKNO,
      </if>
      <if test="introw != null">
        INTROW,
      </if>
      <if test="bekkn != null">
        BEKKN,
      </if>
      <if test="lemin != null">
        LEMIN,
      </if>
      <if test="arewb != null">
        AREWB,
      </if>
      <if test="rewrb != null">
        REWRB,
      </if>
      <if test="saprl != null">
        SAPRL,
      </if>
      <if test="mengePop != null">
        MENGE_POP,
      </if>
      <if test="bpmngPop != null">
        BPMNG_POP,
      </if>
      <if test="dmbtrPop != null">
        DMBTR_POP,
      </if>
      <if test="wrbtrPop != null">
        WRBTR_POP,
      </if>
      <if test="wesbb != null">
        WESBB,
      </if>
      <if test="bpweb != null">
        BPWEB,
      </if>
      <if test="weora != null">
        WEORA,
      </if>
      <if test="arewrPop != null">
        AREWR_POP,
      </if>
      <if test="kudif != null">
        KUDIF,
      </if>
      <if test="retamtFc != null">
        RETAMT_FC,
      </if>
      <if test="retamtLc != null">
        RETAMT_LC,
      </if>
      <if test="retamtpFc != null">
        RETAMTP_FC,
      </if>
      <if test="retamtpLc != null">
        RETAMTP_LC,
      </if>
      <if test="xmacc != null">
        XMACC,
      </if>
      <if test="wkurs != null">
        WKURS,
      </if>
      <if test="invItemOrigin != null">
        INV_ITEM_ORIGIN,
      </if>
      <if test="vbelnSt != null">
        VBELN_ST,
      </if>
      <if test="vbelpSt != null">
        VBELP_ST,
      </if>
      <if test="sgtScat != null">
        SGT_SCAT,
      </if>
      <if test="dataaging != null">
        DATAAGING,
      </if>
      <if test="sesuom != null">
        SESUOM,
      </if>
      <if test="etUpd != null">
        ET_UPD,
      </if>
      <if test="cwmBamng != null">
        CWM_BAMNG,
      </if>
      <if test="cwmWesbs != null">
        CWM_WESBS,
      </if>
      <if test="cwmTy2tq != null">
        CWM_TY2TQ,
      </if>
      <if test="cwmWesbb != null">
        CWM_WESBB,
      </if>
      <if test="jScDieCompF != null">
        J_SC_DIE_COMP_F,
      </if>
      <if test="fshSeasonYear != null">
        FSH_SEASON_YEAR,
      </if>
      <if test="fshSeason != null">
        FSH_SEASON,
      </if>
      <if test="fshCollection != null">
        FSH_COLLECTION,
      </if>
      <if test="fshTheme != null">
        FSH_THEME,
      </if>
      <if test="qtyDiff != null">
        QTY_DIFF,
      </if>
      <if test="wrfCharstc1 != null">
        WRF_CHARSTC1,
      </if>
      <if test="wrfCharstc2 != null">
        WRF_CHARSTC2,
      </if>
      <if test="wrfCharstc3 != null">
        WRF_CHARSTC3,
      </if>
      <if test="zzdate != null">
        ZZDATE,
      </if>
      <if test="zztime != null">
        ZZTIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="mandt != null">
        #{mandt,jdbcType=VARCHAR},
      </if>
      <if test="ebeln != null">
        #{ebeln,jdbcType=VARCHAR},
      </if>
      <if test="ebelp != null">
        #{ebelp,jdbcType=VARCHAR},
      </if>
      <if test="zekkn != null">
        #{zekkn,jdbcType=VARCHAR},
      </if>
      <if test="vgabe != null">
        #{vgabe,jdbcType=VARCHAR},
      </if>
      <if test="gjahr != null">
        #{gjahr,jdbcType=VARCHAR},
      </if>
      <if test="belnr != null">
        #{belnr,jdbcType=VARCHAR},
      </if>
      <if test="buzei != null">
        #{buzei,jdbcType=VARCHAR},
      </if>
      <if test="bewtp != null">
        #{bewtp,jdbcType=VARCHAR},
      </if>
      <if test="bwart != null">
        #{bwart,jdbcType=VARCHAR},
      </if>
      <if test="budat != null">
        #{budat,jdbcType=VARCHAR},
      </if>
      <if test="menge != null">
        #{menge,jdbcType=DECIMAL},
      </if>
      <if test="bpmng != null">
        #{bpmng,jdbcType=DECIMAL},
      </if>
      <if test="dmbtr != null">
        #{dmbtr,jdbcType=DECIMAL},
      </if>
      <if test="wrbtr != null">
        #{wrbtr,jdbcType=DECIMAL},
      </if>
      <if test="waers != null">
        #{waers,jdbcType=VARCHAR},
      </if>
      <if test="arewr != null">
        #{arewr,jdbcType=DECIMAL},
      </if>
      <if test="wesbs != null">
        #{wesbs,jdbcType=DECIMAL},
      </if>
      <if test="bpwes != null">
        #{bpwes,jdbcType=DECIMAL},
      </if>
      <if test="shkzg != null">
        #{shkzg,jdbcType=VARCHAR},
      </if>
      <if test="bwtar != null">
        #{bwtar,jdbcType=VARCHAR},
      </if>
      <if test="elikz != null">
        #{elikz,jdbcType=VARCHAR},
      </if>
      <if test="xblnr != null">
        #{xblnr,jdbcType=VARCHAR},
      </if>
      <if test="lfgja != null">
        #{lfgja,jdbcType=VARCHAR},
      </if>
      <if test="lfbnr != null">
        #{lfbnr,jdbcType=VARCHAR},
      </if>
      <if test="lfpos != null">
        #{lfpos,jdbcType=VARCHAR},
      </if>
      <if test="grund != null">
        #{grund,jdbcType=VARCHAR},
      </if>
      <if test="cpudt != null">
        #{cpudt,jdbcType=VARCHAR},
      </if>
      <if test="cputm != null">
        #{cputm,jdbcType=VARCHAR},
      </if>
      <if test="reewr != null">
        #{reewr,jdbcType=DECIMAL},
      </if>
      <if test="evere != null">
        #{evere,jdbcType=VARCHAR},
      </if>
      <if test="refwr != null">
        #{refwr,jdbcType=DECIMAL},
      </if>
      <if test="matnr != null">
        #{matnr,jdbcType=VARCHAR},
      </if>
      <if test="werks != null">
        #{werks,jdbcType=VARCHAR},
      </if>
      <if test="xwsbr != null">
        #{xwsbr,jdbcType=VARCHAR},
      </if>
      <if test="etens != null">
        #{etens,jdbcType=VARCHAR},
      </if>
      <if test="knumv != null">
        #{knumv,jdbcType=VARCHAR},
      </if>
      <if test="mwskz != null">
        #{mwskz,jdbcType=VARCHAR},
      </if>
      <if test="lsmng != null">
        #{lsmng,jdbcType=DECIMAL},
      </if>
      <if test="lsmeh != null">
        #{lsmeh,jdbcType=VARCHAR},
      </if>
      <if test="ematn != null">
        #{ematn,jdbcType=VARCHAR},
      </if>
      <if test="areww != null">
        #{areww,jdbcType=DECIMAL},
      </if>
      <if test="hswae != null">
        #{hswae,jdbcType=VARCHAR},
      </if>
      <if test="bamng != null">
        #{bamng,jdbcType=DECIMAL},
      </if>
      <if test="charg != null">
        #{charg,jdbcType=VARCHAR},
      </if>
      <if test="bldat != null">
        #{bldat,jdbcType=VARCHAR},
      </if>
      <if test="xwoff != null">
        #{xwoff,jdbcType=VARCHAR},
      </if>
      <if test="xunpl != null">
        #{xunpl,jdbcType=VARCHAR},
      </if>
      <if test="ernam != null">
        #{ernam,jdbcType=VARCHAR},
      </if>
      <if test="srvpos != null">
        #{srvpos,jdbcType=VARCHAR},
      </if>
      <if test="packno != null">
        #{packno,jdbcType=VARCHAR},
      </if>
      <if test="introw != null">
        #{introw,jdbcType=VARCHAR},
      </if>
      <if test="bekkn != null">
        #{bekkn,jdbcType=VARCHAR},
      </if>
      <if test="lemin != null">
        #{lemin,jdbcType=VARCHAR},
      </if>
      <if test="arewb != null">
        #{arewb,jdbcType=DECIMAL},
      </if>
      <if test="rewrb != null">
        #{rewrb,jdbcType=DECIMAL},
      </if>
      <if test="saprl != null">
        #{saprl,jdbcType=VARCHAR},
      </if>
      <if test="mengePop != null">
        #{mengePop,jdbcType=DECIMAL},
      </if>
      <if test="bpmngPop != null">
        #{bpmngPop,jdbcType=DECIMAL},
      </if>
      <if test="dmbtrPop != null">
        #{dmbtrPop,jdbcType=DECIMAL},
      </if>
      <if test="wrbtrPop != null">
        #{wrbtrPop,jdbcType=DECIMAL},
      </if>
      <if test="wesbb != null">
        #{wesbb,jdbcType=DECIMAL},
      </if>
      <if test="bpweb != null">
        #{bpweb,jdbcType=DECIMAL},
      </if>
      <if test="weora != null">
        #{weora,jdbcType=VARCHAR},
      </if>
      <if test="arewrPop != null">
        #{arewrPop,jdbcType=DECIMAL},
      </if>
      <if test="kudif != null">
        #{kudif,jdbcType=DECIMAL},
      </if>
      <if test="retamtFc != null">
        #{retamtFc,jdbcType=DECIMAL},
      </if>
      <if test="retamtLc != null">
        #{retamtLc,jdbcType=DECIMAL},
      </if>
      <if test="retamtpFc != null">
        #{retamtpFc,jdbcType=DECIMAL},
      </if>
      <if test="retamtpLc != null">
        #{retamtpLc,jdbcType=DECIMAL},
      </if>
      <if test="xmacc != null">
        #{xmacc,jdbcType=VARCHAR},
      </if>
      <if test="wkurs != null">
        #{wkurs,jdbcType=DECIMAL},
      </if>
      <if test="invItemOrigin != null">
        #{invItemOrigin,jdbcType=VARCHAR},
      </if>
      <if test="vbelnSt != null">
        #{vbelnSt,jdbcType=VARCHAR},
      </if>
      <if test="vbelpSt != null">
        #{vbelpSt,jdbcType=VARCHAR},
      </if>
      <if test="sgtScat != null">
        #{sgtScat,jdbcType=VARCHAR},
      </if>
      <if test="dataaging != null">
        #{dataaging,jdbcType=VARCHAR},
      </if>
      <if test="sesuom != null">
        #{sesuom,jdbcType=VARCHAR},
      </if>
      <if test="etUpd != null">
        #{etUpd,jdbcType=VARCHAR},
      </if>
      <if test="cwmBamng != null">
        #{cwmBamng,jdbcType=DECIMAL},
      </if>
      <if test="cwmWesbs != null">
        #{cwmWesbs,jdbcType=DECIMAL},
      </if>
      <if test="cwmTy2tq != null">
        #{cwmTy2tq,jdbcType=VARCHAR},
      </if>
      <if test="cwmWesbb != null">
        #{cwmWesbb,jdbcType=DECIMAL},
      </if>
      <if test="jScDieCompF != null">
        #{jScDieCompF,jdbcType=VARCHAR},
      </if>
      <if test="fshSeasonYear != null">
        #{fshSeasonYear,jdbcType=VARCHAR},
      </if>
      <if test="fshSeason != null">
        #{fshSeason,jdbcType=VARCHAR},
      </if>
      <if test="fshCollection != null">
        #{fshCollection,jdbcType=VARCHAR},
      </if>
      <if test="fshTheme != null">
        #{fshTheme,jdbcType=VARCHAR},
      </if>
      <if test="qtyDiff != null">
        #{qtyDiff,jdbcType=DECIMAL},
      </if>
      <if test="wrfCharstc1 != null">
        #{wrfCharstc1,jdbcType=VARCHAR},
      </if>
      <if test="wrfCharstc2 != null">
        #{wrfCharstc2,jdbcType=VARCHAR},
      </if>
      <if test="wrfCharstc3 != null">
        #{wrfCharstc3,jdbcType=VARCHAR},
      </if>
      <if test="zzdate != null">
        #{zzdate,jdbcType=VARCHAR},
      </if>
      <if test="zztime != null">
        #{zztime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.iscm.entity.SapEkbeExample" resultType="java.lang.Long">
    select count(*) from SAP_EKBE
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update SAP_EKBE
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.mandt != null">
        MANDT = #{record.mandt,jdbcType=VARCHAR},
      </if>
      <if test="record.ebeln != null">
        EBELN = #{record.ebeln,jdbcType=VARCHAR},
      </if>
      <if test="record.ebelp != null">
        EBELP = #{record.ebelp,jdbcType=VARCHAR},
      </if>
      <if test="record.zekkn != null">
        ZEKKN = #{record.zekkn,jdbcType=VARCHAR},
      </if>
      <if test="record.vgabe != null">
        VGABE = #{record.vgabe,jdbcType=VARCHAR},
      </if>
      <if test="record.gjahr != null">
        GJAHR = #{record.gjahr,jdbcType=VARCHAR},
      </if>
      <if test="record.belnr != null">
        BELNR = #{record.belnr,jdbcType=VARCHAR},
      </if>
      <if test="record.buzei != null">
        BUZEI = #{record.buzei,jdbcType=VARCHAR},
      </if>
      <if test="record.bewtp != null">
        BEWTP = #{record.bewtp,jdbcType=VARCHAR},
      </if>
      <if test="record.bwart != null">
        BWART = #{record.bwart,jdbcType=VARCHAR},
      </if>
      <if test="record.budat != null">
        BUDAT = #{record.budat,jdbcType=VARCHAR},
      </if>
      <if test="record.menge != null">
        MENGE = #{record.menge,jdbcType=DECIMAL},
      </if>
      <if test="record.bpmng != null">
        BPMNG = #{record.bpmng,jdbcType=DECIMAL},
      </if>
      <if test="record.dmbtr != null">
        DMBTR = #{record.dmbtr,jdbcType=DECIMAL},
      </if>
      <if test="record.wrbtr != null">
        WRBTR = #{record.wrbtr,jdbcType=DECIMAL},
      </if>
      <if test="record.waers != null">
        WAERS = #{record.waers,jdbcType=VARCHAR},
      </if>
      <if test="record.arewr != null">
        AREWR = #{record.arewr,jdbcType=DECIMAL},
      </if>
      <if test="record.wesbs != null">
        WESBS = #{record.wesbs,jdbcType=DECIMAL},
      </if>
      <if test="record.bpwes != null">
        BPWES = #{record.bpwes,jdbcType=DECIMAL},
      </if>
      <if test="record.shkzg != null">
        SHKZG = #{record.shkzg,jdbcType=VARCHAR},
      </if>
      <if test="record.bwtar != null">
        BWTAR = #{record.bwtar,jdbcType=VARCHAR},
      </if>
      <if test="record.elikz != null">
        ELIKZ = #{record.elikz,jdbcType=VARCHAR},
      </if>
      <if test="record.xblnr != null">
        XBLNR = #{record.xblnr,jdbcType=VARCHAR},
      </if>
      <if test="record.lfgja != null">
        LFGJA = #{record.lfgja,jdbcType=VARCHAR},
      </if>
      <if test="record.lfbnr != null">
        LFBNR = #{record.lfbnr,jdbcType=VARCHAR},
      </if>
      <if test="record.lfpos != null">
        LFPOS = #{record.lfpos,jdbcType=VARCHAR},
      </if>
      <if test="record.grund != null">
        GRUND = #{record.grund,jdbcType=VARCHAR},
      </if>
      <if test="record.cpudt != null">
        CPUDT = #{record.cpudt,jdbcType=VARCHAR},
      </if>
      <if test="record.cputm != null">
        CPUTM = #{record.cputm,jdbcType=VARCHAR},
      </if>
      <if test="record.reewr != null">
        REEWR = #{record.reewr,jdbcType=DECIMAL},
      </if>
      <if test="record.evere != null">
        EVERE = #{record.evere,jdbcType=VARCHAR},
      </if>
      <if test="record.refwr != null">
        REFWR = #{record.refwr,jdbcType=DECIMAL},
      </if>
      <if test="record.matnr != null">
        MATNR = #{record.matnr,jdbcType=VARCHAR},
      </if>
      <if test="record.werks != null">
        WERKS = #{record.werks,jdbcType=VARCHAR},
      </if>
      <if test="record.xwsbr != null">
        XWSBR = #{record.xwsbr,jdbcType=VARCHAR},
      </if>
      <if test="record.etens != null">
        ETENS = #{record.etens,jdbcType=VARCHAR},
      </if>
      <if test="record.knumv != null">
        KNUMV = #{record.knumv,jdbcType=VARCHAR},
      </if>
      <if test="record.mwskz != null">
        MWSKZ = #{record.mwskz,jdbcType=VARCHAR},
      </if>
      <if test="record.lsmng != null">
        LSMNG = #{record.lsmng,jdbcType=DECIMAL},
      </if>
      <if test="record.lsmeh != null">
        LSMEH = #{record.lsmeh,jdbcType=VARCHAR},
      </if>
      <if test="record.ematn != null">
        EMATN = #{record.ematn,jdbcType=VARCHAR},
      </if>
      <if test="record.areww != null">
        AREWW = #{record.areww,jdbcType=DECIMAL},
      </if>
      <if test="record.hswae != null">
        HSWAE = #{record.hswae,jdbcType=VARCHAR},
      </if>
      <if test="record.bamng != null">
        BAMNG = #{record.bamng,jdbcType=DECIMAL},
      </if>
      <if test="record.charg != null">
        CHARG = #{record.charg,jdbcType=VARCHAR},
      </if>
      <if test="record.bldat != null">
        BLDAT = #{record.bldat,jdbcType=VARCHAR},
      </if>
      <if test="record.xwoff != null">
        XWOFF = #{record.xwoff,jdbcType=VARCHAR},
      </if>
      <if test="record.xunpl != null">
        XUNPL = #{record.xunpl,jdbcType=VARCHAR},
      </if>
      <if test="record.ernam != null">
        ERNAM = #{record.ernam,jdbcType=VARCHAR},
      </if>
      <if test="record.srvpos != null">
        SRVPOS = #{record.srvpos,jdbcType=VARCHAR},
      </if>
      <if test="record.packno != null">
        PACKNO = #{record.packno,jdbcType=VARCHAR},
      </if>
      <if test="record.introw != null">
        INTROW = #{record.introw,jdbcType=VARCHAR},
      </if>
      <if test="record.bekkn != null">
        BEKKN = #{record.bekkn,jdbcType=VARCHAR},
      </if>
      <if test="record.lemin != null">
        LEMIN = #{record.lemin,jdbcType=VARCHAR},
      </if>
      <if test="record.arewb != null">
        AREWB = #{record.arewb,jdbcType=DECIMAL},
      </if>
      <if test="record.rewrb != null">
        REWRB = #{record.rewrb,jdbcType=DECIMAL},
      </if>
      <if test="record.saprl != null">
        SAPRL = #{record.saprl,jdbcType=VARCHAR},
      </if>
      <if test="record.mengePop != null">
        MENGE_POP = #{record.mengePop,jdbcType=DECIMAL},
      </if>
      <if test="record.bpmngPop != null">
        BPMNG_POP = #{record.bpmngPop,jdbcType=DECIMAL},
      </if>
      <if test="record.dmbtrPop != null">
        DMBTR_POP = #{record.dmbtrPop,jdbcType=DECIMAL},
      </if>
      <if test="record.wrbtrPop != null">
        WRBTR_POP = #{record.wrbtrPop,jdbcType=DECIMAL},
      </if>
      <if test="record.wesbb != null">
        WESBB = #{record.wesbb,jdbcType=DECIMAL},
      </if>
      <if test="record.bpweb != null">
        BPWEB = #{record.bpweb,jdbcType=DECIMAL},
      </if>
      <if test="record.weora != null">
        WEORA = #{record.weora,jdbcType=VARCHAR},
      </if>
      <if test="record.arewrPop != null">
        AREWR_POP = #{record.arewrPop,jdbcType=DECIMAL},
      </if>
      <if test="record.kudif != null">
        KUDIF = #{record.kudif,jdbcType=DECIMAL},
      </if>
      <if test="record.retamtFc != null">
        RETAMT_FC = #{record.retamtFc,jdbcType=DECIMAL},
      </if>
      <if test="record.retamtLc != null">
        RETAMT_LC = #{record.retamtLc,jdbcType=DECIMAL},
      </if>
      <if test="record.retamtpFc != null">
        RETAMTP_FC = #{record.retamtpFc,jdbcType=DECIMAL},
      </if>
      <if test="record.retamtpLc != null">
        RETAMTP_LC = #{record.retamtpLc,jdbcType=DECIMAL},
      </if>
      <if test="record.xmacc != null">
        XMACC = #{record.xmacc,jdbcType=VARCHAR},
      </if>
      <if test="record.wkurs != null">
        WKURS = #{record.wkurs,jdbcType=DECIMAL},
      </if>
      <if test="record.invItemOrigin != null">
        INV_ITEM_ORIGIN = #{record.invItemOrigin,jdbcType=VARCHAR},
      </if>
      <if test="record.vbelnSt != null">
        VBELN_ST = #{record.vbelnSt,jdbcType=VARCHAR},
      </if>
      <if test="record.vbelpSt != null">
        VBELP_ST = #{record.vbelpSt,jdbcType=VARCHAR},
      </if>
      <if test="record.sgtScat != null">
        SGT_SCAT = #{record.sgtScat,jdbcType=VARCHAR},
      </if>
      <if test="record.dataaging != null">
        DATAAGING = #{record.dataaging,jdbcType=VARCHAR},
      </if>
      <if test="record.sesuom != null">
        SESUOM = #{record.sesuom,jdbcType=VARCHAR},
      </if>
      <if test="record.etUpd != null">
        ET_UPD = #{record.etUpd,jdbcType=VARCHAR},
      </if>
      <if test="record.cwmBamng != null">
        CWM_BAMNG = #{record.cwmBamng,jdbcType=DECIMAL},
      </if>
      <if test="record.cwmWesbs != null">
        CWM_WESBS = #{record.cwmWesbs,jdbcType=DECIMAL},
      </if>
      <if test="record.cwmTy2tq != null">
        CWM_TY2TQ = #{record.cwmTy2tq,jdbcType=VARCHAR},
      </if>
      <if test="record.cwmWesbb != null">
        CWM_WESBB = #{record.cwmWesbb,jdbcType=DECIMAL},
      </if>
      <if test="record.jScDieCompF != null">
        J_SC_DIE_COMP_F = #{record.jScDieCompF,jdbcType=VARCHAR},
      </if>
      <if test="record.fshSeasonYear != null">
        FSH_SEASON_YEAR = #{record.fshSeasonYear,jdbcType=VARCHAR},
      </if>
      <if test="record.fshSeason != null">
        FSH_SEASON = #{record.fshSeason,jdbcType=VARCHAR},
      </if>
      <if test="record.fshCollection != null">
        FSH_COLLECTION = #{record.fshCollection,jdbcType=VARCHAR},
      </if>
      <if test="record.fshTheme != null">
        FSH_THEME = #{record.fshTheme,jdbcType=VARCHAR},
      </if>
      <if test="record.qtyDiff != null">
        QTY_DIFF = #{record.qtyDiff,jdbcType=DECIMAL},
      </if>
      <if test="record.wrfCharstc1 != null">
        WRF_CHARSTC1 = #{record.wrfCharstc1,jdbcType=VARCHAR},
      </if>
      <if test="record.wrfCharstc2 != null">
        WRF_CHARSTC2 = #{record.wrfCharstc2,jdbcType=VARCHAR},
      </if>
      <if test="record.wrfCharstc3 != null">
        WRF_CHARSTC3 = #{record.wrfCharstc3,jdbcType=VARCHAR},
      </if>
      <if test="record.zzdate != null">
        ZZDATE = #{record.zzdate,jdbcType=VARCHAR},
      </if>
      <if test="record.zztime != null">
        ZZTIME = #{record.zztime,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update SAP_EKBE
    set id = #{record.id,jdbcType=BIGINT},
      MANDT = #{record.mandt,jdbcType=VARCHAR},
      EBELN = #{record.ebeln,jdbcType=VARCHAR},
      EBELP = #{record.ebelp,jdbcType=VARCHAR},
      ZEKKN = #{record.zekkn,jdbcType=VARCHAR},
      VGABE = #{record.vgabe,jdbcType=VARCHAR},
      GJAHR = #{record.gjahr,jdbcType=VARCHAR},
      BELNR = #{record.belnr,jdbcType=VARCHAR},
      BUZEI = #{record.buzei,jdbcType=VARCHAR},
      BEWTP = #{record.bewtp,jdbcType=VARCHAR},
      BWART = #{record.bwart,jdbcType=VARCHAR},
      BUDAT = #{record.budat,jdbcType=VARCHAR},
      MENGE = #{record.menge,jdbcType=DECIMAL},
      BPMNG = #{record.bpmng,jdbcType=DECIMAL},
      DMBTR = #{record.dmbtr,jdbcType=DECIMAL},
      WRBTR = #{record.wrbtr,jdbcType=DECIMAL},
      WAERS = #{record.waers,jdbcType=VARCHAR},
      AREWR = #{record.arewr,jdbcType=DECIMAL},
      WESBS = #{record.wesbs,jdbcType=DECIMAL},
      BPWES = #{record.bpwes,jdbcType=DECIMAL},
      SHKZG = #{record.shkzg,jdbcType=VARCHAR},
      BWTAR = #{record.bwtar,jdbcType=VARCHAR},
      ELIKZ = #{record.elikz,jdbcType=VARCHAR},
      XBLNR = #{record.xblnr,jdbcType=VARCHAR},
      LFGJA = #{record.lfgja,jdbcType=VARCHAR},
      LFBNR = #{record.lfbnr,jdbcType=VARCHAR},
      LFPOS = #{record.lfpos,jdbcType=VARCHAR},
      GRUND = #{record.grund,jdbcType=VARCHAR},
      CPUDT = #{record.cpudt,jdbcType=VARCHAR},
      CPUTM = #{record.cputm,jdbcType=VARCHAR},
      REEWR = #{record.reewr,jdbcType=DECIMAL},
      EVERE = #{record.evere,jdbcType=VARCHAR},
      REFWR = #{record.refwr,jdbcType=DECIMAL},
      MATNR = #{record.matnr,jdbcType=VARCHAR},
      WERKS = #{record.werks,jdbcType=VARCHAR},
      XWSBR = #{record.xwsbr,jdbcType=VARCHAR},
      ETENS = #{record.etens,jdbcType=VARCHAR},
      KNUMV = #{record.knumv,jdbcType=VARCHAR},
      MWSKZ = #{record.mwskz,jdbcType=VARCHAR},
      LSMNG = #{record.lsmng,jdbcType=DECIMAL},
      LSMEH = #{record.lsmeh,jdbcType=VARCHAR},
      EMATN = #{record.ematn,jdbcType=VARCHAR},
      AREWW = #{record.areww,jdbcType=DECIMAL},
      HSWAE = #{record.hswae,jdbcType=VARCHAR},
      BAMNG = #{record.bamng,jdbcType=DECIMAL},
      CHARG = #{record.charg,jdbcType=VARCHAR},
      BLDAT = #{record.bldat,jdbcType=VARCHAR},
      XWOFF = #{record.xwoff,jdbcType=VARCHAR},
      XUNPL = #{record.xunpl,jdbcType=VARCHAR},
      ERNAM = #{record.ernam,jdbcType=VARCHAR},
      SRVPOS = #{record.srvpos,jdbcType=VARCHAR},
      PACKNO = #{record.packno,jdbcType=VARCHAR},
      INTROW = #{record.introw,jdbcType=VARCHAR},
      BEKKN = #{record.bekkn,jdbcType=VARCHAR},
      LEMIN = #{record.lemin,jdbcType=VARCHAR},
      AREWB = #{record.arewb,jdbcType=DECIMAL},
      REWRB = #{record.rewrb,jdbcType=DECIMAL},
      SAPRL = #{record.saprl,jdbcType=VARCHAR},
      MENGE_POP = #{record.mengePop,jdbcType=DECIMAL},
      BPMNG_POP = #{record.bpmngPop,jdbcType=DECIMAL},
      DMBTR_POP = #{record.dmbtrPop,jdbcType=DECIMAL},
      WRBTR_POP = #{record.wrbtrPop,jdbcType=DECIMAL},
      WESBB = #{record.wesbb,jdbcType=DECIMAL},
      BPWEB = #{record.bpweb,jdbcType=DECIMAL},
      WEORA = #{record.weora,jdbcType=VARCHAR},
      AREWR_POP = #{record.arewrPop,jdbcType=DECIMAL},
      KUDIF = #{record.kudif,jdbcType=DECIMAL},
      RETAMT_FC = #{record.retamtFc,jdbcType=DECIMAL},
      RETAMT_LC = #{record.retamtLc,jdbcType=DECIMAL},
      RETAMTP_FC = #{record.retamtpFc,jdbcType=DECIMAL},
      RETAMTP_LC = #{record.retamtpLc,jdbcType=DECIMAL},
      XMACC = #{record.xmacc,jdbcType=VARCHAR},
      WKURS = #{record.wkurs,jdbcType=DECIMAL},
      INV_ITEM_ORIGIN = #{record.invItemOrigin,jdbcType=VARCHAR},
      VBELN_ST = #{record.vbelnSt,jdbcType=VARCHAR},
      VBELP_ST = #{record.vbelpSt,jdbcType=VARCHAR},
      SGT_SCAT = #{record.sgtScat,jdbcType=VARCHAR},
      DATAAGING = #{record.dataaging,jdbcType=VARCHAR},
      SESUOM = #{record.sesuom,jdbcType=VARCHAR},
      ET_UPD = #{record.etUpd,jdbcType=VARCHAR},
      CWM_BAMNG = #{record.cwmBamng,jdbcType=DECIMAL},
      CWM_WESBS = #{record.cwmWesbs,jdbcType=DECIMAL},
      CWM_TY2TQ = #{record.cwmTy2tq,jdbcType=VARCHAR},
      CWM_WESBB = #{record.cwmWesbb,jdbcType=DECIMAL},
      J_SC_DIE_COMP_F = #{record.jScDieCompF,jdbcType=VARCHAR},
      FSH_SEASON_YEAR = #{record.fshSeasonYear,jdbcType=VARCHAR},
      FSH_SEASON = #{record.fshSeason,jdbcType=VARCHAR},
      FSH_COLLECTION = #{record.fshCollection,jdbcType=VARCHAR},
      FSH_THEME = #{record.fshTheme,jdbcType=VARCHAR},
      QTY_DIFF = #{record.qtyDiff,jdbcType=DECIMAL},
      WRF_CHARSTC1 = #{record.wrfCharstc1,jdbcType=VARCHAR},
      WRF_CHARSTC2 = #{record.wrfCharstc2,jdbcType=VARCHAR},
      WRF_CHARSTC3 = #{record.wrfCharstc3,jdbcType=VARCHAR},
      ZZDATE = #{record.zzdate,jdbcType=VARCHAR},
      ZZTIME = #{record.zztime,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.iscm.entity.SapEkbe">
    update SAP_EKBE
    <set>
      <if test="mandt != null">
        MANDT = #{mandt,jdbcType=VARCHAR},
      </if>
      <if test="ebeln != null">
        EBELN = #{ebeln,jdbcType=VARCHAR},
      </if>
      <if test="ebelp != null">
        EBELP = #{ebelp,jdbcType=VARCHAR},
      </if>
      <if test="zekkn != null">
        ZEKKN = #{zekkn,jdbcType=VARCHAR},
      </if>
      <if test="vgabe != null">
        VGABE = #{vgabe,jdbcType=VARCHAR},
      </if>
      <if test="gjahr != null">
        GJAHR = #{gjahr,jdbcType=VARCHAR},
      </if>
      <if test="belnr != null">
        BELNR = #{belnr,jdbcType=VARCHAR},
      </if>
      <if test="buzei != null">
        BUZEI = #{buzei,jdbcType=VARCHAR},
      </if>
      <if test="bewtp != null">
        BEWTP = #{bewtp,jdbcType=VARCHAR},
      </if>
      <if test="bwart != null">
        BWART = #{bwart,jdbcType=VARCHAR},
      </if>
      <if test="budat != null">
        BUDAT = #{budat,jdbcType=VARCHAR},
      </if>
      <if test="menge != null">
        MENGE = #{menge,jdbcType=DECIMAL},
      </if>
      <if test="bpmng != null">
        BPMNG = #{bpmng,jdbcType=DECIMAL},
      </if>
      <if test="dmbtr != null">
        DMBTR = #{dmbtr,jdbcType=DECIMAL},
      </if>
      <if test="wrbtr != null">
        WRBTR = #{wrbtr,jdbcType=DECIMAL},
      </if>
      <if test="waers != null">
        WAERS = #{waers,jdbcType=VARCHAR},
      </if>
      <if test="arewr != null">
        AREWR = #{arewr,jdbcType=DECIMAL},
      </if>
      <if test="wesbs != null">
        WESBS = #{wesbs,jdbcType=DECIMAL},
      </if>
      <if test="bpwes != null">
        BPWES = #{bpwes,jdbcType=DECIMAL},
      </if>
      <if test="shkzg != null">
        SHKZG = #{shkzg,jdbcType=VARCHAR},
      </if>
      <if test="bwtar != null">
        BWTAR = #{bwtar,jdbcType=VARCHAR},
      </if>
      <if test="elikz != null">
        ELIKZ = #{elikz,jdbcType=VARCHAR},
      </if>
      <if test="xblnr != null">
        XBLNR = #{xblnr,jdbcType=VARCHAR},
      </if>
      <if test="lfgja != null">
        LFGJA = #{lfgja,jdbcType=VARCHAR},
      </if>
      <if test="lfbnr != null">
        LFBNR = #{lfbnr,jdbcType=VARCHAR},
      </if>
      <if test="lfpos != null">
        LFPOS = #{lfpos,jdbcType=VARCHAR},
      </if>
      <if test="grund != null">
        GRUND = #{grund,jdbcType=VARCHAR},
      </if>
      <if test="cpudt != null">
        CPUDT = #{cpudt,jdbcType=VARCHAR},
      </if>
      <if test="cputm != null">
        CPUTM = #{cputm,jdbcType=VARCHAR},
      </if>
      <if test="reewr != null">
        REEWR = #{reewr,jdbcType=DECIMAL},
      </if>
      <if test="evere != null">
        EVERE = #{evere,jdbcType=VARCHAR},
      </if>
      <if test="refwr != null">
        REFWR = #{refwr,jdbcType=DECIMAL},
      </if>
      <if test="matnr != null">
        MATNR = #{matnr,jdbcType=VARCHAR},
      </if>
      <if test="werks != null">
        WERKS = #{werks,jdbcType=VARCHAR},
      </if>
      <if test="xwsbr != null">
        XWSBR = #{xwsbr,jdbcType=VARCHAR},
      </if>
      <if test="etens != null">
        ETENS = #{etens,jdbcType=VARCHAR},
      </if>
      <if test="knumv != null">
        KNUMV = #{knumv,jdbcType=VARCHAR},
      </if>
      <if test="mwskz != null">
        MWSKZ = #{mwskz,jdbcType=VARCHAR},
      </if>
      <if test="lsmng != null">
        LSMNG = #{lsmng,jdbcType=DECIMAL},
      </if>
      <if test="lsmeh != null">
        LSMEH = #{lsmeh,jdbcType=VARCHAR},
      </if>
      <if test="ematn != null">
        EMATN = #{ematn,jdbcType=VARCHAR},
      </if>
      <if test="areww != null">
        AREWW = #{areww,jdbcType=DECIMAL},
      </if>
      <if test="hswae != null">
        HSWAE = #{hswae,jdbcType=VARCHAR},
      </if>
      <if test="bamng != null">
        BAMNG = #{bamng,jdbcType=DECIMAL},
      </if>
      <if test="charg != null">
        CHARG = #{charg,jdbcType=VARCHAR},
      </if>
      <if test="bldat != null">
        BLDAT = #{bldat,jdbcType=VARCHAR},
      </if>
      <if test="xwoff != null">
        XWOFF = #{xwoff,jdbcType=VARCHAR},
      </if>
      <if test="xunpl != null">
        XUNPL = #{xunpl,jdbcType=VARCHAR},
      </if>
      <if test="ernam != null">
        ERNAM = #{ernam,jdbcType=VARCHAR},
      </if>
      <if test="srvpos != null">
        SRVPOS = #{srvpos,jdbcType=VARCHAR},
      </if>
      <if test="packno != null">
        PACKNO = #{packno,jdbcType=VARCHAR},
      </if>
      <if test="introw != null">
        INTROW = #{introw,jdbcType=VARCHAR},
      </if>
      <if test="bekkn != null">
        BEKKN = #{bekkn,jdbcType=VARCHAR},
      </if>
      <if test="lemin != null">
        LEMIN = #{lemin,jdbcType=VARCHAR},
      </if>
      <if test="arewb != null">
        AREWB = #{arewb,jdbcType=DECIMAL},
      </if>
      <if test="rewrb != null">
        REWRB = #{rewrb,jdbcType=DECIMAL},
      </if>
      <if test="saprl != null">
        SAPRL = #{saprl,jdbcType=VARCHAR},
      </if>
      <if test="mengePop != null">
        MENGE_POP = #{mengePop,jdbcType=DECIMAL},
      </if>
      <if test="bpmngPop != null">
        BPMNG_POP = #{bpmngPop,jdbcType=DECIMAL},
      </if>
      <if test="dmbtrPop != null">
        DMBTR_POP = #{dmbtrPop,jdbcType=DECIMAL},
      </if>
      <if test="wrbtrPop != null">
        WRBTR_POP = #{wrbtrPop,jdbcType=DECIMAL},
      </if>
      <if test="wesbb != null">
        WESBB = #{wesbb,jdbcType=DECIMAL},
      </if>
      <if test="bpweb != null">
        BPWEB = #{bpweb,jdbcType=DECIMAL},
      </if>
      <if test="weora != null">
        WEORA = #{weora,jdbcType=VARCHAR},
      </if>
      <if test="arewrPop != null">
        AREWR_POP = #{arewrPop,jdbcType=DECIMAL},
      </if>
      <if test="kudif != null">
        KUDIF = #{kudif,jdbcType=DECIMAL},
      </if>
      <if test="retamtFc != null">
        RETAMT_FC = #{retamtFc,jdbcType=DECIMAL},
      </if>
      <if test="retamtLc != null">
        RETAMT_LC = #{retamtLc,jdbcType=DECIMAL},
      </if>
      <if test="retamtpFc != null">
        RETAMTP_FC = #{retamtpFc,jdbcType=DECIMAL},
      </if>
      <if test="retamtpLc != null">
        RETAMTP_LC = #{retamtpLc,jdbcType=DECIMAL},
      </if>
      <if test="xmacc != null">
        XMACC = #{xmacc,jdbcType=VARCHAR},
      </if>
      <if test="wkurs != null">
        WKURS = #{wkurs,jdbcType=DECIMAL},
      </if>
      <if test="invItemOrigin != null">
        INV_ITEM_ORIGIN = #{invItemOrigin,jdbcType=VARCHAR},
      </if>
      <if test="vbelnSt != null">
        VBELN_ST = #{vbelnSt,jdbcType=VARCHAR},
      </if>
      <if test="vbelpSt != null">
        VBELP_ST = #{vbelpSt,jdbcType=VARCHAR},
      </if>
      <if test="sgtScat != null">
        SGT_SCAT = #{sgtScat,jdbcType=VARCHAR},
      </if>
      <if test="dataaging != null">
        DATAAGING = #{dataaging,jdbcType=VARCHAR},
      </if>
      <if test="sesuom != null">
        SESUOM = #{sesuom,jdbcType=VARCHAR},
      </if>
      <if test="etUpd != null">
        ET_UPD = #{etUpd,jdbcType=VARCHAR},
      </if>
      <if test="cwmBamng != null">
        CWM_BAMNG = #{cwmBamng,jdbcType=DECIMAL},
      </if>
      <if test="cwmWesbs != null">
        CWM_WESBS = #{cwmWesbs,jdbcType=DECIMAL},
      </if>
      <if test="cwmTy2tq != null">
        CWM_TY2TQ = #{cwmTy2tq,jdbcType=VARCHAR},
      </if>
      <if test="cwmWesbb != null">
        CWM_WESBB = #{cwmWesbb,jdbcType=DECIMAL},
      </if>
      <if test="jScDieCompF != null">
        J_SC_DIE_COMP_F = #{jScDieCompF,jdbcType=VARCHAR},
      </if>
      <if test="fshSeasonYear != null">
        FSH_SEASON_YEAR = #{fshSeasonYear,jdbcType=VARCHAR},
      </if>
      <if test="fshSeason != null">
        FSH_SEASON = #{fshSeason,jdbcType=VARCHAR},
      </if>
      <if test="fshCollection != null">
        FSH_COLLECTION = #{fshCollection,jdbcType=VARCHAR},
      </if>
      <if test="fshTheme != null">
        FSH_THEME = #{fshTheme,jdbcType=VARCHAR},
      </if>
      <if test="qtyDiff != null">
        QTY_DIFF = #{qtyDiff,jdbcType=DECIMAL},
      </if>
      <if test="wrfCharstc1 != null">
        WRF_CHARSTC1 = #{wrfCharstc1,jdbcType=VARCHAR},
      </if>
      <if test="wrfCharstc2 != null">
        WRF_CHARSTC2 = #{wrfCharstc2,jdbcType=VARCHAR},
      </if>
      <if test="wrfCharstc3 != null">
        WRF_CHARSTC3 = #{wrfCharstc3,jdbcType=VARCHAR},
      </if>
      <if test="zzdate != null">
        ZZDATE = #{zzdate,jdbcType=VARCHAR},
      </if>
      <if test="zztime != null">
        ZZTIME = #{zztime,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.iscm.entity.SapEkbe">
    update SAP_EKBE
    set MANDT = #{mandt,jdbcType=VARCHAR},
      EBELN = #{ebeln,jdbcType=VARCHAR},
      EBELP = #{ebelp,jdbcType=VARCHAR},
      ZEKKN = #{zekkn,jdbcType=VARCHAR},
      VGABE = #{vgabe,jdbcType=VARCHAR},
      GJAHR = #{gjahr,jdbcType=VARCHAR},
      BELNR = #{belnr,jdbcType=VARCHAR},
      BUZEI = #{buzei,jdbcType=VARCHAR},
      BEWTP = #{bewtp,jdbcType=VARCHAR},
      BWART = #{bwart,jdbcType=VARCHAR},
      BUDAT = #{budat,jdbcType=VARCHAR},
      MENGE = #{menge,jdbcType=DECIMAL},
      BPMNG = #{bpmng,jdbcType=DECIMAL},
      DMBTR = #{dmbtr,jdbcType=DECIMAL},
      WRBTR = #{wrbtr,jdbcType=DECIMAL},
      WAERS = #{waers,jdbcType=VARCHAR},
      AREWR = #{arewr,jdbcType=DECIMAL},
      WESBS = #{wesbs,jdbcType=DECIMAL},
      BPWES = #{bpwes,jdbcType=DECIMAL},
      SHKZG = #{shkzg,jdbcType=VARCHAR},
      BWTAR = #{bwtar,jdbcType=VARCHAR},
      ELIKZ = #{elikz,jdbcType=VARCHAR},
      XBLNR = #{xblnr,jdbcType=VARCHAR},
      LFGJA = #{lfgja,jdbcType=VARCHAR},
      LFBNR = #{lfbnr,jdbcType=VARCHAR},
      LFPOS = #{lfpos,jdbcType=VARCHAR},
      GRUND = #{grund,jdbcType=VARCHAR},
      CPUDT = #{cpudt,jdbcType=VARCHAR},
      CPUTM = #{cputm,jdbcType=VARCHAR},
      REEWR = #{reewr,jdbcType=DECIMAL},
      EVERE = #{evere,jdbcType=VARCHAR},
      REFWR = #{refwr,jdbcType=DECIMAL},
      MATNR = #{matnr,jdbcType=VARCHAR},
      WERKS = #{werks,jdbcType=VARCHAR},
      XWSBR = #{xwsbr,jdbcType=VARCHAR},
      ETENS = #{etens,jdbcType=VARCHAR},
      KNUMV = #{knumv,jdbcType=VARCHAR},
      MWSKZ = #{mwskz,jdbcType=VARCHAR},
      LSMNG = #{lsmng,jdbcType=DECIMAL},
      LSMEH = #{lsmeh,jdbcType=VARCHAR},
      EMATN = #{ematn,jdbcType=VARCHAR},
      AREWW = #{areww,jdbcType=DECIMAL},
      HSWAE = #{hswae,jdbcType=VARCHAR},
      BAMNG = #{bamng,jdbcType=DECIMAL},
      CHARG = #{charg,jdbcType=VARCHAR},
      BLDAT = #{bldat,jdbcType=VARCHAR},
      XWOFF = #{xwoff,jdbcType=VARCHAR},
      XUNPL = #{xunpl,jdbcType=VARCHAR},
      ERNAM = #{ernam,jdbcType=VARCHAR},
      SRVPOS = #{srvpos,jdbcType=VARCHAR},
      PACKNO = #{packno,jdbcType=VARCHAR},
      INTROW = #{introw,jdbcType=VARCHAR},
      BEKKN = #{bekkn,jdbcType=VARCHAR},
      LEMIN = #{lemin,jdbcType=VARCHAR},
      AREWB = #{arewb,jdbcType=DECIMAL},
      REWRB = #{rewrb,jdbcType=DECIMAL},
      SAPRL = #{saprl,jdbcType=VARCHAR},
      MENGE_POP = #{mengePop,jdbcType=DECIMAL},
      BPMNG_POP = #{bpmngPop,jdbcType=DECIMAL},
      DMBTR_POP = #{dmbtrPop,jdbcType=DECIMAL},
      WRBTR_POP = #{wrbtrPop,jdbcType=DECIMAL},
      WESBB = #{wesbb,jdbcType=DECIMAL},
      BPWEB = #{bpweb,jdbcType=DECIMAL},
      WEORA = #{weora,jdbcType=VARCHAR},
      AREWR_POP = #{arewrPop,jdbcType=DECIMAL},
      KUDIF = #{kudif,jdbcType=DECIMAL},
      RETAMT_FC = #{retamtFc,jdbcType=DECIMAL},
      RETAMT_LC = #{retamtLc,jdbcType=DECIMAL},
      RETAMTP_FC = #{retamtpFc,jdbcType=DECIMAL},
      RETAMTP_LC = #{retamtpLc,jdbcType=DECIMAL},
      XMACC = #{xmacc,jdbcType=VARCHAR},
      WKURS = #{wkurs,jdbcType=DECIMAL},
      INV_ITEM_ORIGIN = #{invItemOrigin,jdbcType=VARCHAR},
      VBELN_ST = #{vbelnSt,jdbcType=VARCHAR},
      VBELP_ST = #{vbelpSt,jdbcType=VARCHAR},
      SGT_SCAT = #{sgtScat,jdbcType=VARCHAR},
      DATAAGING = #{dataaging,jdbcType=VARCHAR},
      SESUOM = #{sesuom,jdbcType=VARCHAR},
      ET_UPD = #{etUpd,jdbcType=VARCHAR},
      CWM_BAMNG = #{cwmBamng,jdbcType=DECIMAL},
      CWM_WESBS = #{cwmWesbs,jdbcType=DECIMAL},
      CWM_TY2TQ = #{cwmTy2tq,jdbcType=VARCHAR},
      CWM_WESBB = #{cwmWesbb,jdbcType=DECIMAL},
      J_SC_DIE_COMP_F = #{jScDieCompF,jdbcType=VARCHAR},
      FSH_SEASON_YEAR = #{fshSeasonYear,jdbcType=VARCHAR},
      FSH_SEASON = #{fshSeason,jdbcType=VARCHAR},
      FSH_COLLECTION = #{fshCollection,jdbcType=VARCHAR},
      FSH_THEME = #{fshTheme,jdbcType=VARCHAR},
      QTY_DIFF = #{qtyDiff,jdbcType=DECIMAL},
      WRF_CHARSTC1 = #{wrfCharstc1,jdbcType=VARCHAR},
      WRF_CHARSTC2 = #{wrfCharstc2,jdbcType=VARCHAR},
      WRF_CHARSTC3 = #{wrfCharstc3,jdbcType=VARCHAR},
      ZZDATE = #{zzdate,jdbcType=VARCHAR},
      ZZTIME = #{zztime,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>