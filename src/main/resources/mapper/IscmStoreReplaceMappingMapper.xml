<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.IscmStoreReplaceMappingMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.IscmStoreReplaceMapping">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="platform_org_id" jdbcType="BIGINT" property="platformOrgId" />
    <result column="platform_name" jdbcType="VARCHAR" property="platformName" />
    <result column="company_org_id" jdbcType="BIGINT" property="companyOrgId" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="store_org_id" jdbcType="BIGINT" property="storeOrgId" />
    <result column="store_org_name" jdbcType="VARCHAR" property="storeOrgName" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="replace_store_org_id" jdbcType="BIGINT" property="replaceStoreOrgId" />
    <result column="replace_store_org_name" jdbcType="VARCHAR" property="replaceStoreOrgName" />
    <result column="replace_store_code" jdbcType="VARCHAR" property="replaceStoreCode" />
    <result column="replace_start_date" jdbcType="DATE" property="replaceStartDate" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, platform_org_id, platform_name, company_org_id, company_name, company_code, store_org_id, 
    store_org_name, store_code, replace_store_org_id, replace_store_org_name, replace_store_code, 
    replace_start_date, `status`, gmt_create, gmt_update, extend, version, created_by, 
    created_name, updated_by, updated_name
  </sql>
  <select id="selectByExample" parameterType="com.cowell.iscm.entity.IscmStoreReplaceMappingExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from iscm_store_replace_mapping
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from iscm_store_replace_mapping
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from iscm_store_replace_mapping
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.iscm.entity.IscmStoreReplaceMappingExample">
    delete from iscm_store_replace_mapping
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entity.IscmStoreReplaceMapping" useGeneratedKeys="true">
    insert into iscm_store_replace_mapping (platform_org_id, platform_name, company_org_id, 
      company_name, company_code, store_org_id, 
      store_org_name, store_code, replace_store_org_id, 
      replace_store_org_name, replace_store_code, 
      replace_start_date, `status`, gmt_create, 
      gmt_update, extend, version, 
      created_by, created_name, updated_by, 
      updated_name)
    values (#{platformOrgId,jdbcType=BIGINT}, #{platformName,jdbcType=VARCHAR}, #{companyOrgId,jdbcType=BIGINT}, 
      #{companyName,jdbcType=VARCHAR}, #{companyCode,jdbcType=VARCHAR}, #{storeOrgId,jdbcType=BIGINT}, 
      #{storeOrgName,jdbcType=VARCHAR}, #{storeCode,jdbcType=VARCHAR}, #{replaceStoreOrgId,jdbcType=BIGINT}, 
      #{replaceStoreOrgName,jdbcType=VARCHAR}, #{replaceStoreCode,jdbcType=VARCHAR}, 
      #{replaceStartDate,jdbcType=DATE}, #{status,jdbcType=TINYINT}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtUpdate,jdbcType=TIMESTAMP}, #{extend,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER}, 
      #{createdBy,jdbcType=BIGINT}, #{createdName,jdbcType=VARCHAR}, #{updatedBy,jdbcType=BIGINT}, 
      #{updatedName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entity.IscmStoreReplaceMapping" useGeneratedKeys="true">
    insert into iscm_store_replace_mapping
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="platformOrgId != null">
        platform_org_id,
      </if>
      <if test="platformName != null">
        platform_name,
      </if>
      <if test="companyOrgId != null">
        company_org_id,
      </if>
      <if test="companyName != null">
        company_name,
      </if>
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="storeOrgId != null">
        store_org_id,
      </if>
      <if test="storeOrgName != null">
        store_org_name,
      </if>
      <if test="storeCode != null">
        store_code,
      </if>
      <if test="replaceStoreOrgId != null">
        replace_store_org_id,
      </if>
      <if test="replaceStoreOrgName != null">
        replace_store_org_name,
      </if>
      <if test="replaceStoreCode != null">
        replace_store_code,
      </if>
      <if test="replaceStartDate != null">
        replace_start_date,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedName != null">
        updated_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="platformOrgId != null">
        #{platformOrgId,jdbcType=BIGINT},
      </if>
      <if test="platformName != null">
        #{platformName,jdbcType=VARCHAR},
      </if>
      <if test="companyOrgId != null">
        #{companyOrgId,jdbcType=BIGINT},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="storeOrgId != null">
        #{storeOrgId,jdbcType=BIGINT},
      </if>
      <if test="storeOrgName != null">
        #{storeOrgName,jdbcType=VARCHAR},
      </if>
      <if test="storeCode != null">
        #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="replaceStoreOrgId != null">
        #{replaceStoreOrgId,jdbcType=BIGINT},
      </if>
      <if test="replaceStoreOrgName != null">
        #{replaceStoreOrgName,jdbcType=VARCHAR},
      </if>
      <if test="replaceStoreCode != null">
        #{replaceStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="replaceStartDate != null">
        #{replaceStartDate,jdbcType=DATE},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        #{updatedName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.iscm.entity.IscmStoreReplaceMappingExample" resultType="java.lang.Long">
    select count(*) from iscm_store_replace_mapping
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update iscm_store_replace_mapping
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.platformOrgId != null">
        platform_org_id = #{record.platformOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.platformName != null">
        platform_name = #{record.platformName,jdbcType=VARCHAR},
      </if>
      <if test="record.companyOrgId != null">
        company_org_id = #{record.companyOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.companyName != null">
        company_name = #{record.companyName,jdbcType=VARCHAR},
      </if>
      <if test="record.companyCode != null">
        company_code = #{record.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.storeOrgId != null">
        store_org_id = #{record.storeOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.storeOrgName != null">
        store_org_name = #{record.storeOrgName,jdbcType=VARCHAR},
      </if>
      <if test="record.storeCode != null">
        store_code = #{record.storeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.replaceStoreOrgId != null">
        replace_store_org_id = #{record.replaceStoreOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.replaceStoreOrgName != null">
        replace_store_org_name = #{record.replaceStoreOrgName,jdbcType=VARCHAR},
      </if>
      <if test="record.replaceStoreCode != null">
        replace_store_code = #{record.replaceStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="record.replaceStartDate != null">
        replace_start_date = #{record.replaceStartDate,jdbcType=DATE},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedName != null">
        updated_name = #{record.updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update iscm_store_replace_mapping
    set id = #{record.id,jdbcType=BIGINT},
      platform_org_id = #{record.platformOrgId,jdbcType=BIGINT},
      platform_name = #{record.platformName,jdbcType=VARCHAR},
      company_org_id = #{record.companyOrgId,jdbcType=BIGINT},
      company_name = #{record.companyName,jdbcType=VARCHAR},
      company_code = #{record.companyCode,jdbcType=VARCHAR},
      store_org_id = #{record.storeOrgId,jdbcType=BIGINT},
      store_org_name = #{record.storeOrgName,jdbcType=VARCHAR},
      store_code = #{record.storeCode,jdbcType=VARCHAR},
      replace_store_org_id = #{record.replaceStoreOrgId,jdbcType=BIGINT},
      replace_store_org_name = #{record.replaceStoreOrgName,jdbcType=VARCHAR},
      replace_store_code = #{record.replaceStoreCode,jdbcType=VARCHAR},
      replace_start_date = #{record.replaceStartDate,jdbcType=DATE},
      `status` = #{record.status,jdbcType=TINYINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_name = #{record.createdName,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_name = #{record.updatedName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.iscm.entity.IscmStoreReplaceMapping">
    update iscm_store_replace_mapping
    <set>
      <if test="platformOrgId != null">
        platform_org_id = #{platformOrgId,jdbcType=BIGINT},
      </if>
      <if test="platformName != null">
        platform_name = #{platformName,jdbcType=VARCHAR},
      </if>
      <if test="companyOrgId != null">
        company_org_id = #{companyOrgId,jdbcType=BIGINT},
      </if>
      <if test="companyName != null">
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="storeOrgId != null">
        store_org_id = #{storeOrgId,jdbcType=BIGINT},
      </if>
      <if test="storeOrgName != null">
        store_org_name = #{storeOrgName,jdbcType=VARCHAR},
      </if>
      <if test="storeCode != null">
        store_code = #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="replaceStoreOrgId != null">
        replace_store_org_id = #{replaceStoreOrgId,jdbcType=BIGINT},
      </if>
      <if test="replaceStoreOrgName != null">
        replace_store_org_name = #{replaceStoreOrgName,jdbcType=VARCHAR},
      </if>
      <if test="replaceStoreCode != null">
        replace_store_code = #{replaceStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="replaceStartDate != null">
        replace_start_date = #{replaceStartDate,jdbcType=DATE},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        updated_name = #{updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.iscm.entity.IscmStoreReplaceMapping">
    update iscm_store_replace_mapping
    set platform_org_id = #{platformOrgId,jdbcType=BIGINT},
      platform_name = #{platformName,jdbcType=VARCHAR},
      company_org_id = #{companyOrgId,jdbcType=BIGINT},
      company_name = #{companyName,jdbcType=VARCHAR},
      company_code = #{companyCode,jdbcType=VARCHAR},
      store_org_id = #{storeOrgId,jdbcType=BIGINT},
      store_org_name = #{storeOrgName,jdbcType=VARCHAR},
      store_code = #{storeCode,jdbcType=VARCHAR},
      replace_store_org_id = #{replaceStoreOrgId,jdbcType=BIGINT},
      replace_store_org_name = #{replaceStoreOrgName,jdbcType=VARCHAR},
      replace_store_code = #{replaceStoreCode,jdbcType=VARCHAR},
      replace_start_date = #{replaceStartDate,jdbcType=DATE},
      `status` = #{status,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_name = #{createdName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_name = #{updatedName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>