<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.IscmStoreApplyParamGoodsValidWhitelistOrgMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.IscmStoreApplyParamGoodsValidWhitelistOrg">
    <id column="ID" jdbcType="BIGINT" property="id" />
    <result column="whitelist_ID" jdbcType="BIGINT" property="whitelistId" />
    <result column="rule_org_level" jdbcType="BIGINT" property="ruleOrgLevel" />
    <result column="rule_org_id" jdbcType="BIGINT" property="ruleOrgId" />
    <result column="rule_org_name" jdbcType="VARCHAR" property="ruleOrgName" />
    <result column="rule_org_code" jdbcType="VARCHAR" property="ruleOrgCode" />
    <result column="rule_org_store_id" jdbcType="BIGINT" property="ruleOrgStoreId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    ID, whitelist_ID, rule_org_level, rule_org_id, rule_org_name, rule_org_code, rule_org_store_id
  </sql>
  <select id="selectByExample" parameterType="com.cowell.iscm.entity.IscmStoreApplyParamGoodsValidWhitelistOrgExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from iscm_store_apply_param_goods_valid_whitelist_org
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from iscm_store_apply_param_goods_valid_whitelist_org
    where ID = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from iscm_store_apply_param_goods_valid_whitelist_org
    where ID = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.iscm.entity.IscmStoreApplyParamGoodsValidWhitelistOrgExample">
    delete from iscm_store_apply_param_goods_valid_whitelist_org
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="ID" keyProperty="id" parameterType="com.cowell.iscm.entity.IscmStoreApplyParamGoodsValidWhitelistOrg" useGeneratedKeys="true">
    insert into iscm_store_apply_param_goods_valid_whitelist_org (whitelist_ID, rule_org_level, rule_org_id, 
      rule_org_name, rule_org_code, rule_org_store_id
      )
    values (#{whitelistId,jdbcType=BIGINT}, #{ruleOrgLevel,jdbcType=BIGINT}, #{ruleOrgId,jdbcType=BIGINT}, 
      #{ruleOrgName,jdbcType=VARCHAR}, #{ruleOrgCode,jdbcType=VARCHAR}, #{ruleOrgStoreId,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" keyColumn="ID" keyProperty="id" parameterType="com.cowell.iscm.entity.IscmStoreApplyParamGoodsValidWhitelistOrg" useGeneratedKeys="true">
    insert into iscm_store_apply_param_goods_valid_whitelist_org
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="whitelistId != null">
        whitelist_ID,
      </if>
      <if test="ruleOrgLevel != null">
        rule_org_level,
      </if>
      <if test="ruleOrgId != null">
        rule_org_id,
      </if>
      <if test="ruleOrgName != null">
        rule_org_name,
      </if>
      <if test="ruleOrgCode != null">
        rule_org_code,
      </if>
      <if test="ruleOrgStoreId != null">
        rule_org_store_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="whitelistId != null">
        #{whitelistId,jdbcType=BIGINT},
      </if>
      <if test="ruleOrgLevel != null">
        #{ruleOrgLevel,jdbcType=BIGINT},
      </if>
      <if test="ruleOrgId != null">
        #{ruleOrgId,jdbcType=BIGINT},
      </if>
      <if test="ruleOrgName != null">
        #{ruleOrgName,jdbcType=VARCHAR},
      </if>
      <if test="ruleOrgCode != null">
        #{ruleOrgCode,jdbcType=VARCHAR},
      </if>
      <if test="ruleOrgStoreId != null">
        #{ruleOrgStoreId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.iscm.entity.IscmStoreApplyParamGoodsValidWhitelistOrgExample" resultType="java.lang.Long">
    select count(*) from iscm_store_apply_param_goods_valid_whitelist_org
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update iscm_store_apply_param_goods_valid_whitelist_org
    <set>
      <if test="record.id != null">
        ID = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.whitelistId != null">
        whitelist_ID = #{record.whitelistId,jdbcType=BIGINT},
      </if>
      <if test="record.ruleOrgLevel != null">
        rule_org_level = #{record.ruleOrgLevel,jdbcType=BIGINT},
      </if>
      <if test="record.ruleOrgId != null">
        rule_org_id = #{record.ruleOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.ruleOrgName != null">
        rule_org_name = #{record.ruleOrgName,jdbcType=VARCHAR},
      </if>
      <if test="record.ruleOrgCode != null">
        rule_org_code = #{record.ruleOrgCode,jdbcType=VARCHAR},
      </if>
      <if test="record.ruleOrgStoreId != null">
        rule_org_store_id = #{record.ruleOrgStoreId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update iscm_store_apply_param_goods_valid_whitelist_org
    set ID = #{record.id,jdbcType=BIGINT},
      whitelist_ID = #{record.whitelistId,jdbcType=BIGINT},
      rule_org_level = #{record.ruleOrgLevel,jdbcType=BIGINT},
      rule_org_id = #{record.ruleOrgId,jdbcType=BIGINT},
      rule_org_name = #{record.ruleOrgName,jdbcType=VARCHAR},
      rule_org_code = #{record.ruleOrgCode,jdbcType=VARCHAR},
      rule_org_store_id = #{record.ruleOrgStoreId,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.iscm.entity.IscmStoreApplyParamGoodsValidWhitelistOrg">
    update iscm_store_apply_param_goods_valid_whitelist_org
    <set>
      <if test="whitelistId != null">
        whitelist_ID = #{whitelistId,jdbcType=BIGINT},
      </if>
      <if test="ruleOrgLevel != null">
        rule_org_level = #{ruleOrgLevel,jdbcType=BIGINT},
      </if>
      <if test="ruleOrgId != null">
        rule_org_id = #{ruleOrgId,jdbcType=BIGINT},
      </if>
      <if test="ruleOrgName != null">
        rule_org_name = #{ruleOrgName,jdbcType=VARCHAR},
      </if>
      <if test="ruleOrgCode != null">
        rule_org_code = #{ruleOrgCode,jdbcType=VARCHAR},
      </if>
      <if test="ruleOrgStoreId != null">
        rule_org_store_id = #{ruleOrgStoreId,jdbcType=BIGINT},
      </if>
    </set>
    where ID = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.iscm.entity.IscmStoreApplyParamGoodsValidWhitelistOrg">
    update iscm_store_apply_param_goods_valid_whitelist_org
    set whitelist_ID = #{whitelistId,jdbcType=BIGINT},
      rule_org_level = #{ruleOrgLevel,jdbcType=BIGINT},
      rule_org_id = #{ruleOrgId,jdbcType=BIGINT},
      rule_org_name = #{ruleOrgName,jdbcType=VARCHAR},
      rule_org_code = #{ruleOrgCode,jdbcType=VARCHAR},
      rule_org_store_id = #{ruleOrgStoreId,jdbcType=BIGINT}
    where ID = #{id,jdbcType=BIGINT}
  </update>
</mapper>