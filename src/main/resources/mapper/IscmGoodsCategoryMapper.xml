<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.IscmGoodsCategoryMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.IscmGoodsCategory">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="category_name" jdbcType="VARCHAR" property="categoryName" />
    <result column="parent_id" jdbcType="BIGINT" property="parentId" />
    <result column="category_level" jdbcType="TINYINT" property="categoryLevel" />
    <result column="sort" jdbcType="TINYINT" property="sort" />
    <result column="path" jdbcType="VARCHAR" property="path" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="sort_value" jdbcType="TINYINT" property="sortValue" />
    <result column="features" jdbcType="VARCHAR" property="features" />
    <result column="source" jdbcType="TINYINT" property="source" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, category_name, parent_id, category_level, sort, path, description, sort_value, 
    features, source, status, extend, version, created_by, created_name, updated_by, 
    updated_name, gmt_create, gmt_update
  </sql>
  <select id="selectByExample" parameterType="com.cowell.iscm.entity.IscmGoodsCategoryExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from iscm_goods_category
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from iscm_goods_category
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from iscm_goods_category
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.iscm.entity.IscmGoodsCategoryExample">
    delete from iscm_goods_category
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.iscm.entity.IscmGoodsCategory">
    insert into iscm_goods_category (id, category_name, parent_id, 
      category_level, sort, path, 
      description, sort_value, features, 
      source, status, extend, 
      version, created_by, created_name, 
      updated_by, updated_name, gmt_create, 
      gmt_update)
    values (#{id,jdbcType=BIGINT}, #{categoryName,jdbcType=VARCHAR}, #{parentId,jdbcType=BIGINT}, 
      #{categoryLevel,jdbcType=TINYINT}, #{sort,jdbcType=TINYINT}, #{path,jdbcType=VARCHAR}, 
      #{description,jdbcType=VARCHAR}, #{sortValue,jdbcType=TINYINT}, #{features,jdbcType=VARCHAR}, 
      #{source,jdbcType=TINYINT}, #{status,jdbcType=TINYINT}, #{extend,jdbcType=VARCHAR}, 
      #{version,jdbcType=INTEGER}, #{createdBy,jdbcType=BIGINT}, #{createdName,jdbcType=VARCHAR}, 
      #{updatedBy,jdbcType=BIGINT}, #{updatedName,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtUpdate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.iscm.entity.IscmGoodsCategory">
    insert into iscm_goods_category
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="categoryName != null">
        category_name,
      </if>
      <if test="parentId != null">
        parent_id,
      </if>
      <if test="categoryLevel != null">
        category_level,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="path != null">
        path,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="sortValue != null">
        sort_value,
      </if>
      <if test="features != null">
        features,
      </if>
      <if test="source != null">
        source,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedName != null">
        updated_name,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="categoryName != null">
        #{categoryName,jdbcType=VARCHAR},
      </if>
      <if test="parentId != null">
        #{parentId,jdbcType=BIGINT},
      </if>
      <if test="categoryLevel != null">
        #{categoryLevel,jdbcType=TINYINT},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=TINYINT},
      </if>
      <if test="path != null">
        #{path,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="sortValue != null">
        #{sortValue,jdbcType=TINYINT},
      </if>
      <if test="features != null">
        #{features,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        #{source,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        #{updatedName,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.iscm.entity.IscmGoodsCategoryExample" resultType="java.lang.Long">
    select count(*) from iscm_goods_category
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update iscm_goods_category
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.categoryName != null">
        category_name = #{record.categoryName,jdbcType=VARCHAR},
      </if>
      <if test="record.parentId != null">
        parent_id = #{record.parentId,jdbcType=BIGINT},
      </if>
      <if test="record.categoryLevel != null">
        category_level = #{record.categoryLevel,jdbcType=TINYINT},
      </if>
      <if test="record.sort != null">
        sort = #{record.sort,jdbcType=TINYINT},
      </if>
      <if test="record.path != null">
        path = #{record.path,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.sortValue != null">
        sort_value = #{record.sortValue,jdbcType=TINYINT},
      </if>
      <if test="record.features != null">
        features = #{record.features,jdbcType=VARCHAR},
      </if>
      <if test="record.source != null">
        source = #{record.source,jdbcType=TINYINT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedName != null">
        updated_name = #{record.updatedName,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update iscm_goods_category
    set id = #{record.id,jdbcType=BIGINT},
      category_name = #{record.categoryName,jdbcType=VARCHAR},
      parent_id = #{record.parentId,jdbcType=BIGINT},
      category_level = #{record.categoryLevel,jdbcType=TINYINT},
      sort = #{record.sort,jdbcType=TINYINT},
      path = #{record.path,jdbcType=VARCHAR},
      description = #{record.description,jdbcType=VARCHAR},
      sort_value = #{record.sortValue,jdbcType=TINYINT},
      features = #{record.features,jdbcType=VARCHAR},
      source = #{record.source,jdbcType=TINYINT},
      status = #{record.status,jdbcType=TINYINT},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_name = #{record.createdName,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_name = #{record.updatedName,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.iscm.entity.IscmGoodsCategory">
    update iscm_goods_category
    <set>
      <if test="categoryName != null">
        category_name = #{categoryName,jdbcType=VARCHAR},
      </if>
      <if test="parentId != null">
        parent_id = #{parentId,jdbcType=BIGINT},
      </if>
      <if test="categoryLevel != null">
        category_level = #{categoryLevel,jdbcType=TINYINT},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=TINYINT},
      </if>
      <if test="path != null">
        path = #{path,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="sortValue != null">
        sort_value = #{sortValue,jdbcType=TINYINT},
      </if>
      <if test="features != null">
        features = #{features,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        source = #{source,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        updated_name = #{updatedName,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.iscm.entity.IscmGoodsCategory">
    update iscm_goods_category
    set category_name = #{categoryName,jdbcType=VARCHAR},
      parent_id = #{parentId,jdbcType=BIGINT},
      category_level = #{categoryLevel,jdbcType=TINYINT},
      sort = #{sort,jdbcType=TINYINT},
      path = #{path,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      sort_value = #{sortValue,jdbcType=TINYINT},
      features = #{features,jdbcType=VARCHAR},
      source = #{source,jdbcType=TINYINT},
      status = #{status,jdbcType=TINYINT},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_name = #{createdName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_name = #{updatedName,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>