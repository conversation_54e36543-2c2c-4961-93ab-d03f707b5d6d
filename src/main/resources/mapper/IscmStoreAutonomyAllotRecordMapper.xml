<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.IscmStoreAutonomyAllotRecordMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.IscmStoreAutonomyAllotRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="autonomy_allot_no" jdbcType="VARCHAR" property="autonomyAllotNo" />
    <result column="allot_type" jdbcType="TINYINT" property="allotType" />
    <result column="platform_org_id" jdbcType="BIGINT" property="platformOrgId" />
    <result column="platform_org_name" jdbcType="VARCHAR" property="platformOrgName" />
    <result column="out_company_id" jdbcType="BIGINT" property="outCompanyId" />
    <result column="out_company_code" jdbcType="VARCHAR" property="outCompanyCode" />
    <result column="out_company_name" jdbcType="VARCHAR" property="outCompanyName" />
    <result column="in_company_id" jdbcType="BIGINT" property="inCompanyId" />
    <result column="in_company_code" jdbcType="VARCHAR" property="inCompanyCode" />
    <result column="in_company_name" jdbcType="VARCHAR" property="inCompanyName" />
    <result column="out_store_id" jdbcType="BIGINT" property="outStoreId" />
    <result column="out_store_code" jdbcType="VARCHAR" property="outStoreCode" />
    <result column="out_store_name" jdbcType="VARCHAR" property="outStoreName" />
    <result column="in_store_id" jdbcType="BIGINT" property="inStoreId" />
    <result column="in_store_code" jdbcType="VARCHAR" property="inStoreCode" />
    <result column="in_store_name" jdbcType="VARCHAR" property="inStoreName" />
    <result column="suggest_allot_quantity_all" jdbcType="DECIMAL" property="suggestAllotQuantityAll" />
    <result column="suggest_allot_base_cost" jdbcType="DECIMAL" property="suggestAllotBaseCost" />
    <result column="suggest_allot_breed_all" jdbcType="INTEGER" property="suggestAllotBreedAll" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, autonomy_allot_no, allot_type, platform_org_id, platform_org_name, out_company_id, 
    out_company_code, out_company_name, in_company_id, in_company_code, in_company_name, 
    out_store_id, out_store_code, out_store_name, in_store_id, in_store_code, in_store_name, 
    suggest_allot_quantity_all, suggest_allot_base_cost, suggest_allot_breed_all, `status`, 
    extend, version, created_by, created_name, updated_by, updated_name, gmt_create, 
    gmt_update
  </sql>
  <select id="selectByExample" parameterType="com.cowell.iscm.entity.IscmStoreAutonomyAllotRecordExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from iscm_store_autonomy_allot_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from iscm_store_autonomy_allot_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from iscm_store_autonomy_allot_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.iscm.entity.IscmStoreAutonomyAllotRecordExample">
    delete from iscm_store_autonomy_allot_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entity.IscmStoreAutonomyAllotRecord" useGeneratedKeys="true">
    insert into iscm_store_autonomy_allot_record (autonomy_allot_no, allot_type, platform_org_id, 
      platform_org_name, out_company_id, out_company_code, 
      out_company_name, in_company_id, in_company_code, 
      in_company_name, out_store_id, out_store_code, 
      out_store_name, in_store_id, in_store_code, 
      in_store_name, suggest_allot_quantity_all, suggest_allot_base_cost, 
      suggest_allot_breed_all, `status`, extend, 
      version, created_by, created_name, 
      updated_by, updated_name, gmt_create, 
      gmt_update)
    values (#{autonomyAllotNo,jdbcType=VARCHAR}, #{allotType,jdbcType=TINYINT}, #{platformOrgId,jdbcType=BIGINT}, 
      #{platformOrgName,jdbcType=VARCHAR}, #{outCompanyId,jdbcType=BIGINT}, #{outCompanyCode,jdbcType=VARCHAR}, 
      #{outCompanyName,jdbcType=VARCHAR}, #{inCompanyId,jdbcType=BIGINT}, #{inCompanyCode,jdbcType=VARCHAR}, 
      #{inCompanyName,jdbcType=VARCHAR}, #{outStoreId,jdbcType=BIGINT}, #{outStoreCode,jdbcType=VARCHAR}, 
      #{outStoreName,jdbcType=VARCHAR}, #{inStoreId,jdbcType=BIGINT}, #{inStoreCode,jdbcType=VARCHAR}, 
      #{inStoreName,jdbcType=VARCHAR}, #{suggestAllotQuantityAll,jdbcType=DECIMAL}, #{suggestAllotBaseCost,jdbcType=DECIMAL}, 
      #{suggestAllotBreedAll,jdbcType=INTEGER}, #{status,jdbcType=TINYINT}, #{extend,jdbcType=VARCHAR}, 
      #{version,jdbcType=INTEGER}, #{createdBy,jdbcType=BIGINT}, #{createdName,jdbcType=VARCHAR}, 
      #{updatedBy,jdbcType=BIGINT}, #{updatedName,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtUpdate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entity.IscmStoreAutonomyAllotRecord" useGeneratedKeys="true">
    insert into iscm_store_autonomy_allot_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="autonomyAllotNo != null">
        autonomy_allot_no,
      </if>
      <if test="allotType != null">
        allot_type,
      </if>
      <if test="platformOrgId != null">
        platform_org_id,
      </if>
      <if test="platformOrgName != null">
        platform_org_name,
      </if>
      <if test="outCompanyId != null">
        out_company_id,
      </if>
      <if test="outCompanyCode != null">
        out_company_code,
      </if>
      <if test="outCompanyName != null">
        out_company_name,
      </if>
      <if test="inCompanyId != null">
        in_company_id,
      </if>
      <if test="inCompanyCode != null">
        in_company_code,
      </if>
      <if test="inCompanyName != null">
        in_company_name,
      </if>
      <if test="outStoreId != null">
        out_store_id,
      </if>
      <if test="outStoreCode != null">
        out_store_code,
      </if>
      <if test="outStoreName != null">
        out_store_name,
      </if>
      <if test="inStoreId != null">
        in_store_id,
      </if>
      <if test="inStoreCode != null">
        in_store_code,
      </if>
      <if test="inStoreName != null">
        in_store_name,
      </if>
      <if test="suggestAllotQuantityAll != null">
        suggest_allot_quantity_all,
      </if>
      <if test="suggestAllotBaseCost != null">
        suggest_allot_base_cost,
      </if>
      <if test="suggestAllotBreedAll != null">
        suggest_allot_breed_all,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedName != null">
        updated_name,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="autonomyAllotNo != null">
        #{autonomyAllotNo,jdbcType=VARCHAR},
      </if>
      <if test="allotType != null">
        #{allotType,jdbcType=TINYINT},
      </if>
      <if test="platformOrgId != null">
        #{platformOrgId,jdbcType=BIGINT},
      </if>
      <if test="platformOrgName != null">
        #{platformOrgName,jdbcType=VARCHAR},
      </if>
      <if test="outCompanyId != null">
        #{outCompanyId,jdbcType=BIGINT},
      </if>
      <if test="outCompanyCode != null">
        #{outCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="outCompanyName != null">
        #{outCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="inCompanyId != null">
        #{inCompanyId,jdbcType=BIGINT},
      </if>
      <if test="inCompanyCode != null">
        #{inCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="inCompanyName != null">
        #{inCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="outStoreId != null">
        #{outStoreId,jdbcType=BIGINT},
      </if>
      <if test="outStoreCode != null">
        #{outStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="outStoreName != null">
        #{outStoreName,jdbcType=VARCHAR},
      </if>
      <if test="inStoreId != null">
        #{inStoreId,jdbcType=BIGINT},
      </if>
      <if test="inStoreCode != null">
        #{inStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="inStoreName != null">
        #{inStoreName,jdbcType=VARCHAR},
      </if>
      <if test="suggestAllotQuantityAll != null">
        #{suggestAllotQuantityAll,jdbcType=DECIMAL},
      </if>
      <if test="suggestAllotBaseCost != null">
        #{suggestAllotBaseCost,jdbcType=DECIMAL},
      </if>
      <if test="suggestAllotBreedAll != null">
        #{suggestAllotBreedAll,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        #{updatedName,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.iscm.entity.IscmStoreAutonomyAllotRecordExample" resultType="java.lang.Long">
    select count(*) from iscm_store_autonomy_allot_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update iscm_store_autonomy_allot_record
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.autonomyAllotNo != null">
        autonomy_allot_no = #{record.autonomyAllotNo,jdbcType=VARCHAR},
      </if>
      <if test="record.allotType != null">
        allot_type = #{record.allotType,jdbcType=TINYINT},
      </if>
      <if test="record.platformOrgId != null">
        platform_org_id = #{record.platformOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.platformOrgName != null">
        platform_org_name = #{record.platformOrgName,jdbcType=VARCHAR},
      </if>
      <if test="record.outCompanyId != null">
        out_company_id = #{record.outCompanyId,jdbcType=BIGINT},
      </if>
      <if test="record.outCompanyCode != null">
        out_company_code = #{record.outCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.outCompanyName != null">
        out_company_name = #{record.outCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="record.inCompanyId != null">
        in_company_id = #{record.inCompanyId,jdbcType=BIGINT},
      </if>
      <if test="record.inCompanyCode != null">
        in_company_code = #{record.inCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.inCompanyName != null">
        in_company_name = #{record.inCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="record.outStoreId != null">
        out_store_id = #{record.outStoreId,jdbcType=BIGINT},
      </if>
      <if test="record.outStoreCode != null">
        out_store_code = #{record.outStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="record.outStoreName != null">
        out_store_name = #{record.outStoreName,jdbcType=VARCHAR},
      </if>
      <if test="record.inStoreId != null">
        in_store_id = #{record.inStoreId,jdbcType=BIGINT},
      </if>
      <if test="record.inStoreCode != null">
        in_store_code = #{record.inStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="record.inStoreName != null">
        in_store_name = #{record.inStoreName,jdbcType=VARCHAR},
      </if>
      <if test="record.suggestAllotQuantityAll != null">
        suggest_allot_quantity_all = #{record.suggestAllotQuantityAll,jdbcType=DECIMAL},
      </if>
      <if test="record.suggestAllotBaseCost != null">
        suggest_allot_base_cost = #{record.suggestAllotBaseCost,jdbcType=DECIMAL},
      </if>
      <if test="record.suggestAllotBreedAll != null">
        suggest_allot_breed_all = #{record.suggestAllotBreedAll,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedName != null">
        updated_name = #{record.updatedName,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update iscm_store_autonomy_allot_record
    set id = #{record.id,jdbcType=BIGINT},
      autonomy_allot_no = #{record.autonomyAllotNo,jdbcType=VARCHAR},
      allot_type = #{record.allotType,jdbcType=TINYINT},
      platform_org_id = #{record.platformOrgId,jdbcType=BIGINT},
      platform_org_name = #{record.platformOrgName,jdbcType=VARCHAR},
      out_company_id = #{record.outCompanyId,jdbcType=BIGINT},
      out_company_code = #{record.outCompanyCode,jdbcType=VARCHAR},
      out_company_name = #{record.outCompanyName,jdbcType=VARCHAR},
      in_company_id = #{record.inCompanyId,jdbcType=BIGINT},
      in_company_code = #{record.inCompanyCode,jdbcType=VARCHAR},
      in_company_name = #{record.inCompanyName,jdbcType=VARCHAR},
      out_store_id = #{record.outStoreId,jdbcType=BIGINT},
      out_store_code = #{record.outStoreCode,jdbcType=VARCHAR},
      out_store_name = #{record.outStoreName,jdbcType=VARCHAR},
      in_store_id = #{record.inStoreId,jdbcType=BIGINT},
      in_store_code = #{record.inStoreCode,jdbcType=VARCHAR},
      in_store_name = #{record.inStoreName,jdbcType=VARCHAR},
      suggest_allot_quantity_all = #{record.suggestAllotQuantityAll,jdbcType=DECIMAL},
      suggest_allot_base_cost = #{record.suggestAllotBaseCost,jdbcType=DECIMAL},
      suggest_allot_breed_all = #{record.suggestAllotBreedAll,jdbcType=INTEGER},
      `status` = #{record.status,jdbcType=TINYINT},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_name = #{record.createdName,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_name = #{record.updatedName,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.iscm.entity.IscmStoreAutonomyAllotRecord">
    update iscm_store_autonomy_allot_record
    <set>
      <if test="autonomyAllotNo != null">
        autonomy_allot_no = #{autonomyAllotNo,jdbcType=VARCHAR},
      </if>
      <if test="allotType != null">
        allot_type = #{allotType,jdbcType=TINYINT},
      </if>
      <if test="platformOrgId != null">
        platform_org_id = #{platformOrgId,jdbcType=BIGINT},
      </if>
      <if test="platformOrgName != null">
        platform_org_name = #{platformOrgName,jdbcType=VARCHAR},
      </if>
      <if test="outCompanyId != null">
        out_company_id = #{outCompanyId,jdbcType=BIGINT},
      </if>
      <if test="outCompanyCode != null">
        out_company_code = #{outCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="outCompanyName != null">
        out_company_name = #{outCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="inCompanyId != null">
        in_company_id = #{inCompanyId,jdbcType=BIGINT},
      </if>
      <if test="inCompanyCode != null">
        in_company_code = #{inCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="inCompanyName != null">
        in_company_name = #{inCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="outStoreId != null">
        out_store_id = #{outStoreId,jdbcType=BIGINT},
      </if>
      <if test="outStoreCode != null">
        out_store_code = #{outStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="outStoreName != null">
        out_store_name = #{outStoreName,jdbcType=VARCHAR},
      </if>
      <if test="inStoreId != null">
        in_store_id = #{inStoreId,jdbcType=BIGINT},
      </if>
      <if test="inStoreCode != null">
        in_store_code = #{inStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="inStoreName != null">
        in_store_name = #{inStoreName,jdbcType=VARCHAR},
      </if>
      <if test="suggestAllotQuantityAll != null">
        suggest_allot_quantity_all = #{suggestAllotQuantityAll,jdbcType=DECIMAL},
      </if>
      <if test="suggestAllotBaseCost != null">
        suggest_allot_base_cost = #{suggestAllotBaseCost,jdbcType=DECIMAL},
      </if>
      <if test="suggestAllotBreedAll != null">
        suggest_allot_breed_all = #{suggestAllotBreedAll,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        updated_name = #{updatedName,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.iscm.entity.IscmStoreAutonomyAllotRecord">
    update iscm_store_autonomy_allot_record
    set autonomy_allot_no = #{autonomyAllotNo,jdbcType=VARCHAR},
      allot_type = #{allotType,jdbcType=TINYINT},
      platform_org_id = #{platformOrgId,jdbcType=BIGINT},
      platform_org_name = #{platformOrgName,jdbcType=VARCHAR},
      out_company_id = #{outCompanyId,jdbcType=BIGINT},
      out_company_code = #{outCompanyCode,jdbcType=VARCHAR},
      out_company_name = #{outCompanyName,jdbcType=VARCHAR},
      in_company_id = #{inCompanyId,jdbcType=BIGINT},
      in_company_code = #{inCompanyCode,jdbcType=VARCHAR},
      in_company_name = #{inCompanyName,jdbcType=VARCHAR},
      out_store_id = #{outStoreId,jdbcType=BIGINT},
      out_store_code = #{outStoreCode,jdbcType=VARCHAR},
      out_store_name = #{outStoreName,jdbcType=VARCHAR},
      in_store_id = #{inStoreId,jdbcType=BIGINT},
      in_store_code = #{inStoreCode,jdbcType=VARCHAR},
      in_store_name = #{inStoreName,jdbcType=VARCHAR},
      suggest_allot_quantity_all = #{suggestAllotQuantityAll,jdbcType=DECIMAL},
      suggest_allot_base_cost = #{suggestAllotBaseCost,jdbcType=DECIMAL},
      suggest_allot_breed_all = #{suggestAllotBreedAll,jdbcType=INTEGER},
      `status` = #{status,jdbcType=TINYINT},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_name = #{createdName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_name = #{updatedName,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>