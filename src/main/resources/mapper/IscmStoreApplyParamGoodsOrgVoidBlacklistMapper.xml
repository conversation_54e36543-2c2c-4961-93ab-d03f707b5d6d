<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.IscmStoreApplyParamGoodsOrgVoidBlacklistMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.IscmStoreApplyParamGoodsOrgVoidBlacklist">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="param_code" jdbcType="VARCHAR" property="paramCode" />
    <result column="param_name" jdbcType="VARCHAR" property="paramName" />
    <result column="param_level" jdbcType="INTEGER" property="paramLevel" />
    <result column="org_id" jdbcType="BIGINT" property="orgId" />
    <result column="sap_code" jdbcType="VARCHAR" property="sapCode" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="business_id" jdbcType="BIGINT" property="businessId" />
    <result column="platform_org_id" jdbcType="BIGINT" property="platformOrgId" />
    <result column="platform_name" jdbcType="VARCHAR" property="platformName" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="company_org_id" jdbcType="BIGINT" property="companyOrgId" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="parent_org_id" jdbcType="BIGINT" property="parentOrgId" />
    <result column="parent_org_name" jdbcType="VARCHAR" property="parentOrgName" />
    <result column="store_org_id" jdbcType="BIGINT" property="storeOrgId" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="store_name" jdbcType="VARCHAR" property="storeName" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="bar_code" jdbcType="VARCHAR" property="barCode" />
    <result column="cur_name" jdbcType="VARCHAR" property="curName" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="goods_unit" jdbcType="VARCHAR" property="goodsUnit" />
    <result column="specifications" jdbcType="VARCHAR" property="specifications" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="start_date" jdbcType="TIMESTAMP" property="startDate" />
    <result column="end_date" jdbcType="TIMESTAMP" property="endDate" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="store_type" jdbcType="VARCHAR" property="storeType" />
    <result column="store_level" jdbcType="INTEGER" property="storeLevel" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, param_code, param_name, param_level, org_id, sap_code, org_name, business_id, 
    platform_org_id, platform_name, company_code, company_org_id, company_name, parent_org_id, 
    parent_org_name, store_org_id, store_code, store_name, store_id, goods_no, bar_code, 
    cur_name, goods_name, goods_unit, specifications, manufacturer, start_date, end_date, 
    `status`, extend, version, created_by, created_name, updated_by, updated_name, gmt_create, 
    gmt_update, store_type, store_level
  </sql>
  <select id="selectByExample" parameterType="com.cowell.iscm.entity.IscmStoreApplyParamGoodsOrgVoidBlacklistExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from iscm_store_apply_param_goods_org_void_blacklist
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from iscm_store_apply_param_goods_org_void_blacklist
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from iscm_store_apply_param_goods_org_void_blacklist
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.iscm.entity.IscmStoreApplyParamGoodsOrgVoidBlacklistExample">
    delete from iscm_store_apply_param_goods_org_void_blacklist
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entity.IscmStoreApplyParamGoodsOrgVoidBlacklist" useGeneratedKeys="true">
    insert into iscm_store_apply_param_goods_org_void_blacklist (param_code, param_name, param_level, 
      org_id, sap_code, org_name, 
      business_id, platform_org_id, platform_name, 
      company_code, company_org_id, company_name, 
      parent_org_id, parent_org_name, store_org_id, 
      store_code, store_name, store_id, 
      goods_no, bar_code, cur_name, 
      goods_name, goods_unit, specifications, 
      manufacturer, start_date, end_date, 
      `status`, extend, version, 
      created_by, created_name, updated_by, 
      updated_name, gmt_create, gmt_update, 
      store_type)
    values (#{paramCode,jdbcType=VARCHAR}, #{paramName,jdbcType=VARCHAR}, #{paramLevel,jdbcType=INTEGER}, 
      #{orgId,jdbcType=BIGINT}, #{sapCode,jdbcType=VARCHAR}, #{orgName,jdbcType=VARCHAR}, 
      #{businessId,jdbcType=BIGINT}, #{platformOrgId,jdbcType=BIGINT}, #{platformName,jdbcType=VARCHAR}, 
      #{companyCode,jdbcType=VARCHAR}, #{companyOrgId,jdbcType=BIGINT}, #{companyName,jdbcType=VARCHAR}, 
      #{parentOrgId,jdbcType=BIGINT}, #{parentOrgName,jdbcType=VARCHAR}, #{storeOrgId,jdbcType=BIGINT}, 
      #{storeCode,jdbcType=VARCHAR}, #{storeName,jdbcType=VARCHAR}, #{storeId,jdbcType=BIGINT}, 
      #{goodsNo,jdbcType=VARCHAR}, #{barCode,jdbcType=VARCHAR}, #{curName,jdbcType=VARCHAR}, 
      #{goodsName,jdbcType=VARCHAR}, #{goodsUnit,jdbcType=VARCHAR}, #{specifications,jdbcType=VARCHAR}, 
      #{manufacturer,jdbcType=VARCHAR}, #{startDate,jdbcType=TIMESTAMP}, #{endDate,jdbcType=TIMESTAMP}, 
      #{status,jdbcType=TINYINT}, #{extend,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER}, 
      #{createdBy,jdbcType=BIGINT}, #{createdName,jdbcType=VARCHAR}, #{updatedBy,jdbcType=BIGINT}, 
      #{updatedName,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}, 
      #{storeType,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entity.IscmStoreApplyParamGoodsOrgVoidBlacklist" useGeneratedKeys="true">
    insert into iscm_store_apply_param_goods_org_void_blacklist
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="paramCode != null">
        param_code,
      </if>
      <if test="paramName != null">
        param_name,
      </if>
      <if test="paramLevel != null">
        param_level,
      </if>
      <if test="orgId != null">
        org_id,
      </if>
      <if test="sapCode != null">
        sap_code,
      </if>
      <if test="orgName != null">
        org_name,
      </if>
      <if test="businessId != null">
        business_id,
      </if>
      <if test="platformOrgId != null">
        platform_org_id,
      </if>
      <if test="platformName != null">
        platform_name,
      </if>
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="companyOrgId != null">
        company_org_id,
      </if>
      <if test="companyName != null">
        company_name,
      </if>
      <if test="parentOrgId != null">
        parent_org_id,
      </if>
      <if test="parentOrgName != null">
        parent_org_name,
      </if>
      <if test="storeOrgId != null">
        store_org_id,
      </if>
      <if test="storeCode != null">
        store_code,
      </if>
      <if test="storeName != null">
        store_name,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="goodsNo != null">
        goods_no,
      </if>
      <if test="barCode != null">
        bar_code,
      </if>
      <if test="curName != null">
        cur_name,
      </if>
      <if test="goodsName != null">
        goods_name,
      </if>
      <if test="goodsUnit != null">
        goods_unit,
      </if>
      <if test="specifications != null">
        specifications,
      </if>
      <if test="manufacturer != null">
        manufacturer,
      </if>
      <if test="startDate != null">
        start_date,
      </if>
      <if test="endDate != null">
        end_date,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedName != null">
        updated_name,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="storeType != null">
        store_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="paramCode != null">
        #{paramCode,jdbcType=VARCHAR},
      </if>
      <if test="paramName != null">
        #{paramName,jdbcType=VARCHAR},
      </if>
      <if test="paramLevel != null">
        #{paramLevel,jdbcType=INTEGER},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=BIGINT},
      </if>
      <if test="sapCode != null">
        #{sapCode,jdbcType=VARCHAR},
      </if>
      <if test="orgName != null">
        #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="businessId != null">
        #{businessId,jdbcType=BIGINT},
      </if>
      <if test="platformOrgId != null">
        #{platformOrgId,jdbcType=BIGINT},
      </if>
      <if test="platformName != null">
        #{platformName,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyOrgId != null">
        #{companyOrgId,jdbcType=BIGINT},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="parentOrgId != null">
        #{parentOrgId,jdbcType=BIGINT},
      </if>
      <if test="parentOrgName != null">
        #{parentOrgName,jdbcType=VARCHAR},
      </if>
      <if test="storeOrgId != null">
        #{storeOrgId,jdbcType=BIGINT},
      </if>
      <if test="storeCode != null">
        #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="storeName != null">
        #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="goodsNo != null">
        #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="barCode != null">
        #{barCode,jdbcType=VARCHAR},
      </if>
      <if test="curName != null">
        #{curName,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="goodsUnit != null">
        #{goodsUnit,jdbcType=VARCHAR},
      </if>
      <if test="specifications != null">
        #{specifications,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="startDate != null">
        #{startDate,jdbcType=TIMESTAMP},
      </if>
      <if test="endDate != null">
        #{endDate,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        #{updatedName,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="storeType != null">
        #{storeType,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.iscm.entity.IscmStoreApplyParamGoodsOrgVoidBlacklistExample" resultType="java.lang.Long">
    select count(*) from iscm_store_apply_param_goods_org_void_blacklist
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update iscm_store_apply_param_goods_org_void_blacklist
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.paramCode != null">
        param_code = #{record.paramCode,jdbcType=VARCHAR},
      </if>
      <if test="record.paramName != null">
        param_name = #{record.paramName,jdbcType=VARCHAR},
      </if>
      <if test="record.paramLevel != null">
        param_level = #{record.paramLevel,jdbcType=INTEGER},
      </if>
      <if test="record.orgId != null">
        org_id = #{record.orgId,jdbcType=BIGINT},
      </if>
      <if test="record.sapCode != null">
        sap_code = #{record.sapCode,jdbcType=VARCHAR},
      </if>
      <if test="record.orgName != null">
        org_name = #{record.orgName,jdbcType=VARCHAR},
      </if>
      <if test="record.businessId != null">
        business_id = #{record.businessId,jdbcType=BIGINT},
      </if>
      <if test="record.platformOrgId != null">
        platform_org_id = #{record.platformOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.platformName != null">
        platform_name = #{record.platformName,jdbcType=VARCHAR},
      </if>
      <if test="record.companyCode != null">
        company_code = #{record.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.companyOrgId != null">
        company_org_id = #{record.companyOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.companyName != null">
        company_name = #{record.companyName,jdbcType=VARCHAR},
      </if>
      <if test="record.parentOrgId != null">
        parent_org_id = #{record.parentOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.parentOrgName != null">
        parent_org_name = #{record.parentOrgName,jdbcType=VARCHAR},
      </if>
      <if test="record.storeOrgId != null">
        store_org_id = #{record.storeOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.storeCode != null">
        store_code = #{record.storeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.storeName != null">
        store_name = #{record.storeName,jdbcType=VARCHAR},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.goodsNo != null">
        goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="record.barCode != null">
        bar_code = #{record.barCode,jdbcType=VARCHAR},
      </if>
      <if test="record.curName != null">
        cur_name = #{record.curName,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsName != null">
        goods_name = #{record.goodsName,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsUnit != null">
        goods_unit = #{record.goodsUnit,jdbcType=VARCHAR},
      </if>
      <if test="record.specifications != null">
        specifications = #{record.specifications,jdbcType=VARCHAR},
      </if>
      <if test="record.manufacturer != null">
        manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="record.startDate != null">
        start_date = #{record.startDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.endDate != null">
        end_date = #{record.endDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedName != null">
        updated_name = #{record.updatedName,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.storeType != null">
        store_type = #{record.storeType,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update iscm_store_apply_param_goods_org_void_blacklist
    set id = #{record.id,jdbcType=BIGINT},
      param_code = #{record.paramCode,jdbcType=VARCHAR},
      param_name = #{record.paramName,jdbcType=VARCHAR},
      param_level = #{record.paramLevel,jdbcType=INTEGER},
      org_id = #{record.orgId,jdbcType=BIGINT},
      sap_code = #{record.sapCode,jdbcType=VARCHAR},
      org_name = #{record.orgName,jdbcType=VARCHAR},
      business_id = #{record.businessId,jdbcType=BIGINT},
      platform_org_id = #{record.platformOrgId,jdbcType=BIGINT},
      platform_name = #{record.platformName,jdbcType=VARCHAR},
      company_code = #{record.companyCode,jdbcType=VARCHAR},
      company_org_id = #{record.companyOrgId,jdbcType=BIGINT},
      company_name = #{record.companyName,jdbcType=VARCHAR},
      parent_org_id = #{record.parentOrgId,jdbcType=BIGINT},
      parent_org_name = #{record.parentOrgName,jdbcType=VARCHAR},
      store_org_id = #{record.storeOrgId,jdbcType=BIGINT},
      store_code = #{record.storeCode,jdbcType=VARCHAR},
      store_name = #{record.storeName,jdbcType=VARCHAR},
      store_id = #{record.storeId,jdbcType=BIGINT},
      goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      bar_code = #{record.barCode,jdbcType=VARCHAR},
      cur_name = #{record.curName,jdbcType=VARCHAR},
      goods_name = #{record.goodsName,jdbcType=VARCHAR},
      goods_unit = #{record.goodsUnit,jdbcType=VARCHAR},
      specifications = #{record.specifications,jdbcType=VARCHAR},
      manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      start_date = #{record.startDate,jdbcType=TIMESTAMP},
      end_date = #{record.endDate,jdbcType=TIMESTAMP},
      `status` = #{record.status,jdbcType=TINYINT},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_name = #{record.createdName,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_name = #{record.updatedName,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      store_type = #{record.storeType,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.iscm.entity.IscmStoreApplyParamGoodsOrgVoidBlacklist">
    update iscm_store_apply_param_goods_org_void_blacklist
    <set>
      <if test="paramCode != null">
        param_code = #{paramCode,jdbcType=VARCHAR},
      </if>
      <if test="paramName != null">
        param_name = #{paramName,jdbcType=VARCHAR},
      </if>
      <if test="paramLevel != null">
        param_level = #{paramLevel,jdbcType=INTEGER},
      </if>
      <if test="orgId != null">
        org_id = #{orgId,jdbcType=BIGINT},
      </if>
      <if test="sapCode != null">
        sap_code = #{sapCode,jdbcType=VARCHAR},
      </if>
      <if test="orgName != null">
        org_name = #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=BIGINT},
      </if>
      <if test="platformOrgId != null">
        platform_org_id = #{platformOrgId,jdbcType=BIGINT},
      </if>
      <if test="platformName != null">
        platform_name = #{platformName,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyOrgId != null">
        company_org_id = #{companyOrgId,jdbcType=BIGINT},
      </if>
      <if test="companyName != null">
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="parentOrgId != null">
        parent_org_id = #{parentOrgId,jdbcType=BIGINT},
      </if>
      <if test="parentOrgName != null">
        parent_org_name = #{parentOrgName,jdbcType=VARCHAR},
      </if>
      <if test="storeOrgId != null">
        store_org_id = #{storeOrgId,jdbcType=BIGINT},
      </if>
      <if test="storeCode != null">
        store_code = #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="storeName != null">
        store_name = #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="goodsNo != null">
        goods_no = #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="barCode != null">
        bar_code = #{barCode,jdbcType=VARCHAR},
      </if>
      <if test="curName != null">
        cur_name = #{curName,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        goods_name = #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="goodsUnit != null">
        goods_unit = #{goodsUnit,jdbcType=VARCHAR},
      </if>
      <if test="specifications != null">
        specifications = #{specifications,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        manufacturer = #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="startDate != null">
        start_date = #{startDate,jdbcType=TIMESTAMP},
      </if>
      <if test="endDate != null">
        end_date = #{endDate,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        updated_name = #{updatedName,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="storeType != null">
        store_type = #{storeType,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.iscm.entity.IscmStoreApplyParamGoodsOrgVoidBlacklist">
    update iscm_store_apply_param_goods_org_void_blacklist
    set param_code = #{paramCode,jdbcType=VARCHAR},
      param_name = #{paramName,jdbcType=VARCHAR},
      param_level = #{paramLevel,jdbcType=INTEGER},
      org_id = #{orgId,jdbcType=BIGINT},
      sap_code = #{sapCode,jdbcType=VARCHAR},
      org_name = #{orgName,jdbcType=VARCHAR},
      business_id = #{businessId,jdbcType=BIGINT},
      platform_org_id = #{platformOrgId,jdbcType=BIGINT},
      platform_name = #{platformName,jdbcType=VARCHAR},
      company_code = #{companyCode,jdbcType=VARCHAR},
      company_org_id = #{companyOrgId,jdbcType=BIGINT},
      company_name = #{companyName,jdbcType=VARCHAR},
      parent_org_id = #{parentOrgId,jdbcType=BIGINT},
      parent_org_name = #{parentOrgName,jdbcType=VARCHAR},
      store_org_id = #{storeOrgId,jdbcType=BIGINT},
      store_code = #{storeCode,jdbcType=VARCHAR},
      store_name = #{storeName,jdbcType=VARCHAR},
      store_id = #{storeId,jdbcType=BIGINT},
      goods_no = #{goodsNo,jdbcType=VARCHAR},
      bar_code = #{barCode,jdbcType=VARCHAR},
      cur_name = #{curName,jdbcType=VARCHAR},
      goods_name = #{goodsName,jdbcType=VARCHAR},
      goods_unit = #{goodsUnit,jdbcType=VARCHAR},
      specifications = #{specifications,jdbcType=VARCHAR},
      manufacturer = #{manufacturer,jdbcType=VARCHAR},
      start_date = #{startDate,jdbcType=TIMESTAMP},
      end_date = #{endDate,jdbcType=TIMESTAMP},
      `status` = #{status,jdbcType=TINYINT},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_name = #{createdName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_name = #{updatedName,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      store_type = #{storeType,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>