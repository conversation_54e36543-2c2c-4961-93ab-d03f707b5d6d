<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.IscmStoreReturnExecuteOrderMainMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.IscmStoreReturnExecuteOrderMain">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="return_type" jdbcType="INTEGER" property="returnType" />
    <result column="return_order_no" jdbcType="VARCHAR" property="returnOrderNo" />
    <result column="pos_return_order_no" jdbcType="VARCHAR" property="posReturnOrderNo" />
    <result column="platform_org_id" jdbcType="BIGINT" property="platformOrgId" />
    <result column="platform_org_name" jdbcType="VARCHAR" property="platformOrgName" />
    <result column="company_org_id" jdbcType="BIGINT" property="companyOrgId" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="store_org_id" jdbcType="BIGINT" property="storeOrgId" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="store_name" jdbcType="VARCHAR" property="storeName" />
    <result column="warehouse_code" jdbcType="VARCHAR" property="warehouseCode" />
    <result column="warehouse_name" jdbcType="VARCHAR" property="warehouseName" />
    <result column="return_goods_quantity_total" jdbcType="INTEGER" property="returnGoodsQuantityTotal" />
    <result column="return_quantity_total" jdbcType="DECIMAL" property="returnQuantityTotal" />
    <result column="return_cost_amount_total" jdbcType="DECIMAL" property="returnCostAmountTotal" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
    <result column="issue_by" jdbcType="BIGINT" property="issueBy" />
    <result column="issue_name" jdbcType="VARCHAR" property="issueName" />
    <result column="gmt_issue" jdbcType="TIMESTAMP" property="gmtIssue" />
    <result column="issue_validity_days" jdbcType="INTEGER" property="issueValidityDays" />
    <result column="issue_return_quantity_total" jdbcType="DECIMAL" property="issueReturnQuantityTotal" />
    <result column="issue_return_amount_total" jdbcType="DECIMAL" property="issueReturnAmountTotal" />
    <result column="real_return_quantity_total" jdbcType="DECIMAL" property="realReturnQuantityTotal" />
    <result column="process_status" jdbcType="TINYINT" property="processStatus" />
    <result column="process_status_all" jdbcType="VARCHAR" property="processStatusAll" />
    <result column="created_month" jdbcType="INTEGER" property="createdMonth" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, return_type, return_order_no, pos_return_order_no, platform_org_id, platform_org_name, 
    company_org_id, company_code, company_name, store_org_id, store_code, store_name, 
    warehouse_code, warehouse_name, return_goods_quantity_total, return_quantity_total, 
    return_cost_amount_total, `status`, extend, version, gmt_create, gmt_update, created_by, 
    created_name, updated_by, updated_name, issue_by, issue_name, gmt_issue, issue_validity_days, 
    issue_return_quantity_total, issue_return_amount_total, real_return_quantity_total, 
    process_status, process_status_all, created_month
  </sql>
  <select id="selectByExample" parameterType="com.cowell.iscm.entity.IscmStoreReturnExecuteOrderMainExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from iscm_store_return_execute_order_main
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from iscm_store_return_execute_order_main
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from iscm_store_return_execute_order_main
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.iscm.entity.IscmStoreReturnExecuteOrderMainExample">
    delete from iscm_store_return_execute_order_main
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entity.IscmStoreReturnExecuteOrderMain" useGeneratedKeys="true">
    insert into iscm_store_return_execute_order_main (return_type, return_order_no, pos_return_order_no, 
      platform_org_id, platform_org_name, company_org_id, 
      company_code, company_name, store_org_id, 
      store_code, store_name, warehouse_code, 
      warehouse_name, return_goods_quantity_total, 
      return_quantity_total, return_cost_amount_total, 
      `status`, extend, version, 
      gmt_create, gmt_update, created_by, 
      created_name, updated_by, updated_name, 
      issue_by, issue_name, gmt_issue, 
      issue_validity_days, issue_return_quantity_total, 
      issue_return_amount_total, real_return_quantity_total, 
      process_status, process_status_all, created_month
      )
    values (#{returnType,jdbcType=INTEGER}, #{returnOrderNo,jdbcType=VARCHAR}, #{posReturnOrderNo,jdbcType=VARCHAR}, 
      #{platformOrgId,jdbcType=BIGINT}, #{platformOrgName,jdbcType=VARCHAR}, #{companyOrgId,jdbcType=BIGINT}, 
      #{companyCode,jdbcType=VARCHAR}, #{companyName,jdbcType=VARCHAR}, #{storeOrgId,jdbcType=BIGINT}, 
      #{storeCode,jdbcType=VARCHAR}, #{storeName,jdbcType=VARCHAR}, #{warehouseCode,jdbcType=VARCHAR}, 
      #{warehouseName,jdbcType=VARCHAR}, #{returnGoodsQuantityTotal,jdbcType=INTEGER}, 
      #{returnQuantityTotal,jdbcType=DECIMAL}, #{returnCostAmountTotal,jdbcType=DECIMAL}, 
      #{status,jdbcType=TINYINT}, #{extend,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=BIGINT}, 
      #{createdName,jdbcType=VARCHAR}, #{updatedBy,jdbcType=BIGINT}, #{updatedName,jdbcType=VARCHAR}, 
      #{issueBy,jdbcType=BIGINT}, #{issueName,jdbcType=VARCHAR}, #{gmtIssue,jdbcType=TIMESTAMP}, 
      #{issueValidityDays,jdbcType=INTEGER}, #{issueReturnQuantityTotal,jdbcType=DECIMAL}, 
      #{issueReturnAmountTotal,jdbcType=DECIMAL}, #{realReturnQuantityTotal,jdbcType=DECIMAL}, 
      #{processStatus,jdbcType=TINYINT}, #{processStatusAll,jdbcType=VARCHAR}, #{createdMonth,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entity.IscmStoreReturnExecuteOrderMain" useGeneratedKeys="true">
    insert into iscm_store_return_execute_order_main
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="returnType != null">
        return_type,
      </if>
      <if test="returnOrderNo != null">
        return_order_no,
      </if>
      <if test="posReturnOrderNo != null">
        pos_return_order_no,
      </if>
      <if test="platformOrgId != null">
        platform_org_id,
      </if>
      <if test="platformOrgName != null">
        platform_org_name,
      </if>
      <if test="companyOrgId != null">
        company_org_id,
      </if>
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="companyName != null">
        company_name,
      </if>
      <if test="storeOrgId != null">
        store_org_id,
      </if>
      <if test="storeCode != null">
        store_code,
      </if>
      <if test="storeName != null">
        store_name,
      </if>
      <if test="warehouseCode != null">
        warehouse_code,
      </if>
      <if test="warehouseName != null">
        warehouse_name,
      </if>
      <if test="returnGoodsQuantityTotal != null">
        return_goods_quantity_total,
      </if>
      <if test="returnQuantityTotal != null">
        return_quantity_total,
      </if>
      <if test="returnCostAmountTotal != null">
        return_cost_amount_total,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedName != null">
        updated_name,
      </if>
      <if test="issueBy != null">
        issue_by,
      </if>
      <if test="issueName != null">
        issue_name,
      </if>
      <if test="gmtIssue != null">
        gmt_issue,
      </if>
      <if test="issueValidityDays != null">
        issue_validity_days,
      </if>
      <if test="issueReturnQuantityTotal != null">
        issue_return_quantity_total,
      </if>
      <if test="issueReturnAmountTotal != null">
        issue_return_amount_total,
      </if>
      <if test="realReturnQuantityTotal != null">
        real_return_quantity_total,
      </if>
      <if test="processStatus != null">
        process_status,
      </if>
      <if test="processStatusAll != null">
        process_status_all,
      </if>
      <if test="createdMonth != null">
        created_month,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="returnType != null">
        #{returnType,jdbcType=INTEGER},
      </if>
      <if test="returnOrderNo != null">
        #{returnOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="posReturnOrderNo != null">
        #{posReturnOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="platformOrgId != null">
        #{platformOrgId,jdbcType=BIGINT},
      </if>
      <if test="platformOrgName != null">
        #{platformOrgName,jdbcType=VARCHAR},
      </if>
      <if test="companyOrgId != null">
        #{companyOrgId,jdbcType=BIGINT},
      </if>
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="storeOrgId != null">
        #{storeOrgId,jdbcType=BIGINT},
      </if>
      <if test="storeCode != null">
        #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="storeName != null">
        #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="warehouseCode != null">
        #{warehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="warehouseName != null">
        #{warehouseName,jdbcType=VARCHAR},
      </if>
      <if test="returnGoodsQuantityTotal != null">
        #{returnGoodsQuantityTotal,jdbcType=INTEGER},
      </if>
      <if test="returnQuantityTotal != null">
        #{returnQuantityTotal,jdbcType=DECIMAL},
      </if>
      <if test="returnCostAmountTotal != null">
        #{returnCostAmountTotal,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        #{updatedName,jdbcType=VARCHAR},
      </if>
      <if test="issueBy != null">
        #{issueBy,jdbcType=BIGINT},
      </if>
      <if test="issueName != null">
        #{issueName,jdbcType=VARCHAR},
      </if>
      <if test="gmtIssue != null">
        #{gmtIssue,jdbcType=TIMESTAMP},
      </if>
      <if test="issueValidityDays != null">
        #{issueValidityDays,jdbcType=INTEGER},
      </if>
      <if test="issueReturnQuantityTotal != null">
        #{issueReturnQuantityTotal,jdbcType=DECIMAL},
      </if>
      <if test="issueReturnAmountTotal != null">
        #{issueReturnAmountTotal,jdbcType=DECIMAL},
      </if>
      <if test="realReturnQuantityTotal != null">
        #{realReturnQuantityTotal,jdbcType=DECIMAL},
      </if>
      <if test="processStatus != null">
        #{processStatus,jdbcType=TINYINT},
      </if>
      <if test="processStatusAll != null">
        #{processStatusAll,jdbcType=VARCHAR},
      </if>
      <if test="createdMonth != null">
        #{createdMonth,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.iscm.entity.IscmStoreReturnExecuteOrderMainExample" resultType="java.lang.Long">
    select count(*) from iscm_store_return_execute_order_main
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update iscm_store_return_execute_order_main
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.returnType != null">
        return_type = #{record.returnType,jdbcType=INTEGER},
      </if>
      <if test="record.returnOrderNo != null">
        return_order_no = #{record.returnOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.posReturnOrderNo != null">
        pos_return_order_no = #{record.posReturnOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.platformOrgId != null">
        platform_org_id = #{record.platformOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.platformOrgName != null">
        platform_org_name = #{record.platformOrgName,jdbcType=VARCHAR},
      </if>
      <if test="record.companyOrgId != null">
        company_org_id = #{record.companyOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.companyCode != null">
        company_code = #{record.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.companyName != null">
        company_name = #{record.companyName,jdbcType=VARCHAR},
      </if>
      <if test="record.storeOrgId != null">
        store_org_id = #{record.storeOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.storeCode != null">
        store_code = #{record.storeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.storeName != null">
        store_name = #{record.storeName,jdbcType=VARCHAR},
      </if>
      <if test="record.warehouseCode != null">
        warehouse_code = #{record.warehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="record.warehouseName != null">
        warehouse_name = #{record.warehouseName,jdbcType=VARCHAR},
      </if>
      <if test="record.returnGoodsQuantityTotal != null">
        return_goods_quantity_total = #{record.returnGoodsQuantityTotal,jdbcType=INTEGER},
      </if>
      <if test="record.returnQuantityTotal != null">
        return_quantity_total = #{record.returnQuantityTotal,jdbcType=DECIMAL},
      </if>
      <if test="record.returnCostAmountTotal != null">
        return_cost_amount_total = #{record.returnCostAmountTotal,jdbcType=DECIMAL},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedName != null">
        updated_name = #{record.updatedName,jdbcType=VARCHAR},
      </if>
      <if test="record.issueBy != null">
        issue_by = #{record.issueBy,jdbcType=BIGINT},
      </if>
      <if test="record.issueName != null">
        issue_name = #{record.issueName,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtIssue != null">
        gmt_issue = #{record.gmtIssue,jdbcType=TIMESTAMP},
      </if>
      <if test="record.issueValidityDays != null">
        issue_validity_days = #{record.issueValidityDays,jdbcType=INTEGER},
      </if>
      <if test="record.issueReturnQuantityTotal != null">
        issue_return_quantity_total = #{record.issueReturnQuantityTotal,jdbcType=DECIMAL},
      </if>
      <if test="record.issueReturnAmountTotal != null">
        issue_return_amount_total = #{record.issueReturnAmountTotal,jdbcType=DECIMAL},
      </if>
      <if test="record.realReturnQuantityTotal != null">
        real_return_quantity_total = #{record.realReturnQuantityTotal,jdbcType=DECIMAL},
      </if>
      <if test="record.processStatus != null">
        process_status = #{record.processStatus,jdbcType=TINYINT},
      </if>
      <if test="record.processStatusAll != null">
        process_status_all = #{record.processStatusAll,jdbcType=VARCHAR},
      </if>
      <if test="record.createdMonth != null">
        created_month = #{record.createdMonth,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update iscm_store_return_execute_order_main
    set id = #{record.id,jdbcType=BIGINT},
      return_type = #{record.returnType,jdbcType=INTEGER},
      return_order_no = #{record.returnOrderNo,jdbcType=VARCHAR},
      pos_return_order_no = #{record.posReturnOrderNo,jdbcType=VARCHAR},
      platform_org_id = #{record.platformOrgId,jdbcType=BIGINT},
      platform_org_name = #{record.platformOrgName,jdbcType=VARCHAR},
      company_org_id = #{record.companyOrgId,jdbcType=BIGINT},
      company_code = #{record.companyCode,jdbcType=VARCHAR},
      company_name = #{record.companyName,jdbcType=VARCHAR},
      store_org_id = #{record.storeOrgId,jdbcType=BIGINT},
      store_code = #{record.storeCode,jdbcType=VARCHAR},
      store_name = #{record.storeName,jdbcType=VARCHAR},
      warehouse_code = #{record.warehouseCode,jdbcType=VARCHAR},
      warehouse_name = #{record.warehouseName,jdbcType=VARCHAR},
      return_goods_quantity_total = #{record.returnGoodsQuantityTotal,jdbcType=INTEGER},
      return_quantity_total = #{record.returnQuantityTotal,jdbcType=DECIMAL},
      return_cost_amount_total = #{record.returnCostAmountTotal,jdbcType=DECIMAL},
      `status` = #{record.status,jdbcType=TINYINT},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_name = #{record.createdName,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_name = #{record.updatedName,jdbcType=VARCHAR},
      issue_by = #{record.issueBy,jdbcType=BIGINT},
      issue_name = #{record.issueName,jdbcType=VARCHAR},
      gmt_issue = #{record.gmtIssue,jdbcType=TIMESTAMP},
      issue_validity_days = #{record.issueValidityDays,jdbcType=INTEGER},
      issue_return_quantity_total = #{record.issueReturnQuantityTotal,jdbcType=DECIMAL},
      issue_return_amount_total = #{record.issueReturnAmountTotal,jdbcType=DECIMAL},
      real_return_quantity_total = #{record.realReturnQuantityTotal,jdbcType=DECIMAL},
      process_status = #{record.processStatus,jdbcType=TINYINT},
      process_status_all = #{record.processStatusAll,jdbcType=VARCHAR},
      created_month = #{record.createdMonth,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.iscm.entity.IscmStoreReturnExecuteOrderMain">
    update iscm_store_return_execute_order_main
    <set>
      <if test="returnType != null">
        return_type = #{returnType,jdbcType=INTEGER},
      </if>
      <if test="returnOrderNo != null">
        return_order_no = #{returnOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="posReturnOrderNo != null">
        pos_return_order_no = #{posReturnOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="platformOrgId != null">
        platform_org_id = #{platformOrgId,jdbcType=BIGINT},
      </if>
      <if test="platformOrgName != null">
        platform_org_name = #{platformOrgName,jdbcType=VARCHAR},
      </if>
      <if test="companyOrgId != null">
        company_org_id = #{companyOrgId,jdbcType=BIGINT},
      </if>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="storeOrgId != null">
        store_org_id = #{storeOrgId,jdbcType=BIGINT},
      </if>
      <if test="storeCode != null">
        store_code = #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="storeName != null">
        store_name = #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="warehouseCode != null">
        warehouse_code = #{warehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="warehouseName != null">
        warehouse_name = #{warehouseName,jdbcType=VARCHAR},
      </if>
      <if test="returnGoodsQuantityTotal != null">
        return_goods_quantity_total = #{returnGoodsQuantityTotal,jdbcType=INTEGER},
      </if>
      <if test="returnQuantityTotal != null">
        return_quantity_total = #{returnQuantityTotal,jdbcType=DECIMAL},
      </if>
      <if test="returnCostAmountTotal != null">
        return_cost_amount_total = #{returnCostAmountTotal,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        updated_name = #{updatedName,jdbcType=VARCHAR},
      </if>
      <if test="issueBy != null">
        issue_by = #{issueBy,jdbcType=BIGINT},
      </if>
      <if test="issueName != null">
        issue_name = #{issueName,jdbcType=VARCHAR},
      </if>
      <if test="gmtIssue != null">
        gmt_issue = #{gmtIssue,jdbcType=TIMESTAMP},
      </if>
      <if test="issueValidityDays != null">
        issue_validity_days = #{issueValidityDays,jdbcType=INTEGER},
      </if>
      <if test="issueReturnQuantityTotal != null">
        issue_return_quantity_total = #{issueReturnQuantityTotal,jdbcType=DECIMAL},
      </if>
      <if test="issueReturnAmountTotal != null">
        issue_return_amount_total = #{issueReturnAmountTotal,jdbcType=DECIMAL},
      </if>
      <if test="realReturnQuantityTotal != null">
        real_return_quantity_total = #{realReturnQuantityTotal,jdbcType=DECIMAL},
      </if>
      <if test="processStatus != null">
        process_status = #{processStatus,jdbcType=TINYINT},
      </if>
      <if test="processStatusAll != null">
        process_status_all = #{processStatusAll,jdbcType=VARCHAR},
      </if>
      <if test="createdMonth != null">
        created_month = #{createdMonth,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.iscm.entity.IscmStoreReturnExecuteOrderMain">
    update iscm_store_return_execute_order_main
    set return_type = #{returnType,jdbcType=INTEGER},
      return_order_no = #{returnOrderNo,jdbcType=VARCHAR},
      pos_return_order_no = #{posReturnOrderNo,jdbcType=VARCHAR},
      platform_org_id = #{platformOrgId,jdbcType=BIGINT},
      platform_org_name = #{platformOrgName,jdbcType=VARCHAR},
      company_org_id = #{companyOrgId,jdbcType=BIGINT},
      company_code = #{companyCode,jdbcType=VARCHAR},
      company_name = #{companyName,jdbcType=VARCHAR},
      store_org_id = #{storeOrgId,jdbcType=BIGINT},
      store_code = #{storeCode,jdbcType=VARCHAR},
      store_name = #{storeName,jdbcType=VARCHAR},
      warehouse_code = #{warehouseCode,jdbcType=VARCHAR},
      warehouse_name = #{warehouseName,jdbcType=VARCHAR},
      return_goods_quantity_total = #{returnGoodsQuantityTotal,jdbcType=INTEGER},
      return_quantity_total = #{returnQuantityTotal,jdbcType=DECIMAL},
      return_cost_amount_total = #{returnCostAmountTotal,jdbcType=DECIMAL},
      `status` = #{status,jdbcType=TINYINT},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_name = #{createdName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_name = #{updatedName,jdbcType=VARCHAR},
      issue_by = #{issueBy,jdbcType=BIGINT},
      issue_name = #{issueName,jdbcType=VARCHAR},
      gmt_issue = #{gmtIssue,jdbcType=TIMESTAMP},
      issue_validity_days = #{issueValidityDays,jdbcType=INTEGER},
      issue_return_quantity_total = #{issueReturnQuantityTotal,jdbcType=DECIMAL},
      issue_return_amount_total = #{issueReturnAmountTotal,jdbcType=DECIMAL},
      real_return_quantity_total = #{realReturnQuantityTotal,jdbcType=DECIMAL},
      process_status = #{processStatus,jdbcType=TINYINT},
      process_status_all = #{processStatusAll,jdbcType=VARCHAR},
      created_month = #{createdMonth,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>