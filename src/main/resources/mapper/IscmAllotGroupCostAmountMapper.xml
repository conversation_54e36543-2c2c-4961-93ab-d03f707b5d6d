<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.IscmAllotGroupCostAmountMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.IscmAllotGroupCostAmount">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="bdp_code" jdbcType="VARCHAR" property="bdpCode" />
    <result column="in_allot_group" jdbcType="VARCHAR" property="inAllotGroup" />
    <result column="out_allot_group" jdbcType="VARCHAR" property="outAllotGroup" />
    <result column="same_group_cost_amount" jdbcType="DECIMAL" property="sameGroupCostAmount" />
    <result column="diff_group_cost_amount" jdbcType="DECIMAL" property="diffGroupCostAmount" />
    <result column="cost_amount" jdbcType="DECIMAL" property="costAmount" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, company_id, company_name, company_code, bdp_code, in_allot_group, out_allot_group, 
    same_group_cost_amount, diff_group_cost_amount, cost_amount, `status`, gmt_create, 
    gmt_update, extend, version, created_by, created_name, updated_by, updated_name
  </sql>
  <select id="selectByExample" parameterType="com.cowell.iscm.entity.IscmAllotGroupCostAmountExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from iscm_allot_group_cost_amount
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from iscm_allot_group_cost_amount
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from iscm_allot_group_cost_amount
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.iscm.entity.IscmAllotGroupCostAmountExample">
    delete from iscm_allot_group_cost_amount
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entity.IscmAllotGroupCostAmount" useGeneratedKeys="true">
    insert into iscm_allot_group_cost_amount (company_id, company_name, company_code, 
      bdp_code, in_allot_group, out_allot_group, 
      same_group_cost_amount, diff_group_cost_amount, 
      cost_amount, `status`, gmt_create, 
      gmt_update, extend, version, 
      created_by, created_name, updated_by, 
      updated_name)
    values (#{companyId,jdbcType=BIGINT}, #{companyName,jdbcType=VARCHAR}, #{companyCode,jdbcType=VARCHAR}, 
      #{bdpCode,jdbcType=VARCHAR}, #{inAllotGroup,jdbcType=VARCHAR}, #{outAllotGroup,jdbcType=VARCHAR}, 
      #{sameGroupCostAmount,jdbcType=DECIMAL}, #{diffGroupCostAmount,jdbcType=DECIMAL}, 
      #{costAmount,jdbcType=DECIMAL}, #{status,jdbcType=TINYINT}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtUpdate,jdbcType=TIMESTAMP}, #{extend,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER}, 
      #{createdBy,jdbcType=BIGINT}, #{createdName,jdbcType=VARCHAR}, #{updatedBy,jdbcType=BIGINT}, 
      #{updatedName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entity.IscmAllotGroupCostAmount" useGeneratedKeys="true">
    insert into iscm_allot_group_cost_amount
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="companyName != null">
        company_name,
      </if>
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="bdpCode != null">
        bdp_code,
      </if>
      <if test="inAllotGroup != null">
        in_allot_group,
      </if>
      <if test="outAllotGroup != null">
        out_allot_group,
      </if>
      <if test="sameGroupCostAmount != null">
        same_group_cost_amount,
      </if>
      <if test="diffGroupCostAmount != null">
        diff_group_cost_amount,
      </if>
      <if test="costAmount != null">
        cost_amount,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedName != null">
        updated_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="bdpCode != null">
        #{bdpCode,jdbcType=VARCHAR},
      </if>
      <if test="inAllotGroup != null">
        #{inAllotGroup,jdbcType=VARCHAR},
      </if>
      <if test="outAllotGroup != null">
        #{outAllotGroup,jdbcType=VARCHAR},
      </if>
      <if test="sameGroupCostAmount != null">
        #{sameGroupCostAmount,jdbcType=DECIMAL},
      </if>
      <if test="diffGroupCostAmount != null">
        #{diffGroupCostAmount,jdbcType=DECIMAL},
      </if>
      <if test="costAmount != null">
        #{costAmount,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        #{updatedName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.iscm.entity.IscmAllotGroupCostAmountExample" resultType="java.lang.Long">
    select count(*) from iscm_allot_group_cost_amount
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update iscm_allot_group_cost_amount
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=BIGINT},
      </if>
      <if test="record.companyName != null">
        company_name = #{record.companyName,jdbcType=VARCHAR},
      </if>
      <if test="record.companyCode != null">
        company_code = #{record.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.bdpCode != null">
        bdp_code = #{record.bdpCode,jdbcType=VARCHAR},
      </if>
      <if test="record.inAllotGroup != null">
        in_allot_group = #{record.inAllotGroup,jdbcType=VARCHAR},
      </if>
      <if test="record.outAllotGroup != null">
        out_allot_group = #{record.outAllotGroup,jdbcType=VARCHAR},
      </if>
      <if test="record.sameGroupCostAmount != null">
        same_group_cost_amount = #{record.sameGroupCostAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.diffGroupCostAmount != null">
        diff_group_cost_amount = #{record.diffGroupCostAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.costAmount != null">
        cost_amount = #{record.costAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedName != null">
        updated_name = #{record.updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update iscm_allot_group_cost_amount
    set id = #{record.id,jdbcType=BIGINT},
      company_id = #{record.companyId,jdbcType=BIGINT},
      company_name = #{record.companyName,jdbcType=VARCHAR},
      company_code = #{record.companyCode,jdbcType=VARCHAR},
      bdp_code = #{record.bdpCode,jdbcType=VARCHAR},
      in_allot_group = #{record.inAllotGroup,jdbcType=VARCHAR},
      out_allot_group = #{record.outAllotGroup,jdbcType=VARCHAR},
      same_group_cost_amount = #{record.sameGroupCostAmount,jdbcType=DECIMAL},
      diff_group_cost_amount = #{record.diffGroupCostAmount,jdbcType=DECIMAL},
      cost_amount = #{record.costAmount,jdbcType=DECIMAL},
      `status` = #{record.status,jdbcType=TINYINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_name = #{record.createdName,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_name = #{record.updatedName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.iscm.entity.IscmAllotGroupCostAmount">
    update iscm_allot_group_cost_amount
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="companyName != null">
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="bdpCode != null">
        bdp_code = #{bdpCode,jdbcType=VARCHAR},
      </if>
      <if test="inAllotGroup != null">
        in_allot_group = #{inAllotGroup,jdbcType=VARCHAR},
      </if>
      <if test="outAllotGroup != null">
        out_allot_group = #{outAllotGroup,jdbcType=VARCHAR},
      </if>
      <if test="sameGroupCostAmount != null">
        same_group_cost_amount = #{sameGroupCostAmount,jdbcType=DECIMAL},
      </if>
      <if test="diffGroupCostAmount != null">
        diff_group_cost_amount = #{diffGroupCostAmount,jdbcType=DECIMAL},
      </if>
      <if test="costAmount != null">
        cost_amount = #{costAmount,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        updated_name = #{updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.iscm.entity.IscmAllotGroupCostAmount">
    update iscm_allot_group_cost_amount
    set company_id = #{companyId,jdbcType=BIGINT},
      company_name = #{companyName,jdbcType=VARCHAR},
      company_code = #{companyCode,jdbcType=VARCHAR},
      bdp_code = #{bdpCode,jdbcType=VARCHAR},
      in_allot_group = #{inAllotGroup,jdbcType=VARCHAR},
      out_allot_group = #{outAllotGroup,jdbcType=VARCHAR},
      same_group_cost_amount = #{sameGroupCostAmount,jdbcType=DECIMAL},
      diff_group_cost_amount = #{diffGroupCostAmount,jdbcType=DECIMAL},
      cost_amount = #{costAmount,jdbcType=DECIMAL},
      `status` = #{status,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_name = #{createdName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_name = #{updatedName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>