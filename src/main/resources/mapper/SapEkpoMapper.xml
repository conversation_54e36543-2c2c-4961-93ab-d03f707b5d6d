<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.SapEkpoMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.SapEkpo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="MANDT" jdbcType="VARCHAR" property="mandt" />
    <result column="EBELN" jdbcType="VARCHAR" property="ebeln" />
    <result column="EBELP" jdbcType="VARCHAR" property="ebelp" />
    <result column="LOEKZ" jdbcType="VARCHAR" property="loekz" />
    <result column="STATU" jdbcType="VARCHAR" property="statu" />
    <result column="AEDAT" jdbcType="VARCHAR" property="aedat" />
    <result column="TXZ01" jdbcType="VARCHAR" property="txz01" />
    <result column="MATNR" jdbcType="VARCHAR" property="matnr" />
    <result column="EMATN" jdbcType="VARCHAR" property="ematn" />
    <result column="BUKRS" jdbcType="VARCHAR" property="bukrs" />
    <result column="WERKS" jdbcType="VARCHAR" property="werks" />
    <result column="LGORT" jdbcType="VARCHAR" property="lgort" />
    <result column="BEDNR" jdbcType="VARCHAR" property="bednr" />
    <result column="MATKL" jdbcType="VARCHAR" property="matkl" />
    <result column="INFNR" jdbcType="VARCHAR" property="infnr" />
    <result column="IDNLF" jdbcType="VARCHAR" property="idnlf" />
    <result column="KTMNG" jdbcType="DECIMAL" property="ktmng" />
    <result column="MENGE" jdbcType="DECIMAL" property="menge" />
    <result column="MEINS" jdbcType="VARCHAR" property="meins" />
    <result column="BPRME" jdbcType="VARCHAR" property="bprme" />
    <result column="BPUMZ" jdbcType="DECIMAL" property="bpumz" />
    <result column="BPUMN" jdbcType="DECIMAL" property="bpumn" />
    <result column="UMREZ" jdbcType="DECIMAL" property="umrez" />
    <result column="UMREN" jdbcType="DECIMAL" property="umren" />
    <result column="NETPR" jdbcType="DECIMAL" property="netpr" />
    <result column="PEINH" jdbcType="DECIMAL" property="peinh" />
    <result column="NETWR" jdbcType="DECIMAL" property="netwr" />
    <result column="BRTWR" jdbcType="DECIMAL" property="brtwr" />
    <result column="AGDAT" jdbcType="VARCHAR" property="agdat" />
    <result column="WEBAZ" jdbcType="DECIMAL" property="webaz" />
    <result column="MWSKZ" jdbcType="VARCHAR" property="mwskz" />
    <result column="BONUS" jdbcType="VARCHAR" property="bonus" />
    <result column="INSMK" jdbcType="VARCHAR" property="insmk" />
    <result column="SPINF" jdbcType="VARCHAR" property="spinf" />
    <result column="PRSDR" jdbcType="VARCHAR" property="prsdr" />
    <result column="SCHPR" jdbcType="VARCHAR" property="schpr" />
    <result column="MAHNZ" jdbcType="DECIMAL" property="mahnz" />
    <result column="MAHN1" jdbcType="DECIMAL" property="mahn1" />
    <result column="MAHN2" jdbcType="DECIMAL" property="mahn2" />
    <result column="MAHN3" jdbcType="DECIMAL" property="mahn3" />
    <result column="UEBTO" jdbcType="DECIMAL" property="uebto" />
    <result column="UEBTK" jdbcType="VARCHAR" property="uebtk" />
    <result column="UNTTO" jdbcType="DECIMAL" property="untto" />
    <result column="BWTAR" jdbcType="VARCHAR" property="bwtar" />
    <result column="BWTTY" jdbcType="VARCHAR" property="bwtty" />
    <result column="ABSKZ" jdbcType="VARCHAR" property="abskz" />
    <result column="AGMEM" jdbcType="VARCHAR" property="agmem" />
    <result column="ELIKZ" jdbcType="VARCHAR" property="elikz" />
    <result column="EREKZ" jdbcType="VARCHAR" property="erekz" />
    <result column="PSTYP" jdbcType="VARCHAR" property="pstyp" />
    <result column="KNTTP" jdbcType="VARCHAR" property="knttp" />
    <result column="KZVBR" jdbcType="VARCHAR" property="kzvbr" />
    <result column="VRTKZ" jdbcType="VARCHAR" property="vrtkz" />
    <result column="TWRKZ" jdbcType="VARCHAR" property="twrkz" />
    <result column="WEPOS" jdbcType="VARCHAR" property="wepos" />
    <result column="WEUNB" jdbcType="VARCHAR" property="weunb" />
    <result column="REPOS" jdbcType="VARCHAR" property="repos" />
    <result column="WEBRE" jdbcType="VARCHAR" property="webre" />
    <result column="KZABS" jdbcType="VARCHAR" property="kzabs" />
    <result column="LABNR" jdbcType="VARCHAR" property="labnr" />
    <result column="KONNR" jdbcType="VARCHAR" property="konnr" />
    <result column="KTPNR" jdbcType="VARCHAR" property="ktpnr" />
    <result column="ABDAT" jdbcType="VARCHAR" property="abdat" />
    <result column="ABFTZ" jdbcType="DECIMAL" property="abftz" />
    <result column="ETFZ1" jdbcType="DECIMAL" property="etfz1" />
    <result column="ETFZ2" jdbcType="DECIMAL" property="etfz2" />
    <result column="KZSTU" jdbcType="VARCHAR" property="kzstu" />
    <result column="NOTKZ" jdbcType="VARCHAR" property="notkz" />
    <result column="LMEIN" jdbcType="VARCHAR" property="lmein" />
    <result column="EVERS" jdbcType="VARCHAR" property="evers" />
    <result column="ZWERT" jdbcType="DECIMAL" property="zwert" />
    <result column="NAVNW" jdbcType="DECIMAL" property="navnw" />
    <result column="ABMNG" jdbcType="DECIMAL" property="abmng" />
    <result column="PRDAT" jdbcType="VARCHAR" property="prdat" />
    <result column="BSTYP" jdbcType="VARCHAR" property="bstyp" />
    <result column="EFFWR" jdbcType="DECIMAL" property="effwr" />
    <result column="XOBLR" jdbcType="VARCHAR" property="xoblr" />
    <result column="KUNNR" jdbcType="VARCHAR" property="kunnr" />
    <result column="ADRNR" jdbcType="VARCHAR" property="adrnr" />
    <result column="EKKOL" jdbcType="VARCHAR" property="ekkol" />
    <result column="SKTOF" jdbcType="VARCHAR" property="sktof" />
    <result column="STAFO" jdbcType="VARCHAR" property="stafo" />
    <result column="PLIFZ" jdbcType="DECIMAL" property="plifz" />
    <result column="NTGEW" jdbcType="DECIMAL" property="ntgew" />
    <result column="GEWEI" jdbcType="VARCHAR" property="gewei" />
    <result column="TXJCD" jdbcType="VARCHAR" property="txjcd" />
    <result column="ETDRK" jdbcType="VARCHAR" property="etdrk" />
    <result column="SOBKZ" jdbcType="VARCHAR" property="sobkz" />
    <result column="ARSNR" jdbcType="VARCHAR" property="arsnr" />
    <result column="ARSPS" jdbcType="VARCHAR" property="arsps" />
    <result column="INSNC" jdbcType="VARCHAR" property="insnc" />
    <result column="SSQSS" jdbcType="VARCHAR" property="ssqss" />
    <result column="ZGTYP" jdbcType="VARCHAR" property="zgtyp" />
    <result column="EAN11" jdbcType="VARCHAR" property="ean11" />
    <result column="BSTAE" jdbcType="VARCHAR" property="bstae" />
    <result column="REVLV" jdbcType="VARCHAR" property="revlv" />
    <result column="GEBER" jdbcType="VARCHAR" property="geber" />
    <result column="FISTL" jdbcType="VARCHAR" property="fistl" />
    <result column="FIPOS" jdbcType="VARCHAR" property="fipos" />
    <result column="KO_GSBER" jdbcType="VARCHAR" property="koGsber" />
    <result column="KO_PARGB" jdbcType="VARCHAR" property="koPargb" />
    <result column="KO_PRCTR" jdbcType="VARCHAR" property="koPrctr" />
    <result column="KO_PPRCTR" jdbcType="VARCHAR" property="koPprctr" />
    <result column="MEPRF" jdbcType="VARCHAR" property="meprf" />
    <result column="BRGEW" jdbcType="DECIMAL" property="brgew" />
    <result column="VOLUM" jdbcType="DECIMAL" property="volum" />
    <result column="VOLEH" jdbcType="VARCHAR" property="voleh" />
    <result column="INCO1" jdbcType="VARCHAR" property="inco1" />
    <result column="INCO2" jdbcType="VARCHAR" property="inco2" />
    <result column="VORAB" jdbcType="VARCHAR" property="vorab" />
    <result column="KOLIF" jdbcType="VARCHAR" property="kolif" />
    <result column="LTSNR" jdbcType="VARCHAR" property="ltsnr" />
    <result column="PACKNO" jdbcType="VARCHAR" property="packno" />
    <result column="FPLNR" jdbcType="VARCHAR" property="fplnr" />
    <result column="GNETWR" jdbcType="DECIMAL" property="gnetwr" />
    <result column="STAPO" jdbcType="VARCHAR" property="stapo" />
    <result column="UEBPO" jdbcType="VARCHAR" property="uebpo" />
    <result column="LEWED" jdbcType="VARCHAR" property="lewed" />
    <result column="EMLIF" jdbcType="VARCHAR" property="emlif" />
    <result column="LBLKZ" jdbcType="VARCHAR" property="lblkz" />
    <result column="SATNR" jdbcType="VARCHAR" property="satnr" />
    <result column="ATTYP" jdbcType="VARCHAR" property="attyp" />
    <result column="VSART" jdbcType="VARCHAR" property="vsart" />
    <result column="HANDOVERLOC" jdbcType="VARCHAR" property="handoverloc" />
    <result column="KANBA" jdbcType="VARCHAR" property="kanba" />
    <result column="ADRN2" jdbcType="VARCHAR" property="adrn2" />
    <result column="CUOBJ" jdbcType="VARCHAR" property="cuobj" />
    <result column="XERSY" jdbcType="VARCHAR" property="xersy" />
    <result column="EILDT" jdbcType="VARCHAR" property="eildt" />
    <result column="DRDAT" jdbcType="VARCHAR" property="drdat" />
    <result column="DRUHR" jdbcType="VARCHAR" property="druhr" />
    <result column="DRUNR" jdbcType="VARCHAR" property="drunr" />
    <result column="AKTNR" jdbcType="VARCHAR" property="aktnr" />
    <result column="ABELN" jdbcType="VARCHAR" property="abeln" />
    <result column="ABELP" jdbcType="VARCHAR" property="abelp" />
    <result column="ANZPU" jdbcType="DECIMAL" property="anzpu" />
    <result column="PUNEI" jdbcType="VARCHAR" property="punei" />
    <result column="SAISO" jdbcType="VARCHAR" property="saiso" />
    <result column="SAISJ" jdbcType="VARCHAR" property="saisj" />
    <result column="EBON2" jdbcType="VARCHAR" property="ebon2" />
    <result column="EBON3" jdbcType="VARCHAR" property="ebon3" />
    <result column="EBONF" jdbcType="VARCHAR" property="ebonf" />
    <result column="MLMAA" jdbcType="VARCHAR" property="mlmaa" />
    <result column="MHDRZ" jdbcType="DECIMAL" property="mhdrz" />
    <result column="ANFNR" jdbcType="VARCHAR" property="anfnr" />
    <result column="ANFPS" jdbcType="VARCHAR" property="anfps" />
    <result column="KZKFG" jdbcType="VARCHAR" property="kzkfg" />
    <result column="USEQU" jdbcType="VARCHAR" property="usequ" />
    <result column="UMSOK" jdbcType="VARCHAR" property="umsok" />
    <result column="BANFN" jdbcType="VARCHAR" property="banfn" />
    <result column="BNFPO" jdbcType="VARCHAR" property="bnfpo" />
    <result column="MTART" jdbcType="VARCHAR" property="mtart" />
    <result column="UPTYP" jdbcType="VARCHAR" property="uptyp" />
    <result column="UPVOR" jdbcType="VARCHAR" property="upvor" />
    <result column="KZWI1" jdbcType="DECIMAL" property="kzwi1" />
    <result column="KZWI2" jdbcType="DECIMAL" property="kzwi2" />
    <result column="KZWI3" jdbcType="DECIMAL" property="kzwi3" />
    <result column="KZWI4" jdbcType="DECIMAL" property="kzwi4" />
    <result column="KZWI5" jdbcType="DECIMAL" property="kzwi5" />
    <result column="KZWI6" jdbcType="DECIMAL" property="kzwi6" />
    <result column="SIKGR" jdbcType="VARCHAR" property="sikgr" />
    <result column="MFZHI" jdbcType="DECIMAL" property="mfzhi" />
    <result column="FFZHI" jdbcType="DECIMAL" property="ffzhi" />
    <result column="RETPO" jdbcType="VARCHAR" property="retpo" />
    <result column="AUREL" jdbcType="VARCHAR" property="aurel" />
    <result column="BSGRU" jdbcType="VARCHAR" property="bsgru" />
    <result column="LFRET" jdbcType="VARCHAR" property="lfret" />
    <result column="MFRGR" jdbcType="VARCHAR" property="mfrgr" />
    <result column="NRFHG" jdbcType="VARCHAR" property="nrfhg" />
    <result column="J_1BNBM" jdbcType="VARCHAR" property="j1bnbm" />
    <result column="J_1BMATUSE" jdbcType="VARCHAR" property="j1bmatuse" />
    <result column="J_1BMATORG" jdbcType="VARCHAR" property="j1bmatorg" />
    <result column="J_1BOWNPRO" jdbcType="VARCHAR" property="j1bownpro" />
    <result column="J_1BINDUST" jdbcType="VARCHAR" property="j1bindust" />
    <result column="ABUEB" jdbcType="VARCHAR" property="abueb" />
    <result column="NLABD" jdbcType="VARCHAR" property="nlabd" />
    <result column="NFABD" jdbcType="VARCHAR" property="nfabd" />
    <result column="KZBWS" jdbcType="VARCHAR" property="kzbws" />
    <result column="BONBA" jdbcType="DECIMAL" property="bonba" />
    <result column="FABKZ" jdbcType="VARCHAR" property="fabkz" />
    <result column="J_1AINDXP" jdbcType="VARCHAR" property="j1aindxp" />
    <result column="J_1AIDATEP" jdbcType="VARCHAR" property="j1aidatep" />
    <result column="MPROF" jdbcType="VARCHAR" property="mprof" />
    <result column="EGLKZ" jdbcType="VARCHAR" property="eglkz" />
    <result column="KZTLF" jdbcType="VARCHAR" property="kztlf" />
    <result column="KZFME" jdbcType="VARCHAR" property="kzfme" />
    <result column="RDPRF" jdbcType="VARCHAR" property="rdprf" />
    <result column="TECHS" jdbcType="VARCHAR" property="techs" />
    <result column="CHG_SRV" jdbcType="VARCHAR" property="chgSrv" />
    <result column="CHG_FPLNR" jdbcType="VARCHAR" property="chgFplnr" />
    <result column="MFRPN" jdbcType="VARCHAR" property="mfrpn" />
    <result column="MFRNR" jdbcType="VARCHAR" property="mfrnr" />
    <result column="EMNFR" jdbcType="VARCHAR" property="emnfr" />
    <result column="NOVET" jdbcType="VARCHAR" property="novet" />
    <result column="AFNAM" jdbcType="VARCHAR" property="afnam" />
    <result column="TZONRC" jdbcType="VARCHAR" property="tzonrc" />
    <result column="IPRKZ" jdbcType="VARCHAR" property="iprkz" />
    <result column="LEBRE" jdbcType="VARCHAR" property="lebre" />
    <result column="BERID" jdbcType="VARCHAR" property="berid" />
    <result column="XCONDITIONS" jdbcType="VARCHAR" property="xconditions" />
    <result column="APOMS" jdbcType="VARCHAR" property="apoms" />
    <result column="CCOMP" jdbcType="VARCHAR" property="ccomp" />
    <result column="GRANT_NBR" jdbcType="VARCHAR" property="grantNbr" />
    <result column="FKBER" jdbcType="VARCHAR" property="fkber" />
    <result column="STATUS" jdbcType="VARCHAR" property="status" />
    <result column="RESLO" jdbcType="VARCHAR" property="reslo" />
    <result column="KBLNR" jdbcType="VARCHAR" property="kblnr" />
    <result column="KBLPOS" jdbcType="VARCHAR" property="kblpos" />
    <result column="WEORA" jdbcType="VARCHAR" property="weora" />
    <result column="SRV_BAS_COM" jdbcType="VARCHAR" property="srvBasCom" />
    <result column="PRIO_URG" jdbcType="VARCHAR" property="prioUrg" />
    <result column="PRIO_REQ" jdbcType="VARCHAR" property="prioReq" />
    <result column="EMPST" jdbcType="VARCHAR" property="empst" />
    <result column="DIFF_INVOICE" jdbcType="VARCHAR" property="diffInvoice" />
    <result column="TRMRISK_RELEVANT" jdbcType="VARCHAR" property="trmriskRelevant" />
    <result column="SPE_ABGRU" jdbcType="VARCHAR" property="speAbgru" />
    <result column="SPE_CRM_SO" jdbcType="VARCHAR" property="speCrmSo" />
    <result column="SPE_CRM_SO_ITEM" jdbcType="VARCHAR" property="speCrmSoItem" />
    <result column="SPE_CRM_REF_SO" jdbcType="VARCHAR" property="speCrmRefSo" />
    <result column="SPE_CRM_REF_ITEM" jdbcType="VARCHAR" property="speCrmRefItem" />
    <result column="SPE_CRM_FKREL" jdbcType="VARCHAR" property="speCrmFkrel" />
    <result column="SPE_CHNG_SYS" jdbcType="VARCHAR" property="speChngSys" />
    <result column="SPE_INSMK_SRC" jdbcType="VARCHAR" property="speInsmkSrc" />
    <result column="SPE_CQ_CTRLTYPE" jdbcType="VARCHAR" property="speCqCtrltype" />
    <result column="SPE_CQ_NOCQ" jdbcType="VARCHAR" property="speCqNocq" />
    <result column="REASON_CODE" jdbcType="VARCHAR" property="reasonCode" />
    <result column="CQU_SAR" jdbcType="DECIMAL" property="cquSar" />
    <result column="ANZSN" jdbcType="INTEGER" property="anzsn" />
    <result column="SPE_EWM_DTC" jdbcType="VARCHAR" property="speEwmDtc" />
    <result column="EXLIN" jdbcType="VARCHAR" property="exlin" />
    <result column="EXSNR" jdbcType="VARCHAR" property="exsnr" />
    <result column="EHTYP" jdbcType="VARCHAR" property="ehtyp" />
    <result column="RETPC" jdbcType="DECIMAL" property="retpc" />
    <result column="DPTYP" jdbcType="VARCHAR" property="dptyp" />
    <result column="DPPCT" jdbcType="DECIMAL" property="dppct" />
    <result column="DPAMT" jdbcType="DECIMAL" property="dpamt" />
    <result column="DPDAT" jdbcType="VARCHAR" property="dpdat" />
    <result column="FLS_RSTO" jdbcType="VARCHAR" property="flsRsto" />
    <result column="EXT_RFX_NUMBER" jdbcType="VARCHAR" property="extRfxNumber" />
    <result column="EXT_RFX_ITEM" jdbcType="VARCHAR" property="extRfxItem" />
    <result column="EXT_RFX_SYSTEM" jdbcType="VARCHAR" property="extRfxSystem" />
    <result column="SRM_CONTRACT_ID" jdbcType="VARCHAR" property="srmContractId" />
    <result column="SRM_CONTRACT_ITM" jdbcType="VARCHAR" property="srmContractItm" />
    <result column="BLK_REASON_ID" jdbcType="VARCHAR" property="blkReasonId" />
    <result column="BLK_REASON_TXT" jdbcType="VARCHAR" property="blkReasonTxt" />
    <result column="ITCONS" jdbcType="VARCHAR" property="itcons" />
    <result column="FIXMG" jdbcType="VARCHAR" property="fixmg" />
    <result column="WABWE" jdbcType="VARCHAR" property="wabwe" />
    <result column="CMPL_DLV_ITM" jdbcType="VARCHAR" property="cmplDlvItm" />
    <result column="INCO2_L" jdbcType="VARCHAR" property="inco2L" />
    <result column="INCO3_L" jdbcType="VARCHAR" property="inco3L" />
    <result column="STAWN" jdbcType="VARCHAR" property="stawn" />
    <result column="ISVCO" jdbcType="VARCHAR" property="isvco" />
    <result column="GRWRT" jdbcType="DECIMAL" property="grwrt" />
    <result column="SERVICEPERFORMER" jdbcType="VARCHAR" property="serviceperformer" />
    <result column="PRODUCTTYPE" jdbcType="VARCHAR" property="producttype" />
    <result column="REQUESTFORQUOTATION" jdbcType="VARCHAR" property="requestforquotation" />
    <result column="REQUESTFORQUOTATIONITEM" jdbcType="VARCHAR" property="requestforquotationitem" />
    <result column="EXTERNALREFERENCEID" jdbcType="VARCHAR" property="externalreferenceid" />
    <result column="TC_AUT_DET" jdbcType="VARCHAR" property="tcAutDet" />
    <result column="MANUAL_TC_REASON" jdbcType="VARCHAR" property="manualTcReason" />
    <result column="FISCAL_INCENTIVE" jdbcType="VARCHAR" property="fiscalIncentive" />
    <result column="TAX_SUBJECT_ST" jdbcType="VARCHAR" property="taxSubjectSt" />
    <result column="FISCAL_INCENTIVE_ID" jdbcType="VARCHAR" property="fiscalIncentiveId" />
    <result column="SF_TXJCD" jdbcType="VARCHAR" property="sfTxjcd" />
    <result column="DUMMY_EKPO_INCL_EEW_PS" jdbcType="VARCHAR" property="dummyEkpoInclEewPs" />
    <result column="EXPECTED_VALUE" jdbcType="DECIMAL" property="expectedValue" />
    <result column="LIMIT_AMOUNT" jdbcType="DECIMAL" property="limitAmount" />
    <result column="ENH_DATE1" jdbcType="VARCHAR" property="enhDate1" />
    <result column="ENH_DATE2" jdbcType="VARCHAR" property="enhDate2" />
    <result column="ENH_PERCENT" jdbcType="DECIMAL" property="enhPercent" />
    <result column="ENH_NUMC1" jdbcType="VARCHAR" property="enhNumc1" />
    <result column="DATAAGING" jdbcType="VARCHAR" property="dataaging" />
    <result column="BEV1_NEGEN_ITEM" jdbcType="VARCHAR" property="bev1NegenItem" />
    <result column="BEV1_NEDEPFREE" jdbcType="VARCHAR" property="bev1Nedepfree" />
    <result column="BEV1_NESTRUCCAT" jdbcType="VARCHAR" property="bev1Nestruccat" />
    <result column="ADVCODE" jdbcType="VARCHAR" property="advcode" />
    <result column="BUDGET_PD" jdbcType="VARCHAR" property="budgetPd" />
    <result column="EXCPE" jdbcType="VARCHAR" property="excpe" />
    <result column="FMFGUS_KEY" jdbcType="VARCHAR" property="fmfgusKey" />
    <result column="IUID_RELEVANT" jdbcType="VARCHAR" property="iuidRelevant" />
    <result column="MRPIND" jdbcType="VARCHAR" property="mrpind" />
    <result column="SGT_SCAT" jdbcType="VARCHAR" property="sgtScat" />
    <result column="SGT_RCAT" jdbcType="VARCHAR" property="sgtRcat" />
    <result column="TMS_REF_UUID" jdbcType="VARCHAR" property="tmsRefUuid" />
    <result column="WRF_CHARSTC1" jdbcType="VARCHAR" property="wrfCharstc1" />
    <result column="WRF_CHARSTC2" jdbcType="VARCHAR" property="wrfCharstc2" />
    <result column="WRF_CHARSTC3" jdbcType="VARCHAR" property="wrfCharstc3" />
    <result column="ZZZMDSQ" jdbcType="VARCHAR" property="zzzmdsq" />
    <result column="ZZZMDHH" jdbcType="VARCHAR" property="zzzmdhh" />
    <result column="ZZQXDJH" jdbcType="VARCHAR" property="zzqxdjh" />
    <result column="ZZQXDHH" jdbcType="VARCHAR" property="zzqxdhh" />
    <result column="ZZCXBJS" jdbcType="VARCHAR" property="zzcxbjs" />
    <result column="ZZQTYS" jdbcType="VARCHAR" property="zzqtys" />
    <result column="ZZSPCD" jdbcType="VARCHAR" property="zzspcd" />
    <result column="ZZSCCJ" jdbcType="VARCHAR" property="zzsccj" />
    <result column="ZZZSCPH" jdbcType="VARCHAR" property="zzzscph" />
    <result column="ZZZGJJ" jdbcType="VARCHAR" property="zzzgjj" />
    <result column="REFSITE" jdbcType="VARCHAR" property="refsite" />
    <result column="ZAPCGK" jdbcType="VARCHAR" property="zapcgk" />
    <result column="APCGK_EXTEND" jdbcType="VARCHAR" property="apcgkExtend" />
    <result column="ZBAS_DATE" jdbcType="VARCHAR" property="zbasDate" />
    <result column="ZADATTYP" jdbcType="VARCHAR" property="zadattyp" />
    <result column="ZSTART_DAT" jdbcType="VARCHAR" property="zstartDat" />
    <result column="Z_DEV" jdbcType="DECIMAL" property="zDev" />
    <result column="ZINDANX" jdbcType="VARCHAR" property="zindanx" />
    <result column="ZLIMIT_DAT" jdbcType="VARCHAR" property="zlimitDat" />
    <result column="NUMERATOR" jdbcType="VARCHAR" property="numerator" />
    <result column="HASHCAL_BDAT" jdbcType="VARCHAR" property="hashcalBdat" />
    <result column="HASHCAL" jdbcType="VARCHAR" property="hashcal" />
    <result column="NEGATIVE" jdbcType="VARCHAR" property="negative" />
    <result column="HASHCAL_EXISTS" jdbcType="VARCHAR" property="hashcalExists" />
    <result column="KNOWN_INDEX" jdbcType="VARCHAR" property="knownIndex" />
    <result column="SAPMP_GPOSE" jdbcType="VARCHAR" property="sapmpGpose" />
    <result column="ANGPN" jdbcType="VARCHAR" property="angpn" />
    <result column="ADMOI" jdbcType="VARCHAR" property="admoi" />
    <result column="ADPRI" jdbcType="VARCHAR" property="adpri" />
    <result column="LPRIO" jdbcType="VARCHAR" property="lprio" />
    <result column="ADACN" jdbcType="VARCHAR" property="adacn" />
    <result column="AFPNR" jdbcType="VARCHAR" property="afpnr" />
    <result column="BSARK" jdbcType="VARCHAR" property="bsark" />
    <result column="AUDAT" jdbcType="VARCHAR" property="audat" />
    <result column="ANGNR" jdbcType="VARCHAR" property="angnr" />
    <result column="PNSTAT" jdbcType="VARCHAR" property="pnstat" />
    <result column="ADDNS" jdbcType="VARCHAR" property="addns" />
    <result column="SERRU" jdbcType="VARCHAR" property="serru" />
    <result column="SERNP" jdbcType="VARCHAR" property="sernp" />
    <result column="DISUB_SOBKZ" jdbcType="VARCHAR" property="disubSobkz" />
    <result column="DISUB_PSPNR" jdbcType="VARCHAR" property="disubPspnr" />
    <result column="DISUB_KUNNR" jdbcType="VARCHAR" property="disubKunnr" />
    <result column="DISUB_VBELN" jdbcType="VARCHAR" property="disubVbeln" />
    <result column="DISUB_POSNR" jdbcType="VARCHAR" property="disubPosnr" />
    <result column="DISUB_OWNER" jdbcType="VARCHAR" property="disubOwner" />
    <result column="FSH_SEASON_YEAR" jdbcType="VARCHAR" property="fshSeasonYear" />
    <result column="FSH_SEASON" jdbcType="VARCHAR" property="fshSeason" />
    <result column="FSH_COLLECTION" jdbcType="VARCHAR" property="fshCollection" />
    <result column="FSH_THEME" jdbcType="VARCHAR" property="fshTheme" />
    <result column="FSH_ATP_DATE" jdbcType="VARCHAR" property="fshAtpDate" />
    <result column="FSH_VAS_REL" jdbcType="VARCHAR" property="fshVasRel" />
    <result column="FSH_VAS_PRNT_ID" jdbcType="VARCHAR" property="fshVasPrntId" />
    <result column="FSH_TRANSACTION" jdbcType="VARCHAR" property="fshTransaction" />
    <result column="FSH_ITEM_GROUP" jdbcType="VARCHAR" property="fshItemGroup" />
    <result column="FSH_ITEM" jdbcType="VARCHAR" property="fshItem" />
    <result column="FSH_SS" jdbcType="VARCHAR" property="fshSs" />
    <result column="FSH_GRID_COND_REC" jdbcType="VARCHAR" property="fshGridCondRec" />
    <result column="FSH_PSM_PFM_SPLIT" jdbcType="VARCHAR" property="fshPsmPfmSplit" />
    <result column="CNFM_QTY" jdbcType="DECIMAL" property="cnfmQty" />
    <result column="STPAC" jdbcType="VARCHAR" property="stpac" />
    <result column="LGBZO" jdbcType="VARCHAR" property="lgbzo" />
    <result column="LGBZO_B" jdbcType="VARCHAR" property="lgbzoB" />
    <result column="ADDRNUM" jdbcType="VARCHAR" property="addrnum" />
    <result column="CONSNUM" jdbcType="VARCHAR" property="consnum" />
    <result column="BORGR_MISS" jdbcType="VARCHAR" property="borgrMiss" />
    <result column="DEP_ID" jdbcType="VARCHAR" property="depId" />
    <result column="BELNR" jdbcType="VARCHAR" property="belnr" />
    <result column="KBLPOS_CAB" jdbcType="VARCHAR" property="kblposCab" />
    <result column="KBLNR_COMP" jdbcType="VARCHAR" property="kblnrComp" />
    <result column="KBLPOS_COMP" jdbcType="VARCHAR" property="kblposComp" />
    <result column="WBS_ELEMENT" jdbcType="VARCHAR" property="wbsElement" />
    <result column="RFM_PSST_RULE" jdbcType="VARCHAR" property="rfmPsstRule" />
    <result column="RFM_PSST_GROUP" jdbcType="VARCHAR" property="rfmPsstGroup" />
    <result column="REF_ITEM" jdbcType="VARCHAR" property="refItem" />
    <result column="SOURCE_ID" jdbcType="VARCHAR" property="sourceId" />
    <result column="SOURCE_KEY" jdbcType="VARCHAR" property="sourceKey" />
    <result column="PUT_BACK" jdbcType="VARCHAR" property="putBack" />
    <result column="POL_ID" jdbcType="VARCHAR" property="polId" />
    <result column="CONS_ORDER" jdbcType="VARCHAR" property="consOrder" />
    <result column="ZZPAID" jdbcType="VARCHAR" property="zzpaid" />
    <result column="ZZPAMT" jdbcType="DECIMAL" property="zzpamt" />
    <result column="ZZSIGN" jdbcType="DECIMAL" property="zzsign" />
    <result column="ZZDATE" jdbcType="VARCHAR" property="zzdate" />
    <result column="ZZTIME" jdbcType="VARCHAR" property="zztime" />
    <result column="ZZPRICE" jdbcType="VARCHAR" property="zzprice" />
    <result column="ZZPEINH" jdbcType="VARCHAR" property="zzpeinh" />
    <result column="ZZMWSKZ" jdbcType="VARCHAR" property="zzmwskz" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, MANDT, EBELN, EBELP, LOEKZ, STATU, AEDAT, TXZ01, MATNR, EMATN, BUKRS, WERKS, 
    LGORT, BEDNR, MATKL, INFNR, IDNLF, KTMNG, MENGE, MEINS, BPRME, BPUMZ, BPUMN, UMREZ, 
    UMREN, NETPR, PEINH, NETWR, BRTWR, AGDAT, WEBAZ, MWSKZ, BONUS, INSMK, SPINF, PRSDR, 
    SCHPR, MAHNZ, MAHN1, MAHN2, MAHN3, UEBTO, UEBTK, UNTTO, BWTAR, BWTTY, ABSKZ, AGMEM, 
    ELIKZ, EREKZ, PSTYP, KNTTP, KZVBR, VRTKZ, TWRKZ, WEPOS, WEUNB, REPOS, WEBRE, KZABS, 
    LABNR, KONNR, KTPNR, ABDAT, ABFTZ, ETFZ1, ETFZ2, KZSTU, NOTKZ, LMEIN, EVERS, ZWERT, 
    NAVNW, ABMNG, PRDAT, BSTYP, EFFWR, XOBLR, KUNNR, ADRNR, EKKOL, SKTOF, STAFO, PLIFZ, 
    NTGEW, GEWEI, TXJCD, ETDRK, SOBKZ, ARSNR, ARSPS, INSNC, SSQSS, ZGTYP, EAN11, BSTAE, 
    REVLV, GEBER, FISTL, FIPOS, KO_GSBER, KO_PARGB, KO_PRCTR, KO_PPRCTR, MEPRF, BRGEW, 
    VOLUM, VOLEH, INCO1, INCO2, VORAB, KOLIF, LTSNR, PACKNO, FPLNR, GNETWR, STAPO, UEBPO, 
    LEWED, EMLIF, LBLKZ, SATNR, ATTYP, VSART, HANDOVERLOC, KANBA, ADRN2, CUOBJ, XERSY, 
    EILDT, DRDAT, DRUHR, DRUNR, AKTNR, ABELN, ABELP, ANZPU, PUNEI, SAISO, SAISJ, EBON2, 
    EBON3, EBONF, MLMAA, MHDRZ, ANFNR, ANFPS, KZKFG, USEQU, UMSOK, BANFN, BNFPO, MTART, 
    UPTYP, UPVOR, KZWI1, KZWI2, KZWI3, KZWI4, KZWI5, KZWI6, SIKGR, MFZHI, FFZHI, RETPO, 
    AUREL, BSGRU, LFRET, MFRGR, NRFHG, J_1BNBM, J_1BMATUSE, J_1BMATORG, J_1BOWNPRO, J_1BINDUST, 
    ABUEB, NLABD, NFABD, KZBWS, BONBA, FABKZ, J_1AINDXP, J_1AIDATEP, MPROF, EGLKZ, KZTLF, 
    KZFME, RDPRF, TECHS, CHG_SRV, CHG_FPLNR, MFRPN, MFRNR, EMNFR, NOVET, AFNAM, TZONRC, 
    IPRKZ, LEBRE, BERID, XCONDITIONS, APOMS, CCOMP, GRANT_NBR, FKBER, `STATUS`, RESLO, 
    KBLNR, KBLPOS, WEORA, SRV_BAS_COM, PRIO_URG, PRIO_REQ, EMPST, DIFF_INVOICE, TRMRISK_RELEVANT, 
    SPE_ABGRU, SPE_CRM_SO, SPE_CRM_SO_ITEM, SPE_CRM_REF_SO, SPE_CRM_REF_ITEM, SPE_CRM_FKREL, 
    SPE_CHNG_SYS, SPE_INSMK_SRC, SPE_CQ_CTRLTYPE, SPE_CQ_NOCQ, REASON_CODE, CQU_SAR, 
    ANZSN, SPE_EWM_DTC, EXLIN, EXSNR, EHTYP, RETPC, DPTYP, DPPCT, DPAMT, DPDAT, FLS_RSTO, 
    EXT_RFX_NUMBER, EXT_RFX_ITEM, EXT_RFX_SYSTEM, SRM_CONTRACT_ID, SRM_CONTRACT_ITM, 
    BLK_REASON_ID, BLK_REASON_TXT, ITCONS, FIXMG, WABWE, CMPL_DLV_ITM, INCO2_L, INCO3_L, 
    STAWN, ISVCO, GRWRT, SERVICEPERFORMER, PRODUCTTYPE, REQUESTFORQUOTATION, REQUESTFORQUOTATIONITEM, 
    EXTERNALREFERENCEID, TC_AUT_DET, MANUAL_TC_REASON, FISCAL_INCENTIVE, TAX_SUBJECT_ST, 
    FISCAL_INCENTIVE_ID, SF_TXJCD, DUMMY_EKPO_INCL_EEW_PS, EXPECTED_VALUE, LIMIT_AMOUNT, 
    ENH_DATE1, ENH_DATE2, ENH_PERCENT, ENH_NUMC1, DATAAGING, BEV1_NEGEN_ITEM, BEV1_NEDEPFREE, 
    BEV1_NESTRUCCAT, ADVCODE, BUDGET_PD, EXCPE, FMFGUS_KEY, IUID_RELEVANT, MRPIND, SGT_SCAT, 
    SGT_RCAT, TMS_REF_UUID, WRF_CHARSTC1, WRF_CHARSTC2, WRF_CHARSTC3, ZZZMDSQ, ZZZMDHH, 
    ZZQXDJH, ZZQXDHH, ZZCXBJS, ZZQTYS, ZZSPCD, ZZSCCJ, ZZZSCPH, ZZZGJJ, REFSITE, ZAPCGK, 
    APCGK_EXTEND, ZBAS_DATE, ZADATTYP, ZSTART_DAT, Z_DEV, ZINDANX, ZLIMIT_DAT, NUMERATOR, 
    HASHCAL_BDAT, HASHCAL, NEGATIVE, HASHCAL_EXISTS, KNOWN_INDEX, SAPMP_GPOSE, ANGPN, 
    ADMOI, ADPRI, LPRIO, ADACN, AFPNR, BSARK, AUDAT, ANGNR, PNSTAT, ADDNS, SERRU, SERNP, 
    DISUB_SOBKZ, DISUB_PSPNR, DISUB_KUNNR, DISUB_VBELN, DISUB_POSNR, DISUB_OWNER, FSH_SEASON_YEAR, 
    FSH_SEASON, FSH_COLLECTION, FSH_THEME, FSH_ATP_DATE, FSH_VAS_REL, FSH_VAS_PRNT_ID, 
    FSH_TRANSACTION, FSH_ITEM_GROUP, FSH_ITEM, FSH_SS, FSH_GRID_COND_REC, FSH_PSM_PFM_SPLIT, 
    CNFM_QTY, STPAC, LGBZO, LGBZO_B, ADDRNUM, CONSNUM, BORGR_MISS, DEP_ID, BELNR, KBLPOS_CAB, 
    KBLNR_COMP, KBLPOS_COMP, WBS_ELEMENT, RFM_PSST_RULE, RFM_PSST_GROUP, REF_ITEM, SOURCE_ID, 
    SOURCE_KEY, PUT_BACK, POL_ID, CONS_ORDER, ZZPAID, ZZPAMT, ZZSIGN, ZZDATE, ZZTIME, 
    ZZPRICE, ZZPEINH, ZZMWSKZ
  </sql>
  <select id="selectByExample" parameterType="com.cowell.iscm.entity.SapEkpoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from SAP_EKPO
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from SAP_EKPO
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from SAP_EKPO
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.iscm.entity.SapEkpoExample">
    delete from SAP_EKPO
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entity.SapEkpo" useGeneratedKeys="true">
    insert into SAP_EKPO (MANDT, EBELN, EBELP, 
      LOEKZ, STATU, AEDAT, 
      TXZ01, MATNR, EMATN, 
      BUKRS, WERKS, LGORT, 
      BEDNR, MATKL, INFNR, 
      IDNLF, KTMNG, MENGE, 
      MEINS, BPRME, BPUMZ, 
      BPUMN, UMREZ, UMREN, 
      NETPR, PEINH, NETWR, 
      BRTWR, AGDAT, WEBAZ, 
      MWSKZ, BONUS, INSMK, 
      SPINF, PRSDR, SCHPR, 
      MAHNZ, MAHN1, MAHN2, 
      MAHN3, UEBTO, UEBTK, 
      UNTTO, BWTAR, BWTTY, 
      ABSKZ, AGMEM, ELIKZ, 
      EREKZ, PSTYP, KNTTP, 
      KZVBR, VRTKZ, TWRKZ, 
      WEPOS, WEUNB, REPOS, 
      WEBRE, KZABS, LABNR, 
      KONNR, KTPNR, ABDAT, 
      ABFTZ, ETFZ1, ETFZ2, 
      KZSTU, NOTKZ, LMEIN, 
      EVERS, ZWERT, NAVNW, 
      ABMNG, PRDAT, BSTYP, 
      EFFWR, XOBLR, KUNNR, 
      ADRNR, EKKOL, SKTOF, 
      STAFO, PLIFZ, NTGEW, 
      GEWEI, TXJCD, ETDRK, 
      SOBKZ, ARSNR, ARSPS, 
      INSNC, SSQSS, ZGTYP, 
      EAN11, BSTAE, REVLV, 
      GEBER, FISTL, FIPOS, 
      KO_GSBER, KO_PARGB, KO_PRCTR, 
      KO_PPRCTR, MEPRF, BRGEW, 
      VOLUM, VOLEH, INCO1, 
      INCO2, VORAB, KOLIF, 
      LTSNR, PACKNO, FPLNR, 
      GNETWR, STAPO, UEBPO, 
      LEWED, EMLIF, LBLKZ, 
      SATNR, ATTYP, VSART, 
      HANDOVERLOC, KANBA, ADRN2, 
      CUOBJ, XERSY, EILDT, 
      DRDAT, DRUHR, DRUNR, 
      AKTNR, ABELN, ABELP, 
      ANZPU, PUNEI, SAISO, 
      SAISJ, EBON2, EBON3, 
      EBONF, MLMAA, MHDRZ, 
      ANFNR, ANFPS, KZKFG, 
      USEQU, UMSOK, BANFN, 
      BNFPO, MTART, UPTYP, 
      UPVOR, KZWI1, KZWI2, 
      KZWI3, KZWI4, KZWI5, 
      KZWI6, SIKGR, MFZHI, 
      FFZHI, RETPO, AUREL, 
      BSGRU, LFRET, MFRGR, 
      NRFHG, J_1BNBM, J_1BMATUSE, 
      J_1BMATORG, J_1BOWNPRO, J_1BINDUST, 
      ABUEB, NLABD, NFABD, 
      KZBWS, BONBA, FABKZ, 
      J_1AINDXP, J_1AIDATEP, MPROF, 
      EGLKZ, KZTLF, KZFME, 
      RDPRF, TECHS, CHG_SRV, 
      CHG_FPLNR, MFRPN, MFRNR, 
      EMNFR, NOVET, AFNAM, 
      TZONRC, IPRKZ, LEBRE, 
      BERID, XCONDITIONS, APOMS, 
      CCOMP, GRANT_NBR, FKBER, 
      `STATUS`, RESLO, KBLNR, 
      KBLPOS, WEORA, SRV_BAS_COM, 
      PRIO_URG, PRIO_REQ, EMPST, 
      DIFF_INVOICE, TRMRISK_RELEVANT, SPE_ABGRU, 
      SPE_CRM_SO, SPE_CRM_SO_ITEM, SPE_CRM_REF_SO, 
      SPE_CRM_REF_ITEM, SPE_CRM_FKREL, SPE_CHNG_SYS, 
      SPE_INSMK_SRC, SPE_CQ_CTRLTYPE, SPE_CQ_NOCQ, 
      REASON_CODE, CQU_SAR, ANZSN, 
      SPE_EWM_DTC, EXLIN, EXSNR, 
      EHTYP, RETPC, DPTYP, 
      DPPCT, DPAMT, DPDAT, 
      FLS_RSTO, EXT_RFX_NUMBER, EXT_RFX_ITEM, 
      EXT_RFX_SYSTEM, SRM_CONTRACT_ID, SRM_CONTRACT_ITM, 
      BLK_REASON_ID, BLK_REASON_TXT, ITCONS, 
      FIXMG, WABWE, CMPL_DLV_ITM, 
      INCO2_L, INCO3_L, STAWN, 
      ISVCO, GRWRT, SERVICEPERFORMER, 
      PRODUCTTYPE, REQUESTFORQUOTATION, REQUESTFORQUOTATIONITEM, 
      EXTERNALREFERENCEID, TC_AUT_DET, MANUAL_TC_REASON, 
      FISCAL_INCENTIVE, TAX_SUBJECT_ST, FISCAL_INCENTIVE_ID, 
      SF_TXJCD, DUMMY_EKPO_INCL_EEW_PS, EXPECTED_VALUE, 
      LIMIT_AMOUNT, ENH_DATE1, ENH_DATE2, 
      ENH_PERCENT, ENH_NUMC1, DATAAGING, 
      BEV1_NEGEN_ITEM, BEV1_NEDEPFREE, BEV1_NESTRUCCAT, 
      ADVCODE, BUDGET_PD, EXCPE, 
      FMFGUS_KEY, IUID_RELEVANT, MRPIND, 
      SGT_SCAT, SGT_RCAT, TMS_REF_UUID, 
      WRF_CHARSTC1, WRF_CHARSTC2, WRF_CHARSTC3, 
      ZZZMDSQ, ZZZMDHH, ZZQXDJH, 
      ZZQXDHH, ZZCXBJS, ZZQTYS, 
      ZZSPCD, ZZSCCJ, ZZZSCPH, 
      ZZZGJJ, REFSITE, ZAPCGK, 
      APCGK_EXTEND, ZBAS_DATE, ZADATTYP, 
      ZSTART_DAT, Z_DEV, ZINDANX, 
      ZLIMIT_DAT, NUMERATOR, HASHCAL_BDAT, 
      HASHCAL, NEGATIVE, HASHCAL_EXISTS, 
      KNOWN_INDEX, SAPMP_GPOSE, ANGPN, 
      ADMOI, ADPRI, LPRIO, 
      ADACN, AFPNR, BSARK, 
      AUDAT, ANGNR, PNSTAT, 
      ADDNS, SERRU, SERNP, 
      DISUB_SOBKZ, DISUB_PSPNR, DISUB_KUNNR, 
      DISUB_VBELN, DISUB_POSNR, DISUB_OWNER, 
      FSH_SEASON_YEAR, FSH_SEASON, FSH_COLLECTION, 
      FSH_THEME, FSH_ATP_DATE, FSH_VAS_REL, 
      FSH_VAS_PRNT_ID, FSH_TRANSACTION, FSH_ITEM_GROUP, 
      FSH_ITEM, FSH_SS, FSH_GRID_COND_REC, 
      FSH_PSM_PFM_SPLIT, CNFM_QTY, STPAC, 
      LGBZO, LGBZO_B, ADDRNUM, 
      CONSNUM, BORGR_MISS, DEP_ID, 
      BELNR, KBLPOS_CAB, KBLNR_COMP, 
      KBLPOS_COMP, WBS_ELEMENT, RFM_PSST_RULE, 
      RFM_PSST_GROUP, REF_ITEM, SOURCE_ID, 
      SOURCE_KEY, PUT_BACK, POL_ID, 
      CONS_ORDER, ZZPAID, ZZPAMT, 
      ZZSIGN, ZZDATE, ZZTIME, 
      ZZPRICE, ZZPEINH, ZZMWSKZ
      )
    values (#{mandt,jdbcType=VARCHAR}, #{ebeln,jdbcType=VARCHAR}, #{ebelp,jdbcType=VARCHAR}, 
      #{loekz,jdbcType=VARCHAR}, #{statu,jdbcType=VARCHAR}, #{aedat,jdbcType=VARCHAR}, 
      #{txz01,jdbcType=VARCHAR}, #{matnr,jdbcType=VARCHAR}, #{ematn,jdbcType=VARCHAR}, 
      #{bukrs,jdbcType=VARCHAR}, #{werks,jdbcType=VARCHAR}, #{lgort,jdbcType=VARCHAR}, 
      #{bednr,jdbcType=VARCHAR}, #{matkl,jdbcType=VARCHAR}, #{infnr,jdbcType=VARCHAR}, 
      #{idnlf,jdbcType=VARCHAR}, #{ktmng,jdbcType=DECIMAL}, #{menge,jdbcType=DECIMAL}, 
      #{meins,jdbcType=VARCHAR}, #{bprme,jdbcType=VARCHAR}, #{bpumz,jdbcType=DECIMAL}, 
      #{bpumn,jdbcType=DECIMAL}, #{umrez,jdbcType=DECIMAL}, #{umren,jdbcType=DECIMAL}, 
      #{netpr,jdbcType=DECIMAL}, #{peinh,jdbcType=DECIMAL}, #{netwr,jdbcType=DECIMAL}, 
      #{brtwr,jdbcType=DECIMAL}, #{agdat,jdbcType=VARCHAR}, #{webaz,jdbcType=DECIMAL}, 
      #{mwskz,jdbcType=VARCHAR}, #{bonus,jdbcType=VARCHAR}, #{insmk,jdbcType=VARCHAR}, 
      #{spinf,jdbcType=VARCHAR}, #{prsdr,jdbcType=VARCHAR}, #{schpr,jdbcType=VARCHAR}, 
      #{mahnz,jdbcType=DECIMAL}, #{mahn1,jdbcType=DECIMAL}, #{mahn2,jdbcType=DECIMAL}, 
      #{mahn3,jdbcType=DECIMAL}, #{uebto,jdbcType=DECIMAL}, #{uebtk,jdbcType=VARCHAR}, 
      #{untto,jdbcType=DECIMAL}, #{bwtar,jdbcType=VARCHAR}, #{bwtty,jdbcType=VARCHAR}, 
      #{abskz,jdbcType=VARCHAR}, #{agmem,jdbcType=VARCHAR}, #{elikz,jdbcType=VARCHAR}, 
      #{erekz,jdbcType=VARCHAR}, #{pstyp,jdbcType=VARCHAR}, #{knttp,jdbcType=VARCHAR}, 
      #{kzvbr,jdbcType=VARCHAR}, #{vrtkz,jdbcType=VARCHAR}, #{twrkz,jdbcType=VARCHAR}, 
      #{wepos,jdbcType=VARCHAR}, #{weunb,jdbcType=VARCHAR}, #{repos,jdbcType=VARCHAR}, 
      #{webre,jdbcType=VARCHAR}, #{kzabs,jdbcType=VARCHAR}, #{labnr,jdbcType=VARCHAR}, 
      #{konnr,jdbcType=VARCHAR}, #{ktpnr,jdbcType=VARCHAR}, #{abdat,jdbcType=VARCHAR}, 
      #{abftz,jdbcType=DECIMAL}, #{etfz1,jdbcType=DECIMAL}, #{etfz2,jdbcType=DECIMAL}, 
      #{kzstu,jdbcType=VARCHAR}, #{notkz,jdbcType=VARCHAR}, #{lmein,jdbcType=VARCHAR}, 
      #{evers,jdbcType=VARCHAR}, #{zwert,jdbcType=DECIMAL}, #{navnw,jdbcType=DECIMAL}, 
      #{abmng,jdbcType=DECIMAL}, #{prdat,jdbcType=VARCHAR}, #{bstyp,jdbcType=VARCHAR}, 
      #{effwr,jdbcType=DECIMAL}, #{xoblr,jdbcType=VARCHAR}, #{kunnr,jdbcType=VARCHAR}, 
      #{adrnr,jdbcType=VARCHAR}, #{ekkol,jdbcType=VARCHAR}, #{sktof,jdbcType=VARCHAR}, 
      #{stafo,jdbcType=VARCHAR}, #{plifz,jdbcType=DECIMAL}, #{ntgew,jdbcType=DECIMAL}, 
      #{gewei,jdbcType=VARCHAR}, #{txjcd,jdbcType=VARCHAR}, #{etdrk,jdbcType=VARCHAR}, 
      #{sobkz,jdbcType=VARCHAR}, #{arsnr,jdbcType=VARCHAR}, #{arsps,jdbcType=VARCHAR}, 
      #{insnc,jdbcType=VARCHAR}, #{ssqss,jdbcType=VARCHAR}, #{zgtyp,jdbcType=VARCHAR}, 
      #{ean11,jdbcType=VARCHAR}, #{bstae,jdbcType=VARCHAR}, #{revlv,jdbcType=VARCHAR}, 
      #{geber,jdbcType=VARCHAR}, #{fistl,jdbcType=VARCHAR}, #{fipos,jdbcType=VARCHAR}, 
      #{koGsber,jdbcType=VARCHAR}, #{koPargb,jdbcType=VARCHAR}, #{koPrctr,jdbcType=VARCHAR}, 
      #{koPprctr,jdbcType=VARCHAR}, #{meprf,jdbcType=VARCHAR}, #{brgew,jdbcType=DECIMAL}, 
      #{volum,jdbcType=DECIMAL}, #{voleh,jdbcType=VARCHAR}, #{inco1,jdbcType=VARCHAR}, 
      #{inco2,jdbcType=VARCHAR}, #{vorab,jdbcType=VARCHAR}, #{kolif,jdbcType=VARCHAR}, 
      #{ltsnr,jdbcType=VARCHAR}, #{packno,jdbcType=VARCHAR}, #{fplnr,jdbcType=VARCHAR}, 
      #{gnetwr,jdbcType=DECIMAL}, #{stapo,jdbcType=VARCHAR}, #{uebpo,jdbcType=VARCHAR}, 
      #{lewed,jdbcType=VARCHAR}, #{emlif,jdbcType=VARCHAR}, #{lblkz,jdbcType=VARCHAR}, 
      #{satnr,jdbcType=VARCHAR}, #{attyp,jdbcType=VARCHAR}, #{vsart,jdbcType=VARCHAR}, 
      #{handoverloc,jdbcType=VARCHAR}, #{kanba,jdbcType=VARCHAR}, #{adrn2,jdbcType=VARCHAR}, 
      #{cuobj,jdbcType=VARCHAR}, #{xersy,jdbcType=VARCHAR}, #{eildt,jdbcType=VARCHAR}, 
      #{drdat,jdbcType=VARCHAR}, #{druhr,jdbcType=VARCHAR}, #{drunr,jdbcType=VARCHAR}, 
      #{aktnr,jdbcType=VARCHAR}, #{abeln,jdbcType=VARCHAR}, #{abelp,jdbcType=VARCHAR}, 
      #{anzpu,jdbcType=DECIMAL}, #{punei,jdbcType=VARCHAR}, #{saiso,jdbcType=VARCHAR}, 
      #{saisj,jdbcType=VARCHAR}, #{ebon2,jdbcType=VARCHAR}, #{ebon3,jdbcType=VARCHAR}, 
      #{ebonf,jdbcType=VARCHAR}, #{mlmaa,jdbcType=VARCHAR}, #{mhdrz,jdbcType=DECIMAL}, 
      #{anfnr,jdbcType=VARCHAR}, #{anfps,jdbcType=VARCHAR}, #{kzkfg,jdbcType=VARCHAR}, 
      #{usequ,jdbcType=VARCHAR}, #{umsok,jdbcType=VARCHAR}, #{banfn,jdbcType=VARCHAR}, 
      #{bnfpo,jdbcType=VARCHAR}, #{mtart,jdbcType=VARCHAR}, #{uptyp,jdbcType=VARCHAR}, 
      #{upvor,jdbcType=VARCHAR}, #{kzwi1,jdbcType=DECIMAL}, #{kzwi2,jdbcType=DECIMAL}, 
      #{kzwi3,jdbcType=DECIMAL}, #{kzwi4,jdbcType=DECIMAL}, #{kzwi5,jdbcType=DECIMAL}, 
      #{kzwi6,jdbcType=DECIMAL}, #{sikgr,jdbcType=VARCHAR}, #{mfzhi,jdbcType=DECIMAL}, 
      #{ffzhi,jdbcType=DECIMAL}, #{retpo,jdbcType=VARCHAR}, #{aurel,jdbcType=VARCHAR}, 
      #{bsgru,jdbcType=VARCHAR}, #{lfret,jdbcType=VARCHAR}, #{mfrgr,jdbcType=VARCHAR}, 
      #{nrfhg,jdbcType=VARCHAR}, #{j1bnbm,jdbcType=VARCHAR}, #{j1bmatuse,jdbcType=VARCHAR}, 
      #{j1bmatorg,jdbcType=VARCHAR}, #{j1bownpro,jdbcType=VARCHAR}, #{j1bindust,jdbcType=VARCHAR}, 
      #{abueb,jdbcType=VARCHAR}, #{nlabd,jdbcType=VARCHAR}, #{nfabd,jdbcType=VARCHAR}, 
      #{kzbws,jdbcType=VARCHAR}, #{bonba,jdbcType=DECIMAL}, #{fabkz,jdbcType=VARCHAR}, 
      #{j1aindxp,jdbcType=VARCHAR}, #{j1aidatep,jdbcType=VARCHAR}, #{mprof,jdbcType=VARCHAR}, 
      #{eglkz,jdbcType=VARCHAR}, #{kztlf,jdbcType=VARCHAR}, #{kzfme,jdbcType=VARCHAR}, 
      #{rdprf,jdbcType=VARCHAR}, #{techs,jdbcType=VARCHAR}, #{chgSrv,jdbcType=VARCHAR}, 
      #{chgFplnr,jdbcType=VARCHAR}, #{mfrpn,jdbcType=VARCHAR}, #{mfrnr,jdbcType=VARCHAR}, 
      #{emnfr,jdbcType=VARCHAR}, #{novet,jdbcType=VARCHAR}, #{afnam,jdbcType=VARCHAR}, 
      #{tzonrc,jdbcType=VARCHAR}, #{iprkz,jdbcType=VARCHAR}, #{lebre,jdbcType=VARCHAR}, 
      #{berid,jdbcType=VARCHAR}, #{xconditions,jdbcType=VARCHAR}, #{apoms,jdbcType=VARCHAR}, 
      #{ccomp,jdbcType=VARCHAR}, #{grantNbr,jdbcType=VARCHAR}, #{fkber,jdbcType=VARCHAR}, 
      #{status,jdbcType=VARCHAR}, #{reslo,jdbcType=VARCHAR}, #{kblnr,jdbcType=VARCHAR}, 
      #{kblpos,jdbcType=VARCHAR}, #{weora,jdbcType=VARCHAR}, #{srvBasCom,jdbcType=VARCHAR}, 
      #{prioUrg,jdbcType=VARCHAR}, #{prioReq,jdbcType=VARCHAR}, #{empst,jdbcType=VARCHAR}, 
      #{diffInvoice,jdbcType=VARCHAR}, #{trmriskRelevant,jdbcType=VARCHAR}, #{speAbgru,jdbcType=VARCHAR}, 
      #{speCrmSo,jdbcType=VARCHAR}, #{speCrmSoItem,jdbcType=VARCHAR}, #{speCrmRefSo,jdbcType=VARCHAR}, 
      #{speCrmRefItem,jdbcType=VARCHAR}, #{speCrmFkrel,jdbcType=VARCHAR}, #{speChngSys,jdbcType=VARCHAR}, 
      #{speInsmkSrc,jdbcType=VARCHAR}, #{speCqCtrltype,jdbcType=VARCHAR}, #{speCqNocq,jdbcType=VARCHAR}, 
      #{reasonCode,jdbcType=VARCHAR}, #{cquSar,jdbcType=DECIMAL}, #{anzsn,jdbcType=INTEGER}, 
      #{speEwmDtc,jdbcType=VARCHAR}, #{exlin,jdbcType=VARCHAR}, #{exsnr,jdbcType=VARCHAR}, 
      #{ehtyp,jdbcType=VARCHAR}, #{retpc,jdbcType=DECIMAL}, #{dptyp,jdbcType=VARCHAR}, 
      #{dppct,jdbcType=DECIMAL}, #{dpamt,jdbcType=DECIMAL}, #{dpdat,jdbcType=VARCHAR}, 
      #{flsRsto,jdbcType=VARCHAR}, #{extRfxNumber,jdbcType=VARCHAR}, #{extRfxItem,jdbcType=VARCHAR}, 
      #{extRfxSystem,jdbcType=VARCHAR}, #{srmContractId,jdbcType=VARCHAR}, #{srmContractItm,jdbcType=VARCHAR}, 
      #{blkReasonId,jdbcType=VARCHAR}, #{blkReasonTxt,jdbcType=VARCHAR}, #{itcons,jdbcType=VARCHAR}, 
      #{fixmg,jdbcType=VARCHAR}, #{wabwe,jdbcType=VARCHAR}, #{cmplDlvItm,jdbcType=VARCHAR}, 
      #{inco2L,jdbcType=VARCHAR}, #{inco3L,jdbcType=VARCHAR}, #{stawn,jdbcType=VARCHAR}, 
      #{isvco,jdbcType=VARCHAR}, #{grwrt,jdbcType=DECIMAL}, #{serviceperformer,jdbcType=VARCHAR}, 
      #{producttype,jdbcType=VARCHAR}, #{requestforquotation,jdbcType=VARCHAR}, #{requestforquotationitem,jdbcType=VARCHAR}, 
      #{externalreferenceid,jdbcType=VARCHAR}, #{tcAutDet,jdbcType=VARCHAR}, #{manualTcReason,jdbcType=VARCHAR}, 
      #{fiscalIncentive,jdbcType=VARCHAR}, #{taxSubjectSt,jdbcType=VARCHAR}, #{fiscalIncentiveId,jdbcType=VARCHAR}, 
      #{sfTxjcd,jdbcType=VARCHAR}, #{dummyEkpoInclEewPs,jdbcType=VARCHAR}, #{expectedValue,jdbcType=DECIMAL}, 
      #{limitAmount,jdbcType=DECIMAL}, #{enhDate1,jdbcType=VARCHAR}, #{enhDate2,jdbcType=VARCHAR}, 
      #{enhPercent,jdbcType=DECIMAL}, #{enhNumc1,jdbcType=VARCHAR}, #{dataaging,jdbcType=VARCHAR}, 
      #{bev1NegenItem,jdbcType=VARCHAR}, #{bev1Nedepfree,jdbcType=VARCHAR}, #{bev1Nestruccat,jdbcType=VARCHAR}, 
      #{advcode,jdbcType=VARCHAR}, #{budgetPd,jdbcType=VARCHAR}, #{excpe,jdbcType=VARCHAR}, 
      #{fmfgusKey,jdbcType=VARCHAR}, #{iuidRelevant,jdbcType=VARCHAR}, #{mrpind,jdbcType=VARCHAR}, 
      #{sgtScat,jdbcType=VARCHAR}, #{sgtRcat,jdbcType=VARCHAR}, #{tmsRefUuid,jdbcType=VARCHAR}, 
      #{wrfCharstc1,jdbcType=VARCHAR}, #{wrfCharstc2,jdbcType=VARCHAR}, #{wrfCharstc3,jdbcType=VARCHAR}, 
      #{zzzmdsq,jdbcType=VARCHAR}, #{zzzmdhh,jdbcType=VARCHAR}, #{zzqxdjh,jdbcType=VARCHAR}, 
      #{zzqxdhh,jdbcType=VARCHAR}, #{zzcxbjs,jdbcType=VARCHAR}, #{zzqtys,jdbcType=VARCHAR}, 
      #{zzspcd,jdbcType=VARCHAR}, #{zzsccj,jdbcType=VARCHAR}, #{zzzscph,jdbcType=VARCHAR}, 
      #{zzzgjj,jdbcType=VARCHAR}, #{refsite,jdbcType=VARCHAR}, #{zapcgk,jdbcType=VARCHAR}, 
      #{apcgkExtend,jdbcType=VARCHAR}, #{zbasDate,jdbcType=VARCHAR}, #{zadattyp,jdbcType=VARCHAR}, 
      #{zstartDat,jdbcType=VARCHAR}, #{zDev,jdbcType=DECIMAL}, #{zindanx,jdbcType=VARCHAR}, 
      #{zlimitDat,jdbcType=VARCHAR}, #{numerator,jdbcType=VARCHAR}, #{hashcalBdat,jdbcType=VARCHAR}, 
      #{hashcal,jdbcType=VARCHAR}, #{negative,jdbcType=VARCHAR}, #{hashcalExists,jdbcType=VARCHAR}, 
      #{knownIndex,jdbcType=VARCHAR}, #{sapmpGpose,jdbcType=VARCHAR}, #{angpn,jdbcType=VARCHAR}, 
      #{admoi,jdbcType=VARCHAR}, #{adpri,jdbcType=VARCHAR}, #{lprio,jdbcType=VARCHAR}, 
      #{adacn,jdbcType=VARCHAR}, #{afpnr,jdbcType=VARCHAR}, #{bsark,jdbcType=VARCHAR}, 
      #{audat,jdbcType=VARCHAR}, #{angnr,jdbcType=VARCHAR}, #{pnstat,jdbcType=VARCHAR}, 
      #{addns,jdbcType=VARCHAR}, #{serru,jdbcType=VARCHAR}, #{sernp,jdbcType=VARCHAR}, 
      #{disubSobkz,jdbcType=VARCHAR}, #{disubPspnr,jdbcType=VARCHAR}, #{disubKunnr,jdbcType=VARCHAR}, 
      #{disubVbeln,jdbcType=VARCHAR}, #{disubPosnr,jdbcType=VARCHAR}, #{disubOwner,jdbcType=VARCHAR}, 
      #{fshSeasonYear,jdbcType=VARCHAR}, #{fshSeason,jdbcType=VARCHAR}, #{fshCollection,jdbcType=VARCHAR}, 
      #{fshTheme,jdbcType=VARCHAR}, #{fshAtpDate,jdbcType=VARCHAR}, #{fshVasRel,jdbcType=VARCHAR}, 
      #{fshVasPrntId,jdbcType=VARCHAR}, #{fshTransaction,jdbcType=VARCHAR}, #{fshItemGroup,jdbcType=VARCHAR}, 
      #{fshItem,jdbcType=VARCHAR}, #{fshSs,jdbcType=VARCHAR}, #{fshGridCondRec,jdbcType=VARCHAR}, 
      #{fshPsmPfmSplit,jdbcType=VARCHAR}, #{cnfmQty,jdbcType=DECIMAL}, #{stpac,jdbcType=VARCHAR}, 
      #{lgbzo,jdbcType=VARCHAR}, #{lgbzoB,jdbcType=VARCHAR}, #{addrnum,jdbcType=VARCHAR}, 
      #{consnum,jdbcType=VARCHAR}, #{borgrMiss,jdbcType=VARCHAR}, #{depId,jdbcType=VARCHAR}, 
      #{belnr,jdbcType=VARCHAR}, #{kblposCab,jdbcType=VARCHAR}, #{kblnrComp,jdbcType=VARCHAR}, 
      #{kblposComp,jdbcType=VARCHAR}, #{wbsElement,jdbcType=VARCHAR}, #{rfmPsstRule,jdbcType=VARCHAR}, 
      #{rfmPsstGroup,jdbcType=VARCHAR}, #{refItem,jdbcType=VARCHAR}, #{sourceId,jdbcType=VARCHAR}, 
      #{sourceKey,jdbcType=VARCHAR}, #{putBack,jdbcType=VARCHAR}, #{polId,jdbcType=VARCHAR}, 
      #{consOrder,jdbcType=VARCHAR}, #{zzpaid,jdbcType=VARCHAR}, #{zzpamt,jdbcType=DECIMAL}, 
      #{zzsign,jdbcType=DECIMAL}, #{zzdate,jdbcType=VARCHAR}, #{zztime,jdbcType=VARCHAR}, 
      #{zzprice,jdbcType=VARCHAR}, #{zzpeinh,jdbcType=VARCHAR}, #{zzmwskz,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entity.SapEkpo" useGeneratedKeys="true">
    insert into SAP_EKPO
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="mandt != null">
        MANDT,
      </if>
      <if test="ebeln != null">
        EBELN,
      </if>
      <if test="ebelp != null">
        EBELP,
      </if>
      <if test="loekz != null">
        LOEKZ,
      </if>
      <if test="statu != null">
        STATU,
      </if>
      <if test="aedat != null">
        AEDAT,
      </if>
      <if test="txz01 != null">
        TXZ01,
      </if>
      <if test="matnr != null">
        MATNR,
      </if>
      <if test="ematn != null">
        EMATN,
      </if>
      <if test="bukrs != null">
        BUKRS,
      </if>
      <if test="werks != null">
        WERKS,
      </if>
      <if test="lgort != null">
        LGORT,
      </if>
      <if test="bednr != null">
        BEDNR,
      </if>
      <if test="matkl != null">
        MATKL,
      </if>
      <if test="infnr != null">
        INFNR,
      </if>
      <if test="idnlf != null">
        IDNLF,
      </if>
      <if test="ktmng != null">
        KTMNG,
      </if>
      <if test="menge != null">
        MENGE,
      </if>
      <if test="meins != null">
        MEINS,
      </if>
      <if test="bprme != null">
        BPRME,
      </if>
      <if test="bpumz != null">
        BPUMZ,
      </if>
      <if test="bpumn != null">
        BPUMN,
      </if>
      <if test="umrez != null">
        UMREZ,
      </if>
      <if test="umren != null">
        UMREN,
      </if>
      <if test="netpr != null">
        NETPR,
      </if>
      <if test="peinh != null">
        PEINH,
      </if>
      <if test="netwr != null">
        NETWR,
      </if>
      <if test="brtwr != null">
        BRTWR,
      </if>
      <if test="agdat != null">
        AGDAT,
      </if>
      <if test="webaz != null">
        WEBAZ,
      </if>
      <if test="mwskz != null">
        MWSKZ,
      </if>
      <if test="bonus != null">
        BONUS,
      </if>
      <if test="insmk != null">
        INSMK,
      </if>
      <if test="spinf != null">
        SPINF,
      </if>
      <if test="prsdr != null">
        PRSDR,
      </if>
      <if test="schpr != null">
        SCHPR,
      </if>
      <if test="mahnz != null">
        MAHNZ,
      </if>
      <if test="mahn1 != null">
        MAHN1,
      </if>
      <if test="mahn2 != null">
        MAHN2,
      </if>
      <if test="mahn3 != null">
        MAHN3,
      </if>
      <if test="uebto != null">
        UEBTO,
      </if>
      <if test="uebtk != null">
        UEBTK,
      </if>
      <if test="untto != null">
        UNTTO,
      </if>
      <if test="bwtar != null">
        BWTAR,
      </if>
      <if test="bwtty != null">
        BWTTY,
      </if>
      <if test="abskz != null">
        ABSKZ,
      </if>
      <if test="agmem != null">
        AGMEM,
      </if>
      <if test="elikz != null">
        ELIKZ,
      </if>
      <if test="erekz != null">
        EREKZ,
      </if>
      <if test="pstyp != null">
        PSTYP,
      </if>
      <if test="knttp != null">
        KNTTP,
      </if>
      <if test="kzvbr != null">
        KZVBR,
      </if>
      <if test="vrtkz != null">
        VRTKZ,
      </if>
      <if test="twrkz != null">
        TWRKZ,
      </if>
      <if test="wepos != null">
        WEPOS,
      </if>
      <if test="weunb != null">
        WEUNB,
      </if>
      <if test="repos != null">
        REPOS,
      </if>
      <if test="webre != null">
        WEBRE,
      </if>
      <if test="kzabs != null">
        KZABS,
      </if>
      <if test="labnr != null">
        LABNR,
      </if>
      <if test="konnr != null">
        KONNR,
      </if>
      <if test="ktpnr != null">
        KTPNR,
      </if>
      <if test="abdat != null">
        ABDAT,
      </if>
      <if test="abftz != null">
        ABFTZ,
      </if>
      <if test="etfz1 != null">
        ETFZ1,
      </if>
      <if test="etfz2 != null">
        ETFZ2,
      </if>
      <if test="kzstu != null">
        KZSTU,
      </if>
      <if test="notkz != null">
        NOTKZ,
      </if>
      <if test="lmein != null">
        LMEIN,
      </if>
      <if test="evers != null">
        EVERS,
      </if>
      <if test="zwert != null">
        ZWERT,
      </if>
      <if test="navnw != null">
        NAVNW,
      </if>
      <if test="abmng != null">
        ABMNG,
      </if>
      <if test="prdat != null">
        PRDAT,
      </if>
      <if test="bstyp != null">
        BSTYP,
      </if>
      <if test="effwr != null">
        EFFWR,
      </if>
      <if test="xoblr != null">
        XOBLR,
      </if>
      <if test="kunnr != null">
        KUNNR,
      </if>
      <if test="adrnr != null">
        ADRNR,
      </if>
      <if test="ekkol != null">
        EKKOL,
      </if>
      <if test="sktof != null">
        SKTOF,
      </if>
      <if test="stafo != null">
        STAFO,
      </if>
      <if test="plifz != null">
        PLIFZ,
      </if>
      <if test="ntgew != null">
        NTGEW,
      </if>
      <if test="gewei != null">
        GEWEI,
      </if>
      <if test="txjcd != null">
        TXJCD,
      </if>
      <if test="etdrk != null">
        ETDRK,
      </if>
      <if test="sobkz != null">
        SOBKZ,
      </if>
      <if test="arsnr != null">
        ARSNR,
      </if>
      <if test="arsps != null">
        ARSPS,
      </if>
      <if test="insnc != null">
        INSNC,
      </if>
      <if test="ssqss != null">
        SSQSS,
      </if>
      <if test="zgtyp != null">
        ZGTYP,
      </if>
      <if test="ean11 != null">
        EAN11,
      </if>
      <if test="bstae != null">
        BSTAE,
      </if>
      <if test="revlv != null">
        REVLV,
      </if>
      <if test="geber != null">
        GEBER,
      </if>
      <if test="fistl != null">
        FISTL,
      </if>
      <if test="fipos != null">
        FIPOS,
      </if>
      <if test="koGsber != null">
        KO_GSBER,
      </if>
      <if test="koPargb != null">
        KO_PARGB,
      </if>
      <if test="koPrctr != null">
        KO_PRCTR,
      </if>
      <if test="koPprctr != null">
        KO_PPRCTR,
      </if>
      <if test="meprf != null">
        MEPRF,
      </if>
      <if test="brgew != null">
        BRGEW,
      </if>
      <if test="volum != null">
        VOLUM,
      </if>
      <if test="voleh != null">
        VOLEH,
      </if>
      <if test="inco1 != null">
        INCO1,
      </if>
      <if test="inco2 != null">
        INCO2,
      </if>
      <if test="vorab != null">
        VORAB,
      </if>
      <if test="kolif != null">
        KOLIF,
      </if>
      <if test="ltsnr != null">
        LTSNR,
      </if>
      <if test="packno != null">
        PACKNO,
      </if>
      <if test="fplnr != null">
        FPLNR,
      </if>
      <if test="gnetwr != null">
        GNETWR,
      </if>
      <if test="stapo != null">
        STAPO,
      </if>
      <if test="uebpo != null">
        UEBPO,
      </if>
      <if test="lewed != null">
        LEWED,
      </if>
      <if test="emlif != null">
        EMLIF,
      </if>
      <if test="lblkz != null">
        LBLKZ,
      </if>
      <if test="satnr != null">
        SATNR,
      </if>
      <if test="attyp != null">
        ATTYP,
      </if>
      <if test="vsart != null">
        VSART,
      </if>
      <if test="handoverloc != null">
        HANDOVERLOC,
      </if>
      <if test="kanba != null">
        KANBA,
      </if>
      <if test="adrn2 != null">
        ADRN2,
      </if>
      <if test="cuobj != null">
        CUOBJ,
      </if>
      <if test="xersy != null">
        XERSY,
      </if>
      <if test="eildt != null">
        EILDT,
      </if>
      <if test="drdat != null">
        DRDAT,
      </if>
      <if test="druhr != null">
        DRUHR,
      </if>
      <if test="drunr != null">
        DRUNR,
      </if>
      <if test="aktnr != null">
        AKTNR,
      </if>
      <if test="abeln != null">
        ABELN,
      </if>
      <if test="abelp != null">
        ABELP,
      </if>
      <if test="anzpu != null">
        ANZPU,
      </if>
      <if test="punei != null">
        PUNEI,
      </if>
      <if test="saiso != null">
        SAISO,
      </if>
      <if test="saisj != null">
        SAISJ,
      </if>
      <if test="ebon2 != null">
        EBON2,
      </if>
      <if test="ebon3 != null">
        EBON3,
      </if>
      <if test="ebonf != null">
        EBONF,
      </if>
      <if test="mlmaa != null">
        MLMAA,
      </if>
      <if test="mhdrz != null">
        MHDRZ,
      </if>
      <if test="anfnr != null">
        ANFNR,
      </if>
      <if test="anfps != null">
        ANFPS,
      </if>
      <if test="kzkfg != null">
        KZKFG,
      </if>
      <if test="usequ != null">
        USEQU,
      </if>
      <if test="umsok != null">
        UMSOK,
      </if>
      <if test="banfn != null">
        BANFN,
      </if>
      <if test="bnfpo != null">
        BNFPO,
      </if>
      <if test="mtart != null">
        MTART,
      </if>
      <if test="uptyp != null">
        UPTYP,
      </if>
      <if test="upvor != null">
        UPVOR,
      </if>
      <if test="kzwi1 != null">
        KZWI1,
      </if>
      <if test="kzwi2 != null">
        KZWI2,
      </if>
      <if test="kzwi3 != null">
        KZWI3,
      </if>
      <if test="kzwi4 != null">
        KZWI4,
      </if>
      <if test="kzwi5 != null">
        KZWI5,
      </if>
      <if test="kzwi6 != null">
        KZWI6,
      </if>
      <if test="sikgr != null">
        SIKGR,
      </if>
      <if test="mfzhi != null">
        MFZHI,
      </if>
      <if test="ffzhi != null">
        FFZHI,
      </if>
      <if test="retpo != null">
        RETPO,
      </if>
      <if test="aurel != null">
        AUREL,
      </if>
      <if test="bsgru != null">
        BSGRU,
      </if>
      <if test="lfret != null">
        LFRET,
      </if>
      <if test="mfrgr != null">
        MFRGR,
      </if>
      <if test="nrfhg != null">
        NRFHG,
      </if>
      <if test="j1bnbm != null">
        J_1BNBM,
      </if>
      <if test="j1bmatuse != null">
        J_1BMATUSE,
      </if>
      <if test="j1bmatorg != null">
        J_1BMATORG,
      </if>
      <if test="j1bownpro != null">
        J_1BOWNPRO,
      </if>
      <if test="j1bindust != null">
        J_1BINDUST,
      </if>
      <if test="abueb != null">
        ABUEB,
      </if>
      <if test="nlabd != null">
        NLABD,
      </if>
      <if test="nfabd != null">
        NFABD,
      </if>
      <if test="kzbws != null">
        KZBWS,
      </if>
      <if test="bonba != null">
        BONBA,
      </if>
      <if test="fabkz != null">
        FABKZ,
      </if>
      <if test="j1aindxp != null">
        J_1AINDXP,
      </if>
      <if test="j1aidatep != null">
        J_1AIDATEP,
      </if>
      <if test="mprof != null">
        MPROF,
      </if>
      <if test="eglkz != null">
        EGLKZ,
      </if>
      <if test="kztlf != null">
        KZTLF,
      </if>
      <if test="kzfme != null">
        KZFME,
      </if>
      <if test="rdprf != null">
        RDPRF,
      </if>
      <if test="techs != null">
        TECHS,
      </if>
      <if test="chgSrv != null">
        CHG_SRV,
      </if>
      <if test="chgFplnr != null">
        CHG_FPLNR,
      </if>
      <if test="mfrpn != null">
        MFRPN,
      </if>
      <if test="mfrnr != null">
        MFRNR,
      </if>
      <if test="emnfr != null">
        EMNFR,
      </if>
      <if test="novet != null">
        NOVET,
      </if>
      <if test="afnam != null">
        AFNAM,
      </if>
      <if test="tzonrc != null">
        TZONRC,
      </if>
      <if test="iprkz != null">
        IPRKZ,
      </if>
      <if test="lebre != null">
        LEBRE,
      </if>
      <if test="berid != null">
        BERID,
      </if>
      <if test="xconditions != null">
        XCONDITIONS,
      </if>
      <if test="apoms != null">
        APOMS,
      </if>
      <if test="ccomp != null">
        CCOMP,
      </if>
      <if test="grantNbr != null">
        GRANT_NBR,
      </if>
      <if test="fkber != null">
        FKBER,
      </if>
      <if test="status != null">
        `STATUS`,
      </if>
      <if test="reslo != null">
        RESLO,
      </if>
      <if test="kblnr != null">
        KBLNR,
      </if>
      <if test="kblpos != null">
        KBLPOS,
      </if>
      <if test="weora != null">
        WEORA,
      </if>
      <if test="srvBasCom != null">
        SRV_BAS_COM,
      </if>
      <if test="prioUrg != null">
        PRIO_URG,
      </if>
      <if test="prioReq != null">
        PRIO_REQ,
      </if>
      <if test="empst != null">
        EMPST,
      </if>
      <if test="diffInvoice != null">
        DIFF_INVOICE,
      </if>
      <if test="trmriskRelevant != null">
        TRMRISK_RELEVANT,
      </if>
      <if test="speAbgru != null">
        SPE_ABGRU,
      </if>
      <if test="speCrmSo != null">
        SPE_CRM_SO,
      </if>
      <if test="speCrmSoItem != null">
        SPE_CRM_SO_ITEM,
      </if>
      <if test="speCrmRefSo != null">
        SPE_CRM_REF_SO,
      </if>
      <if test="speCrmRefItem != null">
        SPE_CRM_REF_ITEM,
      </if>
      <if test="speCrmFkrel != null">
        SPE_CRM_FKREL,
      </if>
      <if test="speChngSys != null">
        SPE_CHNG_SYS,
      </if>
      <if test="speInsmkSrc != null">
        SPE_INSMK_SRC,
      </if>
      <if test="speCqCtrltype != null">
        SPE_CQ_CTRLTYPE,
      </if>
      <if test="speCqNocq != null">
        SPE_CQ_NOCQ,
      </if>
      <if test="reasonCode != null">
        REASON_CODE,
      </if>
      <if test="cquSar != null">
        CQU_SAR,
      </if>
      <if test="anzsn != null">
        ANZSN,
      </if>
      <if test="speEwmDtc != null">
        SPE_EWM_DTC,
      </if>
      <if test="exlin != null">
        EXLIN,
      </if>
      <if test="exsnr != null">
        EXSNR,
      </if>
      <if test="ehtyp != null">
        EHTYP,
      </if>
      <if test="retpc != null">
        RETPC,
      </if>
      <if test="dptyp != null">
        DPTYP,
      </if>
      <if test="dppct != null">
        DPPCT,
      </if>
      <if test="dpamt != null">
        DPAMT,
      </if>
      <if test="dpdat != null">
        DPDAT,
      </if>
      <if test="flsRsto != null">
        FLS_RSTO,
      </if>
      <if test="extRfxNumber != null">
        EXT_RFX_NUMBER,
      </if>
      <if test="extRfxItem != null">
        EXT_RFX_ITEM,
      </if>
      <if test="extRfxSystem != null">
        EXT_RFX_SYSTEM,
      </if>
      <if test="srmContractId != null">
        SRM_CONTRACT_ID,
      </if>
      <if test="srmContractItm != null">
        SRM_CONTRACT_ITM,
      </if>
      <if test="blkReasonId != null">
        BLK_REASON_ID,
      </if>
      <if test="blkReasonTxt != null">
        BLK_REASON_TXT,
      </if>
      <if test="itcons != null">
        ITCONS,
      </if>
      <if test="fixmg != null">
        FIXMG,
      </if>
      <if test="wabwe != null">
        WABWE,
      </if>
      <if test="cmplDlvItm != null">
        CMPL_DLV_ITM,
      </if>
      <if test="inco2L != null">
        INCO2_L,
      </if>
      <if test="inco3L != null">
        INCO3_L,
      </if>
      <if test="stawn != null">
        STAWN,
      </if>
      <if test="isvco != null">
        ISVCO,
      </if>
      <if test="grwrt != null">
        GRWRT,
      </if>
      <if test="serviceperformer != null">
        SERVICEPERFORMER,
      </if>
      <if test="producttype != null">
        PRODUCTTYPE,
      </if>
      <if test="requestforquotation != null">
        REQUESTFORQUOTATION,
      </if>
      <if test="requestforquotationitem != null">
        REQUESTFORQUOTATIONITEM,
      </if>
      <if test="externalreferenceid != null">
        EXTERNALREFERENCEID,
      </if>
      <if test="tcAutDet != null">
        TC_AUT_DET,
      </if>
      <if test="manualTcReason != null">
        MANUAL_TC_REASON,
      </if>
      <if test="fiscalIncentive != null">
        FISCAL_INCENTIVE,
      </if>
      <if test="taxSubjectSt != null">
        TAX_SUBJECT_ST,
      </if>
      <if test="fiscalIncentiveId != null">
        FISCAL_INCENTIVE_ID,
      </if>
      <if test="sfTxjcd != null">
        SF_TXJCD,
      </if>
      <if test="dummyEkpoInclEewPs != null">
        DUMMY_EKPO_INCL_EEW_PS,
      </if>
      <if test="expectedValue != null">
        EXPECTED_VALUE,
      </if>
      <if test="limitAmount != null">
        LIMIT_AMOUNT,
      </if>
      <if test="enhDate1 != null">
        ENH_DATE1,
      </if>
      <if test="enhDate2 != null">
        ENH_DATE2,
      </if>
      <if test="enhPercent != null">
        ENH_PERCENT,
      </if>
      <if test="enhNumc1 != null">
        ENH_NUMC1,
      </if>
      <if test="dataaging != null">
        DATAAGING,
      </if>
      <if test="bev1NegenItem != null">
        BEV1_NEGEN_ITEM,
      </if>
      <if test="bev1Nedepfree != null">
        BEV1_NEDEPFREE,
      </if>
      <if test="bev1Nestruccat != null">
        BEV1_NESTRUCCAT,
      </if>
      <if test="advcode != null">
        ADVCODE,
      </if>
      <if test="budgetPd != null">
        BUDGET_PD,
      </if>
      <if test="excpe != null">
        EXCPE,
      </if>
      <if test="fmfgusKey != null">
        FMFGUS_KEY,
      </if>
      <if test="iuidRelevant != null">
        IUID_RELEVANT,
      </if>
      <if test="mrpind != null">
        MRPIND,
      </if>
      <if test="sgtScat != null">
        SGT_SCAT,
      </if>
      <if test="sgtRcat != null">
        SGT_RCAT,
      </if>
      <if test="tmsRefUuid != null">
        TMS_REF_UUID,
      </if>
      <if test="wrfCharstc1 != null">
        WRF_CHARSTC1,
      </if>
      <if test="wrfCharstc2 != null">
        WRF_CHARSTC2,
      </if>
      <if test="wrfCharstc3 != null">
        WRF_CHARSTC3,
      </if>
      <if test="zzzmdsq != null">
        ZZZMDSQ,
      </if>
      <if test="zzzmdhh != null">
        ZZZMDHH,
      </if>
      <if test="zzqxdjh != null">
        ZZQXDJH,
      </if>
      <if test="zzqxdhh != null">
        ZZQXDHH,
      </if>
      <if test="zzcxbjs != null">
        ZZCXBJS,
      </if>
      <if test="zzqtys != null">
        ZZQTYS,
      </if>
      <if test="zzspcd != null">
        ZZSPCD,
      </if>
      <if test="zzsccj != null">
        ZZSCCJ,
      </if>
      <if test="zzzscph != null">
        ZZZSCPH,
      </if>
      <if test="zzzgjj != null">
        ZZZGJJ,
      </if>
      <if test="refsite != null">
        REFSITE,
      </if>
      <if test="zapcgk != null">
        ZAPCGK,
      </if>
      <if test="apcgkExtend != null">
        APCGK_EXTEND,
      </if>
      <if test="zbasDate != null">
        ZBAS_DATE,
      </if>
      <if test="zadattyp != null">
        ZADATTYP,
      </if>
      <if test="zstartDat != null">
        ZSTART_DAT,
      </if>
      <if test="zDev != null">
        Z_DEV,
      </if>
      <if test="zindanx != null">
        ZINDANX,
      </if>
      <if test="zlimitDat != null">
        ZLIMIT_DAT,
      </if>
      <if test="numerator != null">
        NUMERATOR,
      </if>
      <if test="hashcalBdat != null">
        HASHCAL_BDAT,
      </if>
      <if test="hashcal != null">
        HASHCAL,
      </if>
      <if test="negative != null">
        NEGATIVE,
      </if>
      <if test="hashcalExists != null">
        HASHCAL_EXISTS,
      </if>
      <if test="knownIndex != null">
        KNOWN_INDEX,
      </if>
      <if test="sapmpGpose != null">
        SAPMP_GPOSE,
      </if>
      <if test="angpn != null">
        ANGPN,
      </if>
      <if test="admoi != null">
        ADMOI,
      </if>
      <if test="adpri != null">
        ADPRI,
      </if>
      <if test="lprio != null">
        LPRIO,
      </if>
      <if test="adacn != null">
        ADACN,
      </if>
      <if test="afpnr != null">
        AFPNR,
      </if>
      <if test="bsark != null">
        BSARK,
      </if>
      <if test="audat != null">
        AUDAT,
      </if>
      <if test="angnr != null">
        ANGNR,
      </if>
      <if test="pnstat != null">
        PNSTAT,
      </if>
      <if test="addns != null">
        ADDNS,
      </if>
      <if test="serru != null">
        SERRU,
      </if>
      <if test="sernp != null">
        SERNP,
      </if>
      <if test="disubSobkz != null">
        DISUB_SOBKZ,
      </if>
      <if test="disubPspnr != null">
        DISUB_PSPNR,
      </if>
      <if test="disubKunnr != null">
        DISUB_KUNNR,
      </if>
      <if test="disubVbeln != null">
        DISUB_VBELN,
      </if>
      <if test="disubPosnr != null">
        DISUB_POSNR,
      </if>
      <if test="disubOwner != null">
        DISUB_OWNER,
      </if>
      <if test="fshSeasonYear != null">
        FSH_SEASON_YEAR,
      </if>
      <if test="fshSeason != null">
        FSH_SEASON,
      </if>
      <if test="fshCollection != null">
        FSH_COLLECTION,
      </if>
      <if test="fshTheme != null">
        FSH_THEME,
      </if>
      <if test="fshAtpDate != null">
        FSH_ATP_DATE,
      </if>
      <if test="fshVasRel != null">
        FSH_VAS_REL,
      </if>
      <if test="fshVasPrntId != null">
        FSH_VAS_PRNT_ID,
      </if>
      <if test="fshTransaction != null">
        FSH_TRANSACTION,
      </if>
      <if test="fshItemGroup != null">
        FSH_ITEM_GROUP,
      </if>
      <if test="fshItem != null">
        FSH_ITEM,
      </if>
      <if test="fshSs != null">
        FSH_SS,
      </if>
      <if test="fshGridCondRec != null">
        FSH_GRID_COND_REC,
      </if>
      <if test="fshPsmPfmSplit != null">
        FSH_PSM_PFM_SPLIT,
      </if>
      <if test="cnfmQty != null">
        CNFM_QTY,
      </if>
      <if test="stpac != null">
        STPAC,
      </if>
      <if test="lgbzo != null">
        LGBZO,
      </if>
      <if test="lgbzoB != null">
        LGBZO_B,
      </if>
      <if test="addrnum != null">
        ADDRNUM,
      </if>
      <if test="consnum != null">
        CONSNUM,
      </if>
      <if test="borgrMiss != null">
        BORGR_MISS,
      </if>
      <if test="depId != null">
        DEP_ID,
      </if>
      <if test="belnr != null">
        BELNR,
      </if>
      <if test="kblposCab != null">
        KBLPOS_CAB,
      </if>
      <if test="kblnrComp != null">
        KBLNR_COMP,
      </if>
      <if test="kblposComp != null">
        KBLPOS_COMP,
      </if>
      <if test="wbsElement != null">
        WBS_ELEMENT,
      </if>
      <if test="rfmPsstRule != null">
        RFM_PSST_RULE,
      </if>
      <if test="rfmPsstGroup != null">
        RFM_PSST_GROUP,
      </if>
      <if test="refItem != null">
        REF_ITEM,
      </if>
      <if test="sourceId != null">
        SOURCE_ID,
      </if>
      <if test="sourceKey != null">
        SOURCE_KEY,
      </if>
      <if test="putBack != null">
        PUT_BACK,
      </if>
      <if test="polId != null">
        POL_ID,
      </if>
      <if test="consOrder != null">
        CONS_ORDER,
      </if>
      <if test="zzpaid != null">
        ZZPAID,
      </if>
      <if test="zzpamt != null">
        ZZPAMT,
      </if>
      <if test="zzsign != null">
        ZZSIGN,
      </if>
      <if test="zzdate != null">
        ZZDATE,
      </if>
      <if test="zztime != null">
        ZZTIME,
      </if>
      <if test="zzprice != null">
        ZZPRICE,
      </if>
      <if test="zzpeinh != null">
        ZZPEINH,
      </if>
      <if test="zzmwskz != null">
        ZZMWSKZ,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="mandt != null">
        #{mandt,jdbcType=VARCHAR},
      </if>
      <if test="ebeln != null">
        #{ebeln,jdbcType=VARCHAR},
      </if>
      <if test="ebelp != null">
        #{ebelp,jdbcType=VARCHAR},
      </if>
      <if test="loekz != null">
        #{loekz,jdbcType=VARCHAR},
      </if>
      <if test="statu != null">
        #{statu,jdbcType=VARCHAR},
      </if>
      <if test="aedat != null">
        #{aedat,jdbcType=VARCHAR},
      </if>
      <if test="txz01 != null">
        #{txz01,jdbcType=VARCHAR},
      </if>
      <if test="matnr != null">
        #{matnr,jdbcType=VARCHAR},
      </if>
      <if test="ematn != null">
        #{ematn,jdbcType=VARCHAR},
      </if>
      <if test="bukrs != null">
        #{bukrs,jdbcType=VARCHAR},
      </if>
      <if test="werks != null">
        #{werks,jdbcType=VARCHAR},
      </if>
      <if test="lgort != null">
        #{lgort,jdbcType=VARCHAR},
      </if>
      <if test="bednr != null">
        #{bednr,jdbcType=VARCHAR},
      </if>
      <if test="matkl != null">
        #{matkl,jdbcType=VARCHAR},
      </if>
      <if test="infnr != null">
        #{infnr,jdbcType=VARCHAR},
      </if>
      <if test="idnlf != null">
        #{idnlf,jdbcType=VARCHAR},
      </if>
      <if test="ktmng != null">
        #{ktmng,jdbcType=DECIMAL},
      </if>
      <if test="menge != null">
        #{menge,jdbcType=DECIMAL},
      </if>
      <if test="meins != null">
        #{meins,jdbcType=VARCHAR},
      </if>
      <if test="bprme != null">
        #{bprme,jdbcType=VARCHAR},
      </if>
      <if test="bpumz != null">
        #{bpumz,jdbcType=DECIMAL},
      </if>
      <if test="bpumn != null">
        #{bpumn,jdbcType=DECIMAL},
      </if>
      <if test="umrez != null">
        #{umrez,jdbcType=DECIMAL},
      </if>
      <if test="umren != null">
        #{umren,jdbcType=DECIMAL},
      </if>
      <if test="netpr != null">
        #{netpr,jdbcType=DECIMAL},
      </if>
      <if test="peinh != null">
        #{peinh,jdbcType=DECIMAL},
      </if>
      <if test="netwr != null">
        #{netwr,jdbcType=DECIMAL},
      </if>
      <if test="brtwr != null">
        #{brtwr,jdbcType=DECIMAL},
      </if>
      <if test="agdat != null">
        #{agdat,jdbcType=VARCHAR},
      </if>
      <if test="webaz != null">
        #{webaz,jdbcType=DECIMAL},
      </if>
      <if test="mwskz != null">
        #{mwskz,jdbcType=VARCHAR},
      </if>
      <if test="bonus != null">
        #{bonus,jdbcType=VARCHAR},
      </if>
      <if test="insmk != null">
        #{insmk,jdbcType=VARCHAR},
      </if>
      <if test="spinf != null">
        #{spinf,jdbcType=VARCHAR},
      </if>
      <if test="prsdr != null">
        #{prsdr,jdbcType=VARCHAR},
      </if>
      <if test="schpr != null">
        #{schpr,jdbcType=VARCHAR},
      </if>
      <if test="mahnz != null">
        #{mahnz,jdbcType=DECIMAL},
      </if>
      <if test="mahn1 != null">
        #{mahn1,jdbcType=DECIMAL},
      </if>
      <if test="mahn2 != null">
        #{mahn2,jdbcType=DECIMAL},
      </if>
      <if test="mahn3 != null">
        #{mahn3,jdbcType=DECIMAL},
      </if>
      <if test="uebto != null">
        #{uebto,jdbcType=DECIMAL},
      </if>
      <if test="uebtk != null">
        #{uebtk,jdbcType=VARCHAR},
      </if>
      <if test="untto != null">
        #{untto,jdbcType=DECIMAL},
      </if>
      <if test="bwtar != null">
        #{bwtar,jdbcType=VARCHAR},
      </if>
      <if test="bwtty != null">
        #{bwtty,jdbcType=VARCHAR},
      </if>
      <if test="abskz != null">
        #{abskz,jdbcType=VARCHAR},
      </if>
      <if test="agmem != null">
        #{agmem,jdbcType=VARCHAR},
      </if>
      <if test="elikz != null">
        #{elikz,jdbcType=VARCHAR},
      </if>
      <if test="erekz != null">
        #{erekz,jdbcType=VARCHAR},
      </if>
      <if test="pstyp != null">
        #{pstyp,jdbcType=VARCHAR},
      </if>
      <if test="knttp != null">
        #{knttp,jdbcType=VARCHAR},
      </if>
      <if test="kzvbr != null">
        #{kzvbr,jdbcType=VARCHAR},
      </if>
      <if test="vrtkz != null">
        #{vrtkz,jdbcType=VARCHAR},
      </if>
      <if test="twrkz != null">
        #{twrkz,jdbcType=VARCHAR},
      </if>
      <if test="wepos != null">
        #{wepos,jdbcType=VARCHAR},
      </if>
      <if test="weunb != null">
        #{weunb,jdbcType=VARCHAR},
      </if>
      <if test="repos != null">
        #{repos,jdbcType=VARCHAR},
      </if>
      <if test="webre != null">
        #{webre,jdbcType=VARCHAR},
      </if>
      <if test="kzabs != null">
        #{kzabs,jdbcType=VARCHAR},
      </if>
      <if test="labnr != null">
        #{labnr,jdbcType=VARCHAR},
      </if>
      <if test="konnr != null">
        #{konnr,jdbcType=VARCHAR},
      </if>
      <if test="ktpnr != null">
        #{ktpnr,jdbcType=VARCHAR},
      </if>
      <if test="abdat != null">
        #{abdat,jdbcType=VARCHAR},
      </if>
      <if test="abftz != null">
        #{abftz,jdbcType=DECIMAL},
      </if>
      <if test="etfz1 != null">
        #{etfz1,jdbcType=DECIMAL},
      </if>
      <if test="etfz2 != null">
        #{etfz2,jdbcType=DECIMAL},
      </if>
      <if test="kzstu != null">
        #{kzstu,jdbcType=VARCHAR},
      </if>
      <if test="notkz != null">
        #{notkz,jdbcType=VARCHAR},
      </if>
      <if test="lmein != null">
        #{lmein,jdbcType=VARCHAR},
      </if>
      <if test="evers != null">
        #{evers,jdbcType=VARCHAR},
      </if>
      <if test="zwert != null">
        #{zwert,jdbcType=DECIMAL},
      </if>
      <if test="navnw != null">
        #{navnw,jdbcType=DECIMAL},
      </if>
      <if test="abmng != null">
        #{abmng,jdbcType=DECIMAL},
      </if>
      <if test="prdat != null">
        #{prdat,jdbcType=VARCHAR},
      </if>
      <if test="bstyp != null">
        #{bstyp,jdbcType=VARCHAR},
      </if>
      <if test="effwr != null">
        #{effwr,jdbcType=DECIMAL},
      </if>
      <if test="xoblr != null">
        #{xoblr,jdbcType=VARCHAR},
      </if>
      <if test="kunnr != null">
        #{kunnr,jdbcType=VARCHAR},
      </if>
      <if test="adrnr != null">
        #{adrnr,jdbcType=VARCHAR},
      </if>
      <if test="ekkol != null">
        #{ekkol,jdbcType=VARCHAR},
      </if>
      <if test="sktof != null">
        #{sktof,jdbcType=VARCHAR},
      </if>
      <if test="stafo != null">
        #{stafo,jdbcType=VARCHAR},
      </if>
      <if test="plifz != null">
        #{plifz,jdbcType=DECIMAL},
      </if>
      <if test="ntgew != null">
        #{ntgew,jdbcType=DECIMAL},
      </if>
      <if test="gewei != null">
        #{gewei,jdbcType=VARCHAR},
      </if>
      <if test="txjcd != null">
        #{txjcd,jdbcType=VARCHAR},
      </if>
      <if test="etdrk != null">
        #{etdrk,jdbcType=VARCHAR},
      </if>
      <if test="sobkz != null">
        #{sobkz,jdbcType=VARCHAR},
      </if>
      <if test="arsnr != null">
        #{arsnr,jdbcType=VARCHAR},
      </if>
      <if test="arsps != null">
        #{arsps,jdbcType=VARCHAR},
      </if>
      <if test="insnc != null">
        #{insnc,jdbcType=VARCHAR},
      </if>
      <if test="ssqss != null">
        #{ssqss,jdbcType=VARCHAR},
      </if>
      <if test="zgtyp != null">
        #{zgtyp,jdbcType=VARCHAR},
      </if>
      <if test="ean11 != null">
        #{ean11,jdbcType=VARCHAR},
      </if>
      <if test="bstae != null">
        #{bstae,jdbcType=VARCHAR},
      </if>
      <if test="revlv != null">
        #{revlv,jdbcType=VARCHAR},
      </if>
      <if test="geber != null">
        #{geber,jdbcType=VARCHAR},
      </if>
      <if test="fistl != null">
        #{fistl,jdbcType=VARCHAR},
      </if>
      <if test="fipos != null">
        #{fipos,jdbcType=VARCHAR},
      </if>
      <if test="koGsber != null">
        #{koGsber,jdbcType=VARCHAR},
      </if>
      <if test="koPargb != null">
        #{koPargb,jdbcType=VARCHAR},
      </if>
      <if test="koPrctr != null">
        #{koPrctr,jdbcType=VARCHAR},
      </if>
      <if test="koPprctr != null">
        #{koPprctr,jdbcType=VARCHAR},
      </if>
      <if test="meprf != null">
        #{meprf,jdbcType=VARCHAR},
      </if>
      <if test="brgew != null">
        #{brgew,jdbcType=DECIMAL},
      </if>
      <if test="volum != null">
        #{volum,jdbcType=DECIMAL},
      </if>
      <if test="voleh != null">
        #{voleh,jdbcType=VARCHAR},
      </if>
      <if test="inco1 != null">
        #{inco1,jdbcType=VARCHAR},
      </if>
      <if test="inco2 != null">
        #{inco2,jdbcType=VARCHAR},
      </if>
      <if test="vorab != null">
        #{vorab,jdbcType=VARCHAR},
      </if>
      <if test="kolif != null">
        #{kolif,jdbcType=VARCHAR},
      </if>
      <if test="ltsnr != null">
        #{ltsnr,jdbcType=VARCHAR},
      </if>
      <if test="packno != null">
        #{packno,jdbcType=VARCHAR},
      </if>
      <if test="fplnr != null">
        #{fplnr,jdbcType=VARCHAR},
      </if>
      <if test="gnetwr != null">
        #{gnetwr,jdbcType=DECIMAL},
      </if>
      <if test="stapo != null">
        #{stapo,jdbcType=VARCHAR},
      </if>
      <if test="uebpo != null">
        #{uebpo,jdbcType=VARCHAR},
      </if>
      <if test="lewed != null">
        #{lewed,jdbcType=VARCHAR},
      </if>
      <if test="emlif != null">
        #{emlif,jdbcType=VARCHAR},
      </if>
      <if test="lblkz != null">
        #{lblkz,jdbcType=VARCHAR},
      </if>
      <if test="satnr != null">
        #{satnr,jdbcType=VARCHAR},
      </if>
      <if test="attyp != null">
        #{attyp,jdbcType=VARCHAR},
      </if>
      <if test="vsart != null">
        #{vsart,jdbcType=VARCHAR},
      </if>
      <if test="handoverloc != null">
        #{handoverloc,jdbcType=VARCHAR},
      </if>
      <if test="kanba != null">
        #{kanba,jdbcType=VARCHAR},
      </if>
      <if test="adrn2 != null">
        #{adrn2,jdbcType=VARCHAR},
      </if>
      <if test="cuobj != null">
        #{cuobj,jdbcType=VARCHAR},
      </if>
      <if test="xersy != null">
        #{xersy,jdbcType=VARCHAR},
      </if>
      <if test="eildt != null">
        #{eildt,jdbcType=VARCHAR},
      </if>
      <if test="drdat != null">
        #{drdat,jdbcType=VARCHAR},
      </if>
      <if test="druhr != null">
        #{druhr,jdbcType=VARCHAR},
      </if>
      <if test="drunr != null">
        #{drunr,jdbcType=VARCHAR},
      </if>
      <if test="aktnr != null">
        #{aktnr,jdbcType=VARCHAR},
      </if>
      <if test="abeln != null">
        #{abeln,jdbcType=VARCHAR},
      </if>
      <if test="abelp != null">
        #{abelp,jdbcType=VARCHAR},
      </if>
      <if test="anzpu != null">
        #{anzpu,jdbcType=DECIMAL},
      </if>
      <if test="punei != null">
        #{punei,jdbcType=VARCHAR},
      </if>
      <if test="saiso != null">
        #{saiso,jdbcType=VARCHAR},
      </if>
      <if test="saisj != null">
        #{saisj,jdbcType=VARCHAR},
      </if>
      <if test="ebon2 != null">
        #{ebon2,jdbcType=VARCHAR},
      </if>
      <if test="ebon3 != null">
        #{ebon3,jdbcType=VARCHAR},
      </if>
      <if test="ebonf != null">
        #{ebonf,jdbcType=VARCHAR},
      </if>
      <if test="mlmaa != null">
        #{mlmaa,jdbcType=VARCHAR},
      </if>
      <if test="mhdrz != null">
        #{mhdrz,jdbcType=DECIMAL},
      </if>
      <if test="anfnr != null">
        #{anfnr,jdbcType=VARCHAR},
      </if>
      <if test="anfps != null">
        #{anfps,jdbcType=VARCHAR},
      </if>
      <if test="kzkfg != null">
        #{kzkfg,jdbcType=VARCHAR},
      </if>
      <if test="usequ != null">
        #{usequ,jdbcType=VARCHAR},
      </if>
      <if test="umsok != null">
        #{umsok,jdbcType=VARCHAR},
      </if>
      <if test="banfn != null">
        #{banfn,jdbcType=VARCHAR},
      </if>
      <if test="bnfpo != null">
        #{bnfpo,jdbcType=VARCHAR},
      </if>
      <if test="mtart != null">
        #{mtart,jdbcType=VARCHAR},
      </if>
      <if test="uptyp != null">
        #{uptyp,jdbcType=VARCHAR},
      </if>
      <if test="upvor != null">
        #{upvor,jdbcType=VARCHAR},
      </if>
      <if test="kzwi1 != null">
        #{kzwi1,jdbcType=DECIMAL},
      </if>
      <if test="kzwi2 != null">
        #{kzwi2,jdbcType=DECIMAL},
      </if>
      <if test="kzwi3 != null">
        #{kzwi3,jdbcType=DECIMAL},
      </if>
      <if test="kzwi4 != null">
        #{kzwi4,jdbcType=DECIMAL},
      </if>
      <if test="kzwi5 != null">
        #{kzwi5,jdbcType=DECIMAL},
      </if>
      <if test="kzwi6 != null">
        #{kzwi6,jdbcType=DECIMAL},
      </if>
      <if test="sikgr != null">
        #{sikgr,jdbcType=VARCHAR},
      </if>
      <if test="mfzhi != null">
        #{mfzhi,jdbcType=DECIMAL},
      </if>
      <if test="ffzhi != null">
        #{ffzhi,jdbcType=DECIMAL},
      </if>
      <if test="retpo != null">
        #{retpo,jdbcType=VARCHAR},
      </if>
      <if test="aurel != null">
        #{aurel,jdbcType=VARCHAR},
      </if>
      <if test="bsgru != null">
        #{bsgru,jdbcType=VARCHAR},
      </if>
      <if test="lfret != null">
        #{lfret,jdbcType=VARCHAR},
      </if>
      <if test="mfrgr != null">
        #{mfrgr,jdbcType=VARCHAR},
      </if>
      <if test="nrfhg != null">
        #{nrfhg,jdbcType=VARCHAR},
      </if>
      <if test="j1bnbm != null">
        #{j1bnbm,jdbcType=VARCHAR},
      </if>
      <if test="j1bmatuse != null">
        #{j1bmatuse,jdbcType=VARCHAR},
      </if>
      <if test="j1bmatorg != null">
        #{j1bmatorg,jdbcType=VARCHAR},
      </if>
      <if test="j1bownpro != null">
        #{j1bownpro,jdbcType=VARCHAR},
      </if>
      <if test="j1bindust != null">
        #{j1bindust,jdbcType=VARCHAR},
      </if>
      <if test="abueb != null">
        #{abueb,jdbcType=VARCHAR},
      </if>
      <if test="nlabd != null">
        #{nlabd,jdbcType=VARCHAR},
      </if>
      <if test="nfabd != null">
        #{nfabd,jdbcType=VARCHAR},
      </if>
      <if test="kzbws != null">
        #{kzbws,jdbcType=VARCHAR},
      </if>
      <if test="bonba != null">
        #{bonba,jdbcType=DECIMAL},
      </if>
      <if test="fabkz != null">
        #{fabkz,jdbcType=VARCHAR},
      </if>
      <if test="j1aindxp != null">
        #{j1aindxp,jdbcType=VARCHAR},
      </if>
      <if test="j1aidatep != null">
        #{j1aidatep,jdbcType=VARCHAR},
      </if>
      <if test="mprof != null">
        #{mprof,jdbcType=VARCHAR},
      </if>
      <if test="eglkz != null">
        #{eglkz,jdbcType=VARCHAR},
      </if>
      <if test="kztlf != null">
        #{kztlf,jdbcType=VARCHAR},
      </if>
      <if test="kzfme != null">
        #{kzfme,jdbcType=VARCHAR},
      </if>
      <if test="rdprf != null">
        #{rdprf,jdbcType=VARCHAR},
      </if>
      <if test="techs != null">
        #{techs,jdbcType=VARCHAR},
      </if>
      <if test="chgSrv != null">
        #{chgSrv,jdbcType=VARCHAR},
      </if>
      <if test="chgFplnr != null">
        #{chgFplnr,jdbcType=VARCHAR},
      </if>
      <if test="mfrpn != null">
        #{mfrpn,jdbcType=VARCHAR},
      </if>
      <if test="mfrnr != null">
        #{mfrnr,jdbcType=VARCHAR},
      </if>
      <if test="emnfr != null">
        #{emnfr,jdbcType=VARCHAR},
      </if>
      <if test="novet != null">
        #{novet,jdbcType=VARCHAR},
      </if>
      <if test="afnam != null">
        #{afnam,jdbcType=VARCHAR},
      </if>
      <if test="tzonrc != null">
        #{tzonrc,jdbcType=VARCHAR},
      </if>
      <if test="iprkz != null">
        #{iprkz,jdbcType=VARCHAR},
      </if>
      <if test="lebre != null">
        #{lebre,jdbcType=VARCHAR},
      </if>
      <if test="berid != null">
        #{berid,jdbcType=VARCHAR},
      </if>
      <if test="xconditions != null">
        #{xconditions,jdbcType=VARCHAR},
      </if>
      <if test="apoms != null">
        #{apoms,jdbcType=VARCHAR},
      </if>
      <if test="ccomp != null">
        #{ccomp,jdbcType=VARCHAR},
      </if>
      <if test="grantNbr != null">
        #{grantNbr,jdbcType=VARCHAR},
      </if>
      <if test="fkber != null">
        #{fkber,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="reslo != null">
        #{reslo,jdbcType=VARCHAR},
      </if>
      <if test="kblnr != null">
        #{kblnr,jdbcType=VARCHAR},
      </if>
      <if test="kblpos != null">
        #{kblpos,jdbcType=VARCHAR},
      </if>
      <if test="weora != null">
        #{weora,jdbcType=VARCHAR},
      </if>
      <if test="srvBasCom != null">
        #{srvBasCom,jdbcType=VARCHAR},
      </if>
      <if test="prioUrg != null">
        #{prioUrg,jdbcType=VARCHAR},
      </if>
      <if test="prioReq != null">
        #{prioReq,jdbcType=VARCHAR},
      </if>
      <if test="empst != null">
        #{empst,jdbcType=VARCHAR},
      </if>
      <if test="diffInvoice != null">
        #{diffInvoice,jdbcType=VARCHAR},
      </if>
      <if test="trmriskRelevant != null">
        #{trmriskRelevant,jdbcType=VARCHAR},
      </if>
      <if test="speAbgru != null">
        #{speAbgru,jdbcType=VARCHAR},
      </if>
      <if test="speCrmSo != null">
        #{speCrmSo,jdbcType=VARCHAR},
      </if>
      <if test="speCrmSoItem != null">
        #{speCrmSoItem,jdbcType=VARCHAR},
      </if>
      <if test="speCrmRefSo != null">
        #{speCrmRefSo,jdbcType=VARCHAR},
      </if>
      <if test="speCrmRefItem != null">
        #{speCrmRefItem,jdbcType=VARCHAR},
      </if>
      <if test="speCrmFkrel != null">
        #{speCrmFkrel,jdbcType=VARCHAR},
      </if>
      <if test="speChngSys != null">
        #{speChngSys,jdbcType=VARCHAR},
      </if>
      <if test="speInsmkSrc != null">
        #{speInsmkSrc,jdbcType=VARCHAR},
      </if>
      <if test="speCqCtrltype != null">
        #{speCqCtrltype,jdbcType=VARCHAR},
      </if>
      <if test="speCqNocq != null">
        #{speCqNocq,jdbcType=VARCHAR},
      </if>
      <if test="reasonCode != null">
        #{reasonCode,jdbcType=VARCHAR},
      </if>
      <if test="cquSar != null">
        #{cquSar,jdbcType=DECIMAL},
      </if>
      <if test="anzsn != null">
        #{anzsn,jdbcType=INTEGER},
      </if>
      <if test="speEwmDtc != null">
        #{speEwmDtc,jdbcType=VARCHAR},
      </if>
      <if test="exlin != null">
        #{exlin,jdbcType=VARCHAR},
      </if>
      <if test="exsnr != null">
        #{exsnr,jdbcType=VARCHAR},
      </if>
      <if test="ehtyp != null">
        #{ehtyp,jdbcType=VARCHAR},
      </if>
      <if test="retpc != null">
        #{retpc,jdbcType=DECIMAL},
      </if>
      <if test="dptyp != null">
        #{dptyp,jdbcType=VARCHAR},
      </if>
      <if test="dppct != null">
        #{dppct,jdbcType=DECIMAL},
      </if>
      <if test="dpamt != null">
        #{dpamt,jdbcType=DECIMAL},
      </if>
      <if test="dpdat != null">
        #{dpdat,jdbcType=VARCHAR},
      </if>
      <if test="flsRsto != null">
        #{flsRsto,jdbcType=VARCHAR},
      </if>
      <if test="extRfxNumber != null">
        #{extRfxNumber,jdbcType=VARCHAR},
      </if>
      <if test="extRfxItem != null">
        #{extRfxItem,jdbcType=VARCHAR},
      </if>
      <if test="extRfxSystem != null">
        #{extRfxSystem,jdbcType=VARCHAR},
      </if>
      <if test="srmContractId != null">
        #{srmContractId,jdbcType=VARCHAR},
      </if>
      <if test="srmContractItm != null">
        #{srmContractItm,jdbcType=VARCHAR},
      </if>
      <if test="blkReasonId != null">
        #{blkReasonId,jdbcType=VARCHAR},
      </if>
      <if test="blkReasonTxt != null">
        #{blkReasonTxt,jdbcType=VARCHAR},
      </if>
      <if test="itcons != null">
        #{itcons,jdbcType=VARCHAR},
      </if>
      <if test="fixmg != null">
        #{fixmg,jdbcType=VARCHAR},
      </if>
      <if test="wabwe != null">
        #{wabwe,jdbcType=VARCHAR},
      </if>
      <if test="cmplDlvItm != null">
        #{cmplDlvItm,jdbcType=VARCHAR},
      </if>
      <if test="inco2L != null">
        #{inco2L,jdbcType=VARCHAR},
      </if>
      <if test="inco3L != null">
        #{inco3L,jdbcType=VARCHAR},
      </if>
      <if test="stawn != null">
        #{stawn,jdbcType=VARCHAR},
      </if>
      <if test="isvco != null">
        #{isvco,jdbcType=VARCHAR},
      </if>
      <if test="grwrt != null">
        #{grwrt,jdbcType=DECIMAL},
      </if>
      <if test="serviceperformer != null">
        #{serviceperformer,jdbcType=VARCHAR},
      </if>
      <if test="producttype != null">
        #{producttype,jdbcType=VARCHAR},
      </if>
      <if test="requestforquotation != null">
        #{requestforquotation,jdbcType=VARCHAR},
      </if>
      <if test="requestforquotationitem != null">
        #{requestforquotationitem,jdbcType=VARCHAR},
      </if>
      <if test="externalreferenceid != null">
        #{externalreferenceid,jdbcType=VARCHAR},
      </if>
      <if test="tcAutDet != null">
        #{tcAutDet,jdbcType=VARCHAR},
      </if>
      <if test="manualTcReason != null">
        #{manualTcReason,jdbcType=VARCHAR},
      </if>
      <if test="fiscalIncentive != null">
        #{fiscalIncentive,jdbcType=VARCHAR},
      </if>
      <if test="taxSubjectSt != null">
        #{taxSubjectSt,jdbcType=VARCHAR},
      </if>
      <if test="fiscalIncentiveId != null">
        #{fiscalIncentiveId,jdbcType=VARCHAR},
      </if>
      <if test="sfTxjcd != null">
        #{sfTxjcd,jdbcType=VARCHAR},
      </if>
      <if test="dummyEkpoInclEewPs != null">
        #{dummyEkpoInclEewPs,jdbcType=VARCHAR},
      </if>
      <if test="expectedValue != null">
        #{expectedValue,jdbcType=DECIMAL},
      </if>
      <if test="limitAmount != null">
        #{limitAmount,jdbcType=DECIMAL},
      </if>
      <if test="enhDate1 != null">
        #{enhDate1,jdbcType=VARCHAR},
      </if>
      <if test="enhDate2 != null">
        #{enhDate2,jdbcType=VARCHAR},
      </if>
      <if test="enhPercent != null">
        #{enhPercent,jdbcType=DECIMAL},
      </if>
      <if test="enhNumc1 != null">
        #{enhNumc1,jdbcType=VARCHAR},
      </if>
      <if test="dataaging != null">
        #{dataaging,jdbcType=VARCHAR},
      </if>
      <if test="bev1NegenItem != null">
        #{bev1NegenItem,jdbcType=VARCHAR},
      </if>
      <if test="bev1Nedepfree != null">
        #{bev1Nedepfree,jdbcType=VARCHAR},
      </if>
      <if test="bev1Nestruccat != null">
        #{bev1Nestruccat,jdbcType=VARCHAR},
      </if>
      <if test="advcode != null">
        #{advcode,jdbcType=VARCHAR},
      </if>
      <if test="budgetPd != null">
        #{budgetPd,jdbcType=VARCHAR},
      </if>
      <if test="excpe != null">
        #{excpe,jdbcType=VARCHAR},
      </if>
      <if test="fmfgusKey != null">
        #{fmfgusKey,jdbcType=VARCHAR},
      </if>
      <if test="iuidRelevant != null">
        #{iuidRelevant,jdbcType=VARCHAR},
      </if>
      <if test="mrpind != null">
        #{mrpind,jdbcType=VARCHAR},
      </if>
      <if test="sgtScat != null">
        #{sgtScat,jdbcType=VARCHAR},
      </if>
      <if test="sgtRcat != null">
        #{sgtRcat,jdbcType=VARCHAR},
      </if>
      <if test="tmsRefUuid != null">
        #{tmsRefUuid,jdbcType=VARCHAR},
      </if>
      <if test="wrfCharstc1 != null">
        #{wrfCharstc1,jdbcType=VARCHAR},
      </if>
      <if test="wrfCharstc2 != null">
        #{wrfCharstc2,jdbcType=VARCHAR},
      </if>
      <if test="wrfCharstc3 != null">
        #{wrfCharstc3,jdbcType=VARCHAR},
      </if>
      <if test="zzzmdsq != null">
        #{zzzmdsq,jdbcType=VARCHAR},
      </if>
      <if test="zzzmdhh != null">
        #{zzzmdhh,jdbcType=VARCHAR},
      </if>
      <if test="zzqxdjh != null">
        #{zzqxdjh,jdbcType=VARCHAR},
      </if>
      <if test="zzqxdhh != null">
        #{zzqxdhh,jdbcType=VARCHAR},
      </if>
      <if test="zzcxbjs != null">
        #{zzcxbjs,jdbcType=VARCHAR},
      </if>
      <if test="zzqtys != null">
        #{zzqtys,jdbcType=VARCHAR},
      </if>
      <if test="zzspcd != null">
        #{zzspcd,jdbcType=VARCHAR},
      </if>
      <if test="zzsccj != null">
        #{zzsccj,jdbcType=VARCHAR},
      </if>
      <if test="zzzscph != null">
        #{zzzscph,jdbcType=VARCHAR},
      </if>
      <if test="zzzgjj != null">
        #{zzzgjj,jdbcType=VARCHAR},
      </if>
      <if test="refsite != null">
        #{refsite,jdbcType=VARCHAR},
      </if>
      <if test="zapcgk != null">
        #{zapcgk,jdbcType=VARCHAR},
      </if>
      <if test="apcgkExtend != null">
        #{apcgkExtend,jdbcType=VARCHAR},
      </if>
      <if test="zbasDate != null">
        #{zbasDate,jdbcType=VARCHAR},
      </if>
      <if test="zadattyp != null">
        #{zadattyp,jdbcType=VARCHAR},
      </if>
      <if test="zstartDat != null">
        #{zstartDat,jdbcType=VARCHAR},
      </if>
      <if test="zDev != null">
        #{zDev,jdbcType=DECIMAL},
      </if>
      <if test="zindanx != null">
        #{zindanx,jdbcType=VARCHAR},
      </if>
      <if test="zlimitDat != null">
        #{zlimitDat,jdbcType=VARCHAR},
      </if>
      <if test="numerator != null">
        #{numerator,jdbcType=VARCHAR},
      </if>
      <if test="hashcalBdat != null">
        #{hashcalBdat,jdbcType=VARCHAR},
      </if>
      <if test="hashcal != null">
        #{hashcal,jdbcType=VARCHAR},
      </if>
      <if test="negative != null">
        #{negative,jdbcType=VARCHAR},
      </if>
      <if test="hashcalExists != null">
        #{hashcalExists,jdbcType=VARCHAR},
      </if>
      <if test="knownIndex != null">
        #{knownIndex,jdbcType=VARCHAR},
      </if>
      <if test="sapmpGpose != null">
        #{sapmpGpose,jdbcType=VARCHAR},
      </if>
      <if test="angpn != null">
        #{angpn,jdbcType=VARCHAR},
      </if>
      <if test="admoi != null">
        #{admoi,jdbcType=VARCHAR},
      </if>
      <if test="adpri != null">
        #{adpri,jdbcType=VARCHAR},
      </if>
      <if test="lprio != null">
        #{lprio,jdbcType=VARCHAR},
      </if>
      <if test="adacn != null">
        #{adacn,jdbcType=VARCHAR},
      </if>
      <if test="afpnr != null">
        #{afpnr,jdbcType=VARCHAR},
      </if>
      <if test="bsark != null">
        #{bsark,jdbcType=VARCHAR},
      </if>
      <if test="audat != null">
        #{audat,jdbcType=VARCHAR},
      </if>
      <if test="angnr != null">
        #{angnr,jdbcType=VARCHAR},
      </if>
      <if test="pnstat != null">
        #{pnstat,jdbcType=VARCHAR},
      </if>
      <if test="addns != null">
        #{addns,jdbcType=VARCHAR},
      </if>
      <if test="serru != null">
        #{serru,jdbcType=VARCHAR},
      </if>
      <if test="sernp != null">
        #{sernp,jdbcType=VARCHAR},
      </if>
      <if test="disubSobkz != null">
        #{disubSobkz,jdbcType=VARCHAR},
      </if>
      <if test="disubPspnr != null">
        #{disubPspnr,jdbcType=VARCHAR},
      </if>
      <if test="disubKunnr != null">
        #{disubKunnr,jdbcType=VARCHAR},
      </if>
      <if test="disubVbeln != null">
        #{disubVbeln,jdbcType=VARCHAR},
      </if>
      <if test="disubPosnr != null">
        #{disubPosnr,jdbcType=VARCHAR},
      </if>
      <if test="disubOwner != null">
        #{disubOwner,jdbcType=VARCHAR},
      </if>
      <if test="fshSeasonYear != null">
        #{fshSeasonYear,jdbcType=VARCHAR},
      </if>
      <if test="fshSeason != null">
        #{fshSeason,jdbcType=VARCHAR},
      </if>
      <if test="fshCollection != null">
        #{fshCollection,jdbcType=VARCHAR},
      </if>
      <if test="fshTheme != null">
        #{fshTheme,jdbcType=VARCHAR},
      </if>
      <if test="fshAtpDate != null">
        #{fshAtpDate,jdbcType=VARCHAR},
      </if>
      <if test="fshVasRel != null">
        #{fshVasRel,jdbcType=VARCHAR},
      </if>
      <if test="fshVasPrntId != null">
        #{fshVasPrntId,jdbcType=VARCHAR},
      </if>
      <if test="fshTransaction != null">
        #{fshTransaction,jdbcType=VARCHAR},
      </if>
      <if test="fshItemGroup != null">
        #{fshItemGroup,jdbcType=VARCHAR},
      </if>
      <if test="fshItem != null">
        #{fshItem,jdbcType=VARCHAR},
      </if>
      <if test="fshSs != null">
        #{fshSs,jdbcType=VARCHAR},
      </if>
      <if test="fshGridCondRec != null">
        #{fshGridCondRec,jdbcType=VARCHAR},
      </if>
      <if test="fshPsmPfmSplit != null">
        #{fshPsmPfmSplit,jdbcType=VARCHAR},
      </if>
      <if test="cnfmQty != null">
        #{cnfmQty,jdbcType=DECIMAL},
      </if>
      <if test="stpac != null">
        #{stpac,jdbcType=VARCHAR},
      </if>
      <if test="lgbzo != null">
        #{lgbzo,jdbcType=VARCHAR},
      </if>
      <if test="lgbzoB != null">
        #{lgbzoB,jdbcType=VARCHAR},
      </if>
      <if test="addrnum != null">
        #{addrnum,jdbcType=VARCHAR},
      </if>
      <if test="consnum != null">
        #{consnum,jdbcType=VARCHAR},
      </if>
      <if test="borgrMiss != null">
        #{borgrMiss,jdbcType=VARCHAR},
      </if>
      <if test="depId != null">
        #{depId,jdbcType=VARCHAR},
      </if>
      <if test="belnr != null">
        #{belnr,jdbcType=VARCHAR},
      </if>
      <if test="kblposCab != null">
        #{kblposCab,jdbcType=VARCHAR},
      </if>
      <if test="kblnrComp != null">
        #{kblnrComp,jdbcType=VARCHAR},
      </if>
      <if test="kblposComp != null">
        #{kblposComp,jdbcType=VARCHAR},
      </if>
      <if test="wbsElement != null">
        #{wbsElement,jdbcType=VARCHAR},
      </if>
      <if test="rfmPsstRule != null">
        #{rfmPsstRule,jdbcType=VARCHAR},
      </if>
      <if test="rfmPsstGroup != null">
        #{rfmPsstGroup,jdbcType=VARCHAR},
      </if>
      <if test="refItem != null">
        #{refItem,jdbcType=VARCHAR},
      </if>
      <if test="sourceId != null">
        #{sourceId,jdbcType=VARCHAR},
      </if>
      <if test="sourceKey != null">
        #{sourceKey,jdbcType=VARCHAR},
      </if>
      <if test="putBack != null">
        #{putBack,jdbcType=VARCHAR},
      </if>
      <if test="polId != null">
        #{polId,jdbcType=VARCHAR},
      </if>
      <if test="consOrder != null">
        #{consOrder,jdbcType=VARCHAR},
      </if>
      <if test="zzpaid != null">
        #{zzpaid,jdbcType=VARCHAR},
      </if>
      <if test="zzpamt != null">
        #{zzpamt,jdbcType=DECIMAL},
      </if>
      <if test="zzsign != null">
        #{zzsign,jdbcType=DECIMAL},
      </if>
      <if test="zzdate != null">
        #{zzdate,jdbcType=VARCHAR},
      </if>
      <if test="zztime != null">
        #{zztime,jdbcType=VARCHAR},
      </if>
      <if test="zzprice != null">
        #{zzprice,jdbcType=VARCHAR},
      </if>
      <if test="zzpeinh != null">
        #{zzpeinh,jdbcType=VARCHAR},
      </if>
      <if test="zzmwskz != null">
        #{zzmwskz,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.iscm.entity.SapEkpoExample" resultType="java.lang.Long">
    select count(*) from SAP_EKPO
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update SAP_EKPO
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.mandt != null">
        MANDT = #{record.mandt,jdbcType=VARCHAR},
      </if>
      <if test="record.ebeln != null">
        EBELN = #{record.ebeln,jdbcType=VARCHAR},
      </if>
      <if test="record.ebelp != null">
        EBELP = #{record.ebelp,jdbcType=VARCHAR},
      </if>
      <if test="record.loekz != null">
        LOEKZ = #{record.loekz,jdbcType=VARCHAR},
      </if>
      <if test="record.statu != null">
        STATU = #{record.statu,jdbcType=VARCHAR},
      </if>
      <if test="record.aedat != null">
        AEDAT = #{record.aedat,jdbcType=VARCHAR},
      </if>
      <if test="record.txz01 != null">
        TXZ01 = #{record.txz01,jdbcType=VARCHAR},
      </if>
      <if test="record.matnr != null">
        MATNR = #{record.matnr,jdbcType=VARCHAR},
      </if>
      <if test="record.ematn != null">
        EMATN = #{record.ematn,jdbcType=VARCHAR},
      </if>
      <if test="record.bukrs != null">
        BUKRS = #{record.bukrs,jdbcType=VARCHAR},
      </if>
      <if test="record.werks != null">
        WERKS = #{record.werks,jdbcType=VARCHAR},
      </if>
      <if test="record.lgort != null">
        LGORT = #{record.lgort,jdbcType=VARCHAR},
      </if>
      <if test="record.bednr != null">
        BEDNR = #{record.bednr,jdbcType=VARCHAR},
      </if>
      <if test="record.matkl != null">
        MATKL = #{record.matkl,jdbcType=VARCHAR},
      </if>
      <if test="record.infnr != null">
        INFNR = #{record.infnr,jdbcType=VARCHAR},
      </if>
      <if test="record.idnlf != null">
        IDNLF = #{record.idnlf,jdbcType=VARCHAR},
      </if>
      <if test="record.ktmng != null">
        KTMNG = #{record.ktmng,jdbcType=DECIMAL},
      </if>
      <if test="record.menge != null">
        MENGE = #{record.menge,jdbcType=DECIMAL},
      </if>
      <if test="record.meins != null">
        MEINS = #{record.meins,jdbcType=VARCHAR},
      </if>
      <if test="record.bprme != null">
        BPRME = #{record.bprme,jdbcType=VARCHAR},
      </if>
      <if test="record.bpumz != null">
        BPUMZ = #{record.bpumz,jdbcType=DECIMAL},
      </if>
      <if test="record.bpumn != null">
        BPUMN = #{record.bpumn,jdbcType=DECIMAL},
      </if>
      <if test="record.umrez != null">
        UMREZ = #{record.umrez,jdbcType=DECIMAL},
      </if>
      <if test="record.umren != null">
        UMREN = #{record.umren,jdbcType=DECIMAL},
      </if>
      <if test="record.netpr != null">
        NETPR = #{record.netpr,jdbcType=DECIMAL},
      </if>
      <if test="record.peinh != null">
        PEINH = #{record.peinh,jdbcType=DECIMAL},
      </if>
      <if test="record.netwr != null">
        NETWR = #{record.netwr,jdbcType=DECIMAL},
      </if>
      <if test="record.brtwr != null">
        BRTWR = #{record.brtwr,jdbcType=DECIMAL},
      </if>
      <if test="record.agdat != null">
        AGDAT = #{record.agdat,jdbcType=VARCHAR},
      </if>
      <if test="record.webaz != null">
        WEBAZ = #{record.webaz,jdbcType=DECIMAL},
      </if>
      <if test="record.mwskz != null">
        MWSKZ = #{record.mwskz,jdbcType=VARCHAR},
      </if>
      <if test="record.bonus != null">
        BONUS = #{record.bonus,jdbcType=VARCHAR},
      </if>
      <if test="record.insmk != null">
        INSMK = #{record.insmk,jdbcType=VARCHAR},
      </if>
      <if test="record.spinf != null">
        SPINF = #{record.spinf,jdbcType=VARCHAR},
      </if>
      <if test="record.prsdr != null">
        PRSDR = #{record.prsdr,jdbcType=VARCHAR},
      </if>
      <if test="record.schpr != null">
        SCHPR = #{record.schpr,jdbcType=VARCHAR},
      </if>
      <if test="record.mahnz != null">
        MAHNZ = #{record.mahnz,jdbcType=DECIMAL},
      </if>
      <if test="record.mahn1 != null">
        MAHN1 = #{record.mahn1,jdbcType=DECIMAL},
      </if>
      <if test="record.mahn2 != null">
        MAHN2 = #{record.mahn2,jdbcType=DECIMAL},
      </if>
      <if test="record.mahn3 != null">
        MAHN3 = #{record.mahn3,jdbcType=DECIMAL},
      </if>
      <if test="record.uebto != null">
        UEBTO = #{record.uebto,jdbcType=DECIMAL},
      </if>
      <if test="record.uebtk != null">
        UEBTK = #{record.uebtk,jdbcType=VARCHAR},
      </if>
      <if test="record.untto != null">
        UNTTO = #{record.untto,jdbcType=DECIMAL},
      </if>
      <if test="record.bwtar != null">
        BWTAR = #{record.bwtar,jdbcType=VARCHAR},
      </if>
      <if test="record.bwtty != null">
        BWTTY = #{record.bwtty,jdbcType=VARCHAR},
      </if>
      <if test="record.abskz != null">
        ABSKZ = #{record.abskz,jdbcType=VARCHAR},
      </if>
      <if test="record.agmem != null">
        AGMEM = #{record.agmem,jdbcType=VARCHAR},
      </if>
      <if test="record.elikz != null">
        ELIKZ = #{record.elikz,jdbcType=VARCHAR},
      </if>
      <if test="record.erekz != null">
        EREKZ = #{record.erekz,jdbcType=VARCHAR},
      </if>
      <if test="record.pstyp != null">
        PSTYP = #{record.pstyp,jdbcType=VARCHAR},
      </if>
      <if test="record.knttp != null">
        KNTTP = #{record.knttp,jdbcType=VARCHAR},
      </if>
      <if test="record.kzvbr != null">
        KZVBR = #{record.kzvbr,jdbcType=VARCHAR},
      </if>
      <if test="record.vrtkz != null">
        VRTKZ = #{record.vrtkz,jdbcType=VARCHAR},
      </if>
      <if test="record.twrkz != null">
        TWRKZ = #{record.twrkz,jdbcType=VARCHAR},
      </if>
      <if test="record.wepos != null">
        WEPOS = #{record.wepos,jdbcType=VARCHAR},
      </if>
      <if test="record.weunb != null">
        WEUNB = #{record.weunb,jdbcType=VARCHAR},
      </if>
      <if test="record.repos != null">
        REPOS = #{record.repos,jdbcType=VARCHAR},
      </if>
      <if test="record.webre != null">
        WEBRE = #{record.webre,jdbcType=VARCHAR},
      </if>
      <if test="record.kzabs != null">
        KZABS = #{record.kzabs,jdbcType=VARCHAR},
      </if>
      <if test="record.labnr != null">
        LABNR = #{record.labnr,jdbcType=VARCHAR},
      </if>
      <if test="record.konnr != null">
        KONNR = #{record.konnr,jdbcType=VARCHAR},
      </if>
      <if test="record.ktpnr != null">
        KTPNR = #{record.ktpnr,jdbcType=VARCHAR},
      </if>
      <if test="record.abdat != null">
        ABDAT = #{record.abdat,jdbcType=VARCHAR},
      </if>
      <if test="record.abftz != null">
        ABFTZ = #{record.abftz,jdbcType=DECIMAL},
      </if>
      <if test="record.etfz1 != null">
        ETFZ1 = #{record.etfz1,jdbcType=DECIMAL},
      </if>
      <if test="record.etfz2 != null">
        ETFZ2 = #{record.etfz2,jdbcType=DECIMAL},
      </if>
      <if test="record.kzstu != null">
        KZSTU = #{record.kzstu,jdbcType=VARCHAR},
      </if>
      <if test="record.notkz != null">
        NOTKZ = #{record.notkz,jdbcType=VARCHAR},
      </if>
      <if test="record.lmein != null">
        LMEIN = #{record.lmein,jdbcType=VARCHAR},
      </if>
      <if test="record.evers != null">
        EVERS = #{record.evers,jdbcType=VARCHAR},
      </if>
      <if test="record.zwert != null">
        ZWERT = #{record.zwert,jdbcType=DECIMAL},
      </if>
      <if test="record.navnw != null">
        NAVNW = #{record.navnw,jdbcType=DECIMAL},
      </if>
      <if test="record.abmng != null">
        ABMNG = #{record.abmng,jdbcType=DECIMAL},
      </if>
      <if test="record.prdat != null">
        PRDAT = #{record.prdat,jdbcType=VARCHAR},
      </if>
      <if test="record.bstyp != null">
        BSTYP = #{record.bstyp,jdbcType=VARCHAR},
      </if>
      <if test="record.effwr != null">
        EFFWR = #{record.effwr,jdbcType=DECIMAL},
      </if>
      <if test="record.xoblr != null">
        XOBLR = #{record.xoblr,jdbcType=VARCHAR},
      </if>
      <if test="record.kunnr != null">
        KUNNR = #{record.kunnr,jdbcType=VARCHAR},
      </if>
      <if test="record.adrnr != null">
        ADRNR = #{record.adrnr,jdbcType=VARCHAR},
      </if>
      <if test="record.ekkol != null">
        EKKOL = #{record.ekkol,jdbcType=VARCHAR},
      </if>
      <if test="record.sktof != null">
        SKTOF = #{record.sktof,jdbcType=VARCHAR},
      </if>
      <if test="record.stafo != null">
        STAFO = #{record.stafo,jdbcType=VARCHAR},
      </if>
      <if test="record.plifz != null">
        PLIFZ = #{record.plifz,jdbcType=DECIMAL},
      </if>
      <if test="record.ntgew != null">
        NTGEW = #{record.ntgew,jdbcType=DECIMAL},
      </if>
      <if test="record.gewei != null">
        GEWEI = #{record.gewei,jdbcType=VARCHAR},
      </if>
      <if test="record.txjcd != null">
        TXJCD = #{record.txjcd,jdbcType=VARCHAR},
      </if>
      <if test="record.etdrk != null">
        ETDRK = #{record.etdrk,jdbcType=VARCHAR},
      </if>
      <if test="record.sobkz != null">
        SOBKZ = #{record.sobkz,jdbcType=VARCHAR},
      </if>
      <if test="record.arsnr != null">
        ARSNR = #{record.arsnr,jdbcType=VARCHAR},
      </if>
      <if test="record.arsps != null">
        ARSPS = #{record.arsps,jdbcType=VARCHAR},
      </if>
      <if test="record.insnc != null">
        INSNC = #{record.insnc,jdbcType=VARCHAR},
      </if>
      <if test="record.ssqss != null">
        SSQSS = #{record.ssqss,jdbcType=VARCHAR},
      </if>
      <if test="record.zgtyp != null">
        ZGTYP = #{record.zgtyp,jdbcType=VARCHAR},
      </if>
      <if test="record.ean11 != null">
        EAN11 = #{record.ean11,jdbcType=VARCHAR},
      </if>
      <if test="record.bstae != null">
        BSTAE = #{record.bstae,jdbcType=VARCHAR},
      </if>
      <if test="record.revlv != null">
        REVLV = #{record.revlv,jdbcType=VARCHAR},
      </if>
      <if test="record.geber != null">
        GEBER = #{record.geber,jdbcType=VARCHAR},
      </if>
      <if test="record.fistl != null">
        FISTL = #{record.fistl,jdbcType=VARCHAR},
      </if>
      <if test="record.fipos != null">
        FIPOS = #{record.fipos,jdbcType=VARCHAR},
      </if>
      <if test="record.koGsber != null">
        KO_GSBER = #{record.koGsber,jdbcType=VARCHAR},
      </if>
      <if test="record.koPargb != null">
        KO_PARGB = #{record.koPargb,jdbcType=VARCHAR},
      </if>
      <if test="record.koPrctr != null">
        KO_PRCTR = #{record.koPrctr,jdbcType=VARCHAR},
      </if>
      <if test="record.koPprctr != null">
        KO_PPRCTR = #{record.koPprctr,jdbcType=VARCHAR},
      </if>
      <if test="record.meprf != null">
        MEPRF = #{record.meprf,jdbcType=VARCHAR},
      </if>
      <if test="record.brgew != null">
        BRGEW = #{record.brgew,jdbcType=DECIMAL},
      </if>
      <if test="record.volum != null">
        VOLUM = #{record.volum,jdbcType=DECIMAL},
      </if>
      <if test="record.voleh != null">
        VOLEH = #{record.voleh,jdbcType=VARCHAR},
      </if>
      <if test="record.inco1 != null">
        INCO1 = #{record.inco1,jdbcType=VARCHAR},
      </if>
      <if test="record.inco2 != null">
        INCO2 = #{record.inco2,jdbcType=VARCHAR},
      </if>
      <if test="record.vorab != null">
        VORAB = #{record.vorab,jdbcType=VARCHAR},
      </if>
      <if test="record.kolif != null">
        KOLIF = #{record.kolif,jdbcType=VARCHAR},
      </if>
      <if test="record.ltsnr != null">
        LTSNR = #{record.ltsnr,jdbcType=VARCHAR},
      </if>
      <if test="record.packno != null">
        PACKNO = #{record.packno,jdbcType=VARCHAR},
      </if>
      <if test="record.fplnr != null">
        FPLNR = #{record.fplnr,jdbcType=VARCHAR},
      </if>
      <if test="record.gnetwr != null">
        GNETWR = #{record.gnetwr,jdbcType=DECIMAL},
      </if>
      <if test="record.stapo != null">
        STAPO = #{record.stapo,jdbcType=VARCHAR},
      </if>
      <if test="record.uebpo != null">
        UEBPO = #{record.uebpo,jdbcType=VARCHAR},
      </if>
      <if test="record.lewed != null">
        LEWED = #{record.lewed,jdbcType=VARCHAR},
      </if>
      <if test="record.emlif != null">
        EMLIF = #{record.emlif,jdbcType=VARCHAR},
      </if>
      <if test="record.lblkz != null">
        LBLKZ = #{record.lblkz,jdbcType=VARCHAR},
      </if>
      <if test="record.satnr != null">
        SATNR = #{record.satnr,jdbcType=VARCHAR},
      </if>
      <if test="record.attyp != null">
        ATTYP = #{record.attyp,jdbcType=VARCHAR},
      </if>
      <if test="record.vsart != null">
        VSART = #{record.vsart,jdbcType=VARCHAR},
      </if>
      <if test="record.handoverloc != null">
        HANDOVERLOC = #{record.handoverloc,jdbcType=VARCHAR},
      </if>
      <if test="record.kanba != null">
        KANBA = #{record.kanba,jdbcType=VARCHAR},
      </if>
      <if test="record.adrn2 != null">
        ADRN2 = #{record.adrn2,jdbcType=VARCHAR},
      </if>
      <if test="record.cuobj != null">
        CUOBJ = #{record.cuobj,jdbcType=VARCHAR},
      </if>
      <if test="record.xersy != null">
        XERSY = #{record.xersy,jdbcType=VARCHAR},
      </if>
      <if test="record.eildt != null">
        EILDT = #{record.eildt,jdbcType=VARCHAR},
      </if>
      <if test="record.drdat != null">
        DRDAT = #{record.drdat,jdbcType=VARCHAR},
      </if>
      <if test="record.druhr != null">
        DRUHR = #{record.druhr,jdbcType=VARCHAR},
      </if>
      <if test="record.drunr != null">
        DRUNR = #{record.drunr,jdbcType=VARCHAR},
      </if>
      <if test="record.aktnr != null">
        AKTNR = #{record.aktnr,jdbcType=VARCHAR},
      </if>
      <if test="record.abeln != null">
        ABELN = #{record.abeln,jdbcType=VARCHAR},
      </if>
      <if test="record.abelp != null">
        ABELP = #{record.abelp,jdbcType=VARCHAR},
      </if>
      <if test="record.anzpu != null">
        ANZPU = #{record.anzpu,jdbcType=DECIMAL},
      </if>
      <if test="record.punei != null">
        PUNEI = #{record.punei,jdbcType=VARCHAR},
      </if>
      <if test="record.saiso != null">
        SAISO = #{record.saiso,jdbcType=VARCHAR},
      </if>
      <if test="record.saisj != null">
        SAISJ = #{record.saisj,jdbcType=VARCHAR},
      </if>
      <if test="record.ebon2 != null">
        EBON2 = #{record.ebon2,jdbcType=VARCHAR},
      </if>
      <if test="record.ebon3 != null">
        EBON3 = #{record.ebon3,jdbcType=VARCHAR},
      </if>
      <if test="record.ebonf != null">
        EBONF = #{record.ebonf,jdbcType=VARCHAR},
      </if>
      <if test="record.mlmaa != null">
        MLMAA = #{record.mlmaa,jdbcType=VARCHAR},
      </if>
      <if test="record.mhdrz != null">
        MHDRZ = #{record.mhdrz,jdbcType=DECIMAL},
      </if>
      <if test="record.anfnr != null">
        ANFNR = #{record.anfnr,jdbcType=VARCHAR},
      </if>
      <if test="record.anfps != null">
        ANFPS = #{record.anfps,jdbcType=VARCHAR},
      </if>
      <if test="record.kzkfg != null">
        KZKFG = #{record.kzkfg,jdbcType=VARCHAR},
      </if>
      <if test="record.usequ != null">
        USEQU = #{record.usequ,jdbcType=VARCHAR},
      </if>
      <if test="record.umsok != null">
        UMSOK = #{record.umsok,jdbcType=VARCHAR},
      </if>
      <if test="record.banfn != null">
        BANFN = #{record.banfn,jdbcType=VARCHAR},
      </if>
      <if test="record.bnfpo != null">
        BNFPO = #{record.bnfpo,jdbcType=VARCHAR},
      </if>
      <if test="record.mtart != null">
        MTART = #{record.mtart,jdbcType=VARCHAR},
      </if>
      <if test="record.uptyp != null">
        UPTYP = #{record.uptyp,jdbcType=VARCHAR},
      </if>
      <if test="record.upvor != null">
        UPVOR = #{record.upvor,jdbcType=VARCHAR},
      </if>
      <if test="record.kzwi1 != null">
        KZWI1 = #{record.kzwi1,jdbcType=DECIMAL},
      </if>
      <if test="record.kzwi2 != null">
        KZWI2 = #{record.kzwi2,jdbcType=DECIMAL},
      </if>
      <if test="record.kzwi3 != null">
        KZWI3 = #{record.kzwi3,jdbcType=DECIMAL},
      </if>
      <if test="record.kzwi4 != null">
        KZWI4 = #{record.kzwi4,jdbcType=DECIMAL},
      </if>
      <if test="record.kzwi5 != null">
        KZWI5 = #{record.kzwi5,jdbcType=DECIMAL},
      </if>
      <if test="record.kzwi6 != null">
        KZWI6 = #{record.kzwi6,jdbcType=DECIMAL},
      </if>
      <if test="record.sikgr != null">
        SIKGR = #{record.sikgr,jdbcType=VARCHAR},
      </if>
      <if test="record.mfzhi != null">
        MFZHI = #{record.mfzhi,jdbcType=DECIMAL},
      </if>
      <if test="record.ffzhi != null">
        FFZHI = #{record.ffzhi,jdbcType=DECIMAL},
      </if>
      <if test="record.retpo != null">
        RETPO = #{record.retpo,jdbcType=VARCHAR},
      </if>
      <if test="record.aurel != null">
        AUREL = #{record.aurel,jdbcType=VARCHAR},
      </if>
      <if test="record.bsgru != null">
        BSGRU = #{record.bsgru,jdbcType=VARCHAR},
      </if>
      <if test="record.lfret != null">
        LFRET = #{record.lfret,jdbcType=VARCHAR},
      </if>
      <if test="record.mfrgr != null">
        MFRGR = #{record.mfrgr,jdbcType=VARCHAR},
      </if>
      <if test="record.nrfhg != null">
        NRFHG = #{record.nrfhg,jdbcType=VARCHAR},
      </if>
      <if test="record.j1bnbm != null">
        J_1BNBM = #{record.j1bnbm,jdbcType=VARCHAR},
      </if>
      <if test="record.j1bmatuse != null">
        J_1BMATUSE = #{record.j1bmatuse,jdbcType=VARCHAR},
      </if>
      <if test="record.j1bmatorg != null">
        J_1BMATORG = #{record.j1bmatorg,jdbcType=VARCHAR},
      </if>
      <if test="record.j1bownpro != null">
        J_1BOWNPRO = #{record.j1bownpro,jdbcType=VARCHAR},
      </if>
      <if test="record.j1bindust != null">
        J_1BINDUST = #{record.j1bindust,jdbcType=VARCHAR},
      </if>
      <if test="record.abueb != null">
        ABUEB = #{record.abueb,jdbcType=VARCHAR},
      </if>
      <if test="record.nlabd != null">
        NLABD = #{record.nlabd,jdbcType=VARCHAR},
      </if>
      <if test="record.nfabd != null">
        NFABD = #{record.nfabd,jdbcType=VARCHAR},
      </if>
      <if test="record.kzbws != null">
        KZBWS = #{record.kzbws,jdbcType=VARCHAR},
      </if>
      <if test="record.bonba != null">
        BONBA = #{record.bonba,jdbcType=DECIMAL},
      </if>
      <if test="record.fabkz != null">
        FABKZ = #{record.fabkz,jdbcType=VARCHAR},
      </if>
      <if test="record.j1aindxp != null">
        J_1AINDXP = #{record.j1aindxp,jdbcType=VARCHAR},
      </if>
      <if test="record.j1aidatep != null">
        J_1AIDATEP = #{record.j1aidatep,jdbcType=VARCHAR},
      </if>
      <if test="record.mprof != null">
        MPROF = #{record.mprof,jdbcType=VARCHAR},
      </if>
      <if test="record.eglkz != null">
        EGLKZ = #{record.eglkz,jdbcType=VARCHAR},
      </if>
      <if test="record.kztlf != null">
        KZTLF = #{record.kztlf,jdbcType=VARCHAR},
      </if>
      <if test="record.kzfme != null">
        KZFME = #{record.kzfme,jdbcType=VARCHAR},
      </if>
      <if test="record.rdprf != null">
        RDPRF = #{record.rdprf,jdbcType=VARCHAR},
      </if>
      <if test="record.techs != null">
        TECHS = #{record.techs,jdbcType=VARCHAR},
      </if>
      <if test="record.chgSrv != null">
        CHG_SRV = #{record.chgSrv,jdbcType=VARCHAR},
      </if>
      <if test="record.chgFplnr != null">
        CHG_FPLNR = #{record.chgFplnr,jdbcType=VARCHAR},
      </if>
      <if test="record.mfrpn != null">
        MFRPN = #{record.mfrpn,jdbcType=VARCHAR},
      </if>
      <if test="record.mfrnr != null">
        MFRNR = #{record.mfrnr,jdbcType=VARCHAR},
      </if>
      <if test="record.emnfr != null">
        EMNFR = #{record.emnfr,jdbcType=VARCHAR},
      </if>
      <if test="record.novet != null">
        NOVET = #{record.novet,jdbcType=VARCHAR},
      </if>
      <if test="record.afnam != null">
        AFNAM = #{record.afnam,jdbcType=VARCHAR},
      </if>
      <if test="record.tzonrc != null">
        TZONRC = #{record.tzonrc,jdbcType=VARCHAR},
      </if>
      <if test="record.iprkz != null">
        IPRKZ = #{record.iprkz,jdbcType=VARCHAR},
      </if>
      <if test="record.lebre != null">
        LEBRE = #{record.lebre,jdbcType=VARCHAR},
      </if>
      <if test="record.berid != null">
        BERID = #{record.berid,jdbcType=VARCHAR},
      </if>
      <if test="record.xconditions != null">
        XCONDITIONS = #{record.xconditions,jdbcType=VARCHAR},
      </if>
      <if test="record.apoms != null">
        APOMS = #{record.apoms,jdbcType=VARCHAR},
      </if>
      <if test="record.ccomp != null">
        CCOMP = #{record.ccomp,jdbcType=VARCHAR},
      </if>
      <if test="record.grantNbr != null">
        GRANT_NBR = #{record.grantNbr,jdbcType=VARCHAR},
      </if>
      <if test="record.fkber != null">
        FKBER = #{record.fkber,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        `STATUS` = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.reslo != null">
        RESLO = #{record.reslo,jdbcType=VARCHAR},
      </if>
      <if test="record.kblnr != null">
        KBLNR = #{record.kblnr,jdbcType=VARCHAR},
      </if>
      <if test="record.kblpos != null">
        KBLPOS = #{record.kblpos,jdbcType=VARCHAR},
      </if>
      <if test="record.weora != null">
        WEORA = #{record.weora,jdbcType=VARCHAR},
      </if>
      <if test="record.srvBasCom != null">
        SRV_BAS_COM = #{record.srvBasCom,jdbcType=VARCHAR},
      </if>
      <if test="record.prioUrg != null">
        PRIO_URG = #{record.prioUrg,jdbcType=VARCHAR},
      </if>
      <if test="record.prioReq != null">
        PRIO_REQ = #{record.prioReq,jdbcType=VARCHAR},
      </if>
      <if test="record.empst != null">
        EMPST = #{record.empst,jdbcType=VARCHAR},
      </if>
      <if test="record.diffInvoice != null">
        DIFF_INVOICE = #{record.diffInvoice,jdbcType=VARCHAR},
      </if>
      <if test="record.trmriskRelevant != null">
        TRMRISK_RELEVANT = #{record.trmriskRelevant,jdbcType=VARCHAR},
      </if>
      <if test="record.speAbgru != null">
        SPE_ABGRU = #{record.speAbgru,jdbcType=VARCHAR},
      </if>
      <if test="record.speCrmSo != null">
        SPE_CRM_SO = #{record.speCrmSo,jdbcType=VARCHAR},
      </if>
      <if test="record.speCrmSoItem != null">
        SPE_CRM_SO_ITEM = #{record.speCrmSoItem,jdbcType=VARCHAR},
      </if>
      <if test="record.speCrmRefSo != null">
        SPE_CRM_REF_SO = #{record.speCrmRefSo,jdbcType=VARCHAR},
      </if>
      <if test="record.speCrmRefItem != null">
        SPE_CRM_REF_ITEM = #{record.speCrmRefItem,jdbcType=VARCHAR},
      </if>
      <if test="record.speCrmFkrel != null">
        SPE_CRM_FKREL = #{record.speCrmFkrel,jdbcType=VARCHAR},
      </if>
      <if test="record.speChngSys != null">
        SPE_CHNG_SYS = #{record.speChngSys,jdbcType=VARCHAR},
      </if>
      <if test="record.speInsmkSrc != null">
        SPE_INSMK_SRC = #{record.speInsmkSrc,jdbcType=VARCHAR},
      </if>
      <if test="record.speCqCtrltype != null">
        SPE_CQ_CTRLTYPE = #{record.speCqCtrltype,jdbcType=VARCHAR},
      </if>
      <if test="record.speCqNocq != null">
        SPE_CQ_NOCQ = #{record.speCqNocq,jdbcType=VARCHAR},
      </if>
      <if test="record.reasonCode != null">
        REASON_CODE = #{record.reasonCode,jdbcType=VARCHAR},
      </if>
      <if test="record.cquSar != null">
        CQU_SAR = #{record.cquSar,jdbcType=DECIMAL},
      </if>
      <if test="record.anzsn != null">
        ANZSN = #{record.anzsn,jdbcType=INTEGER},
      </if>
      <if test="record.speEwmDtc != null">
        SPE_EWM_DTC = #{record.speEwmDtc,jdbcType=VARCHAR},
      </if>
      <if test="record.exlin != null">
        EXLIN = #{record.exlin,jdbcType=VARCHAR},
      </if>
      <if test="record.exsnr != null">
        EXSNR = #{record.exsnr,jdbcType=VARCHAR},
      </if>
      <if test="record.ehtyp != null">
        EHTYP = #{record.ehtyp,jdbcType=VARCHAR},
      </if>
      <if test="record.retpc != null">
        RETPC = #{record.retpc,jdbcType=DECIMAL},
      </if>
      <if test="record.dptyp != null">
        DPTYP = #{record.dptyp,jdbcType=VARCHAR},
      </if>
      <if test="record.dppct != null">
        DPPCT = #{record.dppct,jdbcType=DECIMAL},
      </if>
      <if test="record.dpamt != null">
        DPAMT = #{record.dpamt,jdbcType=DECIMAL},
      </if>
      <if test="record.dpdat != null">
        DPDAT = #{record.dpdat,jdbcType=VARCHAR},
      </if>
      <if test="record.flsRsto != null">
        FLS_RSTO = #{record.flsRsto,jdbcType=VARCHAR},
      </if>
      <if test="record.extRfxNumber != null">
        EXT_RFX_NUMBER = #{record.extRfxNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.extRfxItem != null">
        EXT_RFX_ITEM = #{record.extRfxItem,jdbcType=VARCHAR},
      </if>
      <if test="record.extRfxSystem != null">
        EXT_RFX_SYSTEM = #{record.extRfxSystem,jdbcType=VARCHAR},
      </if>
      <if test="record.srmContractId != null">
        SRM_CONTRACT_ID = #{record.srmContractId,jdbcType=VARCHAR},
      </if>
      <if test="record.srmContractItm != null">
        SRM_CONTRACT_ITM = #{record.srmContractItm,jdbcType=VARCHAR},
      </if>
      <if test="record.blkReasonId != null">
        BLK_REASON_ID = #{record.blkReasonId,jdbcType=VARCHAR},
      </if>
      <if test="record.blkReasonTxt != null">
        BLK_REASON_TXT = #{record.blkReasonTxt,jdbcType=VARCHAR},
      </if>
      <if test="record.itcons != null">
        ITCONS = #{record.itcons,jdbcType=VARCHAR},
      </if>
      <if test="record.fixmg != null">
        FIXMG = #{record.fixmg,jdbcType=VARCHAR},
      </if>
      <if test="record.wabwe != null">
        WABWE = #{record.wabwe,jdbcType=VARCHAR},
      </if>
      <if test="record.cmplDlvItm != null">
        CMPL_DLV_ITM = #{record.cmplDlvItm,jdbcType=VARCHAR},
      </if>
      <if test="record.inco2L != null">
        INCO2_L = #{record.inco2L,jdbcType=VARCHAR},
      </if>
      <if test="record.inco3L != null">
        INCO3_L = #{record.inco3L,jdbcType=VARCHAR},
      </if>
      <if test="record.stawn != null">
        STAWN = #{record.stawn,jdbcType=VARCHAR},
      </if>
      <if test="record.isvco != null">
        ISVCO = #{record.isvco,jdbcType=VARCHAR},
      </if>
      <if test="record.grwrt != null">
        GRWRT = #{record.grwrt,jdbcType=DECIMAL},
      </if>
      <if test="record.serviceperformer != null">
        SERVICEPERFORMER = #{record.serviceperformer,jdbcType=VARCHAR},
      </if>
      <if test="record.producttype != null">
        PRODUCTTYPE = #{record.producttype,jdbcType=VARCHAR},
      </if>
      <if test="record.requestforquotation != null">
        REQUESTFORQUOTATION = #{record.requestforquotation,jdbcType=VARCHAR},
      </if>
      <if test="record.requestforquotationitem != null">
        REQUESTFORQUOTATIONITEM = #{record.requestforquotationitem,jdbcType=VARCHAR},
      </if>
      <if test="record.externalreferenceid != null">
        EXTERNALREFERENCEID = #{record.externalreferenceid,jdbcType=VARCHAR},
      </if>
      <if test="record.tcAutDet != null">
        TC_AUT_DET = #{record.tcAutDet,jdbcType=VARCHAR},
      </if>
      <if test="record.manualTcReason != null">
        MANUAL_TC_REASON = #{record.manualTcReason,jdbcType=VARCHAR},
      </if>
      <if test="record.fiscalIncentive != null">
        FISCAL_INCENTIVE = #{record.fiscalIncentive,jdbcType=VARCHAR},
      </if>
      <if test="record.taxSubjectSt != null">
        TAX_SUBJECT_ST = #{record.taxSubjectSt,jdbcType=VARCHAR},
      </if>
      <if test="record.fiscalIncentiveId != null">
        FISCAL_INCENTIVE_ID = #{record.fiscalIncentiveId,jdbcType=VARCHAR},
      </if>
      <if test="record.sfTxjcd != null">
        SF_TXJCD = #{record.sfTxjcd,jdbcType=VARCHAR},
      </if>
      <if test="record.dummyEkpoInclEewPs != null">
        DUMMY_EKPO_INCL_EEW_PS = #{record.dummyEkpoInclEewPs,jdbcType=VARCHAR},
      </if>
      <if test="record.expectedValue != null">
        EXPECTED_VALUE = #{record.expectedValue,jdbcType=DECIMAL},
      </if>
      <if test="record.limitAmount != null">
        LIMIT_AMOUNT = #{record.limitAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.enhDate1 != null">
        ENH_DATE1 = #{record.enhDate1,jdbcType=VARCHAR},
      </if>
      <if test="record.enhDate2 != null">
        ENH_DATE2 = #{record.enhDate2,jdbcType=VARCHAR},
      </if>
      <if test="record.enhPercent != null">
        ENH_PERCENT = #{record.enhPercent,jdbcType=DECIMAL},
      </if>
      <if test="record.enhNumc1 != null">
        ENH_NUMC1 = #{record.enhNumc1,jdbcType=VARCHAR},
      </if>
      <if test="record.dataaging != null">
        DATAAGING = #{record.dataaging,jdbcType=VARCHAR},
      </if>
      <if test="record.bev1NegenItem != null">
        BEV1_NEGEN_ITEM = #{record.bev1NegenItem,jdbcType=VARCHAR},
      </if>
      <if test="record.bev1Nedepfree != null">
        BEV1_NEDEPFREE = #{record.bev1Nedepfree,jdbcType=VARCHAR},
      </if>
      <if test="record.bev1Nestruccat != null">
        BEV1_NESTRUCCAT = #{record.bev1Nestruccat,jdbcType=VARCHAR},
      </if>
      <if test="record.advcode != null">
        ADVCODE = #{record.advcode,jdbcType=VARCHAR},
      </if>
      <if test="record.budgetPd != null">
        BUDGET_PD = #{record.budgetPd,jdbcType=VARCHAR},
      </if>
      <if test="record.excpe != null">
        EXCPE = #{record.excpe,jdbcType=VARCHAR},
      </if>
      <if test="record.fmfgusKey != null">
        FMFGUS_KEY = #{record.fmfgusKey,jdbcType=VARCHAR},
      </if>
      <if test="record.iuidRelevant != null">
        IUID_RELEVANT = #{record.iuidRelevant,jdbcType=VARCHAR},
      </if>
      <if test="record.mrpind != null">
        MRPIND = #{record.mrpind,jdbcType=VARCHAR},
      </if>
      <if test="record.sgtScat != null">
        SGT_SCAT = #{record.sgtScat,jdbcType=VARCHAR},
      </if>
      <if test="record.sgtRcat != null">
        SGT_RCAT = #{record.sgtRcat,jdbcType=VARCHAR},
      </if>
      <if test="record.tmsRefUuid != null">
        TMS_REF_UUID = #{record.tmsRefUuid,jdbcType=VARCHAR},
      </if>
      <if test="record.wrfCharstc1 != null">
        WRF_CHARSTC1 = #{record.wrfCharstc1,jdbcType=VARCHAR},
      </if>
      <if test="record.wrfCharstc2 != null">
        WRF_CHARSTC2 = #{record.wrfCharstc2,jdbcType=VARCHAR},
      </if>
      <if test="record.wrfCharstc3 != null">
        WRF_CHARSTC3 = #{record.wrfCharstc3,jdbcType=VARCHAR},
      </if>
      <if test="record.zzzmdsq != null">
        ZZZMDSQ = #{record.zzzmdsq,jdbcType=VARCHAR},
      </if>
      <if test="record.zzzmdhh != null">
        ZZZMDHH = #{record.zzzmdhh,jdbcType=VARCHAR},
      </if>
      <if test="record.zzqxdjh != null">
        ZZQXDJH = #{record.zzqxdjh,jdbcType=VARCHAR},
      </if>
      <if test="record.zzqxdhh != null">
        ZZQXDHH = #{record.zzqxdhh,jdbcType=VARCHAR},
      </if>
      <if test="record.zzcxbjs != null">
        ZZCXBJS = #{record.zzcxbjs,jdbcType=VARCHAR},
      </if>
      <if test="record.zzqtys != null">
        ZZQTYS = #{record.zzqtys,jdbcType=VARCHAR},
      </if>
      <if test="record.zzspcd != null">
        ZZSPCD = #{record.zzspcd,jdbcType=VARCHAR},
      </if>
      <if test="record.zzsccj != null">
        ZZSCCJ = #{record.zzsccj,jdbcType=VARCHAR},
      </if>
      <if test="record.zzzscph != null">
        ZZZSCPH = #{record.zzzscph,jdbcType=VARCHAR},
      </if>
      <if test="record.zzzgjj != null">
        ZZZGJJ = #{record.zzzgjj,jdbcType=VARCHAR},
      </if>
      <if test="record.refsite != null">
        REFSITE = #{record.refsite,jdbcType=VARCHAR},
      </if>
      <if test="record.zapcgk != null">
        ZAPCGK = #{record.zapcgk,jdbcType=VARCHAR},
      </if>
      <if test="record.apcgkExtend != null">
        APCGK_EXTEND = #{record.apcgkExtend,jdbcType=VARCHAR},
      </if>
      <if test="record.zbasDate != null">
        ZBAS_DATE = #{record.zbasDate,jdbcType=VARCHAR},
      </if>
      <if test="record.zadattyp != null">
        ZADATTYP = #{record.zadattyp,jdbcType=VARCHAR},
      </if>
      <if test="record.zstartDat != null">
        ZSTART_DAT = #{record.zstartDat,jdbcType=VARCHAR},
      </if>
      <if test="record.zDev != null">
        Z_DEV = #{record.zDev,jdbcType=DECIMAL},
      </if>
      <if test="record.zindanx != null">
        ZINDANX = #{record.zindanx,jdbcType=VARCHAR},
      </if>
      <if test="record.zlimitDat != null">
        ZLIMIT_DAT = #{record.zlimitDat,jdbcType=VARCHAR},
      </if>
      <if test="record.numerator != null">
        NUMERATOR = #{record.numerator,jdbcType=VARCHAR},
      </if>
      <if test="record.hashcalBdat != null">
        HASHCAL_BDAT = #{record.hashcalBdat,jdbcType=VARCHAR},
      </if>
      <if test="record.hashcal != null">
        HASHCAL = #{record.hashcal,jdbcType=VARCHAR},
      </if>
      <if test="record.negative != null">
        NEGATIVE = #{record.negative,jdbcType=VARCHAR},
      </if>
      <if test="record.hashcalExists != null">
        HASHCAL_EXISTS = #{record.hashcalExists,jdbcType=VARCHAR},
      </if>
      <if test="record.knownIndex != null">
        KNOWN_INDEX = #{record.knownIndex,jdbcType=VARCHAR},
      </if>
      <if test="record.sapmpGpose != null">
        SAPMP_GPOSE = #{record.sapmpGpose,jdbcType=VARCHAR},
      </if>
      <if test="record.angpn != null">
        ANGPN = #{record.angpn,jdbcType=VARCHAR},
      </if>
      <if test="record.admoi != null">
        ADMOI = #{record.admoi,jdbcType=VARCHAR},
      </if>
      <if test="record.adpri != null">
        ADPRI = #{record.adpri,jdbcType=VARCHAR},
      </if>
      <if test="record.lprio != null">
        LPRIO = #{record.lprio,jdbcType=VARCHAR},
      </if>
      <if test="record.adacn != null">
        ADACN = #{record.adacn,jdbcType=VARCHAR},
      </if>
      <if test="record.afpnr != null">
        AFPNR = #{record.afpnr,jdbcType=VARCHAR},
      </if>
      <if test="record.bsark != null">
        BSARK = #{record.bsark,jdbcType=VARCHAR},
      </if>
      <if test="record.audat != null">
        AUDAT = #{record.audat,jdbcType=VARCHAR},
      </if>
      <if test="record.angnr != null">
        ANGNR = #{record.angnr,jdbcType=VARCHAR},
      </if>
      <if test="record.pnstat != null">
        PNSTAT = #{record.pnstat,jdbcType=VARCHAR},
      </if>
      <if test="record.addns != null">
        ADDNS = #{record.addns,jdbcType=VARCHAR},
      </if>
      <if test="record.serru != null">
        SERRU = #{record.serru,jdbcType=VARCHAR},
      </if>
      <if test="record.sernp != null">
        SERNP = #{record.sernp,jdbcType=VARCHAR},
      </if>
      <if test="record.disubSobkz != null">
        DISUB_SOBKZ = #{record.disubSobkz,jdbcType=VARCHAR},
      </if>
      <if test="record.disubPspnr != null">
        DISUB_PSPNR = #{record.disubPspnr,jdbcType=VARCHAR},
      </if>
      <if test="record.disubKunnr != null">
        DISUB_KUNNR = #{record.disubKunnr,jdbcType=VARCHAR},
      </if>
      <if test="record.disubVbeln != null">
        DISUB_VBELN = #{record.disubVbeln,jdbcType=VARCHAR},
      </if>
      <if test="record.disubPosnr != null">
        DISUB_POSNR = #{record.disubPosnr,jdbcType=VARCHAR},
      </if>
      <if test="record.disubOwner != null">
        DISUB_OWNER = #{record.disubOwner,jdbcType=VARCHAR},
      </if>
      <if test="record.fshSeasonYear != null">
        FSH_SEASON_YEAR = #{record.fshSeasonYear,jdbcType=VARCHAR},
      </if>
      <if test="record.fshSeason != null">
        FSH_SEASON = #{record.fshSeason,jdbcType=VARCHAR},
      </if>
      <if test="record.fshCollection != null">
        FSH_COLLECTION = #{record.fshCollection,jdbcType=VARCHAR},
      </if>
      <if test="record.fshTheme != null">
        FSH_THEME = #{record.fshTheme,jdbcType=VARCHAR},
      </if>
      <if test="record.fshAtpDate != null">
        FSH_ATP_DATE = #{record.fshAtpDate,jdbcType=VARCHAR},
      </if>
      <if test="record.fshVasRel != null">
        FSH_VAS_REL = #{record.fshVasRel,jdbcType=VARCHAR},
      </if>
      <if test="record.fshVasPrntId != null">
        FSH_VAS_PRNT_ID = #{record.fshVasPrntId,jdbcType=VARCHAR},
      </if>
      <if test="record.fshTransaction != null">
        FSH_TRANSACTION = #{record.fshTransaction,jdbcType=VARCHAR},
      </if>
      <if test="record.fshItemGroup != null">
        FSH_ITEM_GROUP = #{record.fshItemGroup,jdbcType=VARCHAR},
      </if>
      <if test="record.fshItem != null">
        FSH_ITEM = #{record.fshItem,jdbcType=VARCHAR},
      </if>
      <if test="record.fshSs != null">
        FSH_SS = #{record.fshSs,jdbcType=VARCHAR},
      </if>
      <if test="record.fshGridCondRec != null">
        FSH_GRID_COND_REC = #{record.fshGridCondRec,jdbcType=VARCHAR},
      </if>
      <if test="record.fshPsmPfmSplit != null">
        FSH_PSM_PFM_SPLIT = #{record.fshPsmPfmSplit,jdbcType=VARCHAR},
      </if>
      <if test="record.cnfmQty != null">
        CNFM_QTY = #{record.cnfmQty,jdbcType=DECIMAL},
      </if>
      <if test="record.stpac != null">
        STPAC = #{record.stpac,jdbcType=VARCHAR},
      </if>
      <if test="record.lgbzo != null">
        LGBZO = #{record.lgbzo,jdbcType=VARCHAR},
      </if>
      <if test="record.lgbzoB != null">
        LGBZO_B = #{record.lgbzoB,jdbcType=VARCHAR},
      </if>
      <if test="record.addrnum != null">
        ADDRNUM = #{record.addrnum,jdbcType=VARCHAR},
      </if>
      <if test="record.consnum != null">
        CONSNUM = #{record.consnum,jdbcType=VARCHAR},
      </if>
      <if test="record.borgrMiss != null">
        BORGR_MISS = #{record.borgrMiss,jdbcType=VARCHAR},
      </if>
      <if test="record.depId != null">
        DEP_ID = #{record.depId,jdbcType=VARCHAR},
      </if>
      <if test="record.belnr != null">
        BELNR = #{record.belnr,jdbcType=VARCHAR},
      </if>
      <if test="record.kblposCab != null">
        KBLPOS_CAB = #{record.kblposCab,jdbcType=VARCHAR},
      </if>
      <if test="record.kblnrComp != null">
        KBLNR_COMP = #{record.kblnrComp,jdbcType=VARCHAR},
      </if>
      <if test="record.kblposComp != null">
        KBLPOS_COMP = #{record.kblposComp,jdbcType=VARCHAR},
      </if>
      <if test="record.wbsElement != null">
        WBS_ELEMENT = #{record.wbsElement,jdbcType=VARCHAR},
      </if>
      <if test="record.rfmPsstRule != null">
        RFM_PSST_RULE = #{record.rfmPsstRule,jdbcType=VARCHAR},
      </if>
      <if test="record.rfmPsstGroup != null">
        RFM_PSST_GROUP = #{record.rfmPsstGroup,jdbcType=VARCHAR},
      </if>
      <if test="record.refItem != null">
        REF_ITEM = #{record.refItem,jdbcType=VARCHAR},
      </if>
      <if test="record.sourceId != null">
        SOURCE_ID = #{record.sourceId,jdbcType=VARCHAR},
      </if>
      <if test="record.sourceKey != null">
        SOURCE_KEY = #{record.sourceKey,jdbcType=VARCHAR},
      </if>
      <if test="record.putBack != null">
        PUT_BACK = #{record.putBack,jdbcType=VARCHAR},
      </if>
      <if test="record.polId != null">
        POL_ID = #{record.polId,jdbcType=VARCHAR},
      </if>
      <if test="record.consOrder != null">
        CONS_ORDER = #{record.consOrder,jdbcType=VARCHAR},
      </if>
      <if test="record.zzpaid != null">
        ZZPAID = #{record.zzpaid,jdbcType=VARCHAR},
      </if>
      <if test="record.zzpamt != null">
        ZZPAMT = #{record.zzpamt,jdbcType=DECIMAL},
      </if>
      <if test="record.zzsign != null">
        ZZSIGN = #{record.zzsign,jdbcType=DECIMAL},
      </if>
      <if test="record.zzdate != null">
        ZZDATE = #{record.zzdate,jdbcType=VARCHAR},
      </if>
      <if test="record.zztime != null">
        ZZTIME = #{record.zztime,jdbcType=VARCHAR},
      </if>
      <if test="record.zzprice != null">
        ZZPRICE = #{record.zzprice,jdbcType=VARCHAR},
      </if>
      <if test="record.zzpeinh != null">
        ZZPEINH = #{record.zzpeinh,jdbcType=VARCHAR},
      </if>
      <if test="record.zzmwskz != null">
        ZZMWSKZ = #{record.zzmwskz,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update SAP_EKPO
    set id = #{record.id,jdbcType=BIGINT},
      MANDT = #{record.mandt,jdbcType=VARCHAR},
      EBELN = #{record.ebeln,jdbcType=VARCHAR},
      EBELP = #{record.ebelp,jdbcType=VARCHAR},
      LOEKZ = #{record.loekz,jdbcType=VARCHAR},
      STATU = #{record.statu,jdbcType=VARCHAR},
      AEDAT = #{record.aedat,jdbcType=VARCHAR},
      TXZ01 = #{record.txz01,jdbcType=VARCHAR},
      MATNR = #{record.matnr,jdbcType=VARCHAR},
      EMATN = #{record.ematn,jdbcType=VARCHAR},
      BUKRS = #{record.bukrs,jdbcType=VARCHAR},
      WERKS = #{record.werks,jdbcType=VARCHAR},
      LGORT = #{record.lgort,jdbcType=VARCHAR},
      BEDNR = #{record.bednr,jdbcType=VARCHAR},
      MATKL = #{record.matkl,jdbcType=VARCHAR},
      INFNR = #{record.infnr,jdbcType=VARCHAR},
      IDNLF = #{record.idnlf,jdbcType=VARCHAR},
      KTMNG = #{record.ktmng,jdbcType=DECIMAL},
      MENGE = #{record.menge,jdbcType=DECIMAL},
      MEINS = #{record.meins,jdbcType=VARCHAR},
      BPRME = #{record.bprme,jdbcType=VARCHAR},
      BPUMZ = #{record.bpumz,jdbcType=DECIMAL},
      BPUMN = #{record.bpumn,jdbcType=DECIMAL},
      UMREZ = #{record.umrez,jdbcType=DECIMAL},
      UMREN = #{record.umren,jdbcType=DECIMAL},
      NETPR = #{record.netpr,jdbcType=DECIMAL},
      PEINH = #{record.peinh,jdbcType=DECIMAL},
      NETWR = #{record.netwr,jdbcType=DECIMAL},
      BRTWR = #{record.brtwr,jdbcType=DECIMAL},
      AGDAT = #{record.agdat,jdbcType=VARCHAR},
      WEBAZ = #{record.webaz,jdbcType=DECIMAL},
      MWSKZ = #{record.mwskz,jdbcType=VARCHAR},
      BONUS = #{record.bonus,jdbcType=VARCHAR},
      INSMK = #{record.insmk,jdbcType=VARCHAR},
      SPINF = #{record.spinf,jdbcType=VARCHAR},
      PRSDR = #{record.prsdr,jdbcType=VARCHAR},
      SCHPR = #{record.schpr,jdbcType=VARCHAR},
      MAHNZ = #{record.mahnz,jdbcType=DECIMAL},
      MAHN1 = #{record.mahn1,jdbcType=DECIMAL},
      MAHN2 = #{record.mahn2,jdbcType=DECIMAL},
      MAHN3 = #{record.mahn3,jdbcType=DECIMAL},
      UEBTO = #{record.uebto,jdbcType=DECIMAL},
      UEBTK = #{record.uebtk,jdbcType=VARCHAR},
      UNTTO = #{record.untto,jdbcType=DECIMAL},
      BWTAR = #{record.bwtar,jdbcType=VARCHAR},
      BWTTY = #{record.bwtty,jdbcType=VARCHAR},
      ABSKZ = #{record.abskz,jdbcType=VARCHAR},
      AGMEM = #{record.agmem,jdbcType=VARCHAR},
      ELIKZ = #{record.elikz,jdbcType=VARCHAR},
      EREKZ = #{record.erekz,jdbcType=VARCHAR},
      PSTYP = #{record.pstyp,jdbcType=VARCHAR},
      KNTTP = #{record.knttp,jdbcType=VARCHAR},
      KZVBR = #{record.kzvbr,jdbcType=VARCHAR},
      VRTKZ = #{record.vrtkz,jdbcType=VARCHAR},
      TWRKZ = #{record.twrkz,jdbcType=VARCHAR},
      WEPOS = #{record.wepos,jdbcType=VARCHAR},
      WEUNB = #{record.weunb,jdbcType=VARCHAR},
      REPOS = #{record.repos,jdbcType=VARCHAR},
      WEBRE = #{record.webre,jdbcType=VARCHAR},
      KZABS = #{record.kzabs,jdbcType=VARCHAR},
      LABNR = #{record.labnr,jdbcType=VARCHAR},
      KONNR = #{record.konnr,jdbcType=VARCHAR},
      KTPNR = #{record.ktpnr,jdbcType=VARCHAR},
      ABDAT = #{record.abdat,jdbcType=VARCHAR},
      ABFTZ = #{record.abftz,jdbcType=DECIMAL},
      ETFZ1 = #{record.etfz1,jdbcType=DECIMAL},
      ETFZ2 = #{record.etfz2,jdbcType=DECIMAL},
      KZSTU = #{record.kzstu,jdbcType=VARCHAR},
      NOTKZ = #{record.notkz,jdbcType=VARCHAR},
      LMEIN = #{record.lmein,jdbcType=VARCHAR},
      EVERS = #{record.evers,jdbcType=VARCHAR},
      ZWERT = #{record.zwert,jdbcType=DECIMAL},
      NAVNW = #{record.navnw,jdbcType=DECIMAL},
      ABMNG = #{record.abmng,jdbcType=DECIMAL},
      PRDAT = #{record.prdat,jdbcType=VARCHAR},
      BSTYP = #{record.bstyp,jdbcType=VARCHAR},
      EFFWR = #{record.effwr,jdbcType=DECIMAL},
      XOBLR = #{record.xoblr,jdbcType=VARCHAR},
      KUNNR = #{record.kunnr,jdbcType=VARCHAR},
      ADRNR = #{record.adrnr,jdbcType=VARCHAR},
      EKKOL = #{record.ekkol,jdbcType=VARCHAR},
      SKTOF = #{record.sktof,jdbcType=VARCHAR},
      STAFO = #{record.stafo,jdbcType=VARCHAR},
      PLIFZ = #{record.plifz,jdbcType=DECIMAL},
      NTGEW = #{record.ntgew,jdbcType=DECIMAL},
      GEWEI = #{record.gewei,jdbcType=VARCHAR},
      TXJCD = #{record.txjcd,jdbcType=VARCHAR},
      ETDRK = #{record.etdrk,jdbcType=VARCHAR},
      SOBKZ = #{record.sobkz,jdbcType=VARCHAR},
      ARSNR = #{record.arsnr,jdbcType=VARCHAR},
      ARSPS = #{record.arsps,jdbcType=VARCHAR},
      INSNC = #{record.insnc,jdbcType=VARCHAR},
      SSQSS = #{record.ssqss,jdbcType=VARCHAR},
      ZGTYP = #{record.zgtyp,jdbcType=VARCHAR},
      EAN11 = #{record.ean11,jdbcType=VARCHAR},
      BSTAE = #{record.bstae,jdbcType=VARCHAR},
      REVLV = #{record.revlv,jdbcType=VARCHAR},
      GEBER = #{record.geber,jdbcType=VARCHAR},
      FISTL = #{record.fistl,jdbcType=VARCHAR},
      FIPOS = #{record.fipos,jdbcType=VARCHAR},
      KO_GSBER = #{record.koGsber,jdbcType=VARCHAR},
      KO_PARGB = #{record.koPargb,jdbcType=VARCHAR},
      KO_PRCTR = #{record.koPrctr,jdbcType=VARCHAR},
      KO_PPRCTR = #{record.koPprctr,jdbcType=VARCHAR},
      MEPRF = #{record.meprf,jdbcType=VARCHAR},
      BRGEW = #{record.brgew,jdbcType=DECIMAL},
      VOLUM = #{record.volum,jdbcType=DECIMAL},
      VOLEH = #{record.voleh,jdbcType=VARCHAR},
      INCO1 = #{record.inco1,jdbcType=VARCHAR},
      INCO2 = #{record.inco2,jdbcType=VARCHAR},
      VORAB = #{record.vorab,jdbcType=VARCHAR},
      KOLIF = #{record.kolif,jdbcType=VARCHAR},
      LTSNR = #{record.ltsnr,jdbcType=VARCHAR},
      PACKNO = #{record.packno,jdbcType=VARCHAR},
      FPLNR = #{record.fplnr,jdbcType=VARCHAR},
      GNETWR = #{record.gnetwr,jdbcType=DECIMAL},
      STAPO = #{record.stapo,jdbcType=VARCHAR},
      UEBPO = #{record.uebpo,jdbcType=VARCHAR},
      LEWED = #{record.lewed,jdbcType=VARCHAR},
      EMLIF = #{record.emlif,jdbcType=VARCHAR},
      LBLKZ = #{record.lblkz,jdbcType=VARCHAR},
      SATNR = #{record.satnr,jdbcType=VARCHAR},
      ATTYP = #{record.attyp,jdbcType=VARCHAR},
      VSART = #{record.vsart,jdbcType=VARCHAR},
      HANDOVERLOC = #{record.handoverloc,jdbcType=VARCHAR},
      KANBA = #{record.kanba,jdbcType=VARCHAR},
      ADRN2 = #{record.adrn2,jdbcType=VARCHAR},
      CUOBJ = #{record.cuobj,jdbcType=VARCHAR},
      XERSY = #{record.xersy,jdbcType=VARCHAR},
      EILDT = #{record.eildt,jdbcType=VARCHAR},
      DRDAT = #{record.drdat,jdbcType=VARCHAR},
      DRUHR = #{record.druhr,jdbcType=VARCHAR},
      DRUNR = #{record.drunr,jdbcType=VARCHAR},
      AKTNR = #{record.aktnr,jdbcType=VARCHAR},
      ABELN = #{record.abeln,jdbcType=VARCHAR},
      ABELP = #{record.abelp,jdbcType=VARCHAR},
      ANZPU = #{record.anzpu,jdbcType=DECIMAL},
      PUNEI = #{record.punei,jdbcType=VARCHAR},
      SAISO = #{record.saiso,jdbcType=VARCHAR},
      SAISJ = #{record.saisj,jdbcType=VARCHAR},
      EBON2 = #{record.ebon2,jdbcType=VARCHAR},
      EBON3 = #{record.ebon3,jdbcType=VARCHAR},
      EBONF = #{record.ebonf,jdbcType=VARCHAR},
      MLMAA = #{record.mlmaa,jdbcType=VARCHAR},
      MHDRZ = #{record.mhdrz,jdbcType=DECIMAL},
      ANFNR = #{record.anfnr,jdbcType=VARCHAR},
      ANFPS = #{record.anfps,jdbcType=VARCHAR},
      KZKFG = #{record.kzkfg,jdbcType=VARCHAR},
      USEQU = #{record.usequ,jdbcType=VARCHAR},
      UMSOK = #{record.umsok,jdbcType=VARCHAR},
      BANFN = #{record.banfn,jdbcType=VARCHAR},
      BNFPO = #{record.bnfpo,jdbcType=VARCHAR},
      MTART = #{record.mtart,jdbcType=VARCHAR},
      UPTYP = #{record.uptyp,jdbcType=VARCHAR},
      UPVOR = #{record.upvor,jdbcType=VARCHAR},
      KZWI1 = #{record.kzwi1,jdbcType=DECIMAL},
      KZWI2 = #{record.kzwi2,jdbcType=DECIMAL},
      KZWI3 = #{record.kzwi3,jdbcType=DECIMAL},
      KZWI4 = #{record.kzwi4,jdbcType=DECIMAL},
      KZWI5 = #{record.kzwi5,jdbcType=DECIMAL},
      KZWI6 = #{record.kzwi6,jdbcType=DECIMAL},
      SIKGR = #{record.sikgr,jdbcType=VARCHAR},
      MFZHI = #{record.mfzhi,jdbcType=DECIMAL},
      FFZHI = #{record.ffzhi,jdbcType=DECIMAL},
      RETPO = #{record.retpo,jdbcType=VARCHAR},
      AUREL = #{record.aurel,jdbcType=VARCHAR},
      BSGRU = #{record.bsgru,jdbcType=VARCHAR},
      LFRET = #{record.lfret,jdbcType=VARCHAR},
      MFRGR = #{record.mfrgr,jdbcType=VARCHAR},
      NRFHG = #{record.nrfhg,jdbcType=VARCHAR},
      J_1BNBM = #{record.j1bnbm,jdbcType=VARCHAR},
      J_1BMATUSE = #{record.j1bmatuse,jdbcType=VARCHAR},
      J_1BMATORG = #{record.j1bmatorg,jdbcType=VARCHAR},
      J_1BOWNPRO = #{record.j1bownpro,jdbcType=VARCHAR},
      J_1BINDUST = #{record.j1bindust,jdbcType=VARCHAR},
      ABUEB = #{record.abueb,jdbcType=VARCHAR},
      NLABD = #{record.nlabd,jdbcType=VARCHAR},
      NFABD = #{record.nfabd,jdbcType=VARCHAR},
      KZBWS = #{record.kzbws,jdbcType=VARCHAR},
      BONBA = #{record.bonba,jdbcType=DECIMAL},
      FABKZ = #{record.fabkz,jdbcType=VARCHAR},
      J_1AINDXP = #{record.j1aindxp,jdbcType=VARCHAR},
      J_1AIDATEP = #{record.j1aidatep,jdbcType=VARCHAR},
      MPROF = #{record.mprof,jdbcType=VARCHAR},
      EGLKZ = #{record.eglkz,jdbcType=VARCHAR},
      KZTLF = #{record.kztlf,jdbcType=VARCHAR},
      KZFME = #{record.kzfme,jdbcType=VARCHAR},
      RDPRF = #{record.rdprf,jdbcType=VARCHAR},
      TECHS = #{record.techs,jdbcType=VARCHAR},
      CHG_SRV = #{record.chgSrv,jdbcType=VARCHAR},
      CHG_FPLNR = #{record.chgFplnr,jdbcType=VARCHAR},
      MFRPN = #{record.mfrpn,jdbcType=VARCHAR},
      MFRNR = #{record.mfrnr,jdbcType=VARCHAR},
      EMNFR = #{record.emnfr,jdbcType=VARCHAR},
      NOVET = #{record.novet,jdbcType=VARCHAR},
      AFNAM = #{record.afnam,jdbcType=VARCHAR},
      TZONRC = #{record.tzonrc,jdbcType=VARCHAR},
      IPRKZ = #{record.iprkz,jdbcType=VARCHAR},
      LEBRE = #{record.lebre,jdbcType=VARCHAR},
      BERID = #{record.berid,jdbcType=VARCHAR},
      XCONDITIONS = #{record.xconditions,jdbcType=VARCHAR},
      APOMS = #{record.apoms,jdbcType=VARCHAR},
      CCOMP = #{record.ccomp,jdbcType=VARCHAR},
      GRANT_NBR = #{record.grantNbr,jdbcType=VARCHAR},
      FKBER = #{record.fkber,jdbcType=VARCHAR},
      `STATUS` = #{record.status,jdbcType=VARCHAR},
      RESLO = #{record.reslo,jdbcType=VARCHAR},
      KBLNR = #{record.kblnr,jdbcType=VARCHAR},
      KBLPOS = #{record.kblpos,jdbcType=VARCHAR},
      WEORA = #{record.weora,jdbcType=VARCHAR},
      SRV_BAS_COM = #{record.srvBasCom,jdbcType=VARCHAR},
      PRIO_URG = #{record.prioUrg,jdbcType=VARCHAR},
      PRIO_REQ = #{record.prioReq,jdbcType=VARCHAR},
      EMPST = #{record.empst,jdbcType=VARCHAR},
      DIFF_INVOICE = #{record.diffInvoice,jdbcType=VARCHAR},
      TRMRISK_RELEVANT = #{record.trmriskRelevant,jdbcType=VARCHAR},
      SPE_ABGRU = #{record.speAbgru,jdbcType=VARCHAR},
      SPE_CRM_SO = #{record.speCrmSo,jdbcType=VARCHAR},
      SPE_CRM_SO_ITEM = #{record.speCrmSoItem,jdbcType=VARCHAR},
      SPE_CRM_REF_SO = #{record.speCrmRefSo,jdbcType=VARCHAR},
      SPE_CRM_REF_ITEM = #{record.speCrmRefItem,jdbcType=VARCHAR},
      SPE_CRM_FKREL = #{record.speCrmFkrel,jdbcType=VARCHAR},
      SPE_CHNG_SYS = #{record.speChngSys,jdbcType=VARCHAR},
      SPE_INSMK_SRC = #{record.speInsmkSrc,jdbcType=VARCHAR},
      SPE_CQ_CTRLTYPE = #{record.speCqCtrltype,jdbcType=VARCHAR},
      SPE_CQ_NOCQ = #{record.speCqNocq,jdbcType=VARCHAR},
      REASON_CODE = #{record.reasonCode,jdbcType=VARCHAR},
      CQU_SAR = #{record.cquSar,jdbcType=DECIMAL},
      ANZSN = #{record.anzsn,jdbcType=INTEGER},
      SPE_EWM_DTC = #{record.speEwmDtc,jdbcType=VARCHAR},
      EXLIN = #{record.exlin,jdbcType=VARCHAR},
      EXSNR = #{record.exsnr,jdbcType=VARCHAR},
      EHTYP = #{record.ehtyp,jdbcType=VARCHAR},
      RETPC = #{record.retpc,jdbcType=DECIMAL},
      DPTYP = #{record.dptyp,jdbcType=VARCHAR},
      DPPCT = #{record.dppct,jdbcType=DECIMAL},
      DPAMT = #{record.dpamt,jdbcType=DECIMAL},
      DPDAT = #{record.dpdat,jdbcType=VARCHAR},
      FLS_RSTO = #{record.flsRsto,jdbcType=VARCHAR},
      EXT_RFX_NUMBER = #{record.extRfxNumber,jdbcType=VARCHAR},
      EXT_RFX_ITEM = #{record.extRfxItem,jdbcType=VARCHAR},
      EXT_RFX_SYSTEM = #{record.extRfxSystem,jdbcType=VARCHAR},
      SRM_CONTRACT_ID = #{record.srmContractId,jdbcType=VARCHAR},
      SRM_CONTRACT_ITM = #{record.srmContractItm,jdbcType=VARCHAR},
      BLK_REASON_ID = #{record.blkReasonId,jdbcType=VARCHAR},
      BLK_REASON_TXT = #{record.blkReasonTxt,jdbcType=VARCHAR},
      ITCONS = #{record.itcons,jdbcType=VARCHAR},
      FIXMG = #{record.fixmg,jdbcType=VARCHAR},
      WABWE = #{record.wabwe,jdbcType=VARCHAR},
      CMPL_DLV_ITM = #{record.cmplDlvItm,jdbcType=VARCHAR},
      INCO2_L = #{record.inco2L,jdbcType=VARCHAR},
      INCO3_L = #{record.inco3L,jdbcType=VARCHAR},
      STAWN = #{record.stawn,jdbcType=VARCHAR},
      ISVCO = #{record.isvco,jdbcType=VARCHAR},
      GRWRT = #{record.grwrt,jdbcType=DECIMAL},
      SERVICEPERFORMER = #{record.serviceperformer,jdbcType=VARCHAR},
      PRODUCTTYPE = #{record.producttype,jdbcType=VARCHAR},
      REQUESTFORQUOTATION = #{record.requestforquotation,jdbcType=VARCHAR},
      REQUESTFORQUOTATIONITEM = #{record.requestforquotationitem,jdbcType=VARCHAR},
      EXTERNALREFERENCEID = #{record.externalreferenceid,jdbcType=VARCHAR},
      TC_AUT_DET = #{record.tcAutDet,jdbcType=VARCHAR},
      MANUAL_TC_REASON = #{record.manualTcReason,jdbcType=VARCHAR},
      FISCAL_INCENTIVE = #{record.fiscalIncentive,jdbcType=VARCHAR},
      TAX_SUBJECT_ST = #{record.taxSubjectSt,jdbcType=VARCHAR},
      FISCAL_INCENTIVE_ID = #{record.fiscalIncentiveId,jdbcType=VARCHAR},
      SF_TXJCD = #{record.sfTxjcd,jdbcType=VARCHAR},
      DUMMY_EKPO_INCL_EEW_PS = #{record.dummyEkpoInclEewPs,jdbcType=VARCHAR},
      EXPECTED_VALUE = #{record.expectedValue,jdbcType=DECIMAL},
      LIMIT_AMOUNT = #{record.limitAmount,jdbcType=DECIMAL},
      ENH_DATE1 = #{record.enhDate1,jdbcType=VARCHAR},
      ENH_DATE2 = #{record.enhDate2,jdbcType=VARCHAR},
      ENH_PERCENT = #{record.enhPercent,jdbcType=DECIMAL},
      ENH_NUMC1 = #{record.enhNumc1,jdbcType=VARCHAR},
      DATAAGING = #{record.dataaging,jdbcType=VARCHAR},
      BEV1_NEGEN_ITEM = #{record.bev1NegenItem,jdbcType=VARCHAR},
      BEV1_NEDEPFREE = #{record.bev1Nedepfree,jdbcType=VARCHAR},
      BEV1_NESTRUCCAT = #{record.bev1Nestruccat,jdbcType=VARCHAR},
      ADVCODE = #{record.advcode,jdbcType=VARCHAR},
      BUDGET_PD = #{record.budgetPd,jdbcType=VARCHAR},
      EXCPE = #{record.excpe,jdbcType=VARCHAR},
      FMFGUS_KEY = #{record.fmfgusKey,jdbcType=VARCHAR},
      IUID_RELEVANT = #{record.iuidRelevant,jdbcType=VARCHAR},
      MRPIND = #{record.mrpind,jdbcType=VARCHAR},
      SGT_SCAT = #{record.sgtScat,jdbcType=VARCHAR},
      SGT_RCAT = #{record.sgtRcat,jdbcType=VARCHAR},
      TMS_REF_UUID = #{record.tmsRefUuid,jdbcType=VARCHAR},
      WRF_CHARSTC1 = #{record.wrfCharstc1,jdbcType=VARCHAR},
      WRF_CHARSTC2 = #{record.wrfCharstc2,jdbcType=VARCHAR},
      WRF_CHARSTC3 = #{record.wrfCharstc3,jdbcType=VARCHAR},
      ZZZMDSQ = #{record.zzzmdsq,jdbcType=VARCHAR},
      ZZZMDHH = #{record.zzzmdhh,jdbcType=VARCHAR},
      ZZQXDJH = #{record.zzqxdjh,jdbcType=VARCHAR},
      ZZQXDHH = #{record.zzqxdhh,jdbcType=VARCHAR},
      ZZCXBJS = #{record.zzcxbjs,jdbcType=VARCHAR},
      ZZQTYS = #{record.zzqtys,jdbcType=VARCHAR},
      ZZSPCD = #{record.zzspcd,jdbcType=VARCHAR},
      ZZSCCJ = #{record.zzsccj,jdbcType=VARCHAR},
      ZZZSCPH = #{record.zzzscph,jdbcType=VARCHAR},
      ZZZGJJ = #{record.zzzgjj,jdbcType=VARCHAR},
      REFSITE = #{record.refsite,jdbcType=VARCHAR},
      ZAPCGK = #{record.zapcgk,jdbcType=VARCHAR},
      APCGK_EXTEND = #{record.apcgkExtend,jdbcType=VARCHAR},
      ZBAS_DATE = #{record.zbasDate,jdbcType=VARCHAR},
      ZADATTYP = #{record.zadattyp,jdbcType=VARCHAR},
      ZSTART_DAT = #{record.zstartDat,jdbcType=VARCHAR},
      Z_DEV = #{record.zDev,jdbcType=DECIMAL},
      ZINDANX = #{record.zindanx,jdbcType=VARCHAR},
      ZLIMIT_DAT = #{record.zlimitDat,jdbcType=VARCHAR},
      NUMERATOR = #{record.numerator,jdbcType=VARCHAR},
      HASHCAL_BDAT = #{record.hashcalBdat,jdbcType=VARCHAR},
      HASHCAL = #{record.hashcal,jdbcType=VARCHAR},
      NEGATIVE = #{record.negative,jdbcType=VARCHAR},
      HASHCAL_EXISTS = #{record.hashcalExists,jdbcType=VARCHAR},
      KNOWN_INDEX = #{record.knownIndex,jdbcType=VARCHAR},
      SAPMP_GPOSE = #{record.sapmpGpose,jdbcType=VARCHAR},
      ANGPN = #{record.angpn,jdbcType=VARCHAR},
      ADMOI = #{record.admoi,jdbcType=VARCHAR},
      ADPRI = #{record.adpri,jdbcType=VARCHAR},
      LPRIO = #{record.lprio,jdbcType=VARCHAR},
      ADACN = #{record.adacn,jdbcType=VARCHAR},
      AFPNR = #{record.afpnr,jdbcType=VARCHAR},
      BSARK = #{record.bsark,jdbcType=VARCHAR},
      AUDAT = #{record.audat,jdbcType=VARCHAR},
      ANGNR = #{record.angnr,jdbcType=VARCHAR},
      PNSTAT = #{record.pnstat,jdbcType=VARCHAR},
      ADDNS = #{record.addns,jdbcType=VARCHAR},
      SERRU = #{record.serru,jdbcType=VARCHAR},
      SERNP = #{record.sernp,jdbcType=VARCHAR},
      DISUB_SOBKZ = #{record.disubSobkz,jdbcType=VARCHAR},
      DISUB_PSPNR = #{record.disubPspnr,jdbcType=VARCHAR},
      DISUB_KUNNR = #{record.disubKunnr,jdbcType=VARCHAR},
      DISUB_VBELN = #{record.disubVbeln,jdbcType=VARCHAR},
      DISUB_POSNR = #{record.disubPosnr,jdbcType=VARCHAR},
      DISUB_OWNER = #{record.disubOwner,jdbcType=VARCHAR},
      FSH_SEASON_YEAR = #{record.fshSeasonYear,jdbcType=VARCHAR},
      FSH_SEASON = #{record.fshSeason,jdbcType=VARCHAR},
      FSH_COLLECTION = #{record.fshCollection,jdbcType=VARCHAR},
      FSH_THEME = #{record.fshTheme,jdbcType=VARCHAR},
      FSH_ATP_DATE = #{record.fshAtpDate,jdbcType=VARCHAR},
      FSH_VAS_REL = #{record.fshVasRel,jdbcType=VARCHAR},
      FSH_VAS_PRNT_ID = #{record.fshVasPrntId,jdbcType=VARCHAR},
      FSH_TRANSACTION = #{record.fshTransaction,jdbcType=VARCHAR},
      FSH_ITEM_GROUP = #{record.fshItemGroup,jdbcType=VARCHAR},
      FSH_ITEM = #{record.fshItem,jdbcType=VARCHAR},
      FSH_SS = #{record.fshSs,jdbcType=VARCHAR},
      FSH_GRID_COND_REC = #{record.fshGridCondRec,jdbcType=VARCHAR},
      FSH_PSM_PFM_SPLIT = #{record.fshPsmPfmSplit,jdbcType=VARCHAR},
      CNFM_QTY = #{record.cnfmQty,jdbcType=DECIMAL},
      STPAC = #{record.stpac,jdbcType=VARCHAR},
      LGBZO = #{record.lgbzo,jdbcType=VARCHAR},
      LGBZO_B = #{record.lgbzoB,jdbcType=VARCHAR},
      ADDRNUM = #{record.addrnum,jdbcType=VARCHAR},
      CONSNUM = #{record.consnum,jdbcType=VARCHAR},
      BORGR_MISS = #{record.borgrMiss,jdbcType=VARCHAR},
      DEP_ID = #{record.depId,jdbcType=VARCHAR},
      BELNR = #{record.belnr,jdbcType=VARCHAR},
      KBLPOS_CAB = #{record.kblposCab,jdbcType=VARCHAR},
      KBLNR_COMP = #{record.kblnrComp,jdbcType=VARCHAR},
      KBLPOS_COMP = #{record.kblposComp,jdbcType=VARCHAR},
      WBS_ELEMENT = #{record.wbsElement,jdbcType=VARCHAR},
      RFM_PSST_RULE = #{record.rfmPsstRule,jdbcType=VARCHAR},
      RFM_PSST_GROUP = #{record.rfmPsstGroup,jdbcType=VARCHAR},
      REF_ITEM = #{record.refItem,jdbcType=VARCHAR},
      SOURCE_ID = #{record.sourceId,jdbcType=VARCHAR},
      SOURCE_KEY = #{record.sourceKey,jdbcType=VARCHAR},
      PUT_BACK = #{record.putBack,jdbcType=VARCHAR},
      POL_ID = #{record.polId,jdbcType=VARCHAR},
      CONS_ORDER = #{record.consOrder,jdbcType=VARCHAR},
      ZZPAID = #{record.zzpaid,jdbcType=VARCHAR},
      ZZPAMT = #{record.zzpamt,jdbcType=DECIMAL},
      ZZSIGN = #{record.zzsign,jdbcType=DECIMAL},
      ZZDATE = #{record.zzdate,jdbcType=VARCHAR},
      ZZTIME = #{record.zztime,jdbcType=VARCHAR},
      ZZPRICE = #{record.zzprice,jdbcType=VARCHAR},
      ZZPEINH = #{record.zzpeinh,jdbcType=VARCHAR},
      ZZMWSKZ = #{record.zzmwskz,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.iscm.entity.SapEkpo">
    update SAP_EKPO
    <set>
      <if test="mandt != null">
        MANDT = #{mandt,jdbcType=VARCHAR},
      </if>
      <if test="ebeln != null">
        EBELN = #{ebeln,jdbcType=VARCHAR},
      </if>
      <if test="ebelp != null">
        EBELP = #{ebelp,jdbcType=VARCHAR},
      </if>
      <if test="loekz != null">
        LOEKZ = #{loekz,jdbcType=VARCHAR},
      </if>
      <if test="statu != null">
        STATU = #{statu,jdbcType=VARCHAR},
      </if>
      <if test="aedat != null">
        AEDAT = #{aedat,jdbcType=VARCHAR},
      </if>
      <if test="txz01 != null">
        TXZ01 = #{txz01,jdbcType=VARCHAR},
      </if>
      <if test="matnr != null">
        MATNR = #{matnr,jdbcType=VARCHAR},
      </if>
      <if test="ematn != null">
        EMATN = #{ematn,jdbcType=VARCHAR},
      </if>
      <if test="bukrs != null">
        BUKRS = #{bukrs,jdbcType=VARCHAR},
      </if>
      <if test="werks != null">
        WERKS = #{werks,jdbcType=VARCHAR},
      </if>
      <if test="lgort != null">
        LGORT = #{lgort,jdbcType=VARCHAR},
      </if>
      <if test="bednr != null">
        BEDNR = #{bednr,jdbcType=VARCHAR},
      </if>
      <if test="matkl != null">
        MATKL = #{matkl,jdbcType=VARCHAR},
      </if>
      <if test="infnr != null">
        INFNR = #{infnr,jdbcType=VARCHAR},
      </if>
      <if test="idnlf != null">
        IDNLF = #{idnlf,jdbcType=VARCHAR},
      </if>
      <if test="ktmng != null">
        KTMNG = #{ktmng,jdbcType=DECIMAL},
      </if>
      <if test="menge != null">
        MENGE = #{menge,jdbcType=DECIMAL},
      </if>
      <if test="meins != null">
        MEINS = #{meins,jdbcType=VARCHAR},
      </if>
      <if test="bprme != null">
        BPRME = #{bprme,jdbcType=VARCHAR},
      </if>
      <if test="bpumz != null">
        BPUMZ = #{bpumz,jdbcType=DECIMAL},
      </if>
      <if test="bpumn != null">
        BPUMN = #{bpumn,jdbcType=DECIMAL},
      </if>
      <if test="umrez != null">
        UMREZ = #{umrez,jdbcType=DECIMAL},
      </if>
      <if test="umren != null">
        UMREN = #{umren,jdbcType=DECIMAL},
      </if>
      <if test="netpr != null">
        NETPR = #{netpr,jdbcType=DECIMAL},
      </if>
      <if test="peinh != null">
        PEINH = #{peinh,jdbcType=DECIMAL},
      </if>
      <if test="netwr != null">
        NETWR = #{netwr,jdbcType=DECIMAL},
      </if>
      <if test="brtwr != null">
        BRTWR = #{brtwr,jdbcType=DECIMAL},
      </if>
      <if test="agdat != null">
        AGDAT = #{agdat,jdbcType=VARCHAR},
      </if>
      <if test="webaz != null">
        WEBAZ = #{webaz,jdbcType=DECIMAL},
      </if>
      <if test="mwskz != null">
        MWSKZ = #{mwskz,jdbcType=VARCHAR},
      </if>
      <if test="bonus != null">
        BONUS = #{bonus,jdbcType=VARCHAR},
      </if>
      <if test="insmk != null">
        INSMK = #{insmk,jdbcType=VARCHAR},
      </if>
      <if test="spinf != null">
        SPINF = #{spinf,jdbcType=VARCHAR},
      </if>
      <if test="prsdr != null">
        PRSDR = #{prsdr,jdbcType=VARCHAR},
      </if>
      <if test="schpr != null">
        SCHPR = #{schpr,jdbcType=VARCHAR},
      </if>
      <if test="mahnz != null">
        MAHNZ = #{mahnz,jdbcType=DECIMAL},
      </if>
      <if test="mahn1 != null">
        MAHN1 = #{mahn1,jdbcType=DECIMAL},
      </if>
      <if test="mahn2 != null">
        MAHN2 = #{mahn2,jdbcType=DECIMAL},
      </if>
      <if test="mahn3 != null">
        MAHN3 = #{mahn3,jdbcType=DECIMAL},
      </if>
      <if test="uebto != null">
        UEBTO = #{uebto,jdbcType=DECIMAL},
      </if>
      <if test="uebtk != null">
        UEBTK = #{uebtk,jdbcType=VARCHAR},
      </if>
      <if test="untto != null">
        UNTTO = #{untto,jdbcType=DECIMAL},
      </if>
      <if test="bwtar != null">
        BWTAR = #{bwtar,jdbcType=VARCHAR},
      </if>
      <if test="bwtty != null">
        BWTTY = #{bwtty,jdbcType=VARCHAR},
      </if>
      <if test="abskz != null">
        ABSKZ = #{abskz,jdbcType=VARCHAR},
      </if>
      <if test="agmem != null">
        AGMEM = #{agmem,jdbcType=VARCHAR},
      </if>
      <if test="elikz != null">
        ELIKZ = #{elikz,jdbcType=VARCHAR},
      </if>
      <if test="erekz != null">
        EREKZ = #{erekz,jdbcType=VARCHAR},
      </if>
      <if test="pstyp != null">
        PSTYP = #{pstyp,jdbcType=VARCHAR},
      </if>
      <if test="knttp != null">
        KNTTP = #{knttp,jdbcType=VARCHAR},
      </if>
      <if test="kzvbr != null">
        KZVBR = #{kzvbr,jdbcType=VARCHAR},
      </if>
      <if test="vrtkz != null">
        VRTKZ = #{vrtkz,jdbcType=VARCHAR},
      </if>
      <if test="twrkz != null">
        TWRKZ = #{twrkz,jdbcType=VARCHAR},
      </if>
      <if test="wepos != null">
        WEPOS = #{wepos,jdbcType=VARCHAR},
      </if>
      <if test="weunb != null">
        WEUNB = #{weunb,jdbcType=VARCHAR},
      </if>
      <if test="repos != null">
        REPOS = #{repos,jdbcType=VARCHAR},
      </if>
      <if test="webre != null">
        WEBRE = #{webre,jdbcType=VARCHAR},
      </if>
      <if test="kzabs != null">
        KZABS = #{kzabs,jdbcType=VARCHAR},
      </if>
      <if test="labnr != null">
        LABNR = #{labnr,jdbcType=VARCHAR},
      </if>
      <if test="konnr != null">
        KONNR = #{konnr,jdbcType=VARCHAR},
      </if>
      <if test="ktpnr != null">
        KTPNR = #{ktpnr,jdbcType=VARCHAR},
      </if>
      <if test="abdat != null">
        ABDAT = #{abdat,jdbcType=VARCHAR},
      </if>
      <if test="abftz != null">
        ABFTZ = #{abftz,jdbcType=DECIMAL},
      </if>
      <if test="etfz1 != null">
        ETFZ1 = #{etfz1,jdbcType=DECIMAL},
      </if>
      <if test="etfz2 != null">
        ETFZ2 = #{etfz2,jdbcType=DECIMAL},
      </if>
      <if test="kzstu != null">
        KZSTU = #{kzstu,jdbcType=VARCHAR},
      </if>
      <if test="notkz != null">
        NOTKZ = #{notkz,jdbcType=VARCHAR},
      </if>
      <if test="lmein != null">
        LMEIN = #{lmein,jdbcType=VARCHAR},
      </if>
      <if test="evers != null">
        EVERS = #{evers,jdbcType=VARCHAR},
      </if>
      <if test="zwert != null">
        ZWERT = #{zwert,jdbcType=DECIMAL},
      </if>
      <if test="navnw != null">
        NAVNW = #{navnw,jdbcType=DECIMAL},
      </if>
      <if test="abmng != null">
        ABMNG = #{abmng,jdbcType=DECIMAL},
      </if>
      <if test="prdat != null">
        PRDAT = #{prdat,jdbcType=VARCHAR},
      </if>
      <if test="bstyp != null">
        BSTYP = #{bstyp,jdbcType=VARCHAR},
      </if>
      <if test="effwr != null">
        EFFWR = #{effwr,jdbcType=DECIMAL},
      </if>
      <if test="xoblr != null">
        XOBLR = #{xoblr,jdbcType=VARCHAR},
      </if>
      <if test="kunnr != null">
        KUNNR = #{kunnr,jdbcType=VARCHAR},
      </if>
      <if test="adrnr != null">
        ADRNR = #{adrnr,jdbcType=VARCHAR},
      </if>
      <if test="ekkol != null">
        EKKOL = #{ekkol,jdbcType=VARCHAR},
      </if>
      <if test="sktof != null">
        SKTOF = #{sktof,jdbcType=VARCHAR},
      </if>
      <if test="stafo != null">
        STAFO = #{stafo,jdbcType=VARCHAR},
      </if>
      <if test="plifz != null">
        PLIFZ = #{plifz,jdbcType=DECIMAL},
      </if>
      <if test="ntgew != null">
        NTGEW = #{ntgew,jdbcType=DECIMAL},
      </if>
      <if test="gewei != null">
        GEWEI = #{gewei,jdbcType=VARCHAR},
      </if>
      <if test="txjcd != null">
        TXJCD = #{txjcd,jdbcType=VARCHAR},
      </if>
      <if test="etdrk != null">
        ETDRK = #{etdrk,jdbcType=VARCHAR},
      </if>
      <if test="sobkz != null">
        SOBKZ = #{sobkz,jdbcType=VARCHAR},
      </if>
      <if test="arsnr != null">
        ARSNR = #{arsnr,jdbcType=VARCHAR},
      </if>
      <if test="arsps != null">
        ARSPS = #{arsps,jdbcType=VARCHAR},
      </if>
      <if test="insnc != null">
        INSNC = #{insnc,jdbcType=VARCHAR},
      </if>
      <if test="ssqss != null">
        SSQSS = #{ssqss,jdbcType=VARCHAR},
      </if>
      <if test="zgtyp != null">
        ZGTYP = #{zgtyp,jdbcType=VARCHAR},
      </if>
      <if test="ean11 != null">
        EAN11 = #{ean11,jdbcType=VARCHAR},
      </if>
      <if test="bstae != null">
        BSTAE = #{bstae,jdbcType=VARCHAR},
      </if>
      <if test="revlv != null">
        REVLV = #{revlv,jdbcType=VARCHAR},
      </if>
      <if test="geber != null">
        GEBER = #{geber,jdbcType=VARCHAR},
      </if>
      <if test="fistl != null">
        FISTL = #{fistl,jdbcType=VARCHAR},
      </if>
      <if test="fipos != null">
        FIPOS = #{fipos,jdbcType=VARCHAR},
      </if>
      <if test="koGsber != null">
        KO_GSBER = #{koGsber,jdbcType=VARCHAR},
      </if>
      <if test="koPargb != null">
        KO_PARGB = #{koPargb,jdbcType=VARCHAR},
      </if>
      <if test="koPrctr != null">
        KO_PRCTR = #{koPrctr,jdbcType=VARCHAR},
      </if>
      <if test="koPprctr != null">
        KO_PPRCTR = #{koPprctr,jdbcType=VARCHAR},
      </if>
      <if test="meprf != null">
        MEPRF = #{meprf,jdbcType=VARCHAR},
      </if>
      <if test="brgew != null">
        BRGEW = #{brgew,jdbcType=DECIMAL},
      </if>
      <if test="volum != null">
        VOLUM = #{volum,jdbcType=DECIMAL},
      </if>
      <if test="voleh != null">
        VOLEH = #{voleh,jdbcType=VARCHAR},
      </if>
      <if test="inco1 != null">
        INCO1 = #{inco1,jdbcType=VARCHAR},
      </if>
      <if test="inco2 != null">
        INCO2 = #{inco2,jdbcType=VARCHAR},
      </if>
      <if test="vorab != null">
        VORAB = #{vorab,jdbcType=VARCHAR},
      </if>
      <if test="kolif != null">
        KOLIF = #{kolif,jdbcType=VARCHAR},
      </if>
      <if test="ltsnr != null">
        LTSNR = #{ltsnr,jdbcType=VARCHAR},
      </if>
      <if test="packno != null">
        PACKNO = #{packno,jdbcType=VARCHAR},
      </if>
      <if test="fplnr != null">
        FPLNR = #{fplnr,jdbcType=VARCHAR},
      </if>
      <if test="gnetwr != null">
        GNETWR = #{gnetwr,jdbcType=DECIMAL},
      </if>
      <if test="stapo != null">
        STAPO = #{stapo,jdbcType=VARCHAR},
      </if>
      <if test="uebpo != null">
        UEBPO = #{uebpo,jdbcType=VARCHAR},
      </if>
      <if test="lewed != null">
        LEWED = #{lewed,jdbcType=VARCHAR},
      </if>
      <if test="emlif != null">
        EMLIF = #{emlif,jdbcType=VARCHAR},
      </if>
      <if test="lblkz != null">
        LBLKZ = #{lblkz,jdbcType=VARCHAR},
      </if>
      <if test="satnr != null">
        SATNR = #{satnr,jdbcType=VARCHAR},
      </if>
      <if test="attyp != null">
        ATTYP = #{attyp,jdbcType=VARCHAR},
      </if>
      <if test="vsart != null">
        VSART = #{vsart,jdbcType=VARCHAR},
      </if>
      <if test="handoverloc != null">
        HANDOVERLOC = #{handoverloc,jdbcType=VARCHAR},
      </if>
      <if test="kanba != null">
        KANBA = #{kanba,jdbcType=VARCHAR},
      </if>
      <if test="adrn2 != null">
        ADRN2 = #{adrn2,jdbcType=VARCHAR},
      </if>
      <if test="cuobj != null">
        CUOBJ = #{cuobj,jdbcType=VARCHAR},
      </if>
      <if test="xersy != null">
        XERSY = #{xersy,jdbcType=VARCHAR},
      </if>
      <if test="eildt != null">
        EILDT = #{eildt,jdbcType=VARCHAR},
      </if>
      <if test="drdat != null">
        DRDAT = #{drdat,jdbcType=VARCHAR},
      </if>
      <if test="druhr != null">
        DRUHR = #{druhr,jdbcType=VARCHAR},
      </if>
      <if test="drunr != null">
        DRUNR = #{drunr,jdbcType=VARCHAR},
      </if>
      <if test="aktnr != null">
        AKTNR = #{aktnr,jdbcType=VARCHAR},
      </if>
      <if test="abeln != null">
        ABELN = #{abeln,jdbcType=VARCHAR},
      </if>
      <if test="abelp != null">
        ABELP = #{abelp,jdbcType=VARCHAR},
      </if>
      <if test="anzpu != null">
        ANZPU = #{anzpu,jdbcType=DECIMAL},
      </if>
      <if test="punei != null">
        PUNEI = #{punei,jdbcType=VARCHAR},
      </if>
      <if test="saiso != null">
        SAISO = #{saiso,jdbcType=VARCHAR},
      </if>
      <if test="saisj != null">
        SAISJ = #{saisj,jdbcType=VARCHAR},
      </if>
      <if test="ebon2 != null">
        EBON2 = #{ebon2,jdbcType=VARCHAR},
      </if>
      <if test="ebon3 != null">
        EBON3 = #{ebon3,jdbcType=VARCHAR},
      </if>
      <if test="ebonf != null">
        EBONF = #{ebonf,jdbcType=VARCHAR},
      </if>
      <if test="mlmaa != null">
        MLMAA = #{mlmaa,jdbcType=VARCHAR},
      </if>
      <if test="mhdrz != null">
        MHDRZ = #{mhdrz,jdbcType=DECIMAL},
      </if>
      <if test="anfnr != null">
        ANFNR = #{anfnr,jdbcType=VARCHAR},
      </if>
      <if test="anfps != null">
        ANFPS = #{anfps,jdbcType=VARCHAR},
      </if>
      <if test="kzkfg != null">
        KZKFG = #{kzkfg,jdbcType=VARCHAR},
      </if>
      <if test="usequ != null">
        USEQU = #{usequ,jdbcType=VARCHAR},
      </if>
      <if test="umsok != null">
        UMSOK = #{umsok,jdbcType=VARCHAR},
      </if>
      <if test="banfn != null">
        BANFN = #{banfn,jdbcType=VARCHAR},
      </if>
      <if test="bnfpo != null">
        BNFPO = #{bnfpo,jdbcType=VARCHAR},
      </if>
      <if test="mtart != null">
        MTART = #{mtart,jdbcType=VARCHAR},
      </if>
      <if test="uptyp != null">
        UPTYP = #{uptyp,jdbcType=VARCHAR},
      </if>
      <if test="upvor != null">
        UPVOR = #{upvor,jdbcType=VARCHAR},
      </if>
      <if test="kzwi1 != null">
        KZWI1 = #{kzwi1,jdbcType=DECIMAL},
      </if>
      <if test="kzwi2 != null">
        KZWI2 = #{kzwi2,jdbcType=DECIMAL},
      </if>
      <if test="kzwi3 != null">
        KZWI3 = #{kzwi3,jdbcType=DECIMAL},
      </if>
      <if test="kzwi4 != null">
        KZWI4 = #{kzwi4,jdbcType=DECIMAL},
      </if>
      <if test="kzwi5 != null">
        KZWI5 = #{kzwi5,jdbcType=DECIMAL},
      </if>
      <if test="kzwi6 != null">
        KZWI6 = #{kzwi6,jdbcType=DECIMAL},
      </if>
      <if test="sikgr != null">
        SIKGR = #{sikgr,jdbcType=VARCHAR},
      </if>
      <if test="mfzhi != null">
        MFZHI = #{mfzhi,jdbcType=DECIMAL},
      </if>
      <if test="ffzhi != null">
        FFZHI = #{ffzhi,jdbcType=DECIMAL},
      </if>
      <if test="retpo != null">
        RETPO = #{retpo,jdbcType=VARCHAR},
      </if>
      <if test="aurel != null">
        AUREL = #{aurel,jdbcType=VARCHAR},
      </if>
      <if test="bsgru != null">
        BSGRU = #{bsgru,jdbcType=VARCHAR},
      </if>
      <if test="lfret != null">
        LFRET = #{lfret,jdbcType=VARCHAR},
      </if>
      <if test="mfrgr != null">
        MFRGR = #{mfrgr,jdbcType=VARCHAR},
      </if>
      <if test="nrfhg != null">
        NRFHG = #{nrfhg,jdbcType=VARCHAR},
      </if>
      <if test="j1bnbm != null">
        J_1BNBM = #{j1bnbm,jdbcType=VARCHAR},
      </if>
      <if test="j1bmatuse != null">
        J_1BMATUSE = #{j1bmatuse,jdbcType=VARCHAR},
      </if>
      <if test="j1bmatorg != null">
        J_1BMATORG = #{j1bmatorg,jdbcType=VARCHAR},
      </if>
      <if test="j1bownpro != null">
        J_1BOWNPRO = #{j1bownpro,jdbcType=VARCHAR},
      </if>
      <if test="j1bindust != null">
        J_1BINDUST = #{j1bindust,jdbcType=VARCHAR},
      </if>
      <if test="abueb != null">
        ABUEB = #{abueb,jdbcType=VARCHAR},
      </if>
      <if test="nlabd != null">
        NLABD = #{nlabd,jdbcType=VARCHAR},
      </if>
      <if test="nfabd != null">
        NFABD = #{nfabd,jdbcType=VARCHAR},
      </if>
      <if test="kzbws != null">
        KZBWS = #{kzbws,jdbcType=VARCHAR},
      </if>
      <if test="bonba != null">
        BONBA = #{bonba,jdbcType=DECIMAL},
      </if>
      <if test="fabkz != null">
        FABKZ = #{fabkz,jdbcType=VARCHAR},
      </if>
      <if test="j1aindxp != null">
        J_1AINDXP = #{j1aindxp,jdbcType=VARCHAR},
      </if>
      <if test="j1aidatep != null">
        J_1AIDATEP = #{j1aidatep,jdbcType=VARCHAR},
      </if>
      <if test="mprof != null">
        MPROF = #{mprof,jdbcType=VARCHAR},
      </if>
      <if test="eglkz != null">
        EGLKZ = #{eglkz,jdbcType=VARCHAR},
      </if>
      <if test="kztlf != null">
        KZTLF = #{kztlf,jdbcType=VARCHAR},
      </if>
      <if test="kzfme != null">
        KZFME = #{kzfme,jdbcType=VARCHAR},
      </if>
      <if test="rdprf != null">
        RDPRF = #{rdprf,jdbcType=VARCHAR},
      </if>
      <if test="techs != null">
        TECHS = #{techs,jdbcType=VARCHAR},
      </if>
      <if test="chgSrv != null">
        CHG_SRV = #{chgSrv,jdbcType=VARCHAR},
      </if>
      <if test="chgFplnr != null">
        CHG_FPLNR = #{chgFplnr,jdbcType=VARCHAR},
      </if>
      <if test="mfrpn != null">
        MFRPN = #{mfrpn,jdbcType=VARCHAR},
      </if>
      <if test="mfrnr != null">
        MFRNR = #{mfrnr,jdbcType=VARCHAR},
      </if>
      <if test="emnfr != null">
        EMNFR = #{emnfr,jdbcType=VARCHAR},
      </if>
      <if test="novet != null">
        NOVET = #{novet,jdbcType=VARCHAR},
      </if>
      <if test="afnam != null">
        AFNAM = #{afnam,jdbcType=VARCHAR},
      </if>
      <if test="tzonrc != null">
        TZONRC = #{tzonrc,jdbcType=VARCHAR},
      </if>
      <if test="iprkz != null">
        IPRKZ = #{iprkz,jdbcType=VARCHAR},
      </if>
      <if test="lebre != null">
        LEBRE = #{lebre,jdbcType=VARCHAR},
      </if>
      <if test="berid != null">
        BERID = #{berid,jdbcType=VARCHAR},
      </if>
      <if test="xconditions != null">
        XCONDITIONS = #{xconditions,jdbcType=VARCHAR},
      </if>
      <if test="apoms != null">
        APOMS = #{apoms,jdbcType=VARCHAR},
      </if>
      <if test="ccomp != null">
        CCOMP = #{ccomp,jdbcType=VARCHAR},
      </if>
      <if test="grantNbr != null">
        GRANT_NBR = #{grantNbr,jdbcType=VARCHAR},
      </if>
      <if test="fkber != null">
        FKBER = #{fkber,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `STATUS` = #{status,jdbcType=VARCHAR},
      </if>
      <if test="reslo != null">
        RESLO = #{reslo,jdbcType=VARCHAR},
      </if>
      <if test="kblnr != null">
        KBLNR = #{kblnr,jdbcType=VARCHAR},
      </if>
      <if test="kblpos != null">
        KBLPOS = #{kblpos,jdbcType=VARCHAR},
      </if>
      <if test="weora != null">
        WEORA = #{weora,jdbcType=VARCHAR},
      </if>
      <if test="srvBasCom != null">
        SRV_BAS_COM = #{srvBasCom,jdbcType=VARCHAR},
      </if>
      <if test="prioUrg != null">
        PRIO_URG = #{prioUrg,jdbcType=VARCHAR},
      </if>
      <if test="prioReq != null">
        PRIO_REQ = #{prioReq,jdbcType=VARCHAR},
      </if>
      <if test="empst != null">
        EMPST = #{empst,jdbcType=VARCHAR},
      </if>
      <if test="diffInvoice != null">
        DIFF_INVOICE = #{diffInvoice,jdbcType=VARCHAR},
      </if>
      <if test="trmriskRelevant != null">
        TRMRISK_RELEVANT = #{trmriskRelevant,jdbcType=VARCHAR},
      </if>
      <if test="speAbgru != null">
        SPE_ABGRU = #{speAbgru,jdbcType=VARCHAR},
      </if>
      <if test="speCrmSo != null">
        SPE_CRM_SO = #{speCrmSo,jdbcType=VARCHAR},
      </if>
      <if test="speCrmSoItem != null">
        SPE_CRM_SO_ITEM = #{speCrmSoItem,jdbcType=VARCHAR},
      </if>
      <if test="speCrmRefSo != null">
        SPE_CRM_REF_SO = #{speCrmRefSo,jdbcType=VARCHAR},
      </if>
      <if test="speCrmRefItem != null">
        SPE_CRM_REF_ITEM = #{speCrmRefItem,jdbcType=VARCHAR},
      </if>
      <if test="speCrmFkrel != null">
        SPE_CRM_FKREL = #{speCrmFkrel,jdbcType=VARCHAR},
      </if>
      <if test="speChngSys != null">
        SPE_CHNG_SYS = #{speChngSys,jdbcType=VARCHAR},
      </if>
      <if test="speInsmkSrc != null">
        SPE_INSMK_SRC = #{speInsmkSrc,jdbcType=VARCHAR},
      </if>
      <if test="speCqCtrltype != null">
        SPE_CQ_CTRLTYPE = #{speCqCtrltype,jdbcType=VARCHAR},
      </if>
      <if test="speCqNocq != null">
        SPE_CQ_NOCQ = #{speCqNocq,jdbcType=VARCHAR},
      </if>
      <if test="reasonCode != null">
        REASON_CODE = #{reasonCode,jdbcType=VARCHAR},
      </if>
      <if test="cquSar != null">
        CQU_SAR = #{cquSar,jdbcType=DECIMAL},
      </if>
      <if test="anzsn != null">
        ANZSN = #{anzsn,jdbcType=INTEGER},
      </if>
      <if test="speEwmDtc != null">
        SPE_EWM_DTC = #{speEwmDtc,jdbcType=VARCHAR},
      </if>
      <if test="exlin != null">
        EXLIN = #{exlin,jdbcType=VARCHAR},
      </if>
      <if test="exsnr != null">
        EXSNR = #{exsnr,jdbcType=VARCHAR},
      </if>
      <if test="ehtyp != null">
        EHTYP = #{ehtyp,jdbcType=VARCHAR},
      </if>
      <if test="retpc != null">
        RETPC = #{retpc,jdbcType=DECIMAL},
      </if>
      <if test="dptyp != null">
        DPTYP = #{dptyp,jdbcType=VARCHAR},
      </if>
      <if test="dppct != null">
        DPPCT = #{dppct,jdbcType=DECIMAL},
      </if>
      <if test="dpamt != null">
        DPAMT = #{dpamt,jdbcType=DECIMAL},
      </if>
      <if test="dpdat != null">
        DPDAT = #{dpdat,jdbcType=VARCHAR},
      </if>
      <if test="flsRsto != null">
        FLS_RSTO = #{flsRsto,jdbcType=VARCHAR},
      </if>
      <if test="extRfxNumber != null">
        EXT_RFX_NUMBER = #{extRfxNumber,jdbcType=VARCHAR},
      </if>
      <if test="extRfxItem != null">
        EXT_RFX_ITEM = #{extRfxItem,jdbcType=VARCHAR},
      </if>
      <if test="extRfxSystem != null">
        EXT_RFX_SYSTEM = #{extRfxSystem,jdbcType=VARCHAR},
      </if>
      <if test="srmContractId != null">
        SRM_CONTRACT_ID = #{srmContractId,jdbcType=VARCHAR},
      </if>
      <if test="srmContractItm != null">
        SRM_CONTRACT_ITM = #{srmContractItm,jdbcType=VARCHAR},
      </if>
      <if test="blkReasonId != null">
        BLK_REASON_ID = #{blkReasonId,jdbcType=VARCHAR},
      </if>
      <if test="blkReasonTxt != null">
        BLK_REASON_TXT = #{blkReasonTxt,jdbcType=VARCHAR},
      </if>
      <if test="itcons != null">
        ITCONS = #{itcons,jdbcType=VARCHAR},
      </if>
      <if test="fixmg != null">
        FIXMG = #{fixmg,jdbcType=VARCHAR},
      </if>
      <if test="wabwe != null">
        WABWE = #{wabwe,jdbcType=VARCHAR},
      </if>
      <if test="cmplDlvItm != null">
        CMPL_DLV_ITM = #{cmplDlvItm,jdbcType=VARCHAR},
      </if>
      <if test="inco2L != null">
        INCO2_L = #{inco2L,jdbcType=VARCHAR},
      </if>
      <if test="inco3L != null">
        INCO3_L = #{inco3L,jdbcType=VARCHAR},
      </if>
      <if test="stawn != null">
        STAWN = #{stawn,jdbcType=VARCHAR},
      </if>
      <if test="isvco != null">
        ISVCO = #{isvco,jdbcType=VARCHAR},
      </if>
      <if test="grwrt != null">
        GRWRT = #{grwrt,jdbcType=DECIMAL},
      </if>
      <if test="serviceperformer != null">
        SERVICEPERFORMER = #{serviceperformer,jdbcType=VARCHAR},
      </if>
      <if test="producttype != null">
        PRODUCTTYPE = #{producttype,jdbcType=VARCHAR},
      </if>
      <if test="requestforquotation != null">
        REQUESTFORQUOTATION = #{requestforquotation,jdbcType=VARCHAR},
      </if>
      <if test="requestforquotationitem != null">
        REQUESTFORQUOTATIONITEM = #{requestforquotationitem,jdbcType=VARCHAR},
      </if>
      <if test="externalreferenceid != null">
        EXTERNALREFERENCEID = #{externalreferenceid,jdbcType=VARCHAR},
      </if>
      <if test="tcAutDet != null">
        TC_AUT_DET = #{tcAutDet,jdbcType=VARCHAR},
      </if>
      <if test="manualTcReason != null">
        MANUAL_TC_REASON = #{manualTcReason,jdbcType=VARCHAR},
      </if>
      <if test="fiscalIncentive != null">
        FISCAL_INCENTIVE = #{fiscalIncentive,jdbcType=VARCHAR},
      </if>
      <if test="taxSubjectSt != null">
        TAX_SUBJECT_ST = #{taxSubjectSt,jdbcType=VARCHAR},
      </if>
      <if test="fiscalIncentiveId != null">
        FISCAL_INCENTIVE_ID = #{fiscalIncentiveId,jdbcType=VARCHAR},
      </if>
      <if test="sfTxjcd != null">
        SF_TXJCD = #{sfTxjcd,jdbcType=VARCHAR},
      </if>
      <if test="dummyEkpoInclEewPs != null">
        DUMMY_EKPO_INCL_EEW_PS = #{dummyEkpoInclEewPs,jdbcType=VARCHAR},
      </if>
      <if test="expectedValue != null">
        EXPECTED_VALUE = #{expectedValue,jdbcType=DECIMAL},
      </if>
      <if test="limitAmount != null">
        LIMIT_AMOUNT = #{limitAmount,jdbcType=DECIMAL},
      </if>
      <if test="enhDate1 != null">
        ENH_DATE1 = #{enhDate1,jdbcType=VARCHAR},
      </if>
      <if test="enhDate2 != null">
        ENH_DATE2 = #{enhDate2,jdbcType=VARCHAR},
      </if>
      <if test="enhPercent != null">
        ENH_PERCENT = #{enhPercent,jdbcType=DECIMAL},
      </if>
      <if test="enhNumc1 != null">
        ENH_NUMC1 = #{enhNumc1,jdbcType=VARCHAR},
      </if>
      <if test="dataaging != null">
        DATAAGING = #{dataaging,jdbcType=VARCHAR},
      </if>
      <if test="bev1NegenItem != null">
        BEV1_NEGEN_ITEM = #{bev1NegenItem,jdbcType=VARCHAR},
      </if>
      <if test="bev1Nedepfree != null">
        BEV1_NEDEPFREE = #{bev1Nedepfree,jdbcType=VARCHAR},
      </if>
      <if test="bev1Nestruccat != null">
        BEV1_NESTRUCCAT = #{bev1Nestruccat,jdbcType=VARCHAR},
      </if>
      <if test="advcode != null">
        ADVCODE = #{advcode,jdbcType=VARCHAR},
      </if>
      <if test="budgetPd != null">
        BUDGET_PD = #{budgetPd,jdbcType=VARCHAR},
      </if>
      <if test="excpe != null">
        EXCPE = #{excpe,jdbcType=VARCHAR},
      </if>
      <if test="fmfgusKey != null">
        FMFGUS_KEY = #{fmfgusKey,jdbcType=VARCHAR},
      </if>
      <if test="iuidRelevant != null">
        IUID_RELEVANT = #{iuidRelevant,jdbcType=VARCHAR},
      </if>
      <if test="mrpind != null">
        MRPIND = #{mrpind,jdbcType=VARCHAR},
      </if>
      <if test="sgtScat != null">
        SGT_SCAT = #{sgtScat,jdbcType=VARCHAR},
      </if>
      <if test="sgtRcat != null">
        SGT_RCAT = #{sgtRcat,jdbcType=VARCHAR},
      </if>
      <if test="tmsRefUuid != null">
        TMS_REF_UUID = #{tmsRefUuid,jdbcType=VARCHAR},
      </if>
      <if test="wrfCharstc1 != null">
        WRF_CHARSTC1 = #{wrfCharstc1,jdbcType=VARCHAR},
      </if>
      <if test="wrfCharstc2 != null">
        WRF_CHARSTC2 = #{wrfCharstc2,jdbcType=VARCHAR},
      </if>
      <if test="wrfCharstc3 != null">
        WRF_CHARSTC3 = #{wrfCharstc3,jdbcType=VARCHAR},
      </if>
      <if test="zzzmdsq != null">
        ZZZMDSQ = #{zzzmdsq,jdbcType=VARCHAR},
      </if>
      <if test="zzzmdhh != null">
        ZZZMDHH = #{zzzmdhh,jdbcType=VARCHAR},
      </if>
      <if test="zzqxdjh != null">
        ZZQXDJH = #{zzqxdjh,jdbcType=VARCHAR},
      </if>
      <if test="zzqxdhh != null">
        ZZQXDHH = #{zzqxdhh,jdbcType=VARCHAR},
      </if>
      <if test="zzcxbjs != null">
        ZZCXBJS = #{zzcxbjs,jdbcType=VARCHAR},
      </if>
      <if test="zzqtys != null">
        ZZQTYS = #{zzqtys,jdbcType=VARCHAR},
      </if>
      <if test="zzspcd != null">
        ZZSPCD = #{zzspcd,jdbcType=VARCHAR},
      </if>
      <if test="zzsccj != null">
        ZZSCCJ = #{zzsccj,jdbcType=VARCHAR},
      </if>
      <if test="zzzscph != null">
        ZZZSCPH = #{zzzscph,jdbcType=VARCHAR},
      </if>
      <if test="zzzgjj != null">
        ZZZGJJ = #{zzzgjj,jdbcType=VARCHAR},
      </if>
      <if test="refsite != null">
        REFSITE = #{refsite,jdbcType=VARCHAR},
      </if>
      <if test="zapcgk != null">
        ZAPCGK = #{zapcgk,jdbcType=VARCHAR},
      </if>
      <if test="apcgkExtend != null">
        APCGK_EXTEND = #{apcgkExtend,jdbcType=VARCHAR},
      </if>
      <if test="zbasDate != null">
        ZBAS_DATE = #{zbasDate,jdbcType=VARCHAR},
      </if>
      <if test="zadattyp != null">
        ZADATTYP = #{zadattyp,jdbcType=VARCHAR},
      </if>
      <if test="zstartDat != null">
        ZSTART_DAT = #{zstartDat,jdbcType=VARCHAR},
      </if>
      <if test="zDev != null">
        Z_DEV = #{zDev,jdbcType=DECIMAL},
      </if>
      <if test="zindanx != null">
        ZINDANX = #{zindanx,jdbcType=VARCHAR},
      </if>
      <if test="zlimitDat != null">
        ZLIMIT_DAT = #{zlimitDat,jdbcType=VARCHAR},
      </if>
      <if test="numerator != null">
        NUMERATOR = #{numerator,jdbcType=VARCHAR},
      </if>
      <if test="hashcalBdat != null">
        HASHCAL_BDAT = #{hashcalBdat,jdbcType=VARCHAR},
      </if>
      <if test="hashcal != null">
        HASHCAL = #{hashcal,jdbcType=VARCHAR},
      </if>
      <if test="negative != null">
        NEGATIVE = #{negative,jdbcType=VARCHAR},
      </if>
      <if test="hashcalExists != null">
        HASHCAL_EXISTS = #{hashcalExists,jdbcType=VARCHAR},
      </if>
      <if test="knownIndex != null">
        KNOWN_INDEX = #{knownIndex,jdbcType=VARCHAR},
      </if>
      <if test="sapmpGpose != null">
        SAPMP_GPOSE = #{sapmpGpose,jdbcType=VARCHAR},
      </if>
      <if test="angpn != null">
        ANGPN = #{angpn,jdbcType=VARCHAR},
      </if>
      <if test="admoi != null">
        ADMOI = #{admoi,jdbcType=VARCHAR},
      </if>
      <if test="adpri != null">
        ADPRI = #{adpri,jdbcType=VARCHAR},
      </if>
      <if test="lprio != null">
        LPRIO = #{lprio,jdbcType=VARCHAR},
      </if>
      <if test="adacn != null">
        ADACN = #{adacn,jdbcType=VARCHAR},
      </if>
      <if test="afpnr != null">
        AFPNR = #{afpnr,jdbcType=VARCHAR},
      </if>
      <if test="bsark != null">
        BSARK = #{bsark,jdbcType=VARCHAR},
      </if>
      <if test="audat != null">
        AUDAT = #{audat,jdbcType=VARCHAR},
      </if>
      <if test="angnr != null">
        ANGNR = #{angnr,jdbcType=VARCHAR},
      </if>
      <if test="pnstat != null">
        PNSTAT = #{pnstat,jdbcType=VARCHAR},
      </if>
      <if test="addns != null">
        ADDNS = #{addns,jdbcType=VARCHAR},
      </if>
      <if test="serru != null">
        SERRU = #{serru,jdbcType=VARCHAR},
      </if>
      <if test="sernp != null">
        SERNP = #{sernp,jdbcType=VARCHAR},
      </if>
      <if test="disubSobkz != null">
        DISUB_SOBKZ = #{disubSobkz,jdbcType=VARCHAR},
      </if>
      <if test="disubPspnr != null">
        DISUB_PSPNR = #{disubPspnr,jdbcType=VARCHAR},
      </if>
      <if test="disubKunnr != null">
        DISUB_KUNNR = #{disubKunnr,jdbcType=VARCHAR},
      </if>
      <if test="disubVbeln != null">
        DISUB_VBELN = #{disubVbeln,jdbcType=VARCHAR},
      </if>
      <if test="disubPosnr != null">
        DISUB_POSNR = #{disubPosnr,jdbcType=VARCHAR},
      </if>
      <if test="disubOwner != null">
        DISUB_OWNER = #{disubOwner,jdbcType=VARCHAR},
      </if>
      <if test="fshSeasonYear != null">
        FSH_SEASON_YEAR = #{fshSeasonYear,jdbcType=VARCHAR},
      </if>
      <if test="fshSeason != null">
        FSH_SEASON = #{fshSeason,jdbcType=VARCHAR},
      </if>
      <if test="fshCollection != null">
        FSH_COLLECTION = #{fshCollection,jdbcType=VARCHAR},
      </if>
      <if test="fshTheme != null">
        FSH_THEME = #{fshTheme,jdbcType=VARCHAR},
      </if>
      <if test="fshAtpDate != null">
        FSH_ATP_DATE = #{fshAtpDate,jdbcType=VARCHAR},
      </if>
      <if test="fshVasRel != null">
        FSH_VAS_REL = #{fshVasRel,jdbcType=VARCHAR},
      </if>
      <if test="fshVasPrntId != null">
        FSH_VAS_PRNT_ID = #{fshVasPrntId,jdbcType=VARCHAR},
      </if>
      <if test="fshTransaction != null">
        FSH_TRANSACTION = #{fshTransaction,jdbcType=VARCHAR},
      </if>
      <if test="fshItemGroup != null">
        FSH_ITEM_GROUP = #{fshItemGroup,jdbcType=VARCHAR},
      </if>
      <if test="fshItem != null">
        FSH_ITEM = #{fshItem,jdbcType=VARCHAR},
      </if>
      <if test="fshSs != null">
        FSH_SS = #{fshSs,jdbcType=VARCHAR},
      </if>
      <if test="fshGridCondRec != null">
        FSH_GRID_COND_REC = #{fshGridCondRec,jdbcType=VARCHAR},
      </if>
      <if test="fshPsmPfmSplit != null">
        FSH_PSM_PFM_SPLIT = #{fshPsmPfmSplit,jdbcType=VARCHAR},
      </if>
      <if test="cnfmQty != null">
        CNFM_QTY = #{cnfmQty,jdbcType=DECIMAL},
      </if>
      <if test="stpac != null">
        STPAC = #{stpac,jdbcType=VARCHAR},
      </if>
      <if test="lgbzo != null">
        LGBZO = #{lgbzo,jdbcType=VARCHAR},
      </if>
      <if test="lgbzoB != null">
        LGBZO_B = #{lgbzoB,jdbcType=VARCHAR},
      </if>
      <if test="addrnum != null">
        ADDRNUM = #{addrnum,jdbcType=VARCHAR},
      </if>
      <if test="consnum != null">
        CONSNUM = #{consnum,jdbcType=VARCHAR},
      </if>
      <if test="borgrMiss != null">
        BORGR_MISS = #{borgrMiss,jdbcType=VARCHAR},
      </if>
      <if test="depId != null">
        DEP_ID = #{depId,jdbcType=VARCHAR},
      </if>
      <if test="belnr != null">
        BELNR = #{belnr,jdbcType=VARCHAR},
      </if>
      <if test="kblposCab != null">
        KBLPOS_CAB = #{kblposCab,jdbcType=VARCHAR},
      </if>
      <if test="kblnrComp != null">
        KBLNR_COMP = #{kblnrComp,jdbcType=VARCHAR},
      </if>
      <if test="kblposComp != null">
        KBLPOS_COMP = #{kblposComp,jdbcType=VARCHAR},
      </if>
      <if test="wbsElement != null">
        WBS_ELEMENT = #{wbsElement,jdbcType=VARCHAR},
      </if>
      <if test="rfmPsstRule != null">
        RFM_PSST_RULE = #{rfmPsstRule,jdbcType=VARCHAR},
      </if>
      <if test="rfmPsstGroup != null">
        RFM_PSST_GROUP = #{rfmPsstGroup,jdbcType=VARCHAR},
      </if>
      <if test="refItem != null">
        REF_ITEM = #{refItem,jdbcType=VARCHAR},
      </if>
      <if test="sourceId != null">
        SOURCE_ID = #{sourceId,jdbcType=VARCHAR},
      </if>
      <if test="sourceKey != null">
        SOURCE_KEY = #{sourceKey,jdbcType=VARCHAR},
      </if>
      <if test="putBack != null">
        PUT_BACK = #{putBack,jdbcType=VARCHAR},
      </if>
      <if test="polId != null">
        POL_ID = #{polId,jdbcType=VARCHAR},
      </if>
      <if test="consOrder != null">
        CONS_ORDER = #{consOrder,jdbcType=VARCHAR},
      </if>
      <if test="zzpaid != null">
        ZZPAID = #{zzpaid,jdbcType=VARCHAR},
      </if>
      <if test="zzpamt != null">
        ZZPAMT = #{zzpamt,jdbcType=DECIMAL},
      </if>
      <if test="zzsign != null">
        ZZSIGN = #{zzsign,jdbcType=DECIMAL},
      </if>
      <if test="zzdate != null">
        ZZDATE = #{zzdate,jdbcType=VARCHAR},
      </if>
      <if test="zztime != null">
        ZZTIME = #{zztime,jdbcType=VARCHAR},
      </if>
      <if test="zzprice != null">
        ZZPRICE = #{zzprice,jdbcType=VARCHAR},
      </if>
      <if test="zzpeinh != null">
        ZZPEINH = #{zzpeinh,jdbcType=VARCHAR},
      </if>
      <if test="zzmwskz != null">
        ZZMWSKZ = #{zzmwskz,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.iscm.entity.SapEkpo">
    update SAP_EKPO
    set MANDT = #{mandt,jdbcType=VARCHAR},
      EBELN = #{ebeln,jdbcType=VARCHAR},
      EBELP = #{ebelp,jdbcType=VARCHAR},
      LOEKZ = #{loekz,jdbcType=VARCHAR},
      STATU = #{statu,jdbcType=VARCHAR},
      AEDAT = #{aedat,jdbcType=VARCHAR},
      TXZ01 = #{txz01,jdbcType=VARCHAR},
      MATNR = #{matnr,jdbcType=VARCHAR},
      EMATN = #{ematn,jdbcType=VARCHAR},
      BUKRS = #{bukrs,jdbcType=VARCHAR},
      WERKS = #{werks,jdbcType=VARCHAR},
      LGORT = #{lgort,jdbcType=VARCHAR},
      BEDNR = #{bednr,jdbcType=VARCHAR},
      MATKL = #{matkl,jdbcType=VARCHAR},
      INFNR = #{infnr,jdbcType=VARCHAR},
      IDNLF = #{idnlf,jdbcType=VARCHAR},
      KTMNG = #{ktmng,jdbcType=DECIMAL},
      MENGE = #{menge,jdbcType=DECIMAL},
      MEINS = #{meins,jdbcType=VARCHAR},
      BPRME = #{bprme,jdbcType=VARCHAR},
      BPUMZ = #{bpumz,jdbcType=DECIMAL},
      BPUMN = #{bpumn,jdbcType=DECIMAL},
      UMREZ = #{umrez,jdbcType=DECIMAL},
      UMREN = #{umren,jdbcType=DECIMAL},
      NETPR = #{netpr,jdbcType=DECIMAL},
      PEINH = #{peinh,jdbcType=DECIMAL},
      NETWR = #{netwr,jdbcType=DECIMAL},
      BRTWR = #{brtwr,jdbcType=DECIMAL},
      AGDAT = #{agdat,jdbcType=VARCHAR},
      WEBAZ = #{webaz,jdbcType=DECIMAL},
      MWSKZ = #{mwskz,jdbcType=VARCHAR},
      BONUS = #{bonus,jdbcType=VARCHAR},
      INSMK = #{insmk,jdbcType=VARCHAR},
      SPINF = #{spinf,jdbcType=VARCHAR},
      PRSDR = #{prsdr,jdbcType=VARCHAR},
      SCHPR = #{schpr,jdbcType=VARCHAR},
      MAHNZ = #{mahnz,jdbcType=DECIMAL},
      MAHN1 = #{mahn1,jdbcType=DECIMAL},
      MAHN2 = #{mahn2,jdbcType=DECIMAL},
      MAHN3 = #{mahn3,jdbcType=DECIMAL},
      UEBTO = #{uebto,jdbcType=DECIMAL},
      UEBTK = #{uebtk,jdbcType=VARCHAR},
      UNTTO = #{untto,jdbcType=DECIMAL},
      BWTAR = #{bwtar,jdbcType=VARCHAR},
      BWTTY = #{bwtty,jdbcType=VARCHAR},
      ABSKZ = #{abskz,jdbcType=VARCHAR},
      AGMEM = #{agmem,jdbcType=VARCHAR},
      ELIKZ = #{elikz,jdbcType=VARCHAR},
      EREKZ = #{erekz,jdbcType=VARCHAR},
      PSTYP = #{pstyp,jdbcType=VARCHAR},
      KNTTP = #{knttp,jdbcType=VARCHAR},
      KZVBR = #{kzvbr,jdbcType=VARCHAR},
      VRTKZ = #{vrtkz,jdbcType=VARCHAR},
      TWRKZ = #{twrkz,jdbcType=VARCHAR},
      WEPOS = #{wepos,jdbcType=VARCHAR},
      WEUNB = #{weunb,jdbcType=VARCHAR},
      REPOS = #{repos,jdbcType=VARCHAR},
      WEBRE = #{webre,jdbcType=VARCHAR},
      KZABS = #{kzabs,jdbcType=VARCHAR},
      LABNR = #{labnr,jdbcType=VARCHAR},
      KONNR = #{konnr,jdbcType=VARCHAR},
      KTPNR = #{ktpnr,jdbcType=VARCHAR},
      ABDAT = #{abdat,jdbcType=VARCHAR},
      ABFTZ = #{abftz,jdbcType=DECIMAL},
      ETFZ1 = #{etfz1,jdbcType=DECIMAL},
      ETFZ2 = #{etfz2,jdbcType=DECIMAL},
      KZSTU = #{kzstu,jdbcType=VARCHAR},
      NOTKZ = #{notkz,jdbcType=VARCHAR},
      LMEIN = #{lmein,jdbcType=VARCHAR},
      EVERS = #{evers,jdbcType=VARCHAR},
      ZWERT = #{zwert,jdbcType=DECIMAL},
      NAVNW = #{navnw,jdbcType=DECIMAL},
      ABMNG = #{abmng,jdbcType=DECIMAL},
      PRDAT = #{prdat,jdbcType=VARCHAR},
      BSTYP = #{bstyp,jdbcType=VARCHAR},
      EFFWR = #{effwr,jdbcType=DECIMAL},
      XOBLR = #{xoblr,jdbcType=VARCHAR},
      KUNNR = #{kunnr,jdbcType=VARCHAR},
      ADRNR = #{adrnr,jdbcType=VARCHAR},
      EKKOL = #{ekkol,jdbcType=VARCHAR},
      SKTOF = #{sktof,jdbcType=VARCHAR},
      STAFO = #{stafo,jdbcType=VARCHAR},
      PLIFZ = #{plifz,jdbcType=DECIMAL},
      NTGEW = #{ntgew,jdbcType=DECIMAL},
      GEWEI = #{gewei,jdbcType=VARCHAR},
      TXJCD = #{txjcd,jdbcType=VARCHAR},
      ETDRK = #{etdrk,jdbcType=VARCHAR},
      SOBKZ = #{sobkz,jdbcType=VARCHAR},
      ARSNR = #{arsnr,jdbcType=VARCHAR},
      ARSPS = #{arsps,jdbcType=VARCHAR},
      INSNC = #{insnc,jdbcType=VARCHAR},
      SSQSS = #{ssqss,jdbcType=VARCHAR},
      ZGTYP = #{zgtyp,jdbcType=VARCHAR},
      EAN11 = #{ean11,jdbcType=VARCHAR},
      BSTAE = #{bstae,jdbcType=VARCHAR},
      REVLV = #{revlv,jdbcType=VARCHAR},
      GEBER = #{geber,jdbcType=VARCHAR},
      FISTL = #{fistl,jdbcType=VARCHAR},
      FIPOS = #{fipos,jdbcType=VARCHAR},
      KO_GSBER = #{koGsber,jdbcType=VARCHAR},
      KO_PARGB = #{koPargb,jdbcType=VARCHAR},
      KO_PRCTR = #{koPrctr,jdbcType=VARCHAR},
      KO_PPRCTR = #{koPprctr,jdbcType=VARCHAR},
      MEPRF = #{meprf,jdbcType=VARCHAR},
      BRGEW = #{brgew,jdbcType=DECIMAL},
      VOLUM = #{volum,jdbcType=DECIMAL},
      VOLEH = #{voleh,jdbcType=VARCHAR},
      INCO1 = #{inco1,jdbcType=VARCHAR},
      INCO2 = #{inco2,jdbcType=VARCHAR},
      VORAB = #{vorab,jdbcType=VARCHAR},
      KOLIF = #{kolif,jdbcType=VARCHAR},
      LTSNR = #{ltsnr,jdbcType=VARCHAR},
      PACKNO = #{packno,jdbcType=VARCHAR},
      FPLNR = #{fplnr,jdbcType=VARCHAR},
      GNETWR = #{gnetwr,jdbcType=DECIMAL},
      STAPO = #{stapo,jdbcType=VARCHAR},
      UEBPO = #{uebpo,jdbcType=VARCHAR},
      LEWED = #{lewed,jdbcType=VARCHAR},
      EMLIF = #{emlif,jdbcType=VARCHAR},
      LBLKZ = #{lblkz,jdbcType=VARCHAR},
      SATNR = #{satnr,jdbcType=VARCHAR},
      ATTYP = #{attyp,jdbcType=VARCHAR},
      VSART = #{vsart,jdbcType=VARCHAR},
      HANDOVERLOC = #{handoverloc,jdbcType=VARCHAR},
      KANBA = #{kanba,jdbcType=VARCHAR},
      ADRN2 = #{adrn2,jdbcType=VARCHAR},
      CUOBJ = #{cuobj,jdbcType=VARCHAR},
      XERSY = #{xersy,jdbcType=VARCHAR},
      EILDT = #{eildt,jdbcType=VARCHAR},
      DRDAT = #{drdat,jdbcType=VARCHAR},
      DRUHR = #{druhr,jdbcType=VARCHAR},
      DRUNR = #{drunr,jdbcType=VARCHAR},
      AKTNR = #{aktnr,jdbcType=VARCHAR},
      ABELN = #{abeln,jdbcType=VARCHAR},
      ABELP = #{abelp,jdbcType=VARCHAR},
      ANZPU = #{anzpu,jdbcType=DECIMAL},
      PUNEI = #{punei,jdbcType=VARCHAR},
      SAISO = #{saiso,jdbcType=VARCHAR},
      SAISJ = #{saisj,jdbcType=VARCHAR},
      EBON2 = #{ebon2,jdbcType=VARCHAR},
      EBON3 = #{ebon3,jdbcType=VARCHAR},
      EBONF = #{ebonf,jdbcType=VARCHAR},
      MLMAA = #{mlmaa,jdbcType=VARCHAR},
      MHDRZ = #{mhdrz,jdbcType=DECIMAL},
      ANFNR = #{anfnr,jdbcType=VARCHAR},
      ANFPS = #{anfps,jdbcType=VARCHAR},
      KZKFG = #{kzkfg,jdbcType=VARCHAR},
      USEQU = #{usequ,jdbcType=VARCHAR},
      UMSOK = #{umsok,jdbcType=VARCHAR},
      BANFN = #{banfn,jdbcType=VARCHAR},
      BNFPO = #{bnfpo,jdbcType=VARCHAR},
      MTART = #{mtart,jdbcType=VARCHAR},
      UPTYP = #{uptyp,jdbcType=VARCHAR},
      UPVOR = #{upvor,jdbcType=VARCHAR},
      KZWI1 = #{kzwi1,jdbcType=DECIMAL},
      KZWI2 = #{kzwi2,jdbcType=DECIMAL},
      KZWI3 = #{kzwi3,jdbcType=DECIMAL},
      KZWI4 = #{kzwi4,jdbcType=DECIMAL},
      KZWI5 = #{kzwi5,jdbcType=DECIMAL},
      KZWI6 = #{kzwi6,jdbcType=DECIMAL},
      SIKGR = #{sikgr,jdbcType=VARCHAR},
      MFZHI = #{mfzhi,jdbcType=DECIMAL},
      FFZHI = #{ffzhi,jdbcType=DECIMAL},
      RETPO = #{retpo,jdbcType=VARCHAR},
      AUREL = #{aurel,jdbcType=VARCHAR},
      BSGRU = #{bsgru,jdbcType=VARCHAR},
      LFRET = #{lfret,jdbcType=VARCHAR},
      MFRGR = #{mfrgr,jdbcType=VARCHAR},
      NRFHG = #{nrfhg,jdbcType=VARCHAR},
      J_1BNBM = #{j1bnbm,jdbcType=VARCHAR},
      J_1BMATUSE = #{j1bmatuse,jdbcType=VARCHAR},
      J_1BMATORG = #{j1bmatorg,jdbcType=VARCHAR},
      J_1BOWNPRO = #{j1bownpro,jdbcType=VARCHAR},
      J_1BINDUST = #{j1bindust,jdbcType=VARCHAR},
      ABUEB = #{abueb,jdbcType=VARCHAR},
      NLABD = #{nlabd,jdbcType=VARCHAR},
      NFABD = #{nfabd,jdbcType=VARCHAR},
      KZBWS = #{kzbws,jdbcType=VARCHAR},
      BONBA = #{bonba,jdbcType=DECIMAL},
      FABKZ = #{fabkz,jdbcType=VARCHAR},
      J_1AINDXP = #{j1aindxp,jdbcType=VARCHAR},
      J_1AIDATEP = #{j1aidatep,jdbcType=VARCHAR},
      MPROF = #{mprof,jdbcType=VARCHAR},
      EGLKZ = #{eglkz,jdbcType=VARCHAR},
      KZTLF = #{kztlf,jdbcType=VARCHAR},
      KZFME = #{kzfme,jdbcType=VARCHAR},
      RDPRF = #{rdprf,jdbcType=VARCHAR},
      TECHS = #{techs,jdbcType=VARCHAR},
      CHG_SRV = #{chgSrv,jdbcType=VARCHAR},
      CHG_FPLNR = #{chgFplnr,jdbcType=VARCHAR},
      MFRPN = #{mfrpn,jdbcType=VARCHAR},
      MFRNR = #{mfrnr,jdbcType=VARCHAR},
      EMNFR = #{emnfr,jdbcType=VARCHAR},
      NOVET = #{novet,jdbcType=VARCHAR},
      AFNAM = #{afnam,jdbcType=VARCHAR},
      TZONRC = #{tzonrc,jdbcType=VARCHAR},
      IPRKZ = #{iprkz,jdbcType=VARCHAR},
      LEBRE = #{lebre,jdbcType=VARCHAR},
      BERID = #{berid,jdbcType=VARCHAR},
      XCONDITIONS = #{xconditions,jdbcType=VARCHAR},
      APOMS = #{apoms,jdbcType=VARCHAR},
      CCOMP = #{ccomp,jdbcType=VARCHAR},
      GRANT_NBR = #{grantNbr,jdbcType=VARCHAR},
      FKBER = #{fkber,jdbcType=VARCHAR},
      `STATUS` = #{status,jdbcType=VARCHAR},
      RESLO = #{reslo,jdbcType=VARCHAR},
      KBLNR = #{kblnr,jdbcType=VARCHAR},
      KBLPOS = #{kblpos,jdbcType=VARCHAR},
      WEORA = #{weora,jdbcType=VARCHAR},
      SRV_BAS_COM = #{srvBasCom,jdbcType=VARCHAR},
      PRIO_URG = #{prioUrg,jdbcType=VARCHAR},
      PRIO_REQ = #{prioReq,jdbcType=VARCHAR},
      EMPST = #{empst,jdbcType=VARCHAR},
      DIFF_INVOICE = #{diffInvoice,jdbcType=VARCHAR},
      TRMRISK_RELEVANT = #{trmriskRelevant,jdbcType=VARCHAR},
      SPE_ABGRU = #{speAbgru,jdbcType=VARCHAR},
      SPE_CRM_SO = #{speCrmSo,jdbcType=VARCHAR},
      SPE_CRM_SO_ITEM = #{speCrmSoItem,jdbcType=VARCHAR},
      SPE_CRM_REF_SO = #{speCrmRefSo,jdbcType=VARCHAR},
      SPE_CRM_REF_ITEM = #{speCrmRefItem,jdbcType=VARCHAR},
      SPE_CRM_FKREL = #{speCrmFkrel,jdbcType=VARCHAR},
      SPE_CHNG_SYS = #{speChngSys,jdbcType=VARCHAR},
      SPE_INSMK_SRC = #{speInsmkSrc,jdbcType=VARCHAR},
      SPE_CQ_CTRLTYPE = #{speCqCtrltype,jdbcType=VARCHAR},
      SPE_CQ_NOCQ = #{speCqNocq,jdbcType=VARCHAR},
      REASON_CODE = #{reasonCode,jdbcType=VARCHAR},
      CQU_SAR = #{cquSar,jdbcType=DECIMAL},
      ANZSN = #{anzsn,jdbcType=INTEGER},
      SPE_EWM_DTC = #{speEwmDtc,jdbcType=VARCHAR},
      EXLIN = #{exlin,jdbcType=VARCHAR},
      EXSNR = #{exsnr,jdbcType=VARCHAR},
      EHTYP = #{ehtyp,jdbcType=VARCHAR},
      RETPC = #{retpc,jdbcType=DECIMAL},
      DPTYP = #{dptyp,jdbcType=VARCHAR},
      DPPCT = #{dppct,jdbcType=DECIMAL},
      DPAMT = #{dpamt,jdbcType=DECIMAL},
      DPDAT = #{dpdat,jdbcType=VARCHAR},
      FLS_RSTO = #{flsRsto,jdbcType=VARCHAR},
      EXT_RFX_NUMBER = #{extRfxNumber,jdbcType=VARCHAR},
      EXT_RFX_ITEM = #{extRfxItem,jdbcType=VARCHAR},
      EXT_RFX_SYSTEM = #{extRfxSystem,jdbcType=VARCHAR},
      SRM_CONTRACT_ID = #{srmContractId,jdbcType=VARCHAR},
      SRM_CONTRACT_ITM = #{srmContractItm,jdbcType=VARCHAR},
      BLK_REASON_ID = #{blkReasonId,jdbcType=VARCHAR},
      BLK_REASON_TXT = #{blkReasonTxt,jdbcType=VARCHAR},
      ITCONS = #{itcons,jdbcType=VARCHAR},
      FIXMG = #{fixmg,jdbcType=VARCHAR},
      WABWE = #{wabwe,jdbcType=VARCHAR},
      CMPL_DLV_ITM = #{cmplDlvItm,jdbcType=VARCHAR},
      INCO2_L = #{inco2L,jdbcType=VARCHAR},
      INCO3_L = #{inco3L,jdbcType=VARCHAR},
      STAWN = #{stawn,jdbcType=VARCHAR},
      ISVCO = #{isvco,jdbcType=VARCHAR},
      GRWRT = #{grwrt,jdbcType=DECIMAL},
      SERVICEPERFORMER = #{serviceperformer,jdbcType=VARCHAR},
      PRODUCTTYPE = #{producttype,jdbcType=VARCHAR},
      REQUESTFORQUOTATION = #{requestforquotation,jdbcType=VARCHAR},
      REQUESTFORQUOTATIONITEM = #{requestforquotationitem,jdbcType=VARCHAR},
      EXTERNALREFERENCEID = #{externalreferenceid,jdbcType=VARCHAR},
      TC_AUT_DET = #{tcAutDet,jdbcType=VARCHAR},
      MANUAL_TC_REASON = #{manualTcReason,jdbcType=VARCHAR},
      FISCAL_INCENTIVE = #{fiscalIncentive,jdbcType=VARCHAR},
      TAX_SUBJECT_ST = #{taxSubjectSt,jdbcType=VARCHAR},
      FISCAL_INCENTIVE_ID = #{fiscalIncentiveId,jdbcType=VARCHAR},
      SF_TXJCD = #{sfTxjcd,jdbcType=VARCHAR},
      DUMMY_EKPO_INCL_EEW_PS = #{dummyEkpoInclEewPs,jdbcType=VARCHAR},
      EXPECTED_VALUE = #{expectedValue,jdbcType=DECIMAL},
      LIMIT_AMOUNT = #{limitAmount,jdbcType=DECIMAL},
      ENH_DATE1 = #{enhDate1,jdbcType=VARCHAR},
      ENH_DATE2 = #{enhDate2,jdbcType=VARCHAR},
      ENH_PERCENT = #{enhPercent,jdbcType=DECIMAL},
      ENH_NUMC1 = #{enhNumc1,jdbcType=VARCHAR},
      DATAAGING = #{dataaging,jdbcType=VARCHAR},
      BEV1_NEGEN_ITEM = #{bev1NegenItem,jdbcType=VARCHAR},
      BEV1_NEDEPFREE = #{bev1Nedepfree,jdbcType=VARCHAR},
      BEV1_NESTRUCCAT = #{bev1Nestruccat,jdbcType=VARCHAR},
      ADVCODE = #{advcode,jdbcType=VARCHAR},
      BUDGET_PD = #{budgetPd,jdbcType=VARCHAR},
      EXCPE = #{excpe,jdbcType=VARCHAR},
      FMFGUS_KEY = #{fmfgusKey,jdbcType=VARCHAR},
      IUID_RELEVANT = #{iuidRelevant,jdbcType=VARCHAR},
      MRPIND = #{mrpind,jdbcType=VARCHAR},
      SGT_SCAT = #{sgtScat,jdbcType=VARCHAR},
      SGT_RCAT = #{sgtRcat,jdbcType=VARCHAR},
      TMS_REF_UUID = #{tmsRefUuid,jdbcType=VARCHAR},
      WRF_CHARSTC1 = #{wrfCharstc1,jdbcType=VARCHAR},
      WRF_CHARSTC2 = #{wrfCharstc2,jdbcType=VARCHAR},
      WRF_CHARSTC3 = #{wrfCharstc3,jdbcType=VARCHAR},
      ZZZMDSQ = #{zzzmdsq,jdbcType=VARCHAR},
      ZZZMDHH = #{zzzmdhh,jdbcType=VARCHAR},
      ZZQXDJH = #{zzqxdjh,jdbcType=VARCHAR},
      ZZQXDHH = #{zzqxdhh,jdbcType=VARCHAR},
      ZZCXBJS = #{zzcxbjs,jdbcType=VARCHAR},
      ZZQTYS = #{zzqtys,jdbcType=VARCHAR},
      ZZSPCD = #{zzspcd,jdbcType=VARCHAR},
      ZZSCCJ = #{zzsccj,jdbcType=VARCHAR},
      ZZZSCPH = #{zzzscph,jdbcType=VARCHAR},
      ZZZGJJ = #{zzzgjj,jdbcType=VARCHAR},
      REFSITE = #{refsite,jdbcType=VARCHAR},
      ZAPCGK = #{zapcgk,jdbcType=VARCHAR},
      APCGK_EXTEND = #{apcgkExtend,jdbcType=VARCHAR},
      ZBAS_DATE = #{zbasDate,jdbcType=VARCHAR},
      ZADATTYP = #{zadattyp,jdbcType=VARCHAR},
      ZSTART_DAT = #{zstartDat,jdbcType=VARCHAR},
      Z_DEV = #{zDev,jdbcType=DECIMAL},
      ZINDANX = #{zindanx,jdbcType=VARCHAR},
      ZLIMIT_DAT = #{zlimitDat,jdbcType=VARCHAR},
      NUMERATOR = #{numerator,jdbcType=VARCHAR},
      HASHCAL_BDAT = #{hashcalBdat,jdbcType=VARCHAR},
      HASHCAL = #{hashcal,jdbcType=VARCHAR},
      NEGATIVE = #{negative,jdbcType=VARCHAR},
      HASHCAL_EXISTS = #{hashcalExists,jdbcType=VARCHAR},
      KNOWN_INDEX = #{knownIndex,jdbcType=VARCHAR},
      SAPMP_GPOSE = #{sapmpGpose,jdbcType=VARCHAR},
      ANGPN = #{angpn,jdbcType=VARCHAR},
      ADMOI = #{admoi,jdbcType=VARCHAR},
      ADPRI = #{adpri,jdbcType=VARCHAR},
      LPRIO = #{lprio,jdbcType=VARCHAR},
      ADACN = #{adacn,jdbcType=VARCHAR},
      AFPNR = #{afpnr,jdbcType=VARCHAR},
      BSARK = #{bsark,jdbcType=VARCHAR},
      AUDAT = #{audat,jdbcType=VARCHAR},
      ANGNR = #{angnr,jdbcType=VARCHAR},
      PNSTAT = #{pnstat,jdbcType=VARCHAR},
      ADDNS = #{addns,jdbcType=VARCHAR},
      SERRU = #{serru,jdbcType=VARCHAR},
      SERNP = #{sernp,jdbcType=VARCHAR},
      DISUB_SOBKZ = #{disubSobkz,jdbcType=VARCHAR},
      DISUB_PSPNR = #{disubPspnr,jdbcType=VARCHAR},
      DISUB_KUNNR = #{disubKunnr,jdbcType=VARCHAR},
      DISUB_VBELN = #{disubVbeln,jdbcType=VARCHAR},
      DISUB_POSNR = #{disubPosnr,jdbcType=VARCHAR},
      DISUB_OWNER = #{disubOwner,jdbcType=VARCHAR},
      FSH_SEASON_YEAR = #{fshSeasonYear,jdbcType=VARCHAR},
      FSH_SEASON = #{fshSeason,jdbcType=VARCHAR},
      FSH_COLLECTION = #{fshCollection,jdbcType=VARCHAR},
      FSH_THEME = #{fshTheme,jdbcType=VARCHAR},
      FSH_ATP_DATE = #{fshAtpDate,jdbcType=VARCHAR},
      FSH_VAS_REL = #{fshVasRel,jdbcType=VARCHAR},
      FSH_VAS_PRNT_ID = #{fshVasPrntId,jdbcType=VARCHAR},
      FSH_TRANSACTION = #{fshTransaction,jdbcType=VARCHAR},
      FSH_ITEM_GROUP = #{fshItemGroup,jdbcType=VARCHAR},
      FSH_ITEM = #{fshItem,jdbcType=VARCHAR},
      FSH_SS = #{fshSs,jdbcType=VARCHAR},
      FSH_GRID_COND_REC = #{fshGridCondRec,jdbcType=VARCHAR},
      FSH_PSM_PFM_SPLIT = #{fshPsmPfmSplit,jdbcType=VARCHAR},
      CNFM_QTY = #{cnfmQty,jdbcType=DECIMAL},
      STPAC = #{stpac,jdbcType=VARCHAR},
      LGBZO = #{lgbzo,jdbcType=VARCHAR},
      LGBZO_B = #{lgbzoB,jdbcType=VARCHAR},
      ADDRNUM = #{addrnum,jdbcType=VARCHAR},
      CONSNUM = #{consnum,jdbcType=VARCHAR},
      BORGR_MISS = #{borgrMiss,jdbcType=VARCHAR},
      DEP_ID = #{depId,jdbcType=VARCHAR},
      BELNR = #{belnr,jdbcType=VARCHAR},
      KBLPOS_CAB = #{kblposCab,jdbcType=VARCHAR},
      KBLNR_COMP = #{kblnrComp,jdbcType=VARCHAR},
      KBLPOS_COMP = #{kblposComp,jdbcType=VARCHAR},
      WBS_ELEMENT = #{wbsElement,jdbcType=VARCHAR},
      RFM_PSST_RULE = #{rfmPsstRule,jdbcType=VARCHAR},
      RFM_PSST_GROUP = #{rfmPsstGroup,jdbcType=VARCHAR},
      REF_ITEM = #{refItem,jdbcType=VARCHAR},
      SOURCE_ID = #{sourceId,jdbcType=VARCHAR},
      SOURCE_KEY = #{sourceKey,jdbcType=VARCHAR},
      PUT_BACK = #{putBack,jdbcType=VARCHAR},
      POL_ID = #{polId,jdbcType=VARCHAR},
      CONS_ORDER = #{consOrder,jdbcType=VARCHAR},
      ZZPAID = #{zzpaid,jdbcType=VARCHAR},
      ZZPAMT = #{zzpamt,jdbcType=DECIMAL},
      ZZSIGN = #{zzsign,jdbcType=DECIMAL},
      ZZDATE = #{zzdate,jdbcType=VARCHAR},
      ZZTIME = #{zztime,jdbcType=VARCHAR},
      ZZPRICE = #{zzprice,jdbcType=VARCHAR},
      ZZPEINH = #{zzpeinh,jdbcType=VARCHAR},
      ZZMWSKZ = #{zzmwskz,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>