<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.IscmParamConfigStoreInfoMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.IscmParamConfigStoreInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="param_id" jdbcType="BIGINT" property="paramId" />
    <result column="param_code" jdbcType="VARCHAR" property="paramCode" />
    <result column="platform_org_id" jdbcType="BIGINT" property="platformOrgId" />
    <result column="platform_org_name" jdbcType="VARCHAR" property="platformOrgName" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="store_name" jdbcType="VARCHAR" property="storeName" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, param_id, param_code, platform_org_id, platform_org_name, company_id, company_code, 
    company_name, store_id, store_code, store_name, status, extend, version, created_by, 
    created_name, updated_by, updated_name, gmt_create, gmt_update
  </sql>
  <select id="selectByExample" parameterType="com.cowell.iscm.entity.IscmParamConfigStoreInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from iscm_param_config_store_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from iscm_param_config_store_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from iscm_param_config_store_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.iscm.entity.IscmParamConfigStoreInfoExample">
    delete from iscm_param_config_store_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.iscm.entity.IscmParamConfigStoreInfo">
    insert into iscm_param_config_store_info (id, param_id, param_code, 
      platform_org_id, platform_org_name, company_id, 
      company_code, company_name, store_id, 
      store_code, store_name, status, 
      extend, version, created_by, 
      created_name, updated_by, updated_name, 
      gmt_create, gmt_update)
    values (#{id,jdbcType=BIGINT}, #{paramId,jdbcType=BIGINT}, #{paramCode,jdbcType=VARCHAR}, 
      #{platformOrgId,jdbcType=BIGINT}, #{platformOrgName,jdbcType=VARCHAR}, #{companyId,jdbcType=BIGINT}, 
      #{companyCode,jdbcType=VARCHAR}, #{companyName,jdbcType=VARCHAR}, #{storeId,jdbcType=BIGINT}, 
      #{storeCode,jdbcType=VARCHAR}, #{storeName,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, 
      #{extend,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER}, #{createdBy,jdbcType=BIGINT}, 
      #{createdName,jdbcType=VARCHAR}, #{updatedBy,jdbcType=BIGINT}, #{updatedName,jdbcType=VARCHAR}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.iscm.entity.IscmParamConfigStoreInfo">
    insert into iscm_param_config_store_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="paramId != null">
        param_id,
      </if>
      <if test="paramCode != null">
        param_code,
      </if>
      <if test="platformOrgId != null">
        platform_org_id,
      </if>
      <if test="platformOrgName != null">
        platform_org_name,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="companyName != null">
        company_name,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="storeCode != null">
        store_code,
      </if>
      <if test="storeName != null">
        store_name,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedName != null">
        updated_name,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="paramId != null">
        #{paramId,jdbcType=BIGINT},
      </if>
      <if test="paramCode != null">
        #{paramCode,jdbcType=VARCHAR},
      </if>
      <if test="platformOrgId != null">
        #{platformOrgId,jdbcType=BIGINT},
      </if>
      <if test="platformOrgName != null">
        #{platformOrgName,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="storeCode != null">
        #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="storeName != null">
        #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        #{updatedName,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.iscm.entity.IscmParamConfigStoreInfoExample" resultType="java.lang.Long">
    select count(*) from iscm_param_config_store_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update iscm_param_config_store_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.paramId != null">
        param_id = #{record.paramId,jdbcType=BIGINT},
      </if>
      <if test="record.paramCode != null">
        param_code = #{record.paramCode,jdbcType=VARCHAR},
      </if>
      <if test="record.platformOrgId != null">
        platform_org_id = #{record.platformOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.platformOrgName != null">
        platform_org_name = #{record.platformOrgName,jdbcType=VARCHAR},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=BIGINT},
      </if>
      <if test="record.companyCode != null">
        company_code = #{record.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.companyName != null">
        company_name = #{record.companyName,jdbcType=VARCHAR},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.storeCode != null">
        store_code = #{record.storeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.storeName != null">
        store_name = #{record.storeName,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedName != null">
        updated_name = #{record.updatedName,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update iscm_param_config_store_info
    set id = #{record.id,jdbcType=BIGINT},
      param_id = #{record.paramId,jdbcType=BIGINT},
      param_code = #{record.paramCode,jdbcType=VARCHAR},
      platform_org_id = #{record.platformOrgId,jdbcType=BIGINT},
      platform_org_name = #{record.platformOrgName,jdbcType=VARCHAR},
      company_id = #{record.companyId,jdbcType=BIGINT},
      company_code = #{record.companyCode,jdbcType=VARCHAR},
      company_name = #{record.companyName,jdbcType=VARCHAR},
      store_id = #{record.storeId,jdbcType=BIGINT},
      store_code = #{record.storeCode,jdbcType=VARCHAR},
      store_name = #{record.storeName,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=TINYINT},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_name = #{record.createdName,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_name = #{record.updatedName,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.iscm.entity.IscmParamConfigStoreInfo">
    update iscm_param_config_store_info
    <set>
      <if test="paramId != null">
        param_id = #{paramId,jdbcType=BIGINT},
      </if>
      <if test="paramCode != null">
        param_code = #{paramCode,jdbcType=VARCHAR},
      </if>
      <if test="platformOrgId != null">
        platform_org_id = #{platformOrgId,jdbcType=BIGINT},
      </if>
      <if test="platformOrgName != null">
        platform_org_name = #{platformOrgName,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="storeCode != null">
        store_code = #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="storeName != null">
        store_name = #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        updated_name = #{updatedName,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.iscm.entity.IscmParamConfigStoreInfo">
    update iscm_param_config_store_info
    set param_id = #{paramId,jdbcType=BIGINT},
      param_code = #{paramCode,jdbcType=VARCHAR},
      platform_org_id = #{platformOrgId,jdbcType=BIGINT},
      platform_org_name = #{platformOrgName,jdbcType=VARCHAR},
      company_id = #{companyId,jdbcType=BIGINT},
      company_code = #{companyCode,jdbcType=VARCHAR},
      company_name = #{companyName,jdbcType=VARCHAR},
      store_id = #{storeId,jdbcType=BIGINT},
      store_code = #{storeCode,jdbcType=VARCHAR},
      store_name = #{storeName,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_name = #{createdName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_name = #{updatedName,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>