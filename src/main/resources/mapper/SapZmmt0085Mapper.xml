<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.SapZmmt0085Mapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.SapZmmt0085">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="MANDT" jdbcType="VARCHAR" property="mandt" />
    <result column="PURREQNO" jdbcType="VARCHAR" property="purreqno" />
    <result column="BILLTYPE" jdbcType="VARCHAR" property="billtype" />
    <result column="SOURCETYPE" jdbcType="VARCHAR" property="sourcetype" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="BUKRS" jdbcType="VARCHAR" property="bukrs" />
    <result column="EKORG" jdbcType="VARCHAR" property="ekorg" />
    <result column="ZZZZQHD" jdbcType="VARCHAR" property="zzzzqhd" />
    <result column="BEDAT" jdbcType="VARCHAR" property="bedat" />
    <result column="ZCJSJ" jdbcType="VARCHAR" property="zcjsj" />
    <result column="RETURNREASON" jdbcType="VARCHAR" property="returnreason" />
    <result column="EXPECTEDDATE" jdbcType="VARCHAR" property="expecteddate" />
    <result column="NOTES" jdbcType="VARCHAR" property="notes" />
    <result column="LIFNR" jdbcType="VARCHAR" property="lifnr" />
    <result column="BUKRS_S" jdbcType="VARCHAR" property="bukrsS" />
    <result column="VKORG" jdbcType="VARCHAR" property="vkorg" />
    <result column="RESWK" jdbcType="VARCHAR" property="reswk" />
    <result column="LOGRT" jdbcType="VARCHAR" property="logrt" />
    <result column="ZZZWLMS" jdbcType="VARCHAR" property="zzzwlms" />
    <result column="ZZZZSHD" jdbcType="VARCHAR" property="zzzzshd" />
    <result column="ZZZSHKC" jdbcType="VARCHAR" property="zzzshkc" />
    <result column="ZZZUSERID" jdbcType="VARCHAR" property="zzzuserid" />
    <result column="ZZCGY" jdbcType="VARCHAR" property="zzcgy" />
    <result column="ZZDHY" jdbcType="VARCHAR" property="zzdhy" />
    <result column="EBELN" jdbcType="VARCHAR" property="ebeln" />
    <result column="ZSTATUS" jdbcType="VARCHAR" property="zstatus" />
    <result column="ZSEND" jdbcType="VARCHAR" property="zsend" />
    <result column="MESS" jdbcType="VARCHAR" property="mess" />
    <result column="ZCLOSE" jdbcType="VARCHAR" property="zclose" />
    <result column="ZCLOSE_TEXT" jdbcType="VARCHAR" property="zcloseText" />
    <result column="ZPUCH" jdbcType="VARCHAR" property="zpuch" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, MANDT, PURREQNO, BILLTYPE, SOURCETYPE, CREATOR, BUKRS, EKORG, ZZZZQHD, BEDAT, 
    ZCJSJ, RETURNREASON, EXPECTEDDATE, NOTES, LIFNR, BUKRS_S, VKORG, RESWK, LOGRT, ZZZWLMS, 
    ZZZZSHD, ZZZSHKC, ZZZUSERID, ZZCGY, ZZDHY, EBELN, ZSTATUS, ZSEND, MESS, ZCLOSE, ZCLOSE_TEXT, 
    ZPUCH
  </sql>
  <select id="selectByExample" parameterType="com.cowell.iscm.entity.SapZmmt0085Example" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from SAP_ZMMT0085
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from SAP_ZMMT0085
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from SAP_ZMMT0085
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.iscm.entity.SapZmmt0085Example">
    delete from SAP_ZMMT0085
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entity.SapZmmt0085" useGeneratedKeys="true">
    insert into SAP_ZMMT0085 (MANDT, PURREQNO, BILLTYPE, 
      SOURCETYPE, CREATOR, BUKRS, 
      EKORG, ZZZZQHD, BEDAT, 
      ZCJSJ, RETURNREASON, EXPECTEDDATE, 
      NOTES, LIFNR, BUKRS_S, 
      VKORG, RESWK, LOGRT, 
      ZZZWLMS, ZZZZSHD, ZZZSHKC, 
      ZZZUSERID, ZZCGY, ZZDHY, 
      EBELN, ZSTATUS, ZSEND, 
      MESS, ZCLOSE, ZCLOSE_TEXT, 
      ZPUCH)
    values (#{mandt,jdbcType=VARCHAR}, #{purreqno,jdbcType=VARCHAR}, #{billtype,jdbcType=VARCHAR}, 
      #{sourcetype,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, #{bukrs,jdbcType=VARCHAR}, 
      #{ekorg,jdbcType=VARCHAR}, #{zzzzqhd,jdbcType=VARCHAR}, #{bedat,jdbcType=VARCHAR}, 
      #{zcjsj,jdbcType=VARCHAR}, #{returnreason,jdbcType=VARCHAR}, #{expecteddate,jdbcType=VARCHAR}, 
      #{notes,jdbcType=VARCHAR}, #{lifnr,jdbcType=VARCHAR}, #{bukrsS,jdbcType=VARCHAR}, 
      #{vkorg,jdbcType=VARCHAR}, #{reswk,jdbcType=VARCHAR}, #{logrt,jdbcType=VARCHAR}, 
      #{zzzwlms,jdbcType=VARCHAR}, #{zzzzshd,jdbcType=VARCHAR}, #{zzzshkc,jdbcType=VARCHAR}, 
      #{zzzuserid,jdbcType=VARCHAR}, #{zzcgy,jdbcType=VARCHAR}, #{zzdhy,jdbcType=VARCHAR}, 
      #{ebeln,jdbcType=VARCHAR}, #{zstatus,jdbcType=VARCHAR}, #{zsend,jdbcType=VARCHAR}, 
      #{mess,jdbcType=VARCHAR}, #{zclose,jdbcType=VARCHAR}, #{zcloseText,jdbcType=VARCHAR}, 
      #{zpuch,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entity.SapZmmt0085" useGeneratedKeys="true">
    insert into SAP_ZMMT0085
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="mandt != null">
        MANDT,
      </if>
      <if test="purreqno != null">
        PURREQNO,
      </if>
      <if test="billtype != null">
        BILLTYPE,
      </if>
      <if test="sourcetype != null">
        SOURCETYPE,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="bukrs != null">
        BUKRS,
      </if>
      <if test="ekorg != null">
        EKORG,
      </if>
      <if test="zzzzqhd != null">
        ZZZZQHD,
      </if>
      <if test="bedat != null">
        BEDAT,
      </if>
      <if test="zcjsj != null">
        ZCJSJ,
      </if>
      <if test="returnreason != null">
        RETURNREASON,
      </if>
      <if test="expecteddate != null">
        EXPECTEDDATE,
      </if>
      <if test="notes != null">
        NOTES,
      </if>
      <if test="lifnr != null">
        LIFNR,
      </if>
      <if test="bukrsS != null">
        BUKRS_S,
      </if>
      <if test="vkorg != null">
        VKORG,
      </if>
      <if test="reswk != null">
        RESWK,
      </if>
      <if test="logrt != null">
        LOGRT,
      </if>
      <if test="zzzwlms != null">
        ZZZWLMS,
      </if>
      <if test="zzzzshd != null">
        ZZZZSHD,
      </if>
      <if test="zzzshkc != null">
        ZZZSHKC,
      </if>
      <if test="zzzuserid != null">
        ZZZUSERID,
      </if>
      <if test="zzcgy != null">
        ZZCGY,
      </if>
      <if test="zzdhy != null">
        ZZDHY,
      </if>
      <if test="ebeln != null">
        EBELN,
      </if>
      <if test="zstatus != null">
        ZSTATUS,
      </if>
      <if test="zsend != null">
        ZSEND,
      </if>
      <if test="mess != null">
        MESS,
      </if>
      <if test="zclose != null">
        ZCLOSE,
      </if>
      <if test="zcloseText != null">
        ZCLOSE_TEXT,
      </if>
      <if test="zpuch != null">
        ZPUCH,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="mandt != null">
        #{mandt,jdbcType=VARCHAR},
      </if>
      <if test="purreqno != null">
        #{purreqno,jdbcType=VARCHAR},
      </if>
      <if test="billtype != null">
        #{billtype,jdbcType=VARCHAR},
      </if>
      <if test="sourcetype != null">
        #{sourcetype,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="bukrs != null">
        #{bukrs,jdbcType=VARCHAR},
      </if>
      <if test="ekorg != null">
        #{ekorg,jdbcType=VARCHAR},
      </if>
      <if test="zzzzqhd != null">
        #{zzzzqhd,jdbcType=VARCHAR},
      </if>
      <if test="bedat != null">
        #{bedat,jdbcType=VARCHAR},
      </if>
      <if test="zcjsj != null">
        #{zcjsj,jdbcType=VARCHAR},
      </if>
      <if test="returnreason != null">
        #{returnreason,jdbcType=VARCHAR},
      </if>
      <if test="expecteddate != null">
        #{expecteddate,jdbcType=VARCHAR},
      </if>
      <if test="notes != null">
        #{notes,jdbcType=VARCHAR},
      </if>
      <if test="lifnr != null">
        #{lifnr,jdbcType=VARCHAR},
      </if>
      <if test="bukrsS != null">
        #{bukrsS,jdbcType=VARCHAR},
      </if>
      <if test="vkorg != null">
        #{vkorg,jdbcType=VARCHAR},
      </if>
      <if test="reswk != null">
        #{reswk,jdbcType=VARCHAR},
      </if>
      <if test="logrt != null">
        #{logrt,jdbcType=VARCHAR},
      </if>
      <if test="zzzwlms != null">
        #{zzzwlms,jdbcType=VARCHAR},
      </if>
      <if test="zzzzshd != null">
        #{zzzzshd,jdbcType=VARCHAR},
      </if>
      <if test="zzzshkc != null">
        #{zzzshkc,jdbcType=VARCHAR},
      </if>
      <if test="zzzuserid != null">
        #{zzzuserid,jdbcType=VARCHAR},
      </if>
      <if test="zzcgy != null">
        #{zzcgy,jdbcType=VARCHAR},
      </if>
      <if test="zzdhy != null">
        #{zzdhy,jdbcType=VARCHAR},
      </if>
      <if test="ebeln != null">
        #{ebeln,jdbcType=VARCHAR},
      </if>
      <if test="zstatus != null">
        #{zstatus,jdbcType=VARCHAR},
      </if>
      <if test="zsend != null">
        #{zsend,jdbcType=VARCHAR},
      </if>
      <if test="mess != null">
        #{mess,jdbcType=VARCHAR},
      </if>
      <if test="zclose != null">
        #{zclose,jdbcType=VARCHAR},
      </if>
      <if test="zcloseText != null">
        #{zcloseText,jdbcType=VARCHAR},
      </if>
      <if test="zpuch != null">
        #{zpuch,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.iscm.entity.SapZmmt0085Example" resultType="java.lang.Long">
    select count(*) from SAP_ZMMT0085
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update SAP_ZMMT0085
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.mandt != null">
        MANDT = #{record.mandt,jdbcType=VARCHAR},
      </if>
      <if test="record.purreqno != null">
        PURREQNO = #{record.purreqno,jdbcType=VARCHAR},
      </if>
      <if test="record.billtype != null">
        BILLTYPE = #{record.billtype,jdbcType=VARCHAR},
      </if>
      <if test="record.sourcetype != null">
        SOURCETYPE = #{record.sourcetype,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        CREATOR = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.bukrs != null">
        BUKRS = #{record.bukrs,jdbcType=VARCHAR},
      </if>
      <if test="record.ekorg != null">
        EKORG = #{record.ekorg,jdbcType=VARCHAR},
      </if>
      <if test="record.zzzzqhd != null">
        ZZZZQHD = #{record.zzzzqhd,jdbcType=VARCHAR},
      </if>
      <if test="record.bedat != null">
        BEDAT = #{record.bedat,jdbcType=VARCHAR},
      </if>
      <if test="record.zcjsj != null">
        ZCJSJ = #{record.zcjsj,jdbcType=VARCHAR},
      </if>
      <if test="record.returnreason != null">
        RETURNREASON = #{record.returnreason,jdbcType=VARCHAR},
      </if>
      <if test="record.expecteddate != null">
        EXPECTEDDATE = #{record.expecteddate,jdbcType=VARCHAR},
      </if>
      <if test="record.notes != null">
        NOTES = #{record.notes,jdbcType=VARCHAR},
      </if>
      <if test="record.lifnr != null">
        LIFNR = #{record.lifnr,jdbcType=VARCHAR},
      </if>
      <if test="record.bukrsS != null">
        BUKRS_S = #{record.bukrsS,jdbcType=VARCHAR},
      </if>
      <if test="record.vkorg != null">
        VKORG = #{record.vkorg,jdbcType=VARCHAR},
      </if>
      <if test="record.reswk != null">
        RESWK = #{record.reswk,jdbcType=VARCHAR},
      </if>
      <if test="record.logrt != null">
        LOGRT = #{record.logrt,jdbcType=VARCHAR},
      </if>
      <if test="record.zzzwlms != null">
        ZZZWLMS = #{record.zzzwlms,jdbcType=VARCHAR},
      </if>
      <if test="record.zzzzshd != null">
        ZZZZSHD = #{record.zzzzshd,jdbcType=VARCHAR},
      </if>
      <if test="record.zzzshkc != null">
        ZZZSHKC = #{record.zzzshkc,jdbcType=VARCHAR},
      </if>
      <if test="record.zzzuserid != null">
        ZZZUSERID = #{record.zzzuserid,jdbcType=VARCHAR},
      </if>
      <if test="record.zzcgy != null">
        ZZCGY = #{record.zzcgy,jdbcType=VARCHAR},
      </if>
      <if test="record.zzdhy != null">
        ZZDHY = #{record.zzdhy,jdbcType=VARCHAR},
      </if>
      <if test="record.ebeln != null">
        EBELN = #{record.ebeln,jdbcType=VARCHAR},
      </if>
      <if test="record.zstatus != null">
        ZSTATUS = #{record.zstatus,jdbcType=VARCHAR},
      </if>
      <if test="record.zsend != null">
        ZSEND = #{record.zsend,jdbcType=VARCHAR},
      </if>
      <if test="record.mess != null">
        MESS = #{record.mess,jdbcType=VARCHAR},
      </if>
      <if test="record.zclose != null">
        ZCLOSE = #{record.zclose,jdbcType=VARCHAR},
      </if>
      <if test="record.zcloseText != null">
        ZCLOSE_TEXT = #{record.zcloseText,jdbcType=VARCHAR},
      </if>
      <if test="record.zpuch != null">
        ZPUCH = #{record.zpuch,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update SAP_ZMMT0085
    set id = #{record.id,jdbcType=BIGINT},
      MANDT = #{record.mandt,jdbcType=VARCHAR},
      PURREQNO = #{record.purreqno,jdbcType=VARCHAR},
      BILLTYPE = #{record.billtype,jdbcType=VARCHAR},
      SOURCETYPE = #{record.sourcetype,jdbcType=VARCHAR},
      CREATOR = #{record.creator,jdbcType=VARCHAR},
      BUKRS = #{record.bukrs,jdbcType=VARCHAR},
      EKORG = #{record.ekorg,jdbcType=VARCHAR},
      ZZZZQHD = #{record.zzzzqhd,jdbcType=VARCHAR},
      BEDAT = #{record.bedat,jdbcType=VARCHAR},
      ZCJSJ = #{record.zcjsj,jdbcType=VARCHAR},
      RETURNREASON = #{record.returnreason,jdbcType=VARCHAR},
      EXPECTEDDATE = #{record.expecteddate,jdbcType=VARCHAR},
      NOTES = #{record.notes,jdbcType=VARCHAR},
      LIFNR = #{record.lifnr,jdbcType=VARCHAR},
      BUKRS_S = #{record.bukrsS,jdbcType=VARCHAR},
      VKORG = #{record.vkorg,jdbcType=VARCHAR},
      RESWK = #{record.reswk,jdbcType=VARCHAR},
      LOGRT = #{record.logrt,jdbcType=VARCHAR},
      ZZZWLMS = #{record.zzzwlms,jdbcType=VARCHAR},
      ZZZZSHD = #{record.zzzzshd,jdbcType=VARCHAR},
      ZZZSHKC = #{record.zzzshkc,jdbcType=VARCHAR},
      ZZZUSERID = #{record.zzzuserid,jdbcType=VARCHAR},
      ZZCGY = #{record.zzcgy,jdbcType=VARCHAR},
      ZZDHY = #{record.zzdhy,jdbcType=VARCHAR},
      EBELN = #{record.ebeln,jdbcType=VARCHAR},
      ZSTATUS = #{record.zstatus,jdbcType=VARCHAR},
      ZSEND = #{record.zsend,jdbcType=VARCHAR},
      MESS = #{record.mess,jdbcType=VARCHAR},
      ZCLOSE = #{record.zclose,jdbcType=VARCHAR},
      ZCLOSE_TEXT = #{record.zcloseText,jdbcType=VARCHAR},
      ZPUCH = #{record.zpuch,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.iscm.entity.SapZmmt0085">
    update SAP_ZMMT0085
    <set>
      <if test="mandt != null">
        MANDT = #{mandt,jdbcType=VARCHAR},
      </if>
      <if test="purreqno != null">
        PURREQNO = #{purreqno,jdbcType=VARCHAR},
      </if>
      <if test="billtype != null">
        BILLTYPE = #{billtype,jdbcType=VARCHAR},
      </if>
      <if test="sourcetype != null">
        SOURCETYPE = #{sourcetype,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="bukrs != null">
        BUKRS = #{bukrs,jdbcType=VARCHAR},
      </if>
      <if test="ekorg != null">
        EKORG = #{ekorg,jdbcType=VARCHAR},
      </if>
      <if test="zzzzqhd != null">
        ZZZZQHD = #{zzzzqhd,jdbcType=VARCHAR},
      </if>
      <if test="bedat != null">
        BEDAT = #{bedat,jdbcType=VARCHAR},
      </if>
      <if test="zcjsj != null">
        ZCJSJ = #{zcjsj,jdbcType=VARCHAR},
      </if>
      <if test="returnreason != null">
        RETURNREASON = #{returnreason,jdbcType=VARCHAR},
      </if>
      <if test="expecteddate != null">
        EXPECTEDDATE = #{expecteddate,jdbcType=VARCHAR},
      </if>
      <if test="notes != null">
        NOTES = #{notes,jdbcType=VARCHAR},
      </if>
      <if test="lifnr != null">
        LIFNR = #{lifnr,jdbcType=VARCHAR},
      </if>
      <if test="bukrsS != null">
        BUKRS_S = #{bukrsS,jdbcType=VARCHAR},
      </if>
      <if test="vkorg != null">
        VKORG = #{vkorg,jdbcType=VARCHAR},
      </if>
      <if test="reswk != null">
        RESWK = #{reswk,jdbcType=VARCHAR},
      </if>
      <if test="logrt != null">
        LOGRT = #{logrt,jdbcType=VARCHAR},
      </if>
      <if test="zzzwlms != null">
        ZZZWLMS = #{zzzwlms,jdbcType=VARCHAR},
      </if>
      <if test="zzzzshd != null">
        ZZZZSHD = #{zzzzshd,jdbcType=VARCHAR},
      </if>
      <if test="zzzshkc != null">
        ZZZSHKC = #{zzzshkc,jdbcType=VARCHAR},
      </if>
      <if test="zzzuserid != null">
        ZZZUSERID = #{zzzuserid,jdbcType=VARCHAR},
      </if>
      <if test="zzcgy != null">
        ZZCGY = #{zzcgy,jdbcType=VARCHAR},
      </if>
      <if test="zzdhy != null">
        ZZDHY = #{zzdhy,jdbcType=VARCHAR},
      </if>
      <if test="ebeln != null">
        EBELN = #{ebeln,jdbcType=VARCHAR},
      </if>
      <if test="zstatus != null">
        ZSTATUS = #{zstatus,jdbcType=VARCHAR},
      </if>
      <if test="zsend != null">
        ZSEND = #{zsend,jdbcType=VARCHAR},
      </if>
      <if test="mess != null">
        MESS = #{mess,jdbcType=VARCHAR},
      </if>
      <if test="zclose != null">
        ZCLOSE = #{zclose,jdbcType=VARCHAR},
      </if>
      <if test="zcloseText != null">
        ZCLOSE_TEXT = #{zcloseText,jdbcType=VARCHAR},
      </if>
      <if test="zpuch != null">
        ZPUCH = #{zpuch,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.iscm.entity.SapZmmt0085">
    update SAP_ZMMT0085
    set MANDT = #{mandt,jdbcType=VARCHAR},
      PURREQNO = #{purreqno,jdbcType=VARCHAR},
      BILLTYPE = #{billtype,jdbcType=VARCHAR},
      SOURCETYPE = #{sourcetype,jdbcType=VARCHAR},
      CREATOR = #{creator,jdbcType=VARCHAR},
      BUKRS = #{bukrs,jdbcType=VARCHAR},
      EKORG = #{ekorg,jdbcType=VARCHAR},
      ZZZZQHD = #{zzzzqhd,jdbcType=VARCHAR},
      BEDAT = #{bedat,jdbcType=VARCHAR},
      ZCJSJ = #{zcjsj,jdbcType=VARCHAR},
      RETURNREASON = #{returnreason,jdbcType=VARCHAR},
      EXPECTEDDATE = #{expecteddate,jdbcType=VARCHAR},
      NOTES = #{notes,jdbcType=VARCHAR},
      LIFNR = #{lifnr,jdbcType=VARCHAR},
      BUKRS_S = #{bukrsS,jdbcType=VARCHAR},
      VKORG = #{vkorg,jdbcType=VARCHAR},
      RESWK = #{reswk,jdbcType=VARCHAR},
      LOGRT = #{logrt,jdbcType=VARCHAR},
      ZZZWLMS = #{zzzwlms,jdbcType=VARCHAR},
      ZZZZSHD = #{zzzzshd,jdbcType=VARCHAR},
      ZZZSHKC = #{zzzshkc,jdbcType=VARCHAR},
      ZZZUSERID = #{zzzuserid,jdbcType=VARCHAR},
      ZZCGY = #{zzcgy,jdbcType=VARCHAR},
      ZZDHY = #{zzdhy,jdbcType=VARCHAR},
      EBELN = #{ebeln,jdbcType=VARCHAR},
      ZSTATUS = #{zstatus,jdbcType=VARCHAR},
      ZSEND = #{zsend,jdbcType=VARCHAR},
      MESS = #{mess,jdbcType=VARCHAR},
      ZCLOSE = #{zclose,jdbcType=VARCHAR},
      ZCLOSE_TEXT = #{zcloseText,jdbcType=VARCHAR},
      ZPUCH = #{zpuch,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>