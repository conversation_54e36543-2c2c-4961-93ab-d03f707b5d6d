<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.ControlTowerNonPurchaseMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.ControlTowerNonPurchase">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="param_unique_mark" jdbcType="VARCHAR" property="paramUniqueMark" />
    <result column="werks" jdbcType="VARCHAR" property="werks" />
    <result column="matnr" jdbcType="VARCHAR" property="matnr" />
    <result column="matnr_desc" jdbcType="VARCHAR" property="matnrDesc" />
    <result column="suggest_dhl" jdbcType="DECIMAL" property="suggestDhl" />
    <result column="avg_qty" jdbcType="VARCHAR" property="avgQty" />
    <result column="goods_level" jdbcType="VARCHAR" property="goodsLevel" />
    <result column="inv_upper" jdbcType="DECIMAL" property="invUpper" />
    <result column="weiqingcaigou_7" jdbcType="DECIMAL" property="weiqingcaigou7" />
    <result column="total_dc_stock" jdbcType="VARCHAR" property="totalDcStock" />
    <result column="total_dc_disable_stock" jdbcType="VARCHAR" property="totalDcDisableStock" />
    <result column="dc_stock" jdbcType="DECIMAL" property="dcStock" />
    <result column="dc_disable_stock" jdbcType="DECIMAL" property="dcDisableStock" />
    <result column="total_store_stock" jdbcType="VARCHAR" property="totalStoreStock" />
    <result column="jm_store_stock" jdbcType="DECIMAL" property="jmStoreStock" />
    <result column="lifnr" jdbcType="VARCHAR" property="lifnr" />
    <result column="qty_before_7" jdbcType="DECIMAL" property="qtyBefore7" />
    <result column="qty_before_14" jdbcType="DECIMAL" property="qtyBefore14" />
    <result column="qty_before_30" jdbcType="VARCHAR" property="qtyBefore30" />
    <result column="hb_sale_rate" jdbcType="VARCHAR" property="hbSaleRate" />
    <result column="tb_sale_rate" jdbcType="VARCHAR" property="tbSaleRate" />
    <result column="zdxmds" jdbcType="DECIMAL" property="zdxmds" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_by_id" jdbcType="BIGINT" property="createById" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_by_id" jdbcType="BIGINT" property="updateById" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, param_unique_mark, werks, matnr, matnr_desc, suggest_dhl, avg_qty, goods_level, 
    inv_upper, weiqingcaigou_7, total_dc_stock, total_dc_disable_stock, dc_stock, dc_disable_stock, 
    total_store_stock, jm_store_stock, lifnr, qty_before_7, qty_before_14, qty_before_30, 
    hb_sale_rate, tb_sale_rate, zdxmds, `status`, gmt_create, gmt_update, create_by, 
    create_by_id, update_by, update_by_id, version, extend
  </sql>
  <select id="selectByExample" parameterType="com.cowell.iscm.entity.ControlTowerNonPurchaseExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from control_tower_non_purchase
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from control_tower_non_purchase
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from control_tower_non_purchase
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.iscm.entity.ControlTowerNonPurchaseExample">
    delete from control_tower_non_purchase
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entity.ControlTowerNonPurchase" useGeneratedKeys="true">
    insert into control_tower_non_purchase (param_unique_mark, werks, matnr, 
      matnr_desc, suggest_dhl, avg_qty, 
      goods_level, inv_upper, weiqingcaigou_7, 
      total_dc_stock, total_dc_disable_stock, dc_stock, 
      dc_disable_stock, total_store_stock, jm_store_stock, 
      lifnr, qty_before_7, qty_before_14, 
      qty_before_30, hb_sale_rate, tb_sale_rate, 
      zdxmds, `status`, gmt_create, 
      gmt_update, create_by, create_by_id, 
      update_by, update_by_id, version, 
      extend)
    values (#{paramUniqueMark,jdbcType=VARCHAR}, #{werks,jdbcType=VARCHAR}, #{matnr,jdbcType=VARCHAR}, 
      #{matnrDesc,jdbcType=VARCHAR}, #{suggestDhl,jdbcType=DECIMAL}, #{avgQty,jdbcType=VARCHAR}, 
      #{goodsLevel,jdbcType=VARCHAR}, #{invUpper,jdbcType=DECIMAL}, #{weiqingcaigou7,jdbcType=DECIMAL}, 
      #{totalDcStock,jdbcType=VARCHAR}, #{totalDcDisableStock,jdbcType=VARCHAR}, #{dcStock,jdbcType=DECIMAL}, 
      #{dcDisableStock,jdbcType=DECIMAL}, #{totalStoreStock,jdbcType=VARCHAR}, #{jmStoreStock,jdbcType=DECIMAL}, 
      #{lifnr,jdbcType=VARCHAR}, #{qtyBefore7,jdbcType=DECIMAL}, #{qtyBefore14,jdbcType=DECIMAL}, 
      #{qtyBefore30,jdbcType=VARCHAR}, #{hbSaleRate,jdbcType=VARCHAR}, #{tbSaleRate,jdbcType=VARCHAR}, 
      #{zdxmds,jdbcType=DECIMAL}, #{status,jdbcType=INTEGER}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtUpdate,jdbcType=TIMESTAMP}, #{createBy,jdbcType=VARCHAR}, #{createById,jdbcType=BIGINT}, 
      #{updateBy,jdbcType=VARCHAR}, #{updateById,jdbcType=BIGINT}, #{version,jdbcType=INTEGER}, 
      #{extend,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entity.ControlTowerNonPurchase" useGeneratedKeys="true">
    insert into control_tower_non_purchase
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="paramUniqueMark != null">
        param_unique_mark,
      </if>
      <if test="werks != null">
        werks,
      </if>
      <if test="matnr != null">
        matnr,
      </if>
      <if test="matnrDesc != null">
        matnr_desc,
      </if>
      <if test="suggestDhl != null">
        suggest_dhl,
      </if>
      <if test="avgQty != null">
        avg_qty,
      </if>
      <if test="goodsLevel != null">
        goods_level,
      </if>
      <if test="invUpper != null">
        inv_upper,
      </if>
      <if test="weiqingcaigou7 != null">
        weiqingcaigou_7,
      </if>
      <if test="totalDcStock != null">
        total_dc_stock,
      </if>
      <if test="totalDcDisableStock != null">
        total_dc_disable_stock,
      </if>
      <if test="dcStock != null">
        dc_stock,
      </if>
      <if test="dcDisableStock != null">
        dc_disable_stock,
      </if>
      <if test="totalStoreStock != null">
        total_store_stock,
      </if>
      <if test="jmStoreStock != null">
        jm_store_stock,
      </if>
      <if test="lifnr != null">
        lifnr,
      </if>
      <if test="qtyBefore7 != null">
        qty_before_7,
      </if>
      <if test="qtyBefore14 != null">
        qty_before_14,
      </if>
      <if test="qtyBefore30 != null">
        qty_before_30,
      </if>
      <if test="hbSaleRate != null">
        hb_sale_rate,
      </if>
      <if test="tbSaleRate != null">
        tb_sale_rate,
      </if>
      <if test="zdxmds != null">
        zdxmds,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createById != null">
        create_by_id,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateById != null">
        update_by_id,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="extend != null">
        extend,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="paramUniqueMark != null">
        #{paramUniqueMark,jdbcType=VARCHAR},
      </if>
      <if test="werks != null">
        #{werks,jdbcType=VARCHAR},
      </if>
      <if test="matnr != null">
        #{matnr,jdbcType=VARCHAR},
      </if>
      <if test="matnrDesc != null">
        #{matnrDesc,jdbcType=VARCHAR},
      </if>
      <if test="suggestDhl != null">
        #{suggestDhl,jdbcType=DECIMAL},
      </if>
      <if test="avgQty != null">
        #{avgQty,jdbcType=VARCHAR},
      </if>
      <if test="goodsLevel != null">
        #{goodsLevel,jdbcType=VARCHAR},
      </if>
      <if test="invUpper != null">
        #{invUpper,jdbcType=DECIMAL},
      </if>
      <if test="weiqingcaigou7 != null">
        #{weiqingcaigou7,jdbcType=DECIMAL},
      </if>
      <if test="totalDcStock != null">
        #{totalDcStock,jdbcType=VARCHAR},
      </if>
      <if test="totalDcDisableStock != null">
        #{totalDcDisableStock,jdbcType=VARCHAR},
      </if>
      <if test="dcStock != null">
        #{dcStock,jdbcType=DECIMAL},
      </if>
      <if test="dcDisableStock != null">
        #{dcDisableStock,jdbcType=DECIMAL},
      </if>
      <if test="totalStoreStock != null">
        #{totalStoreStock,jdbcType=VARCHAR},
      </if>
      <if test="jmStoreStock != null">
        #{jmStoreStock,jdbcType=DECIMAL},
      </if>
      <if test="lifnr != null">
        #{lifnr,jdbcType=VARCHAR},
      </if>
      <if test="qtyBefore7 != null">
        #{qtyBefore7,jdbcType=DECIMAL},
      </if>
      <if test="qtyBefore14 != null">
        #{qtyBefore14,jdbcType=DECIMAL},
      </if>
      <if test="qtyBefore30 != null">
        #{qtyBefore30,jdbcType=VARCHAR},
      </if>
      <if test="hbSaleRate != null">
        #{hbSaleRate,jdbcType=VARCHAR},
      </if>
      <if test="tbSaleRate != null">
        #{tbSaleRate,jdbcType=VARCHAR},
      </if>
      <if test="zdxmds != null">
        #{zdxmds,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createById != null">
        #{createById,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateById != null">
        #{updateById,jdbcType=BIGINT},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.iscm.entity.ControlTowerNonPurchaseExample" resultType="java.lang.Long">
    select count(*) from control_tower_non_purchase
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update control_tower_non_purchase
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.paramUniqueMark != null">
        param_unique_mark = #{record.paramUniqueMark,jdbcType=VARCHAR},
      </if>
      <if test="record.werks != null">
        werks = #{record.werks,jdbcType=VARCHAR},
      </if>
      <if test="record.matnr != null">
        matnr = #{record.matnr,jdbcType=VARCHAR},
      </if>
      <if test="record.matnrDesc != null">
        matnr_desc = #{record.matnrDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.suggestDhl != null">
        suggest_dhl = #{record.suggestDhl,jdbcType=DECIMAL},
      </if>
      <if test="record.avgQty != null">
        avg_qty = #{record.avgQty,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsLevel != null">
        goods_level = #{record.goodsLevel,jdbcType=VARCHAR},
      </if>
      <if test="record.invUpper != null">
        inv_upper = #{record.invUpper,jdbcType=DECIMAL},
      </if>
      <if test="record.weiqingcaigou7 != null">
        weiqingcaigou_7 = #{record.weiqingcaigou7,jdbcType=DECIMAL},
      </if>
      <if test="record.totalDcStock != null">
        total_dc_stock = #{record.totalDcStock,jdbcType=VARCHAR},
      </if>
      <if test="record.totalDcDisableStock != null">
        total_dc_disable_stock = #{record.totalDcDisableStock,jdbcType=VARCHAR},
      </if>
      <if test="record.dcStock != null">
        dc_stock = #{record.dcStock,jdbcType=DECIMAL},
      </if>
      <if test="record.dcDisableStock != null">
        dc_disable_stock = #{record.dcDisableStock,jdbcType=DECIMAL},
      </if>
      <if test="record.totalStoreStock != null">
        total_store_stock = #{record.totalStoreStock,jdbcType=VARCHAR},
      </if>
      <if test="record.jmStoreStock != null">
        jm_store_stock = #{record.jmStoreStock,jdbcType=DECIMAL},
      </if>
      <if test="record.lifnr != null">
        lifnr = #{record.lifnr,jdbcType=VARCHAR},
      </if>
      <if test="record.qtyBefore7 != null">
        qty_before_7 = #{record.qtyBefore7,jdbcType=DECIMAL},
      </if>
      <if test="record.qtyBefore14 != null">
        qty_before_14 = #{record.qtyBefore14,jdbcType=DECIMAL},
      </if>
      <if test="record.qtyBefore30 != null">
        qty_before_30 = #{record.qtyBefore30,jdbcType=VARCHAR},
      </if>
      <if test="record.hbSaleRate != null">
        hb_sale_rate = #{record.hbSaleRate,jdbcType=VARCHAR},
      </if>
      <if test="record.tbSaleRate != null">
        tb_sale_rate = #{record.tbSaleRate,jdbcType=VARCHAR},
      </if>
      <if test="record.zdxmds != null">
        zdxmds = #{record.zdxmds,jdbcType=DECIMAL},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createBy != null">
        create_by = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createById != null">
        create_by_id = #{record.createById,jdbcType=BIGINT},
      </if>
      <if test="record.updateBy != null">
        update_by = #{record.updateBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updateById != null">
        update_by_id = #{record.updateById,jdbcType=BIGINT},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update control_tower_non_purchase
    set id = #{record.id,jdbcType=BIGINT},
      param_unique_mark = #{record.paramUniqueMark,jdbcType=VARCHAR},
      werks = #{record.werks,jdbcType=VARCHAR},
      matnr = #{record.matnr,jdbcType=VARCHAR},
      matnr_desc = #{record.matnrDesc,jdbcType=VARCHAR},
      suggest_dhl = #{record.suggestDhl,jdbcType=DECIMAL},
      avg_qty = #{record.avgQty,jdbcType=VARCHAR},
      goods_level = #{record.goodsLevel,jdbcType=VARCHAR},
      inv_upper = #{record.invUpper,jdbcType=DECIMAL},
      weiqingcaigou_7 = #{record.weiqingcaigou7,jdbcType=DECIMAL},
      total_dc_stock = #{record.totalDcStock,jdbcType=VARCHAR},
      total_dc_disable_stock = #{record.totalDcDisableStock,jdbcType=VARCHAR},
      dc_stock = #{record.dcStock,jdbcType=DECIMAL},
      dc_disable_stock = #{record.dcDisableStock,jdbcType=DECIMAL},
      total_store_stock = #{record.totalStoreStock,jdbcType=VARCHAR},
      jm_store_stock = #{record.jmStoreStock,jdbcType=DECIMAL},
      lifnr = #{record.lifnr,jdbcType=VARCHAR},
      qty_before_7 = #{record.qtyBefore7,jdbcType=DECIMAL},
      qty_before_14 = #{record.qtyBefore14,jdbcType=DECIMAL},
      qty_before_30 = #{record.qtyBefore30,jdbcType=VARCHAR},
      hb_sale_rate = #{record.hbSaleRate,jdbcType=VARCHAR},
      tb_sale_rate = #{record.tbSaleRate,jdbcType=VARCHAR},
      zdxmds = #{record.zdxmds,jdbcType=DECIMAL},
      `status` = #{record.status,jdbcType=INTEGER},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      create_by = #{record.createBy,jdbcType=VARCHAR},
      create_by_id = #{record.createById,jdbcType=BIGINT},
      update_by = #{record.updateBy,jdbcType=VARCHAR},
      update_by_id = #{record.updateById,jdbcType=BIGINT},
      version = #{record.version,jdbcType=INTEGER},
      extend = #{record.extend,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.iscm.entity.ControlTowerNonPurchase">
    update control_tower_non_purchase
    <set>
      <if test="paramUniqueMark != null">
        param_unique_mark = #{paramUniqueMark,jdbcType=VARCHAR},
      </if>
      <if test="werks != null">
        werks = #{werks,jdbcType=VARCHAR},
      </if>
      <if test="matnr != null">
        matnr = #{matnr,jdbcType=VARCHAR},
      </if>
      <if test="matnrDesc != null">
        matnr_desc = #{matnrDesc,jdbcType=VARCHAR},
      </if>
      <if test="suggestDhl != null">
        suggest_dhl = #{suggestDhl,jdbcType=DECIMAL},
      </if>
      <if test="avgQty != null">
        avg_qty = #{avgQty,jdbcType=VARCHAR},
      </if>
      <if test="goodsLevel != null">
        goods_level = #{goodsLevel,jdbcType=VARCHAR},
      </if>
      <if test="invUpper != null">
        inv_upper = #{invUpper,jdbcType=DECIMAL},
      </if>
      <if test="weiqingcaigou7 != null">
        weiqingcaigou_7 = #{weiqingcaigou7,jdbcType=DECIMAL},
      </if>
      <if test="totalDcStock != null">
        total_dc_stock = #{totalDcStock,jdbcType=VARCHAR},
      </if>
      <if test="totalDcDisableStock != null">
        total_dc_disable_stock = #{totalDcDisableStock,jdbcType=VARCHAR},
      </if>
      <if test="dcStock != null">
        dc_stock = #{dcStock,jdbcType=DECIMAL},
      </if>
      <if test="dcDisableStock != null">
        dc_disable_stock = #{dcDisableStock,jdbcType=DECIMAL},
      </if>
      <if test="totalStoreStock != null">
        total_store_stock = #{totalStoreStock,jdbcType=VARCHAR},
      </if>
      <if test="jmStoreStock != null">
        jm_store_stock = #{jmStoreStock,jdbcType=DECIMAL},
      </if>
      <if test="lifnr != null">
        lifnr = #{lifnr,jdbcType=VARCHAR},
      </if>
      <if test="qtyBefore7 != null">
        qty_before_7 = #{qtyBefore7,jdbcType=DECIMAL},
      </if>
      <if test="qtyBefore14 != null">
        qty_before_14 = #{qtyBefore14,jdbcType=DECIMAL},
      </if>
      <if test="qtyBefore30 != null">
        qty_before_30 = #{qtyBefore30,jdbcType=VARCHAR},
      </if>
      <if test="hbSaleRate != null">
        hb_sale_rate = #{hbSaleRate,jdbcType=VARCHAR},
      </if>
      <if test="tbSaleRate != null">
        tb_sale_rate = #{tbSaleRate,jdbcType=VARCHAR},
      </if>
      <if test="zdxmds != null">
        zdxmds = #{zdxmds,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createById != null">
        create_by_id = #{createById,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateById != null">
        update_by_id = #{updateById,jdbcType=BIGINT},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.iscm.entity.ControlTowerNonPurchase">
    update control_tower_non_purchase
    set param_unique_mark = #{paramUniqueMark,jdbcType=VARCHAR},
      werks = #{werks,jdbcType=VARCHAR},
      matnr = #{matnr,jdbcType=VARCHAR},
      matnr_desc = #{matnrDesc,jdbcType=VARCHAR},
      suggest_dhl = #{suggestDhl,jdbcType=DECIMAL},
      avg_qty = #{avgQty,jdbcType=VARCHAR},
      goods_level = #{goodsLevel,jdbcType=VARCHAR},
      inv_upper = #{invUpper,jdbcType=DECIMAL},
      weiqingcaigou_7 = #{weiqingcaigou7,jdbcType=DECIMAL},
      total_dc_stock = #{totalDcStock,jdbcType=VARCHAR},
      total_dc_disable_stock = #{totalDcDisableStock,jdbcType=VARCHAR},
      dc_stock = #{dcStock,jdbcType=DECIMAL},
      dc_disable_stock = #{dcDisableStock,jdbcType=DECIMAL},
      total_store_stock = #{totalStoreStock,jdbcType=VARCHAR},
      jm_store_stock = #{jmStoreStock,jdbcType=DECIMAL},
      lifnr = #{lifnr,jdbcType=VARCHAR},
      qty_before_7 = #{qtyBefore7,jdbcType=DECIMAL},
      qty_before_14 = #{qtyBefore14,jdbcType=DECIMAL},
      qty_before_30 = #{qtyBefore30,jdbcType=VARCHAR},
      hb_sale_rate = #{hbSaleRate,jdbcType=VARCHAR},
      tb_sale_rate = #{tbSaleRate,jdbcType=VARCHAR},
      zdxmds = #{zdxmds,jdbcType=DECIMAL},
      `status` = #{status,jdbcType=INTEGER},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      create_by = #{createBy,jdbcType=VARCHAR},
      create_by_id = #{createById,jdbcType=BIGINT},
      update_by = #{updateBy,jdbcType=VARCHAR},
      update_by_id = #{updateById,jdbcType=BIGINT},
      version = #{version,jdbcType=INTEGER},
      extend = #{extend,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>