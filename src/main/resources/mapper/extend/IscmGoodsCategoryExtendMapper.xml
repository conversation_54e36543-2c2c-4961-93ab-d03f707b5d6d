<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.extend.IscmGoodsCategoryExtendMapper">

  <delete id="deleteAllData">
    delete from iscm_goods_category where 1 = 1
  </delete>



  <insert id="batchInsert" parameterType="com.cowell.iscm.entity.IscmGoodsCategory">
    insert into iscm_goods_category (
      id, category_name, parent_id, category_level, sort, path, description, sort_value, features, source,
      status, extend, created_by, created_name, updated_by, updated_name, gmt_create, gmt_update
    )
    values
    <foreach collection="list" item="item" index="index" separator="," >
      ( #{item.id,jdbcType=BIGINT}, #{item.categoryName,jdbcType=VARCHAR}, #{item.parentId,jdbcType=BIGINT}, #{item.categoryLevel,jdbcType=TINYINT}, #{item.sort,jdbcType=TINYINT},
        #{item.path,jdbcType=VARCHAR}, #{item.description,jdbcType=VARCHAR}, #{item.sortValue,jdbcType=TINYINT}, #{item.features,jdbcType=VARCHAR}, #{item.source,jdbcType=TINYINT},
        #{item.status,jdbcType=TINYINT}, #{item.extend,jdbcType=VARCHAR}, #{item.createdBy,jdbcType=BIGINT}, #{item.createdName,jdbcType=VARCHAR}, #{item.updatedBy,jdbcType=BIGINT},
        #{item.updatedName,jdbcType=VARCHAR}, #{item.gmtCreate,jdbcType=TIMESTAMP}, #{item.gmtUpdate,jdbcType=TIMESTAMP}
      )
    </foreach>
  </insert>

</mapper>