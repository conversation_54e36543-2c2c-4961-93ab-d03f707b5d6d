<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.extend.IscmSeasonalGoodsCompanyExtendMapper">
    <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.IscmSeasonalGoodsCompany">
        <id column="Id" jdbcType="BIGINT" property="id"/>
        <result column="season_name" jdbcType="VARCHAR" property="seasonName"/>
        <result column="season_start_md" jdbcType="VARCHAR" property="seasonStartMd"/>
        <result column="season_end_md" jdbcType="VARCHAR" property="seasonEndMd"/>
        <result column="stock_start" jdbcType="VARCHAR" property="stockStart"/>
        <result column="stock_end" jdbcType="VARCHAR" property="stockEnd"/>
        <result column="start_date" jdbcType="DATE" property="startDate"/>
        <result column="end_date" jdbcType="DATE" property="endDate"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate"/>
        <result column="extend" jdbcType="VARCHAR" property="extend"/>
        <result column="version" jdbcType="INTEGER" property="version"/>
        <result column="created_by" jdbcType="BIGINT" property="createdBy"/>
        <result column="created_name" jdbcType="VARCHAR" property="createdName"/>
        <result column="updated_by" jdbcType="BIGINT" property="updatedBy"/>
        <result column="updated_name" jdbcType="VARCHAR" property="updatedName"/>
        <result column="goods_no" jdbcType="VARCHAR" property="goodsNo"/>
        <result column="goods_name" jdbcType="VARCHAR" property="goodsName"/>
        <result column="goods_unit" jdbcType="VARCHAR" property="goodsUnit"/>
        <result column="specifications" jdbcType="VARCHAR" property="specifications"/>
        <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer"/>
        <result column="ingredients" jdbcType="VARCHAR" property="ingredients"/>
        <result column="push_flag" jdbcType="TINYINT" property="pushFlag"/>
        <result column="company_org_id" jdbcType="BIGINT" property="companyOrgId"/>
        <result column="company_name" jdbcType="VARCHAR" property="companyName"/>
        <result column="company_sapcode" jdbcType="VARCHAR" property="companySapcode"/>
        <result column="goods_class_max_id" jdbcType="BIGINT" property="goodsClassMaxId"/>
        <result column="goods_class_mid_id" jdbcType="BIGINT" property="goodsClassMidId"/>
        <result column="goods_class_min_id" jdbcType="BIGINT" property="goodsClassMinId"/>
        <result column="goods_class_max_name" jdbcType="VARCHAR" property="goodsClassMaxName"/>
        <result column="goods_class_mid_name" jdbcType="VARCHAR" property="goodsClassMidName"/>
        <result column="goods_class_min_name" jdbcType="VARCHAR" property="goodsClassMinName"/>
        <result column="core_flag" jdbcType="TINYINT" property="coreFlag"/>
        <result column="display_flag" jdbcType="TINYINT" property="displayFlag"/>
    </resultMap>
    <sql id="Base_Column_List">
        Id, season_name, season_start_md, season_end_md, stock_start, stock_end, start_date,
        end_date, `status`, gmt_create, gmt_update, extend, version, created_by, created_name,
        updated_by, updated_name, goods_no, goods_name, goods_unit, specifications, manufacturer,
        ingredients, push_flag, company_org_id, company_name, company_sapcode, goods_class_max_id,
        goods_class_mid_id, goods_class_min_id, goods_class_max_name, goods_class_mid_name,
        goods_class_min_name, core_flag, display_flag
    </sql>
    
    <select id="countPushGoods" resultType="java.lang.Integer">
        select count(id)
        from iscm_seasonal_goods_company where display_flag=1
        and start_date &lt;= #{date}
        and end_date &gt;= #{date}
    </select>
    
    <select id="queryPushGoodsListByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from iscm_seasonal_goods_company where display_flag=1
        and start_date &lt;= #{date}
        and end_date &gt;= #{date}
        limit #{offset},#{pageSize}
    </select>

</mapper>