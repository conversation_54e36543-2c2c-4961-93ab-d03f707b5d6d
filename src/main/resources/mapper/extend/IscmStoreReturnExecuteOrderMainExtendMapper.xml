<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.extend.IscmStoreReturnExecuteOrderMainExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.IscmStoreReturnExecuteOrderMain">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="return_type" jdbcType="INTEGER" property="returnType" />
    <result column="return_order_no" jdbcType="VARCHAR" property="returnOrderNo" />
    <result column="pos_return_order_no" jdbcType="VARCHAR" property="posReturnOrderNo" />
    <result column="platform_org_id" jdbcType="BIGINT" property="platformOrgId" />
    <result column="platform_org_name" jdbcType="VARCHAR" property="platformOrgName" />
    <result column="company_org_id" jdbcType="BIGINT" property="companyOrgId" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="store_org_id" jdbcType="BIGINT" property="storeOrgId" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="store_name" jdbcType="VARCHAR" property="storeName" />
    <result column="warehouse_code" jdbcType="VARCHAR" property="warehouseCode" />
    <result column="warehouse_name" jdbcType="VARCHAR" property="warehouseName" />
    <result column="return_goods_quantity_total" jdbcType="INTEGER" property="returnGoodsQuantityTotal" />
    <result column="return_quantity_total" jdbcType="DECIMAL" property="returnQuantityTotal" />
    <result column="return_cost_amount_total" jdbcType="DECIMAL" property="returnCostAmountTotal" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
    <result column="issue_by" jdbcType="BIGINT" property="issueBy" />
    <result column="issue_name" jdbcType="VARCHAR" property="issueName" />
    <result column="gmt_issue" jdbcType="TIMESTAMP" property="gmtIssue" />
    <result column="issue_validity_days" jdbcType="INTEGER" property="issueValidityDays" />
    <result column="issue_return_quantity_total" jdbcType="DECIMAL" property="issueReturnQuantityTotal" />
    <result column="issue_return_amount_total" jdbcType="DECIMAL" property="issueReturnAmountTotal" />
    <result column="real_return_quantity_total" jdbcType="DECIMAL" property="realReturnQuantityTotal" />
    <result column="process_status" jdbcType="TINYINT" property="processStatus" />
    <result column="process_status_all" jdbcType="VARCHAR" property="processStatusAll" />
    <result column="created_month" jdbcType="INTEGER" property="createdMonth" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, return_type, return_order_no, pos_return_order_no, platform_org_id, platform_org_name,
    company_org_id, company_code, company_name, store_org_id, store_code, store_name,
    warehouse_code, warehouse_name, return_goods_quantity_total, return_quantity_total,
    return_cost_amount_total, `status`, extend, version, gmt_create, gmt_update, created_by,
    created_name, updated_by, updated_name, issue_by, issue_name, gmt_issue, issue_validity_days,
    issue_return_quantity_total, issue_return_amount_total, real_return_quantity_total,
    process_status, process_status_all, created_month
  </sql>

  <update id="batchUpdate">
    update iscm_store_return_execute_order_main
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="return_goods_quantity_total=case" suffix="end,">
        <foreach collection="list" item="item" index="index">
          WHEN id=#{item.id,jdbcType=BIGINT} then #{item.returnGoodsQuantityTotal}
        </foreach>
      </trim>
      <trim prefix="return_quantity_total=case" suffix="end,">
        <foreach collection="list" item="item" index="index">
          WHEN id=#{item.id,jdbcType=BIGINT} then #{item.returnQuantityTotal}
        </foreach>
      </trim>
      <trim prefix="return_cost_amount_total=case" suffix="end,">
        <foreach collection="list" item="item" index="index">
          WHEN id=#{item.id,jdbcType=BIGINT} then #{item.returnCostAmountTotal}
        </foreach>
      </trim>
      <trim prefix="issue_return_amount_total=case" suffix="end,">
        <foreach collection="list" item="item" index="index">
          WHEN id=#{item.id,jdbcType=BIGINT} then #{item.issueReturnAmountTotal}
        </foreach>
      </trim>
      <trim prefix="issue_return_quantity_total=case" suffix="end,">
        <foreach collection="list" item="item" index="index">
          WHEN id=#{item.id,jdbcType=BIGINT} then #{item.issueReturnQuantityTotal}
        </foreach>
      </trim>
      <trim prefix="real_return_quantity_total=case" suffix="end,">
        <foreach collection="list" item="item" index="index">
          WHEN id=#{item.id,jdbcType=BIGINT} then #{item.realReturnQuantityTotal}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
      #{item.id,jdbcType=BIGINT}
    </foreach>
    and created_month = #{createdMonth}
  </update>

  <insert id="batchInsert" parameterType="java.util.List">
    insert into iscm_store_return_execute_order_main (return_type, return_order_no, pos_return_order_no,
      platform_org_id, platform_org_name, company_org_id, 
      company_code, company_name, store_org_id, 
      store_code, store_name, warehouse_code, 
      warehouse_name, return_goods_quantity_total, 
      return_quantity_total, return_cost_amount_total, 
      created_by,
      created_name, updated_by, updated_name, 
      issue_by, issue_name, gmt_issue, 
      issue_validity_days, issue_return_quantity_total, 
      issue_return_amount_total, real_return_quantity_total, 
      process_status, created_month)
    values
    <foreach collection="list" item="item" index="index" separator="," >
    (#{item.returnType,jdbcType=INTEGER}, #{item.returnOrderNo,jdbcType=VARCHAR}, #{item.posReturnOrderNo,jdbcType=VARCHAR},
      #{item.platformOrgId,jdbcType=BIGINT}, #{item.platformOrgName,jdbcType=VARCHAR}, #{item.companyOrgId,jdbcType=BIGINT},
      #{item.companyCode,jdbcType=VARCHAR}, #{item.companyName,jdbcType=VARCHAR}, #{item.storeOrgId,jdbcType=BIGINT},
      #{item.storeCode,jdbcType=VARCHAR}, #{item.storeName,jdbcType=VARCHAR}, #{item.warehouseCode,jdbcType=VARCHAR},
      #{item.warehouseName,jdbcType=VARCHAR}, #{item.returnGoodsQuantityTotal,jdbcType=INTEGER},
      #{item.returnQuantityTotal,jdbcType=DECIMAL}, #{item.returnCostAmountTotal,jdbcType=DECIMAL},
      #{item.createdBy,jdbcType=BIGINT},
      #{item.createdName,jdbcType=VARCHAR}, #{item.updatedBy,jdbcType=BIGINT}, #{item.updatedName,jdbcType=VARCHAR},
      #{item.issueBy,jdbcType=BIGINT}, #{item.issueName,jdbcType=VARCHAR}, #{item.gmtIssue,jdbcType=TIMESTAMP},
      #{item.issueValidityDays,jdbcType=INTEGER}, #{item.issueReturnQuantityTotal,jdbcType=DECIMAL},
      #{item.issueReturnAmountTotal,jdbcType=DECIMAL}, #{item.realReturnQuantityTotal,jdbcType=DECIMAL},
      #{item.processStatus,jdbcType=TINYINT}, #{item.createdMonth,jdbcType=INTEGER})
    </foreach>
  </insert>

  <update id="updateProcessStatusAll">
    update iscm_store_return_execute_order_main set process_status_all = concat('|', process_status, '|')
    where created_month = #{month}
    and id in
    <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
      #{id}
    </foreach>
  </update>
</mapper>
