<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.extend.IscmGoodsRegisterOrderDetailExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.IscmGoodsRegisterOrderDetail">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="register_order_no" jdbcType="VARCHAR" property="registerOrderNo" />
    <result column="register_type" jdbcType="TINYINT" property="registerType" />
    <result column="register_source" jdbcType="TINYINT" property="registerSource" />
    <result column="data_type" jdbcType="TINYINT" property="dataType" />
    <result column="suggest_status" jdbcType="TINYINT" property="suggestStatus" />
    <result column="platform_org_id" jdbcType="BIGINT" property="platformOrgId" />
    <result column="platform_org_name" jdbcType="VARCHAR" property="platformOrgName" />
    <result column="company_org_id" jdbcType="BIGINT" property="companyOrgId" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="store_org_id" jdbcType="BIGINT" property="storeOrgId" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="store_attr" jdbcType="VARCHAR" property="storeAttr" />
    <result column="allot_group_code" jdbcType="VARCHAR" property="allotGroupCode" />
    <result column="allot_group_name" jdbcType="VARCHAR" property="allotGroupName" />
    <result column="register_month" jdbcType="INTEGER" property="registerMonth" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="bar_code" jdbcType="VARCHAR" property="barCode" />
    <result column="goods_common_name" jdbcType="VARCHAR" property="goodsCommonName" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="goods_unit" jdbcType="VARCHAR" property="goodsUnit" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="specifications" jdbcType="VARCHAR" property="specifications" />
    <result column="dosage_form" jdbcType="VARCHAR" property="dosageForm" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="approval_number" jdbcType="VARCHAR" property="approvalNumber" />
    <result column="habitat" jdbcType="VARCHAR" property="habitat" />
    <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
    <result column="validity_date" jdbcType="TIMESTAMP" property="validityDate" />
    <result column="produce_date" jdbcType="TIMESTAMP" property="produceDate" />
    <result column="warehouse_code" jdbcType="VARCHAR" property="warehouseCode" />
    <result column="warehouse_name" jdbcType="VARCHAR" property="warehouseName" />
    <result column="goods_class_id" jdbcType="BIGINT" property="goodsClassId" />
    <result column="goods_class_name" jdbcType="VARCHAR" property="goodsClassName" />
    <result column="goods_pur_channel" jdbcType="VARCHAR" property="goodsPurChannel" />
    <result column="register_quantity" jdbcType="DECIMAL" property="registerQuantity" />
    <result column="stock_quantity" jdbcType="DECIMAL" property="stockQuantity" />
    <result column="stock_upper_limit" jdbcType="DECIMAL" property="stockUpperLimit" />
    <result column="stock_lower_limit" jdbcType="DECIMAL" property="stockLowerLimit" />
    <result column="non_sale_days" jdbcType="INTEGER" property="nonSaleDays" />
    <result column="expect_sale_days" jdbcType="DECIMAL" property="expectSaleDays" />
    <result column="min_display_quantity" jdbcType="DECIMAL" property="minDisplayQuantity" />
    <result column="non_validity_stock_quantity" jdbcType="DECIMAL" property="nonValidityStockQuantity" />
    <result column="cost_amount" jdbcType="DECIMAL" property="costAmount" />
    <result column="deal_status" jdbcType="TINYINT" property="dealStatus" />
    <result column="order_status" jdbcType="TINYINT" property="orderStatus" />
    <result column="return_warehouse_status" jdbcType="TINYINT" property="returnWarehouseStatus" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, register_order_no, register_type, register_source, data_type, suggest_status,
    platform_org_id, platform_org_name, company_org_id, company_code, store_org_id, store_code,
    store_attr, allot_group_code, allot_group_name, register_month, goods_no, bar_code,
    goods_common_name, goods_name, goods_unit, description, specifications, dosage_form,
    manufacturer, approval_number, habitat, batch_no, validity_date, produce_date, warehouse_code,
    warehouse_name, goods_class_id, goods_class_name, goods_pur_channel, register_quantity,
    stock_quantity, stock_upper_limit, stock_lower_limit, non_sale_days, expect_sale_days,
    min_display_quantity, non_validity_stock_quantity, cost_amount, deal_status, order_status,
    return_warehouse_status, `status`, gmt_create, gmt_update, extend, version, created_by,
    created_name, updated_by, updated_name
  </sql>
  <insert id="batchInsert" parameterType="com.cowell.iscm.entity.IscmGoodsRegisterOrderDetail">
    insert into iscm_goods_register_order_detail (register_order_no, register_type, register_source, data_type, suggest_status,
      platform_org_id, platform_org_name, company_org_id, company_code, store_org_id, store_code, store_attr, allot_group_code, allot_group_name, register_month, goods_no,
      bar_code, goods_common_name, goods_name, goods_unit,
      description, specifications, dosage_form, 
      manufacturer, approval_number, habitat, 
      batch_no, validity_date, produce_date, warehouse_code, warehouse_name, goods_class_id, goods_class_name, goods_pur_channel,
      register_quantity, stock_quantity, stock_upper_limit, 
      stock_lower_limit, non_sale_days, expect_sale_days, min_display_quantity, non_validity_stock_quantity, cost_amount, deal_status,
      created_by,
      created_name, updated_by, updated_name
      )
    values
    <foreach collection="list" item="item" index="index" separator="," >
    (#{item.registerOrderNo,jdbcType=VARCHAR}, #{item.registerType,jdbcType=TINYINT}, #{item.registerSource,jdbcType=TINYINT}, #{item.dataType,jdbcType=TINYINT}, #{item.suggestStatus,jdbcType=TINYINT},
      #{item.platformOrgId,jdbcType=BIGINT}, #{item.platformOrgName,jdbcType=VARCHAR}, #{item.companyOrgId,jdbcType=BIGINT}, #{item.companyCode,jdbcType=VARCHAR}, #{item.storeOrgId,jdbcType=BIGINT}, #{item.storeCode,jdbcType=VARCHAR}, #{item.storeAttr,jdbcType=VARCHAR}, #{item.allotGroupCode,jdbcType=VARCHAR}, #{item.allotGroupName,jdbcType=VARCHAR}, #{item.registerMonth,jdbcType=INTEGER}, #{item.goodsNo,jdbcType=VARCHAR},
      #{item.barCode,jdbcType=VARCHAR}, #{item.goodsCommonName,jdbcType=VARCHAR}, #{item.goodsName,jdbcType=VARCHAR}, #{item.goodsUnit,jdbcType=VARCHAR},
      #{item.description,jdbcType=VARCHAR}, #{item.specifications,jdbcType=VARCHAR}, #{item.dosageForm,jdbcType=VARCHAR},
      #{item.manufacturer,jdbcType=VARCHAR}, #{item.approvalNumber,jdbcType=VARCHAR}, #{item.habitat,jdbcType=VARCHAR},
      #{item.batchNo,jdbcType=VARCHAR}, #{item.validityDate,jdbcType=TIMESTAMP}, #{item.produceDate,jdbcType=TIMESTAMP}, #{item.warehouseCode,jdbcType=VARCHAR}, #{item.warehouseName,jdbcType=VARCHAR}, #{item.goodsClassId,jdbcType=BIGINT}, #{item.goodsClassName,jdbcType=VARCHAR}, #{item.goodsPurChannel,jdbcType=VARCHAR},
      #{item.registerQuantity,jdbcType=DECIMAL}, #{item.stockQuantity,jdbcType=DECIMAL}, #{item.stockUpperLimit,jdbcType=DECIMAL},
      #{item.stockLowerLimit,jdbcType=DECIMAL}, #{item.nonSaleDays,jdbcType=INTEGER}, #{item.expectSaleDays,jdbcType=DECIMAL}, #{item.minDisplayQuantity,jdbcType=DECIMAL}, #{item.nonValidityStockQuantity,jdbcType=DECIMAL}, #{item.costAmount,jdbcType=DECIMAL}, #{item.dealStatus,jdbcType=TINYINT},
      #{item.createdBy,jdbcType=BIGINT},
      #{item.createdName,jdbcType=VARCHAR}, #{item.updatedBy,jdbcType=BIGINT}, #{item.updatedName,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>
    <select id="findGoodsNoByStoreCodeByTypeByDate" resultType="java.lang.String">
      select goods_no
      from iscm_goods_register_order_detail
      where register_month = #{registerMonth}
      and store_code = #{storeCode,jdbcType=VARCHAR}
      and register_type = #{registerType,jdbcType=TINYINT}
      and gmt_create between #{startDate} and #{endDate} and order_status in (1,2)
    </select>
  <select id="findGoodsNoBatchNoByStoreCodeByTypeByDate"
          resultType="com.cowell.iscm.service.dto.registerOrder.GoodsBatchVO">
    select goods_no as goodsNo, batch_no as batchNo
      from iscm_goods_register_order_detail
      where register_month = #{registerMonth}
      and store_code = #{storeCode,jdbcType=VARCHAR}
      and register_type = #{registerType,jdbcType=TINYINT}
      and gmt_create between #{startDate} and #{endDate}  and order_status in (1,2)
    group by goods_no, batch_no
  </select>

    <select id="countByRegisterOrderNos" resultType="java.lang.Long">
      select count(*) from iscm_goods_register_order_detail
      where register_order_no in
      <foreach collection="registerOrderNos" item="registerOrderNo" index="index" open="(" close=")" separator="," >
            #{registerOrderNo}
      </foreach>
      and register_month = #{registerMonth}
    </select>

  <select id="countByRegisterOrderRegisterQuantity" resultType="com.cowell.iscm.service.dto.registerOrder.RegisterOrderDetailCountDTO">
    select register_order_no as registerOrderNo, sum(register_quantity) as registerQuantity
    from  iscm_goods_register_order_detail
    where register_order_no in
    <foreach collection="registerOrderNos" item="registerOrderNo" index="index" open="(" close=")" separator="," >
      #{registerOrderNo}
    </foreach>
    and register_month = #{registerMonth}
    group by register_order_no;
  </select>


    <update id="batchUpdateOrderStatus">
    update iscm_goods_register_order_detail
    set order_status = #{orderStatus}
    where id in
    <foreach collection="detailIds" index="index" item="detailId" separator="," open="(" close=")">
      #{detailId}
    </foreach>
    and register_month = #{registerMonth}
  </update>


  <update id="updateByIdAndRegisterMonth" parameterType="com.cowell.iscm.entity.IscmGoodsRegisterOrderDetail">
    update iscm_goods_register_order_detail
    <set>
      <if test="registerOrderNo != null">
        register_order_no = #{registerOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="registerType != null">
        register_type = #{registerType,jdbcType=TINYINT},
      </if>
      <if test="registerSource != null">
        register_source = #{registerSource,jdbcType=TINYINT},
      </if>
      <if test="dataType != null">
        data_type = #{dataType,jdbcType=TINYINT},
      </if>
      <if test="suggestStatus != null">
        suggest_status = #{suggestStatus,jdbcType=TINYINT},
      </if>
      <if test="platformOrgId != null">
        platform_org_id = #{platformOrgId,jdbcType=BIGINT},
      </if>
      <if test="platformOrgName != null">
        platform_org_name = #{platformOrgName,jdbcType=VARCHAR},
      </if>
      <if test="companyOrgId != null">
        company_org_id = #{companyOrgId,jdbcType=BIGINT},
      </if>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="storeOrgId != null">
        store_org_id = #{storeOrgId,jdbcType=BIGINT},
      </if>
      <if test="storeCode != null">
        store_code = #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="registerMonth != null">
        register_month = #{registerMonth,jdbcType=INTEGER},
      </if>
      <if test="goodsNo != null">
        goods_no = #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="barCode != null">
        bar_code = #{barCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsCommonName != null">
        goods_common_name = #{goodsCommonName,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        goods_name = #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="goodsUnit != null">
        goods_unit = #{goodsUnit,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="specifications != null">
        specifications = #{specifications,jdbcType=VARCHAR},
      </if>
      <if test="dosageForm != null">
        dosage_form = #{dosageForm,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        manufacturer = #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="approvalNumber != null">
        approval_number = #{approvalNumber,jdbcType=VARCHAR},
      </if>
      <if test="habitat != null">
        habitat = #{habitat,jdbcType=VARCHAR},
      </if>
      <if test="batchNo != null">
        batch_no = #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="validityDate != null">
        validity_date = #{validityDate,jdbcType=TIMESTAMP},
      </if>
      <if test="produceDate != null">
        produce_date = #{produceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="warehouseCode != null">
        warehouse_code = #{warehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="warehouseName != null">
        warehouse_name = #{warehouseName,jdbcType=VARCHAR},
      </if>
      <if test="goodsClassId != null">
        goods_class_id = #{goodsClassId,jdbcType=BIGINT},
      </if>
      <if test="goodsClassName != null">
        goods_class_name = #{goodsClassName,jdbcType=VARCHAR},
      </if>
      <if test="goodsPurChannel != null">
        #{goodsPurChannel,jdbcType=VARCHAR},
      </if>
      <if test="registerQuantity != null">
        register_quantity = #{registerQuantity,jdbcType=DECIMAL},
      </if>
      <if test="stockQuantity != null">
        stock_quantity = #{stockQuantity,jdbcType=DECIMAL},
      </if>
      <if test="stockUpperLimit != null">
        stock_upper_limit = #{stockUpperLimit,jdbcType=DECIMAL},
      </if>
      <if test="stockLowerLimit != null">
        stock_lower_limit = #{stockLowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="nonSaleDays != null">
        non_sale_days = #{nonSaleDays,jdbcType=INTEGER},
      </if>
      <if test="expectSaleDays != null">
        expect_sale_days = #{expectSaleDays,jdbcType=DECIMAL},
      </if>
      <if test="minDisplayQuantity != null">
        min_display_quantity = #{minDisplayQuantity,jdbcType=DECIMAL},
      </if>
      <if test="nonValidityStockQuantity != null">
        non_validity_stock_quantity = #{nonValidityStockQuantity,jdbcType=DECIMAL},
      </if>
      <if test="costAmount != null">
        cost_amount = #{costAmount,jdbcType=DECIMAL},
      </if>
      <if test="dealStatus != null">
        deal_status = #{dealStatus,jdbcType=TINYINT},
      </if>
      <if test="orderStatus != null">
        order_status = #{orderStatus,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        updated_name = #{updatedName,jdbcType=VARCHAR},
      </if>
      <if test="noSuggestReason != null">
        no_suggest_reason = #{noSuggestReason,jdbcType=VARCHAR},
      </if>
      <if test="warehouseCode != null">
        warehouse_code = #{warehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="warehouseName != null">
        warehouse_name = #{warehouseName,jdbcType=VARCHAR},
      </if>
      <if test="goodsClassId != null">
        goods_class_id = #{goodsClassId,jdbcType=BIGINT},
      </if>
      <if test="goodsClassName != null">
        goods_class_name = #{goodsClassName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT} and register_month = #{registerMonth,jdbcType=INTEGER}
  </update>

  <update id="updateWarehouseByStoreCodeAndRegisterMonth">
    update iscm_goods_register_order_detail
    <!-- <trim prefix="set" suffixOverrides=",">
      <trim prefix="warehouse_code=case" suffix="end,">
        <foreach collection="warehouseRelations" item="item" index="index">
          WHEN store_code=#{item.storeCode,jdbcType=VARCHAR} then #{item.warehouseCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="warehouse_name=case" suffix="end,">
        <foreach collection="warehouseRelations" item="item" index="index">
          WHEN store_code=#{item.storeCode,jdbcType=VARCHAR} then #{item.warehouseName,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    , gmt_update = gmt_update -->
    set warehouse_code = #{storeWarehouseRelationDTO.warehouseCode}, warehouse_name = #{storeWarehouseRelationDTO.warehouseName}, gmt_update = gmt_update
    where register_month = #{registerMonth}
    and store_code = #{storeWarehouseRelationDTO.storeCode}
    <!-- and store_code in
    <foreach collection="warehouseRelations" index="index" item="item" separator="," open="(" close=")">
      #{item.storeCode,jdbcType=VARCHAR}
    </foreach> -->
  </update>
  <update id="updateGoodsClassByCompanyOrgIdAndGoodsNoAndRegisterMonth">
    update iscm_goods_register_order_detail
    <!-- <trim prefix="set" suffixOverrides=",">
      <trim prefix="goods_class_id=case" suffix="end,">
        <foreach collection="goodsClasses" item="item" index="index">
          WHEN company_org_id=#{item.companyOrgId,jdbcType=BIGINT} and goods_no=#{item.goodsNo,jdbcType=VARCHAR} then #{item.goodsClassId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="goods_class_name=case" suffix="end,">
        <foreach collection="goodsClasses" item="item" index="index">
          WHEN company_org_id=#{item.companyOrgId,jdbcType=BIGINT} and goods_no=#{item.goodsNo,jdbcType=VARCHAR} then #{item.goodsClassName,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    , gmt_update = gmt_update -->
    set goods_class_id = #{goodsClassDTO.goodsClassId}, goods_class_name = #{goodsClassDTO.goodsClassName} ,gmt_update = gmt_update
    where register_month = #{registerMonth,jdbcType=INTEGER}
    and goods_no = #{goodsClassDTO.goodsNo}
    and company_org_id = #{goodsClassDTO.companyOrgId}
<!--    and goods_no in-->
<!--    <foreach collection="goodsClasses" index="index" item="item" separator="," open="(" close=")">-->
<!--      #{item.goodsNo,jdbcType=VARCHAR}-->
<!--    </foreach>-->
<!--    and company_org_id in-->
<!--    <foreach collection="goodsClasses" index="index" item="item" separator="," open="(" close=")">-->
<!--      #{item.companyOrgId,jdbcType=BIGINT}-->
<!--    </foreach>-->
  </update>
  <update id="updatePlatform">
    update iscm_goods_register_order_detail
    set platform_org_id = #{platformOrgId}, platform_org_name = #{platformOrgName},gmt_update = gmt_update
    where register_month = #{registerMonth} and company_org_id = #{companyOrgId}
  </update>
  <update id="updateReturnWarehouseStatus">
    update iscm_goods_register_order_detail set return_warehouse_status = #{returnWarehouseStatus} ,gmt_update = gmt_update
    where register_month = #{registerMonth,jdbcType=INTEGER}
    and goods_no = #{goodsNo}
    and warehouse_code = #{warehouseCode}
    and store_org_id in
    <foreach collection="storeOrgIds" item="storeOrgId" index="index" open="(" close=")" separator="," >
      #{storeOrgId}
    </foreach>
  </update>
  <update id="updatePurChannel">
    update iscm_goods_register_order_detail set goods_pur_channel = #{goodsPurChannel} ,gmt_update = gmt_update
     where register_month = #{registerMonth,jdbcType=INTEGER}
    and goods_no = #{goodsNo}
    and company_org_id = #{companyOrgId}
  </update>
  <update id="updateReturnWarehouseStatusByRegisterNo">
    update iscm_goods_register_order_detail set return_warehouse_status = #{returnWarehouseStatus} ,gmt_update = gmt_update
    where register_month = #{registerMonth,jdbcType=INTEGER}
    and register_order_no in
    <foreach collection="registerOrderNoList" item="registerOrderNo" index="index" open="(" close=")" separator="," >
      #{registerOrderNo}
    </foreach>
  </update>

  <select id="findNoWarehouseFillStoreNosByReturnDay" resultType="java.lang.String">
    select store_code from iscm_goods_register_order_detail
    where register_month = #{registerMonth,jdbcType=INTEGER}
    and (warehouse_code is null or warehouse_code = '')
    and gmt_create between #{startDate} and #{endDate}
    and register_type = 2
  </select>
  <select id="findNoGoodsClassFillStoreNosByReturnDay" resultType="com.cowell.iscm.service.dto.returnWarehouse.GoodsClassDTO">
    select company_org_id as companyOrgId, goods_no as goodsNo  from iscm_goods_register_order_detail
    where register_month = #{registerMonth,jdbcType=INTEGER}
    and goods_class_id = 0
    and gmt_create between #{startDate} and #{endDate}
    and register_type = 2
    group by company_org_id, goods_no
  </select>
  <select id="selectWarehouseInfoByReturnDay"
          resultType="com.cowell.iscm.service.dto.returnWarehouse.WarehouseDTO">
    select warehouse_code as warehouseCode, warehouse_name as warehouseName
    from iscm_goods_register_order_detail
        where register_month = #{registerMonth,jdbcType=INTEGER}
        and platform_org_id = #{platformOrgId,jdbcType=BIGINT}
    <!-- and gmt_create between #{startDate} and #{endDate} -->
    and register_type = 2
    <!-- and (warehouse_code is not null or warehouse_code != '') -->
    and return_warehouse_status = 0
    group by warehouse_code
  </select>
  <select id="countStoreReturnWarehouseConfirmListByExample" parameterType="com.cowell.iscm.entity.IscmGoodsRegisterOrderDetailExample" resultType="java.lang.Long">
    select count(*) from (select
    1
    from iscm_goods_register_order_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause"/>
    </if>
    group by platform_org_id, warehouse_code, goods_no
    ) as a
  </select>
  <select id="getStoreReturnWarehouseConfirmListByExample" parameterType="com.cowell.iscm.entity.IscmGoodsRegisterOrderDetailExample" resultType="com.cowell.iscm.service.dto.returnWarehouse.StoreReturnWarehouseConfirmDTO">
    select
    platform_org_id as platformOrgId, platform_org_name as platformOrgName, warehouse_code as warehouseCode, warehouse_name as warehouseName, goods_no as goodsNo, bar_code as barCode, goods_common_name as goodsCommonName,
    goods_name as goodsName, goods_unit as goodsUnit, description, specifications, dosage_form as dosageForm, manufacturer, approval_number as approvalNumber, count(distinct company_org_id) as companyQuantity, count(distinct store_org_id) as storeQuantity,
    sum(register_quantity) as totalRegisterQuantity, sum(cost_amount) as totalCostAmount, max(gmt_create) as timeUpper, min(gmt_create) as timeLower, group_concat(distinct company_code) as companyCodeStr
    from iscm_goods_register_order_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <!-- test start -->
     <!-- and id in (
     select max(id) from iscm_goods_register_order_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    group by goods_no, store_code
    ) -->
    <!-- test end -->
    group by platform_org_id, warehouse_code, goods_no
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectCompanyOrgIdsByCompanyOrgIdAndGoodsNoAndRegisterMonth" resultType="java.lang.Long">
    select company_org_id  from iscm_goods_register_order_detail
    where register_month = #{registerMonth,jdbcType=INTEGER}
    and gmt_create between #{startDate} and #{endDate}
    group by company_org_id
  </select>
  <select id="selectWillFillChannel" resultType="com.cowell.iscm.service.dto.returnWarehouse.FIllChannelDTO">
    select company_org_id as companyOrgId, goods_no as goodsNo
    from iscm_goods_register_order_detail
    where register_month = #{registerMonth,jdbcType=INTEGER}
    and gmt_create between #{startDate} and #{endDate}
    group by company_org_id, goods_no
  </select>
  <select id="getSimpleStoreReturnWarehouseConfirmListByExample"
          resultType="com.cowell.iscm.service.dto.returnWarehouse.StoreReturnWarehouseConfirmDTO">
    select
    platform_org_id as platformOrgId, warehouse_code as warehouseCode, warehouse_name as warehouseName, goods_no as goodsNo, group_concat(distinct company_code) as companyCodeStr
    from iscm_goods_register_order_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    group by platform_org_id, warehouse_code, goods_no
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>

  </select>

</mapper>
