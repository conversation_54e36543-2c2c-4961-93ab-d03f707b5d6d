<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.extend.IscmMultiVersionModelPermExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.IscmMultiVersionModelPerm">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="model_id" jdbcType="BIGINT" property="modelId" />
    <result column="model_code" jdbcType="VARCHAR" property="modelCode" />
    <result column="model_level" jdbcType="INTEGER" property="modelLevel" />
    <result column="org_id" jdbcType="BIGINT" property="orgId" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="sap_code" jdbcType="VARCHAR" property="sapCode" />
    <result column="bdp_code" jdbcType="VARCHAR" property="bdpCode" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
  </resultMap>
  <sql id="Base_Column_List">
    id, model_id, model_code, model_level, org_id, org_name, sap_code, bdp_code, status, 
    extend, version, created_by, created_name, updated_by, updated_name, gmt_create, 
    gmt_update
  </sql>
  <insert id="batchInsert" parameterType="java.util.List">
    insert into iscm_multi_version_model_perm (model_id, model_code,
      model_level, org_id, org_name, 
      sap_code, bdp_code, status, 
      created_by,
      created_name, updated_by, updated_name)
    values
    <foreach collection="list" item="item" index="index" separator="," >
    (#{item.modelId,jdbcType=BIGINT}, #{item.modelCode,jdbcType=VARCHAR},
      #{item.modelLevel,jdbcType=INTEGER}, #{item.orgId,jdbcType=BIGINT}, #{item.orgName,jdbcType=VARCHAR},
      #{item.sapCode,jdbcType=VARCHAR}, #{item.bdpCode,jdbcType=VARCHAR}, #{item.status,jdbcType=TINYINT},
      #{item.createdBy,jdbcType=BIGINT},
      #{item.createdName,jdbcType=VARCHAR}, #{item.updatedBy,jdbcType=BIGINT}, #{item.updatedName,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>
</mapper>
