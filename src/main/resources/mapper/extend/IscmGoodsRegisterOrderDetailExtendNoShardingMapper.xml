<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.extend.IscmGoodsRegisterOrderDetailExtendNoShardingMapper">
    <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.IscmGoodsRegisterOrderDetail">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="register_order_no" jdbcType="VARCHAR" property="registerOrderNo" />
        <result column="register_type" jdbcType="TINYINT" property="registerType" />
        <result column="register_source" jdbcType="TINYINT" property="registerSource" />
        <result column="data_type" jdbcType="TINYINT" property="dataType" />
        <result column="suggest_status" jdbcType="TINYINT" property="suggestStatus" />
        <result column="platform_org_id" jdbcType="BIGINT" property="platformOrgId" />
        <result column="platform_org_name" jdbcType="VARCHAR" property="platformOrgName" />
        <result column="company_org_id" jdbcType="BIGINT" property="companyOrgId" />
        <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
        <result column="store_org_id" jdbcType="BIGINT" property="storeOrgId" />
        <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
        <result column="allot_group_code" jdbcType="VARCHAR" property="allotGroupCode" />
        <result column="allot_group_name" jdbcType="VARCHAR" property="allotGroupName" />
        <result column="register_month" jdbcType="INTEGER" property="registerMonth" />
        <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
        <result column="bar_code" jdbcType="VARCHAR" property="barCode" />
        <result column="goods_common_name" jdbcType="VARCHAR" property="goodsCommonName" />
        <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
        <result column="goods_unit" jdbcType="VARCHAR" property="goodsUnit" />
        <result column="description" jdbcType="VARCHAR" property="description" />
        <result column="specifications" jdbcType="VARCHAR" property="specifications" />
        <result column="dosage_form" jdbcType="VARCHAR" property="dosageForm" />
        <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
        <result column="approval_number" jdbcType="VARCHAR" property="approvalNumber" />
        <result column="habitat" jdbcType="VARCHAR" property="habitat" />
        <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
        <result column="validity_date" jdbcType="TIMESTAMP" property="validityDate" />
        <result column="produce_date" jdbcType="TIMESTAMP" property="produceDate" />
        <result column="warehouse_code" jdbcType="VARCHAR" property="warehouseCode" />
        <result column="warehouse_name" jdbcType="VARCHAR" property="warehouseName" />
        <result column="goods_class_id" jdbcType="BIGINT" property="goodsClassId" />
        <result column="goods_class_name" jdbcType="VARCHAR" property="goodsClassName" />
        <result column="goods_pur_channel" jdbcType="VARCHAR" property="goodsPurChannel" />
        <result column="register_quantity" jdbcType="DECIMAL" property="registerQuantity" />
        <result column="stock_quantity" jdbcType="DECIMAL" property="stockQuantity" />
        <result column="stock_upper_limit" jdbcType="DECIMAL" property="stockUpperLimit" />
        <result column="stock_lower_limit" jdbcType="DECIMAL" property="stockLowerLimit" />
        <result column="non_sale_days" jdbcType="INTEGER" property="nonSaleDays" />
        <result column="expect_sale_days" jdbcType="DECIMAL" property="expectSaleDays" />
        <result column="min_display_quantity" jdbcType="DECIMAL" property="minDisplayQuantity" />
        <result column="non_validity_stock_quantity" jdbcType="DECIMAL" property="nonValidityStockQuantity" />
        <result column="cost_amount" jdbcType="DECIMAL" property="costAmount" />
        <result column="deal_status" jdbcType="TINYINT" property="dealStatus" />
        <result column="order_status" jdbcType="TINYINT" property="orderStatus" />
        <result column="return_warehouse_status" jdbcType="TINYINT" property="returnWarehouseStatus" />
        <result column="status" jdbcType="TINYINT" property="status" />
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
        <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
        <result column="extend" jdbcType="VARCHAR" property="extend" />
        <result column="version" jdbcType="INTEGER" property="version" />
        <result column="created_by" jdbcType="BIGINT" property="createdBy" />
        <result column="created_name" jdbcType="VARCHAR" property="createdName" />
        <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
        <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="" prefixOverrides="and" suffix="">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
    id, register_order_no, register_type, register_source, data_type, suggest_status,
    platform_org_id, platform_org_name, company_org_id, company_code, store_org_id, store_code,
    allot_group_code, allot_group_name, register_month, goods_no, bar_code, goods_common_name,
    goods_name, goods_unit, description, specifications, dosage_form, manufacturer, approval_number,
    habitat, batch_no, validity_date, produce_date, warehouse_code, warehouse_name, goods_class_id,
    goods_class_name, goods_pur_channel, register_quantity, stock_quantity, stock_upper_limit,
    stock_lower_limit, non_sale_days, expect_sale_days, min_display_quantity, non_validity_stock_quantity,
    cost_amount, deal_status, order_status, return_warehouse_status, `status`, gmt_create,
    gmt_update, extend, version, created_by, created_name, updated_by, updated_name
  </sql>
    <select id="getStoreReturnWarehouseConfirmListByExample" parameterType="com.cowell.iscm.entity.IscmGoodsRegisterOrderDetailExample" resultType="com.cowell.iscm.service.dto.returnWarehouse.StoreReturnWarehouseConfirmDTO">
        select
        platform_org_id as platformOrgId, platform_org_name as platformOrgName, warehouse_code as warehouseCode, warehouse_name as warehouseName, goods_no as goodsNo, bar_code as barCode, goods_common_name as goodsCommonName,
        goods_name as goodsName, goods_unit as goodsUnit, description, specifications, dosage_form as dosageForm, manufacturer, approval_number as approvalNumber, count(distinct company_org_id) as companyQuantity, count(distinct store_org_id) as storeQuantity,
        sum(register_quantity) as totalRegisterQuantity, sum(cost_amount) as totalCostAmount, max(gmt_create) as timeUpper, min(gmt_create) as timeLower
        from ${table}
        <if test="_parameter != null">
            <include refid="Example_Where_Clause" />
        </if>
        <!-- test start -->
        and id in (
        select max(id) from ${table}
       <if test="_parameter != null">
         <include refid="Example_Where_Clause" />
       </if>
       group by goods_no, store_code
       )
        <!-- test end -->
        group by platform_org_id, warehouse_code, goods_no
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        <if test="limit != null">
            <if test="offset != null">
                limit ${offset}, ${limit}
            </if>
            <if test="offset == null">
                limit ${limit}
            </if>
        </if>
    </select>

</mapper>
