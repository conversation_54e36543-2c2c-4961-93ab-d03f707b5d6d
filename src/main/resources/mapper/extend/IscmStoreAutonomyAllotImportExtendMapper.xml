<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.extend.IscmStoreAutonomyAllotImportExtendMapper">


    <insert id="batchInsert" parameterType="java.util.List">
        insert into iscm_store_autonomy_allot_import (allot_type,  out_store_code, out_store_name,
        in_store_code, in_store_name, goods_no,
        goods_name, allot_quantity, batch_no,
        check_status, error_msg, `status`, created_by,
        created_name, updated_by, updated_name)
        values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.allotType,jdbcType=TINYINT},  #{item.outStoreCode,jdbcType=VARCHAR},  #{item.outStoreName,jdbcType=VARCHAR},
            #{item.inStoreCode,jdbcType=VARCHAR}, #{item.inStoreName,jdbcType=VARCHAR}, #{item.goodsNo,jdbcType=VARCHAR},
            #{item.goodsName,jdbcType=VARCHAR}, #{item.allotQuantity,jdbcType=VARCHAR}, #{item.batchNo,jdbcType=VARCHAR},
            #{item.checkStatus,jdbcType=TINYINT}, #{item.errorMsg,jdbcType=VARCHAR}, #{item.status,jdbcType=TINYINT},
            #{item.createdBy,jdbcType=BIGINT}, #{item.createdName,jdbcType=VARCHAR}, #{item.updatedBy,jdbcType=BIGINT}, #{item.updatedName,jdbcType=VARCHAR})
        </foreach>
    </insert>

</mapper>