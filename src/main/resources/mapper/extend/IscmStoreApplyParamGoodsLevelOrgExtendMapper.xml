<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.extend.IscmStoreApplyParamGoodsLevelOrgExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.IscmStoreApplyParamGoodsLevelOrg">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="param_code" jdbcType="VARCHAR" property="paramCode" />
    <result column="param_name" jdbcType="VARCHAR" property="paramName" />
    <result column="goods_level" jdbcType="TINYINT" property="goodsLevel" />
    <result column="goods_level_name" jdbcType="VARCHAR" property="goodsLevelName" />
    <result column="param_level" jdbcType="INTEGER" property="paramLevel" />
    <result column="org_id" jdbcType="BIGINT" property="orgId" />
    <result column="sap_code" jdbcType="VARCHAR" property="sapCode" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="parent_org_id" jdbcType="BIGINT" property="parentOrgId" />
    <result column="parent_org_name" jdbcType="VARCHAR" property="parentOrgName" />
    <result column="inherit_type" jdbcType="TINYINT" property="inheritType" />
    <result column="start_value" jdbcType="INTEGER" property="startValue" />
    <result column="end_value" jdbcType="INTEGER" property="endValue" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
  </resultMap>
  <sql id="Base_Column_List">
    id, param_code, param_name, goods_level, goods_level_name, param_level, org_id, sap_code, 
    org_name, parent_org_id, parent_org_name, inherit_type, start_value, end_value, remarks,
    `status`, extend, version, created_by, created_name, updated_by, updated_name, gmt_create,
    gmt_update
  </sql>
  <insert id="batchInsert" parameterType="com.cowell.iscm.entity.IscmStoreApplyParamGoodsLevelOrg" >
    insert into iscm_store_apply_param_goods_level_org (param_code, param_name, goods_level, 
      goods_level_name, param_level, org_id, 
      sap_code, org_name, parent_org_id, 
      parent_org_name, inherit_type, start_value, 
      end_value, remarks, created_by, created_name,
      updated_by, updated_name)
    values
    <foreach collection="list" item="item" index="index" separator="," >
    (#{item.paramCode,jdbcType=VARCHAR}, #{item.paramName,jdbcType=VARCHAR}, #{item.goodsLevel,jdbcType=TINYINT},
      #{item.goodsLevelName,jdbcType=VARCHAR}, #{item.paramLevel,jdbcType=INTEGER}, #{item.orgId,jdbcType=BIGINT},
      #{item.sapCode,jdbcType=VARCHAR}, #{item.orgName,jdbcType=VARCHAR}, #{item.parentOrgId,jdbcType=BIGINT},
      #{item.parentOrgName,jdbcType=VARCHAR}, #{item.inheritType,jdbcType=TINYINT}, #{item.startValue,jdbcType=INTEGER},
      #{item.endValue,jdbcType=INTEGER},#{item.remarks,jdbcType=VARCHAR}, #{item.createdBy,jdbcType=BIGINT}, #{item.createdName,jdbcType=VARCHAR},
      #{item.updatedBy,jdbcType=BIGINT}, #{item.updatedName,jdbcType=VARCHAR})
    </foreach>
  </insert>
</mapper>
