<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.extend.IscmNearExpiryDateImportExtendMapper">
    <insert id="batchInsert" parameterType="com.cowell.iscm.entity.IscmNearExpiryDateImport">
        insert into iscm_near_expiry_date_import (
            store_code,
            store_name,
            allot_group_id,
            allot_group_code,
            allot_group_name,
            goods_no,
            goods_name,
            batch_no,
            validity_date,
            stock_quantity,
            cost_amount,
            check_status,
            status,
            created_by,
            created_name,
            gmt_create,
            updated_by,
            updated_name,
            gmt_update
        )
        values
        <foreach collection="list" item="item" index="index" separator="," >
            (
            #{item.storeCode,jdbcType=VARCHAR},
            #{item.storeName,jdbcType=VARCHAR},
            #{item.allotGroupId,jdbcType=BIGINT},
            #{item.allotGroupCode,jdbcType=VARCHAR},
            #{item.allotGroupName,jdbcType=VARCHAR},
            #{item.goodsNo,jdbcType=VARCHAR},
            #{item.goodsName,jdbcType=VARCHAR},
            #{item.batchNo,jdbcType=VARCHAR},
            #{item.validityDate,jdbcType=TIMESTAMP},
            #{item.stockQuantity,jdbcType=DECIMAL},
            #{item.costAmount,jdbcType=DECIMAL},
            #{item.checkStatus,jdbcType=TINYINT},
            #{item.status,jdbcType=TINYINT},
            #{item.createdBy,jdbcType=BIGINT},
            #{item.createdName,jdbcType=VARCHAR},
            #{item.gmtCreate,jdbcType=TIMESTAMP},
            #{item.updatedBy,jdbcType=BIGINT},
            #{item.updatedName,jdbcType=VARCHAR},
            #{item.gmtUpdate,jdbcType=TIMESTAMP}
            )
        </foreach>
    </insert>
    <update id="updateMsgByStoreCodesAndUserId">
        update iscm_near_expiry_date_import set error_msg = #{errorMsg}, check_status = 1
        where store_code in
        <foreach collection="storeCodes" item="storeCode" index="index" open="(" close=")" separator=",">
            #{storeCode}
        </foreach>
        and created_by = #{userId}
        and check_status = 0
        and status = 0
    </update>
    <update id="updateMsgByStoreCodeAndGoodsNoAndBatchNoAndUserId">
        update iscm_near_expiry_date_import set error_msg = #{errorMsg}, check_status = 1
        where store_code = #{storeCode}
        and goods_no = #{goodsNo}
        and batch_no = #{batchNo}
        and created_by = #{userId}
        and check_status = 0
        and status = 0
    </update>
    <select id="findStoreCodesByUserId" resultType="java.lang.String">
        select store_code from iscm_near_expiry_date_import where created_by = #{userId} and check_status = 0 and status = 0
    </select>
    <select id="countByStoreCodesAndUserId" resultType="java.lang.Integer">
        select count(*) from iscm_near_expiry_date_import
        where store_code in
        <foreach collection="storeCodes" item="storeCode" index="index" open="(" close=")" separator=",">
            #{storeCode}
        </foreach>
        and created_by = #{userId}
        and check_status = 0
        and status = 0
    </select>

    <select id="selectIdByStoreCodeAndGoodsNoAndBatchNoAndUserId" resultType="java.lang.Long">
        select id from iscm_near_expiry_date_import
        where allot_group_code = #{allotGroupCode}
          and store_code = #{storeCode}
          and goods_no = #{goodsNo}
          and batch_no = #{batchNo}
          and created_by = #{userId}
          and check_status = 0
          and status = 0
    </select>

    <update id="updateMsgById">
        update iscm_near_expiry_date_import set error_msg = #{errorMsg}, check_status = 1
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectIdsByStoreCodesAndUserId" resultType="java.lang.Long">
        select id from iscm_near_expiry_date_import
        where store_code in
        <foreach collection="storeCodes" item="storeCode" index="index" open="(" close=")" separator=",">
            #{storeCode}
        </foreach>
        and created_by = #{userId}
        and check_status = 0
        and status = 0
    </select>
</mapper>
