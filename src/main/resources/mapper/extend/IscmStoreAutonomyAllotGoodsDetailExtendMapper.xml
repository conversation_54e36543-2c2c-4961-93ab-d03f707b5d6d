<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.extend.IscmStoreAutonomyAllotGoodsDetailExtendMapper">
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>



  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="java.util.List" useGeneratedKeys="true">
          insert into iscm_store_autonomy_allot_goods_detail (autonomy_allot_no, pos_allot_no, record_id,
          allot_type, platform_org_id, platform_org_name,
          out_company_id, out_company_code, out_company_name,
          in_company_id, in_company_code, in_company_name,
          out_store_id, out_store_code, out_store_name,
          in_store_id, in_store_code, in_store_name,
          goods_no, goods_name, goods_desc,
          allot_quantity, transfer_cost_amount, batch_no,
          expiry_date, produce_date, import_sys_stock_quantity,
          send_sys_stock_quantity,
          unit, goods_common_name, manufacturer,
          dosage_form, send_status, send_result,
          send_time,  `status`, created_by, created_name,
          updated_by, updated_name)
      values
      <foreach collection="list" item="item" index="index" separator="," >
          (#{item.autonomyAllotNo,jdbcType=VARCHAR}, #{item.posAllotNo,jdbcType=VARCHAR}, #{item.recordId,jdbcType=BIGINT},
          #{item.allotType,jdbcType=TINYINT}, #{item.platformOrgId,jdbcType=BIGINT}, #{item.platformOrgName,jdbcType=VARCHAR},
          #{item.outCompanyId,jdbcType=BIGINT}, #{item.outCompanyCode,jdbcType=VARCHAR}, #{item.outCompanyName,jdbcType=VARCHAR},
          #{item.inCompanyId,jdbcType=BIGINT}, #{item.inCompanyCode,jdbcType=VARCHAR}, #{item.inCompanyName,jdbcType=VARCHAR},
          #{item.outStoreId,jdbcType=BIGINT}, #{item.outStoreCode,jdbcType=VARCHAR}, #{item.outStoreName,jdbcType=VARCHAR},
          #{item.inStoreId,jdbcType=BIGINT}, #{item.inStoreCode,jdbcType=VARCHAR}, #{item.inStoreName,jdbcType=VARCHAR},
          #{item.goodsNo,jdbcType=VARCHAR}, #{item.goodsName,jdbcType=VARCHAR}, #{item.goodsDesc,jdbcType=VARCHAR},
          #{item.allotQuantity,jdbcType=DECIMAL}, #{item.transferCostAmount,jdbcType=DECIMAL}, #{item.batchNo,jdbcType=VARCHAR},
          #{item.expiryDate,jdbcType=DATE}, #{item.produceDate,jdbcType=DATE}, #{item.importSysStockQuantity,jdbcType=DECIMAL},
          #{item.sendSysStockQuantity,jdbcType=DECIMAL},
          #{item.unit,jdbcType=VARCHAR}, #{item.goodsCommonName,jdbcType=VARCHAR}, #{item.manufacturer,jdbcType=VARCHAR},
          #{item.dosageForm,jdbcType=VARCHAR}, #{item.sendStatus,jdbcType=TINYINT}, #{item.sendResult,jdbcType=VARCHAR},
          #{item.sendTime,jdbcType=TIMESTAMP},  #{item.status,jdbcType=TINYINT}, #{item.createdBy,jdbcType=BIGINT}, #{item.createdName,jdbcType=VARCHAR},
          #{item.updatedBy,jdbcType=BIGINT}, #{item.updatedName,jdbcType=VARCHAR})
      </foreach>
  </insert>





<select id="selectIdsByExample" parameterType="com.cowell.iscm.entity.IscmStoreAutonomyAllotGoodsDetailExample" resultType="java.lang.Long">
    select id
    from iscm_store_autonomy_allot_goods_detail
    <if test="_parameter != null">
        <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
        order by ${orderByClause}
    </if>
    <if test="limit != null">
        <if test="offset != null">
            limit ${offset}, ${limit}
        </if>
        <if test="offset == null">
            limit ${limit}
        </if>
    </if>
</select>



<select id="selectOutStoreCodesByExample" parameterType="com.cowell.iscm.entity.IscmStoreAutonomyAllotGoodsDetailExample" resultType="java.lang.String">
    select out_store_code
    from iscm_store_autonomy_allot_goods_detail
    <if test="_parameter != null">
        <include refid="Example_Where_Clause"/>
    </if>
    <if test="orderByClause != null">
        order by
        ${orderByClause}
    </if>
    group by out_store_code;
</select>


</mapper>