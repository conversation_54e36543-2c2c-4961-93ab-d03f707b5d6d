<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.extend.IscmRecalculationRecordExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.IscmRecalculationRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="apply_no" jdbcType="VARCHAR" property="applyNo" />
    <result column="bdp_apply_no" jdbcType="VARCHAR" property="bdpApplyNo" />
    <result column="apply_line" jdbcType="VARCHAR" property="applyLine" />
    <result column="company_org_id" jdbcType="BIGINT" property="companyOrgId" />
    <result column="business_id" jdbcType="BIGINT" property="businessId" />
    <result column="company_org_name" jdbcType="VARCHAR" property="companyOrgName" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="store_org_id" jdbcType="BIGINT" property="storeOrgId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="store_name" jdbcType="VARCHAR" property="storeName" />
    <result column="apply_date" jdbcType="DATE" property="applyDate" />
    <result column="data_origin_type" jdbcType="TINYINT" property="dataOriginType" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="apply_goods_type" jdbcType="TINYINT" property="applyGoodsType" />
    <result column="bdp_apply_total" jdbcType="DECIMAL" property="bdpApplyTotal" />
    <result column="var_a" jdbcType="DECIMAL" property="varA" />
    <result column="var_b" jdbcType="DECIMAL" property="varB" />
    <result column="middle_package_switch" jdbcType="VARCHAR" property="middlePackageSwitch" />
    <result column="middle_package_qty" jdbcType="DECIMAL" property="middlePackageQty" />
    <result column="apply_ratio" jdbcType="DECIMAL" property="applyRatio" />
    <result column="category_id" jdbcType="BIGINT" property="categoryId" />
    <result column="apply_total" jdbcType="DECIMAL" property="applyTotal" />
    <result column="bdp_average_daily_sales" jdbcType="DECIMAL" property="bdpAverageDailySales" />
    <result column="min_display_qty" jdbcType="DECIMAL" property="minDisplayQty" />
    <result column="stock_upper_limit" jdbcType="DECIMAL" property="stockUpperLimit" />
    <result column="stock_lower_limit" jdbcType="DECIMAL" property="stockLowerLimit" />
    <result column="sale_days_before" jdbcType="DECIMAL" property="saleDaysBefore" />
    <result column="sale_days_after" jdbcType="DECIMAL" property="saleDaysAfter" />
    <result column="special_ctrl" jdbcType="VARCHAR" property="specialCtrl" />
    <result column="special_thirty_days_qty" jdbcType="DECIMAL" property="specialThirtyDaysQty" />
    <result column="apply_reason" jdbcType="VARCHAR" property="applyReason" />
    <result column="apply_stock" jdbcType="DECIMAL" property="applyStock" />
    <result column="scarce_limit" jdbcType="INTEGER" property="scarceLimit" />
    <result column="purchase_type" jdbcType="INTEGER" property="purchaseType" />
    <result column="purchase_channel" jdbcType="VARCHAR" property="purchaseChannel" />
    <result column="warehouse_code" jdbcType="VARCHAR" property="warehouseCode" />
    <result column="recommend_reason" jdbcType="VARCHAR" property="recommendReason" />
    <result column="promotion_desc" jdbcType="VARCHAR" property="promotionDesc" />
    <result column="composite_new" jdbcType="TINYINT" property="compositeNew" />
    <result column="thirty_sales_quantity" jdbcType="DECIMAL" property="thirtySalesQuantity" />
    <result column="promotion_title" jdbcType="VARCHAR" property="promotionTitle" />
    <result column="promotion_start_date" jdbcType="VARCHAR" property="promotionStartDate" />
    <result column="promotion_end_date" jdbcType="VARCHAR" property="promotionEndDate" />
    <result column="deal_suggest" jdbcType="TINYINT" property="dealSuggest" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>

  <sql id="Base_Column_List">
    id, apply_no, bdp_apply_no, apply_line, company_org_id, business_id, company_org_name,
    company_code, store_org_id, store_id, store_code, store_name, apply_date, data_origin_type,
    goods_no, apply_goods_type, bdp_apply_total, var_a, var_b, middle_package_switch,
    middle_package_qty, apply_ratio, category_id, apply_total, bdp_average_daily_sales,
    min_display_qty, stock_upper_limit, stock_lower_limit, sale_days_before, sale_days_after,
    special_ctrl, special_thirty_days_qty, apply_reason, apply_stock, scarce_limit, purchase_type,
    purchase_channel, warehouse_code, recommend_reason, promotion_desc, composite_new, thirty_sales_quantity,
    promotion_title, promotion_start_date, promotion_end_date, deal_suggest, gmt_create, gmt_update
  </sql>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entity.IscmRecalculationRecord" useGeneratedKeys="true">
    insert into iscm_recalculation_record (bdp_apply_no, apply_no, apply_line, company_org_id,
      business_id, company_org_name, company_code, 
      store_org_id, store_id, store_code, 
      store_name, apply_date, data_origin_type, 
      goods_no, apply_goods_type, bdp_apply_total, 
      var_a, var_b, middle_package_switch, middle_package_qty, apply_ratio, category_id,
      apply_total, bdp_average_daily_sales, min_display_qty, 
      stock_upper_limit, stock_lower_limit, sale_days_before, 
      sale_days_after, special_ctrl, special_thirty_days_qty,
      apply_reason, apply_stock, scarce_limit,  purchase_type, purchase_channel, warehouse_code,
      recommend_reason, promotion_desc, composite_new,
      thirty_sales_quantity, promotion_title, promotion_start_date,
      promotion_end_date, deal_suggest
    )
    values
    <foreach collection="list" item="item" index="index" separator="," >
    (#{item.bdpApplyNo,jdbcType=VARCHAR}, #{item.applyNo,jdbcType=VARCHAR}, #{item.applyLine,jdbcType=VARCHAR}, #{item.companyOrgId,jdbcType=BIGINT},
      #{item.businessId,jdbcType=BIGINT}, #{item.companyOrgName,jdbcType=VARCHAR}, #{item.companyCode,jdbcType=VARCHAR},
      #{item.storeOrgId,jdbcType=BIGINT}, #{item.storeId,jdbcType=BIGINT}, #{item.storeCode,jdbcType=VARCHAR},
      #{item.storeName,jdbcType=VARCHAR}, #{item.applyDate,jdbcType=DATE}, #{item.dataOriginType,jdbcType=TINYINT},
      #{item.goodsNo,jdbcType=VARCHAR}, #{item.applyGoodsType,jdbcType=TINYINT}, #{item.bdpApplyTotal,jdbcType=DECIMAL},
      #{item.varA,jdbcType=DECIMAL}, #{item.varB,jdbcType=DECIMAL}, #{item.middlePackageSwitch,jdbcType=VARCHAR}, #{item.middlePackageQty,jdbcType=DECIMAL}, #{item.applyRatio,jdbcType=DECIMAL}, #{item.categoryId,jdbcType=DECIMAL},
      #{item.applyTotal,jdbcType=DECIMAL}, #{item.bdpAverageDailySales,jdbcType=DECIMAL}, #{item.minDisplayQty,jdbcType=DECIMAL},
      #{item.stockUpperLimit,jdbcType=DECIMAL}, #{item.stockLowerLimit,jdbcType=DECIMAL}, #{item.saleDaysBefore,jdbcType=DECIMAL},
      #{item.saleDaysAfter,jdbcType=DECIMAL}, #{item.specialCtrl,jdbcType=DECIMAL}, #{item.specialThirtyDaysQty,jdbcType=DECIMAL},
      #{item.applyReason,jdbcType=VARCHAR}, #{item.applyStock,jdbcType=DECIMAL}, #{item.scarceLimit,jdbcType=INTEGER}, #{item.purchaseType,jdbcType=INTEGER}, #{item.purchaseChannel,jdbcType=VARCHAR}, #{item.warehouseCode,jdbcType=VARCHAR},
      #{item.recommendReason,jdbcType=VARCHAR}, #{item.promotionDesc,jdbcType=VARCHAR}, #{item.compositeNew,jdbcType=TINYINT},
      #{item.thirtySalesQuantity,jdbcType=DECIMAL}, #{item.promotionTitle,jdbcType=VARCHAR}, #{item.promotionStartDate,jdbcType=VARCHAR},
      #{item.promotionEndDate,jdbcType=VARCHAR}, #{item.dealSuggest,jdbcType=TINYINT})
    </foreach>
  </insert>
    <delete id="deleteByExample" parameterType="com.cowell.iscm.entity.IscmRecalculationNoResultLogExample">
      delete from iscm_recalculation_record
      <if test="_parameter != null">
        <include refid="Example_Where_Clause" />
      </if>
      <if test="limit != null">
        limit ${limit}
      </if>
    </delete>
    <select id="getIscmWarnByCompanyCode"
            resultType="com.cowell.iscm.service.dto.applyParam.IscmWarnReportDTO">
      select store_code as storeCode, min(gmt_create) as recalculTime, max(gmt_create) as iscmPushTime, apply_no as iscmPushOrderNo, count(*) as iscmPushGoodsNum, 0 as recalculUpdateNum from iscm_recalculation_record
      where apply_date = #{applyDate, jdbcType=DATE} and company_code = #{companyCode}
      <if test="storeCodes != null and storeCodes.size > 0">
        and store_code in
        <foreach collection="storeCodes" item="storeCode" index="index" open="(" close=")" separator=",">
          #{storeCode}
        </foreach>
      </if>
      group by store_code
    </select>
  <select id="getIscmWarnUpdateByCompanyCode"
          resultType="com.cowell.iscm.service.dto.applyParam.IscmWarnReportDTO">
      select store_code as storeCode, count(*) as recalculUpdateNum from iscm_recalculation_record
      where apply_date = #{applyDate, jdbcType=DATE} and company_code = #{companyCode}
      <if test="storeCodes != null and storeCodes.size > 0">
        and store_code in
        <foreach collection="storeCodes" item="storeCode" index="index" open="(" close=")" separator=",">
          #{storeCode}
        </foreach>
      </if>
    and bdp_apply_total != apply_total
      group by store_code
  </select>
  <select id="getApplyNosByCompanyCode" resultType="java.lang.String">
    select group_concat(distinct apply_no) from iscm_recalculation_record
    where apply_date = #{applyDate, jdbcType=DATE} and company_code = #{companyCode}
    group by store_code
  </select>
</mapper>
