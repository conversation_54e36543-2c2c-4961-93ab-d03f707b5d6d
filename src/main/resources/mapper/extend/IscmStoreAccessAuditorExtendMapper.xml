<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.extend.IscmStoreAccessAuditorExtendMapper">
    <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.IscmStoreAccessAuditor">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="emp_code" jdbcType="VARCHAR" property="empCode" />
        <result column="emp_user_id" jdbcType="BIGINT" property="empUserId" />
        <result column="emp_user_name" jdbcType="VARCHAR" property="empUserName" />
        <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
        <result column="store_name" jdbcType="VARCHAR" property="storeName" />
        <result column="company_org_id" jdbcType="BIGINT" property="companyOrgId" />
        <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
        <result column="company_name" jdbcType="VARCHAR" property="companyName" />
        <result column="platform_org_id" jdbcType="BIGINT" property="platformOrgId" />
        <result column="platform_name" jdbcType="VARCHAR" property="platformName" />
        <result column="status" jdbcType="TINYINT" property="status" />
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
        <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
        <result column="extend" jdbcType="VARCHAR" property="extend" />
        <result column="version" jdbcType="INTEGER" property="version" />
        <result column="created_by" jdbcType="BIGINT" property="createdBy" />
        <result column="created_name" jdbcType="VARCHAR" property="createdName" />
        <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
        <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
    </resultMap>

    <sql id="Base_Column_List">
        id, emp_code, emp_user_id, emp_user_name, store_code, store_name, company_org_id,
    company_code, company_name, platform_org_id, platform_name, `status`, gmt_create,
    gmt_update, extend, version, created_by, created_name, updated_by, updated_name
    </sql>

    <sql id="searchConfigFromWhere">
        from iscm_store_access_auditor
        where 1=1
        <if test="listParam.storeCode != null and listParam.storeCode != ''">
            and store_code = #{listParam.storeCode}
        </if>
        <if test="listParam.storeName != null and listParam.storeName != ''">
            and store_name like concat('%', #{listParam.storeName},'%')
        </if>
        <if test="listParam.empCode != null and listParam.empCode != ''">
            and emp_code = #{listParam.empCode}
        </if>
        <if test="listParam.platformOrgId != null">
            and platform_org_id = #{listParam.platformOrgId}
        </if>
        <if test="listParam.companyOrgId != null">
            and company_org_id = #{listParam.companyOrgId}
        </if>
        <if test="listParam.storeCodeList != null and listParam.storeCodeList.size>0">
            and store_code in
            <foreach collection="listParam.storeCodeList" item="item" index="index" separator="," open="("
                     close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </sql>

    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entity.IscmStoreAccessAuditor" useGeneratedKeys="true">
        insert into iscm_store_access_auditor (emp_code, emp_user_id,
        emp_user_name, store_code,
        store_name, company_org_id, company_code, company_name,
        platform_org_id, platform_name, created_by,
        created_name, updated_by, updated_name)
        values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.empCode,jdbcType=VARCHAR}, #{item.empUserId,jdbcType=BIGINT}, #{item.empUserName,jdbcType=VARCHAR}, #{item.storeCode,jdbcType=VARCHAR},
            #{item.storeName,jdbcType=VARCHAR}, #{item.companyOrgId,jdbcType=BIGINT}, #{item.companyCode,jdbcType=VARCHAR}, #{item.companyName,jdbcType=VARCHAR},
            #{item.platformOrgId,jdbcType=BIGINT}, #{item.platformName,jdbcType=VARCHAR}, #{item.createdBy,jdbcType=BIGINT},
            #{item.createdName,jdbcType=VARCHAR}, #{item.updatedBy,jdbcType=BIGINT}, #{item.updatedName,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <delete id="deleteBatchByCondition" parameterType="java.util.List">
        delete from iscm_store_access_auditor WHERE store_code in
        <foreach collection="list" item="code" separator="," open="(" close=")">
            #{code}
        </foreach>
    </delete>

    <select id="searchConfigCount" resultType="java.lang.Long">
        select count(*)
        <include refid="searchConfigFromWhere"></include>
    </select>

    <select id="searchConfigList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        <include refid="searchConfigFromWhere"></include>
        order by gmt_update desc
        LIMIT #{listParam.offset},#{listParam.perPage}
    </select>

    <select id="exportConfigList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        <include refid="searchConfigFromWhere"></include>
    </select>
</mapper>