<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.extend.IscmStoreReturnExecuteOrderDetailExtendMapper">
    <select id="selectSumGroupByOrderNo"
            resultType="com.cowell.iscm.service.dto.returnWarehouse.StoreReturnExecuteOrderSum">
        select
        return_order_no as returnOrderNo,
        company_org_id companyOrgId,
        store_org_id as storeId,
        store_code as storeCode,
        count(*) returnGoodsQuantityTotal,
        sum(return_quantity) returnQuantityTotal,
        sum(round(cost_amount/register_quantity*return_quantity,4)) returnCostAmountTotal,
        sum(issue_return_quantity) issueReturnQuantityTotal,
        sum(issue_return_amount) issueReturnAmountTotal,
        sum(real_return_quantity) realReturnQuantityTotal
        from iscm_store_return_execute_order_detail
        where created_month = #{month}
        and return_order_no in
        <foreach collection="orderNoList" item="orderNo" index="index" open="(" close=")" separator=",">
            #{orderNo}
        </foreach>
        and process_status in
        <foreach collection="statusList" item="status" index="index" open="(" close=")" separator=",">
            #{status}
        </foreach>
        group by return_order_no
        limit #{offset}, #{limit}
    </select>

    <select id="selectQtyGroupByBatchNo"
            resultType="com.cowell.iscm.service.dto.returnWarehouse.StoreReturnExecuteOrderQty">
        select
        return_order_no as returnOrderNo,
        platform_org_id as platformOrgId,
        company_org_id as companyOrgId,
        store_org_id as storeOrgId,
        goods_no as goodsNo,
        batch_no as batchNo,
        batch_stock as batchStock,
        return_quantity as returnQuantity,
        register_quantity as registerQuantity,
        cost_amount as costAmount,
        issue_return_quantity as issueReturnQuantity,
        issue_return_amount as issueReturnAmount,
        gmt_update as gmtUpdate,
        process_status as processStatus
        from iscm_store_return_execute_order_detail
        where created_month = #{month}
        and return_order_no in
        <foreach collection="orderNoList" item="orderNo" index="index" open="(" close=")" separator=",">
            #{orderNo}
        </foreach>
        and process_status in
        <foreach collection="statusList" item="status" index="index" open="(" close=")" separator=",">
            #{status}
        </foreach>
        limit #{offset}, #{limit}
    </select>

    <select id="selectReturnNonSendStock" resultType="java.math.BigDecimal">
        select sum(issue_return_quantity) from iscm_store_return_execute_order_detail
        where created_month = #{month}
        and warehouse_code = #{warehouseCode}
        and goods_no = #{goodsNo}
        and process_status in
        <foreach collection="executeProcessStatusList" item="processStatus" index="index" separator="," open="(" close=")">
            #{processStatus}
        </foreach>
    </select>

    <select id="selectProcessStatusList" resultType="java.lang.Byte">
        select process_status from iscm_store_return_execute_order_detail
        where created_month = #{month}
        and return_order_no = #{orderNo}
    </select>

    <select id="selectProcessStatusListGroupByOrderNo"
            resultType="com.cowell.iscm.service.dto.returnWarehouse.StoreReturnExecuteOrderStatus">
        select return_order_no as returnOrderNo,group_concat(distinct process_status) as processStatusAll
        from iscm_store_return_execute_order_detail
        where created_month = #{month}
        and return_order_no in
        <foreach collection="orderNoList" item="orderNo" index="index" open="(" close=")" separator=",">
            #{orderNo}
        </foreach>
        group by return_order_no
    </select>
    <select id="selectNoticeStoreOrgIds" resultType="java.lang.Long">
        select store_org_id
        from iscm_store_return_execute_order_detail
        where created_month = #{month}
        and gmt_update between #{start} and #{end}
        and return_business_type = 7
        and process_status = 6
        group by store_org_id
    </select>
    <select id="selectNoticeInfos" resultType="com.cowell.iscm.entity.IscmStoreReturnExecuteOrderDetail">
        select goods_no as goodsNo, batch_no as batchNo, handle_quantity as handleQuantity
        from iscm_store_return_execute_order_detail
        where created_month = #{month}
          and store_org_id = #{storeOrgId}
          and gmt_update between #{start} and #{end}
          and return_business_type = 7
          and process_status = 6
    </select>

    <update id="updateRowNo">
        set @rank:=0;
        update iscm_store_return_execute_order_detail_${tbIndex} t set t.row_no = @rank:=LPAD(@rank+1,6,'0')
        where t.return_order_no = #{orderNo} and t.created_month = #{month} order by t.id;
    </update>

    <insert id="batchInsert" parameterType="com.cowell.iscm.entity.IscmStoreReturnExecuteOrderDetail">
        insert into iscm_store_return_execute_order_detail (
        return_order_no, return_business_type,
        platform_org_id, platform_org_name,
        company_org_id, company_code, company_name,
        store_org_id, store_code, store_name,
        warehouse_code, warehouse_name, register_order_no,
        register_quantity, cost_amount, data_type,
        register_date, goods_no, goods_name,
        forecast_sales, bar_code, goods_common_name,
        goods_unit, description, specifications,
        dosage_form, manufacturer, approval_number,
        goods_class_id, goods_class_name, batch_no,
        validity_date, produce_date, validity_days,
        stock_upper_limit_days, stock_lower_limit_days,
        hd_synthesize_average_daily_sales, stock_upper_limit,
        stock_lower_limit, bdp_synthesize_average_daily_sales,
        storage_days, non_sales_days, should_return_quantity,
        return_quantity, goods_stock, batch_stock,
        thirty_sales_quantity, thirty_sales_count,
        min_display_quantity, goodsline, pushlevel,
        goods_pur_channel, purchase_org_code, purchase_org_name, purchase_group,
        forbid_distribute, forbid_return_warehouse,
        forbid_apply, forbid_allot,
        created_by, created_name,
        updated_by, updated_name, issue_by,
        issue_name, gmt_issue, issue_return_quantity,
        issue_return_amount, real_return_quantity,
        pos_return_order_no, row_no, process_status,
        created_month, gmt_create, gmt_update, return_reason_type, return_reason,
        data_version, source_id,
        stock_type, disposal_opinion, handle_quantity)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.returnOrderNo,jdbcType=VARCHAR}, #{item.returnBusinessType,jdbcType=VARCHAR},
            #{item.platformOrgId,jdbcType=BIGINT}, #{item.platformOrgName,jdbcType=VARCHAR},
            #{item.companyOrgId,jdbcType=BIGINT}, #{item.companyCode,jdbcType=VARCHAR}, #{item.companyName,jdbcType=VARCHAR},
            #{item.storeOrgId,jdbcType=BIGINT}, #{item.storeCode,jdbcType=VARCHAR}, #{item.storeName,jdbcType=VARCHAR},
            #{item.warehouseCode,jdbcType=VARCHAR}, #{item.warehouseName,jdbcType=VARCHAR}, #{item.registerOrderNo,jdbcType=VARCHAR},
            #{item.registerQuantity,jdbcType=DECIMAL}, #{item.costAmount,jdbcType=DECIMAL}, #{item.dataType,jdbcType=TINYINT},
            #{item.registerDate,jdbcType=DATE}, #{item.goodsNo,jdbcType=VARCHAR}, #{item.goodsName,jdbcType=VARCHAR},
            #{item.forecastSales,jdbcType=DECIMAL}, #{item.barCode,jdbcType=VARCHAR}, #{item.goodsCommonName,jdbcType=VARCHAR},
            #{item.goodsUnit,jdbcType=VARCHAR}, #{item.description,jdbcType=VARCHAR}, #{item.specifications,jdbcType=VARCHAR},
            #{item.dosageForm,jdbcType=VARCHAR}, #{item.manufacturer,jdbcType=VARCHAR}, #{item.approvalNumber,jdbcType=VARCHAR},
            #{item.goodsClassId,jdbcType=BIGINT}, #{item.goodsClassName,jdbcType=VARCHAR}, #{item.batchNo,jdbcType=VARCHAR},
            #{item.validityDate,jdbcType=TIMESTAMP}, #{item.produceDate,jdbcType=TIMESTAMP}, #{item.validityDays,jdbcType=INTEGER},
            #{item.stockUpperLimitDays,jdbcType=INTEGER}, #{item.stockLowerLimitDays,jdbcType=INTEGER},
            #{item.hdSynthesizeAverageDailySales,jdbcType=DECIMAL}, #{item.stockUpperLimit,jdbcType=DECIMAL},
            #{item.stockLowerLimit,jdbcType=DECIMAL}, #{item.bdpSynthesizeAverageDailySales,jdbcType=DECIMAL},
            #{item.storageDays,jdbcType=INTEGER}, #{item.nonSalesDays,jdbcType=INTEGER}, #{item.shouldReturnQuantity,jdbcType=DECIMAL},
            #{item.returnQuantity,jdbcType=DECIMAL}, #{item.goodsStock,jdbcType=DECIMAL}, #{item.batchStock,jdbcType=DECIMAL},
            #{item.thirtySalesQuantity,jdbcType=DECIMAL}, #{item.thirtySalesCount,jdbcType=INTEGER},
            #{item.minDisplayQuantity,jdbcType=DECIMAL}, #{item.goodsline,jdbcType=VARCHAR}, #{item.pushlevel,jdbcType=VARCHAR},
            #{item.goodsPurChannel,jdbcType=VARCHAR}, #{item.purchaseOrgCode,jdbcType=VARCHAR}, #{item.purchaseOrgName,jdbcType=VARCHAR}, #{item.purchaseGroup,jdbcType=VARCHAR},
            #{item.forbidDistribute,jdbcType=VARCHAR}, #{item.forbidReturnWarehouse,jdbcType=VARCHAR},
            #{item.forbidApply,jdbcType=VARCHAR}, #{item.forbidAllot,jdbcType=VARCHAR},
            #{item.createdBy,jdbcType=BIGINT}, #{item.createdName,jdbcType=VARCHAR},
            #{item.updatedBy,jdbcType=BIGINT}, #{item.updatedName,jdbcType=VARCHAR}, #{item.issueBy,jdbcType=BIGINT},
            #{item.issueName,jdbcType=VARCHAR}, #{item.gmtIssue,jdbcType=TIMESTAMP}, #{item.issueReturnQuantity,jdbcType=DECIMAL},
            #{item.issueReturnAmount,jdbcType=DECIMAL}, #{item.realReturnQuantity,jdbcType=DECIMAL},
            #{item.posReturnOrderNo,jdbcType=VARCHAR}, #{item.rowNo,jdbcType=VARCHAR}, #{item.processStatus,jdbcType=TINYINT},
            #{item.createdMonth,jdbcType=INTEGER}, #{item.gmtCreate,jdbcType=TIMESTAMP}, #{item.gmtUpdate,jdbcType=TIMESTAMP}, #{item.returnReasonType,jdbcType=VARCHAR}, #{item.returnReason,jdbcType=VARCHAR},
            #{item.dataVersion,jdbcType=VARCHAR}, #{item.sourceId,jdbcType=BIGINT},
            #{item.stockType,jdbcType=VARCHAR}, #{item.disposalOpinion,jdbcType=INTEGER}, #{item.handleQuantity,jdbcType=INTEGER})
        </foreach>
    </insert>

</mapper>
