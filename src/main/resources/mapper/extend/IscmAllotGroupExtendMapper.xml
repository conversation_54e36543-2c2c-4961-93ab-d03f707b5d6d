<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.extend.IscmAllotGroupExtendMapper">
    <update id="updateTimeTypeById">
        update iscm_allot_group_info set time_type = #{timeType} where id = #{id}
    </update>

    <select id="selectAllotGroupCount" resultType="java.lang.Long"
            parameterType="com.cowell.iscm.service.dto.allotGroup.AllotGroupQuery">
        SELECT COUNT(*) FROM (<include refid="SQL_SelectAllotGroup" />) T
    </select>
    <select id="selectAllotGroupList" resultType="com.cowell.iscm.service.dto.allotGroup.AllotGroupInfo"
            parameterType="com.cowell.iscm.service.dto.allotGroup.AllotGroupQuery">
        <include refid="SQL_SelectAllotGroup" />
        ORDER BY gmt_create DESC
        LIMIT ${page * pageSize}, ${pageSize}
    </select>
    <sql id="SQL_SelectAllotGroup">
        SELECT
        id,
        group_code AS groupCode,
        group_name AS groupName,
        store_quantity AS storeQuantity,
        enable_status AS enableStatus,
        created_name AS createdName,
        updated_name AS updatedName,
        gmt_create AS gmtCreate,
        gmt_update AS gmtUpdate
        FROM iscm_allot_group_info
        <where>
            <if test="groupName != null and groupName != ''">
                and group_name like concat('%', #{groupName}, '%')
            </if>
            <if test="createdName != null and createdName != ''">
                and created_name = #{createdName}
            </if>
            <if test="enableStatus != null">
                and enable_status = #{enableStatus}
            </if>
        </where>
    </sql>
    <sql id="SQL_SelectAllotGroup_20220609">
        SELECT
            t1.group_code AS groupCode,
            t1.group_name AS groupName,
            t1.store_quantity AS storeQuantity,
            t1.enable_status AS enableStatus,
            t1.created_name AS createdName,
            t1.updated_name AS updatedName,
            t1.gmt_create AS gmtCreate,
            t1.gmt_update AS gmtUpdate
        FROM
            (
                SELECT
                    t1.id,
                    t1.group_code,
                    t1.group_name,
                    t1.store_quantity,
                    t1.enable_status,
                    t1.created_name,
                    t1.updated_name,
                    t1.gmt_create,
                    t1.gmt_update
                FROM
                    iscm_allot_group_info t1
                INNER JOIN iscm_allot_group_company t2 ON t1.group_code = t2.group_code
                <where>
                    <if test="companyIds != null and companyIds.size > 0">
                        AND
                        t2.company_org_id IN
                        <foreach collection="companyIds" item="companyId" index="index" open="(" close=")" separator=",">
                            #{companyId}
                        </foreach>
                    </if>
                    <if test="groupName != null and groupName != ''">
                        AND t1.group_name LIKE concat('%', #{groupName}, '%')
                    </if>
                    <if test="createdName != null and createdName != ''">
                        AND t1.created_name = #{createdName}
                    </if>
                    <if test="enableStatus != null">
                        AND t1.enable_status = #{enableStatus}
                    </if>
                </where>
            ) t1
                LEFT JOIN (
                SELECT
                    group_code
                FROM
                    iscm_allot_group_company
                <if test="companyIds != null and companyIds.size > 0">
                    WHERE
                    company_org_id IN
                    <foreach collection="companyIds" item="companyId" index="index" open="(" close=")" separator=",">
                        #{companyId}
                    </foreach>
                </if>
            ) t2 ON t1.group_code = t2.group_code
        WHERE
            t2.group_code IS NULL
        GROUP BY
            t1.group_code
        ORDER BY
            NULL
    </sql>

    <select id="selectAllotGroupCompany" resultType="com.cowell.iscm.entity.IscmAllotGroupCompany">
        select group_code as groupCode, company_org_id as companyOrgId, company_name as companyName
        from iscm_allot_group_company
        where 1=1
        <if test="companyOrgIds != null and companyOrgIds.size > 0">
            and company_org_id in
            <foreach collection="companyOrgIds" item="companyOrgId" index="index" separator="," open="(" close=")">
                #{companyOrgId}
            </foreach>
        </if>
        limit #{offset}, #{limit}
    </select>
    <select id="selectAllotGroupCompanyByCodes" resultType="com.cowell.iscm.entity.IscmAllotGroupCompany">
        select group_code as groupCode, company_org_id as companyOrgId, company_name as companyName
        from iscm_allot_group_company
        where group_code in
        <foreach collection="groupCodes" item="groupCode" index="index" separator="," open="(" close=")">
            #{groupCode}
        </foreach>
    </select>

    <insert id="batchInsertAllotGroupStore" parameterType="list">
        insert into iscm_allot_group_store
        (
            group_code,
            group_name,
            platform_id,
            platform_name,
            company_org_id,
            company_code,
            company_name,
            business_id,
            store_org_id,
            store_code,
            store_name,
            store_id,
            gmt_create,
            gmt_update,
            created_by,
            created_name,
            updated_by,
            updated_name
        )
        values
        <foreach collection="list" item="item" index="index" separator="," >
            (
            #{item.groupCode,jdbcType=VARCHAR},
            #{item.groupName,jdbcType=VARCHAR},
            #{item.platformId,jdbcType=BIGINT},
            #{item.platformName,jdbcType=VARCHAR},
            #{item.companyOrgId,jdbcType=BIGINT},
            #{item.companyCode,jdbcType=VARCHAR},
            #{item.companyName,jdbcType=VARCHAR},
            #{item.businessId,jdbcType=BIGINT},
            #{item.storeOrgId,jdbcType=BIGINT},
            #{item.storeCode,jdbcType=VARCHAR},
            #{item.storeName,jdbcType=VARCHAR},
            #{item.storeId,jdbcType=BIGINT},
            #{item.gmtCreate,jdbcType=TIMESTAMP},
            #{item.gmtUpdate,jdbcType=TIMESTAMP},
            #{item.createdBy,jdbcType=BIGINT},
            #{item.createdName,jdbcType=VARCHAR},
            #{item.updatedBy,jdbcType=BIGINT},
            #{item.updatedName,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <insert id="batchInsertAllotGroupCompany" parameterType="list">
        insert into iscm_allot_group_company
        (
            group_code,
            group_name,
            platform_id,
            platform_name,
            company_org_id,
            company_code,
            company_name,
            business_id,
            gmt_create,
            gmt_update,
            created_by,
            created_name,
            updated_by,
            updated_name
        )
        values
        <foreach collection="list" item="item" index="index" separator="," >
            (
            #{item.groupCode,jdbcType=VARCHAR},
            #{item.groupName,jdbcType=VARCHAR},
            #{item.platformId,jdbcType=BIGINT},
            #{item.platformName,jdbcType=VARCHAR},
            #{item.companyOrgId,jdbcType=BIGINT},
            #{item.companyCode,jdbcType=VARCHAR},
            #{item.companyName,jdbcType=VARCHAR},
            #{item.businessId,jdbcType=BIGINT},
            #{item.gmtCreate,jdbcType=TIMESTAMP},
            #{item.gmtUpdate,jdbcType=TIMESTAMP},
            #{item.createdBy,jdbcType=BIGINT},
            #{item.createdName,jdbcType=VARCHAR},
            #{item.updatedBy,jdbcType=BIGINT},
            #{item.updatedName,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>
</mapper>
