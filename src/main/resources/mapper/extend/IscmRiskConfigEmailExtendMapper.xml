<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.extend.IscmRiskConfigEmailExtendMapper">
    <insert id="batchInsert" parameterType="com.cowell.iscm.entity.IscmRiskConfigEmail">
        insert into iscm_risk_config_email (
            risk_config_id,
            email,
            receiver,
            created_by,
            created_name,
            gmt_create,
            updated_by,
            updated_name,
            gmt_update
        )
        values
        <foreach collection="list" item="item" index="index" separator="," >
            (
             #{item.riskConfigId,jdbcType=BIGINT},
             #{item.email,jdbcType=VARCHAR},
             #{item.receiver,jdbcType=VARCHAR},
             #{item.createdBy,jdbcType=BIGINT},
             #{item.createdName,jdbcType=VARCHAR},
             #{item.gmtCreate,jdbcType=TIMESTAMP},
             #{item.updatedBy,jdbcType=BIGINT},
             #{item.updatedName,jdbcType=VARCHAR},
             #{item.gmtUpdate,jdbcType=TIMESTAMP}
            )
        </foreach>
    </insert>
</mapper>
