<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.extend.IscmStoreApplyParamGoodsUpperLimitOrgExtendMapper">
    <insert id="batchInsert" parameterType="list" >
        insert into iscm_store_apply_param_goods_upper_limit_org (
            param_code, param_name, param_level,
            org_id, sap_code, org_name,
            parent_org_id, parent_org_name, inherit_type,
            goods_type, goods_type_name,
            goods_level, goods_level_name,
            stock_upper_limit_multiple, marketable_days,
            created_by, created_name,
            updated_by, updated_name)
        values
        <foreach collection="list" item="item" index="index" separator="," >
            (
            #{item.paramCode,jdbcType=VARCHAR}, #{item.paramName,jdbcType=VARCHAR}, #{item.paramLevel,jdbcType=INTEGER},
            #{item.orgId,jdbcType=BIGINT}, #{item.sapCode,jdbcType=VARCHAR}, #{item.orgName,jdbcType=VARCHAR},
            #{item.parentOrgId,jdbcType=BIGINT}, #{item.parentOrgName,jdbcType=VARCHAR}, #{item.inheritType,jdbcType=TINYINT},
            #{item.goodsType,jdbcType=TINYINT}, #{item.goodsTypeName,jdbcType=VARCHAR},
            #{item.goodsLevel,jdbcType=TINYINT}, #{item.goodsLevelName,jdbcType=VARCHAR},
            #{item.stockUpperLimitMultiple,jdbcType=DECIMAL}, #{item.marketableDays,jdbcType=INTEGER},
            #{item.createdBy,jdbcType=BIGINT}, #{item.createdName,jdbcType=VARCHAR},
            #{item.updatedBy,jdbcType=BIGINT}, #{item.updatedName,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>
</mapper>
