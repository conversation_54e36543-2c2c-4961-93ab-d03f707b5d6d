<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.extend.IscmParamOrgGoodsClassExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.IscmParamOrgGoodsClass">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="param_id" jdbcType="BIGINT" property="paramId" />
    <result column="param_unique_mark" jdbcType="VARCHAR" property="paramUniqueMark" />
    <result column="goods_class_id" jdbcType="BIGINT" property="goodsClassId" />
    <result column="goods_class_name" jdbcType="VARCHAR" property="goodsClassName" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
  </resultMap>
  <sql id="Base_Column_List">
    id, param_id, param_unique_mark, goods_class_id, goods_class_name, status, extend, 
    version, created_by, created_name, updated_by, updated_name, gmt_create, gmt_update
  </sql>
  <insert id="batchInsert" parameterType="com.cowell.iscm.entity.IscmParamOrgGoodsClass">
    insert into iscm_param_org_goods_class (param_id, param_unique_mark,
      goods_class_id, goods_class_name, created_by,
      created_name, updated_by, updated_name)
    values
    <foreach collection="list" item="item" index="index" separator="," >
    (#{item.paramId,jdbcType=BIGINT}, #{item.paramUniqueMark,jdbcType=VARCHAR},
      #{item.goodsClassId,jdbcType=BIGINT}, #{item.goodsClassName,jdbcType=VARCHAR},  #{item.createdBy,jdbcType=BIGINT},
      #{item.createdName,jdbcType=VARCHAR}, #{item.updatedBy,jdbcType=BIGINT}, #{item.updatedName,jdbcType=VARCHAR})
    </foreach>
  </insert>
    <delete id="deleteByParamUniqueMark">
      delete from iscm_param_org_goods_class where param_unique_mark = #{paramUniqueMark}
    </delete>
    <select id="selectGoodsClassByParamUniqueMark" resultType="java.lang.Long">
    select goods_class_id
    from iscm_param_org_goods_class
    where param_unique_mark = #{paramUniqueMark}
  </select>
    <select id="selectGoodsClassInfoByParamUniqueMark" resultType="java.lang.Long">
    select goods_class_id
    from iscm_param_org_goods_class
    where param_unique_mark = #{paramUniqueMark}
  </select>
</mapper>
