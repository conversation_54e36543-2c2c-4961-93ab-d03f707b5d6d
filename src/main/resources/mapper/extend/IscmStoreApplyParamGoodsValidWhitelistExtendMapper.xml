<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.extend.IscmStoreApplyParamGoodsValidWhitelistExtendMapper">

    <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.IscmStoreApplyParamGoodsValidWhitelist">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="param_code" jdbcType="VARCHAR" property="paramCode" />
        <result column="param_name" jdbcType="VARCHAR" property="paramName" />
        <result column="param_level" jdbcType="INTEGER" property="paramLevel" />
        <result column="org_id" jdbcType="BIGINT" property="orgId" />
        <result column="org_name" jdbcType="VARCHAR" property="orgName" />
        <result column="start_date" jdbcType="TIMESTAMP" property="startDate" />
        <result column="end_date" jdbcType="TIMESTAMP" property="endDate" />
        <result column="store_type" jdbcType="VARCHAR" property="storeType" />
        <result column="upper_quantity" jdbcType="INTEGER" property="upperQuantity" />
        <result column="business_id" jdbcType="BIGINT" property="businessId" />
        <result column="business_name" jdbcType="VARCHAR" property="businessName" />
        <result column="business_code" jdbcType="VARCHAR" property="businessCode" />
        <result column="platform_org_id" jdbcType="BIGINT" property="platformOrgId" />
        <result column="platform_name" jdbcType="VARCHAR" property="platformName" />
        <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
        <result column="bar_code" jdbcType="VARCHAR" property="barCode" />
        <result column="cur_name" jdbcType="VARCHAR" property="curName" />
        <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
        <result column="goods_unit" jdbcType="VARCHAR" property="goodsUnit" />
        <result column="specifications" jdbcType="VARCHAR" property="specifications" />
        <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
        <result column="status" jdbcType="TINYINT" property="status" />
        <result column="extend" jdbcType="VARCHAR" property="extend" />
        <result column="version" jdbcType="INTEGER" property="version" />
        <result column="created_by" jdbcType="BIGINT" property="createdBy" />
        <result column="created_name" jdbcType="VARCHAR" property="createdName" />
        <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
        <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
        <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    </resultMap>

    <insert id="batchInsert" parameterType="list" keyProperty="id" useGeneratedKeys="true">
        insert into iscm_store_apply_param_goods_valid_whitelist (
            param_code, param_name, param_level,
            org_id, org_name, start_date,
            end_date, store_type, upper_quantity,
            business_id, business_name, business_code,
            platform_org_id, platform_name,
            goods_no, bar_code,
            cur_name, goods_name, goods_unit,
            specifications, manufacturer,
            extend, version, created_by,
            created_name, updated_by, updated_name
        )
        values
        <foreach collection="list" item="item" index="index" separator="," >
        (
            #{item.paramCode,jdbcType=VARCHAR}, #{item.paramName,jdbcType=VARCHAR}, #{item.paramLevel,jdbcType=INTEGER},
            #{item.orgId,jdbcType=BIGINT}, #{item.orgName,jdbcType=VARCHAR}, #{item.startDate,jdbcType=TIMESTAMP},
            #{item.endDate,jdbcType=TIMESTAMP}, #{item.storeType,jdbcType=VARCHAR}, #{item.upperQuantity,jdbcType=INTEGER},
            #{item.businessId,jdbcType=BIGINT}, #{item.businessName,jdbcType=VARCHAR}, #{item.businessCode,jdbcType=VARCHAR},
            #{item.platformOrgId,jdbcType=BIGINT}, #{item.platformName,jdbcType=VARCHAR},
            #{item.goodsNo,jdbcType=VARCHAR}, #{item.barCode,jdbcType=VARCHAR},
            #{item.curName,jdbcType=VARCHAR}, #{item.goodsName,jdbcType=VARCHAR}, #{item.goodsUnit,jdbcType=VARCHAR},
            #{item.specifications,jdbcType=VARCHAR}, #{item.manufacturer,jdbcType=VARCHAR},
            #{item.extend,jdbcType=VARCHAR}, #{item.version,jdbcType=INTEGER}, #{item.createdBy,jdbcType=BIGINT},
            #{item.createdName,jdbcType=VARCHAR}, #{item.updatedBy,jdbcType=BIGINT}, #{item.updatedName,jdbcType=VARCHAR}
        )
        </foreach>
    </insert>
    <update id="updateStoreType">
        update iscm_store_apply_param_goods_valid_whitelist
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="store_type=case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    WHEN id=#{item.id,jdbcType=BIGINT} then #{item.storeType}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="selectCountByStoreTypes" resultMap="BaseResultMap">
        select *
          from iscm_store_apply_param_goods_valid_whitelist t1
         where t1.org_id = #{orgId}
           and t1.business_id = #{businessId}
           and t1.goods_no in (
            <foreach collection="goodsNos" item="item" index="index" separator="," >
            #{item}
            </foreach>
             )
           and (
                t1.store_type = #{storeType}
                <foreach collection="storeTypes" item="item" index="index" separator=" " >
                    or t1.store_type like concat('%', concat(#{item}, '%'))
                </foreach>
               )
    </select>
    <select id="selectWhiteGoodsInfoByOrgId"
            resultType="com.cowell.iscm.service.dto.applyParam.WhiteGoodsNoListDTO">
        select a.goods_no as goodsNo, a.upper_quantity as upperQuantity, a.store_type as storeType from iscm_store_apply_param_goods_valid_whitelist a, iscm_store_apply_param_goods_valid_whitelist_org b
        where a.id = b.whitelist_ID
        and b.rule_org_id = #{orgId ,jdbcType=BIGINT}
        and #{dateStart, jdbcType=TIMESTAMP} >= a.start_date
        and a.end_date >= #{dateEnd, jdbcType=TIMESTAMP}
        order by a.id asc
    </select>

</mapper>
