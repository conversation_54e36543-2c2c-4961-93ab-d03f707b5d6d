<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.extend.SapEkbeExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.SapEkbe">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="MANDT" jdbcType="VARCHAR" property="mandt" />
    <result column="EBELN" jdbcType="VARCHAR" property="ebeln" />
    <result column="EBELP" jdbcType="VARCHAR" property="ebelp" />
    <result column="ZEKKN" jdbcType="VARCHAR" property="zekkn" />
    <result column="VGABE" jdbcType="VARCHAR" property="vgabe" />
    <result column="GJAHR" jdbcType="VARCHAR" property="gjahr" />
    <result column="BELNR" jdbcType="VARCHAR" property="belnr" />
    <result column="BUZEI" jdbcType="VARCHAR" property="buzei" />
    <result column="BEWTP" jdbcType="VARCHAR" property="bewtp" />
    <result column="BWART" jdbcType="VARCHAR" property="bwart" />
    <result column="BUDAT" jdbcType="VARCHAR" property="budat" />
    <result column="MENGE" jdbcType="DECIMAL" property="menge" />
    <result column="BPMNG" jdbcType="DECIMAL" property="bpmng" />
    <result column="DMBTR" jdbcType="DECIMAL" property="dmbtr" />
    <result column="WRBTR" jdbcType="DECIMAL" property="wrbtr" />
    <result column="WAERS" jdbcType="VARCHAR" property="waers" />
    <result column="AREWR" jdbcType="DECIMAL" property="arewr" />
    <result column="WESBS" jdbcType="DECIMAL" property="wesbs" />
    <result column="BPWES" jdbcType="DECIMAL" property="bpwes" />
    <result column="SHKZG" jdbcType="VARCHAR" property="shkzg" />
    <result column="BWTAR" jdbcType="VARCHAR" property="bwtar" />
    <result column="ELIKZ" jdbcType="VARCHAR" property="elikz" />
    <result column="XBLNR" jdbcType="VARCHAR" property="xblnr" />
    <result column="LFGJA" jdbcType="VARCHAR" property="lfgja" />
    <result column="LFBNR" jdbcType="VARCHAR" property="lfbnr" />
    <result column="LFPOS" jdbcType="VARCHAR" property="lfpos" />
    <result column="GRUND" jdbcType="VARCHAR" property="grund" />
    <result column="CPUDT" jdbcType="VARCHAR" property="cpudt" />
    <result column="CPUTM" jdbcType="VARCHAR" property="cputm" />
    <result column="REEWR" jdbcType="DECIMAL" property="reewr" />
    <result column="EVERE" jdbcType="VARCHAR" property="evere" />
    <result column="REFWR" jdbcType="DECIMAL" property="refwr" />
    <result column="MATNR" jdbcType="VARCHAR" property="matnr" />
    <result column="WERKS" jdbcType="VARCHAR" property="werks" />
    <result column="XWSBR" jdbcType="VARCHAR" property="xwsbr" />
    <result column="ETENS" jdbcType="VARCHAR" property="etens" />
    <result column="KNUMV" jdbcType="VARCHAR" property="knumv" />
    <result column="MWSKZ" jdbcType="VARCHAR" property="mwskz" />
    <result column="LSMNG" jdbcType="DECIMAL" property="lsmng" />
    <result column="LSMEH" jdbcType="VARCHAR" property="lsmeh" />
    <result column="EMATN" jdbcType="VARCHAR" property="ematn" />
    <result column="AREWW" jdbcType="DECIMAL" property="areww" />
    <result column="HSWAE" jdbcType="VARCHAR" property="hswae" />
    <result column="BAMNG" jdbcType="DECIMAL" property="bamng" />
    <result column="CHARG" jdbcType="VARCHAR" property="charg" />
    <result column="BLDAT" jdbcType="VARCHAR" property="bldat" />
    <result column="XWOFF" jdbcType="VARCHAR" property="xwoff" />
    <result column="XUNPL" jdbcType="VARCHAR" property="xunpl" />
    <result column="ERNAM" jdbcType="VARCHAR" property="ernam" />
    <result column="SRVPOS" jdbcType="VARCHAR" property="srvpos" />
    <result column="PACKNO" jdbcType="VARCHAR" property="packno" />
    <result column="INTROW" jdbcType="VARCHAR" property="introw" />
    <result column="BEKKN" jdbcType="VARCHAR" property="bekkn" />
    <result column="LEMIN" jdbcType="VARCHAR" property="lemin" />
    <result column="AREWB" jdbcType="DECIMAL" property="arewb" />
    <result column="REWRB" jdbcType="DECIMAL" property="rewrb" />
    <result column="SAPRL" jdbcType="VARCHAR" property="saprl" />
    <result column="MENGE_POP" jdbcType="DECIMAL" property="mengePop" />
    <result column="BPMNG_POP" jdbcType="DECIMAL" property="bpmngPop" />
    <result column="DMBTR_POP" jdbcType="DECIMAL" property="dmbtrPop" />
    <result column="WRBTR_POP" jdbcType="DECIMAL" property="wrbtrPop" />
    <result column="WESBB" jdbcType="DECIMAL" property="wesbb" />
    <result column="BPWEB" jdbcType="DECIMAL" property="bpweb" />
    <result column="WEORA" jdbcType="VARCHAR" property="weora" />
    <result column="AREWR_POP" jdbcType="DECIMAL" property="arewrPop" />
    <result column="KUDIF" jdbcType="DECIMAL" property="kudif" />
    <result column="RETAMT_FC" jdbcType="DECIMAL" property="retamtFc" />
    <result column="RETAMT_LC" jdbcType="DECIMAL" property="retamtLc" />
    <result column="RETAMTP_FC" jdbcType="DECIMAL" property="retamtpFc" />
    <result column="RETAMTP_LC" jdbcType="DECIMAL" property="retamtpLc" />
    <result column="XMACC" jdbcType="VARCHAR" property="xmacc" />
    <result column="WKURS" jdbcType="DECIMAL" property="wkurs" />
    <result column="INV_ITEM_ORIGIN" jdbcType="VARCHAR" property="invItemOrigin" />
    <result column="VBELN_ST" jdbcType="VARCHAR" property="vbelnSt" />
    <result column="VBELP_ST" jdbcType="VARCHAR" property="vbelpSt" />
    <result column="SGT_SCAT" jdbcType="VARCHAR" property="sgtScat" />
    <result column="DATAAGING" jdbcType="VARCHAR" property="dataaging" />
    <result column="SESUOM" jdbcType="VARCHAR" property="sesuom" />
    <result column="ET_UPD" jdbcType="VARCHAR" property="etUpd" />
    <result column="CWM_BAMNG" jdbcType="DECIMAL" property="cwmBamng" />
    <result column="CWM_WESBS" jdbcType="DECIMAL" property="cwmWesbs" />
    <result column="CWM_TY2TQ" jdbcType="VARCHAR" property="cwmTy2tq" />
    <result column="CWM_WESBB" jdbcType="DECIMAL" property="cwmWesbb" />
    <result column="J_SC_DIE_COMP_F" jdbcType="VARCHAR" property="jScDieCompF" />
    <result column="FSH_SEASON_YEAR" jdbcType="VARCHAR" property="fshSeasonYear" />
    <result column="FSH_SEASON" jdbcType="VARCHAR" property="fshSeason" />
    <result column="FSH_COLLECTION" jdbcType="VARCHAR" property="fshCollection" />
    <result column="FSH_THEME" jdbcType="VARCHAR" property="fshTheme" />
    <result column="QTY_DIFF" jdbcType="DECIMAL" property="qtyDiff" />
    <result column="WRF_CHARSTC1" jdbcType="VARCHAR" property="wrfCharstc1" />
    <result column="WRF_CHARSTC2" jdbcType="VARCHAR" property="wrfCharstc2" />
    <result column="WRF_CHARSTC3" jdbcType="VARCHAR" property="wrfCharstc3" />
    <result column="ZZDATE" jdbcType="VARCHAR" property="zzdate" />
    <result column="ZZTIME" jdbcType="VARCHAR" property="zztime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, MANDT, EBELN, EBELP, ZEKKN, VGABE, GJAHR, BELNR, BUZEI, BEWTP, BWART, BUDAT, 
    MENGE, BPMNG, DMBTR, WRBTR, WAERS, AREWR, WESBS, BPWES, SHKZG, BWTAR, ELIKZ, XBLNR, 
    LFGJA, LFBNR, LFPOS, GRUND, CPUDT, CPUTM, REEWR, EVERE, REFWR, MATNR, WERKS, XWSBR, 
    ETENS, KNUMV, MWSKZ, LSMNG, LSMEH, EMATN, AREWW, HSWAE, BAMNG, CHARG, BLDAT, XWOFF, 
    XUNPL, ERNAM, SRVPOS, PACKNO, INTROW, BEKKN, LEMIN, AREWB, REWRB, SAPRL, MENGE_POP, 
    BPMNG_POP, DMBTR_POP, WRBTR_POP, WESBB, BPWEB, WEORA, AREWR_POP, KUDIF, RETAMT_FC, 
    RETAMT_LC, RETAMTP_FC, RETAMTP_LC, XMACC, WKURS, INV_ITEM_ORIGIN, VBELN_ST, VBELP_ST, 
    SGT_SCAT, DATAAGING, SESUOM, ET_UPD, CWM_BAMNG, CWM_WESBS, CWM_TY2TQ, CWM_WESBB, 
    J_SC_DIE_COMP_F, FSH_SEASON_YEAR, FSH_SEASON, FSH_COLLECTION, FSH_THEME, QTY_DIFF, 
    WRF_CHARSTC1, WRF_CHARSTC2, WRF_CHARSTC3, ZZDATE, ZZTIME
  </sql>
  <select id="selectDTOByExample" resultType="com.cowell.iscm.service.dto.controlTower.SapEkbeDTO">
    select
    EBELN as ebeln, MATNR as matnr, SHKZG as shkzg ,SUM(MENGE) as menge
    from `SAP_EKBE`
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    GROUP BY EBELN,MATNR,SHKZG
  </select>
</mapper>
