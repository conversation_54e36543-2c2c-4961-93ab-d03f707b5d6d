<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.extend.IscmStoreReturnExecuteOrderExtendMapper">
    <select id="selectWarehouseInfos"
            resultType="com.cowell.iscm.service.dto.returnWarehouse.WarehouseDTO">
        select warehouse_code as warehouseCode, warehouse_name as warehouseName
        from iscm_store_return_execute_order_main
        where created_month = #{month}
          and (warehouse_code is not null and warehouse_code != '')
        group by warehouse_code
    </select>
    <select id="selectCheckedIds" resultType="java.lang.Long" parameterType="com.cowell.iscm.service.dto.returnWarehouse.ReturnWarehouseExecuteParam">
        select id from iscm_store_return_execute_order_main
        <include refid="Query_Where_Clause_ReturnWarehouseExecute" />
    </select>
    <select id="selectCheckedOrderNos" resultType="java.lang.String"
            parameterType="com.cowell.iscm.service.dto.returnWarehouse.ReturnWarehouseExecuteParam">
        select return_order_no from iscm_store_return_execute_order_main
        where created_month = #{month}
        and gmt_create between #{startDate} and #{endDate}
        <if test="createdName != null and createdName != ''">
            and created_name = #{createdName}
        </if>
        <if test="platformOrgId != null">
            and platform_org_id = #{platformOrgId}
        </if>
        <if test="warehouseCode != null and warehouseCode != ''">
            and warehouse_code = #{warehouseCode}
        </if>
        <if test="companyOrgIds != null and companyOrgIds.size() > 0">
            and company_org_id in
            <foreach collection="companyOrgIds" item="companyOrgId" index="index" open="(" close=")" separator=",">
                #{companyOrgId}
            </foreach>
        </if>
        <if test="storeOrgIds != null and storeOrgIds.size() > 0">
            and store_org_id in
            <foreach collection="storeOrgIds" item="storeOrgId" index="index" open="(" close=")" separator=",">
                #{storeOrgId}
            </foreach>
        </if>
        <if test="returnGoodsQuantityTotalCq == 'GT' and returnGoodsQuantityTotal != null">
            and return_goods_quantity_total &gt; #{returnGoodsQuantityTotal}
        </if>
        <if test="returnGoodsQuantityTotalCq == 'LT' and returnGoodsQuantityTotal != null">
            and return_goods_quantity_total &lt; #{returnGoodsQuantityTotal}
        </if>
        <if test="returnCostAmountTotalCq == 'GT' and returnCostAmountTotal != null">
            and return_cost_amount_total &gt; #{returnCostAmountTotal}
        </if>
        <if test="returnCostAmountTotalCq == 'LT' and returnCostAmountTotal != null">
            and return_cost_amount_total &lt; #{returnCostAmountTotal}
        </if>
        <if test="returnType != null">
            and return_type = #{returnType}
        </if>
        <if test="processStatusAll != null">
            and ${processStatusAll}
        </if>
        limit ${page*pageSize}, ${pageSize}
    </select>

    <sql id="Query_Where_Clause_ReturnWarehouseExecute">
        where created_month = #{month}
        and gmt_create between #{startDate} and #{endDate}
        <if test="createdName != null and createdName != ''">
            and created_name = #{createdName}
        </if>
        <if test="platformOrgId != null">
            and platform_org_id = #{platformOrgId}
        </if>
        <if test="warehouseCode != null and warehouseCode != ''">
            and warehouse_code = #{warehouseCode}
        </if>
        <if test="companyOrgIds != null and companyOrgIds.size() > 0">
            and company_org_id in
            <foreach collection="companyOrgIds" item="companyOrgId" index="index" open="(" close=")" separator=",">
                #{companyOrgId}
            </foreach>
        </if>
        <if test="storeOrgIds != null and storeOrgIds.size() > 0">
            and store_org_id in
            <foreach collection="storeOrgIds" item="storeOrgId" index="index" open="(" close=")" separator=",">
                #{storeOrgId}
            </foreach>
        </if>
        <if test="returnGoodsQuantityTotalCq == 'GT' and returnGoodsQuantityTotal != null">
            and return_goods_quantity_total &gt; #{returnGoodsQuantityTotal}
        </if>
        <if test="returnGoodsQuantityTotalCq == 'LT' and returnGoodsQuantityTotal != null">
            and return_goods_quantity_total &lt; #{returnGoodsQuantityTotal}
        </if>
        <if test="returnCostAmountTotalCq == 'GT' and returnCostAmountTotal != null">
            and return_cost_amount_total &gt; #{returnCostAmountTotal}
        </if>
        <if test="returnCostAmountTotalCq == 'LT' and returnCostAmountTotal != null">
            and return_cost_amount_total &lt; #{returnCostAmountTotal}
        </if>
        <if test="processStatusList != null and processStatusList.size() > 0">
            and process_status in
            <foreach collection="processStatusList" item="processStatus" index="index" open="(" close=")" separator=",">
                #{processStatus}
            </foreach>
        </if>
        limit ${page*pageSize}, ${pageSize}
    </sql>
</mapper>
