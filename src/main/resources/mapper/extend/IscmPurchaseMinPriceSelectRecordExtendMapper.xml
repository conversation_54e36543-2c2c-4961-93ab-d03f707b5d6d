<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.extend.IscmPurchaseMinPriceSelectRecordExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.IscmPurchaseMinPriceSelectRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="select_date" jdbcType="DATE" property="selectDate" />
    <result column="select_times" jdbcType="INTEGER" property="selectTimes" />
    <result column="select_param" jdbcType="VARCHAR" property="selectParam" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.cowell.iscm.entity.IscmPurchaseMinPriceSelectRecord">
    <result column="select_result" jdbcType="VARCHAR" property="selectResult" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, select_date, select_times, select_param, `status`, gmt_create, gmt_update, extend, 
    version, created_by, created_name, updated_by, updated_name
  </sql>
  <sql id="Blob_Column_List">
    select_result
  </sql>
  <select id="selectWithoutWhiteListTimesByDate" resultType="com.cowell.iscm.service.dto.purchaseMinPrice.MonitorMinPriceDTO">
    select select_date as selectDate, created_by as createdBy, created_name as createdName, max(select_times) as selectTimes
    from iscm_purchase_min_price_select_record
    where select_date between #{startDate, jdbcType=DATE} and #{endDate, jdbcType=DATE}
    <if test="timesLimit != null">
    and select_times >= #{timesLimit}
    </if>
    <if test="userIds != null and userIds.size > 0">
      and created_by not in
      <foreach collection="userIds" item="userId" index="index" open="(" close=")" separator=",">
        #{userId}
      </foreach>
    </if>
    group by created_by, select_date
  </select>
  <select id="selectWhiteListTimesByDate"
          resultType="com.cowell.iscm.service.dto.purchaseMinPrice.MonitorMinPriceDTO">
        select select_date as selectDate, created_by as createdBy, created_name as createdName, max(select_times) as selectTimes
    from iscm_purchase_min_price_select_record
    where select_date between #{startDate, jdbcType=DATE} and #{endDate ,jdbcType=DATE}
    and select_times >= #{timesLimit}
    <if test="userIds != null and userIds.size > 0">
      and created_by in
      <foreach collection="userIds" item="userId" index="index" open="(" close=")" separator=",">
        #{userId}
      </foreach>
    </if>
    group by created_by, select_date
  </select>
</mapper>
