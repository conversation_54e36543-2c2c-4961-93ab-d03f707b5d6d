<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.extend.IscmStoreReturnConfirmOrderDetailExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.IscmStoreReturnConfirmOrderDetail">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="return_confirm_order_no" jdbcType="VARCHAR" property="returnConfirmOrderNo" />
    <result column="return_business_type" jdbcType="TINYINT" property="returnBusinessType" />
    <result column="platform_org_id" jdbcType="BIGINT" property="platformOrgId" />
    <result column="platform_org_name" jdbcType="VARCHAR" property="platformOrgName" />
    <result column="company_org_id" jdbcType="BIGINT" property="companyOrgId" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="store_org_id" jdbcType="BIGINT" property="storeOrgId" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="store_name" jdbcType="VARCHAR" property="storeName" />
    <result column="warehouse_code" jdbcType="VARCHAR" property="warehouseCode" />
    <result column="warehouse_name" jdbcType="VARCHAR" property="warehouseName" />
    <result column="register_order_no" jdbcType="VARCHAR" property="registerOrderNo" />
    <result column="register_quantity" jdbcType="DECIMAL" property="registerQuantity" />
    <result column="cost_amount" jdbcType="DECIMAL" property="costAmount" />
    <result column="data_type" jdbcType="TINYINT" property="dataType" />
    <result column="register_date" jdbcType="DATE" property="registerDate" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="forecast_sales" jdbcType="DECIMAL" property="forecastSales" />
    <result column="bar_code" jdbcType="VARCHAR" property="barCode" />
    <result column="goods_common_name" jdbcType="VARCHAR" property="goodsCommonName" />
    <result column="goods_unit" jdbcType="VARCHAR" property="goodsUnit" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="specifications" jdbcType="VARCHAR" property="specifications" />
    <result column="dosage_form" jdbcType="VARCHAR" property="dosageForm" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="approval_number" jdbcType="VARCHAR" property="approvalNumber" />
    <result column="goods_class_id" jdbcType="BIGINT" property="goodsClassId" />
    <result column="goods_class_name" jdbcType="VARCHAR" property="goodsClassName" />
    <result column="goods_pur_channel" jdbcType="VARCHAR" property="goodsPurChannel" />
    <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
    <result column="validity_date" jdbcType="TIMESTAMP" property="validityDate" />
    <result column="produce_date" jdbcType="TIMESTAMP" property="produceDate" />
    <result column="validity_days" jdbcType="INTEGER" property="validityDays" />
    <result column="stock_upper_limit_days" jdbcType="INTEGER" property="stockUpperLimitDays" />
    <result column="stock_lower_limit_days" jdbcType="INTEGER" property="stockLowerLimitDays" />
    <result column="hd_synthesize_average_daily_sales" jdbcType="DECIMAL" property="hdSynthesizeAverageDailySales" />
    <result column="stock_upper_limit" jdbcType="DECIMAL" property="stockUpperLimit" />
    <result column="stock_lower_limit" jdbcType="DECIMAL" property="stockLowerLimit" />
    <result column="bdp_synthesize_average_daily_sales" jdbcType="DECIMAL" property="bdpSynthesizeAverageDailySales" />
    <result column="storage_days" jdbcType="INTEGER" property="storageDays" />
    <result column="non_sales_days" jdbcType="INTEGER" property="nonSalesDays" />
    <result column="should_return_quantity" jdbcType="DECIMAL" property="shouldReturnQuantity" />
    <result column="return_quantity" jdbcType="DECIMAL" property="returnQuantity" />
    <result column="return_amount" jdbcType="DECIMAL" property="returnAmount" />
    <result column="goods_stock" jdbcType="DECIMAL" property="goodsStock" />
    <result column="batch_stock" jdbcType="DECIMAL" property="batchStock" />
    <result column="thirty_sales_quantity" jdbcType="DECIMAL" property="thirtySalesQuantity" />
    <result column="thirty_sales_count" jdbcType="INTEGER" property="thirtySalesCount" />
    <result column="min_display_quantity" jdbcType="DECIMAL" property="minDisplayQuantity" />
    <result column="store_retain_sales_days" jdbcType="INTEGER" property="storeRetainSalesDays" />
    <result column="goodsline" jdbcType="VARCHAR" property="goodsline" />
    <result column="pushlevel" jdbcType="VARCHAR" property="pushlevel" />
    <result column="goods_level" jdbcType="INTEGER" property="goodsLevel" />
    <result column="forbid_distribute" jdbcType="VARCHAR" property="forbidDistribute" />
    <result column="forbid_return_warehouse" jdbcType="VARCHAR" property="forbidReturnWarehouse" />
    <result column="forbid_apply" jdbcType="VARCHAR" property="forbidApply" />
    <result column="forbid_allot" jdbcType="VARCHAR" property="forbidAllot" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, return_confirm_order_no, return_business_type, platform_org_id, platform_org_name,
    company_org_id, company_code, company_name, store_org_id, store_code, store_name,
    warehouse_code, warehouse_name, register_order_no, register_quantity, cost_amount,
    data_type, register_date, goods_no, goods_name, forecast_sales, bar_code, goods_common_name,
    goods_unit, description, specifications, dosage_form, manufacturer, approval_number,
    goods_class_id, goods_class_name, goods_pur_channel, batch_no, validity_date, produce_date,
    validity_days, stock_upper_limit_days, stock_lower_limit_days, hd_synthesize_average_daily_sales,
    stock_upper_limit, stock_lower_limit, bdp_synthesize_average_daily_sales, storage_days,
    non_sales_days, should_return_quantity, return_quantity, return_amount, goods_stock, batch_stock,
    thirty_sales_quantity, thirty_sales_count, min_display_quantity, store_retain_sales_days,
    goodsline, pushlevel, goods_level, forbid_distribute, forbid_return_warehouse, forbid_apply,
    forbid_allot, `status`, gmt_create, gmt_update, extend, version, created_by, created_name,
    updated_by, updated_name
  </sql>
  <insert id="batchInsert" parameterType="java.util.List">
    insert into iscm_store_return_confirm_order_detail (return_confirm_order_no, return_business_type, platform_org_id,
      platform_org_name, company_org_id, company_code,
      company_name, store_org_id, store_code,
      store_name, warehouse_code, warehouse_name,
      register_order_no, register_quantity, cost_amount,
      data_type, register_date, goods_no,
      goods_name, forecast_sales, bar_code,
      goods_common_name, goods_unit, description,
      specifications, dosage_form, manufacturer,
      approval_number, goods_class_id, goods_class_name, goods_pur_channel,
      batch_no, validity_date, produce_date,
      validity_days, stock_upper_limit_days,
      stock_lower_limit_days, hd_synthesize_average_daily_sales,
      stock_upper_limit, stock_lower_limit, bdp_synthesize_average_daily_sales,
      storage_days, non_sales_days, should_return_quantity, return_quantity, return_amount, goods_stock,
      batch_stock, thirty_sales_quantity, thirty_sales_count,
      min_display_quantity, store_retain_sales_days, goodsline, pushlevel, goods_level,
      forbid_distribute, forbid_return_warehouse,
      forbid_apply, forbid_allot, created_by, created_name,
      updated_by, updated_name)
    values
    <foreach collection="list" item="item" index="index" separator="," >
    (#{item.returnConfirmOrderNo,jdbcType=VARCHAR}, #{item.returnBusinessType,jdbcType=TINYINT}, #{item.platformOrgId,jdbcType=BIGINT},
      #{item.platformOrgName,jdbcType=VARCHAR}, #{item.companyOrgId,jdbcType=BIGINT}, #{item.companyCode,jdbcType=VARCHAR},
      #{item.companyName,jdbcType=VARCHAR}, #{item.storeOrgId,jdbcType=BIGINT}, #{item.storeCode,jdbcType=VARCHAR},
      #{item.storeName,jdbcType=VARCHAR}, #{item.warehouseCode,jdbcType=VARCHAR}, #{item.warehouseName,jdbcType=VARCHAR},
      #{item.registerOrderNo,jdbcType=VARCHAR}, #{item.registerQuantity,jdbcType=DECIMAL}, #{item.costAmount,jdbcType=DECIMAL},
      #{item.dataType,jdbcType=TINYINT}, #{item.registerDate,jdbcType=DATE}, #{item.goodsNo,jdbcType=VARCHAR},
      #{item.goodsName,jdbcType=VARCHAR}, #{item.forecastSales,jdbcType=DECIMAL}, #{item.barCode,jdbcType=VARCHAR},
      #{item.goodsCommonName,jdbcType=VARCHAR}, #{item.goodsUnit,jdbcType=VARCHAR}, #{item.description,jdbcType=VARCHAR},
      #{item.specifications,jdbcType=VARCHAR}, #{item.dosageForm,jdbcType=VARCHAR}, #{item.manufacturer,jdbcType=VARCHAR},
      #{item.approvalNumber,jdbcType=VARCHAR}, #{item.goodsClassId,jdbcType=BIGINT}, #{item.goodsClassName,jdbcType=VARCHAR}, #{item.goodsPurChannel,jdbcType=VARCHAR},
      #{item.batchNo,jdbcType=VARCHAR}, #{item.validityDate,jdbcType=TIMESTAMP}, #{item.produceDate,jdbcType=TIMESTAMP},
      #{item.validityDays,jdbcType=INTEGER}, #{item.stockUpperLimitDays,jdbcType=INTEGER},
      #{item.stockLowerLimitDays,jdbcType=INTEGER}, #{item.hdSynthesizeAverageDailySales,jdbcType=DECIMAL},
      #{item.stockUpperLimit,jdbcType=DECIMAL}, #{item.stockLowerLimit,jdbcType=DECIMAL}, #{item.bdpSynthesizeAverageDailySales,jdbcType=DECIMAL},
      #{item.storageDays,jdbcType=INTEGER}, #{item.nonSalesDays,jdbcType=INTEGER}, #{item.shouldReturnQuantity,jdbcType=DECIMAL}, #{item.returnQuantity,jdbcType=DECIMAL}, #{item.returnAmount,jdbcType=DECIMAL}, #{item.goodsStock,jdbcType=DECIMAL},
      #{item.batchStock,jdbcType=DECIMAL}, #{item.thirtySalesQuantity,jdbcType=DECIMAL}, #{item.thirtySalesCount,jdbcType=INTEGER},
      #{item.minDisplayQuantity,jdbcType=DECIMAL}, #{item.storeRetainSalesDays,jdbcType=INTEGER}, #{item.goodsline,jdbcType=VARCHAR}, #{item.pushlevel,jdbcType=VARCHAR}, #{item.goodsLevel,jdbcType=INTEGER},
      #{item.forbidDistribute,jdbcType=VARCHAR}, #{item.forbidReturnWarehouse,jdbcType=VARCHAR},
      #{item.forbidApply,jdbcType=VARCHAR}, #{item.forbidAllot,jdbcType=VARCHAR}, #{item.createdBy,jdbcType=BIGINT}, #{item.createdName,jdbcType=VARCHAR},
      #{item.updatedBy,jdbcType=BIGINT}, #{item.updatedName,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <select id="selectAutoChooseList" resultType="com.cowell.iscm.service.dto.returnWarehouse.AutoChooseDTO">
    select
    id, company_org_id as companyOrgId, store_org_id as storeOrgId, validity_days as validityDays, should_return_quantity as shouldReturnQuantity, return_quantity as returnQuantity,
    goods_stock + should_return_quantity as stockFirst, goods_stock/bdp_synthesize_average_daily_sales as salesFirst, non_sales_days as nonSalesDays, forbid_return_warehouse as forbidReturnWarehouse
    from iscm_store_return_confirm_order_detail
    where return_confirm_order_no = #{returnConfirmOrderNo}
    and company_org_id in
    <foreach collection="companyOrgIds" item="companyOrgId" index="index" open="(" close=")" separator=",">
      #{companyOrgId}
    </foreach>
    and validity_days >= #{warehouseValidityDaysMin}
    and return_quantity > 0
    order by store_code DESC, validity_days DESC
    <!-- <choose>
      <when test="companyOrgIds != null and companyOrgIds.size > 0">
        order by FIELD(company_org_id,
        <foreach collection="companyOrgIds" item="companyOrgId" index="index" separator="," >
          #{companyOrgId}
        </foreach>
        )
      </when>
      <otherwise>
        <if test="returnDistrType == '门店库存数量优先'.toString()">
          order by stockFirst DESC
        </if>
        <if test="returnDistrType == '门店滞销天数优先'.toString()">
          order by non_sales_days DESC
        </if>
        <if test="returnDistrType == '门店库存可销天数优先'.toString()">
          order by salesFirst DESC
        </if>
      </otherwise>
    </choose> -->
  </select>
  <select id="selectAutoChooseOrderList" resultType="com.cowell.iscm.service.dto.returnWarehouse.AutoChooseDTO">
    select
    store_org_id as storeOrgId, company_org_id as companyOrgId, max(goods_stock) / max(bdp_synthesize_average_daily_sales) as salesFirst
    <!-- , sum(should_return_quantity) as shouldReturnQuantity, goods_stock/hd_synthesize_average_daily_sales as salesFirst -->
    from iscm_store_return_confirm_order_detail
    where return_confirm_order_no = #{returnConfirmOrderNo}
    and company_org_id in
    <foreach collection="companyOrgIds" item="companyOrgId" index="index" open="(" close=")" separator=",">
      #{companyOrgId}
    </foreach>
    and validity_days >= #{warehouseValidityDaysMin}
    and return_quantity > 0
    group by store_code, company_org_id
    <if test="returnDistrType == '门店库存数量优先'.toString()">
      order by sum(should_return_quantity) DESC
    </if>
    <if test="returnDistrType == '门店滞销天数优先'.toString()">
      order by non_sales_days DESC
    </if>
  </select>
    <select id="selectSumAmountAndQuantity"
            resultType="com.cowell.iscm.service.dto.returnWarehouse.ReturnDetailCountDTO">
      select
       IFNULL(sum(cost_amount/register_quantity*return_quantity),0) as totalCostAmount, IFNULL(sum(return_quantity),0) as totalReturnQuantity
      from iscm_store_return_confirm_order_detail
      <if test="_parameter != null">
        <include refid="Example_Where_Clause"/>
      </if>
    </select>
  <select id="selectReturnOrderNos" resultType="java.lang.String">
    select return_confirm_order_no
    from iscm_store_return_confirm_order_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
</mapper>
