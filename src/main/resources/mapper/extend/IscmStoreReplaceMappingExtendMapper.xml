<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.extend.IscmStoreReplaceMappingExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.IscmStoreReplaceMapping">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="platform_org_id" jdbcType="BIGINT" property="platformOrgId" />
    <result column="platform_name" jdbcType="VARCHAR" property="platformName" />
    <result column="company_org_id" jdbcType="BIGINT" property="companyOrgId" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="store_org_id" jdbcType="BIGINT" property="storeOrgId" />
    <result column="store_org_name" jdbcType="VARCHAR" property="storeOrgName" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="replace_store_org_id" jdbcType="BIGINT" property="replaceStoreOrgId" />
    <result column="replace_store_org_name" jdbcType="VARCHAR" property="replaceStoreOrgName" />
    <result column="replace_store_code" jdbcType="VARCHAR" property="replaceStoreCode" />
    <result column="replace_start_date" jdbcType="DATE" property="replaceStartDate" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, platform_org_id, platform_name, company_org_id, company_name, company_code, store_org_id, 
    store_org_name, store_code, replace_store_org_id, replace_store_org_name, replace_store_code, 
    replace_start_date, `status`, gmt_create, gmt_update, extend, version, created_by, 
    created_name, updated_by, updated_name
  </sql>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entity.IscmStoreReplaceMapping" useGeneratedKeys="true">
    insert into iscm_store_replace_mapping (platform_org_id, platform_name, company_org_id, 
      company_name, company_code, store_org_id, 
      store_org_name, store_code, replace_store_org_id, 
      replace_store_org_name, replace_store_code, 
      replace_start_date, `status`,
      created_by, created_name, updated_by, 
      updated_name)
    values
    <foreach collection="list" item="item" index="index" separator="," >
    (#{item.platformOrgId,jdbcType=BIGINT}, #{item.platformName,jdbcType=VARCHAR}, #{item.companyOrgId,jdbcType=BIGINT},
      #{item.companyName,jdbcType=VARCHAR}, #{item.companyCode,jdbcType=VARCHAR}, #{item.storeOrgId,jdbcType=BIGINT},
      #{item.storeOrgName,jdbcType=VARCHAR}, #{item.storeCode,jdbcType=VARCHAR}, #{item.replaceStoreOrgId,jdbcType=BIGINT},
      #{item.replaceStoreOrgName,jdbcType=VARCHAR}, #{item.replaceStoreCode,jdbcType=VARCHAR},
      #{item.replaceStartDate,jdbcType=DATE}, #{item.status,jdbcType=TINYINT},
      #{item.createdBy,jdbcType=BIGINT}, #{item.createdName,jdbcType=VARCHAR}, #{item.updatedBy,jdbcType=BIGINT},
      #{item.updatedName,jdbcType=VARCHAR})
    </foreach>
  </insert>
</mapper>
