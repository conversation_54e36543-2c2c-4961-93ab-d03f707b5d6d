<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.extend.BdpAvgDailySalesExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.BdpAvgDailySales">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="store_org_id" jdbcType="BIGINT" property="storeOrgId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="store_attr" jdbcType="TINYINT" property="storeAttr" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="special_ctrl" jdbcType="TINYINT" property="specialCtrl" />
    <result column="distr_forbid" jdbcType="TINYINT" property="distrForbid" />
    <result column="apply_forbid" jdbcType="TINYINT" property="applyForbid" />
    <result column="valuable" jdbcType="TINYINT" property="valuable" />
    <result column="dtp_goods" jdbcType="TINYINT" property="dtpGoods" />
    <result column="coldchainind" jdbcType="TINYINT" property="coldchainind" />
    <result column="special_attr" jdbcType="VARCHAR" property="specialAttr" />
    <result column="purchase_type" jdbcType="VARCHAR" property="purchaseType" />
    <result column="goods_level" jdbcType="TINYINT" property="goodsLevel" />
    <result column="goods_status" jdbcType="TINYINT" property="goodsStatus" />
    <result column="lawful" jdbcType="TINYINT" property="lawful" />
    <result column="newable" jdbcType="TINYINT" property="newable" />
    <result column="category_id" jdbcType="BIGINT" property="categoryId" />
    <result column="middle_category_id" jdbcType="BIGINT" property="middleCategoryId" />
    <result column="small_category_id" jdbcType="BIGINT" property="smallCategoryId" />
    <result column="sub_category_id" jdbcType="BIGINT" property="subCategoryId" />
    <result column="push_level" jdbcType="VARCHAR" property="pushLevel" />
    <result column="goods_line" jdbcType="TINYINT" property="goodsLine" />
    <result column="middle_package_qty" jdbcType="DECIMAL" property="middlePackageQty" />
    <result column="middle_package_switch_biz" jdbcType="VARCHAR" property="middlePackageSwitchBiz" />
    <result column="middle_package_switch_store" jdbcType="VARCHAR" property="middlePackageSwitchStore" />
    <result column="apply_ratio" jdbcType="DECIMAL" property="applyRatio" />
    <result column="special_thirty_days_qty" jdbcType="DECIMAL" property="specialThirtyDaysQty" />
    <result column="stock_upper_limit_days" jdbcType="INTEGER" property="stockUpperLimitDays" />
    <result column="stock_lower_limit_days" jdbcType="INTEGER" property="stockLowerLimitDays" />
    <result column="average_daily_sales" jdbcType="DECIMAL" property="averageDailySales" />
    <result column="min_display_qty" jdbcType="DECIMAL" property="minDisplayQty" />
    <result column="stock_upper_limit" jdbcType="DECIMAL" property="stockUpperLimit" />
    <result column="stock_lower_limit" jdbcType="DECIMAL" property="stockLowerLimit" />
    <result column="thirty_days_sales" jdbcType="DECIMAL" property="thirtyDaysSales" />
    <result column="sixty_days_sales" jdbcType="DECIMAL" property="sixtyDaysSales" />
    <result column="ninety_days_sales" jdbcType="DECIMAL" property="ninetyDaysSales" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
      and (average_daily_sales > 0 or min_display_qty > 0)
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, company_code, store_org_id, store_id, store_code, store_attr, goods_no, special_ctrl, distr_forbid,
    apply_forbid, valuable, dtp_goods, coldchainind, special_attr, purchase_type, goods_level,
    goods_status, lawful, newable, category_id, middle_category_id, small_category_id,
    sub_category_id, push_level, goods_line, middle_package_qty, middle_package_switch_biz,
    middle_package_switch_store, apply_ratio, special_thirty_days_qty, stock_upper_limit_days, stock_lower_limit_days,
    average_daily_sales, min_display_qty, stock_upper_limit, stock_lower_limit, thirty_days_sales, sixty_days_sales,
    ninety_days_sales, gmt_create, gmt_update
  </sql>
    <update id="updateStoreAttr">
      update bdp_avg_daily_sales set store_attr = #{storeAttr, jdbcType=TINYINT} where store_id = #{storeId, jdbcType=BIGINT}
    </update>
    <select id="selectByExample" parameterType="com.cowell.iscm.entity.BdpAvgDailySalesExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from bdp_avg_daily_sales
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
    <select id="selectAvgSalesByStoreIdAndGoodsNo"
            resultType="com.cowell.iscm.service.dto.allotextend.AvgSalesDTO">
      select store_id as storeId, goods_no as goodsNo, average_daily_sales as averageDailySales, min_display_qty as minDisplayQty from bdp_avg_daily_sales
      where store_id = #{storeId,jdbcType=BIGINT}
      and goods_no = #{goodsNo,jdbcType=VARCHAR}
    </select>
    <insert id="batchInsert" parameterType="com.cowell.iscm.entity.BdpAvgDailySales">
    insert into bdp_avg_daily_sales (id, company_code, store_org_id, 
      store_id, store_code, store_attr, goods_no,
      special_ctrl, distr_forbid, apply_forbid, 
      valuable, dtp_goods, coldchainind, 
      special_attr, purchase_type, goods_level, 
      goods_status, lawful, newable, 
      category_id, middle_category_id, small_category_id, 
      sub_category_id, push_level, goods_line, 
      middle_package_qty, middle_package_switch_biz, 
      middle_package_switch_store, apply_ratio, special_thirty_days_qty,
      stock_upper_limit_days, stock_lower_limit_days, 
      average_daily_sales, min_display_qty, stock_upper_limit, 
      stock_lower_limit, thirty_days_sales, sixty_days_sales, ninety_days_sales)
    values
    <foreach collection="list" item="item" index="index" separator="," >
      (#{item.id,jdbcType=BIGINT}, #{item.companyCode,jdbcType=VARCHAR}, #{item.storeOrgId,jdbcType=BIGINT},
      #{item.storeId,jdbcType=BIGINT}, #{item.storeCode,jdbcType=VARCHAR}, #{item.storeAttr,jdbcType=TINYINT}, #{item.goodsNo,jdbcType=VARCHAR},
      #{item.specialCtrl,jdbcType=TINYINT}, #{item.distrForbid,jdbcType=TINYINT}, #{item.applyForbid,jdbcType=TINYINT},
      #{item.valuable,jdbcType=TINYINT}, #{item.dtpGoods,jdbcType=TINYINT}, #{item.coldchainind,jdbcType=TINYINT},
      #{item.specialAttr,jdbcType=VARCHAR}, #{item.purchaseType,jdbcType=VARCHAR}, #{item.goodsLevel,jdbcType=TINYINT},
      #{item.goodsStatus,jdbcType=TINYINT}, #{item.lawful,jdbcType=TINYINT}, #{item.newable,jdbcType=TINYINT},
      #{item.categoryId,jdbcType=BIGINT}, #{item.middleCategoryId,jdbcType=BIGINT}, #{item.smallCategoryId,jdbcType=BIGINT},
      #{item.subCategoryId,jdbcType=BIGINT}, #{item.pushLevel,jdbcType=VARCHAR}, #{item.goodsLine,jdbcType=TINYINT},
      #{item.middlePackageQty,jdbcType=DECIMAL}, #{item.middlePackageSwitchBiz,jdbcType=VARCHAR},
      #{item.middlePackageSwitchStore,jdbcType=VARCHAR}, #{item.applyRatio,jdbcType=DECIMAL}, #{item.specialThirtyDaysQty,jdbcType=DECIMAL},
      #{item.stockUpperLimitDays,jdbcType=INTEGER}, #{item.stockLowerLimitDays,jdbcType=INTEGER},
      #{item.averageDailySales,jdbcType=DECIMAL}, #{item.minDisplayQty,jdbcType=DECIMAL}, #{item.stockUpperLimit,jdbcType=DECIMAL},
      #{item.stockLowerLimit,jdbcType=DECIMAL}, #{item.thirtyDaysSales,jdbcType=DECIMAL}, #{item.sixtyDaysSales,jdbcType=DECIMAL}, #{item.ninetyDaysSales,jdbcType=DECIMAL})
    </foreach>
  </insert>
</mapper>
