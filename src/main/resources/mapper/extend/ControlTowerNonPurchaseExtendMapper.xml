<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.extend.ControlTowerNonPurchaseExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.ControlTowerNonPurchase">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="param_unique_mark" jdbcType="VARCHAR" property="paramUniqueMark" />
    <result column="werks" jdbcType="VARCHAR" property="werks" />
    <result column="matnr" jdbcType="VARCHAR" property="matnr" />
    <result column="matnr_desc" jdbcType="VARCHAR" property="matnrDesc" />
    <result column="suggest_dhl" jdbcType="DECIMAL" property="suggestDhl" />
    <result column="avg_qty" jdbcType="VARCHAR" property="avgQty" />
    <result column="goods_level" jdbcType="VARCHAR" property="goodsLevel" />
    <result column="inv_upper" jdbcType="DECIMAL" property="invUpper" />
    <result column="weiqingcaigou_7" jdbcType="DECIMAL" property="weiqingcaigou7" />
    <result column="total_dc_stock" jdbcType="VARCHAR" property="totalDcStock" />
    <result column="total_dc_disable_stock" jdbcType="VARCHAR" property="totalDcDisableStock" />
    <result column="dc_stock" jdbcType="DECIMAL" property="dcStock" />
    <result column="dc_disable_stock" jdbcType="DECIMAL" property="dcDisableStock" />
    <result column="total_store_stock" jdbcType="VARCHAR" property="totalStoreStock" />
    <result column="jm_store_stock" jdbcType="DECIMAL" property="jmStoreStock" />
    <result column="lifnr" jdbcType="VARCHAR" property="lifnr" />
    <result column="qty_before_7" jdbcType="DECIMAL" property="qtyBefore7" />
    <result column="qty_before_14" jdbcType="DECIMAL" property="qtyBefore14" />
    <result column="qty_before_30" jdbcType="VARCHAR" property="qtyBefore30" />
    <result column="hb_sale_rate" jdbcType="VARCHAR" property="hbSaleRate" />
    <result column="tb_sale_rate" jdbcType="VARCHAR" property="tbSaleRate" />
    <result column="zdxmds" jdbcType="DECIMAL" property="zdxmds" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_by_id" jdbcType="BIGINT" property="createById" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_by_id" jdbcType="BIGINT" property="updateById" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, param_unique_mark, werks, matnr, matnr_desc, suggest_dhl, avg_qty, goods_level, 
    inv_upper, weiqingcaigou_7, total_dc_stock, total_dc_disable_stock, dc_stock, dc_disable_stock, 
    total_store_stock, jm_store_stock, lifnr, qty_before_7, qty_before_14, qty_before_30, 
    hb_sale_rate, tb_sale_rate, zdxmds, `status`, gmt_create, gmt_update, create_by, 
    create_by_id, update_by, update_by_id, version, extend
  </sql>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entity.ControlTowerNonPurchase" useGeneratedKeys="true">
    insert into control_tower_non_purchase (param_unique_mark, werks, matnr, 
      matnr_desc, suggest_dhl, avg_qty, 
      goods_level, inv_upper, weiqingcaigou_7, 
      total_dc_stock, total_dc_disable_stock, dc_stock, 
      dc_disable_stock, total_store_stock, jm_store_stock, 
      lifnr, qty_before_7, qty_before_14, 
      qty_before_30, hb_sale_rate, tb_sale_rate, 
      zdxmds, create_by, create_by_id,
      update_by, update_by_id, extend)
    values
    <foreach collection="list" item="item" index="index" separator=",">
    (#{item.paramUniqueMark,jdbcType=VARCHAR}, #{item.werks,jdbcType=VARCHAR}, #{item.matnr,jdbcType=VARCHAR},
      #{item.matnrDesc,jdbcType=VARCHAR}, #{item.suggestDhl,jdbcType=DECIMAL}, #{item.avgQty,jdbcType=VARCHAR},
      #{item.goodsLevel,jdbcType=VARCHAR}, #{item.invUpper,jdbcType=DECIMAL}, #{item.weiqingcaigou7,jdbcType=DECIMAL},
      #{item.totalDcStock,jdbcType=VARCHAR}, #{item.totalDcDisableStock,jdbcType=VARCHAR}, #{item.dcStock,jdbcType=DECIMAL},
      #{item.dcDisableStock,jdbcType=DECIMAL}, #{item.totalStoreStock,jdbcType=VARCHAR}, #{item.jmStoreStock,jdbcType=DECIMAL},
      #{item.lifnr,jdbcType=VARCHAR}, #{item.qtyBefore7,jdbcType=DECIMAL}, #{item.qtyBefore14,jdbcType=DECIMAL},
      #{item.qtyBefore30,jdbcType=VARCHAR}, #{item.hbSaleRate,jdbcType=VARCHAR}, #{item.tbSaleRate,jdbcType=VARCHAR},
      #{item.zdxmds,jdbcType=DECIMAL}, #{item.createBy,jdbcType=VARCHAR}, #{item.createById,jdbcType=BIGINT},
      #{item.updateBy,jdbcType=VARCHAR}, #{item.updateById,jdbcType=BIGINT}, #{item.extend,jdbcType=VARCHAR})
    </foreach>
  </insert>
</mapper>
