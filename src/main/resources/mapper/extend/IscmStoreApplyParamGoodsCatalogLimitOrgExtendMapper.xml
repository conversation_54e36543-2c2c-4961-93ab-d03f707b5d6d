<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.extend.IscmStoreApplyParamGoodsCatalogLimitOrgExtendMapper">
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <insert id="batchInsert" parameterType="list" >
        insert into iscm_store_apply_param_goods_catalog_limit_org (
            param_code, param_name, param_level,
            org_id, sap_code, org_name,
            parent_org_id, parent_org_name,
            store_org_id, store_code, store_name, store_id,
            goods_no, bar_code, cur_name, goods_name,
            goods_unit, specifications, manufacturer,
            lower_limit_days, upper_limit_days,
            created_by, created_name,
            updated_by, updated_name)
        values
        <foreach collection="list" item="item" index="index" separator="," >
            (
            #{item.paramCode,jdbcType=VARCHAR}, #{item.paramName,jdbcType=VARCHAR}, #{item.paramLevel,jdbcType=INTEGER},
            #{item.orgId,jdbcType=BIGINT}, #{item.sapCode,jdbcType=VARCHAR}, #{item.orgName,jdbcType=VARCHAR},
            #{item.parentOrgId,jdbcType=BIGINT}, #{item.parentOrgName,jdbcType=VARCHAR},
            #{item.storeOrgId,jdbcType=BIGINT}, #{item.storeCode,jdbcType=VARCHAR}, #{item.storeName,jdbcType=VARCHAR}, #{item.storeId,jdbcType=BIGINT},
            #{item.goodsNo,jdbcType=VARCHAR}, #{item.barCode,jdbcType=VARCHAR}, #{item.curName,jdbcType=VARCHAR}, #{item.goodsName,jdbcType=VARCHAR},
            #{item.goodsUnit,jdbcType=VARCHAR}, #{item.specifications,jdbcType=VARCHAR}, #{item.manufacturer,jdbcType=VARCHAR},
            #{item.lowerLimitDays,jdbcType=INTEGER}, #{item.upperLimitDays,jdbcType=INTEGER},
            #{item.createdBy,jdbcType=BIGINT}, #{item.createdName,jdbcType=VARCHAR},
            #{item.updatedBy,jdbcType=BIGINT}, #{item.updatedName,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>
    <delete id="deleteByExample" parameterType="com.cowell.iscm.entity.IscmStoreApplyParamGoodsCatalogLimitOrgExample">
        delete from iscm_store_apply_param_goods_catalog_limit_org
        <if test="_parameter != null">
            <include refid="Example_Where_Clause" />
        </if>
        <if test="limit != null">
            limit ${limit}
        </if>
    </delete>

    <select id="selectCheckedIds" resultType="java.lang.Long" parameterType="java.lang.Long">
        select id from iscm_store_apply_param_goods_catalog_limit_org where org_id = #{orgId}
    </select>
    <select id="getOrgIdByIds" resultType="java.lang.Long">
        select org_id from iscm_store_apply_param_goods_catalog_limit_org
        where id in
        <foreach collection="ids" item="id" index="index" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>
</mapper>
