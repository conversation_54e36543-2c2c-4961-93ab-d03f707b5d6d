<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.extend.IscmManualRegisterdRecordExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.IscmManualRegisterdRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.cowell.iscm.entity.IscmManualRegisterdRecord">
    <result column="manual_id" jdbcType="LONGVARCHAR" property="manualId" />
  </resultMap>
  <sql id="Base_Column_List">
    id, store_code, gmt_create
  </sql>
  <sql id="Blob_Column_List">
    manual_id
  </sql>

  <select id="selectManualIdByStoreCodeAndDate" resultType="java.lang.String">
    select
    manual_id
    from iscm_manual_registerd_record
    where store_code = #{storeCode}
    and gmt_create between #{dateStart} and #{dateEnd}
  </select>

  <update id="updateManualIdByStoreCodeAndDateAndDate">
    update iscm_manual_registerd_record set manual_id = #{manualId,jdbcType=VARCHAR}
   where store_code = #{storeCode}
    and gmt_create between #{dateStart} and #{dateEnd}
  </update>

  <insert id="insertRecord" parameterType="com.cowell.iscm.entity.IscmManualRegisterdRecord">
    insert into iscm_manual_registerd_record (store_code,
      manual_id)
    values (#{storeCode,jdbcType=VARCHAR},
      #{manualId,jdbcType=VARCHAR})
  </insert>


  <delete id="deleteTodayBeforeData" parameterType="java.util.Date">
     delete from iscm_manual_registerd_record where gmt_create &lt; #{todayStartTime}
  </delete>

</mapper>
