<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.extend.ControlTowerRealDiffExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.ControlTowerRealDiff">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="param_unique_mark" jdbcType="VARCHAR" property="paramUniqueMark" />
    <result column="purreqno" jdbcType="VARCHAR" property="purreqno" />
    <result column="storelineno" jdbcType="VARCHAR" property="storelineno" />
    <result column="bukrs" jdbcType="VARCHAR" property="bukrs" />
    <result column="ekorg" jdbcType="VARCHAR" property="ekorg" />
    <result column="werks" jdbcType="VARCHAR" property="werks" />
    <result column="zzzzshd" jdbcType="VARCHAR" property="zzzzshd" />
    <result column="badat" jdbcType="VARCHAR" property="badat" />
    <result column="matnr" jdbcType="VARCHAR" property="matnr" />
    <result column="matnr_desc" jdbcType="VARCHAR" property="matnrDesc" />
    <result column="menge" jdbcType="DECIMAL" property="menge" />
    <result column="zspsl" jdbcType="DECIMAL" property="zspsl" />
    <result column="shkzg" jdbcType="VARCHAR" property="shkzg" />
    <result column="send_qty" jdbcType="DECIMAL" property="sendQty" />
    <result column="ebeln" jdbcType="VARCHAR" property="ebeln" />
    <result column="ebelp" jdbcType="VARCHAR" property="ebelp" />
    <result column="meins" jdbcType="VARCHAR" property="meins" />
    <result column="erdat" jdbcType="VARCHAR" property="erdat" />
    <result column="erzet" jdbcType="VARCHAR" property="erzet" />
    <result column="ernam" jdbcType="VARCHAR" property="ernam" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_by_id" jdbcType="BIGINT" property="createById" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_by_id" jdbcType="BIGINT" property="updateById" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, param_unique_mark, purreqno, storelineno, bukrs, ekorg, werks, zzzzshd, badat,
    matnr, matnr_desc, menge, zspsl, shkzg, send_qty, ebeln, ebelp, meins, erdat, erzet,
    ernam, `status`, gmt_create, gmt_update, create_by, create_by_id, update_by, update_by_id,
    version, extend
  </sql>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entity.ControlTowerRealDiff" useGeneratedKeys="true">
    insert into control_tower_real_diff (param_unique_mark, purreqno, storelineno, 
      bukrs, ekorg, werks, 
      zzzzshd, badat, matnr, matnr_desc,
      menge, zspsl, shkzg, 
      send_qty, ebeln, ebelp, 
      meins, erdat, erzet, 
      ernam, create_by, create_by_id,
      update_by, update_by_id, extend)
    values
    <foreach collection="list" item="item" index="index" separator=",">
    (#{item.paramUniqueMark,jdbcType=VARCHAR}, #{item.purreqno,jdbcType=VARCHAR}, #{item.storelineno,jdbcType=VARCHAR},
      #{item.bukrs,jdbcType=VARCHAR}, #{item.ekorg,jdbcType=VARCHAR}, #{item.werks,jdbcType=VARCHAR},
      #{item.zzzzshd,jdbcType=VARCHAR}, #{item.badat,jdbcType=VARCHAR}, #{item.matnr,jdbcType=VARCHAR}, #{item.matnrDesc,jdbcType=VARCHAR},
      #{item.menge,jdbcType=DECIMAL}, #{item.zspsl,jdbcType=DECIMAL}, #{item.shkzg,jdbcType=VARCHAR},
      #{item.sendQty,jdbcType=DECIMAL}, #{item.ebeln,jdbcType=VARCHAR}, #{item.ebelp,jdbcType=VARCHAR},
      #{item.meins,jdbcType=VARCHAR}, #{item.erdat,jdbcType=VARCHAR}, #{item.erzet,jdbcType=VARCHAR},
      #{item.ernam,jdbcType=VARCHAR}, #{item.createBy,jdbcType=VARCHAR}, #{item.createById,jdbcType=BIGINT},
      #{item.updateBy,jdbcType=VARCHAR}, #{item.updateById,jdbcType=BIGINT}, #{item.extend,jdbcType=VARCHAR})
    </foreach>
  </insert>
</mapper>
