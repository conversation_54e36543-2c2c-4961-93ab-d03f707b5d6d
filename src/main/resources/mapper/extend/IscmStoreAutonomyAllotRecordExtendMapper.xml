<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.extend.IscmStoreAutonomyAllotRecordExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.IscmStoreAutonomyAllotRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="autonomy_allot_no" jdbcType="VARCHAR" property="autonomyAllotNo" />
    <result column="allot_type" jdbcType="TINYINT" property="allotType" />
    <result column="platform_org_id" jdbcType="BIGINT" property="platformOrgId" />
    <result column="platform_org_name" jdbcType="VARCHAR" property="platformOrgName" />
    <result column="out_company_id" jdbcType="BIGINT" property="outCompanyId" />
    <result column="out_company_code" jdbcType="VARCHAR" property="outCompanyCode" />
    <result column="out_company_name" jdbcType="VARCHAR" property="outCompanyName" />
    <result column="in_company_id" jdbcType="BIGINT" property="inCompanyId" />
    <result column="in_company_code" jdbcType="VARCHAR" property="inCompanyCode" />
    <result column="in_company_name" jdbcType="VARCHAR" property="inCompanyName" />
    <result column="out_store_id" jdbcType="BIGINT" property="outStoreId" />
    <result column="out_store_code" jdbcType="VARCHAR" property="outStoreCode" />
    <result column="out_store_name" jdbcType="VARCHAR" property="outStoreName" />
    <result column="in_store_id" jdbcType="BIGINT" property="inStoreId" />
    <result column="in_store_code" jdbcType="VARCHAR" property="inStoreCode" />
    <result column="in_store_name" jdbcType="VARCHAR" property="inStoreName" />
    <result column="suggest_allot_quantity_all" jdbcType="DECIMAL" property="suggestAllotQuantityAll" />
    <result column="suggest_allot_base_cost" jdbcType="DECIMAL" property="suggestAllotBaseCost" />
    <result column="suggest_allot_breed_all" jdbcType="INTEGER" property="suggestAllotBreedAll" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, autonomy_allot_no, allot_type, platform_org_id, platform_org_name, out_company_id, 
    out_company_code, out_company_name, in_company_id, in_company_code, in_company_name, 
    out_store_id, out_store_code, out_store_name, in_store_id, in_store_code, in_store_name, 
    suggest_allot_quantity_all, suggest_allot_base_cost, suggest_allot_breed_all, `status`, 
    extend, version, created_by, created_name, updated_by, updated_name, gmt_create, 
    gmt_update
  </sql>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entity.IscmStoreAutonomyAllotRecord" useGeneratedKeys="true">
    insert into iscm_store_autonomy_allot_record (autonomy_allot_no, allot_type, platform_org_id, 
      platform_org_name, out_company_id, out_company_code, 
      out_company_name, in_company_id, in_company_code, 
      in_company_name, out_store_id, out_store_code, 
      out_store_name, in_store_id, in_store_code, 
      in_store_name, suggest_allot_quantity_all, suggest_allot_base_cost, 
      suggest_allot_breed_all, created_by, created_name,
      updated_by, updated_name)
    values
    <foreach collection="list" item="item" index="index" separator="," >
    (#{item.autonomyAllotNo,jdbcType=VARCHAR}, #{item.allotType,jdbcType=TINYINT}, #{item.platformOrgId,jdbcType=BIGINT},
      #{item.platformOrgName,jdbcType=VARCHAR}, #{item.outCompanyId,jdbcType=BIGINT}, #{item.outCompanyCode,jdbcType=VARCHAR},
      #{item.outCompanyName,jdbcType=VARCHAR}, #{item.inCompanyId,jdbcType=BIGINT}, #{item.inCompanyCode,jdbcType=VARCHAR},
      #{item.inCompanyName,jdbcType=VARCHAR}, #{item.outStoreId,jdbcType=BIGINT}, #{item.outStoreCode,jdbcType=VARCHAR},
      #{item.outStoreName,jdbcType=VARCHAR}, #{item.inStoreId,jdbcType=BIGINT}, #{item.inStoreCode,jdbcType=VARCHAR},
      #{item.inStoreName,jdbcType=VARCHAR}, #{item.suggestAllotQuantityAll,jdbcType=DECIMAL}, #{item.suggestAllotBaseCost,jdbcType=DECIMAL},
      #{item.suggestAllotBreedAll,jdbcType=INTEGER}, #{item.createdBy,jdbcType=BIGINT}, #{item.createdName,jdbcType=VARCHAR},
      #{item.updatedBy,jdbcType=BIGINT}, #{item.updatedName,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <update id="batchUpdateTotal">
    update iscm_store_autonomy_allot_record
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="suggest_allot_quantity_all=case" suffix="end,">
        <foreach collection="list" item="item" index="index">
          WHEN autonomy_allot_no=#{item.autonomyAllotNo,jdbcType=VARCHAR} then suggest_allot_quantity_all - #{item.suggestAllotQuantityAll,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="suggest_allot_base_cost=case" suffix="end,">
        <foreach collection="list" item="item" index="index">
          WHEN autonomy_allot_no=#{item.autonomyAllotNo,jdbcType=VARCHAR} then suggest_allot_base_cost - #{item.suggestAllotBaseCost,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="suggest_allot_breed_all=case" suffix="end,">
        <foreach collection="list" item="item" index="index">
          WHEN autonomy_allot_no=#{item.autonomyAllotNo,jdbcType=VARCHAR} then suggest_allot_breed_all - #{item.suggestAllotBreedAll,jdbcType=INTEGER}
        </foreach>
      </trim>
    </trim>
    where autonomy_allot_no in
    <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
      #{item.autonomyAllotNo,jdbcType=VARCHAR}
    </foreach>
  </update>
</mapper>
