<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.extend.BdpGoodsGiftDomainStoreExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.BdpGoodsGiftDomainStore">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="domain_id" jdbcType="BIGINT" property="domainId" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="begin_time" jdbcType="TIMESTAMP" property="beginTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="platform_id" jdbcType="BIGINT" property="platformId" />
    <result column="platform_name" jdbcType="VARCHAR" property="platformName" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="store_name" jdbcType="VARCHAR" property="storeName" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>

  <select id="selectDomainIdByParam"  parameterType="com.cowell.iscm.service.dto.scib.ScibGoodsGiftRequestParam" resultType="java.lang.Long">
    select  s.domain_id  from bdp_goods_gift_domain_store AS s  left join  bdp_goods_gift_domain_detail AS d  on  s.domain_id=d.domain_id
     <where>
       <if test="giftGoodsNo != null and giftGoodsNo != ''">
         AND  d.gift_goods_no = #{giftGoodsNo}
       </if>
      <if test="goodsNo != null and goodsNo != ''">
        AND  s.goods_no = #{goodsNo}
      </if>
      <if test="queryBeginTimeStart != null and queryBeginTimeStart != '' and queryBeginTimeEnd!= null and queryBeginTimeEnd != '' ">
        AND s.begin_time BETWEEN  #{queryBeginTimeStart}  AND #{queryBeginTimeEnd}
      </if>
      <if test="queryEndTimeStart != null and queryEndTimeStart != '' and queryEndTimeEnd!= null and queryEndTimeEnd != '' ">
        AND s.end_time BETWEEN  #{queryEndTimeStart}  AND   #{queryEndTimeEnd}
      </if>
      <if test="companyOrgIds != null and companyOrgIds.size > 0">
        AND s.company_id IN
        <foreach collection="companyOrgIds" item="companyOrgId" index="index" open="(" close=")" separator=",">
          #{companyOrgId}
        </foreach>
      </if>
      <if test="status != null">
        AND  s.status = #{status}
      </if>
    </where>
    group by s.domain_id
    order by s.domain_id desc
    <if test="page != null">
      <if test="pageSize != null">
        limit ${page * pageSize}, ${pageSize}
      </if>
    </if>
  </select>

  <select id="selectDomainIdByNonGiftInfo"  parameterType="com.cowell.iscm.service.dto.scib.ScibGoodsGiftRequestParam" resultType="java.lang.Long">
    select  domain_id  from bdp_goods_gift_domain_store
    <where>
      <if test="goodsNo != null and goodsNo != ''">
        AND  goods_no = #{goodsNo}
      </if>
      <if test="queryBeginTimeStart != null and queryBeginTimeStart != '' and queryBeginTimeEnd!= null and queryBeginTimeEnd != '' ">
        AND begin_time BETWEEN  #{queryBeginTimeStart}  AND  #{queryBeginTimeEnd}
      </if>

      <if test="queryEndTimeStart != null and queryEndTimeStart != '' and queryEndTimeEnd!= null and queryEndTimeEnd != '' ">
        AND end_time BETWEEN  #{queryEndTimeStart}  AND  #{queryEndTimeEnd}
      </if>
      <if test="companyOrgIds != null and companyOrgIds.size > 0">
        AND company_id IN
        <foreach collection="companyOrgIds" item="companyOrgId" index="index" open="(" close=")" separator=",">
          #{companyOrgId}
        </foreach>
      </if>
      <if test="status != null">
        AND  status = #{status}
      </if>
    </where>
    group by domain_id
    order by domain_id desc
    <if test="page != null">
      <if test="pageSize != null">
        limit ${page * pageSize}, ${pageSize}
      </if>
    </if>
  </select>

  <select id="selectDomainIdByStoreAndGiftList"  parameterType="com.cowell.iscm.service.dto.scib.ScibGoodsGiftRequestParam" resultType="java.lang.Long">
    select  s.domain_id  from bdp_goods_gift_domain_store AS s  left join  bdp_goods_gift_domain_detail AS d  on  s.domain_id=d.domain_id
    <where>
      <if test="goodsNo != null and goodsNo != ''">
        AND  s.goods_no = #{goodsNo}
      </if>
      <if test="queryBeginTimeStart != null and queryBeginTimeStart != '' and queryEndTimeEnd!= null and queryEndTimeEnd != '' ">
        AND  s.begin_time  <![CDATA[ <= ]]>  #{queryEndTimeEnd}    AND   s.end_time <![CDATA[ >= ]]>  #{queryBeginTimeStart}
      </if>
      <if test="storeOrgIds != null and storeOrgIds.size > 0">
        AND s.store_id IN
        <foreach collection="storeOrgIds" item="storeOrgId" index="index" open="(" close=")" separator=",">
          #{storeOrgId}
        </foreach>
      </if>
      <if test="giftGoodsNoList != null and giftGoodsNoList.size > 0">
        AND  d.gift_goods_no IN
        <foreach collection="giftGoodsNoList" item="giftGoodsNo" index="index" open="(" close=")" separator=",">
          #{giftGoodsNo}
        </foreach>
      </if>
      <if test="status != null">
        AND  s.status = #{status}
      </if>
    </where>
    group by s.domain_id
    order by s.domain_id desc
    <if test="page != null">
      <if test="pageSize != null">
        limit ${page * pageSize}, ${pageSize}
      </if>
    </if>
  </select>
  <select id="countDomainIdByParam" parameterType="com.cowell.iscm.service.dto.scib.ScibGoodsGiftRequestParam"  resultType="java.lang.Long">
    select count(*) from (
      select s.domain_id from bdp_goods_gift_domain_store AS s left join bdp_goods_gift_domain_detail AS d on
    s.domain_id=d.domain_id
    <where><if test="giftGoodsNo != null and giftGoodsNo != ''">          AND  d.gift_goods_no = #{giftGoodsNo}
      </if>
      <if test="goodsNo != null and goodsNo != ''">
        AND  s.goods_no = #{goodsNo}
      </if>
      <if test="queryBeginTimeStart != null and queryBeginTimeStart != '' and queryBeginTimeEnd!= null and queryBeginTimeEnd != '' ">
        AND s.begin_time BETWEEN  #{queryBeginTimeStart}  AND #{queryBeginTimeEnd}
      </if>
      <if test="queryEndTimeStart != null and queryEndTimeStart != '' and queryEndTimeEnd!= null and queryEndTimeEnd != '' ">
        AND s.end_time BETWEEN  #{queryEndTimeStart}  AND   #{queryEndTimeEnd}
      </if>
      <if test="companyOrgIds != null and companyOrgIds.size > 0">
        AND s.company_id IN
        <foreach collection="companyOrgIds" item="companyOrgId" index="index" open="(" close=")" separator=",">
          #{companyOrgId}
        </foreach>
      </if>
      <if test="status != null">
        AND  s.status = #{status}
      </if>
    </where>
    group by s.domain_id
    ) as a
  </select>
  <select id="countDomainIdByNonGiftInfo" parameterType="com.cowell.iscm.service.dto.scib.ScibGoodsGiftRequestParam" resultType="java.lang.Long">
    select count(*) from (
      select domain_id from bdp_goods_gift_domain_store
    <where><if test="goodsNo != null and goodsNo != ''">          AND  goods_no = #{goodsNo}
      </if>
      <if test="queryBeginTimeStart != null and queryBeginTimeStart != '' and queryBeginTimeEnd!= null and queryBeginTimeEnd != '' ">
        AND begin_time BETWEEN  #{queryBeginTimeStart}  AND  #{queryBeginTimeEnd}
      </if>

      <if test="queryEndTimeStart != null and queryEndTimeStart != '' and queryEndTimeEnd!= null and queryEndTimeEnd != '' ">
        AND end_time BETWEEN  #{queryEndTimeStart}  AND  #{queryEndTimeEnd}
      </if>
      <if test="companyOrgIds != null and companyOrgIds.size > 0">
        AND company_id IN
        <foreach collection="companyOrgIds" item="companyOrgId" index="index" open="(" close=")" separator=",">
          #{companyOrgId}
        </foreach>
      </if>
      <if test="status != null">
        AND  status = #{status}
      </if>
    </where>
    group by domain_id
    ) as a
  </select>

</mapper>
