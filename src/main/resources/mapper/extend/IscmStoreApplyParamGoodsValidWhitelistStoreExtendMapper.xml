<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.extend.IscmStoreApplyParamGoodsValidWhitelistStoreExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.IscmStoreApplyParamGoodsValidWhitelistStore">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="param_code" jdbcType="VARCHAR" property="paramCode" />
    <result column="param_name" jdbcType="VARCHAR" property="paramName" />
    <result column="param_level" jdbcType="INTEGER" property="paramLevel" />
    <result column="org_id" jdbcType="BIGINT" property="orgId" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="start_date" jdbcType="TIMESTAMP" property="startDate" />
    <result column="end_date" jdbcType="TIMESTAMP" property="endDate" />
    <result column="store_type" jdbcType="VARCHAR" property="storeType" />
    <result column="upper_quantity" jdbcType="INTEGER" property="upperQuantity" />
    <result column="business_id" jdbcType="BIGINT" property="businessId" />
    <result column="business_name" jdbcType="VARCHAR" property="businessName" />
    <result column="business_code" jdbcType="VARCHAR" property="businessCode" />
    <result column="platform_org_id" jdbcType="BIGINT" property="platformOrgId" />
    <result column="platform_name" jdbcType="VARCHAR" property="platformName" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="bar_code" jdbcType="VARCHAR" property="barCode" />
    <result column="cur_name" jdbcType="VARCHAR" property="curName" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="goods_unit" jdbcType="VARCHAR" property="goodsUnit" />
    <result column="specifications" jdbcType="VARCHAR" property="specifications" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="store_name" jdbcType="VARCHAR" property="storeName" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="store_comid" jdbcType="VARCHAR" property="storeComid" />
  </resultMap>


  <insert id="batchInsert" parameterType="list" keyProperty="id" useGeneratedKeys="false">
    insert into iscm_store_apply_param_goods_valid_whitelist_store (
      param_code, param_name, param_level,
      org_id, org_name, start_date,
      end_date, store_type, upper_quantity,
      business_id, business_name, business_code,
      platform_org_id, platform_name, goods_no,
      bar_code, cur_name, goods_name,
      goods_unit, specifications, manufacturer,
      `status`, extend, version,
      created_by, created_name, updated_by,
      updated_name,
      store_id, store_name, store_code,
      store_comid
    )
    values
    <foreach collection="list" item="item" index="index" separator="," >
      (
      #{item.paramCode,jdbcType=VARCHAR}, #{item.paramName,jdbcType=VARCHAR}, #{item.paramLevel,jdbcType=INTEGER},
      #{item.orgId,jdbcType=BIGINT}, #{item.orgName,jdbcType=VARCHAR}, #{item.startDate,jdbcType=TIMESTAMP},
      #{item.endDate,jdbcType=TIMESTAMP}, #{item.storeType,jdbcType=VARCHAR}, #{item.upperQuantity,jdbcType=INTEGER},
      #{item.businessId,jdbcType=BIGINT}, #{item.businessName,jdbcType=VARCHAR}, #{item.businessCode,jdbcType=VARCHAR},
      #{item.platformOrgId,jdbcType=BIGINT}, #{item.platformName,jdbcType=VARCHAR}, #{item.goodsNo,jdbcType=VARCHAR},
      #{item.barCode,jdbcType=VARCHAR}, #{item.curName,jdbcType=VARCHAR}, #{item.goodsName,jdbcType=VARCHAR},
      #{item.goodsUnit,jdbcType=VARCHAR}, #{item.specifications,jdbcType=VARCHAR}, #{item.manufacturer,jdbcType=VARCHAR},
      #{item.status,jdbcType=TINYINT}, #{item.extend,jdbcType=VARCHAR}, #{item.version,jdbcType=INTEGER},
      #{item.createdBy,jdbcType=BIGINT}, #{item.createdName,jdbcType=VARCHAR}, #{item.updatedBy,jdbcType=BIGINT},
      #{item.updatedName,jdbcType=VARCHAR},
      #{item.storeId,jdbcType=BIGINT}, #{item.storeName,jdbcType=VARCHAR}, #{item.storeCode,jdbcType=VARCHAR},
      #{item.storeComid,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>
</mapper>