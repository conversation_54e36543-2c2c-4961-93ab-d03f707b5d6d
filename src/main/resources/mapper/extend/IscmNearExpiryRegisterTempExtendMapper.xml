<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.extend.IscmNearExpiryRegisterTempExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.IscmNearExpiryRegisterTemp">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="org_id" jdbcType="BIGINT" property="orgId" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="platform_org_id" jdbcType="BIGINT" property="platformOrgId" />
    <result column="platform_org_name" jdbcType="VARCHAR" property="platformOrgName" />
    <result column="company_org_id" jdbcType="BIGINT" property="companyOrgId" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="store_org_id" jdbcType="BIGINT" property="storeOrgId" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="allot_group_code" jdbcType="VARCHAR" property="allotGroupCode" />
    <result column="allot_group_name" jdbcType="VARCHAR" property="allotGroupName" />
    <result column="store_name" jdbcType="VARCHAR" property="storeName" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="bar_code" jdbcType="VARCHAR" property="barCode" />
    <result column="goods_common_name" jdbcType="VARCHAR" property="goodsCommonName" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="goods_unit" jdbcType="VARCHAR" property="goodsUnit" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="specifications" jdbcType="VARCHAR" property="specifications" />
    <result column="dosage_form" jdbcType="VARCHAR" property="dosageForm" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="approval_number" jdbcType="VARCHAR" property="approvalNumber" />
    <result column="habitat" jdbcType="VARCHAR" property="habitat" />
    <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
    <result column="validity_date" jdbcType="TIMESTAMP" property="validityDate" />
    <result column="produce_date" jdbcType="TIMESTAMP" property="produceDate" />
    <result column="register_quantity" jdbcType="DECIMAL" property="registerQuantity" />
    <result column="stock_quantity" jdbcType="DECIMAL" property="stockQuantity" />
    <result column="stock_upper_limit" jdbcType="DECIMAL" property="stockUpperLimit" />
    <result column="stock_lower_limit" jdbcType="DECIMAL" property="stockLowerLimit" />
    <result column="stock_cost_amount" jdbcType="DECIMAL" property="stockCostAmount" />
    <result column="cost_amount" jdbcType="DECIMAL" property="costAmount" />
    <result column="register_status" jdbcType="TINYINT" property="registerStatus" />
    <result column="register_error_reason" jdbcType="VARCHAR" property="registerErrorReason" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>

  <sql id="Base_Column_List">
    id, org_id, org_name, platform_org_id, platform_org_name, company_org_id, company_code, 
    company_name, store_org_id, store_code, allot_group_code, allot_group_name, store_name,
    goods_no, bar_code, goods_common_name, goods_name, goods_unit, description, specifications,
    dosage_form, manufacturer, approval_number, habitat, batch_no, validity_date, produce_date,
    register_quantity, stock_quantity, stock_upper_limit, stock_lower_limit, stock_cost_amount,
    cost_amount, register_status, register_error_reason, `status`, gmt_create, gmt_update,
    extend, version, created_by, created_name, updated_by, updated_name
  </sql>
  <insert id="batchInsert" parameterType="com.cowell.iscm.entity.IscmNearExpiryRegisterTemp">
    insert into iscm_near_expiry_register_temp (org_id, org_name,
      platform_org_id, platform_org_name, company_org_id, 
      company_code, company_name, store_org_id, 
      store_code, allot_group_code, allot_group_name, store_name, goods_no,
      bar_code, goods_common_name, goods_name, 
      goods_unit, description, specifications, 
      dosage_form, manufacturer, approval_number, 
      habitat, batch_no, validity_date, 
      produce_date, register_quantity, stock_quantity, 
      stock_upper_limit, stock_lower_limit, cost_amount, stock_cost_amount,
      register_status, register_error_reason, created_by, created_name,
      updated_by, updated_name)
    values
    <foreach collection="list" item="item" index="index" separator="," >
    (#{item.orgId,jdbcType=BIGINT}, #{item.orgName,jdbcType=VARCHAR},
      #{item.platformOrgId,jdbcType=BIGINT}, #{item.platformOrgName,jdbcType=VARCHAR}, #{item.companyOrgId,jdbcType=BIGINT},
      #{item.companyCode,jdbcType=VARCHAR}, #{item.companyName,jdbcType=VARCHAR}, #{item.storeOrgId,jdbcType=BIGINT},
      #{item.storeCode,jdbcType=VARCHAR}, #{item.allotGroupCode,jdbcType=VARCHAR}, #{item.allotGroupName,jdbcType=VARCHAR}, #{item.storeName,jdbcType=VARCHAR}, #{item.goodsNo,jdbcType=VARCHAR},
      #{item.barCode,jdbcType=VARCHAR}, #{item.goodsCommonName,jdbcType=VARCHAR}, #{item.goodsName,jdbcType=VARCHAR},
      #{item.goodsUnit,jdbcType=VARCHAR}, #{item.description,jdbcType=VARCHAR}, #{item.specifications,jdbcType=VARCHAR},
      #{item.dosageForm,jdbcType=VARCHAR}, #{item.manufacturer,jdbcType=VARCHAR}, #{item.approvalNumber,jdbcType=VARCHAR},
      #{item.habitat,jdbcType=VARCHAR}, #{item.batchNo,jdbcType=VARCHAR}, #{item.validityDate,jdbcType=TIMESTAMP},
      #{item.produceDate,jdbcType=TIMESTAMP}, #{item.registerQuantity,jdbcType=DECIMAL}, #{item.stockQuantity,jdbcType=DECIMAL},
      #{item.stockUpperLimit,jdbcType=DECIMAL}, #{item.stockLowerLimit,jdbcType=DECIMAL}, #{item.costAmount,jdbcType=DECIMAL}, #{item.stockCostAmount,jdbcType=DECIMAL},
      #{item.registerStatus,jdbcType=TINYINT}, #{item.registerErrorReason,jdbcType=VARCHAR}, #{item.createdBy,jdbcType=BIGINT}, #{item.createdName,jdbcType=VARCHAR},
      #{item.updatedBy,jdbcType=BIGINT}, #{item.updatedName,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <update id="batchUpdate">
    update iscm_near_expiry_register_temp
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="register_error_reason=case" suffix="end,">
        <foreach collection="list" item="item" index="index">
          WHEN id=#{item.id,jdbcType=BIGINT} then #{item.registerErrorReason}
        </foreach>
      </trim>
      <trim prefix="register_status=case" suffix="end,">
        <foreach collection="list" item="item" index="index">
          WHEN id=#{item.id,jdbcType=BIGINT} then #{item.registerStatus}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>

  <select id="selectStoreOrgIdsByExample" parameterType="com.cowell.iscm.entity.IscmNearExpiryRegisterTempExample" resultType="com.cowell.iscm.service.dto.StoreCommonDTO">
    select
    store_org_id as storeOrgId, company_org_id as companyOrgId
    from iscm_near_expiry_register_temp
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    group by store_org_id, company_org_id
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>

</mapper>
