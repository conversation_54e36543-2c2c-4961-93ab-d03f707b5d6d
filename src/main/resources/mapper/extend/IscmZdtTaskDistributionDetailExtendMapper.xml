<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.extend.IscmZdtTaskDistributionDetailExtendMapper">
    <insert id="batchInsert" parameterType="com.cowell.iscm.entity.IscmZdtTaskDistributionDetail">
        insert into iscm_zdt_task_distribution_detail
        (
            task_id,
            business_id,
            company_code,
            store_id,
            store_code,
            purchase_no,
            purchase_sn,
            goods_no,
            goods_name,
            sales_attrs,
            goodsline,
            pushlevel,
            specifications,
            manufacturer,
            stock,
            stock_upper_limit,
            stock_lower_limit,
            nearly_thirty_days_XL,
            nearly_thirty_days_KL,
            purchase_quantity,
            approved_quantity,
            distributed_quantity,
            subtract_quantity,
            created_by,
            created_name,
            updated_by,
            updated_name,
            gmt_create,
            gmt_update
        )
        values
        <foreach collection="list" item="item" index="index" separator="," >
        (
            #{item.taskId,jdbcType=BIGINT},
            #{item.businessId,jdbcType=BIGINT},
            #{item.companyCode,jdbcType=VARCHAR},
            #{item.storeId,jdbcType=BIGINT},
            #{item.storeCode,jdbcType=VARCHAR},
            #{item.purchaseNo,jdbcType=VARCHAR},
            #{item.purchaseSn,jdbcType=VARCHAR},
            #{item.goodsNo,jdbcType=VARCHAR},
            #{item.goodsName,jdbcType=VARCHAR},
            #{item.salesAttrs,jdbcType=VARCHAR},
            #{item.goodsline,jdbcType=VARCHAR},
            #{item.pushlevel,jdbcType=VARCHAR},
            #{item.specifications,jdbcType=VARCHAR},
            #{item.manufacturer,jdbcType=VARCHAR},
            #{item.stock,jdbcType=DECIMAL},
            #{item.stockUpperLimit,jdbcType=DECIMAL},
            #{item.stockLowerLimit,jdbcType=DECIMAL},
            #{item.nearlyThirtyDaysXl,jdbcType=DECIMAL},
            #{item.nearlyThirtyDaysKl,jdbcType=DECIMAL},
            #{item.purchaseQuantity,jdbcType=DECIMAL},
            #{item.approvedQuantity,jdbcType=DECIMAL},
            #{item.distributedQuantity,jdbcType=DECIMAL},
            #{item.subtractQuantity,jdbcType=DECIMAL},

            #{item.createdBy,jdbcType=BIGINT},
            #{item.createdName,jdbcType=VARCHAR},
            #{item.updatedBy,jdbcType=BIGINT},
            #{item.updatedName,jdbcType=VARCHAR},
            #{item.gmtCreate,jdbcType=TIMESTAMP},
            #{item.gmtUpdate,jdbcType=TIMESTAMP}
        )
        </foreach>
    </insert>
    <select id="selectDistinctStoreCodeListByTodayTime" resultType="java.lang.String" >
        select
             store_code
        from iscm_zdt_task_distribution_detail
        where gmt_create between #{gmtCreateBegin}  and  #{gmtCreateEnd}

    </select>


</mapper>
