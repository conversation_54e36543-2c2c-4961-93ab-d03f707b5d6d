<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.extend.IscmParamOrgManagementExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.IscmParamOrgManagement">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="param_unique_mark" jdbcType="VARCHAR" property="paramUniqueMark" />
    <result column="param_id" jdbcType="BIGINT" property="paramId" />
    <result column="param_code" jdbcType="VARCHAR" property="paramCode" />
    <result column="param_name" jdbcType="VARCHAR" property="paramName" />
    <result column="param_desc" jdbcType="VARCHAR" property="paramDesc" />
    <result column="param_scope" jdbcType="TINYINT" property="paramScope" />
    <result column="param_level" jdbcType="INTEGER" property="paramLevel" />
    <result column="param_type" jdbcType="TINYINT" property="paramType" />
    <result column="param_source" jdbcType="TINYINT" property="paramSource" />
    <result column="param_sequence" jdbcType="INTEGER" property="paramSequence" />
    <result column="param_order" jdbcType="INTEGER" property="paramOrder" />
    <result column="param_sequence_desc" jdbcType="VARCHAR" property="paramSequenceDesc" />
    <result column="org_id" jdbcType="BIGINT" property="orgId" />
    <result column="sap_code" jdbcType="VARCHAR" property="sapCode" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="bdp_code" jdbcType="VARCHAR" property="bdpCode" />
    <result column="org_param_status" jdbcType="TINYINT" property="orgParamStatus" />
    <result column="effect_date" jdbcType="TIMESTAMP" property="effectDate" />
    <result column="invalid_date" jdbcType="TIMESTAMP" property="invalidDate" />
    <result column="param_value" jdbcType="VARCHAR" property="paramValue" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
  </resultMap>
  <sql id="Base_Column_List">
    id, param_unique_mark, param_id, param_code, param_name, param_desc, param_scope,
    param_level, param_type, param_source, param_sequence, param_order, param_sequence_desc,
    org_id, sap_code, org_name, bdp_code, org_param_status, effect_date, invalid_date,
    param_value, status, extend, version, created_by, created_name, updated_by, updated_name,
    gmt_create, gmt_update
  </sql>
  <insert id="batchInsert" parameterType="com.cowell.iscm.entity.IscmParamOrgManagement">
    insert into iscm_param_org_management (param_unique_mark, param_id,
      param_code, param_name, param_sequence, param_order, param_sequence_desc, org_id, sap_code,
      org_name, bdp_code, org_param_status, 
      effect_date, invalid_date, param_value, 
      status,
      created_by, created_name, updated_by, 
      updated_name, param_desc, param_scope, param_level, param_type, param_source
      )
    values
    <foreach collection="list" item="item" index="index" separator="," >
    (#{item.paramUniqueMark,jdbcType=VARCHAR}, #{item.paramId,jdbcType=BIGINT},
      #{item.paramCode,jdbcType=VARCHAR}, #{item.paramName,jdbcType=VARCHAR}, #{item.paramSequence,jdbcType=INTEGER},
      #{item.paramOrder,jdbcType=INTEGER}, #{item.paramSequenceDesc,jdbcType=VARCHAR}, #{item.orgId,jdbcType=BIGINT}, #{item.sapCode,jdbcType=VARCHAR},
      #{item.orgName,jdbcType=VARCHAR}, #{item.bdpCode,jdbcType=VARCHAR}, #{item.orgParamStatus,jdbcType=TINYINT},
      #{item.effectDate,jdbcType=TIMESTAMP}, #{item.invalidDate,jdbcType=TIMESTAMP}, #{item.paramValue,jdbcType=VARCHAR},
      #{item.status,jdbcType=TINYINT},
      #{item.createdBy,jdbcType=BIGINT}, #{item.createdName,jdbcType=VARCHAR}, #{item.updatedBy,jdbcType=BIGINT},
      #{item.updatedName,jdbcType=VARCHAR}, #{item.paramDesc,jdbcType=VARCHAR}, #{item.paramScope,jdbcType=TINYINT}, #{item.paramLevel,jdbcType=INTEGER},
      #{item.paramType,jdbcType=TINYINT}, #{item.paramSource,jdbcType=TINYINT}
      )
    </foreach>
  </insert>

    <update id="batchUpdateValue">
      update iscm_param_org_management
      <trim prefix="set" suffixOverrides=",">
      <trim prefix="param_value=case" suffix="end,">
        <foreach collection="list" item="item" index="index">
          WHEN param_unique_mark=#{item.paramUniqueMark,jdbcType=VARCHAR} then #{item.paramValue}
        </foreach>
      </trim>
      </trim>
      where param_unique_mark in
      <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
        #{item.paramUniqueMark,jdbcType=VARCHAR}
      </foreach>

    </update>
</mapper>
