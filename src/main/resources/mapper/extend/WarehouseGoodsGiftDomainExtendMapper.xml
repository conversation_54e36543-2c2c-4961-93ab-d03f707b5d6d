<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.extend.WarehouseGoodsGiftDomainExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.WarehouseGoodsGiftDomain">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="platform_org_id" jdbcType="BIGINT" property="platformOrgId" />
    <result column="platform_org_name" jdbcType="VARCHAR" property="platformOrgName" />
    <result column="warehouse_code" jdbcType="VARCHAR" property="warehouseCode" />
    <result column="warehouse_name" jdbcType="VARCHAR" property="warehouseName" />
    <result column="purchase_type" jdbcType="TINYINT" property="purchaseType" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="cur_name" jdbcType="VARCHAR" property="curName" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="specifications" jdbcType="VARCHAR" property="specifications" />
    <result column="quantity" jdbcType="DECIMAL" property="quantity" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="begin_time" jdbcType="DATE" property="beginTime" />
    <result column="end_time" jdbcType="DATE" property="endTime" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_by_id" jdbcType="BIGINT" property="createById" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_by_id" jdbcType="BIGINT" property="updateById" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, platform_org_id, platform_org_name, warehouse_code, warehouse_name, purchase_type, 
    goods_no, `name`, cur_name, manufacturer, specifications, quantity, remark, begin_time, 
    end_time, `status`, gmt_create, gmt_update, create_by, create_by_id, update_by, update_by_id, 
    version, extend
  </sql>
  <update id="batchUpdate">
    update warehouse_goods_gift_domain
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="quantity=case" suffix="end,">
        <foreach collection="list" item="item" index="index">
          WHEN id=#{item.id,jdbcType=BIGINT} then #{item.quantity}
        </foreach>
      </trim>
      <trim prefix="begin_time=case" suffix="end,">
        <foreach collection="list" item="item" index="index">
          WHEN id=#{item.id,jdbcType=BIGINT} then #{item.beginTime}
        </foreach>
      </trim>
      <trim prefix="end_time=case" suffix="end,">
        <foreach collection="list" item="item" index="index">
          WHEN id=#{item.id,jdbcType=BIGINT} then #{item.endTime}
        </foreach>
      </trim>
      <trim prefix="update_by=case" suffix="end,">
        <foreach collection="list" item="item" index="index">
          WHEN id=#{item.id,jdbcType=BIGINT} then #{item.updateBy}
        </foreach>
      </trim>
      <trim prefix="update_by_id=case" suffix="end,">
        <foreach collection="list" item="item" index="index">
          WHEN id=#{item.id,jdbcType=BIGINT} then #{item.updateById}
        </foreach>
      </trim>
      <trim prefix="gmt_update=case" suffix="end,">
        <foreach collection="list" item="item" index="index">
          WHEN id=#{item.id,jdbcType=BIGINT} then #{item.gmtUpdate}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>

  <select id="selectByQuery" parameterType="com.cowell.iscm.entity.WarehouseGoodsGiftDomainExample" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from warehouse_goods_gift_domain
    <include refid="queryWhere" />
    <if test="orderBy != null">
      order by ${orderBy}
    </if>
    limit ${offset}, ${limit}
  </select>
  <select id="countByQuery" resultType="java.lang.Long">
    select count(*) from warehouse_goods_gift_domain
    <include refid="queryWhere" />
  </select>
  <sql id="queryWhere">
    <where>
      and platform_org_id = #{platformOrgId,jdbcType=BIGINT}
      and warehouse_code = #{warehouseCode,jdbcType=VARCHAR}
      <if test="dateStart != null">
        and #{dateStart, jdbcType=DATE} between begin_time and end_time
      </if>
      <if test="dateEnd != null">
        and #{dateEnd, jdbcType=DATE} between begin_time and end_time
      </if>
      <if test="goodsNos != null and goodsNos.size > 0">
        and goods_no in
        <foreach collection="goodsNos" index="index" item="goodsNo" open="("  separator="," close=")">
          #{goodsNo,jdbcType=VARCHAR}
        </foreach>
      </if>
      <if test="ids != null and ids.size > 0">
        and id in
        <foreach collection="ids" index="index" item="id" open="("  separator="," close=")">
          #{id,jdbcType=BIGINT}
        </foreach>
      </if>
      <if test="purchaseType != null">
        and purchase_type = #{purchaseType,jdbcType=TINYINT}
      </if>
    </where>
  </sql>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entity.WarehouseGoodsGiftDomain" useGeneratedKeys="true">
    insert into warehouse_goods_gift_domain (platform_org_id, platform_org_name, warehouse_code, 
      warehouse_name, purchase_type, goods_no, 
      `name`, cur_name, manufacturer, 
      specifications, quantity, remark, 
      begin_time, end_time, create_by,
      create_by_id, update_by, update_by_id)
    values
    <foreach collection="list" item="item" index="index" separator="," >
    (#{item.platformOrgId,jdbcType=BIGINT}, #{item.platformOrgName,jdbcType=VARCHAR}, #{item.warehouseCode,jdbcType=VARCHAR},
      #{item.warehouseName,jdbcType=VARCHAR}, #{item.purchaseType,jdbcType=TINYINT}, #{item.goodsNo,jdbcType=VARCHAR},
      #{item.name,jdbcType=VARCHAR}, #{item.curName,jdbcType=VARCHAR}, #{item.manufacturer,jdbcType=VARCHAR},
      #{item.specifications,jdbcType=VARCHAR}, #{item.quantity,jdbcType=DECIMAL}, #{item.remark,jdbcType=VARCHAR},
      #{item.beginTime,jdbcType=DATE}, #{item.endTime,jdbcType=DATE}, #{item.createBy,jdbcType=VARCHAR},
      #{item.createById,jdbcType=BIGINT}, #{item.updateBy,jdbcType=VARCHAR}, #{item.updateById,jdbcType=BIGINT})
    </foreach>
  </insert>
</mapper>
