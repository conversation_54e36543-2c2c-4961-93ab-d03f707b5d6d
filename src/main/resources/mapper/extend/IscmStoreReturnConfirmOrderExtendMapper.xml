<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.extend.IscmStoreReturnConfirmOrderExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.IscmStoreReturnConfirmOrder">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="return_confirm_order_no" jdbcType="VARCHAR" property="returnConfirmOrderNo" />
    <result column="return_business_type" jdbcType="TINYINT" property="returnBusinessType" />
    <result column="platform_org_id" jdbcType="BIGINT" property="platformOrgId" />
    <result column="platform_org_name" jdbcType="VARCHAR" property="platformOrgName" />
    <result column="warehouse_code" jdbcType="VARCHAR" property="warehouseCode" />
    <result column="warehouse_name" jdbcType="VARCHAR" property="warehouseName" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="forecast_sales" jdbcType="DECIMAL" property="forecastSales" />
    <result column="bar_code" jdbcType="VARCHAR" property="barCode" />
    <result column="goods_common_name" jdbcType="VARCHAR" property="goodsCommonName" />
    <result column="goods_unit" jdbcType="VARCHAR" property="goodsUnit" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="specifications" jdbcType="VARCHAR" property="specifications" />
    <result column="dosage_form" jdbcType="VARCHAR" property="dosageForm" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="approval_number" jdbcType="VARCHAR" property="approvalNumber" />
    <result column="goods_class_id" jdbcType="BIGINT" property="goodsClassId" />
    <result column="goods_class_name" jdbcType="VARCHAR" property="goodsClassName" />
    <result column="goods_pur_channel" jdbcType="VARCHAR" property="goodsPurChannel" />
    <result column="warehouse_stock" jdbcType="DECIMAL" property="warehouseStock" />
    <result column="warehouse_purchase_non_clear_stock" jdbcType="DECIMAL" property="warehousePurchaseNonClearStock" />
    <result column="warehouse_return_non_clear_stock" jdbcType="DECIMAL" property="warehouseReturnNonClearStock" />
    <result column="warehouse_return_non_send_stock" jdbcType="DECIMAL" property="warehouseReturnNonSendStock" />
    <result column="warehouse_validity_days_min" jdbcType="INTEGER" property="warehouseValidityDaysMin" />
    <result column="warehouse_consume_days_max" jdbcType="INTEGER" property="warehouseConsumeDaysMax" />
    <result column="warehouse_suggest_receive_return_quantity" jdbcType="DECIMAL" property="warehouseSuggestReceiveReturnQuantity" />
    <result column="warehouse_plan_receive_return_quantity" jdbcType="DECIMAL" property="warehousePlanReceiveReturnQuantity" />
    <result column="return_distr_type" jdbcType="VARCHAR" property="returnDistrType" />
    <result column="register_month" jdbcType="INTEGER" property="registerMonth" />
    <result column="store_quantity" jdbcType="INTEGER" property="storeQuantity" />
    <result column="detail_lines" jdbcType="INTEGER" property="detailLines" />
    <result column="return_amount" jdbcType="DECIMAL" property="returnAmount" />
    <result column="single_goods_return_amount" jdbcType="DECIMAL" property="singleGoodsReturnAmount" />
    <result column="single_goods_return_qty" jdbcType="DECIMAL" property="singleGoodsReturnQty" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, return_confirm_order_no, return_business_type, platform_org_id, platform_org_name,
    warehouse_code, warehouse_name, goods_no, goods_name, forecast_sales, bar_code, goods_common_name,
    goods_unit, description, specifications, dosage_form, manufacturer, approval_number,
    goods_class_id, goods_class_name, goods_pur_channel, warehouse_stock, warehouse_purchase_non_clear_stock,
    warehouse_return_non_clear_stock, warehouse_return_non_send_stock, warehouse_validity_days_min,
    warehouse_consume_days_max, warehouse_suggest_receive_return_quantity, warehouse_plan_receive_return_quantity,
    return_distr_type, register_month, store_quantity, detail_lines, return_amount, single_goods_return_amount, single_goods_return_qty, `status`,
    gmt_create, gmt_update, extend, version, created_by, created_name, updated_by, updated_name
  </sql>
  <insert id="batchInsert" parameterType="java.util.List">
    insert into iscm_store_return_confirm_order (return_confirm_order_no, return_business_type, platform_org_id,
      platform_org_name, warehouse_code, warehouse_name,
      goods_no, goods_name, forecast_sales,
      bar_code, goods_common_name, goods_unit,
      description, specifications, dosage_form,
      manufacturer, approval_number, goods_class_id,
      goods_class_name, goods_pur_channel, warehouse_stock,
      warehouse_purchase_non_clear_stock, warehouse_return_non_clear_stock,
      warehouse_return_non_send_stock, warehouse_validity_days_min,
      warehouse_consume_days_max, warehouse_suggest_receive_return_quantity,
      warehouse_plan_receive_return_quantity, return_distr_type,
      register_month, single_goods_return_amount, single_goods_return_qty,
      created_by, created_name, updated_by,
      updated_name)
    values
    <foreach collection="list" item="item" index="index" separator="," >
    (#{item.returnConfirmOrderNo,jdbcType=VARCHAR}, #{item.returnBusinessType,jdbcType=TINYINT}, #{item.platformOrgId,jdbcType=BIGINT},
      #{item.platformOrgName,jdbcType=VARCHAR}, #{item.warehouseCode,jdbcType=VARCHAR}, #{item.warehouseName,jdbcType=VARCHAR},
      #{item.goodsNo,jdbcType=VARCHAR}, #{item.goodsName,jdbcType=VARCHAR}, #{item.forecastSales,jdbcType=DECIMAL},
      #{item.barCode,jdbcType=VARCHAR}, #{item.goodsCommonName,jdbcType=VARCHAR}, #{item.goodsUnit,jdbcType=VARCHAR},
      #{item.description,jdbcType=VARCHAR}, #{item.specifications,jdbcType=VARCHAR}, #{item.dosageForm,jdbcType=VARCHAR},
      #{item.manufacturer,jdbcType=VARCHAR}, #{item.approvalNumber,jdbcType=VARCHAR}, #{item.goodsClassId,jdbcType=BIGINT},
      #{item.goodsClassName,jdbcType=VARCHAR}, #{item.goodsPurChannel,jdbcType=VARCHAR}, #{item.warehouseStock,jdbcType=DECIMAL},
      #{item.warehousePurchaseNonClearStock,jdbcType=DECIMAL}, #{item.warehouseReturnNonClearStock,jdbcType=DECIMAL},
      #{item.warehouseReturnNonSendStock,jdbcType=DECIMAL}, #{item.warehouseValidityDaysMin,jdbcType=INTEGER},
      #{item.warehouseConsumeDaysMax,jdbcType=INTEGER}, #{item.warehouseSuggestReceiveReturnQuantity,jdbcType=DECIMAL},
      #{item.warehousePlanReceiveReturnQuantity,jdbcType=DECIMAL}, #{item.returnDistrType,jdbcType=VARCHAR},
      #{item.registerMonth,jdbcType=INTEGER}, #{item.singleGoodsReturnAmount,jdbcType=DECIMAL}, #{item.singleGoodsReturnQty,jdbcType=DECIMAL},
      #{item.createdBy,jdbcType=BIGINT}, #{item.createdName,jdbcType=VARCHAR}, #{item.updatedBy,jdbcType=BIGINT},
      #{item.updatedName,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <update id="updateOrderSum">
    update iscm_store_return_confirm_order set store_quantity = store_quantity + #{storeQuantity}, detail_lines = detail_lines + #{detailLines}, return_amount = return_amount + #{returnAmount}
    where return_confirm_order_no = #{returnConfirmOrderNo}
  </update>
    <select id="selectTotal"
            resultType="com.cowell.iscm.service.dto.returnWarehouse.StoreReturnConfirmOrderTotalDTO">
      select
      sum(detail_lines) as detailLines, sum(return_amount) as returnAmount
      from iscm_store_return_confirm_order
      <if test="_parameter != null">
        <include refid="Example_Where_Clause" />
      </if>
      <if test="orderByClause != null">
        order by ${orderByClause}
      </if>
      <if test="limit != null">
        <if test="offset != null">
          limit ${offset}, ${limit}
        </if>
        <if test="offset == null">
          limit ${limit}
        </if>
      </if>
    </select>
</mapper>
