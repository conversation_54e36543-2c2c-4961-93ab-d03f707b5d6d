<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.extend.IscmParamOrgGoodsInfoExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.IscmParamOrgGoodsInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="param_id" jdbcType="BIGINT" property="paramId" />
    <result column="param_unique_mark" jdbcType="VARCHAR" property="paramUniqueMark" />
    <result column="goods_code" jdbcType="VARCHAR" property="goodsCode" />
    <result column="bar_code" jdbcType="VARCHAR" property="barCode" />
    <result column="goods_common_name" jdbcType="VARCHAR" property="goodsCommonName" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="specifications" jdbcType="VARCHAR" property="specifications" />
    <result column="dosage_form" jdbcType="VARCHAR" property="dosageForm" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="approval_number" jdbcType="VARCHAR" property="approvalNumber" />
    <result column="habitat" jdbcType="VARCHAR" property="habitat" />
    <result column="sale_attr" jdbcType="VARCHAR" property="saleAttr" />
    <result column="manage_attr" jdbcType="VARCHAR" property="manageAttr" />
    <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
    <result column="validity_date" jdbcType="TIMESTAMP" property="validityDate" />
    <result column="produce_date" jdbcType="TIMESTAMP" property="produceDate" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
  </resultMap>
  <sql id="Base_Column_List">
    id, param_id, param_unique_mark, goods_code, bar_code, goods_common_name, goods_name, 
    description, specifications, dosage_form, manufacturer, approval_number, habitat, sale_attr, manage_attr,
    batch_no, validity_date, produce_date, status, extend, version, created_by, created_name, 
    updated_by, updated_name, gmt_create, gmt_update
  </sql>
  <insert id="batchInsert" parameterType="com.cowell.iscm.entity.IscmParamOrgGoodsInfo">
    insert into iscm_param_org_goods_info (param_id, param_unique_mark,
      goods_code, bar_code, goods_common_name, 
      goods_name, description, specifications, 
      dosage_form, manufacturer, approval_number, 
      habitat, sale_attr, manage_attr, batch_no, validity_date,
      produce_date, created_by,
      created_name, updated_by, updated_name)
    values
    <foreach collection="list" item="item" index="index" separator="," >
      (#{item.paramId,jdbcType=BIGINT}, #{item.paramUniqueMark,jdbcType=VARCHAR},
      #{item.goodsCode,jdbcType=VARCHAR}, #{item.barCode,jdbcType=VARCHAR}, #{item.goodsCommonName,jdbcType=VARCHAR},
      #{item.goodsName,jdbcType=VARCHAR}, #{item.description,jdbcType=VARCHAR}, #{item.specifications,jdbcType=VARCHAR},
      #{item.dosageForm,jdbcType=VARCHAR}, #{item.manufacturer,jdbcType=VARCHAR}, #{item.approvalNumber,jdbcType=VARCHAR},
      #{item.habitat,jdbcType=VARCHAR}, #{item.saleAttr,jdbcType=VARCHAR}, #{item.manageAttr,jdbcType=VARCHAR}, #{item.batchNo,jdbcType=VARCHAR}, #{item.validityDate,jdbcType=TIMESTAMP},
      #{item.produceDate,jdbcType=TIMESTAMP}, #{item.createdBy,jdbcType=BIGINT}, #{item.createdName,jdbcType=VARCHAR},
      #{item.updatedBy,jdbcType=BIGINT}, #{item.updatedName,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <delete id="deleteByParamUniqueMark">
    delete from iscm_param_org_goods_info where param_unique_mark = #{paramUniqueMark}
  </delete>
  <select id="selectGoodsNoByParamUniqueMark" resultType="java.lang.String">
    select goods_code from iscm_param_org_goods_info where param_unique_mark = #{paramUniqueMark}
  </select>
  <select id="selectGoodsInfoByParamUniqueMark" resultType="com.cowell.iscm.entity.IscmParamOrgGoodsInfo">
    select goods_code as goodsCode, goods_name as goodsName from iscm_param_org_goods_info where param_unique_mark = #{paramUniqueMark}
  </select>
  <select id="selectGoodsNoByParamUniqueMarkAndGoodsNos" resultType="java.lang.String">
        select goods_code from iscm_param_org_goods_info where param_unique_mark = #{paramUniqueMark}
        and goods_code in
        <foreach collection="goodsNos" item="goodsNo" index="index" separator="," open="(" close=")">
          #{goodsNo}
        </foreach>
  </select>
  <select id="selectGoodsInfoByParamUniqueMarkAndGoodsNos"
          resultType="com.cowell.iscm.entity.IscmParamOrgGoodsInfo">
        select goods_code as goodsCode, goods_name as goodsName from iscm_param_org_goods_info where param_unique_mark = #{paramUniqueMark}
        and goods_code in
        <foreach collection="goodsNos" item="goodsNo" index="index" separator="," open="(" close=")">
          #{goodsNo}
        </foreach>
  </select>
  <select id="selectGoodsInfoByParamUniqueMarks" resultType="com.cowell.iscm.entity.IscmParamOrgGoodsInfo">
        select goods_code as goodsCode, param_unique_mark as paramUniqueMark from iscm_param_org_goods_info
        where 1 = 1
        <if test="paramUniqueMarks != null and paramUniqueMarks.size != 0">
          and param_unique_mark in
          <foreach collection="paramUniqueMarks" item="paramUniqueMark" index="index" open="(" close=")" separator="," >
            #{paramUniqueMark, jdbcType=VARCHAR}
          </foreach>
        </if>
  </select>
</mapper>
