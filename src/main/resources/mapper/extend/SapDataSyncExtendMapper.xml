<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.extend.SapDataSyncExtendMapper">

  <insert id="batchInsertSapZmmt0288List" parameterType="java.util.List">
    insert into SAP_ZMMT0288 (MANDT, ZGUID,
    WERKS, MATNR, EKORG,
    BSART, BUKRS, EKGRP,
    NAME1_DC, LIFNR_XD, NAME1_XD,
    MENGE_XD, MEINS_XD, NETPR_XD,
    BRTWR_XD, NETSUM_XD, BRTSUM_XD,
    PEINH_XD, TAX_XD, TAX_XD_T,
    KONNR, KTPNR, ZPLCODE,
    ZPLSUG, ZCJMS, ZTER<PERSON>,
    ZTERM_T, <PERSON>C<PERSON><PERSON>SZ, MENGE_SUG,
    MEINS_SUG, MENGE_ZBZ, MENGE_XBZ,
    MENGE_ZZ, MEINS_ZZ, ZZBZL,
    ZZCQTY, ZXSJWXS, ZXXJWXS,
    KNTTP, KOSTL, ZUSERID,
    ZYWXM, ZZHXS, ZBCDHL,
    ZMDZ, ZMDZ_T, ZSPJB,
    EKORG_02, ZSPCKDDLX, ZGHDC,
    ZZCQTY_BDP, ZZBZL_BDP, ZJHJH,
    LGORT, ZBZGG, REMARK,
    ZCRDATE, ZCRTIME, ZCRNAME,
    ZCHDATE, ZCHTIME, ZCHNAME,
    EBELN, EBELP, PURREQNO,
    STORELINENO, ZBYZD2, ZSTOCK_USE,
    LABST_DC_JS, MENGE_WQ_JS, ZZXL_30_JS,
    ZMDQTY_JS, ZTSDDBS, ZTSDDYDH,
    ZZQXDJH, ZZQXDHH, ZJTNDJTJ,
    ZCYJSWQ, ZTCWQ, ZDCKYKC,
    MENGE_WQ, TZTS, TZTS_YXQZ,
    ZGYSQZQ, ZSPQZQ, ZSPQZQQSRQ,
    ZSPQZQZZRQ, ZBCKCTS, ZBCKCTSQSRQ,
    ZBCKCTSZZRQ, ZBCDHLQSRQ, ZBCDHLZZRQ,
    ZSFJS, ZXQHBS, ZAQKCBCTS,
    ZSFQZ, ZTSXSQSRQ, ZTSXSJSRQ,
    ZTSXSQZZB, ZKCSX, ZKCXX,
    ZCGTPJL, ZCGTPJLXM, ZYJYWXDTS,
    BRTWR_LAST, PEINH_LAST, ZTZDCKC,
    ZSTOCK_DC, ZSTOCK_STORE, ZZC_INACTIVE_DC_STOCK,
    ZSTOCK_TOTAL, ZFIX_TBS, ZSUM_R_V,
    ZDC_INV_UPPER, ZTZDCDKC1, ZTZDCDKC2,
    ZMENGE_WQ7, ZMAX_APPLY, ZINV_UPPER,
    ZMENGE_SUG, ZDHD_B, ZDC_L,
    ZGOODS_L, ZS_STOCK, ZRAW,
    ZNONCXB, ZJM_STORE_STOCK, ZDHD,
    ZHTLXBS, REASON_TYPE, ZPYYY,
    BRTWR_JY, ZHTLXBS_JY, ZNCSQWQ,
    extend)
    values
    <foreach collection="dataList" item="item" separator=",">
      (#{item.mandt,jdbcType=VARCHAR}, #{item.zguid,jdbcType=VARCHAR},
      #{item.werks,jdbcType=VARCHAR}, #{item.matnr,jdbcType=VARCHAR}, #{item.ekorg,jdbcType=VARCHAR},
      #{item.bsart,jdbcType=VARCHAR}, #{item.bukrs,jdbcType=VARCHAR}, #{item.ekgrp,jdbcType=VARCHAR},
      #{item.name1Dc,jdbcType=VARCHAR}, #{item.lifnrXd,jdbcType=VARCHAR}, #{item.name1Xd,jdbcType=VARCHAR},
      #{item.mengeXd,jdbcType=DECIMAL}, #{item.meinsXd,jdbcType=VARCHAR}, #{item.netprXd,jdbcType=DECIMAL},
      #{item.brtwrXd,jdbcType=DECIMAL}, #{item.netsumXd,jdbcType=DECIMAL}, #{item.brtsumXd,jdbcType=DECIMAL},
      #{item.peinhXd,jdbcType=DECIMAL}, #{item.taxXd,jdbcType=VARCHAR}, #{item.taxXdT,jdbcType=VARCHAR},
      #{item.konnr,jdbcType=VARCHAR}, #{item.ktpnr,jdbcType=VARCHAR}, #{item.zplcode,jdbcType=VARCHAR},
      #{item.zplsug,jdbcType=VARCHAR}, #{item.zcjms,jdbcType=VARCHAR}, #{item.zterm,jdbcType=VARCHAR},
      #{item.ztermT,jdbcType=VARCHAR}, #{item.zcgbzsz,jdbcType=VARCHAR}, #{item.mengeSug,jdbcType=DECIMAL},
      #{item.meinsSug,jdbcType=VARCHAR}, #{item.mengeZbz,jdbcType=DECIMAL}, #{item.mengeXbz,jdbcType=DECIMAL},
      #{item.mengeZz,jdbcType=DECIMAL}, #{item.meinsZz,jdbcType=VARCHAR}, #{item.zzbzl,jdbcType=VARCHAR},
      #{item.zzcqty,jdbcType=DECIMAL}, #{item.zxsjwxs,jdbcType=DECIMAL}, #{item.zxxjwxs,jdbcType=DECIMAL},
      #{item.knttp,jdbcType=VARCHAR}, #{item.kostl,jdbcType=VARCHAR}, #{item.zuserid,jdbcType=VARCHAR},
      #{item.zywxm,jdbcType=VARCHAR}, #{item.zzhxs,jdbcType=VARCHAR}, #{item.zbcdhl,jdbcType=DECIMAL},
      #{item.zmdz,jdbcType=VARCHAR}, #{item.zmdzT,jdbcType=VARCHAR}, #{item.zspjb,jdbcType=DECIMAL},
      #{item.ekorg02,jdbcType=VARCHAR}, #{item.zspckddlx,jdbcType=VARCHAR}, #{item.zghdc,jdbcType=VARCHAR},
      #{item.zzcqtyBdp,jdbcType=DECIMAL}, #{item.zzbzlBdp,jdbcType=DECIMAL}, #{item.zjhjh,jdbcType=VARCHAR},
      #{item.lgort,jdbcType=VARCHAR}, #{item.zbzgg,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR},
      #{item.zcrdate,jdbcType=VARCHAR}, #{item.zcrtime,jdbcType=VARCHAR}, #{item.zcrname,jdbcType=VARCHAR},
      #{item.zchdate,jdbcType=VARCHAR}, #{item.zchtime,jdbcType=VARCHAR}, #{item.zchname,jdbcType=VARCHAR},
      #{item.ebeln,jdbcType=VARCHAR}, #{item.ebelp,jdbcType=VARCHAR}, #{item.purreqno,jdbcType=VARCHAR},
      #{item.storelineno,jdbcType=VARCHAR}, #{item.zbyzd2,jdbcType=VARCHAR}, #{item.zstockUse,jdbcType=DECIMAL},
      #{item.labstDcJs,jdbcType=DECIMAL}, #{item.mengeWqJs,jdbcType=DECIMAL}, #{item.zzxl30Js,jdbcType=DECIMAL},
      #{item.zmdqtyJs,jdbcType=DECIMAL}, #{item.ztsddbs,jdbcType=VARCHAR}, #{item.ztsddydh,jdbcType=VARCHAR},
      #{item.zzqxdjh,jdbcType=VARCHAR}, #{item.zzqxdhh,jdbcType=VARCHAR}, #{item.zjtndjtj,jdbcType=VARCHAR},
      #{item.zcyjswq,jdbcType=DECIMAL}, #{item.ztcwq,jdbcType=DECIMAL}, #{item.zdckykc,jdbcType=DECIMAL},
      #{item.mengeWq,jdbcType=DECIMAL}, #{item.tzts,jdbcType=INTEGER}, #{item.tztsYxqz,jdbcType=VARCHAR},
      #{item.zgysqzq,jdbcType=INTEGER}, #{item.zspqzq,jdbcType=DECIMAL}, #{item.zspqzqqsrq,jdbcType=VARCHAR},
      #{item.zspqzqzzrq,jdbcType=VARCHAR}, #{item.zbckcts,jdbcType=DECIMAL}, #{item.zbckctsqsrq,jdbcType=VARCHAR},
      #{item.zbckctszzrq,jdbcType=VARCHAR}, #{item.zbcdhlqsrq,jdbcType=VARCHAR}, #{item.zbcdhlzzrq,jdbcType=VARCHAR},
      #{item.zsfjs,jdbcType=VARCHAR}, #{item.zxqhbs,jdbcType=DECIMAL}, #{item.zaqkcbcts,jdbcType=DECIMAL},
      #{item.zsfqz,jdbcType=VARCHAR}, #{item.ztsxsqsrq,jdbcType=VARCHAR}, #{item.ztsxsjsrq,jdbcType=VARCHAR},
      #{item.ztsxsqzzb,jdbcType=DECIMAL}, #{item.zkcsx,jdbcType=DECIMAL}, #{item.zkcxx,jdbcType=DECIMAL},
      #{item.zcgtpjl,jdbcType=VARCHAR}, #{item.zcgtpjlxm,jdbcType=VARCHAR}, #{item.zyjywxdts,jdbcType=INTEGER},
      #{item.brtwrLast,jdbcType=DECIMAL}, #{item.peinhLast,jdbcType=DECIMAL}, #{item.ztzdckc,jdbcType=DECIMAL},
      #{item.zstockDc,jdbcType=DECIMAL}, #{item.zstockStore,jdbcType=DECIMAL}, #{item.zzcInactiveDcStock,jdbcType=DECIMAL},
      #{item.zstockTotal,jdbcType=DECIMAL}, #{item.zfixTbs,jdbcType=DECIMAL}, #{item.zsumRV,jdbcType=DECIMAL},
      #{item.zdcInvUpper,jdbcType=DECIMAL}, #{item.ztzdcdkc1,jdbcType=DECIMAL}, #{item.ztzdcdkc2,jdbcType=DECIMAL},
      #{item.zmengeWq7,jdbcType=DECIMAL}, #{item.zmaxApply,jdbcType=DECIMAL}, #{item.zinvUpper,jdbcType=DECIMAL},
      #{item.zmengeSug,jdbcType=DECIMAL}, #{item.zdhdB,jdbcType=DECIMAL}, #{item.zdcL,jdbcType=DECIMAL},
      #{item.zgoodsL,jdbcType=VARCHAR}, #{item.zsStock,jdbcType=DECIMAL}, #{item.zraw,jdbcType=DECIMAL},
      #{item.znoncxb,jdbcType=VARCHAR}, #{item.zjmStoreStock,jdbcType=DECIMAL}, #{item.zdhd,jdbcType=DECIMAL},
      #{item.zhtlxbs,jdbcType=VARCHAR}, #{item.reasonType,jdbcType=VARCHAR}, #{item.zpyyy,jdbcType=VARCHAR},
      #{item.brtwrJy,jdbcType=DECIMAL}, #{item.zhtlxbsJy,jdbcType=VARCHAR}, #{item.zncsqwq,jdbcType=DECIMAL},
      #{item.extend,jdbcType=LONGVARCHAR})
    </foreach>
  </insert>



  <insert id="batchInsertSapZmmt0098List" parameterType="java.util.List">
    insert into SAP_ZMMT0098 (MANDT, WERKS,
    MATNR, MATNR_DESC, ZISAPRAS,
    ZCGYGH, ZPURAGT, ZXSJWXS,
    ZXXJWXS, ZSPQZQ, ZSPQZQQSRQ,
    ZSPQZQZZRQ, ZBCKCTS, ZBCKCTSQSRQ,
    ZBCKCTSZZRQ, ZBCDHL, ZBCDHLQSRQ,
    ZBCDHLZZRQ, ZDYZD1, ZDYZD2,
    ZDYZD3, ZDYZD4, ZDYZD5,
    ZTHFS, ZCGBZSZ, ZZCGSX_PF,
    ZBCSX_PF, ZQZQ_PF, ZJGQ_PF,
    ZAQKCTS_PF, ZBCDHL_PF, ZBCKCTS_PF,
    ZQSRQ_PF, ZZZRQ_PF, CPUDT,
    CPUTM, ANNAM, AEDAT,
    AEZET, AENAM, ZZHXS,
    ZSHCK, ZSFJS, ZXQHBS,
    ZAQKCBCTS, ZSFQZ, ZTSXSQSRQ,
    ZTSXSJSRQ, ZTSXSQZZB, ZSPCKDDLX,
    ZGHDC, ZKCSX, ZKCXX,
    ZZCQTY, ZZBZL, ZCGTPJL,
    ZCGTPJLXM, extend)
    values
    <foreach collection="dataList" item="item" separator=",">
      (#{item.mandt,jdbcType=VARCHAR}, #{item.werks,jdbcType=VARCHAR},
      #{item.matnr,jdbcType=VARCHAR}, #{item.matnrDesc,jdbcType=VARCHAR}, #{item.zisapras,jdbcType=VARCHAR},
      #{item.zcgygh,jdbcType=VARCHAR}, #{item.zpuragt,jdbcType=VARCHAR}, #{item.zxsjwxs,jdbcType=DECIMAL},
      #{item.zxxjwxs,jdbcType=DECIMAL}, #{item.zspqzq,jdbcType=DECIMAL}, #{item.zspqzqqsrq,jdbcType=VARCHAR},
      #{item.zspqzqzzrq,jdbcType=VARCHAR}, #{item.zbckcts,jdbcType=DECIMAL}, #{item.zbckctsqsrq,jdbcType=VARCHAR},
      #{item.zbckctszzrq,jdbcType=VARCHAR}, #{item.zbcdhl,jdbcType=DECIMAL}, #{item.zbcdhlqsrq,jdbcType=VARCHAR},
      #{item.zbcdhlzzrq,jdbcType=VARCHAR}, #{item.zdyzd1,jdbcType=VARCHAR}, #{item.zdyzd2,jdbcType=VARCHAR},
      #{item.zdyzd3,jdbcType=VARCHAR}, #{item.zdyzd4,jdbcType=VARCHAR}, #{item.zdyzd5,jdbcType=VARCHAR},
      #{item.zthfs,jdbcType=VARCHAR}, #{item.zcgbzsz,jdbcType=VARCHAR}, #{item.zzcgsxPf,jdbcType=VARCHAR},
      #{item.zbcsxPf,jdbcType=VARCHAR}, #{item.zqzqPf,jdbcType=INTEGER}, #{item.zjgqPf,jdbcType=INTEGER},
      #{item.zaqkctsPf,jdbcType=INTEGER}, #{item.zbcdhlPf,jdbcType=DECIMAL}, #{item.zbckctsPf,jdbcType=INTEGER},
      #{item.zqsrqPf,jdbcType=VARCHAR}, #{item.zzzrqPf,jdbcType=VARCHAR}, #{item.cpudt,jdbcType=VARCHAR},
      #{item.cputm,jdbcType=VARCHAR}, #{item.annam,jdbcType=VARCHAR}, #{item.aedat,jdbcType=VARCHAR},
      #{item.aezet,jdbcType=VARCHAR}, #{item.aenam,jdbcType=VARCHAR}, #{item.zzhxs,jdbcType=VARCHAR},
      #{item.zshck,jdbcType=VARCHAR}, #{item.zsfjs,jdbcType=VARCHAR}, #{item.zxqhbs,jdbcType=DECIMAL},
      #{item.zaqkcbcts,jdbcType=DECIMAL}, #{item.zsfqz,jdbcType=VARCHAR}, #{item.ztsxsqsrq,jdbcType=VARCHAR},
      #{item.ztsxsjsrq,jdbcType=VARCHAR}, #{item.ztsxsqzzb,jdbcType=DECIMAL}, #{item.zspckddlx,jdbcType=VARCHAR},
      #{item.zghdc,jdbcType=VARCHAR}, #{item.zkcsx,jdbcType=DECIMAL}, #{item.zkcxx,jdbcType=DECIMAL},
      #{item.zzcqty,jdbcType=DECIMAL}, #{item.zzbzl,jdbcType=DECIMAL}, #{item.zcgtpjl,jdbcType=VARCHAR},
      #{item.zcgtpjlxm,jdbcType=VARCHAR}, #{item.extend,jdbcType=LONGVARCHAR})
    </foreach>
  </insert>


  <insert id="batchInsertSapZmmt0287List" parameterType="java.util.List">
    insert into SAP_ZMMT0287 ( MANDT, PURREQNO,
    STORELINENO, BUKRS, EKORG,
    WERKS, ZZZZSHD, BSART,
    MATNR, MENGE, ZSPSL,
    MEINS, ZZCXBJS, ZJHBHSL,
    ZYYBZ, BADAT, ZSPZT,
    ZCLZT, LOEKZ, ERDAT,
    ERZET, ERNAM, AEDAT,
    AEZET, AENAM, ZSHCK,
    ZCGYGH, ZPURAGT, CHARG,
    LGORT, RESLO, MSG,
    EBELN, EBELP, VBELN,
    POSNR, BRTWR, KPEIN2,
    ZCGZB, ZYPURREQNO, ZYSTORELINENO,
    ZSFCF, ZMAIN, ZZXL_30_JS,
    ZMDQTY_JS, ZDCKYKC, MENGE_SUG,
    ZPLSUG, ZCYJSWQ, LABST_DC_JS,
    extend)
    values
    <foreach collection="dataList" item="item" separator=",">
      (#{item.mandt,jdbcType=VARCHAR}, #{item.purreqno,jdbcType=VARCHAR},
      #{item.storelineno,jdbcType=VARCHAR}, #{item.bukrs,jdbcType=VARCHAR}, #{item.ekorg,jdbcType=VARCHAR},
      #{item.werks,jdbcType=VARCHAR}, #{item.zzzzshd,jdbcType=VARCHAR}, #{item.bsart,jdbcType=VARCHAR},
      #{item.matnr,jdbcType=VARCHAR}, #{item.menge,jdbcType=DECIMAL}, #{item.zspsl,jdbcType=DECIMAL},
      #{item.meins,jdbcType=VARCHAR}, #{item.zzcxbjs,jdbcType=VARCHAR}, #{item.zjhbhsl,jdbcType=DECIMAL},
      #{item.zyybz,jdbcType=VARCHAR}, #{item.badat,jdbcType=VARCHAR}, #{item.zspzt,jdbcType=VARCHAR},
      #{item.zclzt,jdbcType=VARCHAR}, #{item.loekz,jdbcType=VARCHAR}, #{item.erdat,jdbcType=VARCHAR},
      #{item.erzet,jdbcType=VARCHAR}, #{item.ernam,jdbcType=VARCHAR}, #{item.aedat,jdbcType=VARCHAR},
      #{item.aezet,jdbcType=VARCHAR}, #{item.aenam,jdbcType=VARCHAR}, #{item.zshck,jdbcType=VARCHAR},
      #{item.zcgygh,jdbcType=VARCHAR}, #{item.zpuragt,jdbcType=VARCHAR}, #{item.charg,jdbcType=VARCHAR},
      #{item.lgort,jdbcType=VARCHAR}, #{item.reslo,jdbcType=VARCHAR}, #{item.msg,jdbcType=VARCHAR},
      #{item.ebeln,jdbcType=VARCHAR}, #{item.ebelp,jdbcType=VARCHAR}, #{item.vbeln,jdbcType=VARCHAR},
      #{item.posnr,jdbcType=VARCHAR}, #{item.brtwr,jdbcType=DECIMAL}, #{item.kpein2,jdbcType=DECIMAL},
      #{item.zcgzb,jdbcType=VARCHAR}, #{item.zypurreqno,jdbcType=VARCHAR}, #{item.zystorelineno,jdbcType=VARCHAR},
      #{item.zsfcf,jdbcType=VARCHAR}, #{item.zmain,jdbcType=VARCHAR}, #{item.zzxl30Js,jdbcType=DECIMAL},
      #{item.zmdqtyJs,jdbcType=DECIMAL}, #{item.zdckykc,jdbcType=DECIMAL}, #{item.mengeSug,jdbcType=DECIMAL},
      #{item.zplsug,jdbcType=VARCHAR}, #{item.zcyjswq,jdbcType=DECIMAL}, #{item.labstDcJs,jdbcType=DECIMAL},
      #{item.extend,jdbcType=LONGVARCHAR})
    </foreach>
  </insert>


  <insert id="batchInsertSapZmmt0085List" parameterType="java.util.List">
    insert into SAP_ZMMT0085 (MANDT, PURREQNO,
    BILLTYPE, SOURCETYPE, CREATOR,
    BUKRS, EKORG, ZZZZQHD,
    BEDAT, ZCJSJ, RETURNREASON,
    EXPECTEDDATE, NOTES, LIFNR,
    BUKRS_S, VKORG, RESWK,
    LOGRT, ZZZWLMS, ZZZZSHD,
    ZZZSHKC, ZZZUSERID, ZZCGY,
    ZZDHY, EBELN, ZSTATUS,
    ZSEND, MESS, ZCLOSE,
    ZCLOSE_TEXT, ZPUCH, extend
    )
    values
    <foreach collection="dataList" item="item" separator=",">
      (#{item.mandt,jdbcType=VARCHAR}, #{item.purreqno,jdbcType=VARCHAR},
      #{item.billtype,jdbcType=VARCHAR}, #{item.sourcetype,jdbcType=VARCHAR}, #{item.creator,jdbcType=VARCHAR},
      #{item.bukrs,jdbcType=VARCHAR}, #{item.ekorg,jdbcType=VARCHAR}, #{item.zzzzqhd,jdbcType=VARCHAR},
      #{item.bedat,jdbcType=VARCHAR}, #{item.zcjsj,jdbcType=VARCHAR}, #{item.returnreason,jdbcType=VARCHAR},
      #{item.expecteddate,jdbcType=VARCHAR}, #{item.notes,jdbcType=VARCHAR}, #{item.lifnr,jdbcType=VARCHAR},
      #{item.bukrsS,jdbcType=VARCHAR}, #{item.vkorg,jdbcType=VARCHAR}, #{item.reswk,jdbcType=VARCHAR},
      #{item.logrt,jdbcType=VARCHAR}, #{item.zzzwlms,jdbcType=VARCHAR}, #{item.zzzzshd,jdbcType=VARCHAR},
      #{item.zzzshkc,jdbcType=VARCHAR}, #{item.zzzuserid,jdbcType=VARCHAR}, #{item.zzcgy,jdbcType=VARCHAR},
      #{item.zzdhy,jdbcType=VARCHAR}, #{item.ebeln,jdbcType=VARCHAR}, #{item.zstatus,jdbcType=VARCHAR},
      #{item.zsend,jdbcType=VARCHAR}, #{item.mess,jdbcType=VARCHAR}, #{item.zclose,jdbcType=VARCHAR},
      #{item.zcloseText,jdbcType=VARCHAR}, #{item.zpuch,jdbcType=VARCHAR}, #{item.extend,jdbcType=LONGVARCHAR}
      )
    </foreach>
  </insert>

  <insert id="batchInsertSapZmmt0086List" parameterType="java.util.List">
    insert into SAP_ZMMT0086 (MANDT, PURREQNO,
    STORELINENO, MATNR, MENGE,
    MEINS, SERIAL, CHARG,
    RETREASON2, ZNETPR, ZZCXBJS,
    RETPO, UMSON, MENGE2,
    RESLO, ZDCQTY, ZCKCXB1,
    ZCKCXB2, ZYDYXL1, ZRKCXB2,
    ZYDYXL2, BRTWR, KPEIN2,
    ZYPURREQNO, ZYSTORELINENO, ZSFCF,
    ZMAIN, extend)
    values
    <foreach collection="dataList" item="item" separator=",">
      ( #{item.mandt,jdbcType=VARCHAR}, #{item.purreqno,jdbcType=VARCHAR},
      #{item.storelineno,jdbcType=VARCHAR}, #{item.matnr,jdbcType=VARCHAR}, #{item.menge,jdbcType=DECIMAL},
      #{item.meins,jdbcType=VARCHAR}, #{item.serial,jdbcType=VARCHAR}, #{item.charg,jdbcType=VARCHAR},
      #{item.retreason2,jdbcType=VARCHAR}, #{item.znetpr,jdbcType=DECIMAL}, #{item.zzcxbjs,jdbcType=VARCHAR},
      #{item.retpo,jdbcType=VARCHAR}, #{item.umson,jdbcType=VARCHAR}, #{item.menge2,jdbcType=DECIMAL},
      #{item.reslo,jdbcType=VARCHAR}, #{item.zdcqty,jdbcType=DECIMAL}, #{item.zckcxb1,jdbcType=VARCHAR},
      #{item.zckcxb2,jdbcType=VARCHAR}, #{item.zydyxl1,jdbcType=DECIMAL}, #{item.zrkcxb2,jdbcType=VARCHAR},
      #{item.zydyxl2,jdbcType=DECIMAL}, #{item.brtwr,jdbcType=DECIMAL}, #{item.kpein2,jdbcType=DECIMAL},
      #{item.zypurreqno,jdbcType=VARCHAR}, #{item.zystorelineno,jdbcType=VARCHAR}, #{item.zsfcf,jdbcType=VARCHAR},
      #{item.zmain,jdbcType=VARCHAR}, #{item.extend,jdbcType=LONGVARCHAR})
    </foreach>
  </insert>

  <insert id="batchInsertSapZmmt0355List" parameterType="java.util.List">
    insert into SAP_ZMMT0355 ( MANDT, ZGUID,
    ZCGJHD, ZCGJHDHH, BSART,
    LOEKZ, ZPOFLAG, ZOAFLAG,
    EBELN, EBELP, EKORG,
    BUKRS, EKGRP, LIFNR,
    ZYWCJ, ZZZWLMS, ERNAM,
    ERDAT, ERZET, WERKS,
    MATNR, MENGE, ZMENGE,
    EINDT, ZEINDT, MEINS,
    ZHSDJ, ZQRHSDJ, PEINH,
    ZPEINH, MWSKZ, ZMWSKZ,
    ZCFLAG, ZTERM, ZZZUSERID,
    ZSHCK, ZSPJB, KONNR,
    KTPNR, ZTZHTBH, ZTZHTHH,
    ZPLCODE, ZPLSUG, ZCJBM,
    MENGE_ZZ, ZSTOCK_USE, ZSJFD,
    REMARK, USERNAME, UDATE,
    UTIME, ZMDZ, ZSRMFLAG,
    BRTWR_LAST, PEINH_LAST, LABST_DC_JS,
    MENGE_WQ_JS, ZZXL_30_JS, ZMDQTY_JS,
    ZZZMDSQ, ZZZMDHH, ZJHDAT,
    ZJHTIME, ZSRMDAT, ZSRMTIME,
    ZJHZDAT, ZJHZTIME, FRGGR,
    FRGSX, FRGKX, FRGCO,
    SUBMI, ZRESULT, ZLEVEL,
    ZSPBS, ZMESS, LGORT,
    ZDCKYKC, ZCYJSWQ, ZZDGDSC,
    extend)
    values
    <foreach collection="dataList" item="item" separator=",">
      (#{item.mandt,jdbcType=VARCHAR}, #{item.zguid,jdbcType=VARCHAR},
      #{item.zcgjhd,jdbcType=VARCHAR}, #{item.zcgjhdhh,jdbcType=VARCHAR}, #{item.bsart,jdbcType=VARCHAR},
      #{item.loekz,jdbcType=VARCHAR}, #{item.zpoflag,jdbcType=VARCHAR}, #{item.zoaflag,jdbcType=VARCHAR},
      #{item.ebeln,jdbcType=VARCHAR}, #{item.ebelp,jdbcType=VARCHAR}, #{item.ekorg,jdbcType=VARCHAR},
      #{item.bukrs,jdbcType=VARCHAR}, #{item.ekgrp,jdbcType=VARCHAR}, #{item.lifnr,jdbcType=VARCHAR},
      #{item.zywcj,jdbcType=VARCHAR}, #{item.zzzwlms,jdbcType=VARCHAR}, #{item.ernam,jdbcType=VARCHAR},
      #{item.erdat,jdbcType=VARCHAR}, #{item.erzet,jdbcType=VARCHAR}, #{item.werks,jdbcType=VARCHAR},
      #{item.matnr,jdbcType=VARCHAR}, #{item.menge,jdbcType=DECIMAL}, #{item.zmenge,jdbcType=DECIMAL},
      #{item.eindt,jdbcType=VARCHAR}, #{item.zeindt,jdbcType=VARCHAR}, #{item.meins,jdbcType=VARCHAR},
      #{item.zhsdj,jdbcType=DECIMAL}, #{item.zqrhsdj,jdbcType=DECIMAL}, #{item.peinh,jdbcType=INTEGER},
      #{item.zpeinh,jdbcType=INTEGER}, #{item.mwskz,jdbcType=VARCHAR}, #{item.zmwskz,jdbcType=VARCHAR},
      #{item.zcflag,jdbcType=VARCHAR}, #{item.zterm,jdbcType=VARCHAR}, #{item.zzzuserid,jdbcType=VARCHAR},
      #{item.zshck,jdbcType=VARCHAR}, #{item.zspjb,jdbcType=INTEGER}, #{item.konnr,jdbcType=VARCHAR},
      #{item.ktpnr,jdbcType=VARCHAR}, #{item.ztzhtbh,jdbcType=VARCHAR}, #{item.ztzhthh,jdbcType=VARCHAR},
      #{item.zplcode,jdbcType=VARCHAR}, #{item.zplsug,jdbcType=VARCHAR}, #{item.zcjbm,jdbcType=VARCHAR},
      #{item.mengeZz,jdbcType=DECIMAL}, #{item.zstockUse,jdbcType=DECIMAL}, #{item.zsjfd,jdbcType=DECIMAL},
      #{item.remark,jdbcType=VARCHAR}, #{item.username,jdbcType=VARCHAR}, #{item.udate,jdbcType=VARCHAR},
      #{item.utime,jdbcType=VARCHAR}, #{item.zmdz,jdbcType=VARCHAR}, #{item.zsrmflag,jdbcType=VARCHAR},
      #{item.brtwrLast,jdbcType=DECIMAL}, #{item.peinhLast,jdbcType=INTEGER}, #{item.labstDcJs,jdbcType=DECIMAL},
      #{item.mengeWqJs,jdbcType=DECIMAL}, #{item.zzxl30Js,jdbcType=DECIMAL}, #{item.zmdqtyJs,jdbcType=DECIMAL},
      #{item.zzzmdsq,jdbcType=VARCHAR}, #{item.zzzmdhh,jdbcType=VARCHAR}, #{item.zjhdat,jdbcType=VARCHAR},
      #{item.zjhtime,jdbcType=VARCHAR}, #{item.zsrmdat,jdbcType=VARCHAR}, #{item.zsrmtime,jdbcType=VARCHAR},
      #{item.zjhzdat,jdbcType=VARCHAR}, #{item.zjhztime,jdbcType=VARCHAR}, #{item.frggr,jdbcType=VARCHAR},
      #{item.frgsx,jdbcType=VARCHAR}, #{item.frgkx,jdbcType=VARCHAR}, #{item.frgco,jdbcType=VARCHAR},
      #{item.submi,jdbcType=VARCHAR}, #{item.zresult,jdbcType=VARCHAR}, #{item.zlevel,jdbcType=VARCHAR},
      #{item.zspbs,jdbcType=VARCHAR}, #{item.zmess,jdbcType=VARCHAR}, #{item.lgort,jdbcType=VARCHAR},
      #{item.zdckykc,jdbcType=DECIMAL}, #{item.zcyjswq,jdbcType=DECIMAL}, #{item.zzdgdsc,jdbcType=DECIMAL},
      #{item.extend,jdbcType=LONGVARCHAR})
    </foreach>
  </insert>

  <insert id="batchInsertSapEKKOList" parameterType="java.util.List">
    insert into SAP_EKKO ( MANDT, EBELN,
    BUKRS, BSTYP, BSART,
    BSAKZ, LOEKZ, STATU,
    AEDAT, ERNAM, LASTCHANGEDATETIME,
    PINCR, LPONR, LIFNR,
    SPRAS, ZTERM, ZBD1T,
    ZBD2T, ZBD3T, ZBD1P,
    ZBD2P, EKORG, EKGRP,
    WAERS, WKURS, KUFIX,
    BEDAT, KDATB, KDATE,
    BWBDT, ANGDT, BNDDT,
    GWLDT, AUSNR, ANGNR,
    IHRAN, IHREZ, VERKF,
    TELF1, LLIEF, KUNNR,
    KONNR, ABGRU, AUTLF,
    WEAKT, RESWK, LBLIF,
    INCO1, INCO2, KTWRT,
    SUBMI, KNUMV, KALSM,
    STAFO, LIFRE, EXNUM,
    UNSEZ, LOGSY, UPINC,
    STAKO, FRGGR, FRGSX,
    FRGKE, FRGZU, FRGRL,
    LANDS, LPHIS, ADRNR,
    STCEG_L, STCEG, ABSGR,
    ADDNR, KORNR, MEMORY,
    PROCSTAT, RLWRT, REVNO,
    SCMPROC, REASON_CODE, MEMORYTYPE,
    RETTP, RETPC, DPTYP,
    DPPCT, DPAMT, DPDAT,
    MSR_ID, HIERARCHY_EXISTS, THRESHOLD_EXISTS,
    LEGAL_CONTRACT, DESCRIPTION, RELEASE_DATE,
    VSART, HANDOVERLOC, SHIPCOND,
    INCOV, INCO2_L, INCO3_L,
    GRWCU, INTRA_REL, INTRA_EXCL,
    QTN_ERLST_SUBMSN_DATE, FOLLOWON_DOC_CAT,
    FOLLOWON_DOC_TYPE, DUMMY_EKKO_INCL_EEW_PS,
    EXTERNALSYSTEM, EXTERNALREFERENCEID,
    EXT_REV_TMSTMP, ISEOPBLOCKED, ISAGED,
    FORCE_ID, FORCE_CNT, RELOC_ID,
    RELOC_SEQ_ID, SOURCE_LOGSYS, FSH_TRANSACTION,
    FSH_ITEM_GROUP, FSH_VAS_LAST_ITEM,
    FSH_OS_STG_CHANGE, TMS_REF_UUID, ZZZWLMS,
    ZZZZSHD, ZZZSHKC, ZZZMDSQ,
    ZZZBEIZ, ZZZHGZT, ZZQXDJH,
    ZZZQHBS, ZZZUSERID, ZZCGY,
    ZZDHY, ZZCOER, ZZCOCA,
    ZZYCGDD, ZAPCGK, APCGK_EXTEND,
    ZBAS_DATE, ZADATTYP, ZSTART_DAT,
    Z_DEV, ZINDANX, ZLIMIT_DAT,
    NUMERATOR, HASHCAL_BDAT, HASHCAL,
    NEGATIVE, HASHCAL_EXISTS, KNOWN_INDEX,
    POSTAT, VZSKZ, FSH_SNST_STATUS,
    PROCE, CONC, CONT,
    COMP, OUTR, DESP,
    DESP_DAT, DESP_CARGO, PARE,
    PARE_DAT, PARE_CARGO, PFM_CONTRACT,
    POHF_TYPE, EQ_EINDT, EQ_WERKS,
    FIXPO, EKGRP_ALLOW, WERKS_ALLOW,
    CONTRACT_ALLOW, PSTYP_ALLOW, FIXPO_ALLOW,
    KEY_ID_ALLOW, AUREL_ALLOW, DELPER_ALLOW,
    EINDT_ALLOW, LTSNR_ALLOW, OTB_LEVEL,
    OTB_COND_TYPE, KEY_ID, OTB_VALUE,
    OTB_CURR, OTB_RES_VALUE, OTB_SPEC_VALUE,
    SPR_RSN_PROFILE, BUDG_TYPE, OTB_STATUS,
    OTB_REASON, CHECK_TYPE, CON_OTB_REQ,
    CON_PREBOOK_LEV, CON_DISTR_LEV, ZZYFYE,
    ZSQHZH, ZZSRMZT, ZZPAID,
    ZZPAMT, extend)
    values
    <foreach collection="dataList" item="item" separator=",">
      ( #{item.mandt,jdbcType=VARCHAR}, #{item.ebeln,jdbcType=VARCHAR},
      #{item.bukrs,jdbcType=VARCHAR}, #{item.bstyp,jdbcType=VARCHAR}, #{item.bsart,jdbcType=VARCHAR},
      #{item.bsakz,jdbcType=VARCHAR}, #{item.loekz,jdbcType=VARCHAR}, #{item.statu,jdbcType=VARCHAR},
      #{item.aedat,jdbcType=VARCHAR}, #{item.ernam,jdbcType=VARCHAR}, #{item.lastchangedatetime,jdbcType=DECIMAL},
      #{item.pincr,jdbcType=VARCHAR}, #{item.lponr,jdbcType=VARCHAR}, #{item.lifnr,jdbcType=VARCHAR},
      #{item.spras,jdbcType=VARCHAR}, #{item.zterm,jdbcType=VARCHAR}, #{item.zbd1t,jdbcType=DECIMAL},
      #{item.zbd2t,jdbcType=DECIMAL}, #{item.zbd3t,jdbcType=DECIMAL}, #{item.zbd1p,jdbcType=DECIMAL},
      #{item.zbd2p,jdbcType=DECIMAL}, #{item.ekorg,jdbcType=VARCHAR}, #{item.ekgrp,jdbcType=VARCHAR},
      #{item.waers,jdbcType=VARCHAR}, #{item.wkurs,jdbcType=DECIMAL}, #{item.kufix,jdbcType=VARCHAR},
      #{item.bedat,jdbcType=VARCHAR}, #{item.kdatb,jdbcType=VARCHAR}, #{item.kdate,jdbcType=VARCHAR},
      #{item.bwbdt,jdbcType=VARCHAR}, #{item.angdt,jdbcType=VARCHAR}, #{item.bnddt,jdbcType=VARCHAR},
      #{item.gwldt,jdbcType=VARCHAR}, #{item.ausnr,jdbcType=VARCHAR}, #{item.angnr,jdbcType=VARCHAR},
      #{item.ihran,jdbcType=VARCHAR}, #{item.ihrez,jdbcType=VARCHAR}, #{item.verkf,jdbcType=VARCHAR},
      #{item.telf1,jdbcType=VARCHAR}, #{item.llief,jdbcType=VARCHAR}, #{item.kunnr,jdbcType=VARCHAR},
      #{item.konnr,jdbcType=VARCHAR}, #{item.abgru,jdbcType=VARCHAR}, #{item.autlf,jdbcType=VARCHAR},
      #{item.weakt,jdbcType=VARCHAR}, #{item.reswk,jdbcType=VARCHAR}, #{item.lblif,jdbcType=VARCHAR},
      #{item.inco1,jdbcType=VARCHAR}, #{item.inco2,jdbcType=VARCHAR}, #{item.ktwrt,jdbcType=DECIMAL},
      #{item.submi,jdbcType=VARCHAR}, #{item.knumv,jdbcType=VARCHAR}, #{item.kalsm,jdbcType=VARCHAR},
      #{item.stafo,jdbcType=VARCHAR}, #{item.lifre,jdbcType=VARCHAR}, #{item.exnum,jdbcType=VARCHAR},
      #{item.unsez,jdbcType=VARCHAR}, #{item.logsy,jdbcType=VARCHAR}, #{item.upinc,jdbcType=VARCHAR},
      #{item.stako,jdbcType=VARCHAR}, #{item.frggr,jdbcType=VARCHAR}, #{item.frgsx,jdbcType=VARCHAR},
      #{item.frgke,jdbcType=VARCHAR}, #{item.frgzu,jdbcType=VARCHAR}, #{item.frgrl,jdbcType=VARCHAR},
      #{item.lands,jdbcType=VARCHAR}, #{item.lphis,jdbcType=VARCHAR}, #{item.adrnr,jdbcType=VARCHAR},
      #{item.stcegL,jdbcType=VARCHAR}, #{item.stceg,jdbcType=VARCHAR}, #{item.absgr,jdbcType=VARCHAR},
      #{item.addnr,jdbcType=VARCHAR}, #{item.kornr,jdbcType=VARCHAR}, #{item.memory,jdbcType=VARCHAR},
      #{item.procstat,jdbcType=VARCHAR}, #{item.rlwrt,jdbcType=DECIMAL}, #{item.revno,jdbcType=VARCHAR},
      #{item.scmproc,jdbcType=VARCHAR}, #{item.reasonCode,jdbcType=VARCHAR}, #{item.memorytype,jdbcType=VARCHAR},
      #{item.rettp,jdbcType=VARCHAR}, #{item.retpc,jdbcType=DECIMAL}, #{item.dptyp,jdbcType=VARCHAR},
      #{item.dppct,jdbcType=DECIMAL}, #{item.dpamt,jdbcType=DECIMAL}, #{item.dpdat,jdbcType=VARCHAR},
      #{item.msrId,jdbcType=VARCHAR}, #{item.hierarchyExists,jdbcType=VARCHAR}, #{item.thresholdExists,jdbcType=VARCHAR},
      #{item.legalContract,jdbcType=VARCHAR}, #{item.description,jdbcType=VARCHAR}, #{item.releaseDate,jdbcType=VARCHAR},
      #{item.vsart,jdbcType=VARCHAR}, #{item.handoverloc,jdbcType=VARCHAR}, #{item.shipcond,jdbcType=VARCHAR},
      #{item.incov,jdbcType=VARCHAR}, #{item.inco2L,jdbcType=VARCHAR}, #{item.inco3L,jdbcType=VARCHAR},
      #{item.grwcu,jdbcType=VARCHAR}, #{item.intraRel,jdbcType=VARCHAR}, #{item.intraExcl,jdbcType=VARCHAR},
      #{item.qtnErlstSubmsnDate,jdbcType=VARCHAR}, #{item.followonDocCat,jdbcType=VARCHAR},
      #{item.followonDocType,jdbcType=VARCHAR}, #{item.dummyEkkoInclEewPs,jdbcType=VARCHAR},
      #{item.externalsystem,jdbcType=VARCHAR}, #{item.externalreferenceid,jdbcType=VARCHAR},
      #{item.extRevTmstmp,jdbcType=DECIMAL}, #{item.iseopblocked,jdbcType=VARCHAR}, #{item.isaged,jdbcType=VARCHAR},
      #{item.forceId,jdbcType=VARCHAR}, #{item.forceCnt,jdbcType=VARCHAR}, #{item.relocId,jdbcType=VARCHAR},
      #{item.relocSeqId,jdbcType=VARCHAR}, #{item.sourceLogsys,jdbcType=VARCHAR}, #{item.fshTransaction,jdbcType=VARCHAR},
      #{item.fshItemGroup,jdbcType=VARCHAR}, #{item.fshVasLastItem,jdbcType=VARCHAR},
      #{item.fshOsStgChange,jdbcType=VARCHAR}, #{item.tmsRefUuid,jdbcType=VARCHAR}, #{item.zzzwlms,jdbcType=VARCHAR},
      #{item.zzzzshd,jdbcType=VARCHAR}, #{item.zzzshkc,jdbcType=VARCHAR}, #{item.zzzmdsq,jdbcType=VARCHAR},
      #{item.zzzbeiz,jdbcType=VARCHAR}, #{item.zzzhgzt,jdbcType=VARCHAR}, #{item.zzqxdjh,jdbcType=VARCHAR},
      #{item.zzzqhbs,jdbcType=VARCHAR}, #{item.zzzuserid,jdbcType=VARCHAR}, #{item.zzcgy,jdbcType=VARCHAR},
      #{item.zzdhy,jdbcType=VARCHAR}, #{item.zzcoer,jdbcType=VARCHAR}, #{item.zzcoca,jdbcType=VARCHAR},
      #{item.zzycgdd,jdbcType=VARCHAR}, #{item.zapcgk,jdbcType=VARCHAR}, #{item.apcgkExtend,jdbcType=VARCHAR},
      #{item.zbasDate,jdbcType=VARCHAR}, #{item.zadattyp,jdbcType=VARCHAR}, #{item.zstartDat,jdbcType=VARCHAR},
      #{item.zDev,jdbcType=DECIMAL}, #{item.zindanx,jdbcType=VARCHAR}, #{item.zlimitDat,jdbcType=VARCHAR},
      #{item.numerator,jdbcType=VARCHAR}, #{item.hashcalBdat,jdbcType=VARCHAR}, #{item.hashcal,jdbcType=VARCHAR},
      #{item.negative,jdbcType=VARCHAR}, #{item.hashcalExists,jdbcType=VARCHAR}, #{item.knownIndex,jdbcType=VARCHAR},
      #{item.postat,jdbcType=VARCHAR}, #{item.vzskz,jdbcType=VARCHAR}, #{item.fshSnstStatus,jdbcType=VARCHAR},
      #{item.proce,jdbcType=VARCHAR}, #{item.conc,jdbcType=VARCHAR}, #{item.cont,jdbcType=VARCHAR},
      #{item.comp,jdbcType=VARCHAR}, #{item.outr,jdbcType=VARCHAR}, #{item.desp,jdbcType=VARCHAR},
      #{item.despDat,jdbcType=VARCHAR}, #{item.despCargo,jdbcType=VARCHAR}, #{item.pare,jdbcType=VARCHAR},
      #{item.pareDat,jdbcType=VARCHAR}, #{item.pareCargo,jdbcType=VARCHAR}, #{item.pfmContract,jdbcType=VARCHAR},
      #{item.pohfType,jdbcType=VARCHAR}, #{item.eqEindt,jdbcType=VARCHAR}, #{item.eqWerks,jdbcType=VARCHAR},
      #{item.fixpo,jdbcType=VARCHAR}, #{item.ekgrpAllow,jdbcType=VARCHAR}, #{item.werksAllow,jdbcType=VARCHAR},
      #{item.contractAllow,jdbcType=VARCHAR}, #{item.pstypAllow,jdbcType=VARCHAR}, #{item.fixpoAllow,jdbcType=VARCHAR},
      #{item.keyIdAllow,jdbcType=VARCHAR}, #{item.aurelAllow,jdbcType=VARCHAR}, #{item.delperAllow,jdbcType=VARCHAR},
      #{item.eindtAllow,jdbcType=VARCHAR}, #{item.ltsnrAllow,jdbcType=VARCHAR}, #{item.otbLevel,jdbcType=VARCHAR},
      #{item.otbCondType,jdbcType=VARCHAR}, #{item.keyId,jdbcType=VARCHAR}, #{item.otbValue,jdbcType=DECIMAL},
      #{item.otbCurr,jdbcType=VARCHAR}, #{item.otbResValue,jdbcType=DECIMAL}, #{item.otbSpecValue,jdbcType=DECIMAL},
      #{item.sprRsnProfile,jdbcType=VARCHAR}, #{item.budgType,jdbcType=VARCHAR}, #{item.otbStatus,jdbcType=VARCHAR},
      #{item.otbReason,jdbcType=VARCHAR}, #{item.checkType,jdbcType=VARCHAR}, #{item.conOtbReq,jdbcType=VARCHAR},
      #{item.conPrebookLev,jdbcType=VARCHAR}, #{item.conDistrLev,jdbcType=VARCHAR}, #{item.zzyfye,jdbcType=VARCHAR},
      #{item.zsqhzh,jdbcType=VARCHAR}, #{item.zzsrmzt,jdbcType=VARCHAR}, #{item.zzpaid,jdbcType=VARCHAR},
      #{item.zzpamt,jdbcType=DECIMAL}, #{item.extend,jdbcType=LONGVARCHAR})
    </foreach>
  </insert>

  <insert id="batchInsertSapEKPOList" parameterType="java.util.List">
    insert into SAP_EKPO (MANDT, EBELN,
    EBELP, LOEKZ, STATU,
    AEDAT, TXZ01, MATNR,
    EMATN, BUKRS, WERKS,
    LGORT, BEDNR, MATKL,
    INFNR, IDNLF, KTMNG,
    MENGE, MEINS, BPRME,
    BPUMZ, BPUMN, UMREZ,
    UMREN, NETPR, PEINH,
    NETWR, BRTWR, AGDAT,
    WEBAZ, MWSKZ, BONUS,
    INSMK, SPINF, PRSDR,
    SCHPR, MAHNZ, MAHN1,
    MAHN2, MAHN3, UEBTO,
    UEBTK, UNTTO, BWTAR,
    BWTTY, ABSKZ, AGMEM,
    ELIKZ, EREKZ, PSTYP,
    KNTTP, KZVBR, VRTKZ,
    TWRKZ, WEPOS, WEUNB,
    REPOS, WEBRE, KZABS,
    LABNR, KONNR, KTPNR,
    ABDAT, ABFTZ, ETFZ1,
    ETFZ2, KZSTU, NOTKZ,
    LMEIN, EVERS, ZWERT,
    NAVNW, ABMNG, PRDAT,
    BSTYP, EFFWR, XOBLR,
    KUNNR, ADRNR, EKKOL,
    SKTOF, STAFO, PLIFZ,
    NTGEW, GEWEI, TXJCD,
    ETDRK, SOBKZ, ARSNR,
    ARSPS, INSNC, SSQSS,
    ZGTYP, EAN11, BSTAE,
    REVLV, GEBER, FISTL,
    FIPOS, KO_GSBER, KO_PARGB,
    KO_PRCTR, KO_PPRCTR, MEPRF,
    BRGEW, VOLUM, VOLEH,
    INCO1, INCO2, VORAB,
    KOLIF, LTSNR, PACKNO,
    FPLNR, GNETWR, STAPO,
    UEBPO, LEWED, EMLIF,
    LBLKZ, SATNR, ATTYP,
    VSART, HANDOVERLOC, KANBA,
    ADRN2, CUOBJ, XERSY,
    EILDT, DRDAT, DRUHR,
    DRUNR, AKTNR, ABELN,
    ABELP, ANZPU, PUNEI,
    SAISO, SAISJ, EBON2,
    EBON3, EBONF, MLMAA,
    MHDRZ, ANFNR, ANFPS,
    KZKFG, USEQU, UMSOK,
    BANFN, BNFPO, MTART,
    UPTYP, UPVOR, KZWI1,
    KZWI2, KZWI3, KZWI4,
    KZWI5, KZWI6, SIKGR,
    MFZHI, FFZHI, RETPO,
    AUREL, BSGRU, LFRET,
    MFRGR, NRFHG, J_1BNBM,
    J_1BMATUSE, J_1BMATORG, J_1BOWNPRO,
    J_1BINDUST, ABUEB, NLABD,
    NFABD, KZBWS, BONBA,
    FABKZ, J_1AINDXP, J_1AIDATEP,
    MPROF, EGLKZ, KZTLF,
    KZFME, RDPRF, TECHS,
    CHG_SRV, CHG_FPLNR, MFRPN,
    MFRNR, EMNFR, NOVET,
    AFNAM, TZONRC, IPRKZ,
    LEBRE, BERID, XCONDITIONS,
    APOMS, CCOMP, GRANT_NBR,
    FKBER, `STATUS`, RESLO,
    KBLNR, KBLPOS, WEORA,
    SRV_BAS_COM, PRIO_URG, PRIO_REQ,
    EMPST, DIFF_INVOICE, TRMRISK_RELEVANT,
    SPE_ABGRU, SPE_CRM_SO, SPE_CRM_SO_ITEM,
    SPE_CRM_REF_SO, SPE_CRM_REF_ITEM, SPE_CRM_FKREL,
    SPE_CHNG_SYS, SPE_INSMK_SRC, SPE_CQ_CTRLTYPE,
    SPE_CQ_NOCQ, REASON_CODE, CQU_SAR,
    ANZSN, SPE_EWM_DTC, EXLIN,
    EXSNR, EHTYP, RETPC,
    DPTYP, DPPCT, DPAMT,
    DPDAT, FLS_RSTO, EXT_RFX_NUMBER,
    EXT_RFX_ITEM, EXT_RFX_SYSTEM, SRM_CONTRACT_ID,
    SRM_CONTRACT_ITM, BLK_REASON_ID,
    BLK_REASON_TXT, ITCONS, FIXMG,
    WABWE, CMPL_DLV_ITM, INCO2_L,
    INCO3_L, STAWN, ISVCO,
    GRWRT, SERVICEPERFORMER, PRODUCTTYPE,
    REQUESTFORQUOTATION, REQUESTFORQUOTATIONITEM,
    EXTERNALREFERENCEID, TC_AUT_DET,
    MANUAL_TC_REASON, FISCAL_INCENTIVE,
    TAX_SUBJECT_ST, FISCAL_INCENTIVE_ID,
    SF_TXJCD, DUMMY_EKPO_INCL_EEW_PS,
    EXPECTED_VALUE, LIMIT_AMOUNT, ENH_DATE1,
    ENH_DATE2, ENH_PERCENT, ENH_NUMC1,
    DATAAGING, BEV1_NEGEN_ITEM, BEV1_NEDEPFREE,
    BEV1_NESTRUCCAT, ADVCODE, BUDGET_PD,
    EXCPE, FMFGUS_KEY, IUID_RELEVANT,
    MRPIND, SGT_SCAT, SGT_RCAT,
    TMS_REF_UUID, WRF_CHARSTC1, WRF_CHARSTC2,
    WRF_CHARSTC3, ZZZMDSQ, ZZZMDHH,
    ZZQXDJH, ZZQXDHH, ZZCXBJS,
    ZZQTYS, ZZSPCD, ZZSCCJ,
    ZZZSCPH, ZZZGJJ, REFSITE,
    ZAPCGK, APCGK_EXTEND, ZBAS_DATE,
    ZADATTYP, ZSTART_DAT, Z_DEV,
    ZINDANX, ZLIMIT_DAT, NUMERATOR,
    HASHCAL_BDAT, HASHCAL, NEGATIVE,
    HASHCAL_EXISTS, KNOWN_INDEX, SAPMP_GPOSE,
    ANGPN, ADMOI, ADPRI,
    LPRIO, ADACN, AFPNR,
    BSARK, AUDAT, ANGNR,
    PNSTAT, ADDNS, SERRU,
    SERNP, DISUB_SOBKZ, DISUB_PSPNR,
    DISUB_KUNNR, DISUB_VBELN, DISUB_POSNR,
    DISUB_OWNER, FSH_SEASON_YEAR, FSH_SEASON,
    FSH_COLLECTION, FSH_THEME, FSH_ATP_DATE,
    FSH_VAS_REL, FSH_VAS_PRNT_ID, FSH_TRANSACTION,
    FSH_ITEM_GROUP, FSH_ITEM, FSH_SS,
    FSH_GRID_COND_REC, FSH_PSM_PFM_SPLIT,
    CNFM_QTY, STPAC, LGBZO,
    LGBZO_B, ADDRNUM, CONSNUM,
    BORGR_MISS, DEP_ID, BELNR,
    KBLPOS_CAB, KBLNR_COMP, KBLPOS_COMP,
    WBS_ELEMENT, RFM_PSST_RULE, RFM_PSST_GROUP,
    REF_ITEM, SOURCE_ID, SOURCE_KEY,
    PUT_BACK, POL_ID, CONS_ORDER,
    ZZPAID, ZZPAMT, ZZSIGN,
    ZZDATE, ZZTIME, ZZPRICE,
    ZZPEINH, ZZMWSKZ, extend
    )
    values
    <foreach collection="dataList" item="item" separator=",">
      (#{item.mandt,jdbcType=VARCHAR}, #{item.ebeln,jdbcType=VARCHAR},
      #{item.ebelp,jdbcType=VARCHAR}, #{item.loekz,jdbcType=VARCHAR}, #{item.statu,jdbcType=VARCHAR},
      #{item.aedat,jdbcType=VARCHAR}, #{item.txz01,jdbcType=VARCHAR}, #{item.matnr,jdbcType=VARCHAR},
      #{item.ematn,jdbcType=VARCHAR}, #{item.bukrs,jdbcType=VARCHAR}, #{item.werks,jdbcType=VARCHAR},
      #{item.lgort,jdbcType=VARCHAR}, #{item.bednr,jdbcType=VARCHAR}, #{item.matkl,jdbcType=VARCHAR},
      #{item.infnr,jdbcType=VARCHAR}, #{item.idnlf,jdbcType=VARCHAR}, #{item.ktmng,jdbcType=DECIMAL},
      #{item.menge,jdbcType=DECIMAL}, #{item.meins,jdbcType=VARCHAR}, #{item.bprme,jdbcType=VARCHAR},
      #{item.bpumz,jdbcType=DECIMAL}, #{item.bpumn,jdbcType=DECIMAL}, #{item.umrez,jdbcType=DECIMAL},
      #{item.umren,jdbcType=DECIMAL}, #{item.netpr,jdbcType=DECIMAL}, #{item.peinh,jdbcType=DECIMAL},
      #{item.netwr,jdbcType=DECIMAL}, #{item.brtwr,jdbcType=DECIMAL}, #{item.agdat,jdbcType=VARCHAR},
      #{item.webaz,jdbcType=DECIMAL}, #{item.mwskz,jdbcType=VARCHAR}, #{item.bonus,jdbcType=VARCHAR},
      #{item.insmk,jdbcType=VARCHAR}, #{item.spinf,jdbcType=VARCHAR}, #{item.prsdr,jdbcType=VARCHAR},
      #{item.schpr,jdbcType=VARCHAR}, #{item.mahnz,jdbcType=DECIMAL}, #{item.mahn1,jdbcType=DECIMAL},
      #{item.mahn2,jdbcType=DECIMAL}, #{item.mahn3,jdbcType=DECIMAL}, #{item.uebto,jdbcType=DECIMAL},
      #{item.uebtk,jdbcType=VARCHAR}, #{item.untto,jdbcType=DECIMAL}, #{item.bwtar,jdbcType=VARCHAR},
      #{item.bwtty,jdbcType=VARCHAR}, #{item.abskz,jdbcType=VARCHAR}, #{item.agmem,jdbcType=VARCHAR},
      #{item.elikz,jdbcType=VARCHAR}, #{item.erekz,jdbcType=VARCHAR}, #{item.pstyp,jdbcType=VARCHAR},
      #{item.knttp,jdbcType=VARCHAR}, #{item.kzvbr,jdbcType=VARCHAR}, #{item.vrtkz,jdbcType=VARCHAR},
      #{item.twrkz,jdbcType=VARCHAR}, #{item.wepos,jdbcType=VARCHAR}, #{item.weunb,jdbcType=VARCHAR},
      #{item.repos,jdbcType=VARCHAR}, #{item.webre,jdbcType=VARCHAR}, #{item.kzabs,jdbcType=VARCHAR},
      #{item.labnr,jdbcType=VARCHAR}, #{item.konnr,jdbcType=VARCHAR}, #{item.ktpnr,jdbcType=VARCHAR},
      #{item.abdat,jdbcType=VARCHAR}, #{item.abftz,jdbcType=DECIMAL}, #{item.etfz1,jdbcType=DECIMAL},
      #{item.etfz2,jdbcType=DECIMAL}, #{item.kzstu,jdbcType=VARCHAR}, #{item.notkz,jdbcType=VARCHAR},
      #{item.lmein,jdbcType=VARCHAR}, #{item.evers,jdbcType=VARCHAR}, #{item.zwert,jdbcType=DECIMAL},
      #{item.navnw,jdbcType=DECIMAL}, #{item.abmng,jdbcType=DECIMAL}, #{item.prdat,jdbcType=VARCHAR},
      #{item.bstyp,jdbcType=VARCHAR}, #{item.effwr,jdbcType=DECIMAL}, #{item.xoblr,jdbcType=VARCHAR},
      #{item.kunnr,jdbcType=VARCHAR}, #{item.adrnr,jdbcType=VARCHAR}, #{item.ekkol,jdbcType=VARCHAR},
      #{item.sktof,jdbcType=VARCHAR}, #{item.stafo,jdbcType=VARCHAR}, #{item.plifz,jdbcType=DECIMAL},
      #{item.ntgew,jdbcType=DECIMAL}, #{item.gewei,jdbcType=VARCHAR}, #{item.txjcd,jdbcType=VARCHAR},
      #{item.etdrk,jdbcType=VARCHAR}, #{item.sobkz,jdbcType=VARCHAR}, #{item.arsnr,jdbcType=VARCHAR},
      #{item.arsps,jdbcType=VARCHAR}, #{item.insnc,jdbcType=VARCHAR}, #{item.ssqss,jdbcType=VARCHAR},
      #{item.zgtyp,jdbcType=VARCHAR}, #{item.ean11,jdbcType=VARCHAR}, #{item.bstae,jdbcType=VARCHAR},
      #{item.revlv,jdbcType=VARCHAR}, #{item.geber,jdbcType=VARCHAR}, #{item.fistl,jdbcType=VARCHAR},
      #{item.fipos,jdbcType=VARCHAR}, #{item.koGsber,jdbcType=VARCHAR}, #{item.koPargb,jdbcType=VARCHAR},
      #{item.koPrctr,jdbcType=VARCHAR}, #{item.koPprctr,jdbcType=VARCHAR}, #{item.meprf,jdbcType=VARCHAR},
      #{item.brgew,jdbcType=DECIMAL}, #{item.volum,jdbcType=DECIMAL}, #{item.voleh,jdbcType=VARCHAR},
      #{item.inco1,jdbcType=VARCHAR}, #{item.inco2,jdbcType=VARCHAR}, #{item.vorab,jdbcType=VARCHAR},
      #{item.kolif,jdbcType=VARCHAR}, #{item.ltsnr,jdbcType=VARCHAR}, #{item.packno,jdbcType=VARCHAR},
      #{item.fplnr,jdbcType=VARCHAR}, #{item.gnetwr,jdbcType=DECIMAL}, #{item.stapo,jdbcType=VARCHAR},
      #{item.uebpo,jdbcType=VARCHAR}, #{item.lewed,jdbcType=VARCHAR}, #{item.emlif,jdbcType=VARCHAR},
      #{item.lblkz,jdbcType=VARCHAR}, #{item.satnr,jdbcType=VARCHAR}, #{item.attyp,jdbcType=VARCHAR},
      #{item.vsart,jdbcType=VARCHAR}, #{item.handoverloc,jdbcType=VARCHAR}, #{item.kanba,jdbcType=VARCHAR},
      #{item.adrn2,jdbcType=VARCHAR}, #{item.cuobj,jdbcType=VARCHAR}, #{item.xersy,jdbcType=VARCHAR},
      #{item.eildt,jdbcType=VARCHAR}, #{item.drdat,jdbcType=VARCHAR}, #{item.druhr,jdbcType=VARCHAR},
      #{item.drunr,jdbcType=VARCHAR}, #{item.aktnr,jdbcType=VARCHAR}, #{item.abeln,jdbcType=VARCHAR},
      #{item.abelp,jdbcType=VARCHAR}, #{item.anzpu,jdbcType=DECIMAL}, #{item.punei,jdbcType=VARCHAR},
      #{item.saiso,jdbcType=VARCHAR}, #{item.saisj,jdbcType=VARCHAR}, #{item.ebon2,jdbcType=VARCHAR},
      #{item.ebon3,jdbcType=VARCHAR}, #{item.ebonf,jdbcType=VARCHAR}, #{item.mlmaa,jdbcType=VARCHAR},
      #{item.mhdrz,jdbcType=DECIMAL}, #{item.anfnr,jdbcType=VARCHAR}, #{item.anfps,jdbcType=VARCHAR},
      #{item.kzkfg,jdbcType=VARCHAR}, #{item.usequ,jdbcType=VARCHAR}, #{item.umsok,jdbcType=VARCHAR},
      #{item.banfn,jdbcType=VARCHAR}, #{item.bnfpo,jdbcType=VARCHAR}, #{item.mtart,jdbcType=VARCHAR},
      #{item.uptyp,jdbcType=VARCHAR}, #{item.upvor,jdbcType=VARCHAR}, #{item.kzwi1,jdbcType=DECIMAL},
      #{item.kzwi2,jdbcType=DECIMAL}, #{item.kzwi3,jdbcType=DECIMAL}, #{item.kzwi4,jdbcType=DECIMAL},
      #{item.kzwi5,jdbcType=DECIMAL}, #{item.kzwi6,jdbcType=DECIMAL}, #{item.sikgr,jdbcType=VARCHAR},
      #{item.mfzhi,jdbcType=DECIMAL}, #{item.ffzhi,jdbcType=DECIMAL}, #{item.retpo,jdbcType=VARCHAR},
      #{item.aurel,jdbcType=VARCHAR}, #{item.bsgru,jdbcType=VARCHAR}, #{item.lfret,jdbcType=VARCHAR},
      #{item.mfrgr,jdbcType=VARCHAR}, #{item.nrfhg,jdbcType=VARCHAR}, #{item.j1bnbm,jdbcType=VARCHAR},
      #{item.j1bmatuse,jdbcType=VARCHAR}, #{item.j1bmatorg,jdbcType=VARCHAR}, #{item.j1bownpro,jdbcType=VARCHAR},
      #{item.j1bindust,jdbcType=VARCHAR}, #{item.abueb,jdbcType=VARCHAR}, #{item.nlabd,jdbcType=VARCHAR},
      #{item.nfabd,jdbcType=VARCHAR}, #{item.kzbws,jdbcType=VARCHAR}, #{item.bonba,jdbcType=DECIMAL},
      #{item.fabkz,jdbcType=VARCHAR}, #{item.j1aindxp,jdbcType=VARCHAR}, #{item.j1aidatep,jdbcType=VARCHAR},
      #{item.mprof,jdbcType=VARCHAR}, #{item.eglkz,jdbcType=VARCHAR}, #{item.kztlf,jdbcType=VARCHAR},
      #{item.kzfme,jdbcType=VARCHAR}, #{item.rdprf,jdbcType=VARCHAR}, #{item.techs,jdbcType=VARCHAR},
      #{item.chgSrv,jdbcType=VARCHAR}, #{item.chgFplnr,jdbcType=VARCHAR}, #{item.mfrpn,jdbcType=VARCHAR},
      #{item.mfrnr,jdbcType=VARCHAR}, #{item.emnfr,jdbcType=VARCHAR}, #{item.novet,jdbcType=VARCHAR},
      #{item.afnam,jdbcType=VARCHAR}, #{item.tzonrc,jdbcType=VARCHAR}, #{item.iprkz,jdbcType=VARCHAR},
      #{item.lebre,jdbcType=VARCHAR}, #{item.berid,jdbcType=VARCHAR}, #{item.xconditions,jdbcType=VARCHAR},
      #{item.apoms,jdbcType=VARCHAR}, #{item.ccomp,jdbcType=VARCHAR}, #{item.grantNbr,jdbcType=VARCHAR},
      #{item.fkber,jdbcType=VARCHAR}, #{item.status,jdbcType=VARCHAR}, #{item.reslo,jdbcType=VARCHAR},
      #{item.kblnr,jdbcType=VARCHAR}, #{item.kblpos,jdbcType=VARCHAR}, #{item.weora,jdbcType=VARCHAR},
      #{item.srvBasCom,jdbcType=VARCHAR}, #{item.prioUrg,jdbcType=VARCHAR}, #{item.prioReq,jdbcType=VARCHAR},
      #{item.empst,jdbcType=VARCHAR}, #{item.diffInvoice,jdbcType=VARCHAR}, #{item.trmriskRelevant,jdbcType=VARCHAR},
      #{item.speAbgru,jdbcType=VARCHAR}, #{item.speCrmSo,jdbcType=VARCHAR}, #{item.speCrmSoItem,jdbcType=VARCHAR},
      #{item.speCrmRefSo,jdbcType=VARCHAR}, #{item.speCrmRefItem,jdbcType=VARCHAR}, #{item.speCrmFkrel,jdbcType=VARCHAR},
      #{item.speChngSys,jdbcType=VARCHAR}, #{item.speInsmkSrc,jdbcType=VARCHAR}, #{item.speCqCtrltype,jdbcType=VARCHAR},
      #{item.speCqNocq,jdbcType=VARCHAR}, #{item.reasonCode,jdbcType=VARCHAR}, #{item.cquSar,jdbcType=DECIMAL},
      #{item.anzsn,jdbcType=INTEGER}, #{item.speEwmDtc,jdbcType=VARCHAR}, #{item.exlin,jdbcType=VARCHAR},
      #{item.exsnr,jdbcType=VARCHAR}, #{item.ehtyp,jdbcType=VARCHAR}, #{item.retpc,jdbcType=DECIMAL},
      #{item.dptyp,jdbcType=VARCHAR}, #{item.dppct,jdbcType=DECIMAL}, #{item.dpamt,jdbcType=DECIMAL},
      #{item.dpdat,jdbcType=VARCHAR}, #{item.flsRsto,jdbcType=VARCHAR}, #{item.extRfxNumber,jdbcType=VARCHAR},
      #{item.extRfxItem,jdbcType=VARCHAR}, #{item.extRfxSystem,jdbcType=VARCHAR}, #{item.srmContractId,jdbcType=VARCHAR},
      #{item.srmContractItm,jdbcType=VARCHAR}, #{item.blkReasonId,jdbcType=VARCHAR},
      #{item.blkReasonTxt,jdbcType=VARCHAR}, #{item.itcons,jdbcType=VARCHAR}, #{item.fixmg,jdbcType=VARCHAR},
      #{item.wabwe,jdbcType=VARCHAR}, #{item.cmplDlvItm,jdbcType=VARCHAR}, #{item.inco2L,jdbcType=VARCHAR},
      #{item.inco3L,jdbcType=VARCHAR}, #{item.stawn,jdbcType=VARCHAR}, #{item.isvco,jdbcType=VARCHAR},
      #{item.grwrt,jdbcType=DECIMAL}, #{item.serviceperformer,jdbcType=VARCHAR}, #{item.producttype,jdbcType=VARCHAR},
      #{item.requestforquotation,jdbcType=VARCHAR}, #{item.requestforquotationitem,jdbcType=VARCHAR},
      #{item.externalreferenceid,jdbcType=VARCHAR}, #{item.tcAutDet,jdbcType=VARCHAR},
      #{item.manualTcReason,jdbcType=VARCHAR}, #{item.fiscalIncentive,jdbcType=VARCHAR},
      #{item.taxSubjectSt,jdbcType=VARCHAR}, #{item.fiscalIncentiveId,jdbcType=VARCHAR},
      #{item.sfTxjcd,jdbcType=VARCHAR}, #{item.dummyEkpoInclEewPs,jdbcType=VARCHAR},
      #{item.expectedValue,jdbcType=DECIMAL}, #{item.limitAmount,jdbcType=DECIMAL}, #{item.enhDate1,jdbcType=VARCHAR},
      #{item.enhDate2,jdbcType=VARCHAR}, #{item.enhPercent,jdbcType=DECIMAL}, #{item.enhNumc1,jdbcType=VARCHAR},
      #{item.dataaging,jdbcType=VARCHAR}, #{item.bev1NegenItem,jdbcType=VARCHAR}, #{item.bev1Nedepfree,jdbcType=VARCHAR},
      #{item.bev1Nestruccat,jdbcType=VARCHAR}, #{item.advcode,jdbcType=VARCHAR}, #{item.budgetPd,jdbcType=VARCHAR},
      #{item.excpe,jdbcType=VARCHAR}, #{item.fmfgusKey,jdbcType=VARCHAR}, #{item.iuidRelevant,jdbcType=VARCHAR},
      #{item.mrpind,jdbcType=VARCHAR}, #{item.sgtScat,jdbcType=VARCHAR}, #{item.sgtRcat,jdbcType=VARCHAR},
      #{item.tmsRefUuid,jdbcType=VARCHAR}, #{item.wrfCharstc1,jdbcType=VARCHAR}, #{item.wrfCharstc2,jdbcType=VARCHAR},
      #{item.wrfCharstc3,jdbcType=VARCHAR}, #{item.zzzmdsq,jdbcType=VARCHAR}, #{item.zzzmdhh,jdbcType=VARCHAR},
      #{item.zzqxdjh,jdbcType=VARCHAR}, #{item.zzqxdhh,jdbcType=VARCHAR}, #{item.zzcxbjs,jdbcType=VARCHAR},
      #{item.zzqtys,jdbcType=VARCHAR}, #{item.zzspcd,jdbcType=VARCHAR}, #{item.zzsccj,jdbcType=VARCHAR},
      #{item.zzzscph,jdbcType=VARCHAR}, #{item.zzzgjj,jdbcType=VARCHAR}, #{item.refsite,jdbcType=VARCHAR},
      #{item.zapcgk,jdbcType=VARCHAR}, #{item.apcgkExtend,jdbcType=VARCHAR}, #{item.zbasDate,jdbcType=VARCHAR},
      #{item.zadattyp,jdbcType=VARCHAR}, #{item.zstartDat,jdbcType=VARCHAR}, #{item.zDev,jdbcType=DECIMAL},
      #{item.zindanx,jdbcType=VARCHAR}, #{item.zlimitDat,jdbcType=VARCHAR}, #{item.numerator,jdbcType=VARCHAR},
      #{item.hashcalBdat,jdbcType=VARCHAR}, #{item.hashcal,jdbcType=VARCHAR}, #{item.negative,jdbcType=VARCHAR},
      #{item.hashcalExists,jdbcType=VARCHAR}, #{item.knownIndex,jdbcType=VARCHAR}, #{item.sapmpGpose,jdbcType=VARCHAR},
      #{item.angpn,jdbcType=VARCHAR}, #{item.admoi,jdbcType=VARCHAR}, #{item.adpri,jdbcType=VARCHAR},
      #{item.lprio,jdbcType=VARCHAR}, #{item.adacn,jdbcType=VARCHAR}, #{item.afpnr,jdbcType=VARCHAR},
      #{item.bsark,jdbcType=VARCHAR}, #{item.audat,jdbcType=VARCHAR}, #{item.angnr,jdbcType=VARCHAR},
      #{item.pnstat,jdbcType=VARCHAR}, #{item.addns,jdbcType=VARCHAR}, #{item.serru,jdbcType=VARCHAR},
      #{item.sernp,jdbcType=VARCHAR}, #{item.disubSobkz,jdbcType=VARCHAR}, #{item.disubPspnr,jdbcType=VARCHAR},
      #{item.disubKunnr,jdbcType=VARCHAR}, #{item.disubVbeln,jdbcType=VARCHAR}, #{item.disubPosnr,jdbcType=VARCHAR},
      #{item.disubOwner,jdbcType=VARCHAR}, #{item.fshSeasonYear,jdbcType=VARCHAR}, #{item.fshSeason,jdbcType=VARCHAR},
      #{item.fshCollection,jdbcType=VARCHAR}, #{item.fshTheme,jdbcType=VARCHAR}, #{item.fshAtpDate,jdbcType=VARCHAR},
      #{item.fshVasRel,jdbcType=VARCHAR}, #{item.fshVasPrntId,jdbcType=VARCHAR}, #{item.fshTransaction,jdbcType=VARCHAR},
      #{item.fshItemGroup,jdbcType=VARCHAR}, #{item.fshItem,jdbcType=VARCHAR}, #{item.fshSs,jdbcType=VARCHAR},
      #{item.fshGridCondRec,jdbcType=VARCHAR}, #{item.fshPsmPfmSplit,jdbcType=VARCHAR},
      #{item.cnfmQty,jdbcType=DECIMAL}, #{item.stpac,jdbcType=VARCHAR}, #{item.lgbzo,jdbcType=VARCHAR},
      #{item.lgbzoB,jdbcType=VARCHAR}, #{item.addrnum,jdbcType=VARCHAR}, #{item.consnum,jdbcType=VARCHAR},
      #{item.borgrMiss,jdbcType=VARCHAR}, #{item.depId,jdbcType=VARCHAR}, #{item.belnr,jdbcType=VARCHAR},
      #{item.kblposCab,jdbcType=VARCHAR}, #{item.kblnrComp,jdbcType=VARCHAR}, #{item.kblposComp,jdbcType=VARCHAR},
      #{item.wbsElement,jdbcType=VARCHAR}, #{item.rfmPsstRule,jdbcType=VARCHAR}, #{item.rfmPsstGroup,jdbcType=VARCHAR},
      #{item.refItem,jdbcType=VARCHAR}, #{item.sourceId,jdbcType=VARCHAR}, #{item.sourceKey,jdbcType=VARCHAR},
      #{item.putBack,jdbcType=VARCHAR}, #{item.polId,jdbcType=VARCHAR}, #{item.consOrder,jdbcType=VARCHAR},
      #{item.zzpaid,jdbcType=VARCHAR}, #{item.zzpamt,jdbcType=DECIMAL}, #{item.zzsign,jdbcType=DECIMAL},
      #{item.zzdate,jdbcType=VARCHAR}, #{item.zztime,jdbcType=VARCHAR}, #{item.zzprice,jdbcType=VARCHAR},
      #{item.zzpeinh,jdbcType=VARCHAR}, #{item.zzmwskz,jdbcType=VARCHAR}, #{item.extend,jdbcType=LONGVARCHAR}
      )
    </foreach>
  </insert>

  <insert id="batchInsertSapEKBEList" parameterType="java.util.List">
    insert into SAP_EKBE (MANDT, EBELN,
    EBELP, ZEKKN, VGABE,
    GJAHR, BELNR, BUZEI,
    BEWTP, BWART, BUDAT,
    MENGE, BPMNG, DMBTR,
    WRBTR, WAERS, AREWR,
    WESBS, BPWES, SHKZG,
    BWTAR, ELIKZ, XBLNR,
    LFGJA, LFBNR, LFPOS,
    GRUND, CPUDT, CPUTM,
    REEWR, EVERE, REFWR,
    MATNR, WERKS, XWSBR,
    ETENS, KNUMV, MWSKZ,
    LSMNG, LSMEH, EMATN,
    AREWW, HSWAE, BAMNG,
    CHARG, BLDAT, XWOFF,
    XUNPL, ERNAM, SRVPOS,
    PACKNO, INTROW, BEKKN,
    LEMIN, AREWB, REWRB,
    SAPRL, MENGE_POP, BPMNG_POP,
    DMBTR_POP, WRBTR_POP, WESBB,
    BPWEB, WEORA, AREWR_POP,
    KUDIF, RETAMT_FC, RETAMT_LC,
    RETAMTP_FC, RETAMTP_LC, XMACC,
    WKURS, INV_ITEM_ORIGIN, VBELN_ST,
    VBELP_ST, SGT_SCAT, DATAAGING,
    SESUOM, ET_UPD, CWM_BAMNG,
    CWM_WESBS, CWM_TY2TQ, CWM_WESBB,
    J_SC_DIE_COMP_F, FSH_SEASON_YEAR, FSH_SEASON,
    FSH_COLLECTION, FSH_THEME, QTY_DIFF,
    WRF_CHARSTC1, WRF_CHARSTC2, WRF_CHARSTC3,
    ZZDATE, ZZTIME, extend
    )
    values
    <foreach collection="dataList" item="item" separator=",">
      (#{item.mandt,jdbcType=VARCHAR}, #{item.ebeln,jdbcType=VARCHAR},
      #{item.ebelp,jdbcType=VARCHAR}, #{item.zekkn,jdbcType=VARCHAR}, #{item.vgabe,jdbcType=VARCHAR},
      #{item.gjahr,jdbcType=VARCHAR}, #{item.belnr,jdbcType=VARCHAR}, #{item.buzei,jdbcType=VARCHAR},
      #{item.bewtp,jdbcType=VARCHAR}, #{item.bwart,jdbcType=VARCHAR}, #{item.budat,jdbcType=VARCHAR},
      #{item.menge,jdbcType=DECIMAL}, #{item.bpmng,jdbcType=DECIMAL}, #{item.dmbtr,jdbcType=DECIMAL},
      #{item.wrbtr,jdbcType=DECIMAL}, #{item.waers,jdbcType=VARCHAR}, #{item.arewr,jdbcType=DECIMAL},
      #{item.wesbs,jdbcType=DECIMAL}, #{item.bpwes,jdbcType=DECIMAL}, #{item.shkzg,jdbcType=VARCHAR},
      #{item.bwtar,jdbcType=VARCHAR}, #{item.elikz,jdbcType=VARCHAR}, #{item.xblnr,jdbcType=VARCHAR},
      #{item.lfgja,jdbcType=VARCHAR}, #{item.lfbnr,jdbcType=VARCHAR}, #{item.lfpos,jdbcType=VARCHAR},
      #{item.grund,jdbcType=VARCHAR}, #{item.cpudt,jdbcType=VARCHAR}, #{item.cputm,jdbcType=VARCHAR},
      #{item.reewr,jdbcType=DECIMAL}, #{item.evere,jdbcType=VARCHAR}, #{item.refwr,jdbcType=DECIMAL},
      #{item.matnr,jdbcType=VARCHAR}, #{item.werks,jdbcType=VARCHAR}, #{item.xwsbr,jdbcType=VARCHAR},
      #{item.etens,jdbcType=VARCHAR}, #{item.knumv,jdbcType=VARCHAR}, #{item.mwskz,jdbcType=VARCHAR},
      #{item.lsmng,jdbcType=DECIMAL}, #{item.lsmeh,jdbcType=VARCHAR}, #{item.ematn,jdbcType=VARCHAR},
      #{item.areww,jdbcType=DECIMAL}, #{item.hswae,jdbcType=VARCHAR}, #{item.bamng,jdbcType=DECIMAL},
      #{item.charg,jdbcType=VARCHAR}, #{item.bldat,jdbcType=VARCHAR}, #{item.xwoff,jdbcType=VARCHAR},
      #{item.xunpl,jdbcType=VARCHAR}, #{item.ernam,jdbcType=VARCHAR}, #{item.srvpos,jdbcType=VARCHAR},
      #{item.packno,jdbcType=VARCHAR}, #{item.introw,jdbcType=VARCHAR}, #{item.bekkn,jdbcType=VARCHAR},
      #{item.lemin,jdbcType=VARCHAR}, #{item.arewb,jdbcType=DECIMAL}, #{item.rewrb,jdbcType=DECIMAL},
      #{item.saprl,jdbcType=VARCHAR}, #{item.mengePop,jdbcType=DECIMAL}, #{item.bpmngPop,jdbcType=DECIMAL},
      #{item.dmbtrPop,jdbcType=DECIMAL}, #{item.wrbtrPop,jdbcType=DECIMAL}, #{item.wesbb,jdbcType=DECIMAL},
      #{item.bpweb,jdbcType=DECIMAL}, #{item.weora,jdbcType=VARCHAR}, #{item.arewrPop,jdbcType=DECIMAL},
      #{item.kudif,jdbcType=DECIMAL}, #{item.retamtFc,jdbcType=DECIMAL}, #{item.retamtLc,jdbcType=DECIMAL},
      #{item.retamtpFc,jdbcType=DECIMAL}, #{item.retamtpLc,jdbcType=DECIMAL}, #{item.xmacc,jdbcType=VARCHAR},
      #{item.wkurs,jdbcType=DECIMAL}, #{item.invItemOrigin,jdbcType=VARCHAR}, #{item.vbelnSt,jdbcType=VARCHAR},
      #{item.vbelpSt,jdbcType=VARCHAR}, #{item.sgtScat,jdbcType=VARCHAR}, #{item.dataaging,jdbcType=VARCHAR},
      #{item.sesuom,jdbcType=VARCHAR}, #{item.etUpd,jdbcType=VARCHAR}, #{item.cwmBamng,jdbcType=DECIMAL},
      #{item.cwmWesbs,jdbcType=DECIMAL}, #{item.cwmTy2tq,jdbcType=VARCHAR}, #{item.cwmWesbb,jdbcType=DECIMAL},
      #{item.jScDieCompF,jdbcType=VARCHAR}, #{item.fshSeasonYear,jdbcType=VARCHAR}, #{item.fshSeason,jdbcType=VARCHAR},
      #{item.fshCollection,jdbcType=VARCHAR}, #{item.fshTheme,jdbcType=VARCHAR}, #{item.qtyDiff,jdbcType=DECIMAL},
      #{item.wrfCharstc1,jdbcType=VARCHAR}, #{item.wrfCharstc2,jdbcType=VARCHAR}, #{item.wrfCharstc3,jdbcType=VARCHAR},
      #{item.zzdate,jdbcType=VARCHAR}, #{item.zztime,jdbcType=VARCHAR}, #{item.extend,jdbcType=LONGVARCHAR}
      )
    </foreach>
  </insert>

</mapper>
