<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.extend.IscmAllotTrackRegisterExtendMapper">
    <select id="findByQuery"
            resultType="com.cowell.iscm.service.dto.visualcenter.allottrack.RegisterDTO"
            parameterType="com.cowell.iscm.service.dto.visualcenter.allottrack.TrackQuery">
        <include refid="querySQL_20210325" />
        limit #{page * pageSize}, #{pageSize}
    </select>

    <select id="countByQuery"
            resultType="java.lang.Long"
            parameterType="com.cowell.iscm.service.dto.visualcenter.allottrack.TrackQuery">
        select count(*) from (<include refid="querySQL_20210325" />) t
    </select>

    <sql id="querySQL">
        select
            register.register_type as allotType,
            register.register_order_no as registerNo,
            register.goods_no as goodsNo,
            register.goods_common_name as goodsCommonName,
            register.description as goodsDesc,
            register.goods_unit as goodsUnit,
            register.manufacturer,
            register.batch_no as batchNo,
            date_format(register.validity_date, '%Y-%m-%d') as validityDate,
            register.register_quantity as registerQuantity,
            register.cost_amount as costAmount,
            register.store_org_name as storeOrgName,
            register.company_org_name as companyOrgName,
            register.platform_org_name as platformOrgName,
            register.register_source as registerSource,
            date_format(register.gmt_create, '%Y-%m-%d') as registerDate
        from (
            select
                order_detail.register_type,
                order_detail.register_order_no,
                order_detail.goods_no,
                order_detail.goods_common_name,
                order_detail.description,
                order_detail.goods_unit,
                order_detail.manufacturer,
                order_detail.batch_no,
                order_detail.validity_date,
                order_detail.register_quantity,
                order_detail.cost_amount,
                order_.store_org_id,
                order_.store_org_name,
                order_.company_org_id,
                order_.company_org_name,
                order_.platform_org_id,
                order_.platform_org_name,
                order_.register_source,
                order_.gmt_create
            from iscm_goods_register_order_detail order_detail
            left join iscm_goods_register_order order_
            on order_detail.register_order_no = order_.register_order_no
            <where>
                and order_detail.register_month = #{registerMonth,jdbcType=INTEGER}
                and order_detail.register_type = #{allotType,jdbcType=TINYINT}
                and order_detail.register_order_no = #{registerNo,jdbcType=VARCHAR}
                and order_detail.goods_no = #{goodsNo,jdbcType=VARCHAR}
                and order_detail.goods_common_name = #{goodsCommonName,jdbcType=VARCHAR}
                and order_.register_source = #{registerSource,jdbcType=TINYINT}
                and order_.company_org_id in
                <foreach collection="outCompanyOrgIds" index="index" item="item" open="("  separator="," close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
                and order_.store_org_id in
                <foreach collection="outStoreOrgIds" index="index" item="item" open="("  separator="," close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </where>
        ) register
        left join
        (
            select id,register_no from iscm_suggest_allot_goods_detail allot_detail
            <where>
                and allot_detail.business_date between #{sdate,jdbcType=TIMESTAMP} and #{edate,jdbcType=TIMESTAMP}
                and allot_detail.allot_type = #{allotType,jdbcType=TINYINT}
                and allot_detail.register_source = #{registerSource,jdbcType=TINYINT}
                and allot_detail.register_no = #{registerNo,jdbcType=VARCHAR}
                and allot_detail.goods_no = #{goodsNo,jdbcType=VARCHAR}
                and allot_detail.goods_common_name = #{goodsCommonName,jdbcType=VARCHAR}
                and allot_detail.out_company_id in
                <foreach collection="outCompanyOrgIds" index="index" item="item" open="("  separator="," close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
                and allot_detail.out_store_id in
                <foreach collection="outStoreOrgIds" index="index" item="item" open="("  separator="," close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </where>
        ) allot
        on register.register_order_no = allot.register_no
        where allot.id is null
        order by
            register.register_type,
            register.gmt_create desc,
            register.company_org_id,
            register.store_org_id,
            register.register_order_no,
            register.goods_no
    </sql>

    <sql id="querySQL_20210325">
        select
            order_detail.register_type as allotType,
            order_detail.register_order_no as registerNo,
            order_detail.goods_no as goodsNo,
            order_detail.goods_common_name as goodsCommonName,
            order_detail.description as goodsDesc,
            order_detail.goods_unit as goodsUnit,
            order_detail.manufacturer,
            order_detail.batch_no as batchNo,
            date_format(order_detail.validity_date, '%Y-%m-%d') as validityDate,
            order_detail.register_quantity as registerQuantity,
            order_detail.cost_amount as costAmount,
            order_.store_org_name as storeOrgName,
            order_.company_org_name as companyOrgName,
            order_.platform_org_name as platformOrgName,
            order_.register_source as registerSource,
            date_format(order_.gmt_create, '%Y-%m-%d') as registerDate
        from iscm_goods_register_order_detail order_detail
        left join iscm_goods_register_order order_
        on order_detail.register_order_no = order_.register_order_no
        where order_detail.register_month = #{registerMonth,jdbcType=INTEGER}
        and order_detail.register_type = #{allotType,jdbcType=TINYINT}
        and order_detail.register_order_no = #{registerNo,jdbcType=VARCHAR}
        and order_detail.goods_no = #{goodsNo,jdbcType=VARCHAR}
        and order_detail.goods_common_name = #{goodsCommonName,jdbcType=VARCHAR}
        and order_.register_source = #{registerSource,jdbcType=TINYINT}
        and order_.company_org_id in
        <foreach collection="outCompanyOrgIds" index="index" item="item" open="("  separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
        and order_.store_org_id in
        <foreach collection="outStoreOrgIds" index="index" item="item" open="("  separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
        and not exists (
            select 1 from iscm_suggest_allot_goods_detail allot_detail
            where allot_detail.register_no = order_detail.register_order_no
            and allot_detail.business_date between #{sdate,jdbcType=TIMESTAMP} and #{edate,jdbcType=TIMESTAMP}
            and allot_detail.allot_type = #{allotType,jdbcType=TINYINT}
            and allot_detail.register_source = #{registerSource,jdbcType=TINYINT}
            and allot_detail.register_no = #{registerNo,jdbcType=VARCHAR}
            and allot_detail.goods_no = #{goodsNo,jdbcType=VARCHAR}
            and allot_detail.goods_common_name = #{goodsCommonName,jdbcType=VARCHAR}
            and allot_detail.out_company_id in
            <foreach collection="outCompanyOrgIds" index="index" item="item" open="("  separator="," close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
            and allot_detail.out_store_id in
            <foreach collection="outStoreOrgIds" index="index" item="item" open="("  separator="," close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        )
        order by
            order_detail.register_type,
            order_.gmt_create desc,
            order_.company_org_id,
            order_.store_org_id,
            order_detail.register_order_no,
            order_detail.goods_no
    </sql>
</mapper>
