<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.extend.IscmZdtTaskExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.IscmZdtTask">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="task_key" jdbcType="VARCHAR" property="taskKey" />
    <result column="task_key_type" jdbcType="TINYINT" property="taskKeyType" />
    <result column="task_key_type_desc" jdbcType="VARCHAR" property="taskKeyTypeDesc" />
    <result column="type_id" jdbcType="BIGINT" property="typeId" />
    <result column="type_code" jdbcType="VARCHAR" property="typeCode" />
    <result column="task_name" jdbcType="VARCHAR" property="taskName" />
    <result column="task_mark" jdbcType="VARCHAR" property="taskMark" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.cowell.iscm.entity.IscmZdtTask">
    <result column="task_info" jdbcType="LONGVARCHAR" property="taskInfo" />
  </resultMap>
  <sql id="Base_Column_List">
    id, task_key, task_key_type, task_key_type_desc, type_id, type_code, task_name, task_mark,
    status, gmt_create, gmt_update, extend, version, created_by, created_name, updated_by,
    updated_name
  </sql>
  <sql id="Blob_Column_List">
    task_info
  </sql>
  <insert id="batchInsert" parameterType="com.cowell.iscm.entity.IscmZdtTask" useGeneratedKeys="true" keyProperty="id">
    insert into iscm_zdt_task (task_key, task_key_type, task_key_type_desc,
      type_id, type_code, task_name, 
      task_mark, gmt_create, gmt_update,
      created_by, created_name, updated_by, 
      updated_name, task_info)
    values
    <foreach collection="list" item="item" index="index" separator="," >
    ( #{item.taskKey,jdbcType=VARCHAR}, #{item.taskKeyType,jdbcType=TINYINT}, #{item.taskKeyTypeDesc,jdbcType=VARCHAR},
      #{item.typeId,jdbcType=BIGINT}, #{item.typeCode,jdbcType=VARCHAR}, #{item.taskName,jdbcType=VARCHAR},
      #{item.taskMark,jdbcType=VARCHAR}, #{item.gmtCreate,jdbcType=TIMESTAMP}, #{item.gmtUpdate,jdbcType=TIMESTAMP},
      #{item.createdBy,jdbcType=BIGINT}, #{item.createdName,jdbcType=VARCHAR}, #{item.updatedBy,jdbcType=BIGINT},
      #{item.updatedName,jdbcType=VARCHAR}, #{item.taskInfo,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <select id="getMaxDateByTypeCode" resultType="java.util.Date">
        select max(gmt_create) from iscm_zdt_task
    where type_code = #{typeCode}
  </select>
  <select id="getLastTaskIdByTypeCodeAndTaskKey" resultType="java.lang.Long">
    select max(id) maxId from iscm_zdt_task
    where type_code = #{typeCode}
    and task_key = #{taskKey}
  </select>
    <select id="selectIdsByTypeIdAndTypeCodeWithoutTaskKey" resultType="java.lang.Long">
      select id from iscm_zdt_task
          where type_code = #{typeCode}
            and type_id = #{typeId}
            <if test="taskKeys != null and taskKeys.size > 0">
              and task_key not in
              <foreach collection="taskKeys" item="taskKey" index="index" open="(" close=")" separator=",">
                #{taskKey}
              </foreach>
            </if>
    </select>
</mapper>
