<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.extend.IscmReturnWarehouseProcessExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.IscmReturnWarehouseProcess">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="process_type" jdbcType="TINYINT" property="processType" />
    <result column="platform_org_id" jdbcType="BIGINT" property="platformOrgId" />
    <result column="cache_key" jdbcType="VARCHAR" property="cacheKey" />
    <result column="cache_value" jdbcType="TINYINT" property="cacheValue" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, process_type, platform_org_id, cache_key, cache_value, created_by, created_name, 
    gmt_create, gmt_update
  </sql>
  <insert id="batchInsert" parameterType="com.cowell.iscm.entity.IscmReturnWarehouseProcess">
    insert into iscm_return_warehouse_process (process_type, platform_org_id, cache_key, 
      cache_value, created_by, created_name)
    values
    <foreach collection="list" item="item" index="index" separator=",">
    (#{item.processType,jdbcType=TINYINT}, #{item.platformOrgId,jdbcType=BIGINT}, #{item.cacheKey,jdbcType=VARCHAR},
      #{item.cacheValue,jdbcType=TINYINT}, #{item.createdBy,jdbcType=BIGINT}, #{item.createdName,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <update id="updateValue">
        update iscm_return_warehouse_process set cache_value = #{cacheValue}
    where platform_org_id = #{platformOrgId}
    and created_by = #{userId}
    and process_type = #{processType}
    and cache_key = #{cacheKey}
  </update>
  <delete id="deleteByPlatformOrgIdAndUserId">
    delete from iscm_return_warehouse_process
    where platform_org_id = #{platformOrgId}
    and created_by = #{userId}
    and process_type = #{processType}
    <if test="cacheKeys != null and cacheKeys.size > 0">
      and cache_key in
      <foreach collection="cacheKeys" item="cacheKey" index="index" separator="," open="(" close=")">
        #{cacheKey}
      </foreach>
    </if>
  </delete>

  <select id="selectCache" resultType="com.cowell.iscm.service.dto.returnWarehouse.CacheDTO">
    select cache_key as cacheKey, cache_value as cacheValue
    from iscm_return_warehouse_process
    where platform_org_id = #{platformOrgId}
    and created_by = #{userId}
    and process_type = #{processType}
    <if test="cacheKeys != null and cacheKeys.size > 0">
      and cache_key in
      <foreach collection="cacheKeys" item="cacheKey" index="index" separator="," open="(" close=")">
        #{cacheKey}
      </foreach>
    </if>
  </select>
</mapper>
