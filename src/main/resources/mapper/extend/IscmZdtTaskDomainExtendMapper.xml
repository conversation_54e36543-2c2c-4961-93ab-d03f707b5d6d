<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.extend.IscmZdtTaskDomainExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.IscmZdtTaskDomain">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="domain_key" jdbcType="VARCHAR" property="domainKey" />
    <result column="domain_key_type" jdbcType="TINYINT" property="domainKeyType" />
    <result column="domain_key_type_desc" jdbcType="VARCHAR" property="domainKeyTypeDesc" />
    <result column="type_id" jdbcType="BIGINT" property="typeId" />
    <result column="type_code" jdbcType="VARCHAR" property="typeCode" />
    <result column="task_name" jdbcType="VARCHAR" property="taskName" />
    <result column="domain_mark" jdbcType="VARCHAR" property="domainMark" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.cowell.iscm.entity.IscmZdtTaskDomain">
    <result column="domain_info" jdbcType="LONGVARCHAR" property="domainInfo" />
  </resultMap>
  <sql id="Base_Column_List">
    id, domain_key, domain_key_type, domain_key_type_desc, type_id, type_code, task_name, 
    domain_mark, status, gmt_create, gmt_update, extend, version, created_by, created_name, 
    updated_by, updated_name
  </sql>
  <sql id="Blob_Column_List">
    domain_info
  </sql>
  <insert id="batchInsert" parameterType="com.cowell.iscm.entity.IscmZdtTaskDomain">
    insert into iscm_zdt_task_domain (domain_key, domain_key_type,
      domain_key_type_desc, type_id, type_code, 
      task_name, domain_mark, created_by, created_name,
      updated_by, updated_name, domain_info
      )
    values
    <foreach collection="list" item="item" index="index" separator="," >
    (#{item.domainKey,jdbcType=VARCHAR}, #{item.domainKeyType,jdbcType=TINYINT},
      #{item.domainKeyTypeDesc,jdbcType=VARCHAR}, #{item.typeId,jdbcType=BIGINT}, #{item.typeCode,jdbcType=VARCHAR},
      #{item.taskName,jdbcType=VARCHAR}, #{item.domainMark,jdbcType=VARCHAR}, #{item.createdBy,jdbcType=BIGINT}, #{item.createdName,jdbcType=VARCHAR},
      #{item.updatedBy,jdbcType=BIGINT}, #{item.updatedName,jdbcType=VARCHAR}, #{item.domainInfo,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>
  <select id="selectNoDelDomainsByTypeIdAndTypeCode" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from iscm_zdt_task_domain
    where type_id = #{typeId}
    and type_code = #{typeCode}
    and status = 0
    <if test="domainIds != null and domainIds.size > 0">
      and id not in
      <foreach collection="domainIds" item="domainId" index="index" open="(" close=")" separator=",">
        #{domainId}
      </foreach>
    </if>
  </select>
</mapper>
