<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.extend.IscmStoreApplyParamNoAutoApplyStoreExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.IscmStoreApplyParamNoAutoApplyStore">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="param_code" jdbcType="VARCHAR" property="paramCode" />
    <result column="param_name" jdbcType="VARCHAR" property="paramName" />
    <result column="param_level" jdbcType="INTEGER" property="paramLevel" />
    <result column="org_id" jdbcType="BIGINT" property="orgId" />
    <result column="sap_code" jdbcType="VARCHAR" property="sapCode" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="parent_org_id" jdbcType="BIGINT" property="parentOrgId" />
    <result column="parent_org_name" jdbcType="VARCHAR" property="parentOrgName" />
    <result column="inherit_type" jdbcType="TINYINT" property="inheritType" />
    <result column="store_org_id" jdbcType="BIGINT" property="storeOrgId" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="store_name" jdbcType="VARCHAR" property="storeName" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, param_code, param_name, param_level, org_id, sap_code, org_name, parent_org_id, 
    parent_org_name, inherit_type, store_org_id, store_code, store_name, `status`, extend, 
    version, created_by, created_name, updated_by, updated_name, gmt_create, gmt_update
  </sql>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entity.IscmStoreApplyParamNoAutoApplyStore" useGeneratedKeys="true">
    insert into iscm_store_apply_param_no_auto_apply_store (param_code, param_name, param_level, 
      org_id, sap_code, org_name, 
      parent_org_id, parent_org_name, inherit_type, 
      store_org_id, store_code, store_name, 
      created_by, created_name, updated_by,
      updated_name
      )
    values
    <foreach collection="list" item="item" index="index" separator="," >
    (#{item.paramCode,jdbcType=VARCHAR}, #{item.paramName,jdbcType=VARCHAR}, #{item.paramLevel,jdbcType=INTEGER},
      #{item.orgId,jdbcType=BIGINT}, #{item.sapCode,jdbcType=VARCHAR}, #{item.orgName,jdbcType=VARCHAR},
      #{item.parentOrgId,jdbcType=BIGINT}, #{item.parentOrgName,jdbcType=VARCHAR}, #{item.inheritType,jdbcType=TINYINT},
      #{item.storeOrgId,jdbcType=BIGINT}, #{item.storeCode,jdbcType=VARCHAR}, #{item.storeName,jdbcType=VARCHAR},
      #{item.createdBy,jdbcType=BIGINT}, #{item.createdName,jdbcType=VARCHAR}, #{item.updatedBy,jdbcType=BIGINT},
      #{item.updatedName,jdbcType=VARCHAR}
    )
    </foreach>
  </insert>
</mapper>
