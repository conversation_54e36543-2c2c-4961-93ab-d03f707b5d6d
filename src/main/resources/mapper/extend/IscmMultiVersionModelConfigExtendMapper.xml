<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.extend.IscmMultiVersionModelConfigExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.IscmMultiVersionModelConfig">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="model_code" jdbcType="VARCHAR" property="modelCode" />
    <result column="model_name" jdbcType="VARCHAR" property="modelName" />
    <result column="model_desc" jdbcType="VARCHAR" property="modelDesc" />
    <result column="business_scope" jdbcType="TINYINT" property="businessScope" />
    <result column="bdp_task" jdbcType="VARCHAR" property="bdpTask" />
    <result column="model_status" jdbcType="TINYINT" property="modelStatus" />
    <result column="effect_date" jdbcType="TIMESTAMP" property="effectDate" />
    <result column="invalid_date" jdbcType="TIMESTAMP" property="invalidDate" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
  </resultMap>
  <sql id="Base_Column_List">
    id, model_code, model_name, model_desc, business_scope, bdp_task, model_status, effect_date, 
    invalid_date, status, extend, version, created_by, created_name, updated_by, updated_name, 
    gmt_create, gmt_update
  </sql>
  <insert id="batchInsert" parameterType="java.util.List">
    insert into iscm_multi_version_model_config (model_code, model_name,
      model_desc, business_scope, bdp_task, 
      model_status, effect_date, invalid_date, 
      status,
      created_by, created_name, updated_by, 
      updated_name
      )
    values
    <foreach collection="list" item="item" index="index" separator="," >
    (#{item.modelCode,jdbcType=VARCHAR}, #{item.modelName,jdbcType=VARCHAR},
      #{item.modelDesc,jdbcType=VARCHAR}, #{item.businessScope,jdbcType=TINYINT}, #{item.bdpTask,jdbcType=VARCHAR},
      #{item.modelStatus,jdbcType=TINYINT}, #{item.effectDate,jdbcType=TIMESTAMP}, #{item.invalidDate,jdbcType=TIMESTAMP},
      #{item.status,jdbcType=TINYINT},
      #{item.createdBy,jdbcType=BIGINT}, #{item.createdName,jdbcType=VARCHAR}, #{item.updatedBy,jdbcType=BIGINT},
      #{item.updatedName,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>

</mapper>
