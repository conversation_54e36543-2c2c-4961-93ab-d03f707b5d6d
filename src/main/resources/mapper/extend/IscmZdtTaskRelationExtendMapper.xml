<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.extend.IscmZdtTaskRelationExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.IscmZdtTaskRelation">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="domain_id" jdbcType="BIGINT" property="domainId" />
    <result column="task_id" jdbcType="BIGINT" property="taskId" />
  </resultMap>
  <sql id="Base_Column_List">
    id, domain_id, task_id
  </sql>
  <insert id="batchInsert" parameterType="com.cowell.iscm.entity.IscmZdtTaskRelation">
    insert into iscm_zdt_task_relation ( domain_id, task_id
      )
    values
    <foreach collection="list" item="item" index="index" separator="," >
    ( #{item.domainId,jdbcType=BIGINT}, #{item.taskId,jdbcType=BIGINT}
      )
    </foreach>
  </insert>

  <select id="selectDomainIds" resultType="java.lang.Long">
    select domain_id from iscm_zdt_task_relation
    where 1=1
    <if test="taskIds != null and taskIds.size > 0">
        and task_id not in
    <foreach collection="taskIds" item="taskId" index="index" open="(" close=")" separator=",">
      #{taskId}
    </foreach>
    </if>
  </select>

</mapper>
