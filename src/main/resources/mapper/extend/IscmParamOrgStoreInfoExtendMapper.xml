<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.extend.IscmParamOrgStoreInfoExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.IscmParamOrgStoreInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="param_id" jdbcType="BIGINT" property="paramId" />
    <result column="param_unique_mark" jdbcType="VARCHAR" property="paramUniqueMark" />
    <result column="platform_org_id" jdbcType="BIGINT" property="platformOrgId" />
    <result column="platform_org_name" jdbcType="VARCHAR" property="platformOrgName" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="store_name" jdbcType="VARCHAR" property="storeName" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
  </resultMap>
  <sql id="Base_Column_List">
    id, param_id, param_unique_mark, platform_org_id, platform_org_name, company_id, 
    company_code, company_name, store_id, store_code, store_name, status, extend, version, 
    created_by, created_name, updated_by, updated_name, gmt_create, gmt_update
  </sql>
  <insert id="batchInsert" parameterType="com.cowell.iscm.entity.IscmParamOrgStoreInfo">
    insert into iscm_param_org_store_info (param_id, param_unique_mark,
      platform_org_id, platform_org_name, company_id, 
      company_code, company_name, store_id, 
      store_code, store_name, created_by,
      created_name, updated_by, updated_name)
    values
    <foreach collection="list" item="item" index="index" separator="," >
    (#{item.paramId,jdbcType=BIGINT}, #{item.paramUniqueMark,jdbcType=VARCHAR},
      #{item.platformOrgId,jdbcType=BIGINT}, #{item.platformOrgName,jdbcType=VARCHAR}, #{item.companyId,jdbcType=BIGINT},
      #{item.companyCode,jdbcType=VARCHAR}, #{item.companyName,jdbcType=VARCHAR}, #{item.storeId,jdbcType=BIGINT},
      #{item.storeCode,jdbcType=VARCHAR}, #{item.storeName,jdbcType=VARCHAR}, #{item.createdBy,jdbcType=BIGINT},
      #{item.createdName,jdbcType=VARCHAR}, #{item.updatedBy,jdbcType=BIGINT}, #{item.updatedName,jdbcType=VARCHAR})
    </foreach>
  </insert>
    <delete id="deleteByParamUniqueMark">
      delete from iscm_param_org_store_info where param_unique_mark = #{paramUniqueMark}
    </delete>
    <select id="selectStoreOrgIdsByParamUniqueMark" resultType="java.lang.Long">
    select store_id from iscm_param_org_store_info where param_unique_mark = #{paramUniqueMark}
  </select>
    <select id="selectStoreCodesByParamUniqueMark" resultType="java.lang.String">
      select store_code from iscm_param_org_store_info where param_unique_mark = #{paramUniqueMark}
    </select>
    <select id="selectStoreInfoByParamUniqueMarksAndStoreCodes" resultType="com.cowell.iscm.entity.IscmParamOrgStoreInfo">
      select param_unique_mark as paramUniqueMark, store_code as storeCode from iscm_param_org_store_info
      where param_unique_mark in
        <foreach collection="paramUniqueMarks" item="paramUniqueMark" index="index" open="(" close=")" separator="," >
          #{paramUniqueMark, jdbcType=VARCHAR}
        </foreach>
        and store_code in
        <foreach collection="storeCodes" item="storeCode" index="index" open="(" close=")" separator="," >
          #{storeCode, jdbcType=VARCHAR}
        </foreach>
    </select>
</mapper>
