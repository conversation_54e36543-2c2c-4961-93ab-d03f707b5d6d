<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.extend.SapZmmt0287ExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.SapZmmt0287">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="MANDT" jdbcType="VARCHAR" property="mandt" />
    <result column="PURREQNO" jdbcType="VARCHAR" property="purreqno" />
    <result column="STORELINENO" jdbcType="VARCHAR" property="storelineno" />
    <result column="BUKRS" jdbcType="VARCHAR" property="bukrs" />
    <result column="EKORG" jdbcType="VARCHAR" property="ekorg" />
    <result column="WERKS" jdbcType="VARCHAR" property="werks" />
    <result column="ZZZZSHD" jdbcType="VARCHAR" property="zzzzshd" />
    <result column="BSART" jdbcType="VARCHAR" property="bsart" />
    <result column="MATNR" jdbcType="VARCHAR" property="matnr" />
    <result column="MENGE" jdbcType="DECIMAL" property="menge" />
    <result column="ZSPSL" jdbcType="DECIMAL" property="zspsl" />
    <result column="MEINS" jdbcType="VARCHAR" property="meins" />
    <result column="ZZCXBJS" jdbcType="VARCHAR" property="zzcxbjs" />
    <result column="ZJHBHSL" jdbcType="DECIMAL" property="zjhbhsl" />
    <result column="ZYYBZ" jdbcType="VARCHAR" property="zyybz" />
    <result column="BADAT" jdbcType="VARCHAR" property="badat" />
    <result column="ZSPZT" jdbcType="VARCHAR" property="zspzt" />
    <result column="ZCLZT" jdbcType="VARCHAR" property="zclzt" />
    <result column="LOEKZ" jdbcType="VARCHAR" property="loekz" />
    <result column="ERDAT" jdbcType="VARCHAR" property="erdat" />
    <result column="ERZET" jdbcType="VARCHAR" property="erzet" />
    <result column="ERNAM" jdbcType="VARCHAR" property="ernam" />
    <result column="AEDAT" jdbcType="VARCHAR" property="aedat" />
    <result column="AEZET" jdbcType="VARCHAR" property="aezet" />
    <result column="AENAM" jdbcType="VARCHAR" property="aenam" />
    <result column="ZSHCK" jdbcType="VARCHAR" property="zshck" />
    <result column="ZCGYGH" jdbcType="VARCHAR" property="zcgygh" />
    <result column="ZPURAGT" jdbcType="VARCHAR" property="zpuragt" />
    <result column="CHARG" jdbcType="VARCHAR" property="charg" />
    <result column="LGORT" jdbcType="VARCHAR" property="lgort" />
    <result column="RESLO" jdbcType="VARCHAR" property="reslo" />
    <result column="MSG" jdbcType="VARCHAR" property="msg" />
    <result column="EBELN" jdbcType="VARCHAR" property="ebeln" />
    <result column="EBELP" jdbcType="VARCHAR" property="ebelp" />
    <result column="VBELN" jdbcType="VARCHAR" property="vbeln" />
    <result column="POSNR" jdbcType="VARCHAR" property="posnr" />
    <result column="BRTWR" jdbcType="DECIMAL" property="brtwr" />
    <result column="KPEIN2" jdbcType="DECIMAL" property="kpein2" />
    <result column="ZCGZB" jdbcType="VARCHAR" property="zcgzb" />
    <result column="ZYPURREQNO" jdbcType="VARCHAR" property="zypurreqno" />
    <result column="ZYSTORELINENO" jdbcType="VARCHAR" property="zystorelineno" />
    <result column="ZSFCF" jdbcType="VARCHAR" property="zsfcf" />
    <result column="ZMAIN" jdbcType="VARCHAR" property="zmain" />
    <result column="ZZXL_30_JS" jdbcType="DECIMAL" property="zzxl30Js" />
    <result column="ZMDQTY_JS" jdbcType="DECIMAL" property="zmdqtyJs" />
    <result column="ZDCKYKC" jdbcType="DECIMAL" property="zdckykc" />
    <result column="MENGE_SUG" jdbcType="DECIMAL" property="mengeSug" />
    <result column="ZPLSUG" jdbcType="VARCHAR" property="zplsug" />
    <result column="ZCYJSWQ" jdbcType="DECIMAL" property="zcyjswq" />
    <result column="LABST_DC_JS" jdbcType="DECIMAL" property="labstDcJs" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, MANDT, PURREQNO, STORELINENO, BUKRS, EKORG, WERKS, ZZZZSHD, BSART, MATNR, MENGE, 
    ZSPSL, MEINS, ZZCXBJS, ZJHBHSL, ZYYBZ, BADAT, ZSPZT, ZCLZT, LOEKZ, ERDAT, ERZET, 
    ERNAM, AEDAT, AEZET, AENAM, ZSHCK, ZCGYGH, ZPURAGT, CHARG, LGORT, RESLO, MSG, EBELN, 
    EBELP, VBELN, POSNR, BRTWR, KPEIN2, ZCGZB, ZYPURREQNO, ZYSTORELINENO, ZSFCF, ZMAIN, 
    ZZXL_30_JS, ZMDQTY_JS, ZDCKYKC, MENGE_SUG, ZPLSUG, ZCYJSWQ, LABST_DC_JS
  </sql>

  <sql id = "Query_Where">
    <where>
      <if test="goodsNos != null and goodsNos.size >0">
        and a.MATNR in
        <foreach collection="goodsNos" item="goodsNo" index="index" open="(" close=")" separator=",">
          #{goodsNo}
        </foreach>
      </if>
      <if test="warehouseCodes != null and warehouseCodes.size >0">
        and a.WERKS in
        <foreach collection="warehouseCodes" item="warehouseCode" index="index" open="(" close=")" separator=",">
          #{warehouseCode}
        </foreach>
      </if>
      <if test="purchaseOrgs != null and purchaseOrgs.size >0">
        and a.EKORG in
        <foreach collection="purchaseOrgs" item="purchaseOrg" index="index" open="(" close=")" separator=",">
          #{purchaseOrg}
        </foreach>
      </if>
      and (a.LOEKZ = '' or a.LOEKZ is null)
      and a.ERDAT between #{startDate} and #{endDate}
      <if test="warnTime != null and warnTime != ''">
        and concat(a.AEDAT, a.AEZET) > #{warnTime}
      </if>
    </where>
  </sql>

  <select id="selectNonSendWithNonApporveList" resultType="com.cowell.iscm.service.dto.controlTower.NonSendDTO">
    select a.PURREQNO as purreqno, a.STORELINENO as storelineno, a.BUKRS as bukrs, a.EKORG as ekorg, a.WERKS as werks, a.ZZZZSHD as zzzzshd, a.BADAT as badat, a.MATNR as matnr, a.MENGE as menge,  a.ZSPSL as zspsl,a.MEINS as meins,
     case a.ZSPZT when '1' then '未审批' when '2' then '未审批（发送OA失败）' when '4' then '审批拒绝' when '5' then '未审批（OA接收失败）' end as approveStatus,
     case a.ZSPZT when '1' then '' when '2' then '系统接口异常，可联系SAP运维顾问' when '4' then '领导审批拒绝，可联系创建人登陆OA查看看拒绝原因' when '5' then '系统接口异常，可联系SAP运维顾问' end as approveDesc,
     a.ERNAM as ernam, a.ERDAT as erdat, a.ERZET as erzet
     from `SAP_ZMMT0287` a
    <include refid="Query_Where" />
    and a.ZSPZT in ('1','2','4','5')
  </select>
  <select id="selectNonSendWithNonTranList" resultType="com.cowell.iscm.service.dto.controlTower.NonSendDTO">
    select a.PURREQNO as purreqno, a.STORELINENO as storelineno, a.BUKRS as bukrs, a.EKORG as ekorg, a.WERKS as werks, a.ZZZZSHD as zzzzshd, a.BADAT as badat, a.MATNR as matnr, a.MENGE as menge, a.ZSPSL as zspsl, a.MEINS as meins,
    case when c.ZCLOSE = 'X' then '供货方主动关单' when c.ZCLOSE = '' and c.EBELN = '' then '供货方未转单' when c.ZCLOSE = '' and c.EBELN != '' and a.EBELN = '' then '供货方库存不足系统自动关单' end AS approveStatus,
    '可联系供货方沟沟通具体情况。' as approveDesc,
    a.ERNAM as ernam, a.ERDAT as erdat, a.ERZET as erzet
    from `SAP_ZMMT0287` a, `SAP_ZMMT0085` c
    <include refid="Query_Where" />
    and a.PURREQNO = c.PURREQNO
    and a.ZSPZT = '3'
    and (c.ZCLOSE = 'X'
      or (c.ZCLOSE = '' and c.EBELN = '')
      or (c.ZCLOSE = '' and c.EBELN != '' and a.EBELN = '')
    )
  </select>
  <select id="selectNonSendWithNonSendList" resultType="com.cowell.iscm.service.dto.controlTower.NonSendDTO">
    select a.PURREQNO as purreqno, a.STORELINENO as storelineno, a.BUKRS as bukrs, a.EKORG as ekorg, a.WERKS as werks, a.ZZZZSHD as zzzzshd, a.BADAT as badat, a.MATNR as matnr, a.MENGE as menge, a.ZSPSL as zspsl, a.MEINS as meins,
    '已转单仓库拣配中未发货' as approveStatus,
    '' as approveDesc,
    a.ERNAM as ernam, a.ERDAT as erdat, a.ERZET as erzet, a.EBELN as ebeln, a.EBELP as ebelp
    from `SAP_ZMMT0287` a, `SAP_ZMMT0085` c
    <include refid="Query_Where" />
    and a.PURREQNO = c.PURREQNO
    and a.PURREQNO = #{orderNo}
    and a.ZSPZT = '3'
    and (c.ZCLOSE = '' and c.EBELN != '' and a.EBELN != '')
  </select>
  <select id="selectNonSendOrderNoList" resultType="java.lang.String">
    select a.PURREQNO as purreqno
    from `SAP_ZMMT0287` a, `SAP_ZMMT0085` c
    <include refid="Query_Where" />
    and a.PURREQNO = c.PURREQNO
    and a.ZSPZT = '3'
    and (c.ZCLOSE = '' and c.EBELN != '' and a.EBELN != '')
    group by a.PURREQNO
  </select>

  <select id="selectRealDiffList" resultType="com.cowell.iscm.service.dto.controlTower.RealDiffDTO">
    select a.PURREQNO as purreqno, a.STORELINENO as storelineno, a.BUKRS as bukrs, a.EKORG as ekorg, a.WERKS as werks, a.ZZZZSHD as zzzzshd, a.BADAT as badat, a.MATNR as matnr, a.ZSPSL as zspsl,
    a.EBELN as ebeln, a.EBELP as ebelp, a.MEINS as meins, a.MENGE as menge,
    a.ERNAM as ernam, a.ERDAT as erdat, a.ERZET as erzet
    from `SAP_ZMMT0287` a, `SAP_ZMMT0085` c
    <include refid="Query_Where" />
    and a.PURREQNO = c.PURREQNO
    and a.PURREQNO = #{orderNo}
    and a.ZSPZT = '3'
    and c.ZCLOSE = ''
    and c.EBELN != ''
    and a.EBELN != ''

  </select>
  <select id="getRealDiffOrderNoList" resultType="java.lang.String">
    select a.PURREQNO
    from `SAP_ZMMT0287` a, `SAP_ZMMT0085` c
    <include refid="Query_Where" />
    and a.PURREQNO = c.PURREQNO
    and a.ZSPZT = '3'
    and c.ZCLOSE = ''
    and c.EBELN != ''
    and a.EBELN != ''
    group by a.PURREQNO
  </select>
</mapper>
