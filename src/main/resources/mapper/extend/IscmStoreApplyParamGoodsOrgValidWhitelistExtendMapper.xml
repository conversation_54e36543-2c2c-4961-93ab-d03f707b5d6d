<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.extend.IscmStoreApplyParamGoodsOrgValidWhitelistExtendMapper">

    <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.IscmStoreApplyParamGoodsOrgValidWhitelist">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
        <result column="org_id" jdbcType="BIGINT" property="orgId" />
        <result column="bar_code" jdbcType="VARCHAR" property="barCode" />
        <result column="cur_name" jdbcType="VARCHAR" property="curName" />
        <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
        <result column="goods_unit" jdbcType="VARCHAR" property="goodsUnit" />
        <result column="specifications" jdbcType="VARCHAR" property="specifications" />
        <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
        <result column="start_date" jdbcType="TIMESTAMP" property="startDate" />
        <result column="upper_quantity" jdbcType="INTEGER" property="upperQuantity" />
        <result column="end_date" jdbcType="TIMESTAMP" property="endDate" />
        <result column="created_by" jdbcType="BIGINT" property="createdBy" />
        <result column="created_name" jdbcType="VARCHAR" property="createdName" />
        <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
        <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
        <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
        <result column="store_type" jdbcType="VARCHAR" property="storeType" />
    </resultMap>

    <insert id="batchInsert" parameterType="list" >
        insert into iscm_store_apply_param_goods_org_valid_whitelist (
        param_code, param_name, param_level,
        org_id, business_id, platform_org_id,
        platform_name, company_org_id, company_name,
        company_code, sap_code, org_name,
        parent_org_id, parent_org_name, store_org_id,
        store_code, store_name, store_id,
        goods_no, bar_code, cur_name,
        goods_name, goods_unit, specifications,
        manufacturer, start_date, end_date,
        upper_quantity,
        created_by, created_name, updated_by,
        updated_name, store_level, store_type
        )
        values
        <foreach collection="list" item="item" index="index" separator="," >
            (
            #{item.paramCode,jdbcType=VARCHAR}, #{item.paramName,jdbcType=VARCHAR}, #{item.paramLevel,jdbcType=INTEGER},
            #{item.orgId,jdbcType=BIGINT}, #{item.businessId,jdbcType=BIGINT}, #{item.platformOrgId,jdbcType=BIGINT},
            #{item.platformName,jdbcType=VARCHAR}, #{item.companyOrgId,jdbcType=BIGINT}, #{item.companyName,jdbcType=VARCHAR},
            #{item.companyCode,jdbcType=VARCHAR}, #{item.sapCode,jdbcType=VARCHAR}, #{item.orgName,jdbcType=VARCHAR},
            #{item.parentOrgId,jdbcType=BIGINT}, #{item.parentOrgName,jdbcType=VARCHAR}, #{item.storeOrgId,jdbcType=BIGINT},
            #{item.storeCode,jdbcType=VARCHAR}, #{item.storeName,jdbcType=VARCHAR}, #{item.storeId,jdbcType=BIGINT},
            #{item.goodsNo,jdbcType=VARCHAR}, #{item.barCode,jdbcType=VARCHAR}, #{item.curName,jdbcType=VARCHAR},
            #{item.goodsName,jdbcType=VARCHAR}, #{item.goodsUnit,jdbcType=VARCHAR}, #{item.specifications,jdbcType=VARCHAR},
            #{item.manufacturer,jdbcType=VARCHAR}, #{item.startDate,jdbcType=TIMESTAMP}, #{item.endDate,jdbcType=TIMESTAMP},
            #{item.upperQuantity,jdbcType=TIMESTAMP},
            #{item.createdBy,jdbcType=BIGINT}, #{item.createdName,jdbcType=VARCHAR}, #{item.updatedBy,jdbcType=BIGINT},
            #{item.updatedName,jdbcType=VARCHAR},#{item.storeLevel,jdbcType=INTEGER},
            <choose>
                <when test="item.storeType!=null">
                    #{item.storeType,jdbcType=VARCHAR}
                </when>
                <otherwise>
                    ''
                </otherwise>
            </choose>
            )
        </foreach>
    </insert>

    <delete id="deleteByStoreCodeAndGoodsNo">
        delete from iscm_store_apply_param_goods_org_valid_whitelist where store_code = #{storeCode} and goods_no = #{goodsNo}
    </delete>

    <select id="selectByExample"  resultType="java.lang.Long">
        select
        a.id
        from iscm_store_apply_param_goods_org_valid_whitelist a
        <if test="_parameter != null">
            <include refid="Example_Where_Clause" />
        </if>
    </select>

    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>

    <select id="countByExample" parameterType="com.cowell.iscm.entity.IscmStoreApplyParamGoodsOrgValidWhitelistExample" resultType="java.lang.Long">
        select count(1) from (
        select goods_no,org_id, count(1) from iscm_store_apply_param_goods_org_valid_whitelist
        <if test="_parameter != null">
            <include refid="Example_Where_Clause" />
        </if>
        group by  goods_no,org_id) a
    </select>

    <select id="selectByExtendExample" parameterType="com.cowell.iscm.entity.IscmStoreApplyParamGoodsOrgValidWhitelistExample" resultMap="BaseResultMap">
        select
        goods_no,org_id, id, bar_code, cur_name, goods_name,upper_quantity,
        goods_unit, specifications, manufacturer, start_date, end_date,
        created_by, created_name, updated_by, updated_name, gmt_create, gmt_update
        from iscm_store_apply_param_goods_org_valid_whitelist
        <if test="_parameter != null">
            <include refid="Example_Where_Clause" />
        </if>
        group by  goods_no, org_id
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        <if test="limit != null">
            <if test="offset != null">
                limit ${offset}, ${limit}
            </if>
            <if test="offset == null">
                limit ${limit}
            </if>
        </if>
    </select>
    <select id="selectGoodsNoByExample"
            resultType="com.cowell.iscm.service.dto.applyParam.WhiteGoodsNoListDTO">
        select
        goods_no as goodsNo, upper_quantity as upperQuantity
        from iscm_store_apply_param_goods_org_valid_whitelist
        <if test="_parameter != null">
            <include refid="Example_Where_Clause" />
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        <if test="limit != null">
            <if test="offset != null">
                limit ${offset}, ${limit}
            </if>
            <if test="offset == null">
                limit ${limit}
            </if>
        </if>
    </select>
</mapper>
