<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.extend.IscmGoodsRegisterOrderExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.IscmGoodsRegisterOrder">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="register_order_no" jdbcType="VARCHAR" property="registerOrderNo" />
    <result column="register_type" jdbcType="TINYINT" property="registerType" />
    <result column="register_source" jdbcType="TINYINT" property="registerSource" />
    <result column="org_id" jdbcType="BIGINT" property="orgId" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="platform_org_id" jdbcType="BIGINT" property="platformOrgId" />
    <result column="platform_org_name" jdbcType="VARCHAR" property="platformOrgName" />
    <result column="company_org_id" jdbcType="BIGINT" property="companyOrgId" />
    <result column="company_org_name" jdbcType="VARCHAR" property="companyOrgName" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="store_org_id" jdbcType="BIGINT" property="storeOrgId" />
    <result column="store_org_name" jdbcType="VARCHAR" property="storeOrgName" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="register_month" jdbcType="INTEGER" property="registerMonth" />
    <result column="register_goods_variety" jdbcType="INTEGER" property="registerGoodsVariety" />
    <result column="cost_amount" jdbcType="DECIMAL" property="costAmount" />
    <result column="memo" jdbcType="VARCHAR" property="memo" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Base_Column_List">
    id, register_order_no, register_type, register_source, org_id, org_name,
    platform_org_id, platform_org_name, company_org_id, company_org_name, company_code, 
    store_org_id, store_org_name, store_code, register_month, register_goods_variety, cost_amount, memo, status, gmt_create, gmt_update, extend,
    version, created_by, created_name, updated_by, updated_name
  </sql>
  <insert id="batchInsert" parameterType="com.cowell.iscm.entity.IscmGoodsRegisterOrder">
    insert into iscm_goods_register_order (register_order_no,
      register_type, register_source, org_id, 
      org_name, platform_org_id, platform_org_name, 
      company_org_id, company_org_name, company_code, 
      store_org_id, store_org_name, store_code,
      register_month, register_goods_variety, cost_amount, memo, status,
      created_by, created_name, updated_by, 
      updated_name)
    values
    <foreach collection="list" item="item" index="index" separator="," >
    (#{item.registerOrderNo,jdbcType=VARCHAR},
      #{item.registerType,jdbcType=TINYINT}, #{item.registerSource,jdbcType=TINYINT}, #{item.orgId,jdbcType=BIGINT},
      #{item.orgName,jdbcType=VARCHAR}, #{item.platformOrgId,jdbcType=BIGINT}, #{item.platformOrgName,jdbcType=VARCHAR},
      #{item.companyOrgId,jdbcType=BIGINT}, #{item.companyOrgName,jdbcType=VARCHAR}, #{item.companyCode,jdbcType=VARCHAR},
      #{item.storeOrgId,jdbcType=BIGINT}, #{item.storeOrgName,jdbcType=VARCHAR}, #{item.storeCode,jdbcType=VARCHAR},
      #{item.registerMonth,jdbcType=INTEGER}, #{item.registerGoodsVariety,jdbcType=INTEGER}, #{item.costAmount,jdbcType=DECIMAL}, #{item.memo,jdbcType=VARCHAR},
      #{item.createdBy,jdbcType=BIGINT}, #{item.createdName,jdbcType=VARCHAR}, #{item.updatedBy,jdbcType=BIGINT},
      #{item.updatedName,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <select id="selectRegisterByOutStoreOrgIdsAndAllotTypes"
          resultType="com.cowell.iscm.entity.IscmGoodsRegisterOrder">
    select register_order_no as registerOrderNo, company_org_id as companyOrgId, store_org_id as storeOrgId, cost_amount as costAmount
    from iscm_goods_register_order
    where 1=1
    <if test="companyOrgIds != null and companyOrgIds.size > 0">
        and company_org_id in
      <foreach collection="companyOrgIds" item="companyOrgId" index="index" open="(" close=")" separator="," >
          #{companyOrgId}
      </foreach>
    </if>
    <if test="storeOrgIds != null and storeOrgIds.size > 0">
        and store_org_id in
      <foreach collection="storeOrgIds" item="storeOrgId" index="index" open="(" close=")" separator="," >
          #{storeOrgId}
      </foreach>
    </if>
    <if test="storeAttrs != null and storeAttrs.size > 0">
        and store_attr in
      <foreach collection="storeAttrs" item="storeAttr" index="index" open="(" close=")" separator="," >
          #{storeAttr}
      </foreach>
    </if>
    and register_month = #{queryMonth}
    and register_type in
    <foreach collection="allotTypes" item="allotType" index="index" open="(" close=")" separator="," >
      #{allotType}
    </foreach>
    order by id desc
    limit ${start}, ${pageSize}
  </select>

  <update id="updateByIdAndRegisterMonth" parameterType="com.cowell.iscm.entity.IscmGoodsRegisterOrder">
    update iscm_goods_register_order
    <set>
      <if test="registerOrderNo != null">
        register_order_no = #{registerOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="registerType != null">
        register_type = #{registerType,jdbcType=TINYINT},
      </if>
      <if test="registerSource != null">
        register_source = #{registerSource,jdbcType=TINYINT},
      </if>
      <if test="orderStatus != null">
        order_status = #{orderStatus,jdbcType=TINYINT},
      </if>
      <if test="orgId != null">
        org_id = #{orgId,jdbcType=BIGINT},
      </if>
      <if test="orgName != null">
        org_name = #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="platformOrgId != null">
        platform_org_id = #{platformOrgId,jdbcType=BIGINT},
      </if>
      <if test="platformOrgName != null">
        platform_org_name = #{platformOrgName,jdbcType=VARCHAR},
      </if>
      <if test="companyOrgId != null">
        company_org_id = #{companyOrgId,jdbcType=BIGINT},
      </if>
      <if test="companyOrgName != null">
        company_org_name = #{companyOrgName,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="storeOrgId != null">
        store_org_id = #{storeOrgId,jdbcType=BIGINT},
      </if>
      <if test="storeOrgName != null">
        store_org_name = #{storeOrgName,jdbcType=VARCHAR},
      </if>
      <if test="storeCode != null">
        store_code = #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="registerMonth != null">
        register_month = #{registerMonth,jdbcType=INTEGER},
      </if>
      <if test="registerGoodsVariety != null">
        register_goods_variety = #{registerGoodsVariety,jdbcType=INTEGER},
      </if>
      <if test="costAmount != null">
        cost_amount = #{costAmount,jdbcType=DECIMAL},
      </if>
      <if test="memo != null">
        memo = #{memo,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        updated_name = #{updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT} and register_month = #{registerMonth,jdbcType=INTEGER}
  </update>

</mapper>
