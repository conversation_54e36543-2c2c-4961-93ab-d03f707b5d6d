<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.extend.IscmStoreApplyParamGoodsValidWhitelistOrgExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.IscmStoreApplyParamGoodsValidWhitelistOrg">
    <id column="ID" jdbcType="BIGINT" property="id" />
    <result column="whitelist_ID" jdbcType="BIGINT" property="whitelistId" />
    <result column="rule_org_level" jdbcType="BIGINT" property="ruleOrgLevel" />
    <result column="rule_org_id" jdbcType="BIGINT" property="ruleOrgId" />
    <result column="rule_org_name" jdbcType="VARCHAR" property="ruleOrgName" />
    <result column="rule_org_code" jdbcType="VARCHAR" property="ruleOrgCode" />
    <result column="rule_org_store_id" jdbcType="BIGINT" property="ruleOrgStoreId" />
  </resultMap>

  <insert id="batchInsert" parameterType="list">
    insert into iscm_store_apply_param_goods_valid_whitelist_org (
        whitelist_ID, rule_org_level, rule_org_id,
        rule_org_name, rule_org_code, rule_org_store_id
    )
    values
    <foreach collection="list" item="item" index="index" separator="," >
    (
      #{item.whitelistId,jdbcType=BIGINT}, #{item.ruleOrgLevel,jdbcType=BIGINT}, #{item.ruleOrgId,jdbcType=BIGINT},
      #{item.ruleOrgName,jdbcType=VARCHAR}, #{item.ruleOrgCode,jdbcType=VARCHAR}, #{item.ruleOrgStoreId,jdbcType=BIGINT}
    )
    </foreach>
  </insert>

  <delete id="delByWhitelist">
    DELETE
    from
      iscm_store_apply_param_goods_valid_whitelist_org
    where exists(
      select
        1
      from
        iscm_store_apply_param_goods_valid_whitelist t2
      where
        t2.org_id = #{orgId,jdbcType=BIGINT}
        and t2.store_type = #{storeType,jdbcType=VARCHAR}
        and t2.id = iscm_store_apply_param_goods_valid_whitelist_org.whitelist_ID
        and t2.goods_no in
        <foreach open="(" close=")" collection="goodsNos" item="listItem" separator=",">
          #{listItem}
        </foreach>
    )
  </delete>
</mapper>