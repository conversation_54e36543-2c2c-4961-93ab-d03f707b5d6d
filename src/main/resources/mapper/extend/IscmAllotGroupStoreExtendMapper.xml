<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.extend.IscmAllotGroupStoreExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.IscmAllotGroupStore">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="group_code" jdbcType="VARCHAR" property="groupCode" />
    <result column="group_name" jdbcType="VARCHAR" property="groupName" />
    <result column="platform_id" jdbcType="BIGINT" property="platformId" />
    <result column="platform_name" jdbcType="VARCHAR" property="platformName" />
    <result column="company_org_id" jdbcType="BIGINT" property="companyOrgId" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="business_id" jdbcType="BIGINT" property="businessId" />
    <result column="store_org_id" jdbcType="BIGINT" property="storeOrgId" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="store_name" jdbcType="VARCHAR" property="storeName" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, group_code, group_name, platform_id, platform_name, company_org_id, company_code,
    company_name, business_id, store_org_id, store_code, store_name, store_id, `status`,
    gmt_create, gmt_update, extend, version, created_by, created_name, updated_by, updated_name
  </sql>
  <select id="selectByGroupCodes" resultType="com.cowell.iscm.entity.IscmAllotGroupStore">
    select group_code as groupCode, store_id as storeId from iscm_allot_group_store
    where 1=1
    <if test="groupCodes != null and groupCodes.size > 0">
    and group_code in
    <foreach collection="groupCodes" item="groupCode" separator="," open="(" close=")">
      #{groupCode}
    </foreach>
    </if>
  </select>
  <select id="selectByGroupCodeAndStoreIds" resultType="com.cowell.iscm.entity.IscmAllotGroupStore">
    select group_code as groupCode, store_id as storeId from iscm_allot_group_store
    where group_code = #{groupCode}
    and store_id in
    <foreach collection="storeIds" item="storeId" separator="," open="(" close=")">
      #{storeId}
    </foreach>
</select>
<!--  <select id="selectExistsByStoreIds" resultType="com.cowell.iscm.entity.IscmAllotGroupStore">-->
<!--    select group_code as groupCode, store_id as storeId from iscm_allot_group_store-->
<!--    where store_id in-->
<!--    <foreach collection="storeIds" item="storeId" separator="," open="(" close=")">-->
<!--      #{storeId}-->
<!--    </foreach>-->
<!--    group by group_code,store_id-->
<!--  </select>-->
  <select id="selectExistsByStoreIds" resultType="com.cowell.iscm.entity.IscmAllotGroupStore">
    select a.group_code as groupCode, a.store_id as storeId from iscm_allot_group_store a, iscm_allot_group_info b, iscm_allot_group_use_scene c
    where a.group_code = b.group_code
      and b.group_code = c.group_code
    and c.group_use_scene = #{useScene}
        and a.store_id in
    <foreach collection="storeIds" item="storeId" separator="," open="(" close=")">
      #{storeId}
    </foreach>
    group by a.group_code,a.store_id
  </select>
  <select id="selectExistsByUseSences"
          resultType="com.cowell.iscm.service.dto.allotGroup.AllotGroupStoreSence">
    select c.group_use_scene as useScene, a.store_name as storeName from iscm_allot_group_store a, iscm_allot_group_info b, iscm_allot_group_use_scene c
    where a.group_code = b.group_code
    and b.group_code = c.group_code
    and c.group_use_scene in
    <foreach collection="useSences" item="useSence" separator="," open="(" close=")">
      #{useSence}
    </foreach>
    and a.store_id in
    <foreach collection="storeIds" item="storeId" separator="," open="(" close=")">
      #{storeId}
    </foreach>
    <if test="groupCode != null">
      and a.group_code != #{groupCode}
    </if>
    group by c.group_use_scene,a.store_id
  </select>

</mapper>
