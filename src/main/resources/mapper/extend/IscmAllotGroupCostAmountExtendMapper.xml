<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.extend.IscmAllotGroupCostAmountExtendMapper">

  <select id="selectInAllotGroupList" resultType="java.lang.String">
    select in_allot_group from iscm_allot_group_cost_amount
    where company_id = #{companyId,jdbcType=BIGINT} GROUP BY in_allot_group ORDER BY in_allot_group asc
  </select>

  <select id="selectOutAllotGroupList" resultType="java.lang.String">
      select out_allot_group from iscm_allot_group_cost_amount
      where company_id = #{companyId,jdbcType=BIGINT}  GROUP BY out_allot_group ORDER BY out_allot_group asc
  </select>

  <select id="selectCostAmount" resultType="com.cowell.iscm.entity.IscmAllotGroupCostAmount">
    select same_group_cost_amount sameGroupCostAmount,diff_group_cost_amount diffGroupCostAmount from iscm_allot_group_cost_amount
    where company_id = #{companyId,jdbcType=BIGINT}  LIMIT 1
  </select>


  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="java.util.List" useGeneratedKeys="true">
    insert into iscm_allot_group_cost_amount (company_id, company_name, company_code,
    bdp_code,in_allot_group, out_allot_group, same_group_cost_amount,
      diff_group_cost_amount, cost_amount, `status`, 
      created_by, created_name,  updated_by, updated_name)
    values
    <foreach collection="list" item="item" index="index" separator="," >
      (#{item.companyId,jdbcType=BIGINT}, #{item.companyName,jdbcType=VARCHAR}, #{item.companyCode,jdbcType=VARCHAR},
      #{item.bdpCode,jdbcType=VARCHAR},#{item.inAllotGroup,jdbcType=VARCHAR}, #{item.outAllotGroup,jdbcType=VARCHAR}, #{item.sameGroupCostAmount,jdbcType=DECIMAL},
      #{item.diffGroupCostAmount,jdbcType=DECIMAL}, #{item.costAmount,jdbcType=DECIMAL}, #{item.status,jdbcType=TINYINT}, 
      #{item.createdBy,jdbcType=BIGINT}, #{item.createdName,jdbcType=VARCHAR},
      #{item.updatedBy,jdbcType=BIGINT}, #{item.updatedName,jdbcType=VARCHAR})
    </foreach>
  </insert>
</mapper>