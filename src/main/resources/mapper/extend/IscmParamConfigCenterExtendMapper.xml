<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.extend.IscmParamConfigCenterExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.IscmParamConfigCenter">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="param_code" jdbcType="VARCHAR" property="paramCode" />
    <result column="old_param_code" jdbcType="VARCHAR" property="oldParamCode" />
    <result column="param_name" jdbcType="VARCHAR" property="paramName" />
    <result column="param_desc" jdbcType="VARCHAR" property="paramDesc" />
    <result column="param_scope" jdbcType="TINYINT" property="paramScope" />
    <result column="param_level" jdbcType="INTEGER" property="paramLevel" />
    <result column="param_type" jdbcType="TINYINT" property="paramType" />
    <result column="param_source" jdbcType="TINYINT" property="paramSource" />
    <result column="param_sequence" jdbcType="INTEGER" property="paramSequence" />
    <result column="param_order" jdbcType="INTEGER" property="paramOrder" />
    <result column="param_sequence_desc" jdbcType="VARCHAR" property="paramSequenceDesc" />
    <result column="param_status" jdbcType="TINYINT" property="paramStatus" />
    <result column="param_default_value" jdbcType="VARCHAR" property="paramDefaultValue" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
  </resultMap>
  <sql id="Base_Column_List">
    id, param_code, old_param_code, param_name, param_desc, param_scope, param_level, 
    param_type, param_source, param_sequence, param_order, param_sequence_desc, param_status,
    param_default_value, status, extend, version, created_by, created_name, updated_by,
    updated_name, gmt_create, gmt_update
  </sql>
  <insert id="batchInsert" parameterType="java.util.List">
    insert into iscm_param_config_center (param_code, old_param_code,
      param_name, param_desc, param_scope, 
      param_level, param_type, param_source, param_sequence, param_order, param_sequence_desc, param_default_value,
      param_status, status, created_by, created_name,
      updated_by, updated_name)
    values
    <foreach collection="list" item="item" index="index" separator="," >
        (#{item.paramCode,jdbcType=VARCHAR}, #{item.oldParamCode,jdbcType=VARCHAR},
      #{item.paramName,jdbcType=VARCHAR}, #{item.paramDesc,jdbcType=VARCHAR}, #{item.paramScope,jdbcType=TINYINT},
      #{item.paramLevel,jdbcType=TINYINT}, #{item.paramType,jdbcType=TINYINT}, #{item.paramSource,jdbcType=TINYINT}, #{item.paramSequence,jdbcType=INTEGER}, #{item.paramOrder,jdbcType=INTEGER}, #{item.paramSequenceDesc,jdbcType=VARCHAR}, #{item.paramDefaultValue,jdbcType=VARCHAR},
      #{item.paramStatus,jdbcType=TINYINT}, #{item.status,jdbcType=TINYINT}, #{item.createdBy,jdbcType=BIGINT}, #{item.createdName,jdbcType=VARCHAR},
      #{item.updatedBy,jdbcType=BIGINT}, #{item.updatedName,jdbcType=VARCHAR})
    </foreach>
  </insert>
</mapper>
