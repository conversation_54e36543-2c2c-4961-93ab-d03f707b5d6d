<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.extend.IscmSuggestAllotGoodsDetailExtendMapper">

    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>

    <sql id="Base_Column_List">
        id, business_date, record_id, allot_type, register_no, register_source, register_by,
    register_name, register_time, platform_org_id, platform_org_name, out_company_id,
    out_company_code, out_company_name, in_company_id, in_company_code, in_company_name,
    out_store_id, out_store_code, out_store_name, out_store_attr, out_store_sales_level,
    in_store_id, in_store_code, in_store_name, in_store_attr, in_store_sales_level, allot_group_code,
    allot_group_name, allot_no, pos_allot_no, goods_no, goods_desc, batch_no, expiry_time,
    goods_common_name, manufacturer, dosage_form, unit, season, return_condition, suggest_allot_quantity,
    real_allot_quantity, transfer_cost_amount, in_store_thirty_days_sales, out_store_thirty_days_sales,
    in_store_thirty_days_sale_times, out_store_thirty_days_sale_times, in_store_stock,
    out_store_stock, in_store_stock_min, out_store_stock_min, in_store_stock_max, out_store_stock_max,
    in_store_stock_onway, out_store_stock_onway, in_store_pos_daily_sales, out_store_pos_daily_sales,
    in_store_estimate_sales_days, out_store_estimate_sales_days, approve_status, approve_result,
    approve_by, approve_name, approve_time, goodsline, pushlevel, goods_marks, short_manufacturer,
    specifications, model_code, `status`, gmt_create, gmt_update, extend, version, created_by,
    created_name, updated_by, updated_name
  </sql>

    <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.IscmSuggestAllotGoodsDetail">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="business_date" jdbcType="TIMESTAMP" property="businessDate" />
        <result column="record_id" jdbcType="BIGINT" property="recordId" />
        <result column="allot_type" jdbcType="TINYINT" property="allotType" />
        <result column="register_no" jdbcType="VARCHAR" property="registerNo" />
        <result column="register_source" jdbcType="TINYINT" property="registerSource" />
        <result column="register_by" jdbcType="BIGINT" property="registerBy" />
        <result column="register_name" jdbcType="VARCHAR" property="registerName" />
        <result column="register_time" jdbcType="TIMESTAMP" property="registerTime" />
        <result column="platform_org_id" jdbcType="BIGINT" property="platformOrgId" />
        <result column="platform_org_name" jdbcType="VARCHAR" property="platformOrgName" />
        <result column="out_company_id" jdbcType="BIGINT" property="outCompanyId" />
        <result column="out_company_code" jdbcType="VARCHAR" property="outCompanyCode" />
        <result column="out_company_name" jdbcType="VARCHAR" property="outCompanyName" />
        <result column="in_company_id" jdbcType="BIGINT" property="inCompanyId" />
        <result column="in_company_code" jdbcType="VARCHAR" property="inCompanyCode" />
        <result column="in_company_name" jdbcType="VARCHAR" property="inCompanyName" />
        <result column="out_store_id" jdbcType="BIGINT" property="outStoreId" />
        <result column="out_store_code" jdbcType="VARCHAR" property="outStoreCode" />
        <result column="out_store_name" jdbcType="VARCHAR" property="outStoreName" />
        <result column="out_store_attr" jdbcType="VARCHAR" property="outStoreAttr" />
        <result column="out_store_sales_level" jdbcType="VARCHAR" property="outStoreSalesLevel" />
        <result column="in_store_id" jdbcType="BIGINT" property="inStoreId" />
        <result column="in_store_code" jdbcType="VARCHAR" property="inStoreCode" />
        <result column="in_store_name" jdbcType="VARCHAR" property="inStoreName" />
        <result column="in_store_attr" jdbcType="VARCHAR" property="inStoreAttr" />
        <result column="in_store_sales_level" jdbcType="VARCHAR" property="inStoreSalesLevel" />
        <result column="allot_group_code" jdbcType="VARCHAR" property="allotGroupCode" />
        <result column="allot_group_name" jdbcType="VARCHAR" property="allotGroupName" />
        <result column="allot_no" jdbcType="VARCHAR" property="allotNo" />
        <result column="pos_allot_no" jdbcType="VARCHAR" property="posAllotNo" />
        <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
        <result column="goods_desc" jdbcType="VARCHAR" property="goodsDesc" />
        <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
        <result column="expiry_time" jdbcType="TIMESTAMP" property="expiryTime" />
        <result column="goods_common_name" jdbcType="VARCHAR" property="goodsCommonName" />
        <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
        <result column="dosage_form" jdbcType="VARCHAR" property="dosageForm" />
        <result column="unit" jdbcType="VARCHAR" property="unit" />
        <result column="season" jdbcType="TINYINT" property="season" />
        <result column="return_condition" jdbcType="VARCHAR" property="returnCondition" />
        <result column="suggest_allot_quantity" jdbcType="DECIMAL" property="suggestAllotQuantity" />
        <result column="real_allot_quantity" jdbcType="DECIMAL" property="realAllotQuantity" />
        <result column="transfer_cost_amount" jdbcType="DECIMAL" property="transferCostAmount" />
        <result column="in_store_thirty_days_sales" jdbcType="DECIMAL" property="inStoreThirtyDaysSales" />
        <result column="out_store_thirty_days_sales" jdbcType="DECIMAL" property="outStoreThirtyDaysSales" />
        <result column="in_store_thirty_days_sale_times" jdbcType="DECIMAL" property="inStoreThirtyDaysSaleTimes" />
        <result column="out_store_thirty_days_sale_times" jdbcType="DECIMAL" property="outStoreThirtyDaysSaleTimes" />
        <result column="in_store_stock" jdbcType="DECIMAL" property="inStoreStock" />
        <result column="out_store_stock" jdbcType="DECIMAL" property="outStoreStock" />
        <result column="in_store_stock_min" jdbcType="DECIMAL" property="inStoreStockMin" />
        <result column="out_store_stock_min" jdbcType="DECIMAL" property="outStoreStockMin" />
        <result column="in_store_stock_max" jdbcType="DECIMAL" property="inStoreStockMax" />
        <result column="out_store_stock_max" jdbcType="DECIMAL" property="outStoreStockMax" />
        <result column="in_store_stock_onway" jdbcType="DECIMAL" property="inStoreStockOnway" />
        <result column="out_store_stock_onway" jdbcType="DECIMAL" property="outStoreStockOnway" />
        <result column="in_store_pos_daily_sales" jdbcType="DECIMAL" property="inStorePosDailySales" />
        <result column="out_store_pos_daily_sales" jdbcType="DECIMAL" property="outStorePosDailySales" />
        <result column="in_store_estimate_sales_days" jdbcType="DECIMAL" property="inStoreEstimateSalesDays" />
        <result column="out_store_estimate_sales_days" jdbcType="DECIMAL" property="outStoreEstimateSalesDays" />
        <result column="approve_status" jdbcType="TINYINT" property="approveStatus" />
        <result column="approve_result" jdbcType="VARCHAR" property="approveResult" />
        <result column="approve_by" jdbcType="BIGINT" property="approveBy" />
        <result column="approve_name" jdbcType="VARCHAR" property="approveName" />
        <result column="approve_time" jdbcType="TIMESTAMP" property="approveTime" />
        <result column="goodsline" jdbcType="VARCHAR" property="goodsline" />
        <result column="pushlevel" jdbcType="VARCHAR" property="pushlevel" />
        <result column="goods_marks" jdbcType="VARCHAR" property="goodsMarks" />
        <result column="short_manufacturer" jdbcType="VARCHAR" property="shortManufacturer" />
        <result column="specifications" jdbcType="VARCHAR" property="specifications" />
        <result column="model_code" jdbcType="VARCHAR" property="modelCode" />
        <result column="status" jdbcType="TINYINT" property="status" />
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
        <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
        <result column="extend" jdbcType="VARCHAR" property="extend" />
        <result column="version" jdbcType="INTEGER" property="version" />
        <result column="created_by" jdbcType="BIGINT" property="createdBy" />
        <result column="created_name" jdbcType="VARCHAR" property="createdName" />
        <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
        <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
    </resultMap>



    <insert id="batchInsert" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        insert into iscm_suggest_allot_goods_detail ( business_date, record_id,
        allot_type, register_no, register_source,
        register_by, register_name, register_time,
        platform_org_id, platform_org_name,
        out_company_id, out_company_code, out_company_name,
        in_company_id, in_company_code, in_company_name,
        out_store_id, out_store_code, out_store_name, out_store_attr,
        in_store_id, in_store_code, in_store_name, in_store_attr,
        allot_group_code, allot_group_name,
        allot_no, pos_allot_no,
        goods_no, goods_desc, batch_no,
        expiry_time, goods_common_name, manufacturer,
        dosage_form, unit, season,
        return_condition, suggest_allot_quantity,
        real_allot_quantity, transfer_cost_amount,
        in_store_thirty_days_sales, out_store_thirty_days_sales,
        in_store_thirty_days_sale_times, out_store_thirty_days_sale_times,
        in_store_stock, out_store_stock, in_store_stock_min,
        out_store_stock_min, in_store_stock_max, out_store_stock_max,
        in_store_stock_onway, out_store_stock_onway,
        in_store_pos_daily_sales, out_store_pos_daily_sales,
        in_store_estimate_sales_days, out_store_estimate_sales_days,
        approve_status, approve_result, approve_by,
        approve_name, approve_time,
        goodsline, pushlevel, goods_marks, short_manufacturer, specifications, model_code, extend,
        out_store_sales_level, in_store_sales_level
        )
        values
        <foreach collection="list" item="item" index="index" separator="," >
            (
            #{item.businessDate,jdbcType=TIMESTAMP}, #{item.recordId,jdbcType=BIGINT},
            #{item.allotType,jdbcType=TINYINT}, #{item.registerNo,jdbcType=VARCHAR}, #{item.registerSource,jdbcType=TINYINT},
            #{item.registerBy,jdbcType=BIGINT}, #{item.registerName,jdbcType=VARCHAR}, #{item.registerTime,jdbcType=TIMESTAMP},
            #{item.platformOrgId,jdbcType=BIGINT}, #{item.platformOrgName,jdbcType=VARCHAR},
            #{item.outCompanyId,jdbcType=BIGINT}, #{item.outCompanyCode,jdbcType=VARCHAR}, #{item.outCompanyName,jdbcType=VARCHAR},
            #{item.inCompanyId,jdbcType=BIGINT}, #{item.inCompanyCode,jdbcType=VARCHAR}, #{item.inCompanyName,jdbcType=VARCHAR},
            #{item.outStoreId,jdbcType=BIGINT}, #{item.outStoreCode,jdbcType=VARCHAR}, #{item.outStoreName,jdbcType=VARCHAR},#{item.outStoreAttr,jdbcType=VARCHAR},
            #{item.inStoreId,jdbcType=BIGINT}, #{item.inStoreCode,jdbcType=VARCHAR}, #{item.inStoreName,jdbcType=VARCHAR},#{item.inStoreAttr,jdbcType=VARCHAR},
            #{item.allotGroupCode,jdbcType=VARCHAR}, #{item.allotGroupName,jdbcType=VARCHAR},
            #{item.allotNo,jdbcType=VARCHAR}, #{item.posAllotNo,jdbcType=VARCHAR},
            #{item.goodsNo,jdbcType=VARCHAR}, #{item.goodsDesc,jdbcType=VARCHAR}, #{item.batchNo,jdbcType=VARCHAR},
            #{item.expiryTime,jdbcType=TIMESTAMP}, #{item.goodsCommonName,jdbcType=VARCHAR}, #{item.manufacturer,jdbcType=VARCHAR},
            #{item.dosageForm,jdbcType=VARCHAR}, #{item.unit,jdbcType=VARCHAR}, #{item.season,jdbcType=TINYINT},
            #{item.returnCondition,jdbcType=VARCHAR}, #{item.suggestAllotQuantity,jdbcType=INTEGER},
            #{item.realAllotQuantity,jdbcType=INTEGER}, #{item.transferCostAmount,jdbcType=DECIMAL},
            #{item.inStoreThirtyDaysSales,jdbcType=DECIMAL}, #{item.outStoreThirtyDaysSales,jdbcType=DECIMAL},
            #{item.inStoreThirtyDaysSaleTimes,jdbcType=DECIMAL}, #{item.outStoreThirtyDaysSaleTimes,jdbcType=DECIMAL},
            #{item.inStoreStock,jdbcType=DECIMAL}, #{item.outStoreStock,jdbcType=DECIMAL}, #{item.inStoreStockMin,jdbcType=DECIMAL},
            #{item.outStoreStockMin,jdbcType=DECIMAL}, #{item.inStoreStockMax,jdbcType=DECIMAL}, #{item.outStoreStockMax,jdbcType=DECIMAL},
            #{item.inStoreStockOnway,jdbcType=DECIMAL}, #{item.outStoreStockOnway,jdbcType=DECIMAL},
            #{item.inStorePosDailySales,jdbcType=DECIMAL}, #{item.outStorePosDailySales,jdbcType=DECIMAL},
            #{item.inStoreEstimateSalesDays,jdbcType=DECIMAL}, #{item.outStoreEstimateSalesDays,jdbcType=DECIMAL},
            #{item.approveStatus,jdbcType=TINYINT}, #{item.approveResult,jdbcType=VARCHAR}, #{item.approveBy,jdbcType=BIGINT},
            #{item.approveName,jdbcType=VARCHAR}, #{item.approveTime,jdbcType=TIMESTAMP},
            #{item.goodsline,jdbcType=VARCHAR},#{item.pushlevel,jdbcType=VARCHAR},#{item.goodsMarks,jdbcType=VARCHAR},
            #{item.shortManufacturer,jdbcType=VARCHAR},#{item.specifications,jdbcType=VARCHAR},#{item.modelCode,jdbcType=VARCHAR},#{item.extend,jdbcType=VARCHAR},
            #{item.outStoreSalesLevel,jdbcType=VARCHAR}, #{item.inStoreSalesLevel,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>
    <update id="updateApproveStatusByIds">
        update iscm_suggest_allot_goods_detail set approve_status = #{approveStatus} , approve_by = #{approveBy},approve_name = #{approveName}, approve_time = #{approveTime}
        where id in
        <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>

    <update id="updatePosAllotNoByIds">
        update iscm_suggest_allot_goods_detail set pos_allot_no = #{posAllotNo}
        where id in
        <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>
    <update id="updateApproveResult">
        update iscm_suggest_allot_goods_detail
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="approve_result=case" suffix="end,">
                <foreach collection="results" item="item" index="index">
                    WHEN id=#{item.id,jdbcType=BIGINT} then #{item.result}
                </foreach>
            </trim>
            <trim prefix="extend=case" suffix="end,">
                <foreach collection="results" item="item" index="index">
                    WHEN id=#{item.id,jdbcType=BIGINT} then #{item.extend}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="results" index="index" item="item" separator="," open="(" close=")">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="selectGoodsNoByStoreNoAndGoodsNosAndApproveStatus" resultType="java.lang.String">
        select goods_no from iscm_suggest_allot_goods_detail where out_store_code = #{storeCode}
        and goods_no in
        <foreach collection="goodsNos" item="goodsNo" index="index" open="(" close=")" separator=",">
            #{goodsNo}
        </foreach>
        and approve_status = #{approveStatus}
        and allot_type = 2
    </select>

    <select id="selectGoodsNoBatchNoByStoreNoAndGoodsNosAndApproveStatus"
            resultType="com.cowell.iscm.service.dto.registerOrder.GoodsBatchVO">
        select goods_no as goodsNo, batch_no as batchNo from iscm_suggest_allot_goods_detail where out_store_code = #{storeCode}
        and goods_no in
        <foreach collection="goodsNos" item="goodsNo" index="index" open="(" close=")" separator=",">
            #{goodsNo}
        </foreach>
        and approve_status = #{approveStatus}
        and allot_type = 1
    </select>
    <select id="selectAllotByOutStoreOrgIdsAndAllotTypes"
            resultType="com.cowell.iscm.entity.IscmSuggestAllotGoodsDetail">
        select out_store_id as outStoreId, in_store_id as inStoreId, transfer_cost_amount as transferCostAmount,
        approve_status as approveStatus, suggest_allot_quantity as suggestAllotQuantity, real_allot_quantity as realAllotQuantity
        from iscm_suggest_allot_goods_detail
        where 1=1
        <if test="companyOrgIds != null and companyOrgIds.size > 0">
            and out_company_id in
            <foreach collection="companyOrgIds" item="companyOrgId" index="index" open="(" close=")" separator="," >
                #{companyOrgId}
            </foreach>
        </if>
        <if test="storeOrgIds != null and storeOrgIds.size > 0">
            and out_store_id in
            <foreach collection="storeOrgIds" item="storeOrgId" index="index" open="(" close=")" separator="," >
                #{storeOrgId}
            </foreach>
            and in_store_id in
            <foreach collection="storeOrgIds" item="storeOrgId" index="index" open="(" close=")" separator="," >
                #{storeOrgId}
            </foreach>
      </if>
        <if test="storeAttrs != null and storeAttrs.size > 0">
            and out_store_attr in
            <foreach collection="storeAttrs" item="storeAttr" index="index" open="(" close=")" separator="," >
                #{storeAttr}
            </foreach>
            and in_store_attr in
            <foreach collection="storeAttrs" item="storeAttr" index="index" open="(" close=")" separator="," >
                #{storeAttr}
            </foreach>
      </if>
        and register_time between #{startDate} and #{endDate}
        and allot_type in
        <foreach collection="allotTypes" item="allotType" index="index" open="(" close=")" separator="," >
            #{allotType}
        </foreach>
        order by id desc
        limit ${start}, ${pageSize}
    </select>
    <select id="selectInStroeNoPosApporve" resultType="com.cowell.iscm.entity.IscmSuggestAllotGoodsDetail">
          select in_store_id as inStoreId, pos_allot_no as posAllotNo
            from iscm_suggest_allot_goods_detail
            where approve_status = 1
            and gmt_update between #{startDate} and #{endDate}
    </select>
    <select id="selectOutStroeNoPosApporve" resultType="com.cowell.iscm.entity.IscmSuggestAllotGoodsDetail">
        select out_store_id as outStoreId, pos_allot_no as posAllotNo
        from iscm_suggest_allot_goods_detail
        where approve_status = 1
        and gmt_update between #{startDate} and #{endDate}
    </select>
    <select id="selectNoApproveList" resultType="com.cowell.iscm.service.dto.zdt.SuggestAllotDTO">
        select in_store_id as inStoreId, out_store_id as outStoreId, allot_type as allotType,
        sum(transfer_cost_amount) as costAmountTotal, register_no as registerNo, record_id as recordId, count(*) as detailCount
        from iscm_suggest_allot_goods_detail
        where approve_status = 0
        and gmt_update between #{startDate} and #{endDate}
        group by record_id
    </select>
    <select id="selectGoodsNosByRecordIds" resultType="com.cowell.iscm.entity.IscmSuggestAllotGoodsDetail">
        select record_id as recordId, goods_no as goodsNo
        from iscm_suggest_allot_goods_detail
        where record_id in
        <foreach collection="recordIds" item="recordId" index="index" open="(" close=")" separator="," >
            #{recordId}
        </foreach>
        and approve_status = 0
    </select>


    <select id="selectRecordIdListByExample" resultType="java.lang.Long" parameterType="com.cowell.iscm.entity.IscmSuggestAllotGoodsDetailExample" >
        select
        record_id  as recordId
        from iscm_suggest_allot_goods_detail
        <if test="_parameter != null">
            <include refid="Example_Where_Clause" />
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        <if test="limit != null">
            <if test="offset != null">
                limit ${offset}, ${limit}
            </if>
            <if test="offset == null">
                limit ${limit}
            </if>
        </if>
    </select>


    <select id="selectByExampleFlushCache" parameterType="com.cowell.iscm.entity.IscmSuggestAllotGoodsDetailExample" resultMap="BaseResultMap" flushCache="true">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List" />
        from iscm_suggest_allot_goods_detail
        <if test="_parameter != null">
            <include refid="Example_Where_Clause" />
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        <if test="limit != null">
            <if test="offset != null">
                limit ${offset}, ${limit}
            </if>
            <if test="offset == null">
                limit ${limit}
            </if>
        </if>
    </select>


</mapper>
