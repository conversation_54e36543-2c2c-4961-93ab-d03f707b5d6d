<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.extend.IscmRecalculationNoResultLogExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.IscmRecalculationNoResultLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="apply_no" jdbcType="VARCHAR" property="applyNo" />
    <result column="apply_line" jdbcType="VARCHAR" property="applyLine" />
    <result column="apply_date" jdbcType="DATE" property="applyDate" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="apply_stock" jdbcType="DECIMAL" property="applyStock" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, apply_no, apply_line, apply_date, reason, apply_stock
  </sql>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entity.IscmRecalculationNoResultLog" useGeneratedKeys="true">
    insert into iscm_recalculation_no_result_log (apply_no, apply_line, apply_date, 
      reason, apply_stock)
    values
    <foreach collection="list" item="item" index="index" separator="," >
    (#{item.applyNo,jdbcType=VARCHAR}, #{item.applyLine,jdbcType=VARCHAR}, #{item.applyDate,jdbcType=DATE},
      #{item.reason,jdbcType=VARCHAR},#{item.applyStock,jdbcType=DECIMAL})
    </foreach>
  </insert>

  <delete id="deleteByExample" parameterType="com.cowell.iscm.entity.IscmRecalculationNoResultLogExample">
    delete from iscm_recalculation_no_result_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="limit != null">
      limit ${limit}
    </if>
  </delete>
</mapper>
