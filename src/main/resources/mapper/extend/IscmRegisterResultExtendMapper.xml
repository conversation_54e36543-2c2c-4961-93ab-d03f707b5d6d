<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.extend.IscmRegisterResultExtendMapper">

    <select id="selectDistinctRegisterOrderNo" resultType="java.lang.String">
        SELECT register_order_no FROM `iscm_register_result` WHERE status = 0 and gmt_create between #{startDate} and #{endDate} and register_type = #{registerType}
        GROUP BY register_order_no ORDER BY id asc
    </select>

</mapper>