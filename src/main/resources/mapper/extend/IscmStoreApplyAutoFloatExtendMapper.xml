<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.extend.IscmStoreApplyAutoFloatExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.IscmStoreApplyAutoFloat">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="param_code" jdbcType="VARCHAR" property="paramCode" />
    <result column="param_name" jdbcType="VARCHAR" property="paramName" />
    <result column="param_level" jdbcType="INTEGER" property="paramLevel" />
    <result column="org_id" jdbcType="BIGINT" property="orgId" />
    <result column="sap_code" jdbcType="VARCHAR" property="sapCode" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="parent_org_id" jdbcType="BIGINT" property="parentOrgId" />
    <result column="parent_org_name" jdbcType="VARCHAR" property="parentOrgName" />
    <result column="store_org_id" jdbcType="BIGINT" property="storeOrgId" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="store_name" jdbcType="VARCHAR" property="storeName" />
    <result column="goods_choose_type" jdbcType="TINYINT" property="goodsChooseType" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="businessId" jdbcType="BIGINT" property="businessid" />
    <result column="bar_code" jdbcType="VARCHAR" property="barCode" />
    <result column="cur_name" jdbcType="VARCHAR" property="curName" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="goods_unit" jdbcType="VARCHAR" property="goodsUnit" />
    <result column="specifications" jdbcType="VARCHAR" property="specifications" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="goodsline" jdbcType="VARCHAR" property="goodsline" />
    <result column="specialattributes" jdbcType="VARCHAR" property="specialattributes" />
    <result column="goods_level" jdbcType="TINYINT" property="goodsLevel" />
    <result column="start_date" jdbcType="DATE" property="startDate" />
    <result column="end_date" jdbcType="DATE" property="endDate" />
    <result column="inherit_type" jdbcType="TINYINT" property="inheritType" />
    <result column="effect_status" jdbcType="TINYINT" property="effectStatus" />
    <result column="adjust_type" jdbcType="TINYINT" property="adjustType" />
    <result column="adjust_mode" jdbcType="TINYINT" property="adjustMode" />
    <result column="adjust_upper_quantity" jdbcType="INTEGER" property="adjustUpperQuantity" />
    <result column="adjust_lower_quantity" jdbcType="INTEGER" property="adjustLowerQuantity" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, param_code, param_name, param_level, org_id, sap_code, org_name, parent_org_id, 
    parent_org_name, store_org_id, store_code, store_name, goods_choose_type, goods_no,
    businessId, bar_code, cur_name, goods_name, goods_unit, specifications, manufacturer,
    goodsline, specialattributes, goods_level, start_date, end_date, inherit_type, effect_status,
    adjust_type, adjust_mode, adjust_upper_quantity, adjust_lower_quantity, `status`, 
    extend, version, created_by, created_name, updated_by, updated_name, gmt_create, 
    gmt_update
  </sql>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entity.IscmStoreApplyAutoFloat" useGeneratedKeys="true">
    insert into iscm_store_apply_auto_float (param_code, param_name, param_level, 
      org_id, sap_code, org_name, 
      parent_org_id, parent_org_name, store_org_id, 
      businessId, store_code, store_name, 
      goods_choose_type, goods_no, bar_code, 
      cur_name, goods_name, goods_unit, 
      specifications, manufacturer, goodsline, 
      specialattributes, goods_level, start_date, end_date,
      inherit_type, effect_status, adjust_type, 
      adjust_mode, adjust_upper_quantity, adjust_lower_quantity, 
      created_by, created_name, updated_by,
      updated_name
      )
    values
    <foreach collection="list" item="item" index="index" separator="," >
    (#{item.paramCode,jdbcType=VARCHAR}, #{item.paramName,jdbcType=VARCHAR}, #{item.paramLevel,jdbcType=INTEGER},
      #{item.orgId,jdbcType=BIGINT}, #{item.sapCode,jdbcType=VARCHAR}, #{item.orgName,jdbcType=VARCHAR},
      #{item.parentOrgId,jdbcType=BIGINT}, #{item.parentOrgName,jdbcType=VARCHAR}, #{item.storeOrgId,jdbcType=BIGINT},
      #{item.businessid,jdbcType=BIGINT}, #{item.storeCode,jdbcType=VARCHAR}, #{item.storeName,jdbcType=VARCHAR},
      #{item.goodsChooseType,jdbcType=TINYINT}, #{item.goodsNo,jdbcType=VARCHAR}, #{item.barCode,jdbcType=VARCHAR},
      #{item.curName,jdbcType=VARCHAR}, #{item.goodsName,jdbcType=VARCHAR}, #{item.goodsUnit,jdbcType=VARCHAR},
      #{item.specifications,jdbcType=VARCHAR}, #{item.manufacturer,jdbcType=VARCHAR}, #{item.goodsline,jdbcType=VARCHAR},
      #{item.specialattributes,jdbcType=VARCHAR}, #{item.goodsLevel,jdbcType=TINYINT}, #{item.startDate,jdbcType=DATE}, #{item.endDate,jdbcType=DATE},
      #{item.inheritType,jdbcType=TINYINT}, #{item.effectStatus,jdbcType=TINYINT}, #{item.adjustType,jdbcType=TINYINT},
      #{item.adjustMode,jdbcType=TINYINT}, #{item.adjustUpperQuantity,jdbcType=INTEGER}, #{item.adjustLowerQuantity,jdbcType=INTEGER},
      #{item.createdBy,jdbcType=BIGINT}, #{item.createdName,jdbcType=VARCHAR}, #{item.updatedBy,jdbcType=BIGINT},
      #{item.updatedName,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>
    <delete id="deleteByExample">
      delete from iscm_store_apply_auto_float
      <if test="_parameter != null">
        <include refid="Example_Where_Clause" />
      </if>
      <if test="limit != null">
        limit ${limit}
      </if>
    </delete>
    <select id="getOrgIdByIds" resultType="java.lang.Long">
      select org_id from iscm_store_apply_auto_float
      where id in
      <foreach collection="ids" item="id" index="index" separator="," open="(" close=")">
        #{id}
      </foreach>
    </select>
</mapper>
