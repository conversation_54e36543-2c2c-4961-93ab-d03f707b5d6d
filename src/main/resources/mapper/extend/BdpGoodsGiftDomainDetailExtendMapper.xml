<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.extend.BdpGoodsGiftDomainDetailExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.BdpGoodsGiftDomainDetail">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="domain_id" jdbcType="BIGINT" property="domainId" />
    <result column="gift_type" jdbcType="TINYINT" property="giftType" />
    <result column="gift_goods_no" jdbcType="VARCHAR" property="giftGoodsNo" />
    <result column="gift_name" jdbcType="VARCHAR" property="giftName" />
    <result column="gift_manufacturer" jdbcType="VARCHAR" property="giftManufacturer" />
    <result column="gift_specifications" jdbcType="VARCHAR" property="giftSpecifications" />
    <result column="gift_remark" jdbcType="VARCHAR" property="giftRemark" />
    <result column="gift_quantity" jdbcType="DECIMAL" property="giftQuantity" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, domain_id, gift_type, gift_goods_no, gift_name, gift_manufacturer, gift_specifications, 
    gift_remark, gift_quantity, `status`, gmt_create, gmt_update, extend, version, created_by, 
    created_name, updated_by, updated_name
  </sql>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entity.BdpGoodsGiftDomainDetail" useGeneratedKeys="true">
    insert into bdp_goods_gift_domain_detail (domain_id, gift_type, gift_goods_no, 
      gift_name, gift_manufacturer, gift_specifications, 
      gift_remark, gift_quantity, created_by, created_name,
      updated_by, updated_name)
    values
    <foreach collection="list" item="item" index="index" separator="," >
    (#{item.domainId,jdbcType=BIGINT}, #{item.giftType,jdbcType=TINYINT}, #{item.giftGoodsNo,jdbcType=VARCHAR},
      #{item.giftName,jdbcType=VARCHAR}, #{item.giftManufacturer,jdbcType=VARCHAR}, #{item.giftSpecifications,jdbcType=VARCHAR},
      #{item.giftRemark,jdbcType=VARCHAR}, #{item.giftQuantity,jdbcType=DECIMAL}, #{item.createdBy,jdbcType=BIGINT}, #{item.createdName,jdbcType=VARCHAR},
      #{item.updatedBy,jdbcType=BIGINT}, #{item.updatedName,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <select id="selectRecordIdByTypeAndGiftGoodsNos" resultType="java.lang.Long">
    select domain_id from bdp_goods_gift_domain_detail
    where gift_type = #{giftType,jdbcType=TINYINT}
    and gift_goods_no in
    <foreach collection="giftGoodsNos" item="giftGoodsNo" index="index" open="(" close=")" separator="," >
    #{giftGoodsNo,jdbcType=VARCHAR}
    </foreach>

    group by domain_id
  </select>
</mapper>
