<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.extend.IscmCompanyPushReplenishmentKpiExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.IscmCompanyPushReplenishmentKpi">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="bdp_company_code" jdbcType="VARCHAR" property="bdpCompanyCode" />
    <result column="org_id" jdbcType="BIGINT" property="orgId" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="platform_org_id" jdbcType="BIGINT" property="platformOrgId" />
    <result column="platform_org_name" jdbcType="VARCHAR" property="platformOrgName" />
    <result column="company_org_id" jdbcType="BIGINT" property="companyOrgId" />
    <result column="company_business_id" jdbcType="BIGINT" property="companyBusinessId" />
    <result column="company_org_name" jdbcType="VARCHAR" property="companyOrgName" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="model_version" jdbcType="VARCHAR" property="modelVersion" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="real_sales" jdbcType="DECIMAL" property="realSales" />
    <result column="wmape" jdbcType="DECIMAL" property="wmape" />
    <result column="forecast_sales" jdbcType="DECIMAL" property="forecastSales" />
    <result column="forecast_date" jdbcType="TIMESTAMP" property="forecastDate" />
    <result column="bar_code" jdbcType="VARCHAR" property="barCode" />
    <result column="goods_common_name" jdbcType="VARCHAR" property="goodsCommonName" />
    <result column="goods_unit" jdbcType="VARCHAR" property="goodsUnit" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="specifications" jdbcType="VARCHAR" property="specifications" />
    <result column="dosage_form" jdbcType="VARCHAR" property="dosageForm" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="approval_number" jdbcType="VARCHAR" property="approvalNumber" />
    <result column="habitat" jdbcType="VARCHAR" property="habitat" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Base_Column_List">
    id, bdp_company_code, org_id, org_name, platform_org_id, platform_org_name, company_org_id, 
    company_business_id, company_org_name, company_code, model_version, goods_no, goods_name, 
    real_sales, wmape, forecast_sales, forecast_date, bar_code, goods_common_name, goods_unit, 
    description, specifications, dosage_form, manufacturer, approval_number, habitat, 
    status, gmt_create, gmt_update, extend, version, created_by, created_name, updated_by, 
    updated_name
  </sql>
  <insert id="batchInsert" parameterType="com.cowell.iscm.entity.IscmCompanyPushReplenishmentKpi">
    insert into iscm_company_push_replenishment_kpi (bdp_company_code, org_id,
      org_name, platform_org_id, platform_org_name, 
      company_org_id, company_business_id, company_org_name, 
      company_code, model_version, goods_no, 
      goods_name, real_sales, wmape, 
      forecast_sales, forecast_date, bar_code, 
      goods_common_name, goods_unit, description, 
      specifications, dosage_form, manufacturer, 
      approval_number, habitat, created_by, created_name,
      updated_by, updated_name, extend)
    values
    <foreach collection="list" item="item" index="index" separator="," >
    (#{item.bdpCompanyCode,jdbcType=VARCHAR}, #{item.orgId,jdbcType=BIGINT},
      #{item.orgName,jdbcType=VARCHAR}, #{item.platformOrgId,jdbcType=BIGINT}, #{item.platformOrgName,jdbcType=VARCHAR},
      #{item.companyOrgId,jdbcType=BIGINT}, #{item.companyBusinessId,jdbcType=BIGINT}, #{item.companyOrgName,jdbcType=VARCHAR},
      #{item.companyCode,jdbcType=VARCHAR}, #{item.modelVersion,jdbcType=VARCHAR}, #{item.goodsNo,jdbcType=VARCHAR},
      #{item.goodsName,jdbcType=VARCHAR}, #{item.realSales,jdbcType=DECIMAL}, #{item.wmape,jdbcType=DECIMAL},
      #{item.forecastSales,jdbcType=DECIMAL}, #{item.forecastDate,jdbcType=TIMESTAMP}, #{item.barCode,jdbcType=VARCHAR},
      #{item.goodsCommonName,jdbcType=VARCHAR}, #{item.goodsUnit,jdbcType=VARCHAR}, #{item.description,jdbcType=VARCHAR},
      #{item.specifications,jdbcType=VARCHAR}, #{item.dosageForm,jdbcType=VARCHAR}, #{item.manufacturer,jdbcType=VARCHAR},
      #{item.approvalNumber,jdbcType=VARCHAR}, #{item.habitat,jdbcType=VARCHAR}, #{item.createdBy,jdbcType=BIGINT}, #{item.createdName,jdbcType=VARCHAR},
      #{item.updatedBy,jdbcType=BIGINT}, #{item.updatedName,jdbcType=VARCHAR}, #{item.extend,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <select id="selectWmapeLineChart"
          resultType="com.cowell.iscm.service.dto.saletargetmonitor.WmapeLineChartResponse">
    select model_version as modelVersion, DATE_FORMAT(forecast_date,'%Y-%m-%d') as forecastDate, sum(wmape) / count(*) * 100 as wmape
    from iscm_company_push_replenishment_kpi
    where company_business_id = #{businessId}
    and forecast_date between #{queryDateStart} and #{queryDateEnd}
    <if test="goodsNo != null">
      and goods_no = #{goodsNo}
    </if>
    <if test="modelVersion != null">
      and model_version = #{modelVersion}
    </if>
     group by model_version, forecast_date
  </select>
</mapper>
