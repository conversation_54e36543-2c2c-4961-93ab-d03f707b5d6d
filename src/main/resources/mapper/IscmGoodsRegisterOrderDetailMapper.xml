<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.IscmGoodsRegisterOrderDetailMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.IscmGoodsRegisterOrderDetail">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="register_order_no" jdbcType="VARCHAR" property="registerOrderNo" />
    <result column="register_type" jdbcType="TINYINT" property="registerType" />
    <result column="register_source" jdbcType="TINYINT" property="registerSource" />
    <result column="data_type" jdbcType="TINYINT" property="dataType" />
    <result column="suggest_status" jdbcType="TINYINT" property="suggestStatus" />
    <result column="platform_org_id" jdbcType="BIGINT" property="platformOrgId" />
    <result column="platform_org_name" jdbcType="VARCHAR" property="platformOrgName" />
    <result column="company_org_id" jdbcType="BIGINT" property="companyOrgId" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="store_org_id" jdbcType="BIGINT" property="storeOrgId" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="store_attr" jdbcType="VARCHAR" property="storeAttr" />
    <result column="allot_group_code" jdbcType="VARCHAR" property="allotGroupCode" />
    <result column="allot_group_name" jdbcType="VARCHAR" property="allotGroupName" />
    <result column="register_month" jdbcType="INTEGER" property="registerMonth" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="bar_code" jdbcType="VARCHAR" property="barCode" />
    <result column="goods_common_name" jdbcType="VARCHAR" property="goodsCommonName" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="goods_unit" jdbcType="VARCHAR" property="goodsUnit" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="specifications" jdbcType="VARCHAR" property="specifications" />
    <result column="dosage_form" jdbcType="VARCHAR" property="dosageForm" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="approval_number" jdbcType="VARCHAR" property="approvalNumber" />
    <result column="habitat" jdbcType="VARCHAR" property="habitat" />
    <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
    <result column="validity_date" jdbcType="TIMESTAMP" property="validityDate" />
    <result column="produce_date" jdbcType="TIMESTAMP" property="produceDate" />
    <result column="warehouse_code" jdbcType="VARCHAR" property="warehouseCode" />
    <result column="warehouse_name" jdbcType="VARCHAR" property="warehouseName" />
    <result column="goods_class_id" jdbcType="BIGINT" property="goodsClassId" />
    <result column="goods_class_name" jdbcType="VARCHAR" property="goodsClassName" />
    <result column="goods_pur_channel" jdbcType="VARCHAR" property="goodsPurChannel" />
    <result column="register_quantity" jdbcType="DECIMAL" property="registerQuantity" />
    <result column="stock_quantity" jdbcType="DECIMAL" property="stockQuantity" />
    <result column="stock_upper_limit" jdbcType="DECIMAL" property="stockUpperLimit" />
    <result column="stock_lower_limit" jdbcType="DECIMAL" property="stockLowerLimit" />
    <result column="non_sale_days" jdbcType="INTEGER" property="nonSaleDays" />
    <result column="expect_sale_days" jdbcType="DECIMAL" property="expectSaleDays" />
    <result column="min_display_quantity" jdbcType="DECIMAL" property="minDisplayQuantity" />
    <result column="non_validity_stock_quantity" jdbcType="DECIMAL" property="nonValidityStockQuantity" />
    <result column="cost_amount" jdbcType="DECIMAL" property="costAmount" />
    <result column="deal_status" jdbcType="TINYINT" property="dealStatus" />
    <result column="order_status" jdbcType="TINYINT" property="orderStatus" />
    <result column="return_warehouse_status" jdbcType="TINYINT" property="returnWarehouseStatus" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.cowell.iscm.entity.IscmGoodsRegisterOrderDetail">
    <result column="no_suggest_reason" jdbcType="LONGVARCHAR" property="noSuggestReason" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, register_order_no, register_type, register_source, data_type, suggest_status, 
    platform_org_id, platform_org_name, company_org_id, company_code, store_org_id, store_code, 
    store_attr, allot_group_code, allot_group_name, register_month, goods_no, bar_code, 
    goods_common_name, goods_name, goods_unit, description, specifications, dosage_form, 
    manufacturer, approval_number, habitat, batch_no, validity_date, produce_date, warehouse_code, 
    warehouse_name, goods_class_id, goods_class_name, goods_pur_channel, register_quantity, 
    stock_quantity, stock_upper_limit, stock_lower_limit, non_sale_days, expect_sale_days, 
    min_display_quantity, non_validity_stock_quantity, cost_amount, deal_status, order_status, 
    return_warehouse_status, `status`, gmt_create, gmt_update, extend, version, created_by, 
    created_name, updated_by, updated_name
  </sql>
  <sql id="Blob_Column_List">
    no_suggest_reason
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.cowell.iscm.entity.IscmGoodsRegisterOrderDetailExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from iscm_goods_register_order_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByExample" parameterType="com.cowell.iscm.entity.IscmGoodsRegisterOrderDetailExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from iscm_goods_register_order_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from iscm_goods_register_order_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from iscm_goods_register_order_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.iscm.entity.IscmGoodsRegisterOrderDetailExample">
    delete from iscm_goods_register_order_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entity.IscmGoodsRegisterOrderDetail" useGeneratedKeys="true">
    insert into iscm_goods_register_order_detail (register_order_no, register_type, register_source, 
      data_type, suggest_status, platform_org_id, 
      platform_org_name, company_org_id, company_code, 
      store_org_id, store_code, store_attr, 
      allot_group_code, allot_group_name, register_month, 
      goods_no, bar_code, goods_common_name, 
      goods_name, goods_unit, description, 
      specifications, dosage_form, manufacturer, 
      approval_number, habitat, batch_no, 
      validity_date, produce_date, warehouse_code, 
      warehouse_name, goods_class_id, goods_class_name, 
      goods_pur_channel, register_quantity, stock_quantity, 
      stock_upper_limit, stock_lower_limit, non_sale_days, 
      expect_sale_days, min_display_quantity, non_validity_stock_quantity, 
      cost_amount, deal_status, order_status, 
      return_warehouse_status, `status`, gmt_create, 
      gmt_update, extend, version, 
      created_by, created_name, updated_by, 
      updated_name, no_suggest_reason)
    values (#{registerOrderNo,jdbcType=VARCHAR}, #{registerType,jdbcType=TINYINT}, #{registerSource,jdbcType=TINYINT}, 
      #{dataType,jdbcType=TINYINT}, #{suggestStatus,jdbcType=TINYINT}, #{platformOrgId,jdbcType=BIGINT}, 
      #{platformOrgName,jdbcType=VARCHAR}, #{companyOrgId,jdbcType=BIGINT}, #{companyCode,jdbcType=VARCHAR}, 
      #{storeOrgId,jdbcType=BIGINT}, #{storeCode,jdbcType=VARCHAR}, #{storeAttr,jdbcType=VARCHAR}, 
      #{allotGroupCode,jdbcType=VARCHAR}, #{allotGroupName,jdbcType=VARCHAR}, #{registerMonth,jdbcType=INTEGER}, 
      #{goodsNo,jdbcType=VARCHAR}, #{barCode,jdbcType=VARCHAR}, #{goodsCommonName,jdbcType=VARCHAR}, 
      #{goodsName,jdbcType=VARCHAR}, #{goodsUnit,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, 
      #{specifications,jdbcType=VARCHAR}, #{dosageForm,jdbcType=VARCHAR}, #{manufacturer,jdbcType=VARCHAR}, 
      #{approvalNumber,jdbcType=VARCHAR}, #{habitat,jdbcType=VARCHAR}, #{batchNo,jdbcType=VARCHAR}, 
      #{validityDate,jdbcType=TIMESTAMP}, #{produceDate,jdbcType=TIMESTAMP}, #{warehouseCode,jdbcType=VARCHAR}, 
      #{warehouseName,jdbcType=VARCHAR}, #{goodsClassId,jdbcType=BIGINT}, #{goodsClassName,jdbcType=VARCHAR}, 
      #{goodsPurChannel,jdbcType=VARCHAR}, #{registerQuantity,jdbcType=DECIMAL}, #{stockQuantity,jdbcType=DECIMAL}, 
      #{stockUpperLimit,jdbcType=DECIMAL}, #{stockLowerLimit,jdbcType=DECIMAL}, #{nonSaleDays,jdbcType=INTEGER}, 
      #{expectSaleDays,jdbcType=DECIMAL}, #{minDisplayQuantity,jdbcType=DECIMAL}, #{nonValidityStockQuantity,jdbcType=DECIMAL}, 
      #{costAmount,jdbcType=DECIMAL}, #{dealStatus,jdbcType=TINYINT}, #{orderStatus,jdbcType=TINYINT}, 
      #{returnWarehouseStatus,jdbcType=TINYINT}, #{status,jdbcType=TINYINT}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtUpdate,jdbcType=TIMESTAMP}, #{extend,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER}, 
      #{createdBy,jdbcType=BIGINT}, #{createdName,jdbcType=VARCHAR}, #{updatedBy,jdbcType=BIGINT}, 
      #{updatedName,jdbcType=VARCHAR}, #{noSuggestReason,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entity.IscmGoodsRegisterOrderDetail" useGeneratedKeys="true">
    insert into iscm_goods_register_order_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="registerOrderNo != null">
        register_order_no,
      </if>
      <if test="registerType != null">
        register_type,
      </if>
      <if test="registerSource != null">
        register_source,
      </if>
      <if test="dataType != null">
        data_type,
      </if>
      <if test="suggestStatus != null">
        suggest_status,
      </if>
      <if test="platformOrgId != null">
        platform_org_id,
      </if>
      <if test="platformOrgName != null">
        platform_org_name,
      </if>
      <if test="companyOrgId != null">
        company_org_id,
      </if>
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="storeOrgId != null">
        store_org_id,
      </if>
      <if test="storeCode != null">
        store_code,
      </if>
      <if test="storeAttr != null">
        store_attr,
      </if>
      <if test="allotGroupCode != null">
        allot_group_code,
      </if>
      <if test="allotGroupName != null">
        allot_group_name,
      </if>
      <if test="registerMonth != null">
        register_month,
      </if>
      <if test="goodsNo != null">
        goods_no,
      </if>
      <if test="barCode != null">
        bar_code,
      </if>
      <if test="goodsCommonName != null">
        goods_common_name,
      </if>
      <if test="goodsName != null">
        goods_name,
      </if>
      <if test="goodsUnit != null">
        goods_unit,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="specifications != null">
        specifications,
      </if>
      <if test="dosageForm != null">
        dosage_form,
      </if>
      <if test="manufacturer != null">
        manufacturer,
      </if>
      <if test="approvalNumber != null">
        approval_number,
      </if>
      <if test="habitat != null">
        habitat,
      </if>
      <if test="batchNo != null">
        batch_no,
      </if>
      <if test="validityDate != null">
        validity_date,
      </if>
      <if test="produceDate != null">
        produce_date,
      </if>
      <if test="warehouseCode != null">
        warehouse_code,
      </if>
      <if test="warehouseName != null">
        warehouse_name,
      </if>
      <if test="goodsClassId != null">
        goods_class_id,
      </if>
      <if test="goodsClassName != null">
        goods_class_name,
      </if>
      <if test="goodsPurChannel != null">
        goods_pur_channel,
      </if>
      <if test="registerQuantity != null">
        register_quantity,
      </if>
      <if test="stockQuantity != null">
        stock_quantity,
      </if>
      <if test="stockUpperLimit != null">
        stock_upper_limit,
      </if>
      <if test="stockLowerLimit != null">
        stock_lower_limit,
      </if>
      <if test="nonSaleDays != null">
        non_sale_days,
      </if>
      <if test="expectSaleDays != null">
        expect_sale_days,
      </if>
      <if test="minDisplayQuantity != null">
        min_display_quantity,
      </if>
      <if test="nonValidityStockQuantity != null">
        non_validity_stock_quantity,
      </if>
      <if test="costAmount != null">
        cost_amount,
      </if>
      <if test="dealStatus != null">
        deal_status,
      </if>
      <if test="orderStatus != null">
        order_status,
      </if>
      <if test="returnWarehouseStatus != null">
        return_warehouse_status,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedName != null">
        updated_name,
      </if>
      <if test="noSuggestReason != null">
        no_suggest_reason,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="registerOrderNo != null">
        #{registerOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="registerType != null">
        #{registerType,jdbcType=TINYINT},
      </if>
      <if test="registerSource != null">
        #{registerSource,jdbcType=TINYINT},
      </if>
      <if test="dataType != null">
        #{dataType,jdbcType=TINYINT},
      </if>
      <if test="suggestStatus != null">
        #{suggestStatus,jdbcType=TINYINT},
      </if>
      <if test="platformOrgId != null">
        #{platformOrgId,jdbcType=BIGINT},
      </if>
      <if test="platformOrgName != null">
        #{platformOrgName,jdbcType=VARCHAR},
      </if>
      <if test="companyOrgId != null">
        #{companyOrgId,jdbcType=BIGINT},
      </if>
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="storeOrgId != null">
        #{storeOrgId,jdbcType=BIGINT},
      </if>
      <if test="storeCode != null">
        #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="storeAttr != null">
        #{storeAttr,jdbcType=VARCHAR},
      </if>
      <if test="allotGroupCode != null">
        #{allotGroupCode,jdbcType=VARCHAR},
      </if>
      <if test="allotGroupName != null">
        #{allotGroupName,jdbcType=VARCHAR},
      </if>
      <if test="registerMonth != null">
        #{registerMonth,jdbcType=INTEGER},
      </if>
      <if test="goodsNo != null">
        #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="barCode != null">
        #{barCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsCommonName != null">
        #{goodsCommonName,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="goodsUnit != null">
        #{goodsUnit,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="specifications != null">
        #{specifications,jdbcType=VARCHAR},
      </if>
      <if test="dosageForm != null">
        #{dosageForm,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="approvalNumber != null">
        #{approvalNumber,jdbcType=VARCHAR},
      </if>
      <if test="habitat != null">
        #{habitat,jdbcType=VARCHAR},
      </if>
      <if test="batchNo != null">
        #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="validityDate != null">
        #{validityDate,jdbcType=TIMESTAMP},
      </if>
      <if test="produceDate != null">
        #{produceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="warehouseCode != null">
        #{warehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="warehouseName != null">
        #{warehouseName,jdbcType=VARCHAR},
      </if>
      <if test="goodsClassId != null">
        #{goodsClassId,jdbcType=BIGINT},
      </if>
      <if test="goodsClassName != null">
        #{goodsClassName,jdbcType=VARCHAR},
      </if>
      <if test="goodsPurChannel != null">
        #{goodsPurChannel,jdbcType=VARCHAR},
      </if>
      <if test="registerQuantity != null">
        #{registerQuantity,jdbcType=DECIMAL},
      </if>
      <if test="stockQuantity != null">
        #{stockQuantity,jdbcType=DECIMAL},
      </if>
      <if test="stockUpperLimit != null">
        #{stockUpperLimit,jdbcType=DECIMAL},
      </if>
      <if test="stockLowerLimit != null">
        #{stockLowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="nonSaleDays != null">
        #{nonSaleDays,jdbcType=INTEGER},
      </if>
      <if test="expectSaleDays != null">
        #{expectSaleDays,jdbcType=DECIMAL},
      </if>
      <if test="minDisplayQuantity != null">
        #{minDisplayQuantity,jdbcType=DECIMAL},
      </if>
      <if test="nonValidityStockQuantity != null">
        #{nonValidityStockQuantity,jdbcType=DECIMAL},
      </if>
      <if test="costAmount != null">
        #{costAmount,jdbcType=DECIMAL},
      </if>
      <if test="dealStatus != null">
        #{dealStatus,jdbcType=TINYINT},
      </if>
      <if test="orderStatus != null">
        #{orderStatus,jdbcType=TINYINT},
      </if>
      <if test="returnWarehouseStatus != null">
        #{returnWarehouseStatus,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        #{updatedName,jdbcType=VARCHAR},
      </if>
      <if test="noSuggestReason != null">
        #{noSuggestReason,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.iscm.entity.IscmGoodsRegisterOrderDetailExample" resultType="java.lang.Long">
    select count(*) from iscm_goods_register_order_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update iscm_goods_register_order_detail
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.registerOrderNo != null">
        register_order_no = #{record.registerOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.registerType != null">
        register_type = #{record.registerType,jdbcType=TINYINT},
      </if>
      <if test="record.registerSource != null">
        register_source = #{record.registerSource,jdbcType=TINYINT},
      </if>
      <if test="record.dataType != null">
        data_type = #{record.dataType,jdbcType=TINYINT},
      </if>
      <if test="record.suggestStatus != null">
        suggest_status = #{record.suggestStatus,jdbcType=TINYINT},
      </if>
      <if test="record.platformOrgId != null">
        platform_org_id = #{record.platformOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.platformOrgName != null">
        platform_org_name = #{record.platformOrgName,jdbcType=VARCHAR},
      </if>
      <if test="record.companyOrgId != null">
        company_org_id = #{record.companyOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.companyCode != null">
        company_code = #{record.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.storeOrgId != null">
        store_org_id = #{record.storeOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.storeCode != null">
        store_code = #{record.storeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.storeAttr != null">
        store_attr = #{record.storeAttr,jdbcType=VARCHAR},
      </if>
      <if test="record.allotGroupCode != null">
        allot_group_code = #{record.allotGroupCode,jdbcType=VARCHAR},
      </if>
      <if test="record.allotGroupName != null">
        allot_group_name = #{record.allotGroupName,jdbcType=VARCHAR},
      </if>
      <if test="record.registerMonth != null">
        register_month = #{record.registerMonth,jdbcType=INTEGER},
      </if>
      <if test="record.goodsNo != null">
        goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="record.barCode != null">
        bar_code = #{record.barCode,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsCommonName != null">
        goods_common_name = #{record.goodsCommonName,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsName != null">
        goods_name = #{record.goodsName,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsUnit != null">
        goods_unit = #{record.goodsUnit,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.specifications != null">
        specifications = #{record.specifications,jdbcType=VARCHAR},
      </if>
      <if test="record.dosageForm != null">
        dosage_form = #{record.dosageForm,jdbcType=VARCHAR},
      </if>
      <if test="record.manufacturer != null">
        manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="record.approvalNumber != null">
        approval_number = #{record.approvalNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.habitat != null">
        habitat = #{record.habitat,jdbcType=VARCHAR},
      </if>
      <if test="record.batchNo != null">
        batch_no = #{record.batchNo,jdbcType=VARCHAR},
      </if>
      <if test="record.validityDate != null">
        validity_date = #{record.validityDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.produceDate != null">
        produce_date = #{record.produceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.warehouseCode != null">
        warehouse_code = #{record.warehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="record.warehouseName != null">
        warehouse_name = #{record.warehouseName,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsClassId != null">
        goods_class_id = #{record.goodsClassId,jdbcType=BIGINT},
      </if>
      <if test="record.goodsClassName != null">
        goods_class_name = #{record.goodsClassName,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsPurChannel != null">
        goods_pur_channel = #{record.goodsPurChannel,jdbcType=VARCHAR},
      </if>
      <if test="record.registerQuantity != null">
        register_quantity = #{record.registerQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.stockQuantity != null">
        stock_quantity = #{record.stockQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.stockUpperLimit != null">
        stock_upper_limit = #{record.stockUpperLimit,jdbcType=DECIMAL},
      </if>
      <if test="record.stockLowerLimit != null">
        stock_lower_limit = #{record.stockLowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="record.nonSaleDays != null">
        non_sale_days = #{record.nonSaleDays,jdbcType=INTEGER},
      </if>
      <if test="record.expectSaleDays != null">
        expect_sale_days = #{record.expectSaleDays,jdbcType=DECIMAL},
      </if>
      <if test="record.minDisplayQuantity != null">
        min_display_quantity = #{record.minDisplayQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.nonValidityStockQuantity != null">
        non_validity_stock_quantity = #{record.nonValidityStockQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.costAmount != null">
        cost_amount = #{record.costAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.dealStatus != null">
        deal_status = #{record.dealStatus,jdbcType=TINYINT},
      </if>
      <if test="record.orderStatus != null">
        order_status = #{record.orderStatus,jdbcType=TINYINT},
      </if>
      <if test="record.returnWarehouseStatus != null">
        return_warehouse_status = #{record.returnWarehouseStatus,jdbcType=TINYINT},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedName != null">
        updated_name = #{record.updatedName,jdbcType=VARCHAR},
      </if>
      <if test="record.noSuggestReason != null">
        no_suggest_reason = #{record.noSuggestReason,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update iscm_goods_register_order_detail
    set id = #{record.id,jdbcType=BIGINT},
      register_order_no = #{record.registerOrderNo,jdbcType=VARCHAR},
      register_type = #{record.registerType,jdbcType=TINYINT},
      register_source = #{record.registerSource,jdbcType=TINYINT},
      data_type = #{record.dataType,jdbcType=TINYINT},
      suggest_status = #{record.suggestStatus,jdbcType=TINYINT},
      platform_org_id = #{record.platformOrgId,jdbcType=BIGINT},
      platform_org_name = #{record.platformOrgName,jdbcType=VARCHAR},
      company_org_id = #{record.companyOrgId,jdbcType=BIGINT},
      company_code = #{record.companyCode,jdbcType=VARCHAR},
      store_org_id = #{record.storeOrgId,jdbcType=BIGINT},
      store_code = #{record.storeCode,jdbcType=VARCHAR},
      store_attr = #{record.storeAttr,jdbcType=VARCHAR},
      allot_group_code = #{record.allotGroupCode,jdbcType=VARCHAR},
      allot_group_name = #{record.allotGroupName,jdbcType=VARCHAR},
      register_month = #{record.registerMonth,jdbcType=INTEGER},
      goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      bar_code = #{record.barCode,jdbcType=VARCHAR},
      goods_common_name = #{record.goodsCommonName,jdbcType=VARCHAR},
      goods_name = #{record.goodsName,jdbcType=VARCHAR},
      goods_unit = #{record.goodsUnit,jdbcType=VARCHAR},
      description = #{record.description,jdbcType=VARCHAR},
      specifications = #{record.specifications,jdbcType=VARCHAR},
      dosage_form = #{record.dosageForm,jdbcType=VARCHAR},
      manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      approval_number = #{record.approvalNumber,jdbcType=VARCHAR},
      habitat = #{record.habitat,jdbcType=VARCHAR},
      batch_no = #{record.batchNo,jdbcType=VARCHAR},
      validity_date = #{record.validityDate,jdbcType=TIMESTAMP},
      produce_date = #{record.produceDate,jdbcType=TIMESTAMP},
      warehouse_code = #{record.warehouseCode,jdbcType=VARCHAR},
      warehouse_name = #{record.warehouseName,jdbcType=VARCHAR},
      goods_class_id = #{record.goodsClassId,jdbcType=BIGINT},
      goods_class_name = #{record.goodsClassName,jdbcType=VARCHAR},
      goods_pur_channel = #{record.goodsPurChannel,jdbcType=VARCHAR},
      register_quantity = #{record.registerQuantity,jdbcType=DECIMAL},
      stock_quantity = #{record.stockQuantity,jdbcType=DECIMAL},
      stock_upper_limit = #{record.stockUpperLimit,jdbcType=DECIMAL},
      stock_lower_limit = #{record.stockLowerLimit,jdbcType=DECIMAL},
      non_sale_days = #{record.nonSaleDays,jdbcType=INTEGER},
      expect_sale_days = #{record.expectSaleDays,jdbcType=DECIMAL},
      min_display_quantity = #{record.minDisplayQuantity,jdbcType=DECIMAL},
      non_validity_stock_quantity = #{record.nonValidityStockQuantity,jdbcType=DECIMAL},
      cost_amount = #{record.costAmount,jdbcType=DECIMAL},
      deal_status = #{record.dealStatus,jdbcType=TINYINT},
      order_status = #{record.orderStatus,jdbcType=TINYINT},
      return_warehouse_status = #{record.returnWarehouseStatus,jdbcType=TINYINT},
      `status` = #{record.status,jdbcType=TINYINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_name = #{record.createdName,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_name = #{record.updatedName,jdbcType=VARCHAR},
      no_suggest_reason = #{record.noSuggestReason,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update iscm_goods_register_order_detail
    set id = #{record.id,jdbcType=BIGINT},
      register_order_no = #{record.registerOrderNo,jdbcType=VARCHAR},
      register_type = #{record.registerType,jdbcType=TINYINT},
      register_source = #{record.registerSource,jdbcType=TINYINT},
      data_type = #{record.dataType,jdbcType=TINYINT},
      suggest_status = #{record.suggestStatus,jdbcType=TINYINT},
      platform_org_id = #{record.platformOrgId,jdbcType=BIGINT},
      platform_org_name = #{record.platformOrgName,jdbcType=VARCHAR},
      company_org_id = #{record.companyOrgId,jdbcType=BIGINT},
      company_code = #{record.companyCode,jdbcType=VARCHAR},
      store_org_id = #{record.storeOrgId,jdbcType=BIGINT},
      store_code = #{record.storeCode,jdbcType=VARCHAR},
      store_attr = #{record.storeAttr,jdbcType=VARCHAR},
      allot_group_code = #{record.allotGroupCode,jdbcType=VARCHAR},
      allot_group_name = #{record.allotGroupName,jdbcType=VARCHAR},
      register_month = #{record.registerMonth,jdbcType=INTEGER},
      goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      bar_code = #{record.barCode,jdbcType=VARCHAR},
      goods_common_name = #{record.goodsCommonName,jdbcType=VARCHAR},
      goods_name = #{record.goodsName,jdbcType=VARCHAR},
      goods_unit = #{record.goodsUnit,jdbcType=VARCHAR},
      description = #{record.description,jdbcType=VARCHAR},
      specifications = #{record.specifications,jdbcType=VARCHAR},
      dosage_form = #{record.dosageForm,jdbcType=VARCHAR},
      manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      approval_number = #{record.approvalNumber,jdbcType=VARCHAR},
      habitat = #{record.habitat,jdbcType=VARCHAR},
      batch_no = #{record.batchNo,jdbcType=VARCHAR},
      validity_date = #{record.validityDate,jdbcType=TIMESTAMP},
      produce_date = #{record.produceDate,jdbcType=TIMESTAMP},
      warehouse_code = #{record.warehouseCode,jdbcType=VARCHAR},
      warehouse_name = #{record.warehouseName,jdbcType=VARCHAR},
      goods_class_id = #{record.goodsClassId,jdbcType=BIGINT},
      goods_class_name = #{record.goodsClassName,jdbcType=VARCHAR},
      goods_pur_channel = #{record.goodsPurChannel,jdbcType=VARCHAR},
      register_quantity = #{record.registerQuantity,jdbcType=DECIMAL},
      stock_quantity = #{record.stockQuantity,jdbcType=DECIMAL},
      stock_upper_limit = #{record.stockUpperLimit,jdbcType=DECIMAL},
      stock_lower_limit = #{record.stockLowerLimit,jdbcType=DECIMAL},
      non_sale_days = #{record.nonSaleDays,jdbcType=INTEGER},
      expect_sale_days = #{record.expectSaleDays,jdbcType=DECIMAL},
      min_display_quantity = #{record.minDisplayQuantity,jdbcType=DECIMAL},
      non_validity_stock_quantity = #{record.nonValidityStockQuantity,jdbcType=DECIMAL},
      cost_amount = #{record.costAmount,jdbcType=DECIMAL},
      deal_status = #{record.dealStatus,jdbcType=TINYINT},
      order_status = #{record.orderStatus,jdbcType=TINYINT},
      return_warehouse_status = #{record.returnWarehouseStatus,jdbcType=TINYINT},
      `status` = #{record.status,jdbcType=TINYINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_name = #{record.createdName,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_name = #{record.updatedName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.iscm.entity.IscmGoodsRegisterOrderDetail">
    update iscm_goods_register_order_detail
    <set>
      <if test="registerOrderNo != null">
        register_order_no = #{registerOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="registerType != null">
        register_type = #{registerType,jdbcType=TINYINT},
      </if>
      <if test="registerSource != null">
        register_source = #{registerSource,jdbcType=TINYINT},
      </if>
      <if test="dataType != null">
        data_type = #{dataType,jdbcType=TINYINT},
      </if>
      <if test="suggestStatus != null">
        suggest_status = #{suggestStatus,jdbcType=TINYINT},
      </if>
      <if test="platformOrgId != null">
        platform_org_id = #{platformOrgId,jdbcType=BIGINT},
      </if>
      <if test="platformOrgName != null">
        platform_org_name = #{platformOrgName,jdbcType=VARCHAR},
      </if>
      <if test="companyOrgId != null">
        company_org_id = #{companyOrgId,jdbcType=BIGINT},
      </if>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="storeOrgId != null">
        store_org_id = #{storeOrgId,jdbcType=BIGINT},
      </if>
      <if test="storeCode != null">
        store_code = #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="storeAttr != null">
        store_attr = #{storeAttr,jdbcType=VARCHAR},
      </if>
      <if test="allotGroupCode != null">
        allot_group_code = #{allotGroupCode,jdbcType=VARCHAR},
      </if>
      <if test="allotGroupName != null">
        allot_group_name = #{allotGroupName,jdbcType=VARCHAR},
      </if>
      <if test="registerMonth != null">
        register_month = #{registerMonth,jdbcType=INTEGER},
      </if>
      <if test="goodsNo != null">
        goods_no = #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="barCode != null">
        bar_code = #{barCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsCommonName != null">
        goods_common_name = #{goodsCommonName,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        goods_name = #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="goodsUnit != null">
        goods_unit = #{goodsUnit,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="specifications != null">
        specifications = #{specifications,jdbcType=VARCHAR},
      </if>
      <if test="dosageForm != null">
        dosage_form = #{dosageForm,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        manufacturer = #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="approvalNumber != null">
        approval_number = #{approvalNumber,jdbcType=VARCHAR},
      </if>
      <if test="habitat != null">
        habitat = #{habitat,jdbcType=VARCHAR},
      </if>
      <if test="batchNo != null">
        batch_no = #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="validityDate != null">
        validity_date = #{validityDate,jdbcType=TIMESTAMP},
      </if>
      <if test="produceDate != null">
        produce_date = #{produceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="warehouseCode != null">
        warehouse_code = #{warehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="warehouseName != null">
        warehouse_name = #{warehouseName,jdbcType=VARCHAR},
      </if>
      <if test="goodsClassId != null">
        goods_class_id = #{goodsClassId,jdbcType=BIGINT},
      </if>
      <if test="goodsClassName != null">
        goods_class_name = #{goodsClassName,jdbcType=VARCHAR},
      </if>
      <if test="goodsPurChannel != null">
        goods_pur_channel = #{goodsPurChannel,jdbcType=VARCHAR},
      </if>
      <if test="registerQuantity != null">
        register_quantity = #{registerQuantity,jdbcType=DECIMAL},
      </if>
      <if test="stockQuantity != null">
        stock_quantity = #{stockQuantity,jdbcType=DECIMAL},
      </if>
      <if test="stockUpperLimit != null">
        stock_upper_limit = #{stockUpperLimit,jdbcType=DECIMAL},
      </if>
      <if test="stockLowerLimit != null">
        stock_lower_limit = #{stockLowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="nonSaleDays != null">
        non_sale_days = #{nonSaleDays,jdbcType=INTEGER},
      </if>
      <if test="expectSaleDays != null">
        expect_sale_days = #{expectSaleDays,jdbcType=DECIMAL},
      </if>
      <if test="minDisplayQuantity != null">
        min_display_quantity = #{minDisplayQuantity,jdbcType=DECIMAL},
      </if>
      <if test="nonValidityStockQuantity != null">
        non_validity_stock_quantity = #{nonValidityStockQuantity,jdbcType=DECIMAL},
      </if>
      <if test="costAmount != null">
        cost_amount = #{costAmount,jdbcType=DECIMAL},
      </if>
      <if test="dealStatus != null">
        deal_status = #{dealStatus,jdbcType=TINYINT},
      </if>
      <if test="orderStatus != null">
        order_status = #{orderStatus,jdbcType=TINYINT},
      </if>
      <if test="returnWarehouseStatus != null">
        return_warehouse_status = #{returnWarehouseStatus,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        updated_name = #{updatedName,jdbcType=VARCHAR},
      </if>
      <if test="noSuggestReason != null">
        no_suggest_reason = #{noSuggestReason,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.cowell.iscm.entity.IscmGoodsRegisterOrderDetail">
    update iscm_goods_register_order_detail
    set register_order_no = #{registerOrderNo,jdbcType=VARCHAR},
      register_type = #{registerType,jdbcType=TINYINT},
      register_source = #{registerSource,jdbcType=TINYINT},
      data_type = #{dataType,jdbcType=TINYINT},
      suggest_status = #{suggestStatus,jdbcType=TINYINT},
      platform_org_id = #{platformOrgId,jdbcType=BIGINT},
      platform_org_name = #{platformOrgName,jdbcType=VARCHAR},
      company_org_id = #{companyOrgId,jdbcType=BIGINT},
      company_code = #{companyCode,jdbcType=VARCHAR},
      store_org_id = #{storeOrgId,jdbcType=BIGINT},
      store_code = #{storeCode,jdbcType=VARCHAR},
      store_attr = #{storeAttr,jdbcType=VARCHAR},
      allot_group_code = #{allotGroupCode,jdbcType=VARCHAR},
      allot_group_name = #{allotGroupName,jdbcType=VARCHAR},
      register_month = #{registerMonth,jdbcType=INTEGER},
      goods_no = #{goodsNo,jdbcType=VARCHAR},
      bar_code = #{barCode,jdbcType=VARCHAR},
      goods_common_name = #{goodsCommonName,jdbcType=VARCHAR},
      goods_name = #{goodsName,jdbcType=VARCHAR},
      goods_unit = #{goodsUnit,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      specifications = #{specifications,jdbcType=VARCHAR},
      dosage_form = #{dosageForm,jdbcType=VARCHAR},
      manufacturer = #{manufacturer,jdbcType=VARCHAR},
      approval_number = #{approvalNumber,jdbcType=VARCHAR},
      habitat = #{habitat,jdbcType=VARCHAR},
      batch_no = #{batchNo,jdbcType=VARCHAR},
      validity_date = #{validityDate,jdbcType=TIMESTAMP},
      produce_date = #{produceDate,jdbcType=TIMESTAMP},
      warehouse_code = #{warehouseCode,jdbcType=VARCHAR},
      warehouse_name = #{warehouseName,jdbcType=VARCHAR},
      goods_class_id = #{goodsClassId,jdbcType=BIGINT},
      goods_class_name = #{goodsClassName,jdbcType=VARCHAR},
      goods_pur_channel = #{goodsPurChannel,jdbcType=VARCHAR},
      register_quantity = #{registerQuantity,jdbcType=DECIMAL},
      stock_quantity = #{stockQuantity,jdbcType=DECIMAL},
      stock_upper_limit = #{stockUpperLimit,jdbcType=DECIMAL},
      stock_lower_limit = #{stockLowerLimit,jdbcType=DECIMAL},
      non_sale_days = #{nonSaleDays,jdbcType=INTEGER},
      expect_sale_days = #{expectSaleDays,jdbcType=DECIMAL},
      min_display_quantity = #{minDisplayQuantity,jdbcType=DECIMAL},
      non_validity_stock_quantity = #{nonValidityStockQuantity,jdbcType=DECIMAL},
      cost_amount = #{costAmount,jdbcType=DECIMAL},
      deal_status = #{dealStatus,jdbcType=TINYINT},
      order_status = #{orderStatus,jdbcType=TINYINT},
      return_warehouse_status = #{returnWarehouseStatus,jdbcType=TINYINT},
      `status` = #{status,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_name = #{createdName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_name = #{updatedName,jdbcType=VARCHAR},
      no_suggest_reason = #{noSuggestReason,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.iscm.entity.IscmGoodsRegisterOrderDetail">
    update iscm_goods_register_order_detail
    set register_order_no = #{registerOrderNo,jdbcType=VARCHAR},
      register_type = #{registerType,jdbcType=TINYINT},
      register_source = #{registerSource,jdbcType=TINYINT},
      data_type = #{dataType,jdbcType=TINYINT},
      suggest_status = #{suggestStatus,jdbcType=TINYINT},
      platform_org_id = #{platformOrgId,jdbcType=BIGINT},
      platform_org_name = #{platformOrgName,jdbcType=VARCHAR},
      company_org_id = #{companyOrgId,jdbcType=BIGINT},
      company_code = #{companyCode,jdbcType=VARCHAR},
      store_org_id = #{storeOrgId,jdbcType=BIGINT},
      store_code = #{storeCode,jdbcType=VARCHAR},
      store_attr = #{storeAttr,jdbcType=VARCHAR},
      allot_group_code = #{allotGroupCode,jdbcType=VARCHAR},
      allot_group_name = #{allotGroupName,jdbcType=VARCHAR},
      register_month = #{registerMonth,jdbcType=INTEGER},
      goods_no = #{goodsNo,jdbcType=VARCHAR},
      bar_code = #{barCode,jdbcType=VARCHAR},
      goods_common_name = #{goodsCommonName,jdbcType=VARCHAR},
      goods_name = #{goodsName,jdbcType=VARCHAR},
      goods_unit = #{goodsUnit,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      specifications = #{specifications,jdbcType=VARCHAR},
      dosage_form = #{dosageForm,jdbcType=VARCHAR},
      manufacturer = #{manufacturer,jdbcType=VARCHAR},
      approval_number = #{approvalNumber,jdbcType=VARCHAR},
      habitat = #{habitat,jdbcType=VARCHAR},
      batch_no = #{batchNo,jdbcType=VARCHAR},
      validity_date = #{validityDate,jdbcType=TIMESTAMP},
      produce_date = #{produceDate,jdbcType=TIMESTAMP},
      warehouse_code = #{warehouseCode,jdbcType=VARCHAR},
      warehouse_name = #{warehouseName,jdbcType=VARCHAR},
      goods_class_id = #{goodsClassId,jdbcType=BIGINT},
      goods_class_name = #{goodsClassName,jdbcType=VARCHAR},
      goods_pur_channel = #{goodsPurChannel,jdbcType=VARCHAR},
      register_quantity = #{registerQuantity,jdbcType=DECIMAL},
      stock_quantity = #{stockQuantity,jdbcType=DECIMAL},
      stock_upper_limit = #{stockUpperLimit,jdbcType=DECIMAL},
      stock_lower_limit = #{stockLowerLimit,jdbcType=DECIMAL},
      non_sale_days = #{nonSaleDays,jdbcType=INTEGER},
      expect_sale_days = #{expectSaleDays,jdbcType=DECIMAL},
      min_display_quantity = #{minDisplayQuantity,jdbcType=DECIMAL},
      non_validity_stock_quantity = #{nonValidityStockQuantity,jdbcType=DECIMAL},
      cost_amount = #{costAmount,jdbcType=DECIMAL},
      deal_status = #{dealStatus,jdbcType=TINYINT},
      order_status = #{orderStatus,jdbcType=TINYINT},
      return_warehouse_status = #{returnWarehouseStatus,jdbcType=TINYINT},
      `status` = #{status,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_name = #{createdName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_name = #{updatedName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>