<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.IscmZdtTaskMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.IscmZdtTask">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="task_key" jdbcType="VARCHAR" property="taskKey" />
    <result column="task_key_type" jdbcType="TINYINT" property="taskKeyType" />
    <result column="task_key_type_desc" jdbcType="VARCHAR" property="taskKeyTypeDesc" />
    <result column="type_id" jdbcType="BIGINT" property="typeId" />
    <result column="type_code" jdbcType="VARCHAR" property="typeCode" />
    <result column="task_name" jdbcType="VARCHAR" property="taskName" />
    <result column="task_mark" jdbcType="VARCHAR" property="taskMark" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.cowell.iscm.entity.IscmZdtTask">
    <result column="task_info" jdbcType="LONGVARCHAR" property="taskInfo" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, task_key, task_key_type, task_key_type_desc, type_id, type_code, task_name, task_mark, 
    status, gmt_create, gmt_update, extend, version, created_by, created_name, updated_by, 
    updated_name
  </sql>
  <sql id="Blob_Column_List">
    task_info
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.cowell.iscm.entity.IscmZdtTaskExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from iscm_zdt_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.cowell.iscm.entity.IscmZdtTaskExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from iscm_zdt_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from iscm_zdt_task
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from iscm_zdt_task
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.iscm.entity.IscmZdtTaskExample">
    delete from iscm_zdt_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.iscm.entity.IscmZdtTask">
    insert into iscm_zdt_task (id, task_key, task_key_type, 
      task_key_type_desc, type_id, type_code, 
      task_name, task_mark, status, 
      gmt_create, gmt_update, extend, 
      version, created_by, created_name, 
      updated_by, updated_name, task_info
      )
    values (#{id,jdbcType=BIGINT}, #{taskKey,jdbcType=VARCHAR}, #{taskKeyType,jdbcType=TINYINT}, 
      #{taskKeyTypeDesc,jdbcType=VARCHAR}, #{typeId,jdbcType=BIGINT}, #{typeCode,jdbcType=VARCHAR}, 
      #{taskName,jdbcType=VARCHAR}, #{taskMark,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}, #{extend,jdbcType=VARCHAR}, 
      #{version,jdbcType=INTEGER}, #{createdBy,jdbcType=BIGINT}, #{createdName,jdbcType=VARCHAR}, 
      #{updatedBy,jdbcType=BIGINT}, #{updatedName,jdbcType=VARCHAR}, #{taskInfo,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.iscm.entity.IscmZdtTask">
    insert into iscm_zdt_task
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="taskKey != null">
        task_key,
      </if>
      <if test="taskKeyType != null">
        task_key_type,
      </if>
      <if test="taskKeyTypeDesc != null">
        task_key_type_desc,
      </if>
      <if test="typeId != null">
        type_id,
      </if>
      <if test="typeCode != null">
        type_code,
      </if>
      <if test="taskName != null">
        task_name,
      </if>
      <if test="taskMark != null">
        task_mark,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedName != null">
        updated_name,
      </if>
      <if test="taskInfo != null">
        task_info,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="taskKey != null">
        #{taskKey,jdbcType=VARCHAR},
      </if>
      <if test="taskKeyType != null">
        #{taskKeyType,jdbcType=TINYINT},
      </if>
      <if test="taskKeyTypeDesc != null">
        #{taskKeyTypeDesc,jdbcType=VARCHAR},
      </if>
      <if test="typeId != null">
        #{typeId,jdbcType=BIGINT},
      </if>
      <if test="typeCode != null">
        #{typeCode,jdbcType=VARCHAR},
      </if>
      <if test="taskName != null">
        #{taskName,jdbcType=VARCHAR},
      </if>
      <if test="taskMark != null">
        #{taskMark,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        #{updatedName,jdbcType=VARCHAR},
      </if>
      <if test="taskInfo != null">
        #{taskInfo,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.iscm.entity.IscmZdtTaskExample" resultType="java.lang.Long">
    select count(*) from iscm_zdt_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update iscm_zdt_task
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.taskKey != null">
        task_key = #{record.taskKey,jdbcType=VARCHAR},
      </if>
      <if test="record.taskKeyType != null">
        task_key_type = #{record.taskKeyType,jdbcType=TINYINT},
      </if>
      <if test="record.taskKeyTypeDesc != null">
        task_key_type_desc = #{record.taskKeyTypeDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.typeId != null">
        type_id = #{record.typeId,jdbcType=BIGINT},
      </if>
      <if test="record.typeCode != null">
        type_code = #{record.typeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.taskName != null">
        task_name = #{record.taskName,jdbcType=VARCHAR},
      </if>
      <if test="record.taskMark != null">
        task_mark = #{record.taskMark,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedName != null">
        updated_name = #{record.updatedName,jdbcType=VARCHAR},
      </if>
      <if test="record.taskInfo != null">
        task_info = #{record.taskInfo,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update iscm_zdt_task
    set id = #{record.id,jdbcType=BIGINT},
      task_key = #{record.taskKey,jdbcType=VARCHAR},
      task_key_type = #{record.taskKeyType,jdbcType=TINYINT},
      task_key_type_desc = #{record.taskKeyTypeDesc,jdbcType=VARCHAR},
      type_id = #{record.typeId,jdbcType=BIGINT},
      type_code = #{record.typeCode,jdbcType=VARCHAR},
      task_name = #{record.taskName,jdbcType=VARCHAR},
      task_mark = #{record.taskMark,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=TINYINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_name = #{record.createdName,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_name = #{record.updatedName,jdbcType=VARCHAR},
      task_info = #{record.taskInfo,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update iscm_zdt_task
    set id = #{record.id,jdbcType=BIGINT},
      task_key = #{record.taskKey,jdbcType=VARCHAR},
      task_key_type = #{record.taskKeyType,jdbcType=TINYINT},
      task_key_type_desc = #{record.taskKeyTypeDesc,jdbcType=VARCHAR},
      type_id = #{record.typeId,jdbcType=BIGINT},
      type_code = #{record.typeCode,jdbcType=VARCHAR},
      task_name = #{record.taskName,jdbcType=VARCHAR},
      task_mark = #{record.taskMark,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=TINYINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_name = #{record.createdName,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_name = #{record.updatedName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.iscm.entity.IscmZdtTask">
    update iscm_zdt_task
    <set>
      <if test="taskKey != null">
        task_key = #{taskKey,jdbcType=VARCHAR},
      </if>
      <if test="taskKeyType != null">
        task_key_type = #{taskKeyType,jdbcType=TINYINT},
      </if>
      <if test="taskKeyTypeDesc != null">
        task_key_type_desc = #{taskKeyTypeDesc,jdbcType=VARCHAR},
      </if>
      <if test="typeId != null">
        type_id = #{typeId,jdbcType=BIGINT},
      </if>
      <if test="typeCode != null">
        type_code = #{typeCode,jdbcType=VARCHAR},
      </if>
      <if test="taskName != null">
        task_name = #{taskName,jdbcType=VARCHAR},
      </if>
      <if test="taskMark != null">
        task_mark = #{taskMark,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        updated_name = #{updatedName,jdbcType=VARCHAR},
      </if>
      <if test="taskInfo != null">
        task_info = #{taskInfo,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.cowell.iscm.entity.IscmZdtTask">
    update iscm_zdt_task
    set task_key = #{taskKey,jdbcType=VARCHAR},
      task_key_type = #{taskKeyType,jdbcType=TINYINT},
      task_key_type_desc = #{taskKeyTypeDesc,jdbcType=VARCHAR},
      type_id = #{typeId,jdbcType=BIGINT},
      type_code = #{typeCode,jdbcType=VARCHAR},
      task_name = #{taskName,jdbcType=VARCHAR},
      task_mark = #{taskMark,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_name = #{createdName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_name = #{updatedName,jdbcType=VARCHAR},
      task_info = #{taskInfo,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.iscm.entity.IscmZdtTask">
    update iscm_zdt_task
    set task_key = #{taskKey,jdbcType=VARCHAR},
      task_key_type = #{taskKeyType,jdbcType=TINYINT},
      task_key_type_desc = #{taskKeyTypeDesc,jdbcType=VARCHAR},
      type_id = #{typeId,jdbcType=BIGINT},
      type_code = #{typeCode,jdbcType=VARCHAR},
      task_name = #{taskName,jdbcType=VARCHAR},
      task_mark = #{taskMark,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_name = #{createdName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_name = #{updatedName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>