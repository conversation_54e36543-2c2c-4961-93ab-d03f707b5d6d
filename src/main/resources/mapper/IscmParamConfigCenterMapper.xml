<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.IscmParamConfigCenterMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.IscmParamConfigCenter">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="param_code" jdbcType="VARCHAR" property="paramCode" />
    <result column="old_param_code" jdbcType="VARCHAR" property="oldParamCode" />
    <result column="param_name" jdbcType="VARCHAR" property="paramName" />
    <result column="param_desc" jdbcType="VARCHAR" property="paramDesc" />
    <result column="param_scope" jdbcType="TINYINT" property="paramScope" />
    <result column="param_level" jdbcType="INTEGER" property="paramLevel" />
    <result column="param_type" jdbcType="TINYINT" property="paramType" />
    <result column="param_source" jdbcType="TINYINT" property="paramSource" />
    <result column="param_sequence" jdbcType="INTEGER" property="paramSequence" />
    <result column="param_order" jdbcType="INTEGER" property="paramOrder" />
    <result column="param_sequence_desc" jdbcType="VARCHAR" property="paramSequenceDesc" />
    <result column="param_status" jdbcType="TINYINT" property="paramStatus" />
    <result column="param_default_value" jdbcType="VARCHAR" property="paramDefaultValue" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, param_code, old_param_code, param_name, param_desc, param_scope, param_level, 
    param_type, param_source, param_sequence, param_order, param_sequence_desc, param_status, 
    param_default_value, status, extend, version, created_by, created_name, updated_by, 
    updated_name, gmt_create, gmt_update
  </sql>
  <select id="selectByExample" parameterType="com.cowell.iscm.entity.IscmParamConfigCenterExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from iscm_param_config_center
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from iscm_param_config_center
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from iscm_param_config_center
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.iscm.entity.IscmParamConfigCenterExample">
    delete from iscm_param_config_center
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.iscm.entity.IscmParamConfigCenter">
    insert into iscm_param_config_center (id, param_code, old_param_code, 
      param_name, param_desc, param_scope, 
      param_level, param_type, param_source, 
      param_sequence, param_order, param_sequence_desc, 
      param_status, param_default_value, status, 
      extend, version, created_by, 
      created_name, updated_by, updated_name, 
      gmt_create, gmt_update)
    values (#{id,jdbcType=BIGINT}, #{paramCode,jdbcType=VARCHAR}, #{oldParamCode,jdbcType=VARCHAR}, 
      #{paramName,jdbcType=VARCHAR}, #{paramDesc,jdbcType=VARCHAR}, #{paramScope,jdbcType=TINYINT}, 
      #{paramLevel,jdbcType=INTEGER}, #{paramType,jdbcType=TINYINT}, #{paramSource,jdbcType=TINYINT}, 
      #{paramSequence,jdbcType=INTEGER}, #{paramOrder,jdbcType=INTEGER}, #{paramSequenceDesc,jdbcType=VARCHAR}, 
      #{paramStatus,jdbcType=TINYINT}, #{paramDefaultValue,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, 
      #{extend,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER}, #{createdBy,jdbcType=BIGINT}, 
      #{createdName,jdbcType=VARCHAR}, #{updatedBy,jdbcType=BIGINT}, #{updatedName,jdbcType=VARCHAR}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.iscm.entity.IscmParamConfigCenter">
    insert into iscm_param_config_center
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="paramCode != null">
        param_code,
      </if>
      <if test="oldParamCode != null">
        old_param_code,
      </if>
      <if test="paramName != null">
        param_name,
      </if>
      <if test="paramDesc != null">
        param_desc,
      </if>
      <if test="paramScope != null">
        param_scope,
      </if>
      <if test="paramLevel != null">
        param_level,
      </if>
      <if test="paramType != null">
        param_type,
      </if>
      <if test="paramSource != null">
        param_source,
      </if>
      <if test="paramSequence != null">
        param_sequence,
      </if>
      <if test="paramOrder != null">
        param_order,
      </if>
      <if test="paramSequenceDesc != null">
        param_sequence_desc,
      </if>
      <if test="paramStatus != null">
        param_status,
      </if>
      <if test="paramDefaultValue != null">
        param_default_value,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedName != null">
        updated_name,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="paramCode != null">
        #{paramCode,jdbcType=VARCHAR},
      </if>
      <if test="oldParamCode != null">
        #{oldParamCode,jdbcType=VARCHAR},
      </if>
      <if test="paramName != null">
        #{paramName,jdbcType=VARCHAR},
      </if>
      <if test="paramDesc != null">
        #{paramDesc,jdbcType=VARCHAR},
      </if>
      <if test="paramScope != null">
        #{paramScope,jdbcType=TINYINT},
      </if>
      <if test="paramLevel != null">
        #{paramLevel,jdbcType=INTEGER},
      </if>
      <if test="paramType != null">
        #{paramType,jdbcType=TINYINT},
      </if>
      <if test="paramSource != null">
        #{paramSource,jdbcType=TINYINT},
      </if>
      <if test="paramSequence != null">
        #{paramSequence,jdbcType=INTEGER},
      </if>
      <if test="paramOrder != null">
        #{paramOrder,jdbcType=INTEGER},
      </if>
      <if test="paramSequenceDesc != null">
        #{paramSequenceDesc,jdbcType=VARCHAR},
      </if>
      <if test="paramStatus != null">
        #{paramStatus,jdbcType=TINYINT},
      </if>
      <if test="paramDefaultValue != null">
        #{paramDefaultValue,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        #{updatedName,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.iscm.entity.IscmParamConfigCenterExample" resultType="java.lang.Long">
    select count(*) from iscm_param_config_center
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update iscm_param_config_center
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.paramCode != null">
        param_code = #{record.paramCode,jdbcType=VARCHAR},
      </if>
      <if test="record.oldParamCode != null">
        old_param_code = #{record.oldParamCode,jdbcType=VARCHAR},
      </if>
      <if test="record.paramName != null">
        param_name = #{record.paramName,jdbcType=VARCHAR},
      </if>
      <if test="record.paramDesc != null">
        param_desc = #{record.paramDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.paramScope != null">
        param_scope = #{record.paramScope,jdbcType=TINYINT},
      </if>
      <if test="record.paramLevel != null">
        param_level = #{record.paramLevel,jdbcType=INTEGER},
      </if>
      <if test="record.paramType != null">
        param_type = #{record.paramType,jdbcType=TINYINT},
      </if>
      <if test="record.paramSource != null">
        param_source = #{record.paramSource,jdbcType=TINYINT},
      </if>
      <if test="record.paramSequence != null">
        param_sequence = #{record.paramSequence,jdbcType=INTEGER},
      </if>
      <if test="record.paramOrder != null">
        param_order = #{record.paramOrder,jdbcType=INTEGER},
      </if>
      <if test="record.paramSequenceDesc != null">
        param_sequence_desc = #{record.paramSequenceDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.paramStatus != null">
        param_status = #{record.paramStatus,jdbcType=TINYINT},
      </if>
      <if test="record.paramDefaultValue != null">
        param_default_value = #{record.paramDefaultValue,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedName != null">
        updated_name = #{record.updatedName,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update iscm_param_config_center
    set id = #{record.id,jdbcType=BIGINT},
      param_code = #{record.paramCode,jdbcType=VARCHAR},
      old_param_code = #{record.oldParamCode,jdbcType=VARCHAR},
      param_name = #{record.paramName,jdbcType=VARCHAR},
      param_desc = #{record.paramDesc,jdbcType=VARCHAR},
      param_scope = #{record.paramScope,jdbcType=TINYINT},
      param_level = #{record.paramLevel,jdbcType=INTEGER},
      param_type = #{record.paramType,jdbcType=TINYINT},
      param_source = #{record.paramSource,jdbcType=TINYINT},
      param_sequence = #{record.paramSequence,jdbcType=INTEGER},
      param_order = #{record.paramOrder,jdbcType=INTEGER},
      param_sequence_desc = #{record.paramSequenceDesc,jdbcType=VARCHAR},
      param_status = #{record.paramStatus,jdbcType=TINYINT},
      param_default_value = #{record.paramDefaultValue,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=TINYINT},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_name = #{record.createdName,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_name = #{record.updatedName,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.iscm.entity.IscmParamConfigCenter">
    update iscm_param_config_center
    <set>
      <if test="paramCode != null">
        param_code = #{paramCode,jdbcType=VARCHAR},
      </if>
      <if test="oldParamCode != null">
        old_param_code = #{oldParamCode,jdbcType=VARCHAR},
      </if>
      <if test="paramName != null">
        param_name = #{paramName,jdbcType=VARCHAR},
      </if>
      <if test="paramDesc != null">
        param_desc = #{paramDesc,jdbcType=VARCHAR},
      </if>
      <if test="paramScope != null">
        param_scope = #{paramScope,jdbcType=TINYINT},
      </if>
      <if test="paramLevel != null">
        param_level = #{paramLevel,jdbcType=INTEGER},
      </if>
      <if test="paramType != null">
        param_type = #{paramType,jdbcType=TINYINT},
      </if>
      <if test="paramSource != null">
        param_source = #{paramSource,jdbcType=TINYINT},
      </if>
      <if test="paramSequence != null">
        param_sequence = #{paramSequence,jdbcType=INTEGER},
      </if>
      <if test="paramOrder != null">
        param_order = #{paramOrder,jdbcType=INTEGER},
      </if>
      <if test="paramSequenceDesc != null">
        param_sequence_desc = #{paramSequenceDesc,jdbcType=VARCHAR},
      </if>
      <if test="paramStatus != null">
        param_status = #{paramStatus,jdbcType=TINYINT},
      </if>
      <if test="paramDefaultValue != null">
        param_default_value = #{paramDefaultValue,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        updated_name = #{updatedName,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.iscm.entity.IscmParamConfigCenter">
    update iscm_param_config_center
    set param_code = #{paramCode,jdbcType=VARCHAR},
      old_param_code = #{oldParamCode,jdbcType=VARCHAR},
      param_name = #{paramName,jdbcType=VARCHAR},
      param_desc = #{paramDesc,jdbcType=VARCHAR},
      param_scope = #{paramScope,jdbcType=TINYINT},
      param_level = #{paramLevel,jdbcType=INTEGER},
      param_type = #{paramType,jdbcType=TINYINT},
      param_source = #{paramSource,jdbcType=TINYINT},
      param_sequence = #{paramSequence,jdbcType=INTEGER},
      param_order = #{paramOrder,jdbcType=INTEGER},
      param_sequence_desc = #{paramSequenceDesc,jdbcType=VARCHAR},
      param_status = #{paramStatus,jdbcType=TINYINT},
      param_default_value = #{paramDefaultValue,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_name = #{createdName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_name = #{updatedName,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>