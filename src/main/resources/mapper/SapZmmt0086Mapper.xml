<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.SapZmmt0086Mapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.SapZmmt0086">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="MANDT" jdbcType="VARCHAR" property="mandt" />
    <result column="PURREQNO" jdbcType="VARCHAR" property="purreqno" />
    <result column="STORELINENO" jdbcType="VARCHAR" property="storelineno" />
    <result column="MATNR" jdbcType="VARCHAR" property="matnr" />
    <result column="MENGE" jdbcType="DECIMAL" property="menge" />
    <result column="MEINS" jdbcType="VARCHAR" property="meins" />
    <result column="SERIAL" jdbcType="VARCHAR" property="serial" />
    <result column="CHARG" jdbcType="VARCHAR" property="charg" />
    <result column="RETREASON2" jdbcType="VARCHAR" property="retreason2" />
    <result column="ZNETPR" jdbcType="DECIMAL" property="znetpr" />
    <result column="ZZCXBJS" jdbcType="VARCHAR" property="zzcxbjs" />
    <result column="RETPO" jdbcType="VARCHAR" property="retpo" />
    <result column="UMSON" jdbcType="VARCHAR" property="umson" />
    <result column="MENGE2" jdbcType="DECIMAL" property="menge2" />
    <result column="RESLO" jdbcType="VARCHAR" property="reslo" />
    <result column="ZDCQTY" jdbcType="DECIMAL" property="zdcqty" />
    <result column="ZCKCXB1" jdbcType="VARCHAR" property="zckcxb1" />
    <result column="ZCKCXB2" jdbcType="VARCHAR" property="zckcxb2" />
    <result column="ZYDYXL1" jdbcType="DECIMAL" property="zydyxl1" />
    <result column="ZRKCXB2" jdbcType="VARCHAR" property="zrkcxb2" />
    <result column="ZYDYXL2" jdbcType="DECIMAL" property="zydyxl2" />
    <result column="BRTWR" jdbcType="DECIMAL" property="brtwr" />
    <result column="KPEIN2" jdbcType="DECIMAL" property="kpein2" />
    <result column="ZYPURREQNO" jdbcType="VARCHAR" property="zypurreqno" />
    <result column="ZYSTORELINENO" jdbcType="VARCHAR" property="zystorelineno" />
    <result column="ZSFCF" jdbcType="VARCHAR" property="zsfcf" />
    <result column="ZMAIN" jdbcType="VARCHAR" property="zmain" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, MANDT, PURREQNO, STORELINENO, MATNR, MENGE, MEINS, SERIAL, CHARG, RETREASON2, 
    ZNETPR, ZZCXBJS, RETPO, UMSON, MENGE2, RESLO, ZDCQTY, ZCKCXB1, ZCKCXB2, ZYDYXL1, 
    ZRKCXB2, ZYDYXL2, BRTWR, KPEIN2, ZYPURREQNO, ZYSTORELINENO, ZSFCF, ZMAIN
  </sql>
  <select id="selectByExample" parameterType="com.cowell.iscm.entity.SapZmmt0086Example" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from SAP_ZMMT0086
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from SAP_ZMMT0086
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from SAP_ZMMT0086
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.iscm.entity.SapZmmt0086Example">
    delete from SAP_ZMMT0086
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entity.SapZmmt0086" useGeneratedKeys="true">
    insert into SAP_ZMMT0086 (MANDT, PURREQNO, STORELINENO, 
      MATNR, MENGE, MEINS, 
      SERIAL, CHARG, RETREASON2, 
      ZNETPR, ZZCXBJS, RETPO, 
      UMSON, MENGE2, RESLO, 
      ZDCQTY, ZCKCXB1, ZCKCXB2, 
      ZYDYXL1, ZRKCXB2, ZYDYXL2, 
      BRTWR, KPEIN2, ZYPURREQNO, 
      ZYSTORELINENO, ZSFCF, ZMAIN
      )
    values (#{mandt,jdbcType=VARCHAR}, #{purreqno,jdbcType=VARCHAR}, #{storelineno,jdbcType=VARCHAR}, 
      #{matnr,jdbcType=VARCHAR}, #{menge,jdbcType=DECIMAL}, #{meins,jdbcType=VARCHAR}, 
      #{serial,jdbcType=VARCHAR}, #{charg,jdbcType=VARCHAR}, #{retreason2,jdbcType=VARCHAR}, 
      #{znetpr,jdbcType=DECIMAL}, #{zzcxbjs,jdbcType=VARCHAR}, #{retpo,jdbcType=VARCHAR}, 
      #{umson,jdbcType=VARCHAR}, #{menge2,jdbcType=DECIMAL}, #{reslo,jdbcType=VARCHAR}, 
      #{zdcqty,jdbcType=DECIMAL}, #{zckcxb1,jdbcType=VARCHAR}, #{zckcxb2,jdbcType=VARCHAR}, 
      #{zydyxl1,jdbcType=DECIMAL}, #{zrkcxb2,jdbcType=VARCHAR}, #{zydyxl2,jdbcType=DECIMAL}, 
      #{brtwr,jdbcType=DECIMAL}, #{kpein2,jdbcType=DECIMAL}, #{zypurreqno,jdbcType=VARCHAR}, 
      #{zystorelineno,jdbcType=VARCHAR}, #{zsfcf,jdbcType=VARCHAR}, #{zmain,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entity.SapZmmt0086" useGeneratedKeys="true">
    insert into SAP_ZMMT0086
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="mandt != null">
        MANDT,
      </if>
      <if test="purreqno != null">
        PURREQNO,
      </if>
      <if test="storelineno != null">
        STORELINENO,
      </if>
      <if test="matnr != null">
        MATNR,
      </if>
      <if test="menge != null">
        MENGE,
      </if>
      <if test="meins != null">
        MEINS,
      </if>
      <if test="serial != null">
        SERIAL,
      </if>
      <if test="charg != null">
        CHARG,
      </if>
      <if test="retreason2 != null">
        RETREASON2,
      </if>
      <if test="znetpr != null">
        ZNETPR,
      </if>
      <if test="zzcxbjs != null">
        ZZCXBJS,
      </if>
      <if test="retpo != null">
        RETPO,
      </if>
      <if test="umson != null">
        UMSON,
      </if>
      <if test="menge2 != null">
        MENGE2,
      </if>
      <if test="reslo != null">
        RESLO,
      </if>
      <if test="zdcqty != null">
        ZDCQTY,
      </if>
      <if test="zckcxb1 != null">
        ZCKCXB1,
      </if>
      <if test="zckcxb2 != null">
        ZCKCXB2,
      </if>
      <if test="zydyxl1 != null">
        ZYDYXL1,
      </if>
      <if test="zrkcxb2 != null">
        ZRKCXB2,
      </if>
      <if test="zydyxl2 != null">
        ZYDYXL2,
      </if>
      <if test="brtwr != null">
        BRTWR,
      </if>
      <if test="kpein2 != null">
        KPEIN2,
      </if>
      <if test="zypurreqno != null">
        ZYPURREQNO,
      </if>
      <if test="zystorelineno != null">
        ZYSTORELINENO,
      </if>
      <if test="zsfcf != null">
        ZSFCF,
      </if>
      <if test="zmain != null">
        ZMAIN,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="mandt != null">
        #{mandt,jdbcType=VARCHAR},
      </if>
      <if test="purreqno != null">
        #{purreqno,jdbcType=VARCHAR},
      </if>
      <if test="storelineno != null">
        #{storelineno,jdbcType=VARCHAR},
      </if>
      <if test="matnr != null">
        #{matnr,jdbcType=VARCHAR},
      </if>
      <if test="menge != null">
        #{menge,jdbcType=DECIMAL},
      </if>
      <if test="meins != null">
        #{meins,jdbcType=VARCHAR},
      </if>
      <if test="serial != null">
        #{serial,jdbcType=VARCHAR},
      </if>
      <if test="charg != null">
        #{charg,jdbcType=VARCHAR},
      </if>
      <if test="retreason2 != null">
        #{retreason2,jdbcType=VARCHAR},
      </if>
      <if test="znetpr != null">
        #{znetpr,jdbcType=DECIMAL},
      </if>
      <if test="zzcxbjs != null">
        #{zzcxbjs,jdbcType=VARCHAR},
      </if>
      <if test="retpo != null">
        #{retpo,jdbcType=VARCHAR},
      </if>
      <if test="umson != null">
        #{umson,jdbcType=VARCHAR},
      </if>
      <if test="menge2 != null">
        #{menge2,jdbcType=DECIMAL},
      </if>
      <if test="reslo != null">
        #{reslo,jdbcType=VARCHAR},
      </if>
      <if test="zdcqty != null">
        #{zdcqty,jdbcType=DECIMAL},
      </if>
      <if test="zckcxb1 != null">
        #{zckcxb1,jdbcType=VARCHAR},
      </if>
      <if test="zckcxb2 != null">
        #{zckcxb2,jdbcType=VARCHAR},
      </if>
      <if test="zydyxl1 != null">
        #{zydyxl1,jdbcType=DECIMAL},
      </if>
      <if test="zrkcxb2 != null">
        #{zrkcxb2,jdbcType=VARCHAR},
      </if>
      <if test="zydyxl2 != null">
        #{zydyxl2,jdbcType=DECIMAL},
      </if>
      <if test="brtwr != null">
        #{brtwr,jdbcType=DECIMAL},
      </if>
      <if test="kpein2 != null">
        #{kpein2,jdbcType=DECIMAL},
      </if>
      <if test="zypurreqno != null">
        #{zypurreqno,jdbcType=VARCHAR},
      </if>
      <if test="zystorelineno != null">
        #{zystorelineno,jdbcType=VARCHAR},
      </if>
      <if test="zsfcf != null">
        #{zsfcf,jdbcType=VARCHAR},
      </if>
      <if test="zmain != null">
        #{zmain,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.iscm.entity.SapZmmt0086Example" resultType="java.lang.Long">
    select count(*) from SAP_ZMMT0086
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update SAP_ZMMT0086
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.mandt != null">
        MANDT = #{record.mandt,jdbcType=VARCHAR},
      </if>
      <if test="record.purreqno != null">
        PURREQNO = #{record.purreqno,jdbcType=VARCHAR},
      </if>
      <if test="record.storelineno != null">
        STORELINENO = #{record.storelineno,jdbcType=VARCHAR},
      </if>
      <if test="record.matnr != null">
        MATNR = #{record.matnr,jdbcType=VARCHAR},
      </if>
      <if test="record.menge != null">
        MENGE = #{record.menge,jdbcType=DECIMAL},
      </if>
      <if test="record.meins != null">
        MEINS = #{record.meins,jdbcType=VARCHAR},
      </if>
      <if test="record.serial != null">
        SERIAL = #{record.serial,jdbcType=VARCHAR},
      </if>
      <if test="record.charg != null">
        CHARG = #{record.charg,jdbcType=VARCHAR},
      </if>
      <if test="record.retreason2 != null">
        RETREASON2 = #{record.retreason2,jdbcType=VARCHAR},
      </if>
      <if test="record.znetpr != null">
        ZNETPR = #{record.znetpr,jdbcType=DECIMAL},
      </if>
      <if test="record.zzcxbjs != null">
        ZZCXBJS = #{record.zzcxbjs,jdbcType=VARCHAR},
      </if>
      <if test="record.retpo != null">
        RETPO = #{record.retpo,jdbcType=VARCHAR},
      </if>
      <if test="record.umson != null">
        UMSON = #{record.umson,jdbcType=VARCHAR},
      </if>
      <if test="record.menge2 != null">
        MENGE2 = #{record.menge2,jdbcType=DECIMAL},
      </if>
      <if test="record.reslo != null">
        RESLO = #{record.reslo,jdbcType=VARCHAR},
      </if>
      <if test="record.zdcqty != null">
        ZDCQTY = #{record.zdcqty,jdbcType=DECIMAL},
      </if>
      <if test="record.zckcxb1 != null">
        ZCKCXB1 = #{record.zckcxb1,jdbcType=VARCHAR},
      </if>
      <if test="record.zckcxb2 != null">
        ZCKCXB2 = #{record.zckcxb2,jdbcType=VARCHAR},
      </if>
      <if test="record.zydyxl1 != null">
        ZYDYXL1 = #{record.zydyxl1,jdbcType=DECIMAL},
      </if>
      <if test="record.zrkcxb2 != null">
        ZRKCXB2 = #{record.zrkcxb2,jdbcType=VARCHAR},
      </if>
      <if test="record.zydyxl2 != null">
        ZYDYXL2 = #{record.zydyxl2,jdbcType=DECIMAL},
      </if>
      <if test="record.brtwr != null">
        BRTWR = #{record.brtwr,jdbcType=DECIMAL},
      </if>
      <if test="record.kpein2 != null">
        KPEIN2 = #{record.kpein2,jdbcType=DECIMAL},
      </if>
      <if test="record.zypurreqno != null">
        ZYPURREQNO = #{record.zypurreqno,jdbcType=VARCHAR},
      </if>
      <if test="record.zystorelineno != null">
        ZYSTORELINENO = #{record.zystorelineno,jdbcType=VARCHAR},
      </if>
      <if test="record.zsfcf != null">
        ZSFCF = #{record.zsfcf,jdbcType=VARCHAR},
      </if>
      <if test="record.zmain != null">
        ZMAIN = #{record.zmain,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update SAP_ZMMT0086
    set id = #{record.id,jdbcType=BIGINT},
      MANDT = #{record.mandt,jdbcType=VARCHAR},
      PURREQNO = #{record.purreqno,jdbcType=VARCHAR},
      STORELINENO = #{record.storelineno,jdbcType=VARCHAR},
      MATNR = #{record.matnr,jdbcType=VARCHAR},
      MENGE = #{record.menge,jdbcType=DECIMAL},
      MEINS = #{record.meins,jdbcType=VARCHAR},
      SERIAL = #{record.serial,jdbcType=VARCHAR},
      CHARG = #{record.charg,jdbcType=VARCHAR},
      RETREASON2 = #{record.retreason2,jdbcType=VARCHAR},
      ZNETPR = #{record.znetpr,jdbcType=DECIMAL},
      ZZCXBJS = #{record.zzcxbjs,jdbcType=VARCHAR},
      RETPO = #{record.retpo,jdbcType=VARCHAR},
      UMSON = #{record.umson,jdbcType=VARCHAR},
      MENGE2 = #{record.menge2,jdbcType=DECIMAL},
      RESLO = #{record.reslo,jdbcType=VARCHAR},
      ZDCQTY = #{record.zdcqty,jdbcType=DECIMAL},
      ZCKCXB1 = #{record.zckcxb1,jdbcType=VARCHAR},
      ZCKCXB2 = #{record.zckcxb2,jdbcType=VARCHAR},
      ZYDYXL1 = #{record.zydyxl1,jdbcType=DECIMAL},
      ZRKCXB2 = #{record.zrkcxb2,jdbcType=VARCHAR},
      ZYDYXL2 = #{record.zydyxl2,jdbcType=DECIMAL},
      BRTWR = #{record.brtwr,jdbcType=DECIMAL},
      KPEIN2 = #{record.kpein2,jdbcType=DECIMAL},
      ZYPURREQNO = #{record.zypurreqno,jdbcType=VARCHAR},
      ZYSTORELINENO = #{record.zystorelineno,jdbcType=VARCHAR},
      ZSFCF = #{record.zsfcf,jdbcType=VARCHAR},
      ZMAIN = #{record.zmain,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.iscm.entity.SapZmmt0086">
    update SAP_ZMMT0086
    <set>
      <if test="mandt != null">
        MANDT = #{mandt,jdbcType=VARCHAR},
      </if>
      <if test="purreqno != null">
        PURREQNO = #{purreqno,jdbcType=VARCHAR},
      </if>
      <if test="storelineno != null">
        STORELINENO = #{storelineno,jdbcType=VARCHAR},
      </if>
      <if test="matnr != null">
        MATNR = #{matnr,jdbcType=VARCHAR},
      </if>
      <if test="menge != null">
        MENGE = #{menge,jdbcType=DECIMAL},
      </if>
      <if test="meins != null">
        MEINS = #{meins,jdbcType=VARCHAR},
      </if>
      <if test="serial != null">
        SERIAL = #{serial,jdbcType=VARCHAR},
      </if>
      <if test="charg != null">
        CHARG = #{charg,jdbcType=VARCHAR},
      </if>
      <if test="retreason2 != null">
        RETREASON2 = #{retreason2,jdbcType=VARCHAR},
      </if>
      <if test="znetpr != null">
        ZNETPR = #{znetpr,jdbcType=DECIMAL},
      </if>
      <if test="zzcxbjs != null">
        ZZCXBJS = #{zzcxbjs,jdbcType=VARCHAR},
      </if>
      <if test="retpo != null">
        RETPO = #{retpo,jdbcType=VARCHAR},
      </if>
      <if test="umson != null">
        UMSON = #{umson,jdbcType=VARCHAR},
      </if>
      <if test="menge2 != null">
        MENGE2 = #{menge2,jdbcType=DECIMAL},
      </if>
      <if test="reslo != null">
        RESLO = #{reslo,jdbcType=VARCHAR},
      </if>
      <if test="zdcqty != null">
        ZDCQTY = #{zdcqty,jdbcType=DECIMAL},
      </if>
      <if test="zckcxb1 != null">
        ZCKCXB1 = #{zckcxb1,jdbcType=VARCHAR},
      </if>
      <if test="zckcxb2 != null">
        ZCKCXB2 = #{zckcxb2,jdbcType=VARCHAR},
      </if>
      <if test="zydyxl1 != null">
        ZYDYXL1 = #{zydyxl1,jdbcType=DECIMAL},
      </if>
      <if test="zrkcxb2 != null">
        ZRKCXB2 = #{zrkcxb2,jdbcType=VARCHAR},
      </if>
      <if test="zydyxl2 != null">
        ZYDYXL2 = #{zydyxl2,jdbcType=DECIMAL},
      </if>
      <if test="brtwr != null">
        BRTWR = #{brtwr,jdbcType=DECIMAL},
      </if>
      <if test="kpein2 != null">
        KPEIN2 = #{kpein2,jdbcType=DECIMAL},
      </if>
      <if test="zypurreqno != null">
        ZYPURREQNO = #{zypurreqno,jdbcType=VARCHAR},
      </if>
      <if test="zystorelineno != null">
        ZYSTORELINENO = #{zystorelineno,jdbcType=VARCHAR},
      </if>
      <if test="zsfcf != null">
        ZSFCF = #{zsfcf,jdbcType=VARCHAR},
      </if>
      <if test="zmain != null">
        ZMAIN = #{zmain,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.iscm.entity.SapZmmt0086">
    update SAP_ZMMT0086
    set MANDT = #{mandt,jdbcType=VARCHAR},
      PURREQNO = #{purreqno,jdbcType=VARCHAR},
      STORELINENO = #{storelineno,jdbcType=VARCHAR},
      MATNR = #{matnr,jdbcType=VARCHAR},
      MENGE = #{menge,jdbcType=DECIMAL},
      MEINS = #{meins,jdbcType=VARCHAR},
      SERIAL = #{serial,jdbcType=VARCHAR},
      CHARG = #{charg,jdbcType=VARCHAR},
      RETREASON2 = #{retreason2,jdbcType=VARCHAR},
      ZNETPR = #{znetpr,jdbcType=DECIMAL},
      ZZCXBJS = #{zzcxbjs,jdbcType=VARCHAR},
      RETPO = #{retpo,jdbcType=VARCHAR},
      UMSON = #{umson,jdbcType=VARCHAR},
      MENGE2 = #{menge2,jdbcType=DECIMAL},
      RESLO = #{reslo,jdbcType=VARCHAR},
      ZDCQTY = #{zdcqty,jdbcType=DECIMAL},
      ZCKCXB1 = #{zckcxb1,jdbcType=VARCHAR},
      ZCKCXB2 = #{zckcxb2,jdbcType=VARCHAR},
      ZYDYXL1 = #{zydyxl1,jdbcType=DECIMAL},
      ZRKCXB2 = #{zrkcxb2,jdbcType=VARCHAR},
      ZYDYXL2 = #{zydyxl2,jdbcType=DECIMAL},
      BRTWR = #{brtwr,jdbcType=DECIMAL},
      KPEIN2 = #{kpein2,jdbcType=DECIMAL},
      ZYPURREQNO = #{zypurreqno,jdbcType=VARCHAR},
      ZYSTORELINENO = #{zystorelineno,jdbcType=VARCHAR},
      ZSFCF = #{zsfcf,jdbcType=VARCHAR},
      ZMAIN = #{zmain,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>