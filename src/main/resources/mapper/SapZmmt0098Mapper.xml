<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.SapZmmt0098Mapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.SapZmmt0098">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="MANDT" jdbcType="VARCHAR" property="mandt" />
    <result column="WERKS" jdbcType="VARCHAR" property="werks" />
    <result column="MATNR" jdbcType="VARCHAR" property="matnr" />
    <result column="MATNR_DESC" jdbcType="VARCHAR" property="matnrDesc" />
    <result column="ZISAPRAS" jdbcType="VARCHAR" property="zisapras" />
    <result column="ZCGYGH" jdbcType="VARCHAR" property="zcgygh" />
    <result column="ZPURAGT" jdbcType="VARCHAR" property="zpuragt" />
    <result column="ZXSJWXS" jdbcType="DECIMAL" property="zxsjwxs" />
    <result column="ZXXJWXS" jdbcType="DECIMAL" property="zxxjwxs" />
    <result column="ZSPQZQ" jdbcType="DECIMAL" property="zspqzq" />
    <result column="ZSPQZQQSRQ" jdbcType="VARCHAR" property="zspqzqqsrq" />
    <result column="ZSPQZQZZRQ" jdbcType="VARCHAR" property="zspqzqzzrq" />
    <result column="ZBCKCTS" jdbcType="DECIMAL" property="zbckcts" />
    <result column="ZBCKCTSQSRQ" jdbcType="VARCHAR" property="zbckctsqsrq" />
    <result column="ZBCKCTSZZRQ" jdbcType="VARCHAR" property="zbckctszzrq" />
    <result column="ZBCDHL" jdbcType="DECIMAL" property="zbcdhl" />
    <result column="ZBCDHLQSRQ" jdbcType="VARCHAR" property="zbcdhlqsrq" />
    <result column="ZBCDHLZZRQ" jdbcType="VARCHAR" property="zbcdhlzzrq" />
    <result column="ZDYZD1" jdbcType="VARCHAR" property="zdyzd1" />
    <result column="ZDYZD2" jdbcType="VARCHAR" property="zdyzd2" />
    <result column="ZDYZD3" jdbcType="VARCHAR" property="zdyzd3" />
    <result column="ZDYZD4" jdbcType="VARCHAR" property="zdyzd4" />
    <result column="ZDYZD5" jdbcType="VARCHAR" property="zdyzd5" />
    <result column="ZTHFS" jdbcType="VARCHAR" property="zthfs" />
    <result column="ZCGBZSZ" jdbcType="VARCHAR" property="zcgbzsz" />
    <result column="ZZCGSX_PF" jdbcType="VARCHAR" property="zzcgsxPf" />
    <result column="ZBCSX_PF" jdbcType="VARCHAR" property="zbcsxPf" />
    <result column="ZQZQ_PF" jdbcType="INTEGER" property="zqzqPf" />
    <result column="ZJGQ_PF" jdbcType="INTEGER" property="zjgqPf" />
    <result column="ZAQKCTS_PF" jdbcType="INTEGER" property="zaqkctsPf" />
    <result column="ZBCDHL_PF" jdbcType="DECIMAL" property="zbcdhlPf" />
    <result column="ZBCKCTS_PF" jdbcType="INTEGER" property="zbckctsPf" />
    <result column="ZQSRQ_PF" jdbcType="VARCHAR" property="zqsrqPf" />
    <result column="ZZZRQ_PF" jdbcType="VARCHAR" property="zzzrqPf" />
    <result column="CPUDT" jdbcType="VARCHAR" property="cpudt" />
    <result column="CPUTM" jdbcType="VARCHAR" property="cputm" />
    <result column="ANNAM" jdbcType="VARCHAR" property="annam" />
    <result column="AEDAT" jdbcType="VARCHAR" property="aedat" />
    <result column="AEZET" jdbcType="VARCHAR" property="aezet" />
    <result column="AENAM" jdbcType="VARCHAR" property="aenam" />
    <result column="ZZHXS" jdbcType="VARCHAR" property="zzhxs" />
    <result column="ZSHCK" jdbcType="VARCHAR" property="zshck" />
    <result column="ZSFJS" jdbcType="VARCHAR" property="zsfjs" />
    <result column="ZXQHBS" jdbcType="DECIMAL" property="zxqhbs" />
    <result column="ZAQKCBCTS" jdbcType="DECIMAL" property="zaqkcbcts" />
    <result column="ZSFQZ" jdbcType="VARCHAR" property="zsfqz" />
    <result column="ZTSXSQSRQ" jdbcType="VARCHAR" property="ztsxsqsrq" />
    <result column="ZTSXSJSRQ" jdbcType="VARCHAR" property="ztsxsjsrq" />
    <result column="ZTSXSQZZB" jdbcType="DECIMAL" property="ztsxsqzzb" />
    <result column="ZSPCKDDLX" jdbcType="VARCHAR" property="zspckddlx" />
    <result column="ZGHDC" jdbcType="VARCHAR" property="zghdc" />
    <result column="ZKCSX" jdbcType="DECIMAL" property="zkcsx" />
    <result column="ZKCXX" jdbcType="DECIMAL" property="zkcxx" />
    <result column="ZZCQTY" jdbcType="DECIMAL" property="zzcqty" />
    <result column="ZZBZL" jdbcType="DECIMAL" property="zzbzl" />
    <result column="ZCGTPJL" jdbcType="VARCHAR" property="zcgtpjl" />
    <result column="ZCGTPJLXM" jdbcType="VARCHAR" property="zcgtpjlxm" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, MANDT, WERKS, MATNR, MATNR_DESC, ZISAPRAS, ZCGYGH, ZPURAGT, ZXSJWXS, ZXXJWXS, 
    ZSPQZQ, ZSPQZQQSRQ, ZSPQZQZZRQ, ZBCKCTS, ZBCKCTSQSRQ, ZBCKCTSZZRQ, ZBCDHL, ZBCDHLQSRQ, 
    ZBCDHLZZRQ, ZDYZD1, ZDYZD2, ZDYZD3, ZDYZD4, ZDYZD5, ZTHFS, ZCGBZSZ, ZZCGSX_PF, ZBCSX_PF, 
    ZQZQ_PF, ZJGQ_PF, ZAQKCTS_PF, ZBCDHL_PF, ZBCKCTS_PF, ZQSRQ_PF, ZZZRQ_PF, CPUDT, CPUTM, 
    ANNAM, AEDAT, AEZET, AENAM, ZZHXS, ZSHCK, ZSFJS, ZXQHBS, ZAQKCBCTS, ZSFQZ, ZTSXSQSRQ, 
    ZTSXSJSRQ, ZTSXSQZZB, ZSPCKDDLX, ZGHDC, ZKCSX, ZKCXX, ZZCQTY, ZZBZL, ZCGTPJL, ZCGTPJLXM
  </sql>
  <select id="selectByExample" parameterType="com.cowell.iscm.entity.SapZmmt0098Example" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from SAP_ZMMT0098
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from SAP_ZMMT0098
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from SAP_ZMMT0098
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.iscm.entity.SapZmmt0098Example">
    delete from SAP_ZMMT0098
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entity.SapZmmt0098" useGeneratedKeys="true">
    insert into SAP_ZMMT0098 (MANDT, WERKS, MATNR, 
      MATNR_DESC, ZISAPRAS, ZCGYGH, 
      ZPURAGT, ZXSJWXS, ZXXJWXS, 
      ZSPQZQ, ZSPQZQQSRQ, ZSPQZQZZRQ, 
      ZBCKCTS, ZBCKCTSQSRQ, ZBCKCTSZZRQ, 
      ZBCDHL, ZBCDHLQSRQ, ZBCDHLZZRQ, 
      ZDYZD1, ZDYZD2, ZDYZD3, 
      ZDYZD4, ZDYZD5, ZTHFS, 
      ZCGBZSZ, ZZCGSX_PF, ZBCSX_PF, 
      ZQZQ_PF, ZJGQ_PF, ZAQKCTS_PF, 
      ZBCDHL_PF, ZBCKCTS_PF, ZQSRQ_PF, 
      ZZZRQ_PF, CPUDT, CPUTM, 
      ANNAM, AEDAT, AEZET, 
      AENAM, ZZHXS, ZSHCK, 
      ZSFJS, ZXQHBS, ZAQKCBCTS, 
      ZSFQZ, ZTSXSQSRQ, ZTSXSJSRQ, 
      ZTSXSQZZB, ZSPCKDDLX, ZGHDC, 
      ZKCSX, ZKCXX, ZZCQTY, 
      ZZBZL, ZCGTPJL, ZCGTPJLXM
      )
    values (#{mandt,jdbcType=VARCHAR}, #{werks,jdbcType=VARCHAR}, #{matnr,jdbcType=VARCHAR}, 
      #{matnrDesc,jdbcType=VARCHAR}, #{zisapras,jdbcType=VARCHAR}, #{zcgygh,jdbcType=VARCHAR}, 
      #{zpuragt,jdbcType=VARCHAR}, #{zxsjwxs,jdbcType=DECIMAL}, #{zxxjwxs,jdbcType=DECIMAL}, 
      #{zspqzq,jdbcType=DECIMAL}, #{zspqzqqsrq,jdbcType=VARCHAR}, #{zspqzqzzrq,jdbcType=VARCHAR}, 
      #{zbckcts,jdbcType=DECIMAL}, #{zbckctsqsrq,jdbcType=VARCHAR}, #{zbckctszzrq,jdbcType=VARCHAR}, 
      #{zbcdhl,jdbcType=DECIMAL}, #{zbcdhlqsrq,jdbcType=VARCHAR}, #{zbcdhlzzrq,jdbcType=VARCHAR}, 
      #{zdyzd1,jdbcType=VARCHAR}, #{zdyzd2,jdbcType=VARCHAR}, #{zdyzd3,jdbcType=VARCHAR}, 
      #{zdyzd4,jdbcType=VARCHAR}, #{zdyzd5,jdbcType=VARCHAR}, #{zthfs,jdbcType=VARCHAR}, 
      #{zcgbzsz,jdbcType=VARCHAR}, #{zzcgsxPf,jdbcType=VARCHAR}, #{zbcsxPf,jdbcType=VARCHAR}, 
      #{zqzqPf,jdbcType=INTEGER}, #{zjgqPf,jdbcType=INTEGER}, #{zaqkctsPf,jdbcType=INTEGER}, 
      #{zbcdhlPf,jdbcType=DECIMAL}, #{zbckctsPf,jdbcType=INTEGER}, #{zqsrqPf,jdbcType=VARCHAR}, 
      #{zzzrqPf,jdbcType=VARCHAR}, #{cpudt,jdbcType=VARCHAR}, #{cputm,jdbcType=VARCHAR}, 
      #{annam,jdbcType=VARCHAR}, #{aedat,jdbcType=VARCHAR}, #{aezet,jdbcType=VARCHAR}, 
      #{aenam,jdbcType=VARCHAR}, #{zzhxs,jdbcType=VARCHAR}, #{zshck,jdbcType=VARCHAR}, 
      #{zsfjs,jdbcType=VARCHAR}, #{zxqhbs,jdbcType=DECIMAL}, #{zaqkcbcts,jdbcType=DECIMAL}, 
      #{zsfqz,jdbcType=VARCHAR}, #{ztsxsqsrq,jdbcType=VARCHAR}, #{ztsxsjsrq,jdbcType=VARCHAR}, 
      #{ztsxsqzzb,jdbcType=DECIMAL}, #{zspckddlx,jdbcType=VARCHAR}, #{zghdc,jdbcType=VARCHAR}, 
      #{zkcsx,jdbcType=DECIMAL}, #{zkcxx,jdbcType=DECIMAL}, #{zzcqty,jdbcType=DECIMAL}, 
      #{zzbzl,jdbcType=DECIMAL}, #{zcgtpjl,jdbcType=VARCHAR}, #{zcgtpjlxm,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entity.SapZmmt0098" useGeneratedKeys="true">
    insert into SAP_ZMMT0098
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="mandt != null">
        MANDT,
      </if>
      <if test="werks != null">
        WERKS,
      </if>
      <if test="matnr != null">
        MATNR,
      </if>
      <if test="matnrDesc != null">
        MATNR_DESC,
      </if>
      <if test="zisapras != null">
        ZISAPRAS,
      </if>
      <if test="zcgygh != null">
        ZCGYGH,
      </if>
      <if test="zpuragt != null">
        ZPURAGT,
      </if>
      <if test="zxsjwxs != null">
        ZXSJWXS,
      </if>
      <if test="zxxjwxs != null">
        ZXXJWXS,
      </if>
      <if test="zspqzq != null">
        ZSPQZQ,
      </if>
      <if test="zspqzqqsrq != null">
        ZSPQZQQSRQ,
      </if>
      <if test="zspqzqzzrq != null">
        ZSPQZQZZRQ,
      </if>
      <if test="zbckcts != null">
        ZBCKCTS,
      </if>
      <if test="zbckctsqsrq != null">
        ZBCKCTSQSRQ,
      </if>
      <if test="zbckctszzrq != null">
        ZBCKCTSZZRQ,
      </if>
      <if test="zbcdhl != null">
        ZBCDHL,
      </if>
      <if test="zbcdhlqsrq != null">
        ZBCDHLQSRQ,
      </if>
      <if test="zbcdhlzzrq != null">
        ZBCDHLZZRQ,
      </if>
      <if test="zdyzd1 != null">
        ZDYZD1,
      </if>
      <if test="zdyzd2 != null">
        ZDYZD2,
      </if>
      <if test="zdyzd3 != null">
        ZDYZD3,
      </if>
      <if test="zdyzd4 != null">
        ZDYZD4,
      </if>
      <if test="zdyzd5 != null">
        ZDYZD5,
      </if>
      <if test="zthfs != null">
        ZTHFS,
      </if>
      <if test="zcgbzsz != null">
        ZCGBZSZ,
      </if>
      <if test="zzcgsxPf != null">
        ZZCGSX_PF,
      </if>
      <if test="zbcsxPf != null">
        ZBCSX_PF,
      </if>
      <if test="zqzqPf != null">
        ZQZQ_PF,
      </if>
      <if test="zjgqPf != null">
        ZJGQ_PF,
      </if>
      <if test="zaqkctsPf != null">
        ZAQKCTS_PF,
      </if>
      <if test="zbcdhlPf != null">
        ZBCDHL_PF,
      </if>
      <if test="zbckctsPf != null">
        ZBCKCTS_PF,
      </if>
      <if test="zqsrqPf != null">
        ZQSRQ_PF,
      </if>
      <if test="zzzrqPf != null">
        ZZZRQ_PF,
      </if>
      <if test="cpudt != null">
        CPUDT,
      </if>
      <if test="cputm != null">
        CPUTM,
      </if>
      <if test="annam != null">
        ANNAM,
      </if>
      <if test="aedat != null">
        AEDAT,
      </if>
      <if test="aezet != null">
        AEZET,
      </if>
      <if test="aenam != null">
        AENAM,
      </if>
      <if test="zzhxs != null">
        ZZHXS,
      </if>
      <if test="zshck != null">
        ZSHCK,
      </if>
      <if test="zsfjs != null">
        ZSFJS,
      </if>
      <if test="zxqhbs != null">
        ZXQHBS,
      </if>
      <if test="zaqkcbcts != null">
        ZAQKCBCTS,
      </if>
      <if test="zsfqz != null">
        ZSFQZ,
      </if>
      <if test="ztsxsqsrq != null">
        ZTSXSQSRQ,
      </if>
      <if test="ztsxsjsrq != null">
        ZTSXSJSRQ,
      </if>
      <if test="ztsxsqzzb != null">
        ZTSXSQZZB,
      </if>
      <if test="zspckddlx != null">
        ZSPCKDDLX,
      </if>
      <if test="zghdc != null">
        ZGHDC,
      </if>
      <if test="zkcsx != null">
        ZKCSX,
      </if>
      <if test="zkcxx != null">
        ZKCXX,
      </if>
      <if test="zzcqty != null">
        ZZCQTY,
      </if>
      <if test="zzbzl != null">
        ZZBZL,
      </if>
      <if test="zcgtpjl != null">
        ZCGTPJL,
      </if>
      <if test="zcgtpjlxm != null">
        ZCGTPJLXM,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="mandt != null">
        #{mandt,jdbcType=VARCHAR},
      </if>
      <if test="werks != null">
        #{werks,jdbcType=VARCHAR},
      </if>
      <if test="matnr != null">
        #{matnr,jdbcType=VARCHAR},
      </if>
      <if test="matnrDesc != null">
        #{matnrDesc,jdbcType=VARCHAR},
      </if>
      <if test="zisapras != null">
        #{zisapras,jdbcType=VARCHAR},
      </if>
      <if test="zcgygh != null">
        #{zcgygh,jdbcType=VARCHAR},
      </if>
      <if test="zpuragt != null">
        #{zpuragt,jdbcType=VARCHAR},
      </if>
      <if test="zxsjwxs != null">
        #{zxsjwxs,jdbcType=DECIMAL},
      </if>
      <if test="zxxjwxs != null">
        #{zxxjwxs,jdbcType=DECIMAL},
      </if>
      <if test="zspqzq != null">
        #{zspqzq,jdbcType=DECIMAL},
      </if>
      <if test="zspqzqqsrq != null">
        #{zspqzqqsrq,jdbcType=VARCHAR},
      </if>
      <if test="zspqzqzzrq != null">
        #{zspqzqzzrq,jdbcType=VARCHAR},
      </if>
      <if test="zbckcts != null">
        #{zbckcts,jdbcType=DECIMAL},
      </if>
      <if test="zbckctsqsrq != null">
        #{zbckctsqsrq,jdbcType=VARCHAR},
      </if>
      <if test="zbckctszzrq != null">
        #{zbckctszzrq,jdbcType=VARCHAR},
      </if>
      <if test="zbcdhl != null">
        #{zbcdhl,jdbcType=DECIMAL},
      </if>
      <if test="zbcdhlqsrq != null">
        #{zbcdhlqsrq,jdbcType=VARCHAR},
      </if>
      <if test="zbcdhlzzrq != null">
        #{zbcdhlzzrq,jdbcType=VARCHAR},
      </if>
      <if test="zdyzd1 != null">
        #{zdyzd1,jdbcType=VARCHAR},
      </if>
      <if test="zdyzd2 != null">
        #{zdyzd2,jdbcType=VARCHAR},
      </if>
      <if test="zdyzd3 != null">
        #{zdyzd3,jdbcType=VARCHAR},
      </if>
      <if test="zdyzd4 != null">
        #{zdyzd4,jdbcType=VARCHAR},
      </if>
      <if test="zdyzd5 != null">
        #{zdyzd5,jdbcType=VARCHAR},
      </if>
      <if test="zthfs != null">
        #{zthfs,jdbcType=VARCHAR},
      </if>
      <if test="zcgbzsz != null">
        #{zcgbzsz,jdbcType=VARCHAR},
      </if>
      <if test="zzcgsxPf != null">
        #{zzcgsxPf,jdbcType=VARCHAR},
      </if>
      <if test="zbcsxPf != null">
        #{zbcsxPf,jdbcType=VARCHAR},
      </if>
      <if test="zqzqPf != null">
        #{zqzqPf,jdbcType=INTEGER},
      </if>
      <if test="zjgqPf != null">
        #{zjgqPf,jdbcType=INTEGER},
      </if>
      <if test="zaqkctsPf != null">
        #{zaqkctsPf,jdbcType=INTEGER},
      </if>
      <if test="zbcdhlPf != null">
        #{zbcdhlPf,jdbcType=DECIMAL},
      </if>
      <if test="zbckctsPf != null">
        #{zbckctsPf,jdbcType=INTEGER},
      </if>
      <if test="zqsrqPf != null">
        #{zqsrqPf,jdbcType=VARCHAR},
      </if>
      <if test="zzzrqPf != null">
        #{zzzrqPf,jdbcType=VARCHAR},
      </if>
      <if test="cpudt != null">
        #{cpudt,jdbcType=VARCHAR},
      </if>
      <if test="cputm != null">
        #{cputm,jdbcType=VARCHAR},
      </if>
      <if test="annam != null">
        #{annam,jdbcType=VARCHAR},
      </if>
      <if test="aedat != null">
        #{aedat,jdbcType=VARCHAR},
      </if>
      <if test="aezet != null">
        #{aezet,jdbcType=VARCHAR},
      </if>
      <if test="aenam != null">
        #{aenam,jdbcType=VARCHAR},
      </if>
      <if test="zzhxs != null">
        #{zzhxs,jdbcType=VARCHAR},
      </if>
      <if test="zshck != null">
        #{zshck,jdbcType=VARCHAR},
      </if>
      <if test="zsfjs != null">
        #{zsfjs,jdbcType=VARCHAR},
      </if>
      <if test="zxqhbs != null">
        #{zxqhbs,jdbcType=DECIMAL},
      </if>
      <if test="zaqkcbcts != null">
        #{zaqkcbcts,jdbcType=DECIMAL},
      </if>
      <if test="zsfqz != null">
        #{zsfqz,jdbcType=VARCHAR},
      </if>
      <if test="ztsxsqsrq != null">
        #{ztsxsqsrq,jdbcType=VARCHAR},
      </if>
      <if test="ztsxsjsrq != null">
        #{ztsxsjsrq,jdbcType=VARCHAR},
      </if>
      <if test="ztsxsqzzb != null">
        #{ztsxsqzzb,jdbcType=DECIMAL},
      </if>
      <if test="zspckddlx != null">
        #{zspckddlx,jdbcType=VARCHAR},
      </if>
      <if test="zghdc != null">
        #{zghdc,jdbcType=VARCHAR},
      </if>
      <if test="zkcsx != null">
        #{zkcsx,jdbcType=DECIMAL},
      </if>
      <if test="zkcxx != null">
        #{zkcxx,jdbcType=DECIMAL},
      </if>
      <if test="zzcqty != null">
        #{zzcqty,jdbcType=DECIMAL},
      </if>
      <if test="zzbzl != null">
        #{zzbzl,jdbcType=DECIMAL},
      </if>
      <if test="zcgtpjl != null">
        #{zcgtpjl,jdbcType=VARCHAR},
      </if>
      <if test="zcgtpjlxm != null">
        #{zcgtpjlxm,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.iscm.entity.SapZmmt0098Example" resultType="java.lang.Long">
    select count(*) from SAP_ZMMT0098
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update SAP_ZMMT0098
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.mandt != null">
        MANDT = #{record.mandt,jdbcType=VARCHAR},
      </if>
      <if test="record.werks != null">
        WERKS = #{record.werks,jdbcType=VARCHAR},
      </if>
      <if test="record.matnr != null">
        MATNR = #{record.matnr,jdbcType=VARCHAR},
      </if>
      <if test="record.matnrDesc != null">
        MATNR_DESC = #{record.matnrDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.zisapras != null">
        ZISAPRAS = #{record.zisapras,jdbcType=VARCHAR},
      </if>
      <if test="record.zcgygh != null">
        ZCGYGH = #{record.zcgygh,jdbcType=VARCHAR},
      </if>
      <if test="record.zpuragt != null">
        ZPURAGT = #{record.zpuragt,jdbcType=VARCHAR},
      </if>
      <if test="record.zxsjwxs != null">
        ZXSJWXS = #{record.zxsjwxs,jdbcType=DECIMAL},
      </if>
      <if test="record.zxxjwxs != null">
        ZXXJWXS = #{record.zxxjwxs,jdbcType=DECIMAL},
      </if>
      <if test="record.zspqzq != null">
        ZSPQZQ = #{record.zspqzq,jdbcType=DECIMAL},
      </if>
      <if test="record.zspqzqqsrq != null">
        ZSPQZQQSRQ = #{record.zspqzqqsrq,jdbcType=VARCHAR},
      </if>
      <if test="record.zspqzqzzrq != null">
        ZSPQZQZZRQ = #{record.zspqzqzzrq,jdbcType=VARCHAR},
      </if>
      <if test="record.zbckcts != null">
        ZBCKCTS = #{record.zbckcts,jdbcType=DECIMAL},
      </if>
      <if test="record.zbckctsqsrq != null">
        ZBCKCTSQSRQ = #{record.zbckctsqsrq,jdbcType=VARCHAR},
      </if>
      <if test="record.zbckctszzrq != null">
        ZBCKCTSZZRQ = #{record.zbckctszzrq,jdbcType=VARCHAR},
      </if>
      <if test="record.zbcdhl != null">
        ZBCDHL = #{record.zbcdhl,jdbcType=DECIMAL},
      </if>
      <if test="record.zbcdhlqsrq != null">
        ZBCDHLQSRQ = #{record.zbcdhlqsrq,jdbcType=VARCHAR},
      </if>
      <if test="record.zbcdhlzzrq != null">
        ZBCDHLZZRQ = #{record.zbcdhlzzrq,jdbcType=VARCHAR},
      </if>
      <if test="record.zdyzd1 != null">
        ZDYZD1 = #{record.zdyzd1,jdbcType=VARCHAR},
      </if>
      <if test="record.zdyzd2 != null">
        ZDYZD2 = #{record.zdyzd2,jdbcType=VARCHAR},
      </if>
      <if test="record.zdyzd3 != null">
        ZDYZD3 = #{record.zdyzd3,jdbcType=VARCHAR},
      </if>
      <if test="record.zdyzd4 != null">
        ZDYZD4 = #{record.zdyzd4,jdbcType=VARCHAR},
      </if>
      <if test="record.zdyzd5 != null">
        ZDYZD5 = #{record.zdyzd5,jdbcType=VARCHAR},
      </if>
      <if test="record.zthfs != null">
        ZTHFS = #{record.zthfs,jdbcType=VARCHAR},
      </if>
      <if test="record.zcgbzsz != null">
        ZCGBZSZ = #{record.zcgbzsz,jdbcType=VARCHAR},
      </if>
      <if test="record.zzcgsxPf != null">
        ZZCGSX_PF = #{record.zzcgsxPf,jdbcType=VARCHAR},
      </if>
      <if test="record.zbcsxPf != null">
        ZBCSX_PF = #{record.zbcsxPf,jdbcType=VARCHAR},
      </if>
      <if test="record.zqzqPf != null">
        ZQZQ_PF = #{record.zqzqPf,jdbcType=INTEGER},
      </if>
      <if test="record.zjgqPf != null">
        ZJGQ_PF = #{record.zjgqPf,jdbcType=INTEGER},
      </if>
      <if test="record.zaqkctsPf != null">
        ZAQKCTS_PF = #{record.zaqkctsPf,jdbcType=INTEGER},
      </if>
      <if test="record.zbcdhlPf != null">
        ZBCDHL_PF = #{record.zbcdhlPf,jdbcType=DECIMAL},
      </if>
      <if test="record.zbckctsPf != null">
        ZBCKCTS_PF = #{record.zbckctsPf,jdbcType=INTEGER},
      </if>
      <if test="record.zqsrqPf != null">
        ZQSRQ_PF = #{record.zqsrqPf,jdbcType=VARCHAR},
      </if>
      <if test="record.zzzrqPf != null">
        ZZZRQ_PF = #{record.zzzrqPf,jdbcType=VARCHAR},
      </if>
      <if test="record.cpudt != null">
        CPUDT = #{record.cpudt,jdbcType=VARCHAR},
      </if>
      <if test="record.cputm != null">
        CPUTM = #{record.cputm,jdbcType=VARCHAR},
      </if>
      <if test="record.annam != null">
        ANNAM = #{record.annam,jdbcType=VARCHAR},
      </if>
      <if test="record.aedat != null">
        AEDAT = #{record.aedat,jdbcType=VARCHAR},
      </if>
      <if test="record.aezet != null">
        AEZET = #{record.aezet,jdbcType=VARCHAR},
      </if>
      <if test="record.aenam != null">
        AENAM = #{record.aenam,jdbcType=VARCHAR},
      </if>
      <if test="record.zzhxs != null">
        ZZHXS = #{record.zzhxs,jdbcType=VARCHAR},
      </if>
      <if test="record.zshck != null">
        ZSHCK = #{record.zshck,jdbcType=VARCHAR},
      </if>
      <if test="record.zsfjs != null">
        ZSFJS = #{record.zsfjs,jdbcType=VARCHAR},
      </if>
      <if test="record.zxqhbs != null">
        ZXQHBS = #{record.zxqhbs,jdbcType=DECIMAL},
      </if>
      <if test="record.zaqkcbcts != null">
        ZAQKCBCTS = #{record.zaqkcbcts,jdbcType=DECIMAL},
      </if>
      <if test="record.zsfqz != null">
        ZSFQZ = #{record.zsfqz,jdbcType=VARCHAR},
      </if>
      <if test="record.ztsxsqsrq != null">
        ZTSXSQSRQ = #{record.ztsxsqsrq,jdbcType=VARCHAR},
      </if>
      <if test="record.ztsxsjsrq != null">
        ZTSXSJSRQ = #{record.ztsxsjsrq,jdbcType=VARCHAR},
      </if>
      <if test="record.ztsxsqzzb != null">
        ZTSXSQZZB = #{record.ztsxsqzzb,jdbcType=DECIMAL},
      </if>
      <if test="record.zspckddlx != null">
        ZSPCKDDLX = #{record.zspckddlx,jdbcType=VARCHAR},
      </if>
      <if test="record.zghdc != null">
        ZGHDC = #{record.zghdc,jdbcType=VARCHAR},
      </if>
      <if test="record.zkcsx != null">
        ZKCSX = #{record.zkcsx,jdbcType=DECIMAL},
      </if>
      <if test="record.zkcxx != null">
        ZKCXX = #{record.zkcxx,jdbcType=DECIMAL},
      </if>
      <if test="record.zzcqty != null">
        ZZCQTY = #{record.zzcqty,jdbcType=DECIMAL},
      </if>
      <if test="record.zzbzl != null">
        ZZBZL = #{record.zzbzl,jdbcType=DECIMAL},
      </if>
      <if test="record.zcgtpjl != null">
        ZCGTPJL = #{record.zcgtpjl,jdbcType=VARCHAR},
      </if>
      <if test="record.zcgtpjlxm != null">
        ZCGTPJLXM = #{record.zcgtpjlxm,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update SAP_ZMMT0098
    set id = #{record.id,jdbcType=BIGINT},
      MANDT = #{record.mandt,jdbcType=VARCHAR},
      WERKS = #{record.werks,jdbcType=VARCHAR},
      MATNR = #{record.matnr,jdbcType=VARCHAR},
      MATNR_DESC = #{record.matnrDesc,jdbcType=VARCHAR},
      ZISAPRAS = #{record.zisapras,jdbcType=VARCHAR},
      ZCGYGH = #{record.zcgygh,jdbcType=VARCHAR},
      ZPURAGT = #{record.zpuragt,jdbcType=VARCHAR},
      ZXSJWXS = #{record.zxsjwxs,jdbcType=DECIMAL},
      ZXXJWXS = #{record.zxxjwxs,jdbcType=DECIMAL},
      ZSPQZQ = #{record.zspqzq,jdbcType=DECIMAL},
      ZSPQZQQSRQ = #{record.zspqzqqsrq,jdbcType=VARCHAR},
      ZSPQZQZZRQ = #{record.zspqzqzzrq,jdbcType=VARCHAR},
      ZBCKCTS = #{record.zbckcts,jdbcType=DECIMAL},
      ZBCKCTSQSRQ = #{record.zbckctsqsrq,jdbcType=VARCHAR},
      ZBCKCTSZZRQ = #{record.zbckctszzrq,jdbcType=VARCHAR},
      ZBCDHL = #{record.zbcdhl,jdbcType=DECIMAL},
      ZBCDHLQSRQ = #{record.zbcdhlqsrq,jdbcType=VARCHAR},
      ZBCDHLZZRQ = #{record.zbcdhlzzrq,jdbcType=VARCHAR},
      ZDYZD1 = #{record.zdyzd1,jdbcType=VARCHAR},
      ZDYZD2 = #{record.zdyzd2,jdbcType=VARCHAR},
      ZDYZD3 = #{record.zdyzd3,jdbcType=VARCHAR},
      ZDYZD4 = #{record.zdyzd4,jdbcType=VARCHAR},
      ZDYZD5 = #{record.zdyzd5,jdbcType=VARCHAR},
      ZTHFS = #{record.zthfs,jdbcType=VARCHAR},
      ZCGBZSZ = #{record.zcgbzsz,jdbcType=VARCHAR},
      ZZCGSX_PF = #{record.zzcgsxPf,jdbcType=VARCHAR},
      ZBCSX_PF = #{record.zbcsxPf,jdbcType=VARCHAR},
      ZQZQ_PF = #{record.zqzqPf,jdbcType=INTEGER},
      ZJGQ_PF = #{record.zjgqPf,jdbcType=INTEGER},
      ZAQKCTS_PF = #{record.zaqkctsPf,jdbcType=INTEGER},
      ZBCDHL_PF = #{record.zbcdhlPf,jdbcType=DECIMAL},
      ZBCKCTS_PF = #{record.zbckctsPf,jdbcType=INTEGER},
      ZQSRQ_PF = #{record.zqsrqPf,jdbcType=VARCHAR},
      ZZZRQ_PF = #{record.zzzrqPf,jdbcType=VARCHAR},
      CPUDT = #{record.cpudt,jdbcType=VARCHAR},
      CPUTM = #{record.cputm,jdbcType=VARCHAR},
      ANNAM = #{record.annam,jdbcType=VARCHAR},
      AEDAT = #{record.aedat,jdbcType=VARCHAR},
      AEZET = #{record.aezet,jdbcType=VARCHAR},
      AENAM = #{record.aenam,jdbcType=VARCHAR},
      ZZHXS = #{record.zzhxs,jdbcType=VARCHAR},
      ZSHCK = #{record.zshck,jdbcType=VARCHAR},
      ZSFJS = #{record.zsfjs,jdbcType=VARCHAR},
      ZXQHBS = #{record.zxqhbs,jdbcType=DECIMAL},
      ZAQKCBCTS = #{record.zaqkcbcts,jdbcType=DECIMAL},
      ZSFQZ = #{record.zsfqz,jdbcType=VARCHAR},
      ZTSXSQSRQ = #{record.ztsxsqsrq,jdbcType=VARCHAR},
      ZTSXSJSRQ = #{record.ztsxsjsrq,jdbcType=VARCHAR},
      ZTSXSQZZB = #{record.ztsxsqzzb,jdbcType=DECIMAL},
      ZSPCKDDLX = #{record.zspckddlx,jdbcType=VARCHAR},
      ZGHDC = #{record.zghdc,jdbcType=VARCHAR},
      ZKCSX = #{record.zkcsx,jdbcType=DECIMAL},
      ZKCXX = #{record.zkcxx,jdbcType=DECIMAL},
      ZZCQTY = #{record.zzcqty,jdbcType=DECIMAL},
      ZZBZL = #{record.zzbzl,jdbcType=DECIMAL},
      ZCGTPJL = #{record.zcgtpjl,jdbcType=VARCHAR},
      ZCGTPJLXM = #{record.zcgtpjlxm,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.iscm.entity.SapZmmt0098">
    update SAP_ZMMT0098
    <set>
      <if test="mandt != null">
        MANDT = #{mandt,jdbcType=VARCHAR},
      </if>
      <if test="werks != null">
        WERKS = #{werks,jdbcType=VARCHAR},
      </if>
      <if test="matnr != null">
        MATNR = #{matnr,jdbcType=VARCHAR},
      </if>
      <if test="matnrDesc != null">
        MATNR_DESC = #{matnrDesc,jdbcType=VARCHAR},
      </if>
      <if test="zisapras != null">
        ZISAPRAS = #{zisapras,jdbcType=VARCHAR},
      </if>
      <if test="zcgygh != null">
        ZCGYGH = #{zcgygh,jdbcType=VARCHAR},
      </if>
      <if test="zpuragt != null">
        ZPURAGT = #{zpuragt,jdbcType=VARCHAR},
      </if>
      <if test="zxsjwxs != null">
        ZXSJWXS = #{zxsjwxs,jdbcType=DECIMAL},
      </if>
      <if test="zxxjwxs != null">
        ZXXJWXS = #{zxxjwxs,jdbcType=DECIMAL},
      </if>
      <if test="zspqzq != null">
        ZSPQZQ = #{zspqzq,jdbcType=DECIMAL},
      </if>
      <if test="zspqzqqsrq != null">
        ZSPQZQQSRQ = #{zspqzqqsrq,jdbcType=VARCHAR},
      </if>
      <if test="zspqzqzzrq != null">
        ZSPQZQZZRQ = #{zspqzqzzrq,jdbcType=VARCHAR},
      </if>
      <if test="zbckcts != null">
        ZBCKCTS = #{zbckcts,jdbcType=DECIMAL},
      </if>
      <if test="zbckctsqsrq != null">
        ZBCKCTSQSRQ = #{zbckctsqsrq,jdbcType=VARCHAR},
      </if>
      <if test="zbckctszzrq != null">
        ZBCKCTSZZRQ = #{zbckctszzrq,jdbcType=VARCHAR},
      </if>
      <if test="zbcdhl != null">
        ZBCDHL = #{zbcdhl,jdbcType=DECIMAL},
      </if>
      <if test="zbcdhlqsrq != null">
        ZBCDHLQSRQ = #{zbcdhlqsrq,jdbcType=VARCHAR},
      </if>
      <if test="zbcdhlzzrq != null">
        ZBCDHLZZRQ = #{zbcdhlzzrq,jdbcType=VARCHAR},
      </if>
      <if test="zdyzd1 != null">
        ZDYZD1 = #{zdyzd1,jdbcType=VARCHAR},
      </if>
      <if test="zdyzd2 != null">
        ZDYZD2 = #{zdyzd2,jdbcType=VARCHAR},
      </if>
      <if test="zdyzd3 != null">
        ZDYZD3 = #{zdyzd3,jdbcType=VARCHAR},
      </if>
      <if test="zdyzd4 != null">
        ZDYZD4 = #{zdyzd4,jdbcType=VARCHAR},
      </if>
      <if test="zdyzd5 != null">
        ZDYZD5 = #{zdyzd5,jdbcType=VARCHAR},
      </if>
      <if test="zthfs != null">
        ZTHFS = #{zthfs,jdbcType=VARCHAR},
      </if>
      <if test="zcgbzsz != null">
        ZCGBZSZ = #{zcgbzsz,jdbcType=VARCHAR},
      </if>
      <if test="zzcgsxPf != null">
        ZZCGSX_PF = #{zzcgsxPf,jdbcType=VARCHAR},
      </if>
      <if test="zbcsxPf != null">
        ZBCSX_PF = #{zbcsxPf,jdbcType=VARCHAR},
      </if>
      <if test="zqzqPf != null">
        ZQZQ_PF = #{zqzqPf,jdbcType=INTEGER},
      </if>
      <if test="zjgqPf != null">
        ZJGQ_PF = #{zjgqPf,jdbcType=INTEGER},
      </if>
      <if test="zaqkctsPf != null">
        ZAQKCTS_PF = #{zaqkctsPf,jdbcType=INTEGER},
      </if>
      <if test="zbcdhlPf != null">
        ZBCDHL_PF = #{zbcdhlPf,jdbcType=DECIMAL},
      </if>
      <if test="zbckctsPf != null">
        ZBCKCTS_PF = #{zbckctsPf,jdbcType=INTEGER},
      </if>
      <if test="zqsrqPf != null">
        ZQSRQ_PF = #{zqsrqPf,jdbcType=VARCHAR},
      </if>
      <if test="zzzrqPf != null">
        ZZZRQ_PF = #{zzzrqPf,jdbcType=VARCHAR},
      </if>
      <if test="cpudt != null">
        CPUDT = #{cpudt,jdbcType=VARCHAR},
      </if>
      <if test="cputm != null">
        CPUTM = #{cputm,jdbcType=VARCHAR},
      </if>
      <if test="annam != null">
        ANNAM = #{annam,jdbcType=VARCHAR},
      </if>
      <if test="aedat != null">
        AEDAT = #{aedat,jdbcType=VARCHAR},
      </if>
      <if test="aezet != null">
        AEZET = #{aezet,jdbcType=VARCHAR},
      </if>
      <if test="aenam != null">
        AENAM = #{aenam,jdbcType=VARCHAR},
      </if>
      <if test="zzhxs != null">
        ZZHXS = #{zzhxs,jdbcType=VARCHAR},
      </if>
      <if test="zshck != null">
        ZSHCK = #{zshck,jdbcType=VARCHAR},
      </if>
      <if test="zsfjs != null">
        ZSFJS = #{zsfjs,jdbcType=VARCHAR},
      </if>
      <if test="zxqhbs != null">
        ZXQHBS = #{zxqhbs,jdbcType=DECIMAL},
      </if>
      <if test="zaqkcbcts != null">
        ZAQKCBCTS = #{zaqkcbcts,jdbcType=DECIMAL},
      </if>
      <if test="zsfqz != null">
        ZSFQZ = #{zsfqz,jdbcType=VARCHAR},
      </if>
      <if test="ztsxsqsrq != null">
        ZTSXSQSRQ = #{ztsxsqsrq,jdbcType=VARCHAR},
      </if>
      <if test="ztsxsjsrq != null">
        ZTSXSJSRQ = #{ztsxsjsrq,jdbcType=VARCHAR},
      </if>
      <if test="ztsxsqzzb != null">
        ZTSXSQZZB = #{ztsxsqzzb,jdbcType=DECIMAL},
      </if>
      <if test="zspckddlx != null">
        ZSPCKDDLX = #{zspckddlx,jdbcType=VARCHAR},
      </if>
      <if test="zghdc != null">
        ZGHDC = #{zghdc,jdbcType=VARCHAR},
      </if>
      <if test="zkcsx != null">
        ZKCSX = #{zkcsx,jdbcType=DECIMAL},
      </if>
      <if test="zkcxx != null">
        ZKCXX = #{zkcxx,jdbcType=DECIMAL},
      </if>
      <if test="zzcqty != null">
        ZZCQTY = #{zzcqty,jdbcType=DECIMAL},
      </if>
      <if test="zzbzl != null">
        ZZBZL = #{zzbzl,jdbcType=DECIMAL},
      </if>
      <if test="zcgtpjl != null">
        ZCGTPJL = #{zcgtpjl,jdbcType=VARCHAR},
      </if>
      <if test="zcgtpjlxm != null">
        ZCGTPJLXM = #{zcgtpjlxm,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.iscm.entity.SapZmmt0098">
    update SAP_ZMMT0098
    set MANDT = #{mandt,jdbcType=VARCHAR},
      WERKS = #{werks,jdbcType=VARCHAR},
      MATNR = #{matnr,jdbcType=VARCHAR},
      MATNR_DESC = #{matnrDesc,jdbcType=VARCHAR},
      ZISAPRAS = #{zisapras,jdbcType=VARCHAR},
      ZCGYGH = #{zcgygh,jdbcType=VARCHAR},
      ZPURAGT = #{zpuragt,jdbcType=VARCHAR},
      ZXSJWXS = #{zxsjwxs,jdbcType=DECIMAL},
      ZXXJWXS = #{zxxjwxs,jdbcType=DECIMAL},
      ZSPQZQ = #{zspqzq,jdbcType=DECIMAL},
      ZSPQZQQSRQ = #{zspqzqqsrq,jdbcType=VARCHAR},
      ZSPQZQZZRQ = #{zspqzqzzrq,jdbcType=VARCHAR},
      ZBCKCTS = #{zbckcts,jdbcType=DECIMAL},
      ZBCKCTSQSRQ = #{zbckctsqsrq,jdbcType=VARCHAR},
      ZBCKCTSZZRQ = #{zbckctszzrq,jdbcType=VARCHAR},
      ZBCDHL = #{zbcdhl,jdbcType=DECIMAL},
      ZBCDHLQSRQ = #{zbcdhlqsrq,jdbcType=VARCHAR},
      ZBCDHLZZRQ = #{zbcdhlzzrq,jdbcType=VARCHAR},
      ZDYZD1 = #{zdyzd1,jdbcType=VARCHAR},
      ZDYZD2 = #{zdyzd2,jdbcType=VARCHAR},
      ZDYZD3 = #{zdyzd3,jdbcType=VARCHAR},
      ZDYZD4 = #{zdyzd4,jdbcType=VARCHAR},
      ZDYZD5 = #{zdyzd5,jdbcType=VARCHAR},
      ZTHFS = #{zthfs,jdbcType=VARCHAR},
      ZCGBZSZ = #{zcgbzsz,jdbcType=VARCHAR},
      ZZCGSX_PF = #{zzcgsxPf,jdbcType=VARCHAR},
      ZBCSX_PF = #{zbcsxPf,jdbcType=VARCHAR},
      ZQZQ_PF = #{zqzqPf,jdbcType=INTEGER},
      ZJGQ_PF = #{zjgqPf,jdbcType=INTEGER},
      ZAQKCTS_PF = #{zaqkctsPf,jdbcType=INTEGER},
      ZBCDHL_PF = #{zbcdhlPf,jdbcType=DECIMAL},
      ZBCKCTS_PF = #{zbckctsPf,jdbcType=INTEGER},
      ZQSRQ_PF = #{zqsrqPf,jdbcType=VARCHAR},
      ZZZRQ_PF = #{zzzrqPf,jdbcType=VARCHAR},
      CPUDT = #{cpudt,jdbcType=VARCHAR},
      CPUTM = #{cputm,jdbcType=VARCHAR},
      ANNAM = #{annam,jdbcType=VARCHAR},
      AEDAT = #{aedat,jdbcType=VARCHAR},
      AEZET = #{aezet,jdbcType=VARCHAR},
      AENAM = #{aenam,jdbcType=VARCHAR},
      ZZHXS = #{zzhxs,jdbcType=VARCHAR},
      ZSHCK = #{zshck,jdbcType=VARCHAR},
      ZSFJS = #{zsfjs,jdbcType=VARCHAR},
      ZXQHBS = #{zxqhbs,jdbcType=DECIMAL},
      ZAQKCBCTS = #{zaqkcbcts,jdbcType=DECIMAL},
      ZSFQZ = #{zsfqz,jdbcType=VARCHAR},
      ZTSXSQSRQ = #{ztsxsqsrq,jdbcType=VARCHAR},
      ZTSXSJSRQ = #{ztsxsjsrq,jdbcType=VARCHAR},
      ZTSXSQZZB = #{ztsxsqzzb,jdbcType=DECIMAL},
      ZSPCKDDLX = #{zspckddlx,jdbcType=VARCHAR},
      ZGHDC = #{zghdc,jdbcType=VARCHAR},
      ZKCSX = #{zkcsx,jdbcType=DECIMAL},
      ZKCXX = #{zkcxx,jdbcType=DECIMAL},
      ZZCQTY = #{zzcqty,jdbcType=DECIMAL},
      ZZBZL = #{zzbzl,jdbcType=DECIMAL},
      ZCGTPJL = #{zcgtpjl,jdbcType=VARCHAR},
      ZCGTPJLXM = #{zcgtpjlxm,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>