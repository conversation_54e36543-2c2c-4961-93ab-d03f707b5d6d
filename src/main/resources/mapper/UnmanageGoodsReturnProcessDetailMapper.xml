<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.UnmanageGoodsReturnProcessDetailMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.UnmanageGoodsReturnProcessDetail">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="task_id" jdbcType="BIGINT" property="taskId" />
    <result column="task_code" jdbcType="VARCHAR" property="taskCode" />
    <result column="store_org_id" jdbcType="BIGINT" property="storeOrgId" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="store_name" jdbcType="VARCHAR" property="storeName" />
    <result column="company_org_id" jdbcType="BIGINT" property="companyOrgId" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="goods_count" jdbcType="INTEGER" property="goodsCount" />
    <result column="return_qty" jdbcType="DECIMAL" property="returnQty" />
    <result column="return_amount" jdbcType="DECIMAL" property="returnAmount" />
    <result column="return_order_count" jdbcType="INTEGER" property="returnOrderCount" />
    <result column="return_order_nos" jdbcType="VARCHAR" property="returnOrderNos" />
    <result column="process_status" jdbcType="TINYINT" property="processStatus" />
    <result column="error_msg" jdbcType="VARCHAR" property="errorMsg" />
    <result column="process_time" jdbcType="TIMESTAMP" property="processTime" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, task_id, task_code, store_org_id, store_code, store_name, company_org_id, company_code, 
    company_name, goods_count, return_qty, return_amount, return_order_count, return_order_nos, 
    process_status, error_msg, process_time, extend, version, `status`, gmt_create, gmt_update
  </sql>
  <select id="selectByExample" parameterType="com.cowell.iscm.entity.UnmanageGoodsReturnProcessDetailExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from unmanage_goods_return_process_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from unmanage_goods_return_process_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from unmanage_goods_return_process_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.iscm.entity.UnmanageGoodsReturnProcessDetailExample">
    delete from unmanage_goods_return_process_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.iscm.entity.UnmanageGoodsReturnProcessDetail">
    insert into unmanage_goods_return_process_detail (id, task_id, task_code, 
      store_org_id, store_code, store_name, 
      company_org_id, company_code, company_name, 
      goods_count, return_qty, return_amount, 
      return_order_count, return_order_nos, process_status, 
      error_msg, process_time, extend, 
      version, `status`, gmt_create, 
      gmt_update)
    values (#{id,jdbcType=BIGINT}, #{taskId,jdbcType=BIGINT}, #{taskCode,jdbcType=VARCHAR}, 
      #{storeOrgId,jdbcType=BIGINT}, #{storeCode,jdbcType=VARCHAR}, #{storeName,jdbcType=VARCHAR}, 
      #{companyOrgId,jdbcType=BIGINT}, #{companyCode,jdbcType=VARCHAR}, #{companyName,jdbcType=VARCHAR}, 
      #{goodsCount,jdbcType=INTEGER}, #{returnQty,jdbcType=DECIMAL}, #{returnAmount,jdbcType=DECIMAL}, 
      #{returnOrderCount,jdbcType=INTEGER}, #{returnOrderNos,jdbcType=VARCHAR}, #{processStatus,jdbcType=TINYINT}, 
      #{errorMsg,jdbcType=VARCHAR}, #{processTime,jdbcType=TIMESTAMP}, #{extend,jdbcType=VARCHAR}, 
      #{version,jdbcType=INTEGER}, #{status,jdbcType=TINYINT}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtUpdate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.iscm.entity.UnmanageGoodsReturnProcessDetail">
    insert into unmanage_goods_return_process_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="taskId != null">
        task_id,
      </if>
      <if test="taskCode != null">
        task_code,
      </if>
      <if test="storeOrgId != null">
        store_org_id,
      </if>
      <if test="storeCode != null">
        store_code,
      </if>
      <if test="storeName != null">
        store_name,
      </if>
      <if test="companyOrgId != null">
        company_org_id,
      </if>
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="companyName != null">
        company_name,
      </if>
      <if test="goodsCount != null">
        goods_count,
      </if>
      <if test="returnQty != null">
        return_qty,
      </if>
      <if test="returnAmount != null">
        return_amount,
      </if>
      <if test="returnOrderCount != null">
        return_order_count,
      </if>
      <if test="returnOrderNos != null">
        return_order_nos,
      </if>
      <if test="processStatus != null">
        process_status,
      </if>
      <if test="errorMsg != null">
        error_msg,
      </if>
      <if test="processTime != null">
        process_time,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="taskId != null">
        #{taskId,jdbcType=BIGINT},
      </if>
      <if test="taskCode != null">
        #{taskCode,jdbcType=VARCHAR},
      </if>
      <if test="storeOrgId != null">
        #{storeOrgId,jdbcType=BIGINT},
      </if>
      <if test="storeCode != null">
        #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="storeName != null">
        #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="companyOrgId != null">
        #{companyOrgId,jdbcType=BIGINT},
      </if>
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="goodsCount != null">
        #{goodsCount,jdbcType=INTEGER},
      </if>
      <if test="returnQty != null">
        #{returnQty,jdbcType=DECIMAL},
      </if>
      <if test="returnAmount != null">
        #{returnAmount,jdbcType=DECIMAL},
      </if>
      <if test="returnOrderCount != null">
        #{returnOrderCount,jdbcType=INTEGER},
      </if>
      <if test="returnOrderNos != null">
        #{returnOrderNos,jdbcType=VARCHAR},
      </if>
      <if test="processStatus != null">
        #{processStatus,jdbcType=TINYINT},
      </if>
      <if test="errorMsg != null">
        #{errorMsg,jdbcType=VARCHAR},
      </if>
      <if test="processTime != null">
        #{processTime,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.iscm.entity.UnmanageGoodsReturnProcessDetailExample" resultType="java.lang.Long">
    select count(*) from unmanage_goods_return_process_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update unmanage_goods_return_process_detail
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.taskId != null">
        task_id = #{record.taskId,jdbcType=BIGINT},
      </if>
      <if test="record.taskCode != null">
        task_code = #{record.taskCode,jdbcType=VARCHAR},
      </if>
      <if test="record.storeOrgId != null">
        store_org_id = #{record.storeOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.storeCode != null">
        store_code = #{record.storeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.storeName != null">
        store_name = #{record.storeName,jdbcType=VARCHAR},
      </if>
      <if test="record.companyOrgId != null">
        company_org_id = #{record.companyOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.companyCode != null">
        company_code = #{record.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.companyName != null">
        company_name = #{record.companyName,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsCount != null">
        goods_count = #{record.goodsCount,jdbcType=INTEGER},
      </if>
      <if test="record.returnQty != null">
        return_qty = #{record.returnQty,jdbcType=DECIMAL},
      </if>
      <if test="record.returnAmount != null">
        return_amount = #{record.returnAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.returnOrderCount != null">
        return_order_count = #{record.returnOrderCount,jdbcType=INTEGER},
      </if>
      <if test="record.returnOrderNos != null">
        return_order_nos = #{record.returnOrderNos,jdbcType=VARCHAR},
      </if>
      <if test="record.processStatus != null">
        process_status = #{record.processStatus,jdbcType=TINYINT},
      </if>
      <if test="record.errorMsg != null">
        error_msg = #{record.errorMsg,jdbcType=VARCHAR},
      </if>
      <if test="record.processTime != null">
        process_time = #{record.processTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update unmanage_goods_return_process_detail
    set id = #{record.id,jdbcType=BIGINT},
      task_id = #{record.taskId,jdbcType=BIGINT},
      task_code = #{record.taskCode,jdbcType=VARCHAR},
      store_org_id = #{record.storeOrgId,jdbcType=BIGINT},
      store_code = #{record.storeCode,jdbcType=VARCHAR},
      store_name = #{record.storeName,jdbcType=VARCHAR},
      company_org_id = #{record.companyOrgId,jdbcType=BIGINT},
      company_code = #{record.companyCode,jdbcType=VARCHAR},
      company_name = #{record.companyName,jdbcType=VARCHAR},
      goods_count = #{record.goodsCount,jdbcType=INTEGER},
      return_qty = #{record.returnQty,jdbcType=DECIMAL},
      return_amount = #{record.returnAmount,jdbcType=DECIMAL},
      return_order_count = #{record.returnOrderCount,jdbcType=INTEGER},
      return_order_nos = #{record.returnOrderNos,jdbcType=VARCHAR},
      process_status = #{record.processStatus,jdbcType=TINYINT},
      error_msg = #{record.errorMsg,jdbcType=VARCHAR},
      process_time = #{record.processTime,jdbcType=TIMESTAMP},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      `status` = #{record.status,jdbcType=TINYINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.iscm.entity.UnmanageGoodsReturnProcessDetail">
    update unmanage_goods_return_process_detail
    <set>
      <if test="taskId != null">
        task_id = #{taskId,jdbcType=BIGINT},
      </if>
      <if test="taskCode != null">
        task_code = #{taskCode,jdbcType=VARCHAR},
      </if>
      <if test="storeOrgId != null">
        store_org_id = #{storeOrgId,jdbcType=BIGINT},
      </if>
      <if test="storeCode != null">
        store_code = #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="storeName != null">
        store_name = #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="companyOrgId != null">
        company_org_id = #{companyOrgId,jdbcType=BIGINT},
      </if>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="goodsCount != null">
        goods_count = #{goodsCount,jdbcType=INTEGER},
      </if>
      <if test="returnQty != null">
        return_qty = #{returnQty,jdbcType=DECIMAL},
      </if>
      <if test="returnAmount != null">
        return_amount = #{returnAmount,jdbcType=DECIMAL},
      </if>
      <if test="returnOrderCount != null">
        return_order_count = #{returnOrderCount,jdbcType=INTEGER},
      </if>
      <if test="returnOrderNos != null">
        return_order_nos = #{returnOrderNos,jdbcType=VARCHAR},
      </if>
      <if test="processStatus != null">
        process_status = #{processStatus,jdbcType=TINYINT},
      </if>
      <if test="errorMsg != null">
        error_msg = #{errorMsg,jdbcType=VARCHAR},
      </if>
      <if test="processTime != null">
        process_time = #{processTime,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.iscm.entity.UnmanageGoodsReturnProcessDetail">
    update unmanage_goods_return_process_detail
    set task_id = #{taskId,jdbcType=BIGINT},
      task_code = #{taskCode,jdbcType=VARCHAR},
      store_org_id = #{storeOrgId,jdbcType=BIGINT},
      store_code = #{storeCode,jdbcType=VARCHAR},
      store_name = #{storeName,jdbcType=VARCHAR},
      company_org_id = #{companyOrgId,jdbcType=BIGINT},
      company_code = #{companyCode,jdbcType=VARCHAR},
      company_name = #{companyName,jdbcType=VARCHAR},
      goods_count = #{goodsCount,jdbcType=INTEGER},
      return_qty = #{returnQty,jdbcType=DECIMAL},
      return_amount = #{returnAmount,jdbcType=DECIMAL},
      return_order_count = #{returnOrderCount,jdbcType=INTEGER},
      return_order_nos = #{returnOrderNos,jdbcType=VARCHAR},
      process_status = #{processStatus,jdbcType=TINYINT},
      error_msg = #{errorMsg,jdbcType=VARCHAR},
      process_time = #{processTime,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      `status` = #{status,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>