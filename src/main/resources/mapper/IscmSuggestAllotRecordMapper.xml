<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapper.IscmSuggestAllotRecordMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entity.IscmSuggestAllotRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="business_date" jdbcType="TIMESTAMP" property="businessDate" />
    <result column="allot_type" jdbcType="TINYINT" property="allotType" />
    <result column="register_no" jdbcType="VARCHAR" property="registerNo" />
    <result column="register_source" jdbcType="TINYINT" property="registerSource" />
    <result column="register_by" jdbcType="BIGINT" property="registerBy" />
    <result column="register_name" jdbcType="VARCHAR" property="registerName" />
    <result column="register_time" jdbcType="TIMESTAMP" property="registerTime" />
    <result column="out_company_id" jdbcType="BIGINT" property="outCompanyId" />
    <result column="out_company_code" jdbcType="VARCHAR" property="outCompanyCode" />
    <result column="out_company_name" jdbcType="VARCHAR" property="outCompanyName" />
    <result column="in_company_id" jdbcType="BIGINT" property="inCompanyId" />
    <result column="in_company_code" jdbcType="VARCHAR" property="inCompanyCode" />
    <result column="in_company_name" jdbcType="VARCHAR" property="inCompanyName" />
    <result column="out_store_id" jdbcType="BIGINT" property="outStoreId" />
    <result column="out_store_code" jdbcType="VARCHAR" property="outStoreCode" />
    <result column="out_store_name" jdbcType="VARCHAR" property="outStoreName" />
    <result column="out_store_attr" jdbcType="VARCHAR" property="outStoreAttr" />
    <result column="out_store_sales_level" jdbcType="VARCHAR" property="outStoreSalesLevel" />
    <result column="in_store_id" jdbcType="BIGINT" property="inStoreId" />
    <result column="in_store_code" jdbcType="VARCHAR" property="inStoreCode" />
    <result column="in_store_name" jdbcType="VARCHAR" property="inStoreName" />
    <result column="in_store_attr" jdbcType="VARCHAR" property="inStoreAttr" />
    <result column="in_store_sales_level" jdbcType="VARCHAR" property="inStoreSalesLevel" />
    <result column="allot_group_code" jdbcType="VARCHAR" property="allotGroupCode" />
    <result column="allot_group_name" jdbcType="VARCHAR" property="allotGroupName" />
    <result column="suggest_allot_quantity_all" jdbcType="DECIMAL" property="suggestAllotQuantityAll" />
    <result column="suggest_allot_base_cost" jdbcType="DECIMAL" property="suggestAllotBaseCost" />
    <result column="suggest_allot_breed_all" jdbcType="INTEGER" property="suggestAllotBreedAll" />
    <result column="model_code" jdbcType="VARCHAR" property="modelCode" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, business_date, allot_type, register_no, register_source, register_by, register_name, 
    register_time, out_company_id, out_company_code, out_company_name, in_company_id, 
    in_company_code, in_company_name, out_store_id, out_store_code, out_store_name, out_store_attr, 
    out_store_sales_level, in_store_id, in_store_code, in_store_name, in_store_attr, 
    in_store_sales_level, allot_group_code, allot_group_name, suggest_allot_quantity_all, 
    suggest_allot_base_cost, suggest_allot_breed_all, model_code, `status`, gmt_create, 
    gmt_update, extend, version, created_by, created_name, updated_by, updated_name
  </sql>
  <select id="selectByExample" parameterType="com.cowell.iscm.entity.IscmSuggestAllotRecordExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from iscm_suggest_allot_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from iscm_suggest_allot_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from iscm_suggest_allot_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.iscm.entity.IscmSuggestAllotRecordExample">
    delete from iscm_suggest_allot_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entity.IscmSuggestAllotRecord" useGeneratedKeys="true">
    insert into iscm_suggest_allot_record (business_date, allot_type, register_no, 
      register_source, register_by, register_name, 
      register_time, out_company_id, out_company_code, 
      out_company_name, in_company_id, in_company_code, 
      in_company_name, out_store_id, out_store_code, 
      out_store_name, out_store_attr, out_store_sales_level, 
      in_store_id, in_store_code, in_store_name, 
      in_store_attr, in_store_sales_level, allot_group_code, 
      allot_group_name, suggest_allot_quantity_all, 
      suggest_allot_base_cost, suggest_allot_breed_all, 
      model_code, `status`, gmt_create, 
      gmt_update, extend, version, 
      created_by, created_name, updated_by, 
      updated_name)
    values (#{businessDate,jdbcType=TIMESTAMP}, #{allotType,jdbcType=TINYINT}, #{registerNo,jdbcType=VARCHAR}, 
      #{registerSource,jdbcType=TINYINT}, #{registerBy,jdbcType=BIGINT}, #{registerName,jdbcType=VARCHAR}, 
      #{registerTime,jdbcType=TIMESTAMP}, #{outCompanyId,jdbcType=BIGINT}, #{outCompanyCode,jdbcType=VARCHAR}, 
      #{outCompanyName,jdbcType=VARCHAR}, #{inCompanyId,jdbcType=BIGINT}, #{inCompanyCode,jdbcType=VARCHAR}, 
      #{inCompanyName,jdbcType=VARCHAR}, #{outStoreId,jdbcType=BIGINT}, #{outStoreCode,jdbcType=VARCHAR}, 
      #{outStoreName,jdbcType=VARCHAR}, #{outStoreAttr,jdbcType=VARCHAR}, #{outStoreSalesLevel,jdbcType=VARCHAR}, 
      #{inStoreId,jdbcType=BIGINT}, #{inStoreCode,jdbcType=VARCHAR}, #{inStoreName,jdbcType=VARCHAR}, 
      #{inStoreAttr,jdbcType=VARCHAR}, #{inStoreSalesLevel,jdbcType=VARCHAR}, #{allotGroupCode,jdbcType=VARCHAR}, 
      #{allotGroupName,jdbcType=VARCHAR}, #{suggestAllotQuantityAll,jdbcType=DECIMAL}, 
      #{suggestAllotBaseCost,jdbcType=DECIMAL}, #{suggestAllotBreedAll,jdbcType=INTEGER}, 
      #{modelCode,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtUpdate,jdbcType=TIMESTAMP}, #{extend,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER}, 
      #{createdBy,jdbcType=BIGINT}, #{createdName,jdbcType=VARCHAR}, #{updatedBy,jdbcType=BIGINT}, 
      #{updatedName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entity.IscmSuggestAllotRecord" useGeneratedKeys="true">
    insert into iscm_suggest_allot_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessDate != null">
        business_date,
      </if>
      <if test="allotType != null">
        allot_type,
      </if>
      <if test="registerNo != null">
        register_no,
      </if>
      <if test="registerSource != null">
        register_source,
      </if>
      <if test="registerBy != null">
        register_by,
      </if>
      <if test="registerName != null">
        register_name,
      </if>
      <if test="registerTime != null">
        register_time,
      </if>
      <if test="outCompanyId != null">
        out_company_id,
      </if>
      <if test="outCompanyCode != null">
        out_company_code,
      </if>
      <if test="outCompanyName != null">
        out_company_name,
      </if>
      <if test="inCompanyId != null">
        in_company_id,
      </if>
      <if test="inCompanyCode != null">
        in_company_code,
      </if>
      <if test="inCompanyName != null">
        in_company_name,
      </if>
      <if test="outStoreId != null">
        out_store_id,
      </if>
      <if test="outStoreCode != null">
        out_store_code,
      </if>
      <if test="outStoreName != null">
        out_store_name,
      </if>
      <if test="outStoreAttr != null">
        out_store_attr,
      </if>
      <if test="outStoreSalesLevel != null">
        out_store_sales_level,
      </if>
      <if test="inStoreId != null">
        in_store_id,
      </if>
      <if test="inStoreCode != null">
        in_store_code,
      </if>
      <if test="inStoreName != null">
        in_store_name,
      </if>
      <if test="inStoreAttr != null">
        in_store_attr,
      </if>
      <if test="inStoreSalesLevel != null">
        in_store_sales_level,
      </if>
      <if test="allotGroupCode != null">
        allot_group_code,
      </if>
      <if test="allotGroupName != null">
        allot_group_name,
      </if>
      <if test="suggestAllotQuantityAll != null">
        suggest_allot_quantity_all,
      </if>
      <if test="suggestAllotBaseCost != null">
        suggest_allot_base_cost,
      </if>
      <if test="suggestAllotBreedAll != null">
        suggest_allot_breed_all,
      </if>
      <if test="modelCode != null">
        model_code,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedName != null">
        updated_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessDate != null">
        #{businessDate,jdbcType=TIMESTAMP},
      </if>
      <if test="allotType != null">
        #{allotType,jdbcType=TINYINT},
      </if>
      <if test="registerNo != null">
        #{registerNo,jdbcType=VARCHAR},
      </if>
      <if test="registerSource != null">
        #{registerSource,jdbcType=TINYINT},
      </if>
      <if test="registerBy != null">
        #{registerBy,jdbcType=BIGINT},
      </if>
      <if test="registerName != null">
        #{registerName,jdbcType=VARCHAR},
      </if>
      <if test="registerTime != null">
        #{registerTime,jdbcType=TIMESTAMP},
      </if>
      <if test="outCompanyId != null">
        #{outCompanyId,jdbcType=BIGINT},
      </if>
      <if test="outCompanyCode != null">
        #{outCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="outCompanyName != null">
        #{outCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="inCompanyId != null">
        #{inCompanyId,jdbcType=BIGINT},
      </if>
      <if test="inCompanyCode != null">
        #{inCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="inCompanyName != null">
        #{inCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="outStoreId != null">
        #{outStoreId,jdbcType=BIGINT},
      </if>
      <if test="outStoreCode != null">
        #{outStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="outStoreName != null">
        #{outStoreName,jdbcType=VARCHAR},
      </if>
      <if test="outStoreAttr != null">
        #{outStoreAttr,jdbcType=VARCHAR},
      </if>
      <if test="outStoreSalesLevel != null">
        #{outStoreSalesLevel,jdbcType=VARCHAR},
      </if>
      <if test="inStoreId != null">
        #{inStoreId,jdbcType=BIGINT},
      </if>
      <if test="inStoreCode != null">
        #{inStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="inStoreName != null">
        #{inStoreName,jdbcType=VARCHAR},
      </if>
      <if test="inStoreAttr != null">
        #{inStoreAttr,jdbcType=VARCHAR},
      </if>
      <if test="inStoreSalesLevel != null">
        #{inStoreSalesLevel,jdbcType=VARCHAR},
      </if>
      <if test="allotGroupCode != null">
        #{allotGroupCode,jdbcType=VARCHAR},
      </if>
      <if test="allotGroupName != null">
        #{allotGroupName,jdbcType=VARCHAR},
      </if>
      <if test="suggestAllotQuantityAll != null">
        #{suggestAllotQuantityAll,jdbcType=DECIMAL},
      </if>
      <if test="suggestAllotBaseCost != null">
        #{suggestAllotBaseCost,jdbcType=DECIMAL},
      </if>
      <if test="suggestAllotBreedAll != null">
        #{suggestAllotBreedAll,jdbcType=INTEGER},
      </if>
      <if test="modelCode != null">
        #{modelCode,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        #{updatedName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.iscm.entity.IscmSuggestAllotRecordExample" resultType="java.lang.Long">
    select count(*) from iscm_suggest_allot_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update iscm_suggest_allot_record
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.businessDate != null">
        business_date = #{record.businessDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.allotType != null">
        allot_type = #{record.allotType,jdbcType=TINYINT},
      </if>
      <if test="record.registerNo != null">
        register_no = #{record.registerNo,jdbcType=VARCHAR},
      </if>
      <if test="record.registerSource != null">
        register_source = #{record.registerSource,jdbcType=TINYINT},
      </if>
      <if test="record.registerBy != null">
        register_by = #{record.registerBy,jdbcType=BIGINT},
      </if>
      <if test="record.registerName != null">
        register_name = #{record.registerName,jdbcType=VARCHAR},
      </if>
      <if test="record.registerTime != null">
        register_time = #{record.registerTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.outCompanyId != null">
        out_company_id = #{record.outCompanyId,jdbcType=BIGINT},
      </if>
      <if test="record.outCompanyCode != null">
        out_company_code = #{record.outCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.outCompanyName != null">
        out_company_name = #{record.outCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="record.inCompanyId != null">
        in_company_id = #{record.inCompanyId,jdbcType=BIGINT},
      </if>
      <if test="record.inCompanyCode != null">
        in_company_code = #{record.inCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.inCompanyName != null">
        in_company_name = #{record.inCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="record.outStoreId != null">
        out_store_id = #{record.outStoreId,jdbcType=BIGINT},
      </if>
      <if test="record.outStoreCode != null">
        out_store_code = #{record.outStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="record.outStoreName != null">
        out_store_name = #{record.outStoreName,jdbcType=VARCHAR},
      </if>
      <if test="record.outStoreAttr != null">
        out_store_attr = #{record.outStoreAttr,jdbcType=VARCHAR},
      </if>
      <if test="record.outStoreSalesLevel != null">
        out_store_sales_level = #{record.outStoreSalesLevel,jdbcType=VARCHAR},
      </if>
      <if test="record.inStoreId != null">
        in_store_id = #{record.inStoreId,jdbcType=BIGINT},
      </if>
      <if test="record.inStoreCode != null">
        in_store_code = #{record.inStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="record.inStoreName != null">
        in_store_name = #{record.inStoreName,jdbcType=VARCHAR},
      </if>
      <if test="record.inStoreAttr != null">
        in_store_attr = #{record.inStoreAttr,jdbcType=VARCHAR},
      </if>
      <if test="record.inStoreSalesLevel != null">
        in_store_sales_level = #{record.inStoreSalesLevel,jdbcType=VARCHAR},
      </if>
      <if test="record.allotGroupCode != null">
        allot_group_code = #{record.allotGroupCode,jdbcType=VARCHAR},
      </if>
      <if test="record.allotGroupName != null">
        allot_group_name = #{record.allotGroupName,jdbcType=VARCHAR},
      </if>
      <if test="record.suggestAllotQuantityAll != null">
        suggest_allot_quantity_all = #{record.suggestAllotQuantityAll,jdbcType=DECIMAL},
      </if>
      <if test="record.suggestAllotBaseCost != null">
        suggest_allot_base_cost = #{record.suggestAllotBaseCost,jdbcType=DECIMAL},
      </if>
      <if test="record.suggestAllotBreedAll != null">
        suggest_allot_breed_all = #{record.suggestAllotBreedAll,jdbcType=INTEGER},
      </if>
      <if test="record.modelCode != null">
        model_code = #{record.modelCode,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedName != null">
        updated_name = #{record.updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update iscm_suggest_allot_record
    set id = #{record.id,jdbcType=BIGINT},
      business_date = #{record.businessDate,jdbcType=TIMESTAMP},
      allot_type = #{record.allotType,jdbcType=TINYINT},
      register_no = #{record.registerNo,jdbcType=VARCHAR},
      register_source = #{record.registerSource,jdbcType=TINYINT},
      register_by = #{record.registerBy,jdbcType=BIGINT},
      register_name = #{record.registerName,jdbcType=VARCHAR},
      register_time = #{record.registerTime,jdbcType=TIMESTAMP},
      out_company_id = #{record.outCompanyId,jdbcType=BIGINT},
      out_company_code = #{record.outCompanyCode,jdbcType=VARCHAR},
      out_company_name = #{record.outCompanyName,jdbcType=VARCHAR},
      in_company_id = #{record.inCompanyId,jdbcType=BIGINT},
      in_company_code = #{record.inCompanyCode,jdbcType=VARCHAR},
      in_company_name = #{record.inCompanyName,jdbcType=VARCHAR},
      out_store_id = #{record.outStoreId,jdbcType=BIGINT},
      out_store_code = #{record.outStoreCode,jdbcType=VARCHAR},
      out_store_name = #{record.outStoreName,jdbcType=VARCHAR},
      out_store_attr = #{record.outStoreAttr,jdbcType=VARCHAR},
      out_store_sales_level = #{record.outStoreSalesLevel,jdbcType=VARCHAR},
      in_store_id = #{record.inStoreId,jdbcType=BIGINT},
      in_store_code = #{record.inStoreCode,jdbcType=VARCHAR},
      in_store_name = #{record.inStoreName,jdbcType=VARCHAR},
      in_store_attr = #{record.inStoreAttr,jdbcType=VARCHAR},
      in_store_sales_level = #{record.inStoreSalesLevel,jdbcType=VARCHAR},
      allot_group_code = #{record.allotGroupCode,jdbcType=VARCHAR},
      allot_group_name = #{record.allotGroupName,jdbcType=VARCHAR},
      suggest_allot_quantity_all = #{record.suggestAllotQuantityAll,jdbcType=DECIMAL},
      suggest_allot_base_cost = #{record.suggestAllotBaseCost,jdbcType=DECIMAL},
      suggest_allot_breed_all = #{record.suggestAllotBreedAll,jdbcType=INTEGER},
      model_code = #{record.modelCode,jdbcType=VARCHAR},
      `status` = #{record.status,jdbcType=TINYINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_name = #{record.createdName,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_name = #{record.updatedName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.iscm.entity.IscmSuggestAllotRecord">
    update iscm_suggest_allot_record
    <set>
      <if test="businessDate != null">
        business_date = #{businessDate,jdbcType=TIMESTAMP},
      </if>
      <if test="allotType != null">
        allot_type = #{allotType,jdbcType=TINYINT},
      </if>
      <if test="registerNo != null">
        register_no = #{registerNo,jdbcType=VARCHAR},
      </if>
      <if test="registerSource != null">
        register_source = #{registerSource,jdbcType=TINYINT},
      </if>
      <if test="registerBy != null">
        register_by = #{registerBy,jdbcType=BIGINT},
      </if>
      <if test="registerName != null">
        register_name = #{registerName,jdbcType=VARCHAR},
      </if>
      <if test="registerTime != null">
        register_time = #{registerTime,jdbcType=TIMESTAMP},
      </if>
      <if test="outCompanyId != null">
        out_company_id = #{outCompanyId,jdbcType=BIGINT},
      </if>
      <if test="outCompanyCode != null">
        out_company_code = #{outCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="outCompanyName != null">
        out_company_name = #{outCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="inCompanyId != null">
        in_company_id = #{inCompanyId,jdbcType=BIGINT},
      </if>
      <if test="inCompanyCode != null">
        in_company_code = #{inCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="inCompanyName != null">
        in_company_name = #{inCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="outStoreId != null">
        out_store_id = #{outStoreId,jdbcType=BIGINT},
      </if>
      <if test="outStoreCode != null">
        out_store_code = #{outStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="outStoreName != null">
        out_store_name = #{outStoreName,jdbcType=VARCHAR},
      </if>
      <if test="outStoreAttr != null">
        out_store_attr = #{outStoreAttr,jdbcType=VARCHAR},
      </if>
      <if test="outStoreSalesLevel != null">
        out_store_sales_level = #{outStoreSalesLevel,jdbcType=VARCHAR},
      </if>
      <if test="inStoreId != null">
        in_store_id = #{inStoreId,jdbcType=BIGINT},
      </if>
      <if test="inStoreCode != null">
        in_store_code = #{inStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="inStoreName != null">
        in_store_name = #{inStoreName,jdbcType=VARCHAR},
      </if>
      <if test="inStoreAttr != null">
        in_store_attr = #{inStoreAttr,jdbcType=VARCHAR},
      </if>
      <if test="inStoreSalesLevel != null">
        in_store_sales_level = #{inStoreSalesLevel,jdbcType=VARCHAR},
      </if>
      <if test="allotGroupCode != null">
        allot_group_code = #{allotGroupCode,jdbcType=VARCHAR},
      </if>
      <if test="allotGroupName != null">
        allot_group_name = #{allotGroupName,jdbcType=VARCHAR},
      </if>
      <if test="suggestAllotQuantityAll != null">
        suggest_allot_quantity_all = #{suggestAllotQuantityAll,jdbcType=DECIMAL},
      </if>
      <if test="suggestAllotBaseCost != null">
        suggest_allot_base_cost = #{suggestAllotBaseCost,jdbcType=DECIMAL},
      </if>
      <if test="suggestAllotBreedAll != null">
        suggest_allot_breed_all = #{suggestAllotBreedAll,jdbcType=INTEGER},
      </if>
      <if test="modelCode != null">
        model_code = #{modelCode,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        updated_name = #{updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.iscm.entity.IscmSuggestAllotRecord">
    update iscm_suggest_allot_record
    set business_date = #{businessDate,jdbcType=TIMESTAMP},
      allot_type = #{allotType,jdbcType=TINYINT},
      register_no = #{registerNo,jdbcType=VARCHAR},
      register_source = #{registerSource,jdbcType=TINYINT},
      register_by = #{registerBy,jdbcType=BIGINT},
      register_name = #{registerName,jdbcType=VARCHAR},
      register_time = #{registerTime,jdbcType=TIMESTAMP},
      out_company_id = #{outCompanyId,jdbcType=BIGINT},
      out_company_code = #{outCompanyCode,jdbcType=VARCHAR},
      out_company_name = #{outCompanyName,jdbcType=VARCHAR},
      in_company_id = #{inCompanyId,jdbcType=BIGINT},
      in_company_code = #{inCompanyCode,jdbcType=VARCHAR},
      in_company_name = #{inCompanyName,jdbcType=VARCHAR},
      out_store_id = #{outStoreId,jdbcType=BIGINT},
      out_store_code = #{outStoreCode,jdbcType=VARCHAR},
      out_store_name = #{outStoreName,jdbcType=VARCHAR},
      out_store_attr = #{outStoreAttr,jdbcType=VARCHAR},
      out_store_sales_level = #{outStoreSalesLevel,jdbcType=VARCHAR},
      in_store_id = #{inStoreId,jdbcType=BIGINT},
      in_store_code = #{inStoreCode,jdbcType=VARCHAR},
      in_store_name = #{inStoreName,jdbcType=VARCHAR},
      in_store_attr = #{inStoreAttr,jdbcType=VARCHAR},
      in_store_sales_level = #{inStoreSalesLevel,jdbcType=VARCHAR},
      allot_group_code = #{allotGroupCode,jdbcType=VARCHAR},
      allot_group_name = #{allotGroupName,jdbcType=VARCHAR},
      suggest_allot_quantity_all = #{suggestAllotQuantityAll,jdbcType=DECIMAL},
      suggest_allot_base_cost = #{suggestAllotBaseCost,jdbcType=DECIMAL},
      suggest_allot_breed_all = #{suggestAllotBreedAll,jdbcType=INTEGER},
      model_code = #{modelCode,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_name = #{createdName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_name = #{updatedName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>