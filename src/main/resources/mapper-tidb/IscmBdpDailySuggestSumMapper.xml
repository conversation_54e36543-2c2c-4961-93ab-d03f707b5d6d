<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapperTidb.IscmBdpDailySuggestSumMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entityTidb.IscmBdpDailySuggestSum">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="platform_name" jdbcType="VARCHAR" property="platformName" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="bdp_interface_count" jdbcType="INTEGER" property="bdpInterfaceCount" />
    <result column="pos_interface_count" jdbcType="INTEGER" property="posInterfaceCount" />
    <result column="pos_business_count" jdbcType="INTEGER" property="posBusinessCount" />
    <result column="bdp_pos_interface_diff_count" jdbcType="INTEGER" property="bdpPosInterfaceDiffCount" />
    <result column="pos_interface_business_diff_count" jdbcType="INTEGER" property="posInterfaceBusinessDiffCount" />
    <result column="diff_count" jdbcType="INTEGER" property="diffCount" />
    <result column="dt" jdbcType="VARCHAR" property="dt" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, platform_name, company_code, company_name, bdp_interface_count, pos_interface_count, 
    pos_business_count, bdp_pos_interface_diff_count, pos_interface_business_diff_count, 
    diff_count, dt
  </sql>
  <select id="selectByExample" parameterType="com.cowell.iscm.entityTidb.IscmBdpDailySuggestSumExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from iscm_bdp_daily_suggest_sum
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from iscm_bdp_daily_suggest_sum
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from iscm_bdp_daily_suggest_sum
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.iscm.entityTidb.IscmBdpDailySuggestSumExample">
    delete from iscm_bdp_daily_suggest_sum
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.iscm.entityTidb.IscmBdpDailySuggestSum">
    insert into iscm_bdp_daily_suggest_sum (id, platform_name, company_code, 
      company_name, bdp_interface_count, pos_interface_count, 
      pos_business_count, bdp_pos_interface_diff_count, 
      pos_interface_business_diff_count, diff_count, 
      dt)
    values (#{id,jdbcType=BIGINT}, #{platformName,jdbcType=VARCHAR}, #{companyCode,jdbcType=VARCHAR}, 
      #{companyName,jdbcType=VARCHAR}, #{bdpInterfaceCount,jdbcType=INTEGER}, #{posInterfaceCount,jdbcType=INTEGER}, 
      #{posBusinessCount,jdbcType=INTEGER}, #{bdpPosInterfaceDiffCount,jdbcType=INTEGER}, 
      #{posInterfaceBusinessDiffCount,jdbcType=INTEGER}, #{diffCount,jdbcType=INTEGER}, 
      #{dt,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.iscm.entityTidb.IscmBdpDailySuggestSum">
    insert into iscm_bdp_daily_suggest_sum
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="platformName != null">
        platform_name,
      </if>
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="companyName != null">
        company_name,
      </if>
      <if test="bdpInterfaceCount != null">
        bdp_interface_count,
      </if>
      <if test="posInterfaceCount != null">
        pos_interface_count,
      </if>
      <if test="posBusinessCount != null">
        pos_business_count,
      </if>
      <if test="bdpPosInterfaceDiffCount != null">
        bdp_pos_interface_diff_count,
      </if>
      <if test="posInterfaceBusinessDiffCount != null">
        pos_interface_business_diff_count,
      </if>
      <if test="diffCount != null">
        diff_count,
      </if>
      <if test="dt != null">
        dt,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="platformName != null">
        #{platformName,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="bdpInterfaceCount != null">
        #{bdpInterfaceCount,jdbcType=INTEGER},
      </if>
      <if test="posInterfaceCount != null">
        #{posInterfaceCount,jdbcType=INTEGER},
      </if>
      <if test="posBusinessCount != null">
        #{posBusinessCount,jdbcType=INTEGER},
      </if>
      <if test="bdpPosInterfaceDiffCount != null">
        #{bdpPosInterfaceDiffCount,jdbcType=INTEGER},
      </if>
      <if test="posInterfaceBusinessDiffCount != null">
        #{posInterfaceBusinessDiffCount,jdbcType=INTEGER},
      </if>
      <if test="diffCount != null">
        #{diffCount,jdbcType=INTEGER},
      </if>
      <if test="dt != null">
        #{dt,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.iscm.entityTidb.IscmBdpDailySuggestSumExample" resultType="java.lang.Long">
    select count(*) from iscm_bdp_daily_suggest_sum
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update iscm_bdp_daily_suggest_sum
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.platformName != null">
        platform_name = #{record.platformName,jdbcType=VARCHAR},
      </if>
      <if test="record.companyCode != null">
        company_code = #{record.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.companyName != null">
        company_name = #{record.companyName,jdbcType=VARCHAR},
      </if>
      <if test="record.bdpInterfaceCount != null">
        bdp_interface_count = #{record.bdpInterfaceCount,jdbcType=INTEGER},
      </if>
      <if test="record.posInterfaceCount != null">
        pos_interface_count = #{record.posInterfaceCount,jdbcType=INTEGER},
      </if>
      <if test="record.posBusinessCount != null">
        pos_business_count = #{record.posBusinessCount,jdbcType=INTEGER},
      </if>
      <if test="record.bdpPosInterfaceDiffCount != null">
        bdp_pos_interface_diff_count = #{record.bdpPosInterfaceDiffCount,jdbcType=INTEGER},
      </if>
      <if test="record.posInterfaceBusinessDiffCount != null">
        pos_interface_business_diff_count = #{record.posInterfaceBusinessDiffCount,jdbcType=INTEGER},
      </if>
      <if test="record.diffCount != null">
        diff_count = #{record.diffCount,jdbcType=INTEGER},
      </if>
      <if test="record.dt != null">
        dt = #{record.dt,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update iscm_bdp_daily_suggest_sum
    set id = #{record.id,jdbcType=BIGINT},
      platform_name = #{record.platformName,jdbcType=VARCHAR},
      company_code = #{record.companyCode,jdbcType=VARCHAR},
      company_name = #{record.companyName,jdbcType=VARCHAR},
      bdp_interface_count = #{record.bdpInterfaceCount,jdbcType=INTEGER},
      pos_interface_count = #{record.posInterfaceCount,jdbcType=INTEGER},
      pos_business_count = #{record.posBusinessCount,jdbcType=INTEGER},
      bdp_pos_interface_diff_count = #{record.bdpPosInterfaceDiffCount,jdbcType=INTEGER},
      pos_interface_business_diff_count = #{record.posInterfaceBusinessDiffCount,jdbcType=INTEGER},
      diff_count = #{record.diffCount,jdbcType=INTEGER},
      dt = #{record.dt,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.iscm.entityTidb.IscmBdpDailySuggestSum">
    update iscm_bdp_daily_suggest_sum
    <set>
      <if test="platformName != null">
        platform_name = #{platformName,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="bdpInterfaceCount != null">
        bdp_interface_count = #{bdpInterfaceCount,jdbcType=INTEGER},
      </if>
      <if test="posInterfaceCount != null">
        pos_interface_count = #{posInterfaceCount,jdbcType=INTEGER},
      </if>
      <if test="posBusinessCount != null">
        pos_business_count = #{posBusinessCount,jdbcType=INTEGER},
      </if>
      <if test="bdpPosInterfaceDiffCount != null">
        bdp_pos_interface_diff_count = #{bdpPosInterfaceDiffCount,jdbcType=INTEGER},
      </if>
      <if test="posInterfaceBusinessDiffCount != null">
        pos_interface_business_diff_count = #{posInterfaceBusinessDiffCount,jdbcType=INTEGER},
      </if>
      <if test="diffCount != null">
        diff_count = #{diffCount,jdbcType=INTEGER},
      </if>
      <if test="dt != null">
        dt = #{dt,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.iscm.entityTidb.IscmBdpDailySuggestSum">
    update iscm_bdp_daily_suggest_sum
    set platform_name = #{platformName,jdbcType=VARCHAR},
      company_code = #{companyCode,jdbcType=VARCHAR},
      company_name = #{companyName,jdbcType=VARCHAR},
      bdp_interface_count = #{bdpInterfaceCount,jdbcType=INTEGER},
      pos_interface_count = #{posInterfaceCount,jdbcType=INTEGER},
      pos_business_count = #{posBusinessCount,jdbcType=INTEGER},
      bdp_pos_interface_diff_count = #{bdpPosInterfaceDiffCount,jdbcType=INTEGER},
      pos_interface_business_diff_count = #{posInterfaceBusinessDiffCount,jdbcType=INTEGER},
      diff_count = #{diffCount,jdbcType=INTEGER},
      dt = #{dt,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>