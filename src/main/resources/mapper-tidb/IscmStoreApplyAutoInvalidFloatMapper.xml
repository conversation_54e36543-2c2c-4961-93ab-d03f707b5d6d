<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapperTidb.IscmStoreApplyAutoInvalidFloatMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entityTidb.IscmStoreApplyAutoInvalidFloat">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="param_code" jdbcType="VARCHAR" property="paramCode" />
    <result column="param_name" jdbcType="VARCHAR" property="paramName" />
    <result column="param_level" jdbcType="INTEGER" property="paramLevel" />
    <result column="org_id" jdbcType="BIGINT" property="orgId" />
    <result column="sap_code" jdbcType="VARCHAR" property="sapCode" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="parent_org_id" jdbcType="BIGINT" property="parentOrgId" />
    <result column="parent_org_name" jdbcType="VARCHAR" property="parentOrgName" />
    <result column="store_org_id" jdbcType="BIGINT" property="storeOrgId" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="store_name" jdbcType="VARCHAR" property="storeName" />
    <result column="goods_choose_type" jdbcType="TINYINT" property="goodsChooseType" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="businessId" jdbcType="BIGINT" property="businessid" />
    <result column="bar_code" jdbcType="VARCHAR" property="barCode" />
    <result column="cur_name" jdbcType="VARCHAR" property="curName" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="goods_unit" jdbcType="VARCHAR" property="goodsUnit" />
    <result column="specifications" jdbcType="VARCHAR" property="specifications" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="goodsline" jdbcType="VARCHAR" property="goodsline" />
    <result column="specialattributes" jdbcType="VARCHAR" property="specialattributes" />
    <result column="goods_level" jdbcType="TINYINT" property="goodsLevel" />
    <result column="start_date" jdbcType="DATE" property="startDate" />
    <result column="end_date" jdbcType="DATE" property="endDate" />
    <result column="inherit_type" jdbcType="TINYINT" property="inheritType" />
    <result column="effect_status" jdbcType="TINYINT" property="effectStatus" />
    <result column="adjust_type" jdbcType="TINYINT" property="adjustType" />
    <result column="adjust_mode" jdbcType="TINYINT" property="adjustMode" />
    <result column="adjust_upper_quantity" jdbcType="INTEGER" property="adjustUpperQuantity" />
    <result column="adjust_lower_quantity" jdbcType="INTEGER" property="adjustLowerQuantity" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, param_code, param_name, param_level, org_id, sap_code, org_name, parent_org_id, 
    parent_org_name, store_org_id, store_code, store_name, goods_choose_type, goods_no, 
    businessId, bar_code, cur_name, goods_name, goods_unit, specifications, manufacturer, 
    goodsline, specialattributes, goods_level, start_date, end_date, inherit_type, effect_status, 
    adjust_type, adjust_mode, adjust_upper_quantity, adjust_lower_quantity, `status`, 
    extend, version, created_by, created_name, updated_by, updated_name, gmt_create, 
    gmt_update
  </sql>
  <select id="selectByExample" parameterType="com.cowell.iscm.entityTidb.IscmStoreApplyAutoInvalidFloatExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from iscm_store_apply_auto_invalid_float
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from iscm_store_apply_auto_invalid_float
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from iscm_store_apply_auto_invalid_float
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.iscm.entityTidb.IscmStoreApplyAutoInvalidFloatExample">
    delete from iscm_store_apply_auto_invalid_float
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entityTidb.IscmStoreApplyAutoInvalidFloat" useGeneratedKeys="true">
    insert into iscm_store_apply_auto_invalid_float (param_code, param_name, param_level, 
      org_id, sap_code, org_name, 
      parent_org_id, parent_org_name, store_org_id, 
      store_code, store_name, goods_choose_type, 
      goods_no, businessId, bar_code, 
      cur_name, goods_name, goods_unit, 
      specifications, manufacturer, goodsline, 
      specialattributes, goods_level, start_date, 
      end_date, inherit_type, effect_status, 
      adjust_type, adjust_mode, adjust_upper_quantity, 
      adjust_lower_quantity, `status`, extend, 
      version, created_by, created_name, 
      updated_by, updated_name, gmt_create, 
      gmt_update)
    values (#{paramCode,jdbcType=VARCHAR}, #{paramName,jdbcType=VARCHAR}, #{paramLevel,jdbcType=INTEGER}, 
      #{orgId,jdbcType=BIGINT}, #{sapCode,jdbcType=VARCHAR}, #{orgName,jdbcType=VARCHAR}, 
      #{parentOrgId,jdbcType=BIGINT}, #{parentOrgName,jdbcType=VARCHAR}, #{storeOrgId,jdbcType=BIGINT}, 
      #{storeCode,jdbcType=VARCHAR}, #{storeName,jdbcType=VARCHAR}, #{goodsChooseType,jdbcType=TINYINT}, 
      #{goodsNo,jdbcType=VARCHAR}, #{businessid,jdbcType=BIGINT}, #{barCode,jdbcType=VARCHAR}, 
      #{curName,jdbcType=VARCHAR}, #{goodsName,jdbcType=VARCHAR}, #{goodsUnit,jdbcType=VARCHAR}, 
      #{specifications,jdbcType=VARCHAR}, #{manufacturer,jdbcType=VARCHAR}, #{goodsline,jdbcType=VARCHAR}, 
      #{specialattributes,jdbcType=VARCHAR}, #{goodsLevel,jdbcType=TINYINT}, #{startDate,jdbcType=DATE}, 
      #{endDate,jdbcType=DATE}, #{inheritType,jdbcType=TINYINT}, #{effectStatus,jdbcType=TINYINT}, 
      #{adjustType,jdbcType=TINYINT}, #{adjustMode,jdbcType=TINYINT}, #{adjustUpperQuantity,jdbcType=INTEGER}, 
      #{adjustLowerQuantity,jdbcType=INTEGER}, #{status,jdbcType=TINYINT}, #{extend,jdbcType=VARCHAR}, 
      #{version,jdbcType=INTEGER}, #{createdBy,jdbcType=BIGINT}, #{createdName,jdbcType=VARCHAR}, 
      #{updatedBy,jdbcType=BIGINT}, #{updatedName,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtUpdate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entityTidb.IscmStoreApplyAutoInvalidFloat" useGeneratedKeys="true">
    insert into iscm_store_apply_auto_invalid_float
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="paramCode != null">
        param_code,
      </if>
      <if test="paramName != null">
        param_name,
      </if>
      <if test="paramLevel != null">
        param_level,
      </if>
      <if test="orgId != null">
        org_id,
      </if>
      <if test="sapCode != null">
        sap_code,
      </if>
      <if test="orgName != null">
        org_name,
      </if>
      <if test="parentOrgId != null">
        parent_org_id,
      </if>
      <if test="parentOrgName != null">
        parent_org_name,
      </if>
      <if test="storeOrgId != null">
        store_org_id,
      </if>
      <if test="storeCode != null">
        store_code,
      </if>
      <if test="storeName != null">
        store_name,
      </if>
      <if test="goodsChooseType != null">
        goods_choose_type,
      </if>
      <if test="goodsNo != null">
        goods_no,
      </if>
      <if test="businessid != null">
        businessId,
      </if>
      <if test="barCode != null">
        bar_code,
      </if>
      <if test="curName != null">
        cur_name,
      </if>
      <if test="goodsName != null">
        goods_name,
      </if>
      <if test="goodsUnit != null">
        goods_unit,
      </if>
      <if test="specifications != null">
        specifications,
      </if>
      <if test="manufacturer != null">
        manufacturer,
      </if>
      <if test="goodsline != null">
        goodsline,
      </if>
      <if test="specialattributes != null">
        specialattributes,
      </if>
      <if test="goodsLevel != null">
        goods_level,
      </if>
      <if test="startDate != null">
        start_date,
      </if>
      <if test="endDate != null">
        end_date,
      </if>
      <if test="inheritType != null">
        inherit_type,
      </if>
      <if test="effectStatus != null">
        effect_status,
      </if>
      <if test="adjustType != null">
        adjust_type,
      </if>
      <if test="adjustMode != null">
        adjust_mode,
      </if>
      <if test="adjustUpperQuantity != null">
        adjust_upper_quantity,
      </if>
      <if test="adjustLowerQuantity != null">
        adjust_lower_quantity,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedName != null">
        updated_name,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="paramCode != null">
        #{paramCode,jdbcType=VARCHAR},
      </if>
      <if test="paramName != null">
        #{paramName,jdbcType=VARCHAR},
      </if>
      <if test="paramLevel != null">
        #{paramLevel,jdbcType=INTEGER},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=BIGINT},
      </if>
      <if test="sapCode != null">
        #{sapCode,jdbcType=VARCHAR},
      </if>
      <if test="orgName != null">
        #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="parentOrgId != null">
        #{parentOrgId,jdbcType=BIGINT},
      </if>
      <if test="parentOrgName != null">
        #{parentOrgName,jdbcType=VARCHAR},
      </if>
      <if test="storeOrgId != null">
        #{storeOrgId,jdbcType=BIGINT},
      </if>
      <if test="storeCode != null">
        #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="storeName != null">
        #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="goodsChooseType != null">
        #{goodsChooseType,jdbcType=TINYINT},
      </if>
      <if test="goodsNo != null">
        #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="businessid != null">
        #{businessid,jdbcType=BIGINT},
      </if>
      <if test="barCode != null">
        #{barCode,jdbcType=VARCHAR},
      </if>
      <if test="curName != null">
        #{curName,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="goodsUnit != null">
        #{goodsUnit,jdbcType=VARCHAR},
      </if>
      <if test="specifications != null">
        #{specifications,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="goodsline != null">
        #{goodsline,jdbcType=VARCHAR},
      </if>
      <if test="specialattributes != null">
        #{specialattributes,jdbcType=VARCHAR},
      </if>
      <if test="goodsLevel != null">
        #{goodsLevel,jdbcType=TINYINT},
      </if>
      <if test="startDate != null">
        #{startDate,jdbcType=DATE},
      </if>
      <if test="endDate != null">
        #{endDate,jdbcType=DATE},
      </if>
      <if test="inheritType != null">
        #{inheritType,jdbcType=TINYINT},
      </if>
      <if test="effectStatus != null">
        #{effectStatus,jdbcType=TINYINT},
      </if>
      <if test="adjustType != null">
        #{adjustType,jdbcType=TINYINT},
      </if>
      <if test="adjustMode != null">
        #{adjustMode,jdbcType=TINYINT},
      </if>
      <if test="adjustUpperQuantity != null">
        #{adjustUpperQuantity,jdbcType=INTEGER},
      </if>
      <if test="adjustLowerQuantity != null">
        #{adjustLowerQuantity,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        #{updatedName,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.iscm.entityTidb.IscmStoreApplyAutoInvalidFloatExample" resultType="java.lang.Long">
    select count(*) from iscm_store_apply_auto_invalid_float
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update iscm_store_apply_auto_invalid_float
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.paramCode != null">
        param_code = #{record.paramCode,jdbcType=VARCHAR},
      </if>
      <if test="record.paramName != null">
        param_name = #{record.paramName,jdbcType=VARCHAR},
      </if>
      <if test="record.paramLevel != null">
        param_level = #{record.paramLevel,jdbcType=INTEGER},
      </if>
      <if test="record.orgId != null">
        org_id = #{record.orgId,jdbcType=BIGINT},
      </if>
      <if test="record.sapCode != null">
        sap_code = #{record.sapCode,jdbcType=VARCHAR},
      </if>
      <if test="record.orgName != null">
        org_name = #{record.orgName,jdbcType=VARCHAR},
      </if>
      <if test="record.parentOrgId != null">
        parent_org_id = #{record.parentOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.parentOrgName != null">
        parent_org_name = #{record.parentOrgName,jdbcType=VARCHAR},
      </if>
      <if test="record.storeOrgId != null">
        store_org_id = #{record.storeOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.storeCode != null">
        store_code = #{record.storeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.storeName != null">
        store_name = #{record.storeName,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsChooseType != null">
        goods_choose_type = #{record.goodsChooseType,jdbcType=TINYINT},
      </if>
      <if test="record.goodsNo != null">
        goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="record.businessid != null">
        businessId = #{record.businessid,jdbcType=BIGINT},
      </if>
      <if test="record.barCode != null">
        bar_code = #{record.barCode,jdbcType=VARCHAR},
      </if>
      <if test="record.curName != null">
        cur_name = #{record.curName,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsName != null">
        goods_name = #{record.goodsName,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsUnit != null">
        goods_unit = #{record.goodsUnit,jdbcType=VARCHAR},
      </if>
      <if test="record.specifications != null">
        specifications = #{record.specifications,jdbcType=VARCHAR},
      </if>
      <if test="record.manufacturer != null">
        manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsline != null">
        goodsline = #{record.goodsline,jdbcType=VARCHAR},
      </if>
      <if test="record.specialattributes != null">
        specialattributes = #{record.specialattributes,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsLevel != null">
        goods_level = #{record.goodsLevel,jdbcType=TINYINT},
      </if>
      <if test="record.startDate != null">
        start_date = #{record.startDate,jdbcType=DATE},
      </if>
      <if test="record.endDate != null">
        end_date = #{record.endDate,jdbcType=DATE},
      </if>
      <if test="record.inheritType != null">
        inherit_type = #{record.inheritType,jdbcType=TINYINT},
      </if>
      <if test="record.effectStatus != null">
        effect_status = #{record.effectStatus,jdbcType=TINYINT},
      </if>
      <if test="record.adjustType != null">
        adjust_type = #{record.adjustType,jdbcType=TINYINT},
      </if>
      <if test="record.adjustMode != null">
        adjust_mode = #{record.adjustMode,jdbcType=TINYINT},
      </if>
      <if test="record.adjustUpperQuantity != null">
        adjust_upper_quantity = #{record.adjustUpperQuantity,jdbcType=INTEGER},
      </if>
      <if test="record.adjustLowerQuantity != null">
        adjust_lower_quantity = #{record.adjustLowerQuantity,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedName != null">
        updated_name = #{record.updatedName,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update iscm_store_apply_auto_invalid_float
    set id = #{record.id,jdbcType=BIGINT},
      param_code = #{record.paramCode,jdbcType=VARCHAR},
      param_name = #{record.paramName,jdbcType=VARCHAR},
      param_level = #{record.paramLevel,jdbcType=INTEGER},
      org_id = #{record.orgId,jdbcType=BIGINT},
      sap_code = #{record.sapCode,jdbcType=VARCHAR},
      org_name = #{record.orgName,jdbcType=VARCHAR},
      parent_org_id = #{record.parentOrgId,jdbcType=BIGINT},
      parent_org_name = #{record.parentOrgName,jdbcType=VARCHAR},
      store_org_id = #{record.storeOrgId,jdbcType=BIGINT},
      store_code = #{record.storeCode,jdbcType=VARCHAR},
      store_name = #{record.storeName,jdbcType=VARCHAR},
      goods_choose_type = #{record.goodsChooseType,jdbcType=TINYINT},
      goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      businessId = #{record.businessid,jdbcType=BIGINT},
      bar_code = #{record.barCode,jdbcType=VARCHAR},
      cur_name = #{record.curName,jdbcType=VARCHAR},
      goods_name = #{record.goodsName,jdbcType=VARCHAR},
      goods_unit = #{record.goodsUnit,jdbcType=VARCHAR},
      specifications = #{record.specifications,jdbcType=VARCHAR},
      manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      goodsline = #{record.goodsline,jdbcType=VARCHAR},
      specialattributes = #{record.specialattributes,jdbcType=VARCHAR},
      goods_level = #{record.goodsLevel,jdbcType=TINYINT},
      start_date = #{record.startDate,jdbcType=DATE},
      end_date = #{record.endDate,jdbcType=DATE},
      inherit_type = #{record.inheritType,jdbcType=TINYINT},
      effect_status = #{record.effectStatus,jdbcType=TINYINT},
      adjust_type = #{record.adjustType,jdbcType=TINYINT},
      adjust_mode = #{record.adjustMode,jdbcType=TINYINT},
      adjust_upper_quantity = #{record.adjustUpperQuantity,jdbcType=INTEGER},
      adjust_lower_quantity = #{record.adjustLowerQuantity,jdbcType=INTEGER},
      `status` = #{record.status,jdbcType=TINYINT},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_name = #{record.createdName,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_name = #{record.updatedName,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.iscm.entityTidb.IscmStoreApplyAutoInvalidFloat">
    update iscm_store_apply_auto_invalid_float
    <set>
      <if test="paramCode != null">
        param_code = #{paramCode,jdbcType=VARCHAR},
      </if>
      <if test="paramName != null">
        param_name = #{paramName,jdbcType=VARCHAR},
      </if>
      <if test="paramLevel != null">
        param_level = #{paramLevel,jdbcType=INTEGER},
      </if>
      <if test="orgId != null">
        org_id = #{orgId,jdbcType=BIGINT},
      </if>
      <if test="sapCode != null">
        sap_code = #{sapCode,jdbcType=VARCHAR},
      </if>
      <if test="orgName != null">
        org_name = #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="parentOrgId != null">
        parent_org_id = #{parentOrgId,jdbcType=BIGINT},
      </if>
      <if test="parentOrgName != null">
        parent_org_name = #{parentOrgName,jdbcType=VARCHAR},
      </if>
      <if test="storeOrgId != null">
        store_org_id = #{storeOrgId,jdbcType=BIGINT},
      </if>
      <if test="storeCode != null">
        store_code = #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="storeName != null">
        store_name = #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="goodsChooseType != null">
        goods_choose_type = #{goodsChooseType,jdbcType=TINYINT},
      </if>
      <if test="goodsNo != null">
        goods_no = #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="businessid != null">
        businessId = #{businessid,jdbcType=BIGINT},
      </if>
      <if test="barCode != null">
        bar_code = #{barCode,jdbcType=VARCHAR},
      </if>
      <if test="curName != null">
        cur_name = #{curName,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        goods_name = #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="goodsUnit != null">
        goods_unit = #{goodsUnit,jdbcType=VARCHAR},
      </if>
      <if test="specifications != null">
        specifications = #{specifications,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        manufacturer = #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="goodsline != null">
        goodsline = #{goodsline,jdbcType=VARCHAR},
      </if>
      <if test="specialattributes != null">
        specialattributes = #{specialattributes,jdbcType=VARCHAR},
      </if>
      <if test="goodsLevel != null">
        goods_level = #{goodsLevel,jdbcType=TINYINT},
      </if>
      <if test="startDate != null">
        start_date = #{startDate,jdbcType=DATE},
      </if>
      <if test="endDate != null">
        end_date = #{endDate,jdbcType=DATE},
      </if>
      <if test="inheritType != null">
        inherit_type = #{inheritType,jdbcType=TINYINT},
      </if>
      <if test="effectStatus != null">
        effect_status = #{effectStatus,jdbcType=TINYINT},
      </if>
      <if test="adjustType != null">
        adjust_type = #{adjustType,jdbcType=TINYINT},
      </if>
      <if test="adjustMode != null">
        adjust_mode = #{adjustMode,jdbcType=TINYINT},
      </if>
      <if test="adjustUpperQuantity != null">
        adjust_upper_quantity = #{adjustUpperQuantity,jdbcType=INTEGER},
      </if>
      <if test="adjustLowerQuantity != null">
        adjust_lower_quantity = #{adjustLowerQuantity,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        updated_name = #{updatedName,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.iscm.entityTidb.IscmStoreApplyAutoInvalidFloat">
    update iscm_store_apply_auto_invalid_float
    set param_code = #{paramCode,jdbcType=VARCHAR},
      param_name = #{paramName,jdbcType=VARCHAR},
      param_level = #{paramLevel,jdbcType=INTEGER},
      org_id = #{orgId,jdbcType=BIGINT},
      sap_code = #{sapCode,jdbcType=VARCHAR},
      org_name = #{orgName,jdbcType=VARCHAR},
      parent_org_id = #{parentOrgId,jdbcType=BIGINT},
      parent_org_name = #{parentOrgName,jdbcType=VARCHAR},
      store_org_id = #{storeOrgId,jdbcType=BIGINT},
      store_code = #{storeCode,jdbcType=VARCHAR},
      store_name = #{storeName,jdbcType=VARCHAR},
      goods_choose_type = #{goodsChooseType,jdbcType=TINYINT},
      goods_no = #{goodsNo,jdbcType=VARCHAR},
      businessId = #{businessid,jdbcType=BIGINT},
      bar_code = #{barCode,jdbcType=VARCHAR},
      cur_name = #{curName,jdbcType=VARCHAR},
      goods_name = #{goodsName,jdbcType=VARCHAR},
      goods_unit = #{goodsUnit,jdbcType=VARCHAR},
      specifications = #{specifications,jdbcType=VARCHAR},
      manufacturer = #{manufacturer,jdbcType=VARCHAR},
      goodsline = #{goodsline,jdbcType=VARCHAR},
      specialattributes = #{specialattributes,jdbcType=VARCHAR},
      goods_level = #{goodsLevel,jdbcType=TINYINT},
      start_date = #{startDate,jdbcType=DATE},
      end_date = #{endDate,jdbcType=DATE},
      inherit_type = #{inheritType,jdbcType=TINYINT},
      effect_status = #{effectStatus,jdbcType=TINYINT},
      adjust_type = #{adjustType,jdbcType=TINYINT},
      adjust_mode = #{adjustMode,jdbcType=TINYINT},
      adjust_upper_quantity = #{adjustUpperQuantity,jdbcType=INTEGER},
      adjust_lower_quantity = #{adjustLowerQuantity,jdbcType=INTEGER},
      `status` = #{status,jdbcType=TINYINT},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_name = #{createdName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_name = #{updatedName,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>