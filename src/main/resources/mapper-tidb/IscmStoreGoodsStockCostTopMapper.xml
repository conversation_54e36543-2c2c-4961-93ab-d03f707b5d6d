<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapperTidb.IscmStoreGoodsStockCostTopMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entityTidb.IscmStoreGoodsStockCostTop">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="company_bdp_code" jdbcType="VARCHAR" property="companyBdpCode" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="store_rank" jdbcType="INTEGER" property="storeRank" />
    <result column="storage_days" jdbcType="INTEGER" property="storageDays" />
    <result column="non_sales_days" jdbcType="INTEGER" property="nonSalesDays" />
    <result column="thirty_sales_quantity" jdbcType="DECIMAL" property="thirtySalesQuantity" />
    <result column="thirty_sales_count" jdbcType="INTEGER" property="thirtySalesCount" />
    <result column="min_display_quantity" jdbcType="DECIMAL" property="minDisplayQuantity" />
    <result column="warehouse_code" jdbcType="VARCHAR" property="warehouseCode" />
    <result column="warehouse_name" jdbcType="VARCHAR" property="warehouseName" />
    <result column="goodsline" jdbcType="VARCHAR" property="goodsline" />
    <result column="pushlevel" jdbcType="VARCHAR" property="pushlevel" />
    <result column="goods_level" jdbcType="INTEGER" property="goodsLevel" />
    <result column="forbid_distribute" jdbcType="VARCHAR" property="forbidDistribute" />
    <result column="forbid_return_warehouse" jdbcType="VARCHAR" property="forbidReturnWarehouse" />
    <result column="forbid_apply" jdbcType="VARCHAR" property="forbidApply" />
    <result column="forbid_allot" jdbcType="VARCHAR" property="forbidAllot" />
    <result column="stock_quantity" jdbcType="DECIMAL" property="stockQuantity" />
    <result column="cost_amount" jdbcType="DECIMAL" property="costAmount" />
    <result column="stock_upper_limit_days" jdbcType="INTEGER" property="stockUpperLimitDays" />
    <result column="stock_lower_limit_days" jdbcType="INTEGER" property="stockLowerLimitDays" />
    <result column="hd_synthesize_average_daily_sales" jdbcType="DECIMAL" property="hdSynthesizeAverageDailySales" />
    <result column="stock_upper_limit" jdbcType="DECIMAL" property="stockUpperLimit" />
    <result column="stock_lower_limit" jdbcType="DECIMAL" property="stockLowerLimit" />
    <result column="bdp_synthesize_average_daily_sales" jdbcType="DECIMAL" property="bdpSynthesizeAverageDailySales" />
    <result column="expect_sale_days" jdbcType="DECIMAL" property="expectSaleDays" />
    <result column="deal_status" jdbcType="TINYINT" property="dealStatus" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, company_code, company_bdp_code, store_code, goods_no, store_rank, storage_days, 
    non_sales_days, thirty_sales_quantity, thirty_sales_count, min_display_quantity, 
    warehouse_code, warehouse_name, goodsline, pushlevel, goods_level, forbid_distribute, 
    forbid_return_warehouse, forbid_apply, forbid_allot, stock_quantity, cost_amount, 
    stock_upper_limit_days, stock_lower_limit_days, hd_synthesize_average_daily_sales, 
    stock_upper_limit, stock_lower_limit, bdp_synthesize_average_daily_sales, expect_sale_days, 
    deal_status, `status`, gmt_create, gmt_update, extend, version, created_by, created_name, 
    updated_by, updated_name
  </sql>
  <select id="selectByExample" parameterType="com.cowell.iscm.entityTidb.IscmStoreGoodsStockCostTopExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from iscm_store_goods_stock_cost_top
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from iscm_store_goods_stock_cost_top
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from iscm_store_goods_stock_cost_top
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.iscm.entityTidb.IscmStoreGoodsStockCostTopExample">
    delete from iscm_store_goods_stock_cost_top
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entityTidb.IscmStoreGoodsStockCostTop" useGeneratedKeys="true">
    insert into iscm_store_goods_stock_cost_top (company_code, company_bdp_code, store_code, 
      goods_no, store_rank, storage_days, 
      non_sales_days, thirty_sales_quantity, thirty_sales_count, 
      min_display_quantity, warehouse_code, warehouse_name, 
      goodsline, pushlevel, goods_level, 
      forbid_distribute, forbid_return_warehouse, 
      forbid_apply, forbid_allot, stock_quantity, 
      cost_amount, stock_upper_limit_days, stock_lower_limit_days, 
      hd_synthesize_average_daily_sales, stock_upper_limit, 
      stock_lower_limit, bdp_synthesize_average_daily_sales, 
      expect_sale_days, deal_status, `status`, 
      gmt_create, gmt_update, extend, 
      version, created_by, created_name, 
      updated_by, updated_name)
    values (#{companyCode,jdbcType=VARCHAR}, #{companyBdpCode,jdbcType=VARCHAR}, #{storeCode,jdbcType=VARCHAR}, 
      #{goodsNo,jdbcType=VARCHAR}, #{storeRank,jdbcType=INTEGER}, #{storageDays,jdbcType=INTEGER}, 
      #{nonSalesDays,jdbcType=INTEGER}, #{thirtySalesQuantity,jdbcType=DECIMAL}, #{thirtySalesCount,jdbcType=INTEGER}, 
      #{minDisplayQuantity,jdbcType=DECIMAL}, #{warehouseCode,jdbcType=VARCHAR}, #{warehouseName,jdbcType=VARCHAR}, 
      #{goodsline,jdbcType=VARCHAR}, #{pushlevel,jdbcType=VARCHAR}, #{goodsLevel,jdbcType=INTEGER}, 
      #{forbidDistribute,jdbcType=VARCHAR}, #{forbidReturnWarehouse,jdbcType=VARCHAR}, 
      #{forbidApply,jdbcType=VARCHAR}, #{forbidAllot,jdbcType=VARCHAR}, #{stockQuantity,jdbcType=DECIMAL}, 
      #{costAmount,jdbcType=DECIMAL}, #{stockUpperLimitDays,jdbcType=INTEGER}, #{stockLowerLimitDays,jdbcType=INTEGER}, 
      #{hdSynthesizeAverageDailySales,jdbcType=DECIMAL}, #{stockUpperLimit,jdbcType=DECIMAL}, 
      #{stockLowerLimit,jdbcType=DECIMAL}, #{bdpSynthesizeAverageDailySales,jdbcType=DECIMAL}, 
      #{expectSaleDays,jdbcType=DECIMAL}, #{dealStatus,jdbcType=TINYINT}, #{status,jdbcType=TINYINT}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}, #{extend,jdbcType=VARCHAR}, 
      #{version,jdbcType=INTEGER}, #{createdBy,jdbcType=BIGINT}, #{createdName,jdbcType=VARCHAR}, 
      #{updatedBy,jdbcType=BIGINT}, #{updatedName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entityTidb.IscmStoreGoodsStockCostTop" useGeneratedKeys="true">
    insert into iscm_store_goods_stock_cost_top
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="companyBdpCode != null">
        company_bdp_code,
      </if>
      <if test="storeCode != null">
        store_code,
      </if>
      <if test="goodsNo != null">
        goods_no,
      </if>
      <if test="storeRank != null">
        store_rank,
      </if>
      <if test="storageDays != null">
        storage_days,
      </if>
      <if test="nonSalesDays != null">
        non_sales_days,
      </if>
      <if test="thirtySalesQuantity != null">
        thirty_sales_quantity,
      </if>
      <if test="thirtySalesCount != null">
        thirty_sales_count,
      </if>
      <if test="minDisplayQuantity != null">
        min_display_quantity,
      </if>
      <if test="warehouseCode != null">
        warehouse_code,
      </if>
      <if test="warehouseName != null">
        warehouse_name,
      </if>
      <if test="goodsline != null">
        goodsline,
      </if>
      <if test="pushlevel != null">
        pushlevel,
      </if>
      <if test="goodsLevel != null">
        goods_level,
      </if>
      <if test="forbidDistribute != null">
        forbid_distribute,
      </if>
      <if test="forbidReturnWarehouse != null">
        forbid_return_warehouse,
      </if>
      <if test="forbidApply != null">
        forbid_apply,
      </if>
      <if test="forbidAllot != null">
        forbid_allot,
      </if>
      <if test="stockQuantity != null">
        stock_quantity,
      </if>
      <if test="costAmount != null">
        cost_amount,
      </if>
      <if test="stockUpperLimitDays != null">
        stock_upper_limit_days,
      </if>
      <if test="stockLowerLimitDays != null">
        stock_lower_limit_days,
      </if>
      <if test="hdSynthesizeAverageDailySales != null">
        hd_synthesize_average_daily_sales,
      </if>
      <if test="stockUpperLimit != null">
        stock_upper_limit,
      </if>
      <if test="stockLowerLimit != null">
        stock_lower_limit,
      </if>
      <if test="bdpSynthesizeAverageDailySales != null">
        bdp_synthesize_average_daily_sales,
      </if>
      <if test="expectSaleDays != null">
        expect_sale_days,
      </if>
      <if test="dealStatus != null">
        deal_status,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedName != null">
        updated_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyBdpCode != null">
        #{companyBdpCode,jdbcType=VARCHAR},
      </if>
      <if test="storeCode != null">
        #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsNo != null">
        #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="storeRank != null">
        #{storeRank,jdbcType=INTEGER},
      </if>
      <if test="storageDays != null">
        #{storageDays,jdbcType=INTEGER},
      </if>
      <if test="nonSalesDays != null">
        #{nonSalesDays,jdbcType=INTEGER},
      </if>
      <if test="thirtySalesQuantity != null">
        #{thirtySalesQuantity,jdbcType=DECIMAL},
      </if>
      <if test="thirtySalesCount != null">
        #{thirtySalesCount,jdbcType=INTEGER},
      </if>
      <if test="minDisplayQuantity != null">
        #{minDisplayQuantity,jdbcType=DECIMAL},
      </if>
      <if test="warehouseCode != null">
        #{warehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="warehouseName != null">
        #{warehouseName,jdbcType=VARCHAR},
      </if>
      <if test="goodsline != null">
        #{goodsline,jdbcType=VARCHAR},
      </if>
      <if test="pushlevel != null">
        #{pushlevel,jdbcType=VARCHAR},
      </if>
      <if test="goodsLevel != null">
        #{goodsLevel,jdbcType=INTEGER},
      </if>
      <if test="forbidDistribute != null">
        #{forbidDistribute,jdbcType=VARCHAR},
      </if>
      <if test="forbidReturnWarehouse != null">
        #{forbidReturnWarehouse,jdbcType=VARCHAR},
      </if>
      <if test="forbidApply != null">
        #{forbidApply,jdbcType=VARCHAR},
      </if>
      <if test="forbidAllot != null">
        #{forbidAllot,jdbcType=VARCHAR},
      </if>
      <if test="stockQuantity != null">
        #{stockQuantity,jdbcType=DECIMAL},
      </if>
      <if test="costAmount != null">
        #{costAmount,jdbcType=DECIMAL},
      </if>
      <if test="stockUpperLimitDays != null">
        #{stockUpperLimitDays,jdbcType=INTEGER},
      </if>
      <if test="stockLowerLimitDays != null">
        #{stockLowerLimitDays,jdbcType=INTEGER},
      </if>
      <if test="hdSynthesizeAverageDailySales != null">
        #{hdSynthesizeAverageDailySales,jdbcType=DECIMAL},
      </if>
      <if test="stockUpperLimit != null">
        #{stockUpperLimit,jdbcType=DECIMAL},
      </if>
      <if test="stockLowerLimit != null">
        #{stockLowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="bdpSynthesizeAverageDailySales != null">
        #{bdpSynthesizeAverageDailySales,jdbcType=DECIMAL},
      </if>
      <if test="expectSaleDays != null">
        #{expectSaleDays,jdbcType=DECIMAL},
      </if>
      <if test="dealStatus != null">
        #{dealStatus,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        #{updatedName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.iscm.entityTidb.IscmStoreGoodsStockCostTopExample" resultType="java.lang.Long">
    select count(*) from iscm_store_goods_stock_cost_top
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update iscm_store_goods_stock_cost_top
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.companyCode != null">
        company_code = #{record.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.companyBdpCode != null">
        company_bdp_code = #{record.companyBdpCode,jdbcType=VARCHAR},
      </if>
      <if test="record.storeCode != null">
        store_code = #{record.storeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsNo != null">
        goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="record.storeRank != null">
        store_rank = #{record.storeRank,jdbcType=INTEGER},
      </if>
      <if test="record.storageDays != null">
        storage_days = #{record.storageDays,jdbcType=INTEGER},
      </if>
      <if test="record.nonSalesDays != null">
        non_sales_days = #{record.nonSalesDays,jdbcType=INTEGER},
      </if>
      <if test="record.thirtySalesQuantity != null">
        thirty_sales_quantity = #{record.thirtySalesQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.thirtySalesCount != null">
        thirty_sales_count = #{record.thirtySalesCount,jdbcType=INTEGER},
      </if>
      <if test="record.minDisplayQuantity != null">
        min_display_quantity = #{record.minDisplayQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.warehouseCode != null">
        warehouse_code = #{record.warehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="record.warehouseName != null">
        warehouse_name = #{record.warehouseName,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsline != null">
        goodsline = #{record.goodsline,jdbcType=VARCHAR},
      </if>
      <if test="record.pushlevel != null">
        pushlevel = #{record.pushlevel,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsLevel != null">
        goods_level = #{record.goodsLevel,jdbcType=INTEGER},
      </if>
      <if test="record.forbidDistribute != null">
        forbid_distribute = #{record.forbidDistribute,jdbcType=VARCHAR},
      </if>
      <if test="record.forbidReturnWarehouse != null">
        forbid_return_warehouse = #{record.forbidReturnWarehouse,jdbcType=VARCHAR},
      </if>
      <if test="record.forbidApply != null">
        forbid_apply = #{record.forbidApply,jdbcType=VARCHAR},
      </if>
      <if test="record.forbidAllot != null">
        forbid_allot = #{record.forbidAllot,jdbcType=VARCHAR},
      </if>
      <if test="record.stockQuantity != null">
        stock_quantity = #{record.stockQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.costAmount != null">
        cost_amount = #{record.costAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.stockUpperLimitDays != null">
        stock_upper_limit_days = #{record.stockUpperLimitDays,jdbcType=INTEGER},
      </if>
      <if test="record.stockLowerLimitDays != null">
        stock_lower_limit_days = #{record.stockLowerLimitDays,jdbcType=INTEGER},
      </if>
      <if test="record.hdSynthesizeAverageDailySales != null">
        hd_synthesize_average_daily_sales = #{record.hdSynthesizeAverageDailySales,jdbcType=DECIMAL},
      </if>
      <if test="record.stockUpperLimit != null">
        stock_upper_limit = #{record.stockUpperLimit,jdbcType=DECIMAL},
      </if>
      <if test="record.stockLowerLimit != null">
        stock_lower_limit = #{record.stockLowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="record.bdpSynthesizeAverageDailySales != null">
        bdp_synthesize_average_daily_sales = #{record.bdpSynthesizeAverageDailySales,jdbcType=DECIMAL},
      </if>
      <if test="record.expectSaleDays != null">
        expect_sale_days = #{record.expectSaleDays,jdbcType=DECIMAL},
      </if>
      <if test="record.dealStatus != null">
        deal_status = #{record.dealStatus,jdbcType=TINYINT},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedName != null">
        updated_name = #{record.updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update iscm_store_goods_stock_cost_top
    set id = #{record.id,jdbcType=BIGINT},
      company_code = #{record.companyCode,jdbcType=VARCHAR},
      company_bdp_code = #{record.companyBdpCode,jdbcType=VARCHAR},
      store_code = #{record.storeCode,jdbcType=VARCHAR},
      goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      store_rank = #{record.storeRank,jdbcType=INTEGER},
      storage_days = #{record.storageDays,jdbcType=INTEGER},
      non_sales_days = #{record.nonSalesDays,jdbcType=INTEGER},
      thirty_sales_quantity = #{record.thirtySalesQuantity,jdbcType=DECIMAL},
      thirty_sales_count = #{record.thirtySalesCount,jdbcType=INTEGER},
      min_display_quantity = #{record.minDisplayQuantity,jdbcType=DECIMAL},
      warehouse_code = #{record.warehouseCode,jdbcType=VARCHAR},
      warehouse_name = #{record.warehouseName,jdbcType=VARCHAR},
      goodsline = #{record.goodsline,jdbcType=VARCHAR},
      pushlevel = #{record.pushlevel,jdbcType=VARCHAR},
      goods_level = #{record.goodsLevel,jdbcType=INTEGER},
      forbid_distribute = #{record.forbidDistribute,jdbcType=VARCHAR},
      forbid_return_warehouse = #{record.forbidReturnWarehouse,jdbcType=VARCHAR},
      forbid_apply = #{record.forbidApply,jdbcType=VARCHAR},
      forbid_allot = #{record.forbidAllot,jdbcType=VARCHAR},
      stock_quantity = #{record.stockQuantity,jdbcType=DECIMAL},
      cost_amount = #{record.costAmount,jdbcType=DECIMAL},
      stock_upper_limit_days = #{record.stockUpperLimitDays,jdbcType=INTEGER},
      stock_lower_limit_days = #{record.stockLowerLimitDays,jdbcType=INTEGER},
      hd_synthesize_average_daily_sales = #{record.hdSynthesizeAverageDailySales,jdbcType=DECIMAL},
      stock_upper_limit = #{record.stockUpperLimit,jdbcType=DECIMAL},
      stock_lower_limit = #{record.stockLowerLimit,jdbcType=DECIMAL},
      bdp_synthesize_average_daily_sales = #{record.bdpSynthesizeAverageDailySales,jdbcType=DECIMAL},
      expect_sale_days = #{record.expectSaleDays,jdbcType=DECIMAL},
      deal_status = #{record.dealStatus,jdbcType=TINYINT},
      `status` = #{record.status,jdbcType=TINYINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_name = #{record.createdName,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_name = #{record.updatedName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.iscm.entityTidb.IscmStoreGoodsStockCostTop">
    update iscm_store_goods_stock_cost_top
    <set>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyBdpCode != null">
        company_bdp_code = #{companyBdpCode,jdbcType=VARCHAR},
      </if>
      <if test="storeCode != null">
        store_code = #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsNo != null">
        goods_no = #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="storeRank != null">
        store_rank = #{storeRank,jdbcType=INTEGER},
      </if>
      <if test="storageDays != null">
        storage_days = #{storageDays,jdbcType=INTEGER},
      </if>
      <if test="nonSalesDays != null">
        non_sales_days = #{nonSalesDays,jdbcType=INTEGER},
      </if>
      <if test="thirtySalesQuantity != null">
        thirty_sales_quantity = #{thirtySalesQuantity,jdbcType=DECIMAL},
      </if>
      <if test="thirtySalesCount != null">
        thirty_sales_count = #{thirtySalesCount,jdbcType=INTEGER},
      </if>
      <if test="minDisplayQuantity != null">
        min_display_quantity = #{minDisplayQuantity,jdbcType=DECIMAL},
      </if>
      <if test="warehouseCode != null">
        warehouse_code = #{warehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="warehouseName != null">
        warehouse_name = #{warehouseName,jdbcType=VARCHAR},
      </if>
      <if test="goodsline != null">
        goodsline = #{goodsline,jdbcType=VARCHAR},
      </if>
      <if test="pushlevel != null">
        pushlevel = #{pushlevel,jdbcType=VARCHAR},
      </if>
      <if test="goodsLevel != null">
        goods_level = #{goodsLevel,jdbcType=INTEGER},
      </if>
      <if test="forbidDistribute != null">
        forbid_distribute = #{forbidDistribute,jdbcType=VARCHAR},
      </if>
      <if test="forbidReturnWarehouse != null">
        forbid_return_warehouse = #{forbidReturnWarehouse,jdbcType=VARCHAR},
      </if>
      <if test="forbidApply != null">
        forbid_apply = #{forbidApply,jdbcType=VARCHAR},
      </if>
      <if test="forbidAllot != null">
        forbid_allot = #{forbidAllot,jdbcType=VARCHAR},
      </if>
      <if test="stockQuantity != null">
        stock_quantity = #{stockQuantity,jdbcType=DECIMAL},
      </if>
      <if test="costAmount != null">
        cost_amount = #{costAmount,jdbcType=DECIMAL},
      </if>
      <if test="stockUpperLimitDays != null">
        stock_upper_limit_days = #{stockUpperLimitDays,jdbcType=INTEGER},
      </if>
      <if test="stockLowerLimitDays != null">
        stock_lower_limit_days = #{stockLowerLimitDays,jdbcType=INTEGER},
      </if>
      <if test="hdSynthesizeAverageDailySales != null">
        hd_synthesize_average_daily_sales = #{hdSynthesizeAverageDailySales,jdbcType=DECIMAL},
      </if>
      <if test="stockUpperLimit != null">
        stock_upper_limit = #{stockUpperLimit,jdbcType=DECIMAL},
      </if>
      <if test="stockLowerLimit != null">
        stock_lower_limit = #{stockLowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="bdpSynthesizeAverageDailySales != null">
        bdp_synthesize_average_daily_sales = #{bdpSynthesizeAverageDailySales,jdbcType=DECIMAL},
      </if>
      <if test="expectSaleDays != null">
        expect_sale_days = #{expectSaleDays,jdbcType=DECIMAL},
      </if>
      <if test="dealStatus != null">
        deal_status = #{dealStatus,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        updated_name = #{updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.iscm.entityTidb.IscmStoreGoodsStockCostTop">
    update iscm_store_goods_stock_cost_top
    set company_code = #{companyCode,jdbcType=VARCHAR},
      company_bdp_code = #{companyBdpCode,jdbcType=VARCHAR},
      store_code = #{storeCode,jdbcType=VARCHAR},
      goods_no = #{goodsNo,jdbcType=VARCHAR},
      store_rank = #{storeRank,jdbcType=INTEGER},
      storage_days = #{storageDays,jdbcType=INTEGER},
      non_sales_days = #{nonSalesDays,jdbcType=INTEGER},
      thirty_sales_quantity = #{thirtySalesQuantity,jdbcType=DECIMAL},
      thirty_sales_count = #{thirtySalesCount,jdbcType=INTEGER},
      min_display_quantity = #{minDisplayQuantity,jdbcType=DECIMAL},
      warehouse_code = #{warehouseCode,jdbcType=VARCHAR},
      warehouse_name = #{warehouseName,jdbcType=VARCHAR},
      goodsline = #{goodsline,jdbcType=VARCHAR},
      pushlevel = #{pushlevel,jdbcType=VARCHAR},
      goods_level = #{goodsLevel,jdbcType=INTEGER},
      forbid_distribute = #{forbidDistribute,jdbcType=VARCHAR},
      forbid_return_warehouse = #{forbidReturnWarehouse,jdbcType=VARCHAR},
      forbid_apply = #{forbidApply,jdbcType=VARCHAR},
      forbid_allot = #{forbidAllot,jdbcType=VARCHAR},
      stock_quantity = #{stockQuantity,jdbcType=DECIMAL},
      cost_amount = #{costAmount,jdbcType=DECIMAL},
      stock_upper_limit_days = #{stockUpperLimitDays,jdbcType=INTEGER},
      stock_lower_limit_days = #{stockLowerLimitDays,jdbcType=INTEGER},
      hd_synthesize_average_daily_sales = #{hdSynthesizeAverageDailySales,jdbcType=DECIMAL},
      stock_upper_limit = #{stockUpperLimit,jdbcType=DECIMAL},
      stock_lower_limit = #{stockLowerLimit,jdbcType=DECIMAL},
      bdp_synthesize_average_daily_sales = #{bdpSynthesizeAverageDailySales,jdbcType=DECIMAL},
      expect_sale_days = #{expectSaleDays,jdbcType=DECIMAL},
      deal_status = #{dealStatus,jdbcType=TINYINT},
      `status` = #{status,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_name = #{createdName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_name = #{updatedName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>