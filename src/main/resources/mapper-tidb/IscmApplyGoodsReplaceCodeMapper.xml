<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapperTidb.IscmApplyGoodsReplaceCodeMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entityTidb.IscmApplyGoodsReplaceCode">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="master_goods_no" jdbcType="VARCHAR" property="masterGoodsNo" />
    <result column="replace_goods_no" jdbcType="VARCHAR" property="replaceGoodsNo" />
    <result column="change_ratio" jdbcType="DECIMAL" property="changeRatio" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, company_code, master_goods_no, replace_goods_no, change_ratio, gmt_create, gmt_update
  </sql>
  <select id="selectByExample" parameterType="com.cowell.iscm.entityTidb.IscmApplyGoodsReplaceCodeExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from iscm_apply_goods_replace_code
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from iscm_apply_goods_replace_code
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from iscm_apply_goods_replace_code
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.iscm.entityTidb.IscmApplyGoodsReplaceCodeExample">
    delete from iscm_apply_goods_replace_code
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entityTidb.IscmApplyGoodsReplaceCode" useGeneratedKeys="true">
    insert into iscm_apply_goods_replace_code (company_code, master_goods_no, replace_goods_no, 
      change_ratio, gmt_create, gmt_update
      )
    values (#{companyCode,jdbcType=VARCHAR}, #{masterGoodsNo,jdbcType=VARCHAR}, #{replaceGoodsNo,jdbcType=VARCHAR}, 
      #{changeRatio,jdbcType=DECIMAL}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entityTidb.IscmApplyGoodsReplaceCode" useGeneratedKeys="true">
    insert into iscm_apply_goods_replace_code
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="masterGoodsNo != null">
        master_goods_no,
      </if>
      <if test="replaceGoodsNo != null">
        replace_goods_no,
      </if>
      <if test="changeRatio != null">
        change_ratio,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="masterGoodsNo != null">
        #{masterGoodsNo,jdbcType=VARCHAR},
      </if>
      <if test="replaceGoodsNo != null">
        #{replaceGoodsNo,jdbcType=VARCHAR},
      </if>
      <if test="changeRatio != null">
        #{changeRatio,jdbcType=DECIMAL},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.iscm.entityTidb.IscmApplyGoodsReplaceCodeExample" resultType="java.lang.Long">
    select count(*) from iscm_apply_goods_replace_code
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update iscm_apply_goods_replace_code
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.companyCode != null">
        company_code = #{record.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.masterGoodsNo != null">
        master_goods_no = #{record.masterGoodsNo,jdbcType=VARCHAR},
      </if>
      <if test="record.replaceGoodsNo != null">
        replace_goods_no = #{record.replaceGoodsNo,jdbcType=VARCHAR},
      </if>
      <if test="record.changeRatio != null">
        change_ratio = #{record.changeRatio,jdbcType=DECIMAL},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update iscm_apply_goods_replace_code
    set id = #{record.id,jdbcType=BIGINT},
      company_code = #{record.companyCode,jdbcType=VARCHAR},
      master_goods_no = #{record.masterGoodsNo,jdbcType=VARCHAR},
      replace_goods_no = #{record.replaceGoodsNo,jdbcType=VARCHAR},
      change_ratio = #{record.changeRatio,jdbcType=DECIMAL},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.iscm.entityTidb.IscmApplyGoodsReplaceCode">
    update iscm_apply_goods_replace_code
    <set>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="masterGoodsNo != null">
        master_goods_no = #{masterGoodsNo,jdbcType=VARCHAR},
      </if>
      <if test="replaceGoodsNo != null">
        replace_goods_no = #{replaceGoodsNo,jdbcType=VARCHAR},
      </if>
      <if test="changeRatio != null">
        change_ratio = #{changeRatio,jdbcType=DECIMAL},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.iscm.entityTidb.IscmApplyGoodsReplaceCode">
    update iscm_apply_goods_replace_code
    set company_code = #{companyCode,jdbcType=VARCHAR},
      master_goods_no = #{masterGoodsNo,jdbcType=VARCHAR},
      replace_goods_no = #{replaceGoodsNo,jdbcType=VARCHAR},
      change_ratio = #{changeRatio,jdbcType=DECIMAL},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>