<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapperTidb.IscmManualRegisterMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entityTidb.IscmManualRegister">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="data_type" jdbcType="TINYINT" property="dataType" />
    <result column="register_type" jdbcType="TINYINT" property="registerType" />
    <result column="company_bdp_code" jdbcType="VARCHAR" property="companyBdpCode" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="warehouse_code" jdbcType="VARCHAR" property="warehouseCode" />
    <result column="warehouse_name" jdbcType="VARCHAR" property="warehouseName" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="register_quantity" jdbcType="DECIMAL" property="registerQuantity" />
    <result column="non_validity_stock_quantity" jdbcType="DECIMAL" property="nonValidityStockQuantity" />
    <result column="stock_upper_limit" jdbcType="DECIMAL" property="stockUpperLimit" />
    <result column="stock_lower_limit" jdbcType="DECIMAL" property="stockLowerLimit" />
    <result column="no_tax_inventory_cost_amount" jdbcType="DECIMAL" property="noTaxInventoryCostAmount" />
    <result column="non_sale_days" jdbcType="INTEGER" property="nonSaleDays" />
    <result column="synthesize_average_daily_sales" jdbcType="DECIMAL" property="synthesizeAverageDailySales" />
    <result column="thirty_sales_quantity" jdbcType="DECIMAL" property="thirtySalesQuantity" />
    <result column="thirty_sales_count" jdbcType="INTEGER" property="thirtySalesCount" />
    <result column="stock_quantity" jdbcType="DECIMAL" property="stockQuantity" />
    <result column="min_display_quantity" jdbcType="DECIMAL" property="minDisplayQuantity" />
    <result column="expect_sale_days" jdbcType="DECIMAL" property="expectSaleDays" />
    <result column="deal_status" jdbcType="TINYINT" property="dealStatus" />
    <result column="return_status" jdbcType="TINYINT" property="returnStatus" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, data_type, register_type, company_bdp_code, company_code, store_code, warehouse_code, 
    warehouse_name, goods_no, batch_no, manufacturer, register_quantity, non_validity_stock_quantity, 
    stock_upper_limit, stock_lower_limit, no_tax_inventory_cost_amount, non_sale_days, 
    synthesize_average_daily_sales, thirty_sales_quantity, thirty_sales_count, stock_quantity, 
    min_display_quantity, expect_sale_days, deal_status, return_status, `status`, gmt_create, 
    gmt_update, extend, version, created_by, created_name, updated_by, updated_name
  </sql>
  <select id="selectByExample" parameterType="com.cowell.iscm.entityTidb.IscmManualRegisterExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from iscm_manual_register
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from iscm_manual_register
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from iscm_manual_register
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.iscm.entityTidb.IscmManualRegisterExample">
    delete from iscm_manual_register
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.iscm.entityTidb.IscmManualRegister">
    insert into iscm_manual_register (id, data_type, register_type, 
      company_bdp_code, company_code, store_code, 
      warehouse_code, warehouse_name, goods_no, 
      batch_no, manufacturer, register_quantity, 
      non_validity_stock_quantity, stock_upper_limit, 
      stock_lower_limit, no_tax_inventory_cost_amount, 
      non_sale_days, synthesize_average_daily_sales, 
      thirty_sales_quantity, thirty_sales_count, 
      stock_quantity, min_display_quantity, expect_sale_days, 
      deal_status, return_status, `status`, 
      gmt_create, gmt_update, extend, 
      version, created_by, created_name, 
      updated_by, updated_name)
    values (#{id,jdbcType=BIGINT}, #{dataType,jdbcType=TINYINT}, #{registerType,jdbcType=TINYINT}, 
      #{companyBdpCode,jdbcType=VARCHAR}, #{companyCode,jdbcType=VARCHAR}, #{storeCode,jdbcType=VARCHAR}, 
      #{warehouseCode,jdbcType=VARCHAR}, #{warehouseName,jdbcType=VARCHAR}, #{goodsNo,jdbcType=VARCHAR}, 
      #{batchNo,jdbcType=VARCHAR}, #{manufacturer,jdbcType=VARCHAR}, #{registerQuantity,jdbcType=DECIMAL}, 
      #{nonValidityStockQuantity,jdbcType=DECIMAL}, #{stockUpperLimit,jdbcType=DECIMAL}, 
      #{stockLowerLimit,jdbcType=DECIMAL}, #{noTaxInventoryCostAmount,jdbcType=DECIMAL}, 
      #{nonSaleDays,jdbcType=INTEGER}, #{synthesizeAverageDailySales,jdbcType=DECIMAL}, 
      #{thirtySalesQuantity,jdbcType=DECIMAL}, #{thirtySalesCount,jdbcType=INTEGER}, 
      #{stockQuantity,jdbcType=DECIMAL}, #{minDisplayQuantity,jdbcType=DECIMAL}, #{expectSaleDays,jdbcType=DECIMAL}, 
      #{dealStatus,jdbcType=TINYINT}, #{returnStatus,jdbcType=TINYINT}, #{status,jdbcType=TINYINT}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}, #{extend,jdbcType=VARCHAR}, 
      #{version,jdbcType=INTEGER}, #{createdBy,jdbcType=BIGINT}, #{createdName,jdbcType=VARCHAR}, 
      #{updatedBy,jdbcType=BIGINT}, #{updatedName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.iscm.entityTidb.IscmManualRegister">
    insert into iscm_manual_register
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="dataType != null">
        data_type,
      </if>
      <if test="registerType != null">
        register_type,
      </if>
      <if test="companyBdpCode != null">
        company_bdp_code,
      </if>
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="storeCode != null">
        store_code,
      </if>
      <if test="warehouseCode != null">
        warehouse_code,
      </if>
      <if test="warehouseName != null">
        warehouse_name,
      </if>
      <if test="goodsNo != null">
        goods_no,
      </if>
      <if test="batchNo != null">
        batch_no,
      </if>
      <if test="manufacturer != null">
        manufacturer,
      </if>
      <if test="registerQuantity != null">
        register_quantity,
      </if>
      <if test="nonValidityStockQuantity != null">
        non_validity_stock_quantity,
      </if>
      <if test="stockUpperLimit != null">
        stock_upper_limit,
      </if>
      <if test="stockLowerLimit != null">
        stock_lower_limit,
      </if>
      <if test="noTaxInventoryCostAmount != null">
        no_tax_inventory_cost_amount,
      </if>
      <if test="nonSaleDays != null">
        non_sale_days,
      </if>
      <if test="synthesizeAverageDailySales != null">
        synthesize_average_daily_sales,
      </if>
      <if test="thirtySalesQuantity != null">
        thirty_sales_quantity,
      </if>
      <if test="thirtySalesCount != null">
        thirty_sales_count,
      </if>
      <if test="stockQuantity != null">
        stock_quantity,
      </if>
      <if test="minDisplayQuantity != null">
        min_display_quantity,
      </if>
      <if test="expectSaleDays != null">
        expect_sale_days,
      </if>
      <if test="dealStatus != null">
        deal_status,
      </if>
      <if test="returnStatus != null">
        return_status,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedName != null">
        updated_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="dataType != null">
        #{dataType,jdbcType=TINYINT},
      </if>
      <if test="registerType != null">
        #{registerType,jdbcType=TINYINT},
      </if>
      <if test="companyBdpCode != null">
        #{companyBdpCode,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="storeCode != null">
        #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="warehouseCode != null">
        #{warehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="warehouseName != null">
        #{warehouseName,jdbcType=VARCHAR},
      </if>
      <if test="goodsNo != null">
        #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="batchNo != null">
        #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="registerQuantity != null">
        #{registerQuantity,jdbcType=DECIMAL},
      </if>
      <if test="nonValidityStockQuantity != null">
        #{nonValidityStockQuantity,jdbcType=DECIMAL},
      </if>
      <if test="stockUpperLimit != null">
        #{stockUpperLimit,jdbcType=DECIMAL},
      </if>
      <if test="stockLowerLimit != null">
        #{stockLowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="noTaxInventoryCostAmount != null">
        #{noTaxInventoryCostAmount,jdbcType=DECIMAL},
      </if>
      <if test="nonSaleDays != null">
        #{nonSaleDays,jdbcType=INTEGER},
      </if>
      <if test="synthesizeAverageDailySales != null">
        #{synthesizeAverageDailySales,jdbcType=DECIMAL},
      </if>
      <if test="thirtySalesQuantity != null">
        #{thirtySalesQuantity,jdbcType=DECIMAL},
      </if>
      <if test="thirtySalesCount != null">
        #{thirtySalesCount,jdbcType=INTEGER},
      </if>
      <if test="stockQuantity != null">
        #{stockQuantity,jdbcType=DECIMAL},
      </if>
      <if test="minDisplayQuantity != null">
        #{minDisplayQuantity,jdbcType=DECIMAL},
      </if>
      <if test="expectSaleDays != null">
        #{expectSaleDays,jdbcType=DECIMAL},
      </if>
      <if test="dealStatus != null">
        #{dealStatus,jdbcType=TINYINT},
      </if>
      <if test="returnStatus != null">
        #{returnStatus,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        #{updatedName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.iscm.entityTidb.IscmManualRegisterExample" resultType="java.lang.Long">
    select count(*) from iscm_manual_register
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update iscm_manual_register
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.dataType != null">
        data_type = #{record.dataType,jdbcType=TINYINT},
      </if>
      <if test="record.registerType != null">
        register_type = #{record.registerType,jdbcType=TINYINT},
      </if>
      <if test="record.companyBdpCode != null">
        company_bdp_code = #{record.companyBdpCode,jdbcType=VARCHAR},
      </if>
      <if test="record.companyCode != null">
        company_code = #{record.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.storeCode != null">
        store_code = #{record.storeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.warehouseCode != null">
        warehouse_code = #{record.warehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="record.warehouseName != null">
        warehouse_name = #{record.warehouseName,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsNo != null">
        goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="record.batchNo != null">
        batch_no = #{record.batchNo,jdbcType=VARCHAR},
      </if>
      <if test="record.manufacturer != null">
        manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="record.registerQuantity != null">
        register_quantity = #{record.registerQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.nonValidityStockQuantity != null">
        non_validity_stock_quantity = #{record.nonValidityStockQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.stockUpperLimit != null">
        stock_upper_limit = #{record.stockUpperLimit,jdbcType=DECIMAL},
      </if>
      <if test="record.stockLowerLimit != null">
        stock_lower_limit = #{record.stockLowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="record.noTaxInventoryCostAmount != null">
        no_tax_inventory_cost_amount = #{record.noTaxInventoryCostAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.nonSaleDays != null">
        non_sale_days = #{record.nonSaleDays,jdbcType=INTEGER},
      </if>
      <if test="record.synthesizeAverageDailySales != null">
        synthesize_average_daily_sales = #{record.synthesizeAverageDailySales,jdbcType=DECIMAL},
      </if>
      <if test="record.thirtySalesQuantity != null">
        thirty_sales_quantity = #{record.thirtySalesQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.thirtySalesCount != null">
        thirty_sales_count = #{record.thirtySalesCount,jdbcType=INTEGER},
      </if>
      <if test="record.stockQuantity != null">
        stock_quantity = #{record.stockQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.minDisplayQuantity != null">
        min_display_quantity = #{record.minDisplayQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.expectSaleDays != null">
        expect_sale_days = #{record.expectSaleDays,jdbcType=DECIMAL},
      </if>
      <if test="record.dealStatus != null">
        deal_status = #{record.dealStatus,jdbcType=TINYINT},
      </if>
      <if test="record.returnStatus != null">
        return_status = #{record.returnStatus,jdbcType=TINYINT},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedName != null">
        updated_name = #{record.updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update iscm_manual_register
    set id = #{record.id,jdbcType=BIGINT},
      data_type = #{record.dataType,jdbcType=TINYINT},
      register_type = #{record.registerType,jdbcType=TINYINT},
      company_bdp_code = #{record.companyBdpCode,jdbcType=VARCHAR},
      company_code = #{record.companyCode,jdbcType=VARCHAR},
      store_code = #{record.storeCode,jdbcType=VARCHAR},
      warehouse_code = #{record.warehouseCode,jdbcType=VARCHAR},
      warehouse_name = #{record.warehouseName,jdbcType=VARCHAR},
      goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      batch_no = #{record.batchNo,jdbcType=VARCHAR},
      manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      register_quantity = #{record.registerQuantity,jdbcType=DECIMAL},
      non_validity_stock_quantity = #{record.nonValidityStockQuantity,jdbcType=DECIMAL},
      stock_upper_limit = #{record.stockUpperLimit,jdbcType=DECIMAL},
      stock_lower_limit = #{record.stockLowerLimit,jdbcType=DECIMAL},
      no_tax_inventory_cost_amount = #{record.noTaxInventoryCostAmount,jdbcType=DECIMAL},
      non_sale_days = #{record.nonSaleDays,jdbcType=INTEGER},
      synthesize_average_daily_sales = #{record.synthesizeAverageDailySales,jdbcType=DECIMAL},
      thirty_sales_quantity = #{record.thirtySalesQuantity,jdbcType=DECIMAL},
      thirty_sales_count = #{record.thirtySalesCount,jdbcType=INTEGER},
      stock_quantity = #{record.stockQuantity,jdbcType=DECIMAL},
      min_display_quantity = #{record.minDisplayQuantity,jdbcType=DECIMAL},
      expect_sale_days = #{record.expectSaleDays,jdbcType=DECIMAL},
      deal_status = #{record.dealStatus,jdbcType=TINYINT},
      return_status = #{record.returnStatus,jdbcType=TINYINT},
      `status` = #{record.status,jdbcType=TINYINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_name = #{record.createdName,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_name = #{record.updatedName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.iscm.entityTidb.IscmManualRegister">
    update iscm_manual_register
    <set>
      <if test="dataType != null">
        data_type = #{dataType,jdbcType=TINYINT},
      </if>
      <if test="registerType != null">
        register_type = #{registerType,jdbcType=TINYINT},
      </if>
      <if test="companyBdpCode != null">
        company_bdp_code = #{companyBdpCode,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="storeCode != null">
        store_code = #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="warehouseCode != null">
        warehouse_code = #{warehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="warehouseName != null">
        warehouse_name = #{warehouseName,jdbcType=VARCHAR},
      </if>
      <if test="goodsNo != null">
        goods_no = #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="batchNo != null">
        batch_no = #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        manufacturer = #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="registerQuantity != null">
        register_quantity = #{registerQuantity,jdbcType=DECIMAL},
      </if>
      <if test="nonValidityStockQuantity != null">
        non_validity_stock_quantity = #{nonValidityStockQuantity,jdbcType=DECIMAL},
      </if>
      <if test="stockUpperLimit != null">
        stock_upper_limit = #{stockUpperLimit,jdbcType=DECIMAL},
      </if>
      <if test="stockLowerLimit != null">
        stock_lower_limit = #{stockLowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="noTaxInventoryCostAmount != null">
        no_tax_inventory_cost_amount = #{noTaxInventoryCostAmount,jdbcType=DECIMAL},
      </if>
      <if test="nonSaleDays != null">
        non_sale_days = #{nonSaleDays,jdbcType=INTEGER},
      </if>
      <if test="synthesizeAverageDailySales != null">
        synthesize_average_daily_sales = #{synthesizeAverageDailySales,jdbcType=DECIMAL},
      </if>
      <if test="thirtySalesQuantity != null">
        thirty_sales_quantity = #{thirtySalesQuantity,jdbcType=DECIMAL},
      </if>
      <if test="thirtySalesCount != null">
        thirty_sales_count = #{thirtySalesCount,jdbcType=INTEGER},
      </if>
      <if test="stockQuantity != null">
        stock_quantity = #{stockQuantity,jdbcType=DECIMAL},
      </if>
      <if test="minDisplayQuantity != null">
        min_display_quantity = #{minDisplayQuantity,jdbcType=DECIMAL},
      </if>
      <if test="expectSaleDays != null">
        expect_sale_days = #{expectSaleDays,jdbcType=DECIMAL},
      </if>
      <if test="dealStatus != null">
        deal_status = #{dealStatus,jdbcType=TINYINT},
      </if>
      <if test="returnStatus != null">
        return_status = #{returnStatus,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        updated_name = #{updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.iscm.entityTidb.IscmManualRegister">
    update iscm_manual_register
    set data_type = #{dataType,jdbcType=TINYINT},
      register_type = #{registerType,jdbcType=TINYINT},
      company_bdp_code = #{companyBdpCode,jdbcType=VARCHAR},
      company_code = #{companyCode,jdbcType=VARCHAR},
      store_code = #{storeCode,jdbcType=VARCHAR},
      warehouse_code = #{warehouseCode,jdbcType=VARCHAR},
      warehouse_name = #{warehouseName,jdbcType=VARCHAR},
      goods_no = #{goodsNo,jdbcType=VARCHAR},
      batch_no = #{batchNo,jdbcType=VARCHAR},
      manufacturer = #{manufacturer,jdbcType=VARCHAR},
      register_quantity = #{registerQuantity,jdbcType=DECIMAL},
      non_validity_stock_quantity = #{nonValidityStockQuantity,jdbcType=DECIMAL},
      stock_upper_limit = #{stockUpperLimit,jdbcType=DECIMAL},
      stock_lower_limit = #{stockLowerLimit,jdbcType=DECIMAL},
      no_tax_inventory_cost_amount = #{noTaxInventoryCostAmount,jdbcType=DECIMAL},
      non_sale_days = #{nonSaleDays,jdbcType=INTEGER},
      synthesize_average_daily_sales = #{synthesizeAverageDailySales,jdbcType=DECIMAL},
      thirty_sales_quantity = #{thirtySalesQuantity,jdbcType=DECIMAL},
      thirty_sales_count = #{thirtySalesCount,jdbcType=INTEGER},
      stock_quantity = #{stockQuantity,jdbcType=DECIMAL},
      min_display_quantity = #{minDisplayQuantity,jdbcType=DECIMAL},
      expect_sale_days = #{expectSaleDays,jdbcType=DECIMAL},
      deal_status = #{dealStatus,jdbcType=TINYINT},
      return_status = #{returnStatus,jdbcType=TINYINT},
      `status` = #{status,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_name = #{createdName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_name = #{updatedName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>