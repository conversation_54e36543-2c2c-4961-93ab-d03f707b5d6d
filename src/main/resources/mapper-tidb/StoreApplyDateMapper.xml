<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapperTidb.StoreApplyDateMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entityTidb.StoreApplyDate">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="store_attr" jdbcType="VARCHAR" property="storeAttr" />
    <result column="open_date" jdbcType="DATE" property="openDate" />
    <result column="apply_date" jdbcType="DATE" property="applyDate" />
    <result column="store_status" jdbcType="VARCHAR" property="storeStatus" />
    <result column="store_electric_able" jdbcType="VARCHAR" property="storeElectricAble" />
    <result column="distr_circle" jdbcType="VARCHAR" property="distrCircle" />
    <result column="distr_transit_day" jdbcType="INTEGER" property="distrTransitDay" />
    <result column="month_sales_level" jdbcType="VARCHAR" property="monthSalesLevel" />
    <result column="store_count" jdbcType="INTEGER" property="storeCount" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, company_code, store_code, store_attr, open_date, apply_date, store_status, store_electric_able, 
    distr_circle, distr_transit_day, month_sales_level, store_count, gmt_create, gmt_update
  </sql>
  <select id="selectByExample" parameterType="com.cowell.iscm.entityTidb.StoreApplyDateExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from store_apply_date
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from store_apply_date
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from store_apply_date
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.iscm.entityTidb.StoreApplyDateExample">
    delete from store_apply_date
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entityTidb.StoreApplyDate" useGeneratedKeys="true">
    insert into store_apply_date (company_code, store_code, store_attr, 
      open_date, apply_date, store_status, 
      store_electric_able, distr_circle, distr_transit_day, 
      month_sales_level, store_count, gmt_create, 
      gmt_update)
    values (#{companyCode,jdbcType=VARCHAR}, #{storeCode,jdbcType=VARCHAR}, #{storeAttr,jdbcType=VARCHAR}, 
      #{openDate,jdbcType=DATE}, #{applyDate,jdbcType=DATE}, #{storeStatus,jdbcType=VARCHAR}, 
      #{storeElectricAble,jdbcType=VARCHAR}, #{distrCircle,jdbcType=VARCHAR}, #{distrTransitDay,jdbcType=INTEGER}, 
      #{monthSalesLevel,jdbcType=VARCHAR}, #{storeCount,jdbcType=INTEGER}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtUpdate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entityTidb.StoreApplyDate" useGeneratedKeys="true">
    insert into store_apply_date
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="storeCode != null">
        store_code,
      </if>
      <if test="storeAttr != null">
        store_attr,
      </if>
      <if test="openDate != null">
        open_date,
      </if>
      <if test="applyDate != null">
        apply_date,
      </if>
      <if test="storeStatus != null">
        store_status,
      </if>
      <if test="storeElectricAble != null">
        store_electric_able,
      </if>
      <if test="distrCircle != null">
        distr_circle,
      </if>
      <if test="distrTransitDay != null">
        distr_transit_day,
      </if>
      <if test="monthSalesLevel != null">
        month_sales_level,
      </if>
      <if test="storeCount != null">
        store_count,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="storeCode != null">
        #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="storeAttr != null">
        #{storeAttr,jdbcType=VARCHAR},
      </if>
      <if test="openDate != null">
        #{openDate,jdbcType=DATE},
      </if>
      <if test="applyDate != null">
        #{applyDate,jdbcType=DATE},
      </if>
      <if test="storeStatus != null">
        #{storeStatus,jdbcType=VARCHAR},
      </if>
      <if test="storeElectricAble != null">
        #{storeElectricAble,jdbcType=VARCHAR},
      </if>
      <if test="distrCircle != null">
        #{distrCircle,jdbcType=VARCHAR},
      </if>
      <if test="distrTransitDay != null">
        #{distrTransitDay,jdbcType=INTEGER},
      </if>
      <if test="monthSalesLevel != null">
        #{monthSalesLevel,jdbcType=VARCHAR},
      </if>
      <if test="storeCount != null">
        #{storeCount,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.iscm.entityTidb.StoreApplyDateExample" resultType="java.lang.Long">
    select count(*) from store_apply_date
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update store_apply_date
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.companyCode != null">
        company_code = #{record.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.storeCode != null">
        store_code = #{record.storeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.storeAttr != null">
        store_attr = #{record.storeAttr,jdbcType=VARCHAR},
      </if>
      <if test="record.openDate != null">
        open_date = #{record.openDate,jdbcType=DATE},
      </if>
      <if test="record.applyDate != null">
        apply_date = #{record.applyDate,jdbcType=DATE},
      </if>
      <if test="record.storeStatus != null">
        store_status = #{record.storeStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.storeElectricAble != null">
        store_electric_able = #{record.storeElectricAble,jdbcType=VARCHAR},
      </if>
      <if test="record.distrCircle != null">
        distr_circle = #{record.distrCircle,jdbcType=VARCHAR},
      </if>
      <if test="record.distrTransitDay != null">
        distr_transit_day = #{record.distrTransitDay,jdbcType=INTEGER},
      </if>
      <if test="record.monthSalesLevel != null">
        month_sales_level = #{record.monthSalesLevel,jdbcType=VARCHAR},
      </if>
      <if test="record.storeCount != null">
        store_count = #{record.storeCount,jdbcType=INTEGER},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update store_apply_date
    set id = #{record.id,jdbcType=BIGINT},
      company_code = #{record.companyCode,jdbcType=VARCHAR},
      store_code = #{record.storeCode,jdbcType=VARCHAR},
      store_attr = #{record.storeAttr,jdbcType=VARCHAR},
      open_date = #{record.openDate,jdbcType=DATE},
      apply_date = #{record.applyDate,jdbcType=DATE},
      store_status = #{record.storeStatus,jdbcType=VARCHAR},
      store_electric_able = #{record.storeElectricAble,jdbcType=VARCHAR},
      distr_circle = #{record.distrCircle,jdbcType=VARCHAR},
      distr_transit_day = #{record.distrTransitDay,jdbcType=INTEGER},
      month_sales_level = #{record.monthSalesLevel,jdbcType=VARCHAR},
      store_count = #{record.storeCount,jdbcType=INTEGER},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.iscm.entityTidb.StoreApplyDate">
    update store_apply_date
    <set>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="storeCode != null">
        store_code = #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="storeAttr != null">
        store_attr = #{storeAttr,jdbcType=VARCHAR},
      </if>
      <if test="openDate != null">
        open_date = #{openDate,jdbcType=DATE},
      </if>
      <if test="applyDate != null">
        apply_date = #{applyDate,jdbcType=DATE},
      </if>
      <if test="storeStatus != null">
        store_status = #{storeStatus,jdbcType=VARCHAR},
      </if>
      <if test="storeElectricAble != null">
        store_electric_able = #{storeElectricAble,jdbcType=VARCHAR},
      </if>
      <if test="distrCircle != null">
        distr_circle = #{distrCircle,jdbcType=VARCHAR},
      </if>
      <if test="distrTransitDay != null">
        distr_transit_day = #{distrTransitDay,jdbcType=INTEGER},
      </if>
      <if test="monthSalesLevel != null">
        month_sales_level = #{monthSalesLevel,jdbcType=VARCHAR},
      </if>
      <if test="storeCount != null">
        store_count = #{storeCount,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.iscm.entityTidb.StoreApplyDate">
    update store_apply_date
    set company_code = #{companyCode,jdbcType=VARCHAR},
      store_code = #{storeCode,jdbcType=VARCHAR},
      store_attr = #{storeAttr,jdbcType=VARCHAR},
      open_date = #{openDate,jdbcType=DATE},
      apply_date = #{applyDate,jdbcType=DATE},
      store_status = #{storeStatus,jdbcType=VARCHAR},
      store_electric_able = #{storeElectricAble,jdbcType=VARCHAR},
      distr_circle = #{distrCircle,jdbcType=VARCHAR},
      distr_transit_day = #{distrTransitDay,jdbcType=INTEGER},
      month_sales_level = #{monthSalesLevel,jdbcType=VARCHAR},
      store_count = #{storeCount,jdbcType=INTEGER},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>