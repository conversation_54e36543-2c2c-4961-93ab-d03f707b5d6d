<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapperTidb.IscmStoreDistributeApplyOrderMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entityTidb.IscmStoreDistributeApplyOrder">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="platform_name" jdbcType="VARCHAR" property="platformName" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="store_name" jdbcType="VARCHAR" property="storeName" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="apply_date" jdbcType="DATE" property="applyDate" />
    <result column="generate_time" jdbcType="TIMESTAMP" property="generateTime" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="specifications" jdbcType="VARCHAR" property="specifications" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="pos_apply_order_no" jdbcType="VARCHAR" property="posApplyOrderNo" />
    <result column="apply_quantity" jdbcType="DECIMAL" property="applyQuantity" />
    <result column="apporve_quantity" jdbcType="DECIMAL" property="apporveQuantity" />
    <result column="modify_result" jdbcType="TINYINT" property="modifyResult" />
    <result column="pos_average_daily_sales" jdbcType="DECIMAL" property="posAverageDailySales" />
    <result column="bdp_average_daily_sales" jdbcType="DECIMAL" property="bdpAverageDailySales" />
    <result column="store_then_month_sales" jdbcType="DECIMAL" property="storeThenMonthSales" />
    <result column="store_then_stock_quantity" jdbcType="DECIMAL" property="storeThenStockQuantity" />
    <result column="store_upper_limit" jdbcType="DECIMAL" property="storeUpperLimit" />
    <result column="store_lower_limit" jdbcType="DECIMAL" property="storeLowerLimit" />
    <result column="synthesize_average_daily_sales" jdbcType="DECIMAL" property="synthesizeAverageDailySales" />
    <result column="store_stock_quantity" jdbcType="DECIMAL" property="storeStockQuantity" />
    <result column="transit_quantity" jdbcType="DECIMAL" property="transitQuantity" />
    <result column="lower_display_quantity" jdbcType="DECIMAL" property="lowerDisplayQuantity" />
    <result column="order_type" jdbcType="TINYINT" property="orderType" />
    <result column="order_status" jdbcType="TINYINT" property="orderStatus" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, platform_name, company_code, company_name, store_name, store_code, apply_date, 
    generate_time, goods_no, goods_name, specifications, manufacturer, pos_apply_order_no, 
    apply_quantity, apporve_quantity, modify_result, pos_average_daily_sales, bdp_average_daily_sales, 
    store_then_month_sales, store_then_stock_quantity, store_upper_limit, store_lower_limit, 
    synthesize_average_daily_sales, store_stock_quantity, transit_quantity, lower_display_quantity, 
    order_type, order_status, `status`, gmt_create, gmt_update, extend, version, created_by, 
    created_name, updated_by, updated_name
  </sql>
  <select id="selectByExample" parameterType="com.cowell.iscm.entityTidb.IscmStoreDistributeApplyOrderExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from iscm_store_distribute_apply_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from iscm_store_distribute_apply_order
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from iscm_store_distribute_apply_order
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.iscm.entityTidb.IscmStoreDistributeApplyOrderExample">
    delete from iscm_store_distribute_apply_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.iscm.entityTidb.IscmStoreDistributeApplyOrder">
    insert into iscm_store_distribute_apply_order (id, platform_name, company_code, 
      company_name, store_name, store_code, 
      apply_date, generate_time, goods_no, 
      goods_name, specifications, manufacturer, 
      pos_apply_order_no, apply_quantity, apporve_quantity, 
      modify_result, pos_average_daily_sales, bdp_average_daily_sales, 
      store_then_month_sales, store_then_stock_quantity, 
      store_upper_limit, store_lower_limit, synthesize_average_daily_sales, 
      store_stock_quantity, transit_quantity, lower_display_quantity, 
      order_type, order_status, `status`, 
      gmt_create, gmt_update, extend, 
      version, created_by, created_name, 
      updated_by, updated_name)
    values (#{id,jdbcType=BIGINT}, #{platformName,jdbcType=VARCHAR}, #{companyCode,jdbcType=VARCHAR}, 
      #{companyName,jdbcType=VARCHAR}, #{storeName,jdbcType=VARCHAR}, #{storeCode,jdbcType=VARCHAR}, 
      #{applyDate,jdbcType=DATE}, #{generateTime,jdbcType=TIMESTAMP}, #{goodsNo,jdbcType=VARCHAR}, 
      #{goodsName,jdbcType=VARCHAR}, #{specifications,jdbcType=VARCHAR}, #{manufacturer,jdbcType=VARCHAR}, 
      #{posApplyOrderNo,jdbcType=VARCHAR}, #{applyQuantity,jdbcType=DECIMAL}, #{apporveQuantity,jdbcType=DECIMAL}, 
      #{modifyResult,jdbcType=TINYINT}, #{posAverageDailySales,jdbcType=DECIMAL}, #{bdpAverageDailySales,jdbcType=DECIMAL}, 
      #{storeThenMonthSales,jdbcType=DECIMAL}, #{storeThenStockQuantity,jdbcType=DECIMAL}, 
      #{storeUpperLimit,jdbcType=DECIMAL}, #{storeLowerLimit,jdbcType=DECIMAL}, #{synthesizeAverageDailySales,jdbcType=DECIMAL}, 
      #{storeStockQuantity,jdbcType=DECIMAL}, #{transitQuantity,jdbcType=DECIMAL}, #{lowerDisplayQuantity,jdbcType=DECIMAL}, 
      #{orderType,jdbcType=TINYINT}, #{orderStatus,jdbcType=TINYINT}, #{status,jdbcType=TINYINT}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}, #{extend,jdbcType=VARCHAR}, 
      #{version,jdbcType=INTEGER}, #{createdBy,jdbcType=BIGINT}, #{createdName,jdbcType=VARCHAR}, 
      #{updatedBy,jdbcType=BIGINT}, #{updatedName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.iscm.entityTidb.IscmStoreDistributeApplyOrder">
    insert into iscm_store_distribute_apply_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="platformName != null">
        platform_name,
      </if>
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="companyName != null">
        company_name,
      </if>
      <if test="storeName != null">
        store_name,
      </if>
      <if test="storeCode != null">
        store_code,
      </if>
      <if test="applyDate != null">
        apply_date,
      </if>
      <if test="generateTime != null">
        generate_time,
      </if>
      <if test="goodsNo != null">
        goods_no,
      </if>
      <if test="goodsName != null">
        goods_name,
      </if>
      <if test="specifications != null">
        specifications,
      </if>
      <if test="manufacturer != null">
        manufacturer,
      </if>
      <if test="posApplyOrderNo != null">
        pos_apply_order_no,
      </if>
      <if test="applyQuantity != null">
        apply_quantity,
      </if>
      <if test="apporveQuantity != null">
        apporve_quantity,
      </if>
      <if test="modifyResult != null">
        modify_result,
      </if>
      <if test="posAverageDailySales != null">
        pos_average_daily_sales,
      </if>
      <if test="bdpAverageDailySales != null">
        bdp_average_daily_sales,
      </if>
      <if test="storeThenMonthSales != null">
        store_then_month_sales,
      </if>
      <if test="storeThenStockQuantity != null">
        store_then_stock_quantity,
      </if>
      <if test="storeUpperLimit != null">
        store_upper_limit,
      </if>
      <if test="storeLowerLimit != null">
        store_lower_limit,
      </if>
      <if test="synthesizeAverageDailySales != null">
        synthesize_average_daily_sales,
      </if>
      <if test="storeStockQuantity != null">
        store_stock_quantity,
      </if>
      <if test="transitQuantity != null">
        transit_quantity,
      </if>
      <if test="lowerDisplayQuantity != null">
        lower_display_quantity,
      </if>
      <if test="orderType != null">
        order_type,
      </if>
      <if test="orderStatus != null">
        order_status,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedName != null">
        updated_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="platformName != null">
        #{platformName,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="storeName != null">
        #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="storeCode != null">
        #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="applyDate != null">
        #{applyDate,jdbcType=DATE},
      </if>
      <if test="generateTime != null">
        #{generateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="goodsNo != null">
        #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="specifications != null">
        #{specifications,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="posApplyOrderNo != null">
        #{posApplyOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="applyQuantity != null">
        #{applyQuantity,jdbcType=DECIMAL},
      </if>
      <if test="apporveQuantity != null">
        #{apporveQuantity,jdbcType=DECIMAL},
      </if>
      <if test="modifyResult != null">
        #{modifyResult,jdbcType=TINYINT},
      </if>
      <if test="posAverageDailySales != null">
        #{posAverageDailySales,jdbcType=DECIMAL},
      </if>
      <if test="bdpAverageDailySales != null">
        #{bdpAverageDailySales,jdbcType=DECIMAL},
      </if>
      <if test="storeThenMonthSales != null">
        #{storeThenMonthSales,jdbcType=DECIMAL},
      </if>
      <if test="storeThenStockQuantity != null">
        #{storeThenStockQuantity,jdbcType=DECIMAL},
      </if>
      <if test="storeUpperLimit != null">
        #{storeUpperLimit,jdbcType=DECIMAL},
      </if>
      <if test="storeLowerLimit != null">
        #{storeLowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="synthesizeAverageDailySales != null">
        #{synthesizeAverageDailySales,jdbcType=DECIMAL},
      </if>
      <if test="storeStockQuantity != null">
        #{storeStockQuantity,jdbcType=DECIMAL},
      </if>
      <if test="transitQuantity != null">
        #{transitQuantity,jdbcType=DECIMAL},
      </if>
      <if test="lowerDisplayQuantity != null">
        #{lowerDisplayQuantity,jdbcType=DECIMAL},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=TINYINT},
      </if>
      <if test="orderStatus != null">
        #{orderStatus,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        #{updatedName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.iscm.entityTidb.IscmStoreDistributeApplyOrderExample" resultType="java.lang.Long">
    select count(*) from iscm_store_distribute_apply_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update iscm_store_distribute_apply_order
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.platformName != null">
        platform_name = #{record.platformName,jdbcType=VARCHAR},
      </if>
      <if test="record.companyCode != null">
        company_code = #{record.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.companyName != null">
        company_name = #{record.companyName,jdbcType=VARCHAR},
      </if>
      <if test="record.storeName != null">
        store_name = #{record.storeName,jdbcType=VARCHAR},
      </if>
      <if test="record.storeCode != null">
        store_code = #{record.storeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.applyDate != null">
        apply_date = #{record.applyDate,jdbcType=DATE},
      </if>
      <if test="record.generateTime != null">
        generate_time = #{record.generateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.goodsNo != null">
        goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsName != null">
        goods_name = #{record.goodsName,jdbcType=VARCHAR},
      </if>
      <if test="record.specifications != null">
        specifications = #{record.specifications,jdbcType=VARCHAR},
      </if>
      <if test="record.manufacturer != null">
        manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="record.posApplyOrderNo != null">
        pos_apply_order_no = #{record.posApplyOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.applyQuantity != null">
        apply_quantity = #{record.applyQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.apporveQuantity != null">
        apporve_quantity = #{record.apporveQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.modifyResult != null">
        modify_result = #{record.modifyResult,jdbcType=TINYINT},
      </if>
      <if test="record.posAverageDailySales != null">
        pos_average_daily_sales = #{record.posAverageDailySales,jdbcType=DECIMAL},
      </if>
      <if test="record.bdpAverageDailySales != null">
        bdp_average_daily_sales = #{record.bdpAverageDailySales,jdbcType=DECIMAL},
      </if>
      <if test="record.storeThenMonthSales != null">
        store_then_month_sales = #{record.storeThenMonthSales,jdbcType=DECIMAL},
      </if>
      <if test="record.storeThenStockQuantity != null">
        store_then_stock_quantity = #{record.storeThenStockQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.storeUpperLimit != null">
        store_upper_limit = #{record.storeUpperLimit,jdbcType=DECIMAL},
      </if>
      <if test="record.storeLowerLimit != null">
        store_lower_limit = #{record.storeLowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="record.synthesizeAverageDailySales != null">
        synthesize_average_daily_sales = #{record.synthesizeAverageDailySales,jdbcType=DECIMAL},
      </if>
      <if test="record.storeStockQuantity != null">
        store_stock_quantity = #{record.storeStockQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.transitQuantity != null">
        transit_quantity = #{record.transitQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.lowerDisplayQuantity != null">
        lower_display_quantity = #{record.lowerDisplayQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.orderType != null">
        order_type = #{record.orderType,jdbcType=TINYINT},
      </if>
      <if test="record.orderStatus != null">
        order_status = #{record.orderStatus,jdbcType=TINYINT},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedName != null">
        updated_name = #{record.updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update iscm_store_distribute_apply_order
    set id = #{record.id,jdbcType=BIGINT},
      platform_name = #{record.platformName,jdbcType=VARCHAR},
      company_code = #{record.companyCode,jdbcType=VARCHAR},
      company_name = #{record.companyName,jdbcType=VARCHAR},
      store_name = #{record.storeName,jdbcType=VARCHAR},
      store_code = #{record.storeCode,jdbcType=VARCHAR},
      apply_date = #{record.applyDate,jdbcType=DATE},
      generate_time = #{record.generateTime,jdbcType=TIMESTAMP},
      goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      goods_name = #{record.goodsName,jdbcType=VARCHAR},
      specifications = #{record.specifications,jdbcType=VARCHAR},
      manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      pos_apply_order_no = #{record.posApplyOrderNo,jdbcType=VARCHAR},
      apply_quantity = #{record.applyQuantity,jdbcType=DECIMAL},
      apporve_quantity = #{record.apporveQuantity,jdbcType=DECIMAL},
      modify_result = #{record.modifyResult,jdbcType=TINYINT},
      pos_average_daily_sales = #{record.posAverageDailySales,jdbcType=DECIMAL},
      bdp_average_daily_sales = #{record.bdpAverageDailySales,jdbcType=DECIMAL},
      store_then_month_sales = #{record.storeThenMonthSales,jdbcType=DECIMAL},
      store_then_stock_quantity = #{record.storeThenStockQuantity,jdbcType=DECIMAL},
      store_upper_limit = #{record.storeUpperLimit,jdbcType=DECIMAL},
      store_lower_limit = #{record.storeLowerLimit,jdbcType=DECIMAL},
      synthesize_average_daily_sales = #{record.synthesizeAverageDailySales,jdbcType=DECIMAL},
      store_stock_quantity = #{record.storeStockQuantity,jdbcType=DECIMAL},
      transit_quantity = #{record.transitQuantity,jdbcType=DECIMAL},
      lower_display_quantity = #{record.lowerDisplayQuantity,jdbcType=DECIMAL},
      order_type = #{record.orderType,jdbcType=TINYINT},
      order_status = #{record.orderStatus,jdbcType=TINYINT},
      `status` = #{record.status,jdbcType=TINYINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_name = #{record.createdName,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_name = #{record.updatedName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.iscm.entityTidb.IscmStoreDistributeApplyOrder">
    update iscm_store_distribute_apply_order
    <set>
      <if test="platformName != null">
        platform_name = #{platformName,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="storeName != null">
        store_name = #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="storeCode != null">
        store_code = #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="applyDate != null">
        apply_date = #{applyDate,jdbcType=DATE},
      </if>
      <if test="generateTime != null">
        generate_time = #{generateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="goodsNo != null">
        goods_no = #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        goods_name = #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="specifications != null">
        specifications = #{specifications,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        manufacturer = #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="posApplyOrderNo != null">
        pos_apply_order_no = #{posApplyOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="applyQuantity != null">
        apply_quantity = #{applyQuantity,jdbcType=DECIMAL},
      </if>
      <if test="apporveQuantity != null">
        apporve_quantity = #{apporveQuantity,jdbcType=DECIMAL},
      </if>
      <if test="modifyResult != null">
        modify_result = #{modifyResult,jdbcType=TINYINT},
      </if>
      <if test="posAverageDailySales != null">
        pos_average_daily_sales = #{posAverageDailySales,jdbcType=DECIMAL},
      </if>
      <if test="bdpAverageDailySales != null">
        bdp_average_daily_sales = #{bdpAverageDailySales,jdbcType=DECIMAL},
      </if>
      <if test="storeThenMonthSales != null">
        store_then_month_sales = #{storeThenMonthSales,jdbcType=DECIMAL},
      </if>
      <if test="storeThenStockQuantity != null">
        store_then_stock_quantity = #{storeThenStockQuantity,jdbcType=DECIMAL},
      </if>
      <if test="storeUpperLimit != null">
        store_upper_limit = #{storeUpperLimit,jdbcType=DECIMAL},
      </if>
      <if test="storeLowerLimit != null">
        store_lower_limit = #{storeLowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="synthesizeAverageDailySales != null">
        synthesize_average_daily_sales = #{synthesizeAverageDailySales,jdbcType=DECIMAL},
      </if>
      <if test="storeStockQuantity != null">
        store_stock_quantity = #{storeStockQuantity,jdbcType=DECIMAL},
      </if>
      <if test="transitQuantity != null">
        transit_quantity = #{transitQuantity,jdbcType=DECIMAL},
      </if>
      <if test="lowerDisplayQuantity != null">
        lower_display_quantity = #{lowerDisplayQuantity,jdbcType=DECIMAL},
      </if>
      <if test="orderType != null">
        order_type = #{orderType,jdbcType=TINYINT},
      </if>
      <if test="orderStatus != null">
        order_status = #{orderStatus,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        updated_name = #{updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.iscm.entityTidb.IscmStoreDistributeApplyOrder">
    update iscm_store_distribute_apply_order
    set platform_name = #{platformName,jdbcType=VARCHAR},
      company_code = #{companyCode,jdbcType=VARCHAR},
      company_name = #{companyName,jdbcType=VARCHAR},
      store_name = #{storeName,jdbcType=VARCHAR},
      store_code = #{storeCode,jdbcType=VARCHAR},
      apply_date = #{applyDate,jdbcType=DATE},
      generate_time = #{generateTime,jdbcType=TIMESTAMP},
      goods_no = #{goodsNo,jdbcType=VARCHAR},
      goods_name = #{goodsName,jdbcType=VARCHAR},
      specifications = #{specifications,jdbcType=VARCHAR},
      manufacturer = #{manufacturer,jdbcType=VARCHAR},
      pos_apply_order_no = #{posApplyOrderNo,jdbcType=VARCHAR},
      apply_quantity = #{applyQuantity,jdbcType=DECIMAL},
      apporve_quantity = #{apporveQuantity,jdbcType=DECIMAL},
      modify_result = #{modifyResult,jdbcType=TINYINT},
      pos_average_daily_sales = #{posAverageDailySales,jdbcType=DECIMAL},
      bdp_average_daily_sales = #{bdpAverageDailySales,jdbcType=DECIMAL},
      store_then_month_sales = #{storeThenMonthSales,jdbcType=DECIMAL},
      store_then_stock_quantity = #{storeThenStockQuantity,jdbcType=DECIMAL},
      store_upper_limit = #{storeUpperLimit,jdbcType=DECIMAL},
      store_lower_limit = #{storeLowerLimit,jdbcType=DECIMAL},
      synthesize_average_daily_sales = #{synthesizeAverageDailySales,jdbcType=DECIMAL},
      store_stock_quantity = #{storeStockQuantity,jdbcType=DECIMAL},
      transit_quantity = #{transitQuantity,jdbcType=DECIMAL},
      lower_display_quantity = #{lowerDisplayQuantity,jdbcType=DECIMAL},
      order_type = #{orderType,jdbcType=TINYINT},
      order_status = #{orderStatus,jdbcType=TINYINT},
      `status` = #{status,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_name = #{createdName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_name = #{updatedName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>