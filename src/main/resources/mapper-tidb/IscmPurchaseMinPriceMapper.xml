<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapperTidb.IscmPurchaseMinPriceMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entityTidb.IscmPurchaseMinPrice">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="transfer_date" jdbcType="DATE" property="transferDate" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="goods_name_len" jdbcType="INTEGER" property="goodsNameLen" />
    <result column="manufacturer_code" jdbcType="VARCHAR" property="manufacturerCode" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="manufacturer_len" jdbcType="INTEGER" property="manufacturerLen" />
    <result column="purchase_org_type" jdbcType="TINYINT" property="purchaseOrgType" />
    <result column="three_purchase_min_price" jdbcType="DECIMAL" property="threePurchaseMinPrice" />
    <result column="six_purchase_min_price" jdbcType="DECIMAL" property="sixPurchaseMinPrice" />
    <result column="twelve_purchase_min_price" jdbcType="DECIMAL" property="twelvePurchaseMinPrice" />
    <result column="three_purchase_date" jdbcType="DATE" property="threePurchaseDate" />
    <result column="six_purchase_date" jdbcType="DATE" property="sixPurchaseDate" />
    <result column="twelve_purchase_date" jdbcType="DATE" property="twelvePurchaseDate" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, transfer_date, goods_no, goods_name, goods_name_len, manufacturer_code, manufacturer, 
    manufacturer_len, purchase_org_type, three_purchase_min_price, six_purchase_min_price, 
    twelve_purchase_min_price, three_purchase_date, six_purchase_date, twelve_purchase_date, 
    gmt_create, gmt_update
  </sql>
  <select id="selectByExample" parameterType="com.cowell.iscm.entityTidb.IscmPurchaseMinPriceExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from iscm_purchase_min_price
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from iscm_purchase_min_price
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from iscm_purchase_min_price
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.iscm.entityTidb.IscmPurchaseMinPriceExample">
    delete from iscm_purchase_min_price
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entityTidb.IscmPurchaseMinPrice" useGeneratedKeys="true">
    insert into iscm_purchase_min_price (transfer_date, goods_no, goods_name, 
      goods_name_len, manufacturer_code, manufacturer, 
      manufacturer_len, purchase_org_type, three_purchase_min_price, 
      six_purchase_min_price, twelve_purchase_min_price, 
      three_purchase_date, six_purchase_date, twelve_purchase_date, 
      gmt_create, gmt_update)
    values (#{transferDate,jdbcType=DATE}, #{goodsNo,jdbcType=VARCHAR}, #{goodsName,jdbcType=VARCHAR}, 
      #{goodsNameLen,jdbcType=INTEGER}, #{manufacturerCode,jdbcType=VARCHAR}, #{manufacturer,jdbcType=VARCHAR}, 
      #{manufacturerLen,jdbcType=INTEGER}, #{purchaseOrgType,jdbcType=TINYINT}, #{threePurchaseMinPrice,jdbcType=DECIMAL}, 
      #{sixPurchaseMinPrice,jdbcType=DECIMAL}, #{twelvePurchaseMinPrice,jdbcType=DECIMAL}, 
      #{threePurchaseDate,jdbcType=DATE}, #{sixPurchaseDate,jdbcType=DATE}, #{twelvePurchaseDate,jdbcType=DATE}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entityTidb.IscmPurchaseMinPrice" useGeneratedKeys="true">
    insert into iscm_purchase_min_price
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="transferDate != null">
        transfer_date,
      </if>
      <if test="goodsNo != null">
        goods_no,
      </if>
      <if test="goodsName != null">
        goods_name,
      </if>
      <if test="goodsNameLen != null">
        goods_name_len,
      </if>
      <if test="manufacturerCode != null">
        manufacturer_code,
      </if>
      <if test="manufacturer != null">
        manufacturer,
      </if>
      <if test="manufacturerLen != null">
        manufacturer_len,
      </if>
      <if test="purchaseOrgType != null">
        purchase_org_type,
      </if>
      <if test="threePurchaseMinPrice != null">
        three_purchase_min_price,
      </if>
      <if test="sixPurchaseMinPrice != null">
        six_purchase_min_price,
      </if>
      <if test="twelvePurchaseMinPrice != null">
        twelve_purchase_min_price,
      </if>
      <if test="threePurchaseDate != null">
        three_purchase_date,
      </if>
      <if test="sixPurchaseDate != null">
        six_purchase_date,
      </if>
      <if test="twelvePurchaseDate != null">
        twelve_purchase_date,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="transferDate != null">
        #{transferDate,jdbcType=DATE},
      </if>
      <if test="goodsNo != null">
        #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="goodsNameLen != null">
        #{goodsNameLen,jdbcType=INTEGER},
      </if>
      <if test="manufacturerCode != null">
        #{manufacturerCode,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="manufacturerLen != null">
        #{manufacturerLen,jdbcType=INTEGER},
      </if>
      <if test="purchaseOrgType != null">
        #{purchaseOrgType,jdbcType=TINYINT},
      </if>
      <if test="threePurchaseMinPrice != null">
        #{threePurchaseMinPrice,jdbcType=DECIMAL},
      </if>
      <if test="sixPurchaseMinPrice != null">
        #{sixPurchaseMinPrice,jdbcType=DECIMAL},
      </if>
      <if test="twelvePurchaseMinPrice != null">
        #{twelvePurchaseMinPrice,jdbcType=DECIMAL},
      </if>
      <if test="threePurchaseDate != null">
        #{threePurchaseDate,jdbcType=DATE},
      </if>
      <if test="sixPurchaseDate != null">
        #{sixPurchaseDate,jdbcType=DATE},
      </if>
      <if test="twelvePurchaseDate != null">
        #{twelvePurchaseDate,jdbcType=DATE},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.iscm.entityTidb.IscmPurchaseMinPriceExample" resultType="java.lang.Long">
    select count(*) from iscm_purchase_min_price
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update iscm_purchase_min_price
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.transferDate != null">
        transfer_date = #{record.transferDate,jdbcType=DATE},
      </if>
      <if test="record.goodsNo != null">
        goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsName != null">
        goods_name = #{record.goodsName,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsNameLen != null">
        goods_name_len = #{record.goodsNameLen,jdbcType=INTEGER},
      </if>
      <if test="record.manufacturerCode != null">
        manufacturer_code = #{record.manufacturerCode,jdbcType=VARCHAR},
      </if>
      <if test="record.manufacturer != null">
        manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="record.manufacturerLen != null">
        manufacturer_len = #{record.manufacturerLen,jdbcType=INTEGER},
      </if>
      <if test="record.purchaseOrgType != null">
        purchase_org_type = #{record.purchaseOrgType,jdbcType=TINYINT},
      </if>
      <if test="record.threePurchaseMinPrice != null">
        three_purchase_min_price = #{record.threePurchaseMinPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.sixPurchaseMinPrice != null">
        six_purchase_min_price = #{record.sixPurchaseMinPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.twelvePurchaseMinPrice != null">
        twelve_purchase_min_price = #{record.twelvePurchaseMinPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.threePurchaseDate != null">
        three_purchase_date = #{record.threePurchaseDate,jdbcType=DATE},
      </if>
      <if test="record.sixPurchaseDate != null">
        six_purchase_date = #{record.sixPurchaseDate,jdbcType=DATE},
      </if>
      <if test="record.twelvePurchaseDate != null">
        twelve_purchase_date = #{record.twelvePurchaseDate,jdbcType=DATE},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update iscm_purchase_min_price
    set id = #{record.id,jdbcType=BIGINT},
      transfer_date = #{record.transferDate,jdbcType=DATE},
      goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      goods_name = #{record.goodsName,jdbcType=VARCHAR},
      goods_name_len = #{record.goodsNameLen,jdbcType=INTEGER},
      manufacturer_code = #{record.manufacturerCode,jdbcType=VARCHAR},
      manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      manufacturer_len = #{record.manufacturerLen,jdbcType=INTEGER},
      purchase_org_type = #{record.purchaseOrgType,jdbcType=TINYINT},
      three_purchase_min_price = #{record.threePurchaseMinPrice,jdbcType=DECIMAL},
      six_purchase_min_price = #{record.sixPurchaseMinPrice,jdbcType=DECIMAL},
      twelve_purchase_min_price = #{record.twelvePurchaseMinPrice,jdbcType=DECIMAL},
      three_purchase_date = #{record.threePurchaseDate,jdbcType=DATE},
      six_purchase_date = #{record.sixPurchaseDate,jdbcType=DATE},
      twelve_purchase_date = #{record.twelvePurchaseDate,jdbcType=DATE},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.iscm.entityTidb.IscmPurchaseMinPrice">
    update iscm_purchase_min_price
    <set>
      <if test="transferDate != null">
        transfer_date = #{transferDate,jdbcType=DATE},
      </if>
      <if test="goodsNo != null">
        goods_no = #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        goods_name = #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="goodsNameLen != null">
        goods_name_len = #{goodsNameLen,jdbcType=INTEGER},
      </if>
      <if test="manufacturerCode != null">
        manufacturer_code = #{manufacturerCode,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        manufacturer = #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="manufacturerLen != null">
        manufacturer_len = #{manufacturerLen,jdbcType=INTEGER},
      </if>
      <if test="purchaseOrgType != null">
        purchase_org_type = #{purchaseOrgType,jdbcType=TINYINT},
      </if>
      <if test="threePurchaseMinPrice != null">
        three_purchase_min_price = #{threePurchaseMinPrice,jdbcType=DECIMAL},
      </if>
      <if test="sixPurchaseMinPrice != null">
        six_purchase_min_price = #{sixPurchaseMinPrice,jdbcType=DECIMAL},
      </if>
      <if test="twelvePurchaseMinPrice != null">
        twelve_purchase_min_price = #{twelvePurchaseMinPrice,jdbcType=DECIMAL},
      </if>
      <if test="threePurchaseDate != null">
        three_purchase_date = #{threePurchaseDate,jdbcType=DATE},
      </if>
      <if test="sixPurchaseDate != null">
        six_purchase_date = #{sixPurchaseDate,jdbcType=DATE},
      </if>
      <if test="twelvePurchaseDate != null">
        twelve_purchase_date = #{twelvePurchaseDate,jdbcType=DATE},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.iscm.entityTidb.IscmPurchaseMinPrice">
    update iscm_purchase_min_price
    set transfer_date = #{transferDate,jdbcType=DATE},
      goods_no = #{goodsNo,jdbcType=VARCHAR},
      goods_name = #{goodsName,jdbcType=VARCHAR},
      goods_name_len = #{goodsNameLen,jdbcType=INTEGER},
      manufacturer_code = #{manufacturerCode,jdbcType=VARCHAR},
      manufacturer = #{manufacturer,jdbcType=VARCHAR},
      manufacturer_len = #{manufacturerLen,jdbcType=INTEGER},
      purchase_org_type = #{purchaseOrgType,jdbcType=TINYINT},
      three_purchase_min_price = #{threePurchaseMinPrice,jdbcType=DECIMAL},
      six_purchase_min_price = #{sixPurchaseMinPrice,jdbcType=DECIMAL},
      twelve_purchase_min_price = #{twelvePurchaseMinPrice,jdbcType=DECIMAL},
      three_purchase_date = #{threePurchaseDate,jdbcType=DATE},
      six_purchase_date = #{sixPurchaseDate,jdbcType=DATE},
      twelve_purchase_date = #{twelvePurchaseDate,jdbcType=DATE},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>