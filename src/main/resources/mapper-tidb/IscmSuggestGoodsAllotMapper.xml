<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapperTidb.IscmSuggestGoodsAllotMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entityTidb.IscmSuggestGoodsAllot">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="business_date" jdbcType="TIMESTAMP" property="businessDate" />
    <result column="allot_no" jdbcType="VARCHAR" property="allotNo" />
    <result column="allot_detail_no" jdbcType="VARCHAR" property="allotDetailNo" />
    <result column="register_no" jdbcType="VARCHAR" property="registerNo" />
    <result column="allot_type" jdbcType="TINYINT" property="allotType" />
    <result column="register_source" jdbcType="TINYINT" property="registerSource" />
    <result column="out_company_code" jdbcType="VARCHAR" property="outCompanyCode" />
    <result column="in_company_code" jdbcType="VARCHAR" property="inCompanyCode" />
    <result column="out_store_code" jdbcType="VARCHAR" property="outStoreCode" />
    <result column="in_store_code" jdbcType="VARCHAR" property="inStoreCode" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
    <result column="suggest_allot_quantity" jdbcType="DECIMAL" property="suggestAllotQuantity" />
    <result column="real_allot_quantity" jdbcType="DECIMAL" property="realAllotQuantity" />
    <result column="expect_sale_days" jdbcType="DECIMAL" property="expectSaleDays" />
    <result column="approve_status" jdbcType="TINYINT" property="approveStatus" />
    <result column="approve_by" jdbcType="BIGINT" property="approveBy" />
    <result column="approve_name" jdbcType="VARCHAR" property="approveName" />
    <result column="approve_time" jdbcType="TIMESTAMP" property="approveTime" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, business_date, allot_no, allot_detail_no, register_no, allot_type, register_source, 
    out_company_code, in_company_code, out_store_code, in_store_code, goods_no, batch_no, 
    suggest_allot_quantity, real_allot_quantity, expect_sale_days, approve_status, approve_by, 
    approve_name, approve_time, status, gmt_create, gmt_update, extend, version, created_by, 
    created_name, updated_by, updated_name
  </sql>
  <select id="selectByExample" parameterType="com.cowell.iscm.entityTidb.IscmSuggestGoodsAllotExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from iscm_suggest_goods_allot
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from iscm_suggest_goods_allot
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from iscm_suggest_goods_allot
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.iscm.entityTidb.IscmSuggestGoodsAllotExample">
    delete from iscm_suggest_goods_allot
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.iscm.entityTidb.IscmSuggestGoodsAllot">
    insert into iscm_suggest_goods_allot (id, business_date, allot_no, 
      allot_detail_no, register_no, allot_type, 
      register_source, out_company_code, in_company_code, 
      out_store_code, in_store_code, goods_no, 
      batch_no, suggest_allot_quantity, real_allot_quantity, 
      expect_sale_days, approve_status, approve_by, 
      approve_name, approve_time, status, 
      gmt_create, gmt_update, extend, 
      version, created_by, created_name, 
      updated_by, updated_name)
    values (#{id,jdbcType=BIGINT}, #{businessDate,jdbcType=TIMESTAMP}, #{allotNo,jdbcType=VARCHAR}, 
      #{allotDetailNo,jdbcType=VARCHAR}, #{registerNo,jdbcType=VARCHAR}, #{allotType,jdbcType=TINYINT}, 
      #{registerSource,jdbcType=TINYINT}, #{outCompanyCode,jdbcType=VARCHAR}, #{inCompanyCode,jdbcType=VARCHAR}, 
      #{outStoreCode,jdbcType=VARCHAR}, #{inStoreCode,jdbcType=VARCHAR}, #{goodsNo,jdbcType=VARCHAR}, 
      #{batchNo,jdbcType=VARCHAR}, #{suggestAllotQuantity,jdbcType=DECIMAL}, #{realAllotQuantity,jdbcType=DECIMAL}, 
      #{expectSaleDays,jdbcType=DECIMAL}, #{approveStatus,jdbcType=TINYINT}, #{approveBy,jdbcType=BIGINT}, 
      #{approveName,jdbcType=VARCHAR}, #{approveTime,jdbcType=TIMESTAMP}, #{status,jdbcType=TINYINT}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}, #{extend,jdbcType=VARCHAR}, 
      #{version,jdbcType=INTEGER}, #{createdBy,jdbcType=BIGINT}, #{createdName,jdbcType=VARCHAR}, 
      #{updatedBy,jdbcType=BIGINT}, #{updatedName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.iscm.entityTidb.IscmSuggestGoodsAllot">
    insert into iscm_suggest_goods_allot
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="businessDate != null">
        business_date,
      </if>
      <if test="allotNo != null">
        allot_no,
      </if>
      <if test="allotDetailNo != null">
        allot_detail_no,
      </if>
      <if test="registerNo != null">
        register_no,
      </if>
      <if test="allotType != null">
        allot_type,
      </if>
      <if test="registerSource != null">
        register_source,
      </if>
      <if test="outCompanyCode != null">
        out_company_code,
      </if>
      <if test="inCompanyCode != null">
        in_company_code,
      </if>
      <if test="outStoreCode != null">
        out_store_code,
      </if>
      <if test="inStoreCode != null">
        in_store_code,
      </if>
      <if test="goodsNo != null">
        goods_no,
      </if>
      <if test="batchNo != null">
        batch_no,
      </if>
      <if test="suggestAllotQuantity != null">
        suggest_allot_quantity,
      </if>
      <if test="realAllotQuantity != null">
        real_allot_quantity,
      </if>
      <if test="expectSaleDays != null">
        expect_sale_days,
      </if>
      <if test="approveStatus != null">
        approve_status,
      </if>
      <if test="approveBy != null">
        approve_by,
      </if>
      <if test="approveName != null">
        approve_name,
      </if>
      <if test="approveTime != null">
        approve_time,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedName != null">
        updated_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="businessDate != null">
        #{businessDate,jdbcType=TIMESTAMP},
      </if>
      <if test="allotNo != null">
        #{allotNo,jdbcType=VARCHAR},
      </if>
      <if test="allotDetailNo != null">
        #{allotDetailNo,jdbcType=VARCHAR},
      </if>
      <if test="registerNo != null">
        #{registerNo,jdbcType=VARCHAR},
      </if>
      <if test="allotType != null">
        #{allotType,jdbcType=TINYINT},
      </if>
      <if test="registerSource != null">
        #{registerSource,jdbcType=TINYINT},
      </if>
      <if test="outCompanyCode != null">
        #{outCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="inCompanyCode != null">
        #{inCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="outStoreCode != null">
        #{outStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="inStoreCode != null">
        #{inStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsNo != null">
        #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="batchNo != null">
        #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="suggestAllotQuantity != null">
        #{suggestAllotQuantity,jdbcType=DECIMAL},
      </if>
      <if test="realAllotQuantity != null">
        #{realAllotQuantity,jdbcType=DECIMAL},
      </if>
      <if test="expectSaleDays != null">
        #{expectSaleDays,jdbcType=DECIMAL},
      </if>
      <if test="approveStatus != null">
        #{approveStatus,jdbcType=TINYINT},
      </if>
      <if test="approveBy != null">
        #{approveBy,jdbcType=BIGINT},
      </if>
      <if test="approveName != null">
        #{approveName,jdbcType=VARCHAR},
      </if>
      <if test="approveTime != null">
        #{approveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        #{updatedName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.iscm.entityTidb.IscmSuggestGoodsAllotExample" resultType="java.lang.Long">
    select count(*) from iscm_suggest_goods_allot
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update iscm_suggest_goods_allot
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.businessDate != null">
        business_date = #{record.businessDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.allotNo != null">
        allot_no = #{record.allotNo,jdbcType=VARCHAR},
      </if>
      <if test="record.allotDetailNo != null">
        allot_detail_no = #{record.allotDetailNo,jdbcType=VARCHAR},
      </if>
      <if test="record.registerNo != null">
        register_no = #{record.registerNo,jdbcType=VARCHAR},
      </if>
      <if test="record.allotType != null">
        allot_type = #{record.allotType,jdbcType=TINYINT},
      </if>
      <if test="record.registerSource != null">
        register_source = #{record.registerSource,jdbcType=TINYINT},
      </if>
      <if test="record.outCompanyCode != null">
        out_company_code = #{record.outCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.inCompanyCode != null">
        in_company_code = #{record.inCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.outStoreCode != null">
        out_store_code = #{record.outStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="record.inStoreCode != null">
        in_store_code = #{record.inStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsNo != null">
        goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="record.batchNo != null">
        batch_no = #{record.batchNo,jdbcType=VARCHAR},
      </if>
      <if test="record.suggestAllotQuantity != null">
        suggest_allot_quantity = #{record.suggestAllotQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.realAllotQuantity != null">
        real_allot_quantity = #{record.realAllotQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.expectSaleDays != null">
        expect_sale_days = #{record.expectSaleDays,jdbcType=DECIMAL},
      </if>
      <if test="record.approveStatus != null">
        approve_status = #{record.approveStatus,jdbcType=TINYINT},
      </if>
      <if test="record.approveBy != null">
        approve_by = #{record.approveBy,jdbcType=BIGINT},
      </if>
      <if test="record.approveName != null">
        approve_name = #{record.approveName,jdbcType=VARCHAR},
      </if>
      <if test="record.approveTime != null">
        approve_time = #{record.approveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedName != null">
        updated_name = #{record.updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update iscm_suggest_goods_allot
    set id = #{record.id,jdbcType=BIGINT},
      business_date = #{record.businessDate,jdbcType=TIMESTAMP},
      allot_no = #{record.allotNo,jdbcType=VARCHAR},
      allot_detail_no = #{record.allotDetailNo,jdbcType=VARCHAR},
      register_no = #{record.registerNo,jdbcType=VARCHAR},
      allot_type = #{record.allotType,jdbcType=TINYINT},
      register_source = #{record.registerSource,jdbcType=TINYINT},
      out_company_code = #{record.outCompanyCode,jdbcType=VARCHAR},
      in_company_code = #{record.inCompanyCode,jdbcType=VARCHAR},
      out_store_code = #{record.outStoreCode,jdbcType=VARCHAR},
      in_store_code = #{record.inStoreCode,jdbcType=VARCHAR},
      goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      batch_no = #{record.batchNo,jdbcType=VARCHAR},
      suggest_allot_quantity = #{record.suggestAllotQuantity,jdbcType=DECIMAL},
      real_allot_quantity = #{record.realAllotQuantity,jdbcType=DECIMAL},
      expect_sale_days = #{record.expectSaleDays,jdbcType=DECIMAL},
      approve_status = #{record.approveStatus,jdbcType=TINYINT},
      approve_by = #{record.approveBy,jdbcType=BIGINT},
      approve_name = #{record.approveName,jdbcType=VARCHAR},
      approve_time = #{record.approveTime,jdbcType=TIMESTAMP},
      status = #{record.status,jdbcType=TINYINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_name = #{record.createdName,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_name = #{record.updatedName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.iscm.entityTidb.IscmSuggestGoodsAllot">
    update iscm_suggest_goods_allot
    <set>
      <if test="businessDate != null">
        business_date = #{businessDate,jdbcType=TIMESTAMP},
      </if>
      <if test="allotNo != null">
        allot_no = #{allotNo,jdbcType=VARCHAR},
      </if>
      <if test="allotDetailNo != null">
        allot_detail_no = #{allotDetailNo,jdbcType=VARCHAR},
      </if>
      <if test="registerNo != null">
        register_no = #{registerNo,jdbcType=VARCHAR},
      </if>
      <if test="allotType != null">
        allot_type = #{allotType,jdbcType=TINYINT},
      </if>
      <if test="registerSource != null">
        register_source = #{registerSource,jdbcType=TINYINT},
      </if>
      <if test="outCompanyCode != null">
        out_company_code = #{outCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="inCompanyCode != null">
        in_company_code = #{inCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="outStoreCode != null">
        out_store_code = #{outStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="inStoreCode != null">
        in_store_code = #{inStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsNo != null">
        goods_no = #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="batchNo != null">
        batch_no = #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="suggestAllotQuantity != null">
        suggest_allot_quantity = #{suggestAllotQuantity,jdbcType=DECIMAL},
      </if>
      <if test="realAllotQuantity != null">
        real_allot_quantity = #{realAllotQuantity,jdbcType=DECIMAL},
      </if>
      <if test="expectSaleDays != null">
        expect_sale_days = #{expectSaleDays,jdbcType=DECIMAL},
      </if>
      <if test="approveStatus != null">
        approve_status = #{approveStatus,jdbcType=TINYINT},
      </if>
      <if test="approveBy != null">
        approve_by = #{approveBy,jdbcType=BIGINT},
      </if>
      <if test="approveName != null">
        approve_name = #{approveName,jdbcType=VARCHAR},
      </if>
      <if test="approveTime != null">
        approve_time = #{approveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        updated_name = #{updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.iscm.entityTidb.IscmSuggestGoodsAllot">
    update iscm_suggest_goods_allot
    set business_date = #{businessDate,jdbcType=TIMESTAMP},
      allot_no = #{allotNo,jdbcType=VARCHAR},
      allot_detail_no = #{allotDetailNo,jdbcType=VARCHAR},
      register_no = #{registerNo,jdbcType=VARCHAR},
      allot_type = #{allotType,jdbcType=TINYINT},
      register_source = #{registerSource,jdbcType=TINYINT},
      out_company_code = #{outCompanyCode,jdbcType=VARCHAR},
      in_company_code = #{inCompanyCode,jdbcType=VARCHAR},
      out_store_code = #{outStoreCode,jdbcType=VARCHAR},
      in_store_code = #{inStoreCode,jdbcType=VARCHAR},
      goods_no = #{goodsNo,jdbcType=VARCHAR},
      batch_no = #{batchNo,jdbcType=VARCHAR},
      suggest_allot_quantity = #{suggestAllotQuantity,jdbcType=DECIMAL},
      real_allot_quantity = #{realAllotQuantity,jdbcType=DECIMAL},
      expect_sale_days = #{expectSaleDays,jdbcType=DECIMAL},
      approve_status = #{approveStatus,jdbcType=TINYINT},
      approve_by = #{approveBy,jdbcType=BIGINT},
      approve_name = #{approveName,jdbcType=VARCHAR},
      approve_time = #{approveTime,jdbcType=TIMESTAMP},
      status = #{status,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_name = #{createdName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_name = #{updatedName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>