<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapperTidb.IscmNodePlanTimeMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entityTidb.IscmNodePlanTime">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="pos_genorder_time" jdbcType="TIME" property="posGenorderTime" />
    <result column="pos_mergeorder_time" jdbcType="TIME" property="posMergeorderTime" />
    <result column="sap_sap_purchase_approve_time" jdbcType="TIME" property="sapSapPurchaseApproveTime" />
    <result column="bdp_distribute_time" jdbcType="TIME" property="bdpDistributeTime" />
    <result column="sap_sap_transfer_time" jdbcType="TIME" property="sapSapTransferTime" />
    <result column="dt" jdbcType="VARCHAR" property="dt" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, company_code, company_name, pos_genorder_time, pos_mergeorder_time, sap_sap_purchase_approve_time, 
    bdp_distribute_time, sap_sap_transfer_time, dt
  </sql>
  <select id="selectByExample" parameterType="com.cowell.iscm.entityTidb.IscmNodePlanTimeExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from iscm_node_plan_time
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from iscm_node_plan_time
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from iscm_node_plan_time
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.iscm.entityTidb.IscmNodePlanTimeExample">
    delete from iscm_node_plan_time
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.iscm.entityTidb.IscmNodePlanTime">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into iscm_node_plan_time (company_code, company_name, pos_genorder_time, 
      pos_mergeorder_time, sap_sap_purchase_approve_time, 
      bdp_distribute_time, sap_sap_transfer_time, dt
      )
    values (#{companyCode,jdbcType=VARCHAR}, #{companyName,jdbcType=VARCHAR}, #{posGenorderTime,jdbcType=TIME}, 
      #{posMergeorderTime,jdbcType=TIME}, #{sapSapPurchaseApproveTime,jdbcType=TIME}, 
      #{bdpDistributeTime,jdbcType=TIME}, #{sapSapTransferTime,jdbcType=TIME}, #{dt,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.iscm.entityTidb.IscmNodePlanTime">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into iscm_node_plan_time
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="companyName != null">
        company_name,
      </if>
      <if test="posGenorderTime != null">
        pos_genorder_time,
      </if>
      <if test="posMergeorderTime != null">
        pos_mergeorder_time,
      </if>
      <if test="sapSapPurchaseApproveTime != null">
        sap_sap_purchase_approve_time,
      </if>
      <if test="bdpDistributeTime != null">
        bdp_distribute_time,
      </if>
      <if test="sapSapTransferTime != null">
        sap_sap_transfer_time,
      </if>
      <if test="dt != null">
        dt,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="posGenorderTime != null">
        #{posGenorderTime,jdbcType=TIME},
      </if>
      <if test="posMergeorderTime != null">
        #{posMergeorderTime,jdbcType=TIME},
      </if>
      <if test="sapSapPurchaseApproveTime != null">
        #{sapSapPurchaseApproveTime,jdbcType=TIME},
      </if>
      <if test="bdpDistributeTime != null">
        #{bdpDistributeTime,jdbcType=TIME},
      </if>
      <if test="sapSapTransferTime != null">
        #{sapSapTransferTime,jdbcType=TIME},
      </if>
      <if test="dt != null">
        #{dt,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.iscm.entityTidb.IscmNodePlanTimeExample" resultType="java.lang.Long">
    select count(*) from iscm_node_plan_time
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update iscm_node_plan_time
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.companyCode != null">
        company_code = #{record.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.companyName != null">
        company_name = #{record.companyName,jdbcType=VARCHAR},
      </if>
      <if test="record.posGenorderTime != null">
        pos_genorder_time = #{record.posGenorderTime,jdbcType=TIME},
      </if>
      <if test="record.posMergeorderTime != null">
        pos_mergeorder_time = #{record.posMergeorderTime,jdbcType=TIME},
      </if>
      <if test="record.sapSapPurchaseApproveTime != null">
        sap_sap_purchase_approve_time = #{record.sapSapPurchaseApproveTime,jdbcType=TIME},
      </if>
      <if test="record.bdpDistributeTime != null">
        bdp_distribute_time = #{record.bdpDistributeTime,jdbcType=TIME},
      </if>
      <if test="record.sapSapTransferTime != null">
        sap_sap_transfer_time = #{record.sapSapTransferTime,jdbcType=TIME},
      </if>
      <if test="record.dt != null">
        dt = #{record.dt,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update iscm_node_plan_time
    set id = #{record.id,jdbcType=BIGINT},
      company_code = #{record.companyCode,jdbcType=VARCHAR},
      company_name = #{record.companyName,jdbcType=VARCHAR},
      pos_genorder_time = #{record.posGenorderTime,jdbcType=TIME},
      pos_mergeorder_time = #{record.posMergeorderTime,jdbcType=TIME},
      sap_sap_purchase_approve_time = #{record.sapSapPurchaseApproveTime,jdbcType=TIME},
      bdp_distribute_time = #{record.bdpDistributeTime,jdbcType=TIME},
      sap_sap_transfer_time = #{record.sapSapTransferTime,jdbcType=TIME},
      dt = #{record.dt,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.iscm.entityTidb.IscmNodePlanTime">
    update iscm_node_plan_time
    <set>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="posGenorderTime != null">
        pos_genorder_time = #{posGenorderTime,jdbcType=TIME},
      </if>
      <if test="posMergeorderTime != null">
        pos_mergeorder_time = #{posMergeorderTime,jdbcType=TIME},
      </if>
      <if test="sapSapPurchaseApproveTime != null">
        sap_sap_purchase_approve_time = #{sapSapPurchaseApproveTime,jdbcType=TIME},
      </if>
      <if test="bdpDistributeTime != null">
        bdp_distribute_time = #{bdpDistributeTime,jdbcType=TIME},
      </if>
      <if test="sapSapTransferTime != null">
        sap_sap_transfer_time = #{sapSapTransferTime,jdbcType=TIME},
      </if>
      <if test="dt != null">
        dt = #{dt,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.iscm.entityTidb.IscmNodePlanTime">
    update iscm_node_plan_time
    set company_code = #{companyCode,jdbcType=VARCHAR},
      company_name = #{companyName,jdbcType=VARCHAR},
      pos_genorder_time = #{posGenorderTime,jdbcType=TIME},
      pos_mergeorder_time = #{posMergeorderTime,jdbcType=TIME},
      sap_sap_purchase_approve_time = #{sapSapPurchaseApproveTime,jdbcType=TIME},
      bdp_distribute_time = #{bdpDistributeTime,jdbcType=TIME},
      sap_sap_transfer_time = #{sapSapTransferTime,jdbcType=TIME},
      dt = #{dt,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>