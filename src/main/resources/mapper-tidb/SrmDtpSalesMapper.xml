<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapperTidb.SrmDtpSalesMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entityTidb.SrmDtpSales">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="business_day_date" jdbcType="DATE" property="businessDayDate" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="lifnr" jdbcType="VARCHAR" property="lifnr" />
    <result column="lifnr_name" jdbcType="VARCHAR" property="lifnrName" />
    <result column="retail_store_id" jdbcType="VARCHAR" property="retailStoreId" />
    <result column="retail_store_name" jdbcType="VARCHAR" property="retailStoreName" />
    <result column="temid" jdbcType="VARCHAR" property="temid" />
    <result column="temid_name" jdbcType="VARCHAR" property="temidName" />
    <result column="temid_spec" jdbcType="VARCHAR" property="temidSpec" />
    <result column="manu_id" jdbcType="VARCHAR" property="manuId" />
    <result column="manu" jdbcType="VARCHAR" property="manu" />
    <result column="batch_code" jdbcType="VARCHAR" property="batchCode" />
    <result column="eff_date" jdbcType="DATE" property="effDate" />
    <result column="product_date" jdbcType="DATE" property="productDate" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="bar_code" jdbcType="VARCHAR" property="barCode" />
    <result column="retail_quantity" jdbcType="DECIMAL" property="retailQuantity" />
    <result column="retail_amount" jdbcType="DECIMAL" property="retailAmount" />
    <result column="hospital_id" jdbcType="VARCHAR" property="hospitalId" />
    <result column="hospital_name" jdbcType="VARCHAR" property="hospitalName" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="is_sap" jdbcType="TINYINT" property="isSap" />
    <result column="prescription_number" jdbcType="VARCHAR" property="prescriptionNumber" />
    <result column="prescription_date" jdbcType="TIMESTAMP" property="prescriptionDate" />
    <result column="doctor_name" jdbcType="VARCHAR" property="doctorName" />
    <result column="department" jdbcType="VARCHAR" property="department" />
    <result column="disease_description" jdbcType="VARCHAR" property="diseaseDescription" />
    <result column="approval_number" jdbcType="VARCHAR" property="approvalNumber" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, business_day_date, company_code, company_name, lifnr, lifnr_name, retail_store_id, 
    retail_store_name, temid, temid_name, temid_spec, manu_id, manu, batch_code, eff_date, 
    product_date, unit, bar_code, retail_quantity, retail_amount, hospital_id, hospital_name, 
    order_no, is_sap, prescription_number, prescription_date, doctor_name, department, 
    disease_description, approval_number
  </sql>
  <select id="selectByExample" parameterType="com.cowell.iscm.entityTidb.SrmDtpSalesExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from srm_dtp_sales
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from srm_dtp_sales
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from srm_dtp_sales
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.iscm.entityTidb.SrmDtpSalesExample">
    delete from srm_dtp_sales
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.iscm.entityTidb.SrmDtpSales">
    insert into srm_dtp_sales (id, business_day_date, company_code, 
      company_name, lifnr, lifnr_name, 
      retail_store_id, retail_store_name, temid, 
      temid_name, temid_spec, manu_id, 
      manu, batch_code, eff_date, 
      product_date, unit, bar_code, 
      retail_quantity, retail_amount, hospital_id, 
      hospital_name, order_no, is_sap, 
      prescription_number, prescription_date, 
      doctor_name, department, disease_description, 
      approval_number)
    values (#{id,jdbcType=BIGINT}, #{businessDayDate,jdbcType=DATE}, #{companyCode,jdbcType=VARCHAR}, 
      #{companyName,jdbcType=VARCHAR}, #{lifnr,jdbcType=VARCHAR}, #{lifnrName,jdbcType=VARCHAR}, 
      #{retailStoreId,jdbcType=VARCHAR}, #{retailStoreName,jdbcType=VARCHAR}, #{temid,jdbcType=VARCHAR}, 
      #{temidName,jdbcType=VARCHAR}, #{temidSpec,jdbcType=VARCHAR}, #{manuId,jdbcType=VARCHAR}, 
      #{manu,jdbcType=VARCHAR}, #{batchCode,jdbcType=VARCHAR}, #{effDate,jdbcType=DATE}, 
      #{productDate,jdbcType=DATE}, #{unit,jdbcType=VARCHAR}, #{barCode,jdbcType=VARCHAR}, 
      #{retailQuantity,jdbcType=DECIMAL}, #{retailAmount,jdbcType=DECIMAL}, #{hospitalId,jdbcType=VARCHAR}, 
      #{hospitalName,jdbcType=VARCHAR}, #{orderNo,jdbcType=VARCHAR}, #{isSap,jdbcType=TINYINT}, 
      #{prescriptionNumber,jdbcType=VARCHAR}, #{prescriptionDate,jdbcType=TIMESTAMP}, 
      #{doctorName,jdbcType=VARCHAR}, #{department,jdbcType=VARCHAR}, #{diseaseDescription,jdbcType=VARCHAR}, 
      #{approvalNumber,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.iscm.entityTidb.SrmDtpSales">
    insert into srm_dtp_sales
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="businessDayDate != null">
        business_day_date,
      </if>
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="companyName != null">
        company_name,
      </if>
      <if test="lifnr != null">
        lifnr,
      </if>
      <if test="lifnrName != null">
        lifnr_name,
      </if>
      <if test="retailStoreId != null">
        retail_store_id,
      </if>
      <if test="retailStoreName != null">
        retail_store_name,
      </if>
      <if test="temid != null">
        temid,
      </if>
      <if test="temidName != null">
        temid_name,
      </if>
      <if test="temidSpec != null">
        temid_spec,
      </if>
      <if test="manuId != null">
        manu_id,
      </if>
      <if test="manu != null">
        manu,
      </if>
      <if test="batchCode != null">
        batch_code,
      </if>
      <if test="effDate != null">
        eff_date,
      </if>
      <if test="productDate != null">
        product_date,
      </if>
      <if test="unit != null">
        unit,
      </if>
      <if test="barCode != null">
        bar_code,
      </if>
      <if test="retailQuantity != null">
        retail_quantity,
      </if>
      <if test="retailAmount != null">
        retail_amount,
      </if>
      <if test="hospitalId != null">
        hospital_id,
      </if>
      <if test="hospitalName != null">
        hospital_name,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="isSap != null">
        is_sap,
      </if>
      <if test="prescriptionNumber != null">
        prescription_number,
      </if>
      <if test="prescriptionDate != null">
        prescription_date,
      </if>
      <if test="doctorName != null">
        doctor_name,
      </if>
      <if test="department != null">
        department,
      </if>
      <if test="diseaseDescription != null">
        disease_description,
      </if>
      <if test="approvalNumber != null">
        approval_number,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="businessDayDate != null">
        #{businessDayDate,jdbcType=DATE},
      </if>
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="lifnr != null">
        #{lifnr,jdbcType=VARCHAR},
      </if>
      <if test="lifnrName != null">
        #{lifnrName,jdbcType=VARCHAR},
      </if>
      <if test="retailStoreId != null">
        #{retailStoreId,jdbcType=VARCHAR},
      </if>
      <if test="retailStoreName != null">
        #{retailStoreName,jdbcType=VARCHAR},
      </if>
      <if test="temid != null">
        #{temid,jdbcType=VARCHAR},
      </if>
      <if test="temidName != null">
        #{temidName,jdbcType=VARCHAR},
      </if>
      <if test="temidSpec != null">
        #{temidSpec,jdbcType=VARCHAR},
      </if>
      <if test="manuId != null">
        #{manuId,jdbcType=VARCHAR},
      </if>
      <if test="manu != null">
        #{manu,jdbcType=VARCHAR},
      </if>
      <if test="batchCode != null">
        #{batchCode,jdbcType=VARCHAR},
      </if>
      <if test="effDate != null">
        #{effDate,jdbcType=DATE},
      </if>
      <if test="productDate != null">
        #{productDate,jdbcType=DATE},
      </if>
      <if test="unit != null">
        #{unit,jdbcType=VARCHAR},
      </if>
      <if test="barCode != null">
        #{barCode,jdbcType=VARCHAR},
      </if>
      <if test="retailQuantity != null">
        #{retailQuantity,jdbcType=DECIMAL},
      </if>
      <if test="retailAmount != null">
        #{retailAmount,jdbcType=DECIMAL},
      </if>
      <if test="hospitalId != null">
        #{hospitalId,jdbcType=VARCHAR},
      </if>
      <if test="hospitalName != null">
        #{hospitalName,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="isSap != null">
        #{isSap,jdbcType=TINYINT},
      </if>
      <if test="prescriptionNumber != null">
        #{prescriptionNumber,jdbcType=VARCHAR},
      </if>
      <if test="prescriptionDate != null">
        #{prescriptionDate,jdbcType=TIMESTAMP},
      </if>
      <if test="doctorName != null">
        #{doctorName,jdbcType=VARCHAR},
      </if>
      <if test="department != null">
        #{department,jdbcType=VARCHAR},
      </if>
      <if test="diseaseDescription != null">
        #{diseaseDescription,jdbcType=VARCHAR},
      </if>
      <if test="approvalNumber != null">
        #{approvalNumber,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.iscm.entityTidb.SrmDtpSalesExample" resultType="java.lang.Long">
    select count(*) from srm_dtp_sales
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update srm_dtp_sales
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.businessDayDate != null">
        business_day_date = #{record.businessDayDate,jdbcType=DATE},
      </if>
      <if test="record.companyCode != null">
        company_code = #{record.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.companyName != null">
        company_name = #{record.companyName,jdbcType=VARCHAR},
      </if>
      <if test="record.lifnr != null">
        lifnr = #{record.lifnr,jdbcType=VARCHAR},
      </if>
      <if test="record.lifnrName != null">
        lifnr_name = #{record.lifnrName,jdbcType=VARCHAR},
      </if>
      <if test="record.retailStoreId != null">
        retail_store_id = #{record.retailStoreId,jdbcType=VARCHAR},
      </if>
      <if test="record.retailStoreName != null">
        retail_store_name = #{record.retailStoreName,jdbcType=VARCHAR},
      </if>
      <if test="record.temid != null">
        temid = #{record.temid,jdbcType=VARCHAR},
      </if>
      <if test="record.temidName != null">
        temid_name = #{record.temidName,jdbcType=VARCHAR},
      </if>
      <if test="record.temidSpec != null">
        temid_spec = #{record.temidSpec,jdbcType=VARCHAR},
      </if>
      <if test="record.manuId != null">
        manu_id = #{record.manuId,jdbcType=VARCHAR},
      </if>
      <if test="record.manu != null">
        manu = #{record.manu,jdbcType=VARCHAR},
      </if>
      <if test="record.batchCode != null">
        batch_code = #{record.batchCode,jdbcType=VARCHAR},
      </if>
      <if test="record.effDate != null">
        eff_date = #{record.effDate,jdbcType=DATE},
      </if>
      <if test="record.productDate != null">
        product_date = #{record.productDate,jdbcType=DATE},
      </if>
      <if test="record.unit != null">
        unit = #{record.unit,jdbcType=VARCHAR},
      </if>
      <if test="record.barCode != null">
        bar_code = #{record.barCode,jdbcType=VARCHAR},
      </if>
      <if test="record.retailQuantity != null">
        retail_quantity = #{record.retailQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.retailAmount != null">
        retail_amount = #{record.retailAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.hospitalId != null">
        hospital_id = #{record.hospitalId,jdbcType=VARCHAR},
      </if>
      <if test="record.hospitalName != null">
        hospital_name = #{record.hospitalName,jdbcType=VARCHAR},
      </if>
      <if test="record.orderNo != null">
        order_no = #{record.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.isSap != null">
        is_sap = #{record.isSap,jdbcType=TINYINT},
      </if>
      <if test="record.prescriptionNumber != null">
        prescription_number = #{record.prescriptionNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.prescriptionDate != null">
        prescription_date = #{record.prescriptionDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.doctorName != null">
        doctor_name = #{record.doctorName,jdbcType=VARCHAR},
      </if>
      <if test="record.department != null">
        department = #{record.department,jdbcType=VARCHAR},
      </if>
      <if test="record.diseaseDescription != null">
        disease_description = #{record.diseaseDescription,jdbcType=VARCHAR},
      </if>
      <if test="record.approvalNumber != null">
        approval_number = #{record.approvalNumber,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update srm_dtp_sales
    set id = #{record.id,jdbcType=BIGINT},
      business_day_date = #{record.businessDayDate,jdbcType=DATE},
      company_code = #{record.companyCode,jdbcType=VARCHAR},
      company_name = #{record.companyName,jdbcType=VARCHAR},
      lifnr = #{record.lifnr,jdbcType=VARCHAR},
      lifnr_name = #{record.lifnrName,jdbcType=VARCHAR},
      retail_store_id = #{record.retailStoreId,jdbcType=VARCHAR},
      retail_store_name = #{record.retailStoreName,jdbcType=VARCHAR},
      temid = #{record.temid,jdbcType=VARCHAR},
      temid_name = #{record.temidName,jdbcType=VARCHAR},
      temid_spec = #{record.temidSpec,jdbcType=VARCHAR},
      manu_id = #{record.manuId,jdbcType=VARCHAR},
      manu = #{record.manu,jdbcType=VARCHAR},
      batch_code = #{record.batchCode,jdbcType=VARCHAR},
      eff_date = #{record.effDate,jdbcType=DATE},
      product_date = #{record.productDate,jdbcType=DATE},
      unit = #{record.unit,jdbcType=VARCHAR},
      bar_code = #{record.barCode,jdbcType=VARCHAR},
      retail_quantity = #{record.retailQuantity,jdbcType=DECIMAL},
      retail_amount = #{record.retailAmount,jdbcType=DECIMAL},
      hospital_id = #{record.hospitalId,jdbcType=VARCHAR},
      hospital_name = #{record.hospitalName,jdbcType=VARCHAR},
      order_no = #{record.orderNo,jdbcType=VARCHAR},
      is_sap = #{record.isSap,jdbcType=TINYINT},
      prescription_number = #{record.prescriptionNumber,jdbcType=VARCHAR},
      prescription_date = #{record.prescriptionDate,jdbcType=TIMESTAMP},
      doctor_name = #{record.doctorName,jdbcType=VARCHAR},
      department = #{record.department,jdbcType=VARCHAR},
      disease_description = #{record.diseaseDescription,jdbcType=VARCHAR},
      approval_number = #{record.approvalNumber,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.iscm.entityTidb.SrmDtpSales">
    update srm_dtp_sales
    <set>
      <if test="businessDayDate != null">
        business_day_date = #{businessDayDate,jdbcType=DATE},
      </if>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="lifnr != null">
        lifnr = #{lifnr,jdbcType=VARCHAR},
      </if>
      <if test="lifnrName != null">
        lifnr_name = #{lifnrName,jdbcType=VARCHAR},
      </if>
      <if test="retailStoreId != null">
        retail_store_id = #{retailStoreId,jdbcType=VARCHAR},
      </if>
      <if test="retailStoreName != null">
        retail_store_name = #{retailStoreName,jdbcType=VARCHAR},
      </if>
      <if test="temid != null">
        temid = #{temid,jdbcType=VARCHAR},
      </if>
      <if test="temidName != null">
        temid_name = #{temidName,jdbcType=VARCHAR},
      </if>
      <if test="temidSpec != null">
        temid_spec = #{temidSpec,jdbcType=VARCHAR},
      </if>
      <if test="manuId != null">
        manu_id = #{manuId,jdbcType=VARCHAR},
      </if>
      <if test="manu != null">
        manu = #{manu,jdbcType=VARCHAR},
      </if>
      <if test="batchCode != null">
        batch_code = #{batchCode,jdbcType=VARCHAR},
      </if>
      <if test="effDate != null">
        eff_date = #{effDate,jdbcType=DATE},
      </if>
      <if test="productDate != null">
        product_date = #{productDate,jdbcType=DATE},
      </if>
      <if test="unit != null">
        unit = #{unit,jdbcType=VARCHAR},
      </if>
      <if test="barCode != null">
        bar_code = #{barCode,jdbcType=VARCHAR},
      </if>
      <if test="retailQuantity != null">
        retail_quantity = #{retailQuantity,jdbcType=DECIMAL},
      </if>
      <if test="retailAmount != null">
        retail_amount = #{retailAmount,jdbcType=DECIMAL},
      </if>
      <if test="hospitalId != null">
        hospital_id = #{hospitalId,jdbcType=VARCHAR},
      </if>
      <if test="hospitalName != null">
        hospital_name = #{hospitalName,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="isSap != null">
        is_sap = #{isSap,jdbcType=TINYINT},
      </if>
      <if test="prescriptionNumber != null">
        prescription_number = #{prescriptionNumber,jdbcType=VARCHAR},
      </if>
      <if test="prescriptionDate != null">
        prescription_date = #{prescriptionDate,jdbcType=TIMESTAMP},
      </if>
      <if test="doctorName != null">
        doctor_name = #{doctorName,jdbcType=VARCHAR},
      </if>
      <if test="department != null">
        department = #{department,jdbcType=VARCHAR},
      </if>
      <if test="diseaseDescription != null">
        disease_description = #{diseaseDescription,jdbcType=VARCHAR},
      </if>
      <if test="approvalNumber != null">
        approval_number = #{approvalNumber,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.iscm.entityTidb.SrmDtpSales">
    update srm_dtp_sales
    set business_day_date = #{businessDayDate,jdbcType=DATE},
      company_code = #{companyCode,jdbcType=VARCHAR},
      company_name = #{companyName,jdbcType=VARCHAR},
      lifnr = #{lifnr,jdbcType=VARCHAR},
      lifnr_name = #{lifnrName,jdbcType=VARCHAR},
      retail_store_id = #{retailStoreId,jdbcType=VARCHAR},
      retail_store_name = #{retailStoreName,jdbcType=VARCHAR},
      temid = #{temid,jdbcType=VARCHAR},
      temid_name = #{temidName,jdbcType=VARCHAR},
      temid_spec = #{temidSpec,jdbcType=VARCHAR},
      manu_id = #{manuId,jdbcType=VARCHAR},
      manu = #{manu,jdbcType=VARCHAR},
      batch_code = #{batchCode,jdbcType=VARCHAR},
      eff_date = #{effDate,jdbcType=DATE},
      product_date = #{productDate,jdbcType=DATE},
      unit = #{unit,jdbcType=VARCHAR},
      bar_code = #{barCode,jdbcType=VARCHAR},
      retail_quantity = #{retailQuantity,jdbcType=DECIMAL},
      retail_amount = #{retailAmount,jdbcType=DECIMAL},
      hospital_id = #{hospitalId,jdbcType=VARCHAR},
      hospital_name = #{hospitalName,jdbcType=VARCHAR},
      order_no = #{orderNo,jdbcType=VARCHAR},
      is_sap = #{isSap,jdbcType=TINYINT},
      prescription_number = #{prescriptionNumber,jdbcType=VARCHAR},
      prescription_date = #{prescriptionDate,jdbcType=TIMESTAMP},
      doctor_name = #{doctorName,jdbcType=VARCHAR},
      department = #{department,jdbcType=VARCHAR},
      disease_description = #{diseaseDescription,jdbcType=VARCHAR},
      approval_number = #{approvalNumber,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>