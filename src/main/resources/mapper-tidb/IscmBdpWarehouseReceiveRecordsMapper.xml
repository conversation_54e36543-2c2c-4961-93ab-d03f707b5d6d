<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapperTidb.IscmBdpWarehouseReceiveRecordsMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entityTidb.IscmBdpWarehouseReceiveRecords">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="company_bdp_code" jdbcType="VARCHAR" property="companyBdpCode" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="store_bdp_code" jdbcType="VARCHAR" property="storeBdpCode" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="goods_hd_no" jdbcType="VARCHAR" property="goodsHdNo" />
    <result column="distr_no" jdbcType="VARCHAR" property="distrNo" />
    <result column="distr_line_no" jdbcType="VARCHAR" property="distrLineNo" />
    <result column="distr_date" jdbcType="TIMESTAMP" property="distrDate" />
    <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
    <result column="validity_date" jdbcType="TIMESTAMP" property="validityDate" />
    <result column="sap_batch_code" jdbcType="VARCHAR" property="sapBatchCode" />
    <result column="hd_batch_code" jdbcType="VARCHAR" property="hdBatchCode" />
    <result column="distr_quantity" jdbcType="DECIMAL" property="distrQuantity" />
    <result column="should_return_quantity" jdbcType="DECIMAL" property="shouldReturnQuantity" />
    <result column="return_quantity" jdbcType="DECIMAL" property="returnQuantity" />
    <result column="warehouse_code" jdbcType="VARCHAR" property="warehouseCode" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, company_code, company_bdp_code, store_code, store_bdp_code, goods_no, goods_hd_no, 
    distr_no, distr_line_no, distr_date, batch_no, validity_date, sap_batch_code, hd_batch_code, 
    distr_quantity, should_return_quantity, return_quantity, warehouse_code
  </sql>
  <select id="selectByExample" parameterType="com.cowell.iscm.entityTidb.IscmBdpWarehouseReceiveRecordsExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from iscm_bdp_warehouse_receive_records
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from iscm_bdp_warehouse_receive_records
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from iscm_bdp_warehouse_receive_records
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.iscm.entityTidb.IscmBdpWarehouseReceiveRecordsExample">
    delete from iscm_bdp_warehouse_receive_records
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entityTidb.IscmBdpWarehouseReceiveRecords" useGeneratedKeys="true">
    insert into iscm_bdp_warehouse_receive_records (company_code, company_bdp_code, store_code, 
      store_bdp_code, goods_no, goods_hd_no, 
      distr_no, distr_line_no, distr_date, 
      batch_no, validity_date, sap_batch_code, 
      hd_batch_code, distr_quantity, should_return_quantity, 
      return_quantity, warehouse_code)
    values (#{companyCode,jdbcType=VARCHAR}, #{companyBdpCode,jdbcType=VARCHAR}, #{storeCode,jdbcType=VARCHAR}, 
      #{storeBdpCode,jdbcType=VARCHAR}, #{goodsNo,jdbcType=VARCHAR}, #{goodsHdNo,jdbcType=VARCHAR}, 
      #{distrNo,jdbcType=VARCHAR}, #{distrLineNo,jdbcType=VARCHAR}, #{distrDate,jdbcType=TIMESTAMP}, 
      #{batchNo,jdbcType=VARCHAR}, #{validityDate,jdbcType=TIMESTAMP}, #{sapBatchCode,jdbcType=VARCHAR}, 
      #{hdBatchCode,jdbcType=VARCHAR}, #{distrQuantity,jdbcType=DECIMAL}, #{shouldReturnQuantity,jdbcType=DECIMAL}, 
      #{returnQuantity,jdbcType=DECIMAL}, #{warehouseCode,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entityTidb.IscmBdpWarehouseReceiveRecords" useGeneratedKeys="true">
    insert into iscm_bdp_warehouse_receive_records
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="companyBdpCode != null">
        company_bdp_code,
      </if>
      <if test="storeCode != null">
        store_code,
      </if>
      <if test="storeBdpCode != null">
        store_bdp_code,
      </if>
      <if test="goodsNo != null">
        goods_no,
      </if>
      <if test="goodsHdNo != null">
        goods_hd_no,
      </if>
      <if test="distrNo != null">
        distr_no,
      </if>
      <if test="distrLineNo != null">
        distr_line_no,
      </if>
      <if test="distrDate != null">
        distr_date,
      </if>
      <if test="batchNo != null">
        batch_no,
      </if>
      <if test="validityDate != null">
        validity_date,
      </if>
      <if test="sapBatchCode != null">
        sap_batch_code,
      </if>
      <if test="hdBatchCode != null">
        hd_batch_code,
      </if>
      <if test="distrQuantity != null">
        distr_quantity,
      </if>
      <if test="shouldReturnQuantity != null">
        should_return_quantity,
      </if>
      <if test="returnQuantity != null">
        return_quantity,
      </if>
      <if test="warehouseCode != null">
        warehouse_code,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyBdpCode != null">
        #{companyBdpCode,jdbcType=VARCHAR},
      </if>
      <if test="storeCode != null">
        #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="storeBdpCode != null">
        #{storeBdpCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsNo != null">
        #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="goodsHdNo != null">
        #{goodsHdNo,jdbcType=VARCHAR},
      </if>
      <if test="distrNo != null">
        #{distrNo,jdbcType=VARCHAR},
      </if>
      <if test="distrLineNo != null">
        #{distrLineNo,jdbcType=VARCHAR},
      </if>
      <if test="distrDate != null">
        #{distrDate,jdbcType=TIMESTAMP},
      </if>
      <if test="batchNo != null">
        #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="validityDate != null">
        #{validityDate,jdbcType=TIMESTAMP},
      </if>
      <if test="sapBatchCode != null">
        #{sapBatchCode,jdbcType=VARCHAR},
      </if>
      <if test="hdBatchCode != null">
        #{hdBatchCode,jdbcType=VARCHAR},
      </if>
      <if test="distrQuantity != null">
        #{distrQuantity,jdbcType=DECIMAL},
      </if>
      <if test="shouldReturnQuantity != null">
        #{shouldReturnQuantity,jdbcType=DECIMAL},
      </if>
      <if test="returnQuantity != null">
        #{returnQuantity,jdbcType=DECIMAL},
      </if>
      <if test="warehouseCode != null">
        #{warehouseCode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.iscm.entityTidb.IscmBdpWarehouseReceiveRecordsExample" resultType="java.lang.Long">
    select count(*) from iscm_bdp_warehouse_receive_records
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update iscm_bdp_warehouse_receive_records
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.companyCode != null">
        company_code = #{record.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.companyBdpCode != null">
        company_bdp_code = #{record.companyBdpCode,jdbcType=VARCHAR},
      </if>
      <if test="record.storeCode != null">
        store_code = #{record.storeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.storeBdpCode != null">
        store_bdp_code = #{record.storeBdpCode,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsNo != null">
        goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsHdNo != null">
        goods_hd_no = #{record.goodsHdNo,jdbcType=VARCHAR},
      </if>
      <if test="record.distrNo != null">
        distr_no = #{record.distrNo,jdbcType=VARCHAR},
      </if>
      <if test="record.distrLineNo != null">
        distr_line_no = #{record.distrLineNo,jdbcType=VARCHAR},
      </if>
      <if test="record.distrDate != null">
        distr_date = #{record.distrDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.batchNo != null">
        batch_no = #{record.batchNo,jdbcType=VARCHAR},
      </if>
      <if test="record.validityDate != null">
        validity_date = #{record.validityDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.sapBatchCode != null">
        sap_batch_code = #{record.sapBatchCode,jdbcType=VARCHAR},
      </if>
      <if test="record.hdBatchCode != null">
        hd_batch_code = #{record.hdBatchCode,jdbcType=VARCHAR},
      </if>
      <if test="record.distrQuantity != null">
        distr_quantity = #{record.distrQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.shouldReturnQuantity != null">
        should_return_quantity = #{record.shouldReturnQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.returnQuantity != null">
        return_quantity = #{record.returnQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.warehouseCode != null">
        warehouse_code = #{record.warehouseCode,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update iscm_bdp_warehouse_receive_records
    set id = #{record.id,jdbcType=BIGINT},
      company_code = #{record.companyCode,jdbcType=VARCHAR},
      company_bdp_code = #{record.companyBdpCode,jdbcType=VARCHAR},
      store_code = #{record.storeCode,jdbcType=VARCHAR},
      store_bdp_code = #{record.storeBdpCode,jdbcType=VARCHAR},
      goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      goods_hd_no = #{record.goodsHdNo,jdbcType=VARCHAR},
      distr_no = #{record.distrNo,jdbcType=VARCHAR},
      distr_line_no = #{record.distrLineNo,jdbcType=VARCHAR},
      distr_date = #{record.distrDate,jdbcType=TIMESTAMP},
      batch_no = #{record.batchNo,jdbcType=VARCHAR},
      validity_date = #{record.validityDate,jdbcType=TIMESTAMP},
      sap_batch_code = #{record.sapBatchCode,jdbcType=VARCHAR},
      hd_batch_code = #{record.hdBatchCode,jdbcType=VARCHAR},
      distr_quantity = #{record.distrQuantity,jdbcType=DECIMAL},
      should_return_quantity = #{record.shouldReturnQuantity,jdbcType=DECIMAL},
      return_quantity = #{record.returnQuantity,jdbcType=DECIMAL},
      warehouse_code = #{record.warehouseCode,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.iscm.entityTidb.IscmBdpWarehouseReceiveRecords">
    update iscm_bdp_warehouse_receive_records
    <set>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyBdpCode != null">
        company_bdp_code = #{companyBdpCode,jdbcType=VARCHAR},
      </if>
      <if test="storeCode != null">
        store_code = #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="storeBdpCode != null">
        store_bdp_code = #{storeBdpCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsNo != null">
        goods_no = #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="goodsHdNo != null">
        goods_hd_no = #{goodsHdNo,jdbcType=VARCHAR},
      </if>
      <if test="distrNo != null">
        distr_no = #{distrNo,jdbcType=VARCHAR},
      </if>
      <if test="distrLineNo != null">
        distr_line_no = #{distrLineNo,jdbcType=VARCHAR},
      </if>
      <if test="distrDate != null">
        distr_date = #{distrDate,jdbcType=TIMESTAMP},
      </if>
      <if test="batchNo != null">
        batch_no = #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="validityDate != null">
        validity_date = #{validityDate,jdbcType=TIMESTAMP},
      </if>
      <if test="sapBatchCode != null">
        sap_batch_code = #{sapBatchCode,jdbcType=VARCHAR},
      </if>
      <if test="hdBatchCode != null">
        hd_batch_code = #{hdBatchCode,jdbcType=VARCHAR},
      </if>
      <if test="distrQuantity != null">
        distr_quantity = #{distrQuantity,jdbcType=DECIMAL},
      </if>
      <if test="shouldReturnQuantity != null">
        should_return_quantity = #{shouldReturnQuantity,jdbcType=DECIMAL},
      </if>
      <if test="returnQuantity != null">
        return_quantity = #{returnQuantity,jdbcType=DECIMAL},
      </if>
      <if test="warehouseCode != null">
        warehouse_code = #{warehouseCode,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.iscm.entityTidb.IscmBdpWarehouseReceiveRecords">
    update iscm_bdp_warehouse_receive_records
    set company_code = #{companyCode,jdbcType=VARCHAR},
      company_bdp_code = #{companyBdpCode,jdbcType=VARCHAR},
      store_code = #{storeCode,jdbcType=VARCHAR},
      store_bdp_code = #{storeBdpCode,jdbcType=VARCHAR},
      goods_no = #{goodsNo,jdbcType=VARCHAR},
      goods_hd_no = #{goodsHdNo,jdbcType=VARCHAR},
      distr_no = #{distrNo,jdbcType=VARCHAR},
      distr_line_no = #{distrLineNo,jdbcType=VARCHAR},
      distr_date = #{distrDate,jdbcType=TIMESTAMP},
      batch_no = #{batchNo,jdbcType=VARCHAR},
      validity_date = #{validityDate,jdbcType=TIMESTAMP},
      sap_batch_code = #{sapBatchCode,jdbcType=VARCHAR},
      hd_batch_code = #{hdBatchCode,jdbcType=VARCHAR},
      distr_quantity = #{distrQuantity,jdbcType=DECIMAL},
      should_return_quantity = #{shouldReturnQuantity,jdbcType=DECIMAL},
      return_quantity = #{returnQuantity,jdbcType=DECIMAL},
      warehouse_code = #{warehouseCode,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>