<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapperTidb.BdpSuggestBestGoodsMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entityTidb.BdpSuggestBestGoods">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="suggest_date" jdbcType="DATE" property="suggestDate" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="push_hd_status" jdbcType="TINYINT" property="pushHdStatus" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="specifications" jdbcType="VARCHAR" property="specifications" />
    <result column="dosage_form" jdbcType="VARCHAR" property="dosageForm" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="component" jdbcType="VARCHAR" property="component" />
    <result column="thirty_sales_quantity" jdbcType="DECIMAL" property="thirtySalesQuantity" />
    <result column="refer_retail_price" jdbcType="DECIMAL" property="referRetailPrice" />
    <result column="refer_gross_profit" jdbcType="DECIMAL" property="referGrossProfit" />
    <result column="sales_rank" jdbcType="INTEGER" property="salesRank" />
    <result column="push_level" jdbcType="VARCHAR" property="pushLevel" />
    <result column="medicine_people" jdbcType="VARCHAR" property="medicinePeople" />
    <result column="otc_able" jdbcType="TINYINT" property="otcAble" />
    <result column="rx_able" jdbcType="TINYINT" property="rxAble" />
    <result column="yb_able" jdbcType="TINYINT" property="ybAble" />
    <result column="sensitive_able" jdbcType="TINYINT" property="sensitiveAble" />
    <result column="category_id" jdbcType="BIGINT" property="categoryId" />
    <result column="category" jdbcType="VARCHAR" property="category" />
    <result column="promotion_tag" jdbcType="VARCHAR" property="promotionTag" />
    <result column="promotion_name" jdbcType="VARCHAR" property="promotionName" />
    <result column="promotion_way" jdbcType="VARCHAR" property="promotionWay" />
    <result column="threshold_info" jdbcType="VARCHAR" property="thresholdInfo" />
    <result column="fav_info" jdbcType="VARCHAR" property="favInfo" />
    <result column="recommend_reason" jdbcType="VARCHAR" property="recommendReason" />
    <result column="composite_new" jdbcType="TINYINT" property="compositeNew" />
    <result column="indications" jdbcType="VARCHAR" property="indications" />
    <result column="picture_url" jdbcType="VARCHAR" property="pictureUrl" />
    <result column="suggest_type" jdbcType="TINYINT" property="suggestType" />
    <result column="deal_suggest" jdbcType="TINYINT" property="dealSuggest" />
    <result column="complement_qty" jdbcType="INTEGER" property="complementQty" />
    <result column="pop_win" jdbcType="TINYINT" property="popWin" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, suggest_date, company_code, store_code, push_hd_status, goods_no, goods_name, 
    specifications, dosage_form, manufacturer, component, thirty_sales_quantity, refer_retail_price, 
    refer_gross_profit, sales_rank, push_level, medicine_people, otc_able, rx_able, yb_able, 
    sensitive_able, category_id, category, promotion_tag, promotion_name, promotion_way, 
    threshold_info, fav_info, recommend_reason, composite_new, indications, picture_url, 
    suggest_type, deal_suggest, complement_qty, pop_win, `status`, gmt_create, gmt_update, 
    extend, version, created_by, created_name, updated_by, updated_name
  </sql>
  <select id="selectByExample" parameterType="com.cowell.iscm.entityTidb.BdpSuggestBestGoodsExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from bdp_suggest_best_goods
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from bdp_suggest_best_goods
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from bdp_suggest_best_goods
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.iscm.entityTidb.BdpSuggestBestGoodsExample">
    delete from bdp_suggest_best_goods
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entityTidb.BdpSuggestBestGoods" useGeneratedKeys="true">
    insert into bdp_suggest_best_goods (suggest_date, company_code, store_code, 
      push_hd_status, goods_no, goods_name, 
      specifications, dosage_form, manufacturer, 
      component, thirty_sales_quantity, refer_retail_price, 
      refer_gross_profit, sales_rank, push_level, 
      medicine_people, otc_able, rx_able, 
      yb_able, sensitive_able, category_id, 
      category, promotion_tag, promotion_name, 
      promotion_way, threshold_info, fav_info, 
      recommend_reason, composite_new, indications, 
      picture_url, suggest_type, deal_suggest, 
      complement_qty, pop_win, `status`, 
      gmt_create, gmt_update, extend, 
      version, created_by, created_name, 
      updated_by, updated_name)
    values (#{suggestDate,jdbcType=DATE}, #{companyCode,jdbcType=VARCHAR}, #{storeCode,jdbcType=VARCHAR}, 
      #{pushHdStatus,jdbcType=TINYINT}, #{goodsNo,jdbcType=VARCHAR}, #{goodsName,jdbcType=VARCHAR}, 
      #{specifications,jdbcType=VARCHAR}, #{dosageForm,jdbcType=VARCHAR}, #{manufacturer,jdbcType=VARCHAR}, 
      #{component,jdbcType=VARCHAR}, #{thirtySalesQuantity,jdbcType=DECIMAL}, #{referRetailPrice,jdbcType=DECIMAL}, 
      #{referGrossProfit,jdbcType=DECIMAL}, #{salesRank,jdbcType=INTEGER}, #{pushLevel,jdbcType=VARCHAR}, 
      #{medicinePeople,jdbcType=VARCHAR}, #{otcAble,jdbcType=TINYINT}, #{rxAble,jdbcType=TINYINT}, 
      #{ybAble,jdbcType=TINYINT}, #{sensitiveAble,jdbcType=TINYINT}, #{categoryId,jdbcType=BIGINT}, 
      #{category,jdbcType=VARCHAR}, #{promotionTag,jdbcType=VARCHAR}, #{promotionName,jdbcType=VARCHAR}, 
      #{promotionWay,jdbcType=VARCHAR}, #{thresholdInfo,jdbcType=VARCHAR}, #{favInfo,jdbcType=VARCHAR}, 
      #{recommendReason,jdbcType=VARCHAR}, #{compositeNew,jdbcType=TINYINT}, #{indications,jdbcType=VARCHAR}, 
      #{pictureUrl,jdbcType=VARCHAR}, #{suggestType,jdbcType=TINYINT}, #{dealSuggest,jdbcType=TINYINT}, 
      #{complementQty,jdbcType=INTEGER}, #{popWin,jdbcType=TINYINT}, #{status,jdbcType=TINYINT}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}, #{extend,jdbcType=VARCHAR}, 
      #{version,jdbcType=INTEGER}, #{createdBy,jdbcType=BIGINT}, #{createdName,jdbcType=VARCHAR}, 
      #{updatedBy,jdbcType=BIGINT}, #{updatedName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entityTidb.BdpSuggestBestGoods" useGeneratedKeys="true">
    insert into bdp_suggest_best_goods
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="suggestDate != null">
        suggest_date,
      </if>
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="storeCode != null">
        store_code,
      </if>
      <if test="pushHdStatus != null">
        push_hd_status,
      </if>
      <if test="goodsNo != null">
        goods_no,
      </if>
      <if test="goodsName != null">
        goods_name,
      </if>
      <if test="specifications != null">
        specifications,
      </if>
      <if test="dosageForm != null">
        dosage_form,
      </if>
      <if test="manufacturer != null">
        manufacturer,
      </if>
      <if test="component != null">
        component,
      </if>
      <if test="thirtySalesQuantity != null">
        thirty_sales_quantity,
      </if>
      <if test="referRetailPrice != null">
        refer_retail_price,
      </if>
      <if test="referGrossProfit != null">
        refer_gross_profit,
      </if>
      <if test="salesRank != null">
        sales_rank,
      </if>
      <if test="pushLevel != null">
        push_level,
      </if>
      <if test="medicinePeople != null">
        medicine_people,
      </if>
      <if test="otcAble != null">
        otc_able,
      </if>
      <if test="rxAble != null">
        rx_able,
      </if>
      <if test="ybAble != null">
        yb_able,
      </if>
      <if test="sensitiveAble != null">
        sensitive_able,
      </if>
      <if test="categoryId != null">
        category_id,
      </if>
      <if test="category != null">
        category,
      </if>
      <if test="promotionTag != null">
        promotion_tag,
      </if>
      <if test="promotionName != null">
        promotion_name,
      </if>
      <if test="promotionWay != null">
        promotion_way,
      </if>
      <if test="thresholdInfo != null">
        threshold_info,
      </if>
      <if test="favInfo != null">
        fav_info,
      </if>
      <if test="recommendReason != null">
        recommend_reason,
      </if>
      <if test="compositeNew != null">
        composite_new,
      </if>
      <if test="indications != null">
        indications,
      </if>
      <if test="pictureUrl != null">
        picture_url,
      </if>
      <if test="suggestType != null">
        suggest_type,
      </if>
      <if test="dealSuggest != null">
        deal_suggest,
      </if>
      <if test="complementQty != null">
        complement_qty,
      </if>
      <if test="popWin != null">
        pop_win,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedName != null">
        updated_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="suggestDate != null">
        #{suggestDate,jdbcType=DATE},
      </if>
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="storeCode != null">
        #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="pushHdStatus != null">
        #{pushHdStatus,jdbcType=TINYINT},
      </if>
      <if test="goodsNo != null">
        #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="specifications != null">
        #{specifications,jdbcType=VARCHAR},
      </if>
      <if test="dosageForm != null">
        #{dosageForm,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="component != null">
        #{component,jdbcType=VARCHAR},
      </if>
      <if test="thirtySalesQuantity != null">
        #{thirtySalesQuantity,jdbcType=DECIMAL},
      </if>
      <if test="referRetailPrice != null">
        #{referRetailPrice,jdbcType=DECIMAL},
      </if>
      <if test="referGrossProfit != null">
        #{referGrossProfit,jdbcType=DECIMAL},
      </if>
      <if test="salesRank != null">
        #{salesRank,jdbcType=INTEGER},
      </if>
      <if test="pushLevel != null">
        #{pushLevel,jdbcType=VARCHAR},
      </if>
      <if test="medicinePeople != null">
        #{medicinePeople,jdbcType=VARCHAR},
      </if>
      <if test="otcAble != null">
        #{otcAble,jdbcType=TINYINT},
      </if>
      <if test="rxAble != null">
        #{rxAble,jdbcType=TINYINT},
      </if>
      <if test="ybAble != null">
        #{ybAble,jdbcType=TINYINT},
      </if>
      <if test="sensitiveAble != null">
        #{sensitiveAble,jdbcType=TINYINT},
      </if>
      <if test="categoryId != null">
        #{categoryId,jdbcType=BIGINT},
      </if>
      <if test="category != null">
        #{category,jdbcType=VARCHAR},
      </if>
      <if test="promotionTag != null">
        #{promotionTag,jdbcType=VARCHAR},
      </if>
      <if test="promotionName != null">
        #{promotionName,jdbcType=VARCHAR},
      </if>
      <if test="promotionWay != null">
        #{promotionWay,jdbcType=VARCHAR},
      </if>
      <if test="thresholdInfo != null">
        #{thresholdInfo,jdbcType=VARCHAR},
      </if>
      <if test="favInfo != null">
        #{favInfo,jdbcType=VARCHAR},
      </if>
      <if test="recommendReason != null">
        #{recommendReason,jdbcType=VARCHAR},
      </if>
      <if test="compositeNew != null">
        #{compositeNew,jdbcType=TINYINT},
      </if>
      <if test="indications != null">
        #{indications,jdbcType=VARCHAR},
      </if>
      <if test="pictureUrl != null">
        #{pictureUrl,jdbcType=VARCHAR},
      </if>
      <if test="suggestType != null">
        #{suggestType,jdbcType=TINYINT},
      </if>
      <if test="dealSuggest != null">
        #{dealSuggest,jdbcType=TINYINT},
      </if>
      <if test="complementQty != null">
        #{complementQty,jdbcType=INTEGER},
      </if>
      <if test="popWin != null">
        #{popWin,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        #{updatedName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.iscm.entityTidb.BdpSuggestBestGoodsExample" resultType="java.lang.Long">
    select count(*) from bdp_suggest_best_goods
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update bdp_suggest_best_goods
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.suggestDate != null">
        suggest_date = #{record.suggestDate,jdbcType=DATE},
      </if>
      <if test="record.companyCode != null">
        company_code = #{record.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.storeCode != null">
        store_code = #{record.storeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.pushHdStatus != null">
        push_hd_status = #{record.pushHdStatus,jdbcType=TINYINT},
      </if>
      <if test="record.goodsNo != null">
        goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsName != null">
        goods_name = #{record.goodsName,jdbcType=VARCHAR},
      </if>
      <if test="record.specifications != null">
        specifications = #{record.specifications,jdbcType=VARCHAR},
      </if>
      <if test="record.dosageForm != null">
        dosage_form = #{record.dosageForm,jdbcType=VARCHAR},
      </if>
      <if test="record.manufacturer != null">
        manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="record.component != null">
        component = #{record.component,jdbcType=VARCHAR},
      </if>
      <if test="record.thirtySalesQuantity != null">
        thirty_sales_quantity = #{record.thirtySalesQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.referRetailPrice != null">
        refer_retail_price = #{record.referRetailPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.referGrossProfit != null">
        refer_gross_profit = #{record.referGrossProfit,jdbcType=DECIMAL},
      </if>
      <if test="record.salesRank != null">
        sales_rank = #{record.salesRank,jdbcType=INTEGER},
      </if>
      <if test="record.pushLevel != null">
        push_level = #{record.pushLevel,jdbcType=VARCHAR},
      </if>
      <if test="record.medicinePeople != null">
        medicine_people = #{record.medicinePeople,jdbcType=VARCHAR},
      </if>
      <if test="record.otcAble != null">
        otc_able = #{record.otcAble,jdbcType=TINYINT},
      </if>
      <if test="record.rxAble != null">
        rx_able = #{record.rxAble,jdbcType=TINYINT},
      </if>
      <if test="record.ybAble != null">
        yb_able = #{record.ybAble,jdbcType=TINYINT},
      </if>
      <if test="record.sensitiveAble != null">
        sensitive_able = #{record.sensitiveAble,jdbcType=TINYINT},
      </if>
      <if test="record.categoryId != null">
        category_id = #{record.categoryId,jdbcType=BIGINT},
      </if>
      <if test="record.category != null">
        category = #{record.category,jdbcType=VARCHAR},
      </if>
      <if test="record.promotionTag != null">
        promotion_tag = #{record.promotionTag,jdbcType=VARCHAR},
      </if>
      <if test="record.promotionName != null">
        promotion_name = #{record.promotionName,jdbcType=VARCHAR},
      </if>
      <if test="record.promotionWay != null">
        promotion_way = #{record.promotionWay,jdbcType=VARCHAR},
      </if>
      <if test="record.thresholdInfo != null">
        threshold_info = #{record.thresholdInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.favInfo != null">
        fav_info = #{record.favInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.recommendReason != null">
        recommend_reason = #{record.recommendReason,jdbcType=VARCHAR},
      </if>
      <if test="record.compositeNew != null">
        composite_new = #{record.compositeNew,jdbcType=TINYINT},
      </if>
      <if test="record.indications != null">
        indications = #{record.indications,jdbcType=VARCHAR},
      </if>
      <if test="record.pictureUrl != null">
        picture_url = #{record.pictureUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.suggestType != null">
        suggest_type = #{record.suggestType,jdbcType=TINYINT},
      </if>
      <if test="record.dealSuggest != null">
        deal_suggest = #{record.dealSuggest,jdbcType=TINYINT},
      </if>
      <if test="record.complementQty != null">
        complement_qty = #{record.complementQty,jdbcType=INTEGER},
      </if>
      <if test="record.popWin != null">
        pop_win = #{record.popWin,jdbcType=TINYINT},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedName != null">
        updated_name = #{record.updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update bdp_suggest_best_goods
    set id = #{record.id,jdbcType=BIGINT},
      suggest_date = #{record.suggestDate,jdbcType=DATE},
      company_code = #{record.companyCode,jdbcType=VARCHAR},
      store_code = #{record.storeCode,jdbcType=VARCHAR},
      push_hd_status = #{record.pushHdStatus,jdbcType=TINYINT},
      goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      goods_name = #{record.goodsName,jdbcType=VARCHAR},
      specifications = #{record.specifications,jdbcType=VARCHAR},
      dosage_form = #{record.dosageForm,jdbcType=VARCHAR},
      manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      component = #{record.component,jdbcType=VARCHAR},
      thirty_sales_quantity = #{record.thirtySalesQuantity,jdbcType=DECIMAL},
      refer_retail_price = #{record.referRetailPrice,jdbcType=DECIMAL},
      refer_gross_profit = #{record.referGrossProfit,jdbcType=DECIMAL},
      sales_rank = #{record.salesRank,jdbcType=INTEGER},
      push_level = #{record.pushLevel,jdbcType=VARCHAR},
      medicine_people = #{record.medicinePeople,jdbcType=VARCHAR},
      otc_able = #{record.otcAble,jdbcType=TINYINT},
      rx_able = #{record.rxAble,jdbcType=TINYINT},
      yb_able = #{record.ybAble,jdbcType=TINYINT},
      sensitive_able = #{record.sensitiveAble,jdbcType=TINYINT},
      category_id = #{record.categoryId,jdbcType=BIGINT},
      category = #{record.category,jdbcType=VARCHAR},
      promotion_tag = #{record.promotionTag,jdbcType=VARCHAR},
      promotion_name = #{record.promotionName,jdbcType=VARCHAR},
      promotion_way = #{record.promotionWay,jdbcType=VARCHAR},
      threshold_info = #{record.thresholdInfo,jdbcType=VARCHAR},
      fav_info = #{record.favInfo,jdbcType=VARCHAR},
      recommend_reason = #{record.recommendReason,jdbcType=VARCHAR},
      composite_new = #{record.compositeNew,jdbcType=TINYINT},
      indications = #{record.indications,jdbcType=VARCHAR},
      picture_url = #{record.pictureUrl,jdbcType=VARCHAR},
      suggest_type = #{record.suggestType,jdbcType=TINYINT},
      deal_suggest = #{record.dealSuggest,jdbcType=TINYINT},
      complement_qty = #{record.complementQty,jdbcType=INTEGER},
      pop_win = #{record.popWin,jdbcType=TINYINT},
      `status` = #{record.status,jdbcType=TINYINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_name = #{record.createdName,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_name = #{record.updatedName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.iscm.entityTidb.BdpSuggestBestGoods">
    update bdp_suggest_best_goods
    <set>
      <if test="suggestDate != null">
        suggest_date = #{suggestDate,jdbcType=DATE},
      </if>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="storeCode != null">
        store_code = #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="pushHdStatus != null">
        push_hd_status = #{pushHdStatus,jdbcType=TINYINT},
      </if>
      <if test="goodsNo != null">
        goods_no = #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        goods_name = #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="specifications != null">
        specifications = #{specifications,jdbcType=VARCHAR},
      </if>
      <if test="dosageForm != null">
        dosage_form = #{dosageForm,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        manufacturer = #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="component != null">
        component = #{component,jdbcType=VARCHAR},
      </if>
      <if test="thirtySalesQuantity != null">
        thirty_sales_quantity = #{thirtySalesQuantity,jdbcType=DECIMAL},
      </if>
      <if test="referRetailPrice != null">
        refer_retail_price = #{referRetailPrice,jdbcType=DECIMAL},
      </if>
      <if test="referGrossProfit != null">
        refer_gross_profit = #{referGrossProfit,jdbcType=DECIMAL},
      </if>
      <if test="salesRank != null">
        sales_rank = #{salesRank,jdbcType=INTEGER},
      </if>
      <if test="pushLevel != null">
        push_level = #{pushLevel,jdbcType=VARCHAR},
      </if>
      <if test="medicinePeople != null">
        medicine_people = #{medicinePeople,jdbcType=VARCHAR},
      </if>
      <if test="otcAble != null">
        otc_able = #{otcAble,jdbcType=TINYINT},
      </if>
      <if test="rxAble != null">
        rx_able = #{rxAble,jdbcType=TINYINT},
      </if>
      <if test="ybAble != null">
        yb_able = #{ybAble,jdbcType=TINYINT},
      </if>
      <if test="sensitiveAble != null">
        sensitive_able = #{sensitiveAble,jdbcType=TINYINT},
      </if>
      <if test="categoryId != null">
        category_id = #{categoryId,jdbcType=BIGINT},
      </if>
      <if test="category != null">
        category = #{category,jdbcType=VARCHAR},
      </if>
      <if test="promotionTag != null">
        promotion_tag = #{promotionTag,jdbcType=VARCHAR},
      </if>
      <if test="promotionName != null">
        promotion_name = #{promotionName,jdbcType=VARCHAR},
      </if>
      <if test="promotionWay != null">
        promotion_way = #{promotionWay,jdbcType=VARCHAR},
      </if>
      <if test="thresholdInfo != null">
        threshold_info = #{thresholdInfo,jdbcType=VARCHAR},
      </if>
      <if test="favInfo != null">
        fav_info = #{favInfo,jdbcType=VARCHAR},
      </if>
      <if test="recommendReason != null">
        recommend_reason = #{recommendReason,jdbcType=VARCHAR},
      </if>
      <if test="compositeNew != null">
        composite_new = #{compositeNew,jdbcType=TINYINT},
      </if>
      <if test="indications != null">
        indications = #{indications,jdbcType=VARCHAR},
      </if>
      <if test="pictureUrl != null">
        picture_url = #{pictureUrl,jdbcType=VARCHAR},
      </if>
      <if test="suggestType != null">
        suggest_type = #{suggestType,jdbcType=TINYINT},
      </if>
      <if test="dealSuggest != null">
        deal_suggest = #{dealSuggest,jdbcType=TINYINT},
      </if>
      <if test="complementQty != null">
        complement_qty = #{complementQty,jdbcType=INTEGER},
      </if>
      <if test="popWin != null">
        pop_win = #{popWin,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        updated_name = #{updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.iscm.entityTidb.BdpSuggestBestGoods">
    update bdp_suggest_best_goods
    set suggest_date = #{suggestDate,jdbcType=DATE},
      company_code = #{companyCode,jdbcType=VARCHAR},
      store_code = #{storeCode,jdbcType=VARCHAR},
      push_hd_status = #{pushHdStatus,jdbcType=TINYINT},
      goods_no = #{goodsNo,jdbcType=VARCHAR},
      goods_name = #{goodsName,jdbcType=VARCHAR},
      specifications = #{specifications,jdbcType=VARCHAR},
      dosage_form = #{dosageForm,jdbcType=VARCHAR},
      manufacturer = #{manufacturer,jdbcType=VARCHAR},
      component = #{component,jdbcType=VARCHAR},
      thirty_sales_quantity = #{thirtySalesQuantity,jdbcType=DECIMAL},
      refer_retail_price = #{referRetailPrice,jdbcType=DECIMAL},
      refer_gross_profit = #{referGrossProfit,jdbcType=DECIMAL},
      sales_rank = #{salesRank,jdbcType=INTEGER},
      push_level = #{pushLevel,jdbcType=VARCHAR},
      medicine_people = #{medicinePeople,jdbcType=VARCHAR},
      otc_able = #{otcAble,jdbcType=TINYINT},
      rx_able = #{rxAble,jdbcType=TINYINT},
      yb_able = #{ybAble,jdbcType=TINYINT},
      sensitive_able = #{sensitiveAble,jdbcType=TINYINT},
      category_id = #{categoryId,jdbcType=BIGINT},
      category = #{category,jdbcType=VARCHAR},
      promotion_tag = #{promotionTag,jdbcType=VARCHAR},
      promotion_name = #{promotionName,jdbcType=VARCHAR},
      promotion_way = #{promotionWay,jdbcType=VARCHAR},
      threshold_info = #{thresholdInfo,jdbcType=VARCHAR},
      fav_info = #{favInfo,jdbcType=VARCHAR},
      recommend_reason = #{recommendReason,jdbcType=VARCHAR},
      composite_new = #{compositeNew,jdbcType=TINYINT},
      indications = #{indications,jdbcType=VARCHAR},
      picture_url = #{pictureUrl,jdbcType=VARCHAR},
      suggest_type = #{suggestType,jdbcType=TINYINT},
      deal_suggest = #{dealSuggest,jdbcType=TINYINT},
      complement_qty = #{complementQty,jdbcType=INTEGER},
      pop_win = #{popWin,jdbcType=TINYINT},
      `status` = #{status,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_name = #{createdName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_name = #{updatedName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
