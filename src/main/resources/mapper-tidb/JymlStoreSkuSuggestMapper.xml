<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapperTidb.JymlStoreSkuSuggestMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entityTidb.JymlStoreSkuSuggest">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="business_org_id" jdbcType="BIGINT" property="businessOrgId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="suggest_manage_status" jdbcType="VARCHAR" property="suggestManageStatus" />
    <result column="suggest_manage_status_name" jdbcType="VARCHAR" property="suggestManageStatusName" />
    <result column="category" jdbcType="VARCHAR" property="category" />
    <result column="category_name" jdbcType="VARCHAR" property="categoryName" />
    <result column="rx_otc" jdbcType="VARCHAR" property="rxOtc" />
    <result column="middle_category" jdbcType="VARCHAR" property="middleCategory" />
    <result column="middle_category_name" jdbcType="VARCHAR" property="middleCategoryName" />
    <result column="small_category" jdbcType="VARCHAR" property="smallCategory" />
    <result column="small_category_name" jdbcType="VARCHAR" property="smallCategoryName" />
    <result column="sub_category" jdbcType="VARCHAR" property="subCategory" />
    <result column="sub_category_name" jdbcType="VARCHAR" property="subCategoryName" />
    <result column="component" jdbcType="VARCHAR" property="component" />
    <result column="stock_quantity" jdbcType="VARCHAR" property="stockQuantity" />
    <result column="sales_last_30d" jdbcType="VARCHAR" property="salesLast30d" />
    <result column="sales_last_60d" jdbcType="VARCHAR" property="salesLast60d" />
    <result column="sales_last_90d" jdbcType="VARCHAR" property="salesLast90d" />
    <result column="sales_last_180d" jdbcType="VARCHAR" property="salesLast180d" />
    <result column="seasonal_component" jdbcType="VARCHAR" property="seasonalComponent" />
    <result column="sales_last_year_same_period_last_90d" jdbcType="VARCHAR" property="salesLastYearSamePeriodLast90d" />
    <result column="city_store_sales_rate_90d" jdbcType="VARCHAR" property="cityStoreSalesRate90d" />
    <result column="store_type_sales_rate_90d" jdbcType="VARCHAR" property="storeTypeSalesRate90d" />
    <result column="days_unsold" jdbcType="VARCHAR" property="daysUnsold" />
    <result column="city_product_contribution_rank" jdbcType="VARCHAR" property="cityProductContributionRank" />
    <result column="city_subcategory_contribution_rank" jdbcType="VARCHAR" property="citySubcategoryContributionRank" />
    <result column="price_lsj" jdbcType="VARCHAR" property="priceLsj" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="BIGINT" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, business_org_id, store_id, store_code, goods_no, goods_name, suggest_manage_status,
    suggest_manage_status_name, category, category_name, rx_otc, middle_category, middle_category_name,
    small_category, small_category_name, sub_category, sub_category_name, component,
    stock_quantity, sales_last_30d, sales_last_60d, sales_last_90d, sales_last_180d,
    seasonal_component, sales_last_year_same_period_last_90d, city_store_sales_rate_90d,
    store_type_sales_rate_90d, days_unsold, city_product_contribution_rank, city_subcategory_contribution_rank,
    price_lsj, `status`, gmt_create, gmt_update, extend, version, created_by, created_name,
    updated_by, updated_name
  </sql>
  <select id="selectByExample" parameterType="com.cowell.iscm.entityTidb.JymlStoreSkuSuggestExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from jyml_store_sku_suggest
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from jyml_store_sku_suggest
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from jyml_store_sku_suggest
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.iscm.entityTidb.JymlStoreSkuSuggestExample">
    delete from jyml_store_sku_suggest
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </delete>

  <insert id="insert" parameterType="com.cowell.iscm.entityTidb.JymlStoreSkuSuggest">
    insert into jyml_store_sku_suggest (id, business_org_id, store_id,
    store_code, goods_no, goods_name,
    suggest_manage_status, suggest_manage_status_name,
    category, category_name, rx_otc,
    middle_category, middle_category_name, small_category,
    small_category_name, sub_category, sub_category_name,
    component, stock_quantity, sales_last_30d,
    sales_last_60d, sales_last_90d, sales_last_180d,
    seasonal_component, sales_last_year_same_period_last_90d,
    city_store_sales_rate_90d, store_type_sales_rate_90d,
    days_unsold, city_product_contribution_rank,
    city_subcategory_contribution_rank, price_lsj,
    `status`, gmt_create, gmt_update,
    extend, version, created_by,
    created_name, updated_by, updated_name
    )
    values (#{id,jdbcType=BIGINT}, #{businessOrgId,jdbcType=BIGINT}, #{storeId,jdbcType=BIGINT},
    #{storeCode,jdbcType=VARCHAR}, #{goodsNo,jdbcType=VARCHAR}, #{goodsName,jdbcType=VARCHAR},
    #{suggestManageStatus,jdbcType=VARCHAR}, #{suggestManageStatusName,jdbcType=VARCHAR},
    #{category,jdbcType=VARCHAR}, #{categoryName,jdbcType=VARCHAR}, #{rxOtc,jdbcType=VARCHAR},
    #{middleCategory,jdbcType=VARCHAR}, #{middleCategoryName,jdbcType=VARCHAR}, #{smallCategory,jdbcType=VARCHAR},
    #{smallCategoryName,jdbcType=VARCHAR}, #{subCategory,jdbcType=VARCHAR}, #{subCategoryName,jdbcType=VARCHAR},
    #{component,jdbcType=VARCHAR}, #{stockQuantity,jdbcType=VARCHAR}, #{salesLast30d,jdbcType=VARCHAR},
    #{salesLast60d,jdbcType=VARCHAR}, #{salesLast90d,jdbcType=VARCHAR}, #{salesLast180d,jdbcType=VARCHAR},
    #{seasonalComponent,jdbcType=VARCHAR}, #{salesLastYearSamePeriodLast90d,jdbcType=VARCHAR},
    #{cityStoreSalesRate90d,jdbcType=VARCHAR}, #{storeTypeSalesRate90d,jdbcType=VARCHAR},
    #{daysUnsold,jdbcType=VARCHAR}, #{cityProductContributionRank,jdbcType=VARCHAR},
    #{citySubcategoryContributionRank,jdbcType=VARCHAR}, #{priceLsj,jdbcType=VARCHAR},
    #{status,jdbcType=TINYINT}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP},
    #{extend,jdbcType=VARCHAR}, #{version,jdbcType=BIGINT}, #{createdBy,jdbcType=BIGINT},
    #{createdName,jdbcType=VARCHAR}, #{updatedBy,jdbcType=BIGINT}, #{updatedName,jdbcType=VARCHAR}
    )
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.iscm.entityTidb.JymlStoreSkuSuggest">
    insert into jyml_store_sku_suggest
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="businessOrgId != null">
        business_org_id,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="storeCode != null">
        store_code,
      </if>
      <if test="goodsNo != null">
        goods_no,
      </if>
      <if test="goodsName != null">
        goods_name,
      </if>
      <if test="suggestManageStatus != null">
        suggest_manage_status,
      </if>
      <if test="suggestManageStatusName != null">
        suggest_manage_status_name,
      </if>
      <if test="category != null">
        category,
      </if>
      <if test="categoryName != null">
        category_name,
      </if>
      <if test="rxOtc != null">
        rx_otc,
      </if>
      <if test="middleCategory != null">
        middle_category,
      </if>
      <if test="middleCategoryName != null">
        middle_category_name,
      </if>
      <if test="smallCategory != null">
        small_category,
      </if>
      <if test="smallCategoryName != null">
        small_category_name,
      </if>
      <if test="subCategory != null">
        sub_category,
      </if>
      <if test="subCategoryName != null">
        sub_category_name,
      </if>
      <if test="component != null">
        component,
      </if>
      <if test="stockQuantity != null">
        stock_quantity,
      </if>
      <if test="salesLast30d != null">
        sales_last_30d,
      </if>
      <if test="salesLast60d != null">
        sales_last_60d,
      </if>
      <if test="salesLast90d != null">
        sales_last_90d,
      </if>
      <if test="salesLast180d != null">
        sales_last_180d,
      </if>
      <if test="seasonalComponent != null">
        seasonal_component,
      </if>
      <if test="salesLastYearSamePeriodLast90d != null">
        sales_last_year_same_period_last_90d,
      </if>
      <if test="cityStoreSalesRate90d != null">
        city_store_sales_rate_90d,
      </if>
      <if test="storeTypeSalesRate90d != null">
        store_type_sales_rate_90d,
      </if>
      <if test="daysUnsold != null">
        days_unsold,
      </if>
      <if test="cityProductContributionRank != null">
        city_product_contribution_rank,
      </if>
      <if test="citySubcategoryContributionRank != null">
        city_subcategory_contribution_rank,
      </if>
      <if test="priceLsj != null">
        price_lsj,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedName != null">
        updated_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="businessOrgId != null">
        #{businessOrgId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="storeCode != null">
        #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsNo != null">
        #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="suggestManageStatus != null">
        #{suggestManageStatus,jdbcType=VARCHAR},
      </if>
      <if test="suggestManageStatusName != null">
        #{suggestManageStatusName,jdbcType=VARCHAR},
      </if>
      <if test="category != null">
        #{category,jdbcType=VARCHAR},
      </if>
      <if test="categoryName != null">
        #{categoryName,jdbcType=VARCHAR},
      </if>
      <if test="rxOtc != null">
        #{rxOtc,jdbcType=VARCHAR},
      </if>
      <if test="middleCategory != null">
        #{middleCategory,jdbcType=VARCHAR},
      </if>
      <if test="middleCategoryName != null">
        #{middleCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="smallCategory != null">
        #{smallCategory,jdbcType=VARCHAR},
      </if>
      <if test="smallCategoryName != null">
        #{smallCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="subCategory != null">
        #{subCategory,jdbcType=VARCHAR},
      </if>
      <if test="subCategoryName != null">
        #{subCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="component != null">
        #{component,jdbcType=VARCHAR},
      </if>
      <if test="stockQuantity != null">
        #{stockQuantity,jdbcType=VARCHAR},
      </if>
      <if test="salesLast30d != null">
        #{salesLast30d,jdbcType=VARCHAR},
      </if>
      <if test="salesLast60d != null">
        #{salesLast60d,jdbcType=VARCHAR},
      </if>
      <if test="salesLast90d != null">
        #{salesLast90d,jdbcType=VARCHAR},
      </if>
      <if test="salesLast180d != null">
        #{salesLast180d,jdbcType=VARCHAR},
      </if>
      <if test="seasonalComponent != null">
        #{seasonalComponent,jdbcType=VARCHAR},
      </if>
      <if test="salesLastYearSamePeriodLast90d != null">
        #{salesLastYearSamePeriodLast90d,jdbcType=VARCHAR},
      </if>
      <if test="cityStoreSalesRate90d != null">
        #{cityStoreSalesRate90d,jdbcType=VARCHAR},
      </if>
      <if test="storeTypeSalesRate90d != null">
        #{storeTypeSalesRate90d,jdbcType=VARCHAR},
      </if>
      <if test="daysUnsold != null">
        #{daysUnsold,jdbcType=VARCHAR},
      </if>
      <if test="cityProductContributionRank != null">
        #{cityProductContributionRank,jdbcType=VARCHAR},
      </if>
      <if test="citySubcategoryContributionRank != null">
        #{citySubcategoryContributionRank,jdbcType=VARCHAR},
      </if>
      <if test="priceLsj != null">
        #{priceLsj,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=BIGINT},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        #{updatedName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.iscm.entityTidb.JymlStoreSkuSuggestExample" resultType="java.lang.Long">
    select count(*) from jyml_store_sku_suggest
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update jyml_store_sku_suggest
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.businessOrgId != null">
        business_org_id = #{record.businessOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.storeCode != null">
        store_code = #{record.storeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsNo != null">
        goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsName != null">
        goods_name = #{record.goodsName,jdbcType=VARCHAR},
      </if>
      <if test="record.suggestManageStatus != null">
        suggest_manage_status = #{record.suggestManageStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.suggestManageStatusName != null">
        suggest_manage_status_name = #{record.suggestManageStatusName,jdbcType=VARCHAR},
      </if>
      <if test="record.category != null">
        category = #{record.category,jdbcType=VARCHAR},
      </if>
      <if test="record.categoryName != null">
        category_name = #{record.categoryName,jdbcType=VARCHAR},
      </if>
      <if test="record.rxOtc != null">
        rx_otc = #{record.rxOtc,jdbcType=VARCHAR},
      </if>
      <if test="record.middleCategory != null">
        middle_category = #{record.middleCategory,jdbcType=VARCHAR},
      </if>
      <if test="record.middleCategoryName != null">
        middle_category_name = #{record.middleCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="record.smallCategory != null">
        small_category = #{record.smallCategory,jdbcType=VARCHAR},
      </if>
      <if test="record.smallCategoryName != null">
        small_category_name = #{record.smallCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="record.subCategory != null">
        sub_category = #{record.subCategory,jdbcType=VARCHAR},
      </if>
      <if test="record.subCategoryName != null">
        sub_category_name = #{record.subCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="record.component != null">
        component = #{record.component,jdbcType=VARCHAR},
      </if>
      <if test="record.stockQuantity != null">
        stock_quantity = #{record.stockQuantity,jdbcType=VARCHAR},
      </if>
      <if test="record.salesLast30d != null">
        sales_last_30d = #{record.salesLast30d,jdbcType=VARCHAR},
      </if>
      <if test="record.salesLast60d != null">
        sales_last_60d = #{record.salesLast60d,jdbcType=VARCHAR},
      </if>
      <if test="record.salesLast90d != null">
        sales_last_90d = #{record.salesLast90d,jdbcType=VARCHAR},
      </if>
      <if test="record.salesLast180d != null">
        sales_last_180d = #{record.salesLast180d,jdbcType=VARCHAR},
      </if>
      <if test="record.seasonalComponent != null">
        seasonal_component = #{record.seasonalComponent,jdbcType=VARCHAR},
      </if>
      <if test="record.salesLastYearSamePeriodLast90d != null">
        sales_last_year_same_period_last_90d = #{record.salesLastYearSamePeriodLast90d,jdbcType=VARCHAR},
      </if>
      <if test="record.cityStoreSalesRate90d != null">
        city_store_sales_rate_90d = #{record.cityStoreSalesRate90d,jdbcType=VARCHAR},
      </if>
      <if test="record.storeTypeSalesRate90d != null">
        store_type_sales_rate_90d = #{record.storeTypeSalesRate90d,jdbcType=VARCHAR},
      </if>
      <if test="record.daysUnsold != null">
        days_unsold = #{record.daysUnsold,jdbcType=VARCHAR},
      </if>
      <if test="record.cityProductContributionRank != null">
        city_product_contribution_rank = #{record.cityProductContributionRank,jdbcType=VARCHAR},
      </if>
      <if test="record.citySubcategoryContributionRank != null">
        city_subcategory_contribution_rank = #{record.citySubcategoryContributionRank,jdbcType=VARCHAR},
      </if>
      <if test="record.priceLsj != null">
        price_lsj = #{record.priceLsj,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=BIGINT},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedName != null">
        updated_name = #{record.updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update jyml_store_sku_suggest
    set id = #{record.id,jdbcType=BIGINT},
    business_org_id = #{record.businessOrgId,jdbcType=BIGINT},
    store_id = #{record.storeId,jdbcType=BIGINT},
    store_code = #{record.storeCode,jdbcType=VARCHAR},
    goods_no = #{record.goodsNo,jdbcType=VARCHAR},
    goods_name = #{record.goodsName,jdbcType=VARCHAR},
    suggest_manage_status = #{record.suggestManageStatus,jdbcType=VARCHAR},
    suggest_manage_status_name = #{record.suggestManageStatusName,jdbcType=VARCHAR},
    category = #{record.category,jdbcType=VARCHAR},
    category_name = #{record.categoryName,jdbcType=VARCHAR},
    rx_otc = #{record.rxOtc,jdbcType=VARCHAR},
    middle_category = #{record.middleCategory,jdbcType=VARCHAR},
    middle_category_name = #{record.middleCategoryName,jdbcType=VARCHAR},
    small_category = #{record.smallCategory,jdbcType=VARCHAR},
    small_category_name = #{record.smallCategoryName,jdbcType=VARCHAR},
    sub_category = #{record.subCategory,jdbcType=VARCHAR},
    sub_category_name = #{record.subCategoryName,jdbcType=VARCHAR},
    component = #{record.component,jdbcType=VARCHAR},
    stock_quantity = #{record.stockQuantity,jdbcType=VARCHAR},
    sales_last_30d = #{record.salesLast30d,jdbcType=VARCHAR},
    sales_last_60d = #{record.salesLast60d,jdbcType=VARCHAR},
    sales_last_90d = #{record.salesLast90d,jdbcType=VARCHAR},
    sales_last_180d = #{record.salesLast180d,jdbcType=VARCHAR},
    seasonal_component = #{record.seasonalComponent,jdbcType=VARCHAR},
    sales_last_year_same_period_last_90d = #{record.salesLastYearSamePeriodLast90d,jdbcType=VARCHAR},
    city_store_sales_rate_90d = #{record.cityStoreSalesRate90d,jdbcType=VARCHAR},
    store_type_sales_rate_90d = #{record.storeTypeSalesRate90d,jdbcType=VARCHAR},
    days_unsold = #{record.daysUnsold,jdbcType=VARCHAR},
    city_product_contribution_rank = #{record.cityProductContributionRank,jdbcType=VARCHAR},
    city_subcategory_contribution_rank = #{record.citySubcategoryContributionRank,jdbcType=VARCHAR},
    price_lsj = #{record.priceLsj,jdbcType=VARCHAR},
    `status` = #{record.status,jdbcType=TINYINT},
    gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
    gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
    extend = #{record.extend,jdbcType=VARCHAR},
    version = #{record.version,jdbcType=BIGINT},
    created_by = #{record.createdBy,jdbcType=BIGINT},
    created_name = #{record.createdName,jdbcType=VARCHAR},
    updated_by = #{record.updatedBy,jdbcType=BIGINT},
    updated_name = #{record.updatedName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.iscm.entityTidb.JymlStoreSkuSuggest">
    update jyml_store_sku_suggest
    <set>
      <if test="businessOrgId != null">
        business_org_id = #{businessOrgId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="storeCode != null">
        store_code = #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsNo != null">
        goods_no = #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        goods_name = #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="suggestManageStatus != null">
        suggest_manage_status = #{suggestManageStatus,jdbcType=VARCHAR},
      </if>
      <if test="suggestManageStatusName != null">
        suggest_manage_status_name = #{suggestManageStatusName,jdbcType=VARCHAR},
      </if>
      <if test="category != null">
        category = #{category,jdbcType=VARCHAR},
      </if>
      <if test="categoryName != null">
        category_name = #{categoryName,jdbcType=VARCHAR},
      </if>
      <if test="rxOtc != null">
        rx_otc = #{rxOtc,jdbcType=VARCHAR},
      </if>
      <if test="middleCategory != null">
        middle_category = #{middleCategory,jdbcType=VARCHAR},
      </if>
      <if test="middleCategoryName != null">
        middle_category_name = #{middleCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="smallCategory != null">
        small_category = #{smallCategory,jdbcType=VARCHAR},
      </if>
      <if test="smallCategoryName != null">
        small_category_name = #{smallCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="subCategory != null">
        sub_category = #{subCategory,jdbcType=VARCHAR},
      </if>
      <if test="subCategoryName != null">
        sub_category_name = #{subCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="component != null">
        component = #{component,jdbcType=VARCHAR},
      </if>
      <if test="stockQuantity != null">
        stock_quantity = #{stockQuantity,jdbcType=VARCHAR},
      </if>
      <if test="salesLast30d != null">
        sales_last_30d = #{salesLast30d,jdbcType=VARCHAR},
      </if>
      <if test="salesLast60d != null">
        sales_last_60d = #{salesLast60d,jdbcType=VARCHAR},
      </if>
      <if test="salesLast90d != null">
        sales_last_90d = #{salesLast90d,jdbcType=VARCHAR},
      </if>
      <if test="salesLast180d != null">
        sales_last_180d = #{salesLast180d,jdbcType=VARCHAR},
      </if>
      <if test="seasonalComponent != null">
        seasonal_component = #{seasonalComponent,jdbcType=VARCHAR},
      </if>
      <if test="salesLastYearSamePeriodLast90d != null">
        sales_last_year_same_period_last_90d = #{salesLastYearSamePeriodLast90d,jdbcType=VARCHAR},
      </if>
      <if test="cityStoreSalesRate90d != null">
        city_store_sales_rate_90d = #{cityStoreSalesRate90d,jdbcType=VARCHAR},
      </if>
      <if test="storeTypeSalesRate90d != null">
        store_type_sales_rate_90d = #{storeTypeSalesRate90d,jdbcType=VARCHAR},
      </if>
      <if test="daysUnsold != null">
        days_unsold = #{daysUnsold,jdbcType=VARCHAR},
      </if>
      <if test="cityProductContributionRank != null">
        city_product_contribution_rank = #{cityProductContributionRank,jdbcType=VARCHAR},
      </if>
      <if test="citySubcategoryContributionRank != null">
        city_subcategory_contribution_rank = #{citySubcategoryContributionRank,jdbcType=VARCHAR},
      </if>
      <if test="priceLsj != null">
        price_lsj = #{priceLsj,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=BIGINT},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        updated_name = #{updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.iscm.entityTidb.JymlStoreSkuSuggest">
    update jyml_store_sku_suggest
    set business_org_id = #{businessOrgId,jdbcType=BIGINT},
    store_id = #{storeId,jdbcType=BIGINT},
    store_code = #{storeCode,jdbcType=VARCHAR},
    goods_no = #{goodsNo,jdbcType=VARCHAR},
    goods_name = #{goodsName,jdbcType=VARCHAR},
    suggest_manage_status = #{suggestManageStatus,jdbcType=VARCHAR},
    suggest_manage_status_name = #{suggestManageStatusName,jdbcType=VARCHAR},
    category = #{category,jdbcType=VARCHAR},
    category_name = #{categoryName,jdbcType=VARCHAR},
    rx_otc = #{rxOtc,jdbcType=VARCHAR},
    middle_category = #{middleCategory,jdbcType=VARCHAR},
    middle_category_name = #{middleCategoryName,jdbcType=VARCHAR},
    small_category = #{smallCategory,jdbcType=VARCHAR},
    small_category_name = #{smallCategoryName,jdbcType=VARCHAR},
    sub_category = #{subCategory,jdbcType=VARCHAR},
    sub_category_name = #{subCategoryName,jdbcType=VARCHAR},
    component = #{component,jdbcType=VARCHAR},
    stock_quantity = #{stockQuantity,jdbcType=VARCHAR},
    sales_last_30d = #{salesLast30d,jdbcType=VARCHAR},
    sales_last_60d = #{salesLast60d,jdbcType=VARCHAR},
    sales_last_90d = #{salesLast90d,jdbcType=VARCHAR},
    sales_last_180d = #{salesLast180d,jdbcType=VARCHAR},
    seasonal_component = #{seasonalComponent,jdbcType=VARCHAR},
    sales_last_year_same_period_last_90d = #{salesLastYearSamePeriodLast90d,jdbcType=VARCHAR},
    city_store_sales_rate_90d = #{cityStoreSalesRate90d,jdbcType=VARCHAR},
    store_type_sales_rate_90d = #{storeTypeSalesRate90d,jdbcType=VARCHAR},
    days_unsold = #{daysUnsold,jdbcType=VARCHAR},
    city_product_contribution_rank = #{cityProductContributionRank,jdbcType=VARCHAR},
    city_subcategory_contribution_rank = #{citySubcategoryContributionRank,jdbcType=VARCHAR},
    price_lsj = #{priceLsj,jdbcType=VARCHAR},
    `status` = #{status,jdbcType=TINYINT},
    gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
    gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
    extend = #{extend,jdbcType=VARCHAR},
    version = #{version,jdbcType=BIGINT},
    created_by = #{createdBy,jdbcType=BIGINT},
    created_name = #{createdName,jdbcType=VARCHAR},
    updated_by = #{updatedBy,jdbcType=BIGINT},
    updated_name = #{updatedName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
