<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapperTidb.IscmSuggestDistexecMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entityTidb.IscmSuggestDistexec">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="register_date" jdbcType="TIMESTAMP" property="registerDate" />
    <result column="allot_type" jdbcType="TINYINT" property="allotType" />
    <result column="register_no" jdbcType="VARCHAR" property="registerNo" />
    <result column="pos_allot_no" jdbcType="VARCHAR" property="posAllotNo" />
    <result column="pos_allot_detail_id" jdbcType="BIGINT" property="posAllotDetailId" />
    <result column="out_company_code" jdbcType="VARCHAR" property="outCompanyCode" />
    <result column="in_company_code" jdbcType="VARCHAR" property="inCompanyCode" />
    <result column="out_store_code" jdbcType="VARCHAR" property="outStoreCode" />
    <result column="in_store_code" jdbcType="VARCHAR" property="inStoreCode" />
    <result column="out_approve_status" jdbcType="TINYINT" property="outApproveStatus" />
    <result column="out_approve_time" jdbcType="TIMESTAMP" property="outApproveTime" />
    <result column="out_allot_quantity" jdbcType="DECIMAL" property="outAllotQuantity" />
    <result column="in_approve_status" jdbcType="TINYINT" property="inApproveStatus" />
    <result column="in_approve_time" jdbcType="TIMESTAMP" property="inApproveTime" />
    <result column="in_allot_quantity" jdbcType="DECIMAL" property="inAllotQuantity" />
    <result column="in_stock_time" jdbcType="TIMESTAMP" property="inStockTime" />
    <result column="allot_quantity" jdbcType="DECIMAL" property="allotQuantity" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
    <result column="produce_date" jdbcType="VARCHAR" property="produceDate" />
    <result column="validity_date" jdbcType="VARCHAR" property="validityDate" />
    <result column="hd_batch_no" jdbcType="VARCHAR" property="hdBatchNo" />
    <result column="sap_batch_no" jdbcType="VARCHAR" property="sapBatchNo" />
    <result column="notes" jdbcType="VARCHAR" property="notes" />
    <result column="void_reason" jdbcType="VARCHAR" property="voidReason" />
    <result column="src_dist_no" jdbcType="VARCHAR" property="srcDistNo" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, register_date, allot_type, register_no, pos_allot_no, pos_allot_detail_id, out_company_code, 
    in_company_code, out_store_code, in_store_code, out_approve_status, out_approve_time, 
    out_allot_quantity, in_approve_status, in_approve_time, in_allot_quantity, in_stock_time, 
    allot_quantity, goods_no, batch_no, produce_date, validity_date, hd_batch_no, sap_batch_no, 
    notes, void_reason,src_dist_no, status, gmt_create, gmt_update, extend, version, created_by,
    created_name, updated_by, updated_name
  </sql>
  <select id="selectByExample" parameterType="com.cowell.iscm.entityTidb.IscmSuggestDistexecExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from iscm_suggest_distexec
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from iscm_suggest_distexec
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from iscm_suggest_distexec
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.iscm.entityTidb.IscmSuggestDistexecExample">
    delete from iscm_suggest_distexec
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.iscm.entityTidb.IscmSuggestDistexec">
    insert into iscm_suggest_distexec (id, register_date, allot_type, 
      register_no, pos_allot_no, pos_allot_detail_id, 
      out_company_code, in_company_code, out_store_code, 
      in_store_code, out_approve_status, out_approve_time, 
      out_allot_quantity, in_approve_status, in_approve_time, 
      in_allot_quantity, in_stock_time, allot_quantity, 
      goods_no, batch_no, produce_date, 
      validity_date, hd_batch_no, sap_batch_no, 
      notes, void_reason, status, 
      gmt_create, gmt_update, extend, 
      version, created_by, created_name, 
      updated_by, updated_name)
    values (#{id,jdbcType=BIGINT}, #{registerDate,jdbcType=TIMESTAMP}, #{allotType,jdbcType=TINYINT}, 
      #{registerNo,jdbcType=VARCHAR}, #{posAllotNo,jdbcType=VARCHAR}, #{posAllotDetailId,jdbcType=BIGINT}, 
      #{outCompanyCode,jdbcType=VARCHAR}, #{inCompanyCode,jdbcType=VARCHAR}, #{outStoreCode,jdbcType=VARCHAR}, 
      #{inStoreCode,jdbcType=VARCHAR}, #{outApproveStatus,jdbcType=TINYINT}, #{outApproveTime,jdbcType=TIMESTAMP}, 
      #{outAllotQuantity,jdbcType=DECIMAL}, #{inApproveStatus,jdbcType=TINYINT}, #{inApproveTime,jdbcType=TIMESTAMP}, 
      #{inAllotQuantity,jdbcType=DECIMAL}, #{inStockTime,jdbcType=TIMESTAMP}, #{allotQuantity,jdbcType=DECIMAL}, 
      #{goodsNo,jdbcType=VARCHAR}, #{batchNo,jdbcType=VARCHAR}, #{produceDate,jdbcType=VARCHAR}, 
      #{validityDate,jdbcType=VARCHAR}, #{hdBatchNo,jdbcType=VARCHAR}, #{sapBatchNo,jdbcType=VARCHAR}, 
      #{notes,jdbcType=VARCHAR}, #{voidReason,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}, #{extend,jdbcType=VARCHAR}, 
      #{version,jdbcType=INTEGER}, #{createdBy,jdbcType=BIGINT}, #{createdName,jdbcType=VARCHAR}, 
      #{updatedBy,jdbcType=BIGINT}, #{updatedName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.iscm.entityTidb.IscmSuggestDistexec">
    insert into iscm_suggest_distexec
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="registerDate != null">
        register_date,
      </if>
      <if test="allotType != null">
        allot_type,
      </if>
      <if test="registerNo != null">
        register_no,
      </if>
      <if test="posAllotNo != null">
        pos_allot_no,
      </if>
      <if test="posAllotDetailId != null">
        pos_allot_detail_id,
      </if>
      <if test="outCompanyCode != null">
        out_company_code,
      </if>
      <if test="inCompanyCode != null">
        in_company_code,
      </if>
      <if test="outStoreCode != null">
        out_store_code,
      </if>
      <if test="inStoreCode != null">
        in_store_code,
      </if>
      <if test="outApproveStatus != null">
        out_approve_status,
      </if>
      <if test="outApproveTime != null">
        out_approve_time,
      </if>
      <if test="outAllotQuantity != null">
        out_allot_quantity,
      </if>
      <if test="inApproveStatus != null">
        in_approve_status,
      </if>
      <if test="inApproveTime != null">
        in_approve_time,
      </if>
      <if test="inAllotQuantity != null">
        in_allot_quantity,
      </if>
      <if test="inStockTime != null">
        in_stock_time,
      </if>
      <if test="allotQuantity != null">
        allot_quantity,
      </if>
      <if test="goodsNo != null">
        goods_no,
      </if>
      <if test="batchNo != null">
        batch_no,
      </if>
      <if test="produceDate != null">
        produce_date,
      </if>
      <if test="validityDate != null">
        validity_date,
      </if>
      <if test="hdBatchNo != null">
        hd_batch_no,
      </if>
      <if test="sapBatchNo != null">
        sap_batch_no,
      </if>
      <if test="notes != null">
        notes,
      </if>
      <if test="voidReason != null">
        void_reason,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedName != null">
        updated_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="registerDate != null">
        #{registerDate,jdbcType=TIMESTAMP},
      </if>
      <if test="allotType != null">
        #{allotType,jdbcType=TINYINT},
      </if>
      <if test="registerNo != null">
        #{registerNo,jdbcType=VARCHAR},
      </if>
      <if test="posAllotNo != null">
        #{posAllotNo,jdbcType=VARCHAR},
      </if>
      <if test="posAllotDetailId != null">
        #{posAllotDetailId,jdbcType=BIGINT},
      </if>
      <if test="outCompanyCode != null">
        #{outCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="inCompanyCode != null">
        #{inCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="outStoreCode != null">
        #{outStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="inStoreCode != null">
        #{inStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="outApproveStatus != null">
        #{outApproveStatus,jdbcType=TINYINT},
      </if>
      <if test="outApproveTime != null">
        #{outApproveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="outAllotQuantity != null">
        #{outAllotQuantity,jdbcType=DECIMAL},
      </if>
      <if test="inApproveStatus != null">
        #{inApproveStatus,jdbcType=TINYINT},
      </if>
      <if test="inApproveTime != null">
        #{inApproveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="inAllotQuantity != null">
        #{inAllotQuantity,jdbcType=DECIMAL},
      </if>
      <if test="inStockTime != null">
        #{inStockTime,jdbcType=TIMESTAMP},
      </if>
      <if test="allotQuantity != null">
        #{allotQuantity,jdbcType=DECIMAL},
      </if>
      <if test="goodsNo != null">
        #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="batchNo != null">
        #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="produceDate != null">
        #{produceDate,jdbcType=VARCHAR},
      </if>
      <if test="validityDate != null">
        #{validityDate,jdbcType=VARCHAR},
      </if>
      <if test="hdBatchNo != null">
        #{hdBatchNo,jdbcType=VARCHAR},
      </if>
      <if test="sapBatchNo != null">
        #{sapBatchNo,jdbcType=VARCHAR},
      </if>
      <if test="notes != null">
        #{notes,jdbcType=VARCHAR},
      </if>
      <if test="voidReason != null">
        #{voidReason,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        #{updatedName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.iscm.entityTidb.IscmSuggestDistexecExample" resultType="java.lang.Long">
    select count(*) from iscm_suggest_distexec
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update iscm_suggest_distexec
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.registerDate != null">
        register_date = #{record.registerDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.allotType != null">
        allot_type = #{record.allotType,jdbcType=TINYINT},
      </if>
      <if test="record.registerNo != null">
        register_no = #{record.registerNo,jdbcType=VARCHAR},
      </if>
      <if test="record.posAllotNo != null">
        pos_allot_no = #{record.posAllotNo,jdbcType=VARCHAR},
      </if>
      <if test="record.posAllotDetailId != null">
        pos_allot_detail_id = #{record.posAllotDetailId,jdbcType=BIGINT},
      </if>
      <if test="record.outCompanyCode != null">
        out_company_code = #{record.outCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.inCompanyCode != null">
        in_company_code = #{record.inCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.outStoreCode != null">
        out_store_code = #{record.outStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="record.inStoreCode != null">
        in_store_code = #{record.inStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="record.outApproveStatus != null">
        out_approve_status = #{record.outApproveStatus,jdbcType=TINYINT},
      </if>
      <if test="record.outApproveTime != null">
        out_approve_time = #{record.outApproveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.outAllotQuantity != null">
        out_allot_quantity = #{record.outAllotQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.inApproveStatus != null">
        in_approve_status = #{record.inApproveStatus,jdbcType=TINYINT},
      </if>
      <if test="record.inApproveTime != null">
        in_approve_time = #{record.inApproveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.inAllotQuantity != null">
        in_allot_quantity = #{record.inAllotQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.inStockTime != null">
        in_stock_time = #{record.inStockTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.allotQuantity != null">
        allot_quantity = #{record.allotQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.goodsNo != null">
        goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="record.batchNo != null">
        batch_no = #{record.batchNo,jdbcType=VARCHAR},
      </if>
      <if test="record.produceDate != null">
        produce_date = #{record.produceDate,jdbcType=VARCHAR},
      </if>
      <if test="record.validityDate != null">
        validity_date = #{record.validityDate,jdbcType=VARCHAR},
      </if>
      <if test="record.hdBatchNo != null">
        hd_batch_no = #{record.hdBatchNo,jdbcType=VARCHAR},
      </if>
      <if test="record.sapBatchNo != null">
        sap_batch_no = #{record.sapBatchNo,jdbcType=VARCHAR},
      </if>
      <if test="record.notes != null">
        notes = #{record.notes,jdbcType=VARCHAR},
      </if>
      <if test="record.voidReason != null">
        void_reason = #{record.voidReason,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedName != null">
        updated_name = #{record.updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update iscm_suggest_distexec
    set id = #{record.id,jdbcType=BIGINT},
      register_date = #{record.registerDate,jdbcType=TIMESTAMP},
      allot_type = #{record.allotType,jdbcType=TINYINT},
      register_no = #{record.registerNo,jdbcType=VARCHAR},
      pos_allot_no = #{record.posAllotNo,jdbcType=VARCHAR},
      pos_allot_detail_id = #{record.posAllotDetailId,jdbcType=BIGINT},
      out_company_code = #{record.outCompanyCode,jdbcType=VARCHAR},
      in_company_code = #{record.inCompanyCode,jdbcType=VARCHAR},
      out_store_code = #{record.outStoreCode,jdbcType=VARCHAR},
      in_store_code = #{record.inStoreCode,jdbcType=VARCHAR},
      out_approve_status = #{record.outApproveStatus,jdbcType=TINYINT},
      out_approve_time = #{record.outApproveTime,jdbcType=TIMESTAMP},
      out_allot_quantity = #{record.outAllotQuantity,jdbcType=DECIMAL},
      in_approve_status = #{record.inApproveStatus,jdbcType=TINYINT},
      in_approve_time = #{record.inApproveTime,jdbcType=TIMESTAMP},
      in_allot_quantity = #{record.inAllotQuantity,jdbcType=DECIMAL},
      in_stock_time = #{record.inStockTime,jdbcType=TIMESTAMP},
      allot_quantity = #{record.allotQuantity,jdbcType=DECIMAL},
      goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      batch_no = #{record.batchNo,jdbcType=VARCHAR},
      produce_date = #{record.produceDate,jdbcType=VARCHAR},
      validity_date = #{record.validityDate,jdbcType=VARCHAR},
      hd_batch_no = #{record.hdBatchNo,jdbcType=VARCHAR},
      sap_batch_no = #{record.sapBatchNo,jdbcType=VARCHAR},
      notes = #{record.notes,jdbcType=VARCHAR},
      void_reason = #{record.voidReason,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=TINYINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_name = #{record.createdName,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_name = #{record.updatedName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.iscm.entityTidb.IscmSuggestDistexec">
    update iscm_suggest_distexec
    <set>
      <if test="registerDate != null">
        register_date = #{registerDate,jdbcType=TIMESTAMP},
      </if>
      <if test="allotType != null">
        allot_type = #{allotType,jdbcType=TINYINT},
      </if>
      <if test="registerNo != null">
        register_no = #{registerNo,jdbcType=VARCHAR},
      </if>
      <if test="posAllotNo != null">
        pos_allot_no = #{posAllotNo,jdbcType=VARCHAR},
      </if>
      <if test="posAllotDetailId != null">
        pos_allot_detail_id = #{posAllotDetailId,jdbcType=BIGINT},
      </if>
      <if test="outCompanyCode != null">
        out_company_code = #{outCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="inCompanyCode != null">
        in_company_code = #{inCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="outStoreCode != null">
        out_store_code = #{outStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="inStoreCode != null">
        in_store_code = #{inStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="outApproveStatus != null">
        out_approve_status = #{outApproveStatus,jdbcType=TINYINT},
      </if>
      <if test="outApproveTime != null">
        out_approve_time = #{outApproveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="outAllotQuantity != null">
        out_allot_quantity = #{outAllotQuantity,jdbcType=DECIMAL},
      </if>
      <if test="inApproveStatus != null">
        in_approve_status = #{inApproveStatus,jdbcType=TINYINT},
      </if>
      <if test="inApproveTime != null">
        in_approve_time = #{inApproveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="inAllotQuantity != null">
        in_allot_quantity = #{inAllotQuantity,jdbcType=DECIMAL},
      </if>
      <if test="inStockTime != null">
        in_stock_time = #{inStockTime,jdbcType=TIMESTAMP},
      </if>
      <if test="allotQuantity != null">
        allot_quantity = #{allotQuantity,jdbcType=DECIMAL},
      </if>
      <if test="goodsNo != null">
        goods_no = #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="batchNo != null">
        batch_no = #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="produceDate != null">
        produce_date = #{produceDate,jdbcType=VARCHAR},
      </if>
      <if test="validityDate != null">
        validity_date = #{validityDate,jdbcType=VARCHAR},
      </if>
      <if test="hdBatchNo != null">
        hd_batch_no = #{hdBatchNo,jdbcType=VARCHAR},
      </if>
      <if test="sapBatchNo != null">
        sap_batch_no = #{sapBatchNo,jdbcType=VARCHAR},
      </if>
      <if test="notes != null">
        notes = #{notes,jdbcType=VARCHAR},
      </if>
      <if test="voidReason != null">
        void_reason = #{voidReason,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        updated_name = #{updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.iscm.entityTidb.IscmSuggestDistexec">
    update iscm_suggest_distexec
    set register_date = #{registerDate,jdbcType=TIMESTAMP},
      allot_type = #{allotType,jdbcType=TINYINT},
      register_no = #{registerNo,jdbcType=VARCHAR},
      pos_allot_no = #{posAllotNo,jdbcType=VARCHAR},
      pos_allot_detail_id = #{posAllotDetailId,jdbcType=BIGINT},
      out_company_code = #{outCompanyCode,jdbcType=VARCHAR},
      in_company_code = #{inCompanyCode,jdbcType=VARCHAR},
      out_store_code = #{outStoreCode,jdbcType=VARCHAR},
      in_store_code = #{inStoreCode,jdbcType=VARCHAR},
      out_approve_status = #{outApproveStatus,jdbcType=TINYINT},
      out_approve_time = #{outApproveTime,jdbcType=TIMESTAMP},
      out_allot_quantity = #{outAllotQuantity,jdbcType=DECIMAL},
      in_approve_status = #{inApproveStatus,jdbcType=TINYINT},
      in_approve_time = #{inApproveTime,jdbcType=TIMESTAMP},
      in_allot_quantity = #{inAllotQuantity,jdbcType=DECIMAL},
      in_stock_time = #{inStockTime,jdbcType=TIMESTAMP},
      allot_quantity = #{allotQuantity,jdbcType=DECIMAL},
      goods_no = #{goodsNo,jdbcType=VARCHAR},
      batch_no = #{batchNo,jdbcType=VARCHAR},
      produce_date = #{produceDate,jdbcType=VARCHAR},
      validity_date = #{validityDate,jdbcType=VARCHAR},
      hd_batch_no = #{hdBatchNo,jdbcType=VARCHAR},
      sap_batch_no = #{sapBatchNo,jdbcType=VARCHAR},
      notes = #{notes,jdbcType=VARCHAR},
      void_reason = #{voidReason,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_name = #{createdName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_name = #{updatedName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
