<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapperTidb.extend.IscmShouldApplyStoreExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entityTidb.IscmShouldApplyStore">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="platform_name" jdbcType="VARCHAR" property="platformName" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="store_name" jdbcType="VARCHAR" property="storeName" />
    <result column="should_apply_date" jdbcType="DATE" property="shouldApplyDate" />
  </resultMap>
  <sql id="Base_Column_List">
    id, platform_name, company_code, company_name, store_code, store_name, should_apply_date
  </sql>
  <select id="countShouldApplyStore" resultType="java.lang.Integer">
    select count(distinct store_code) from iscm_should_apply_store where company_code = #{companyCode} and should_apply_date = #{applyDate}
  </select>
    <select id="countShouldApplyStoreGroupByCompany"
            resultType="com.cowell.iscm.service.dto.pushReplenishmentMonitor.StoreQuantityDTO">
      select company_code as companyCode, count(distinct store_code) as quantity
      from iscm_should_apply_store
      where  company_code in
      <foreach collection="companyCodes" item="companyCode" index="index" open="(" close=")" separator="," >
        #{companyCode}
      </foreach>
      and should_apply_date = #{applyDate}
      group by companyCode
    </select>
</mapper>
