<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapperTidb.extend.IscmManualRegisterExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entityTidb.IscmManualRegister">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="data_type" jdbcType="TINYINT" property="dataType" />
    <result column="register_type" jdbcType="TINYINT" property="registerType" />
    <result column="company_bdp_code" jdbcType="VARCHAR" property="companyBdpCode" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="warehouse_code" jdbcType="VARCHAR" property="warehouseCode" />
    <result column="warehouse_name" jdbcType="VARCHAR" property="warehouseName" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="register_quantity" jdbcType="DECIMAL" property="registerQuantity" />
    <result column="non_validity_stock_quantity" jdbcType="DECIMAL" property="nonValidityStockQuantity" />
    <result column="stock_upper_limit" jdbcType="DECIMAL" property="stockUpperLimit" />
    <result column="stock_lower_limit" jdbcType="DECIMAL" property="stockLowerLimit" />
    <result column="no_tax_inventory_cost_amount" jdbcType="DECIMAL" property="noTaxInventoryCostAmount" />
    <result column="non_sale_days" jdbcType="INTEGER" property="nonSaleDays" />
    <result column="synthesize_average_daily_sales" jdbcType="DECIMAL" property="synthesizeAverageDailySales" />
    <result column="thirty_sales_quantity" jdbcType="DECIMAL" property="thirtySalesQuantity" />
    <result column="thirty_sales_count" jdbcType="INTEGER" property="thirtySalesCount" />
    <result column="stock_quantity" jdbcType="DECIMAL" property="stockQuantity" />
    <result column="min_display_quantity" jdbcType="DECIMAL" property="minDisplayQuantity" />
    <result column="expect_sale_days" jdbcType="DECIMAL" property="expectSaleDays" />
    <result column="deal_status" jdbcType="TINYINT" property="dealStatus" />
    <result column="return_status" jdbcType="TINYINT" property="returnStatus" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Base_Column_List">
    id, data_type, register_type, company_bdp_code, company_code, store_code, warehouse_code,
    warehouse_name, goods_no, batch_no, manufacturer, register_quantity, non_validity_stock_quantity,
    stock_upper_limit, stock_lower_limit, no_tax_inventory_cost_amount, non_sale_days,
    synthesize_average_daily_sales, thirty_sales_quantity, thirty_sales_count, stock_quantity,
    min_display_quantity, expect_sale_days, deal_status, return_status, `status`, gmt_create,
    gmt_update, extend, version, created_by, created_name, updated_by, updated_name
  </sql>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>

  <update id="updateDealStatusByIds">
    update iscm_manual_register set deal_status = #{dealStatus}
    where id in
    <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
      #{id}
    </foreach>
  </update>

  <select id="selectWillRegister" resultMap="BaseResultMap">
<!--    select-->
<!--    <include refid="Base_Column_List" />-->
<!--    from-->
<!--    (-->
    select
    <include refid="Base_Column_List" />
    from iscm_manual_register
    where store_code in
    <foreach collection="storeCodes" item="storeCode" index="index" open="(" close=")" separator=",">
      #{storeCode}
    </foreach>
    and data_type in
    <foreach collection="dataTypes" item="dataType" index="index" open="(" close=")" separator=",">
      #{dataType}
    </foreach>
    and expect_sale_days between #{expectSaleDaysStart} and #{expectSaleDaysEnd}
    and non_sale_days between #{nonSaleDaysStart} and #{nonSaleDaysEnd}
    and deal_status = 2
    <if test="goodsNos != null and goodsNos.size >0">
      and goods_no in
      <foreach collection="goodsNos" item="goodsNo" index="index" open="(" close=")" separator=",">
        #{goodsNo}
      </foreach>
    </if>
    <if test="manufacturer != null">
      and manufacturer LIKE CONCAT(#{manufacturer},'%')
    </if>
    <if test="registerQuantityStart != null">
      and register_quantity &gt;= #{registerQuantityStart}
    </if>
    <if test="registerQuantityEnd != null">
      and register_quantity &lt;= #{registerQuantityEnd}
    </if>
    <if test="costAmountStart != null">
      and no_tax_inventory_cost_amount &gt;= #{costAmountStart}
    </if>
    <if test="costAmountEnd != null">
      and no_tax_inventory_cost_amount &lt;= #{costAmountEnd}
    </if>

    <if test="stockLowerLimitEnd != null">
      and stock_lower_limit &lt;= #{stockLowerLimitEnd}
    </if>
    <if test="stockLowerLimitStart != null">
      and stock_lower_limit &gt;= #{stockLowerLimitStart}
    </if>
    <if test="stockQuantityEnd != null">
      and stock_quantity &lt;= #{stockQuantityEnd}
    </if>
    <if test="stockQuantityStart != null">
      and stock_quantity &gt;= #{stockQuantityStart}
    </if>
    <if test="stockUpperLimitEnd != null">
      and stock_upper_limit &lt;= #{stockUpperLimitEnd}
    </if>
    <if test="stockUpperLimitStart != null">
      and stock_upper_limit &gt;= #{stockUpperLimitStart}
    </if>
    <if test="thirtySalesCountEnd != null">
      and thirty_sales_count &lt;= #{thirtySalesCountEnd}
    </if>
    <if test="thirtySalesCountStart != null">
      and thirty_sales_count &gt;= #{thirtySalesCountStart}
    </if>
    <if test="thirtySalesQuantityEnd != null">
      and thirty_sales_quantity &lt;= #{thirtySalesQuantityEnd}
    </if>
    <if test="thirtySalesQuantityStart != null">
      and thirty_sales_quantity &gt;= #{thirtySalesQuantityStart}
    </if>
<!--    ) a-->
<!--    where 1=1-->
<!--    <if test="ids != null and ids.size > 0">-->
<!--      and a.id not in-->
<!--      <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">-->
<!--        #{id}-->
<!--      </foreach>-->
<!--    </if>-->

    <choose>
      <when test='sortFieldName != null and sortFieldName != "" and sortType != null and sortType == 1'>
        order by ${sortFieldName} asc
      </when>
      <when test='sortFieldName != null and sortFieldName != "" and sortType != null and sortType == 2'>
        order by ${sortFieldName} desc
      </when>
      <otherwise>
        order by id DESC, store_code DESC, goods_no DESC
      </otherwise>
    </choose>
    limit ${offset}, ${limit}
  </select>

  <select id="counttWillRegister" resultType="java.lang.Long">
--     select
--     count(*)
--     from
--     (
    select count(*)
    from iscm_manual_register
    where store_code in
    <foreach collection="storeCodes" item="storeCode" index="index" open="(" close=")" separator=",">
      #{storeCode}
    </foreach>
    and data_type in
    <foreach collection="dataTypes" item="dataType" index="index" open="(" close=")" separator=",">
      #{dataType}
    </foreach>
    and expect_sale_days between #{expectSaleDaysStart} and #{expectSaleDaysEnd}
    and non_sale_days between #{nonSaleDaysStart} and #{nonSaleDaysEnd}
    and deal_status = 2
    <if test="goodsNos != null and goodsNos.size >0">
      and goods_no in
      <foreach collection="goodsNos" item="goodsNo" index="index" open="(" close=")" separator=",">
        #{goodsNo}
      </foreach>
    </if>
    <if test="manufacturer != null">
      and manufacturer LIKE CONCAT(#{manufacturer},'%')
    </if>
    <if test="goodsCommonName != null">
      and manufacturer LIKE CONCAT(#{goodsCommonName},'%')
    </if>

    <if test="registerQuantityStart != null">
      and register_quantity &gt;= #{registerQuantityStart}
    </if>
    <if test="registerQuantityEnd != null">
      and register_quantity &lt;= #{registerQuantityEnd}
    </if>
    <if test="costAmountStart != null">
      and no_tax_inventory_cost_amount &gt;= #{costAmountStart}
    </if>
    <if test="costAmountEnd != null">
      and no_tax_inventory_cost_amount &lt;= #{costAmountEnd}
    </if>

    <if test="stockLowerLimitEnd != null">
      and stock_lower_limit &lt;= #{stockLowerLimitEnd}
    </if>
    <if test="stockLowerLimitStart != null">
      and stock_lower_limit &gt;= #{stockLowerLimitStart}
    </if>
    <if test="stockQuantityEnd != null">
      and stock_quantity &lt;= #{stockQuantityEnd}
    </if>
    <if test="stockQuantityStart != null">
      and stock_quantity &gt;= #{stockQuantityStart}
    </if>
    <if test="stockUpperLimitEnd != null">
      and stock_upper_limit &lt;= #{stockUpperLimitEnd}
    </if>
    <if test="stockUpperLimitStart != null">
      and stock_upper_limit &gt;= #{stockUpperLimitStart}
    </if>
    <if test="thirtySalesCountEnd != null">
      and thirty_sales_count &lt;= #{thirtySalesCountEnd}
    </if>
    <if test="thirtySalesCountStart != null">
      and thirty_sales_count &gt;= #{thirtySalesCountStart}
    </if>
    <if test="thirtySalesQuantityEnd != null">
      and thirty_sales_quantity &lt;= #{thirtySalesQuantityEnd}
    </if>
    <if test="thirtySalesQuantityStart != null">
      and thirty_sales_quantity &gt;= #{thirtySalesQuantityStart}
    </if>

<!--    ) a-->
<!--    where 1=1-->
<!--    <if test="ids != null and ids.size > 0">-->
<!--    and a.id not in-->
<!--      <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">-->
<!--        #{id}-->
<!--      </foreach>-->
<!--    </if>-->
  </select>

  <select id="selectWillRegisterByStoreCode" resultMap="BaseResultMap">
<!--    select-->
<!--    <include refid="Base_Column_List" />-->
<!--    from-->
<!--    (-->
    select
    <include refid="Base_Column_List" />
    from iscm_manual_register
    where store_code = #{storeCode}
    and data_type in
    <foreach collection="dataTypes" item="dataType" index="index" open="(" close=")" separator=",">
      #{dataType}
    </foreach>
    and expect_sale_days between #{expectSaleDaysStart} and #{expectSaleDaysEnd}
    and non_sale_days between #{nonSaleDaysStart} and #{nonSaleDaysEnd}
    and deal_status = 2
    <if test="goodsNos != null and goodsNos.size >0">
      and goods_no in
      <foreach collection="goodsNos" item="goodsNo" index="index" open="(" close=")" separator=",">
        #{goodsNo}
      </foreach>
    </if>
    <if test="manufacturer != null">
      and manufacturer LIKE CONCAT(#{manufacturer},'%')
    </if>
    <if test="registerQuantityStart != null">
      and register_quantity &gt;= #{registerQuantityStart}
    </if>
    <if test="registerQuantityEnd != null">
      and register_quantity &lt;= #{registerQuantityEnd}
    </if>
    <if test="costAmountStart != null">
      and no_tax_inventory_cost_amount &gt;= #{costAmountStart}
    </if>
    <if test="costAmountEnd != null">
      and no_tax_inventory_cost_amount &lt;= #{costAmountEnd}
    </if>


    <if test="stockLowerLimitEnd != null">
      and stock_lower_limit &lt;= #{stockLowerLimitEnd}
    </if>
    <if test="stockLowerLimitStart != null">
      and stock_lower_limit &gt;= #{stockLowerLimitStart}
    </if>
    <if test="stockQuantityEnd != null">
      and stock_quantity &lt;= #{stockQuantityEnd}
    </if>
    <if test="stockQuantityStart != null">
      and stock_quantity &gt;= #{stockQuantityStart}
    </if>
    <if test="stockUpperLimitEnd != null">
      and stock_upper_limit &lt;= #{stockUpperLimitEnd}
    </if>
    <if test="stockUpperLimitStart != null">
      and stock_upper_limit &gt;= #{stockUpperLimitStart}
    </if>
    <if test="thirtySalesCountEnd != null">
      and thirty_sales_count &lt;= #{thirtySalesCountEnd}
    </if>
    <if test="thirtySalesCountStart != null">
      and thirty_sales_count &gt;= #{thirtySalesCountStart}
    </if>
    <if test="thirtySalesQuantityEnd != null">
      and thirty_sales_quantity &lt;= #{thirtySalesQuantityEnd}
    </if>
    <if test="thirtySalesQuantityStart != null">
      and thirty_sales_quantity &gt;= #{thirtySalesQuantityStart}
    </if>



<!--    ) a-->
<!--    where 1=1-->
<!--    <if test="ids != null and ids.size > 0">-->
<!--      and a.id not in-->
<!--      <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">-->
<!--        #{id}-->
<!--      </foreach>-->
<!--    </if>-->
    order by id DESC
    limit ${offset}, ${limit}
  </select>


  <select id="selectIdByStoreCodeAndGoodsNoAndBatchNo" parameterType="com.cowell.iscm.entityTidb.IscmManualRegister" resultType="java.lang.Long">
    SELECT id FROM iscm_manual_register WHERE store_code = #{storeCode,jdbcType=VARCHAR} and goods_no = #{goodsNo,jdbcType=VARCHAR}
    and register_type = #{registerType,jdbcType=TINYINT} and `status` = #{status,jdbcType=TINYINT}
    <if test="batchNo != null">
      and batch_no = #{batchNo,jdbcType=VARCHAR}
    </if>
    -- and deal_status = 1
  </select>
  <select id="countReturnExample" resultType="java.lang.Long">
      select
      count(*) from (
      select 1 from iscm_manual_register
      <if test="_parameter != null">
        <include refid="Example_Where_Clause" />
      </if>
      group by warehouse_code, goods_no ) as a
  </select>

  <select id="selectReturnByExample"
          resultType="com.cowell.iscm.service.dto.returnWarehouse.StoreReturnWarehouseConfirmDTO">
    select
        warehouse_code as warehouseCode, warehouse_name as warehouseName, goods_no as goodsNo, count(distinct company_code) as companyQuantity,
        count(distinct store_code) as storeQuantity, sum(stock_quantity) as totalRegisterQuantity, sum(no_tax_inventory_cost_amount / register_quantity * stock_quantity) as totalCostAmount, group_concat(distinct company_code) as companyCodeStr
    from iscm_manual_register
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    group by warehouse_code, goods_no
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
</mapper>
