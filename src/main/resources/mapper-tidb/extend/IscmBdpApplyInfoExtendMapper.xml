<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapperTidb.extend.IscmBdpApplyInfoExtendMapper">
    <resultMap id="BaseResultMap" type="com.cowell.iscm.entityTidb.IscmBdpApplyInfo">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="apply_no" jdbcType="VARCHAR" property="applyNo" />
        <result column="apply_line" jdbcType="VARCHAR" property="applyLine" />
        <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
        <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
        <result column="apply_date" jdbcType="DATE" property="applyDate" />
        <result column="data_origin_type" jdbcType="TINYINT" property="dataOriginType" />
        <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
        <result column="apply_goods_type" jdbcType="TINYINT" property="applyGoodsType" />
        <result column="apply_total" jdbcType="DECIMAL" property="applyTotal" />
        <result column="unqualified_await_stock" jdbcType="DECIMAL" property="unqualifiedAwaitStock" />
        <result column="transit_stock" jdbcType="DECIMAL" property="transitStock" />
        <result column="stock" jdbcType="DECIMAL" property="stock" />
        <result column="lock_stock" jdbcType="DECIMAL" property="lockStock" />
        <result column="unqualified_stock" jdbcType="DECIMAL" property="unqualifiedStock" />
        <result column="apply_transit_stock" jdbcType="DECIMAL" property="applyTransitStock" />
        <result column="in_transit_stock" jdbcType="DECIMAL" property="inTransitStock" />
        <result column="distr_transit_stock" jdbcType="DECIMAL" property="distrTransitStock" />
        <result column="special_ctrl" jdbcType="VARCHAR" property="specialCtrl" />
        <result column="special_thirty_days_qty" jdbcType="DECIMAL" property="specialThirtyDaysQty" />
        <result column="goods_level" jdbcType="VARCHAR" property="goodsLevel" />
        <result column="stock_upper_limit_days" jdbcType="INTEGER" property="stockUpperLimitDays" />
        <result column="stock_lower_limit_days" jdbcType="INTEGER" property="stockLowerLimitDays" />
        <result column="bdp_average_daily_sales" jdbcType="DECIMAL" property="bdpAverageDailySales" />
        <result column="min_display_qty" jdbcType="DECIMAL" property="minDisplayQty" />
        <result column="stock_upper_limit" jdbcType="DECIMAL" property="stockUpperLimit" />
        <result column="stock_lower_limit" jdbcType="DECIMAL" property="stockLowerLimit" />
        <result column="buy_stock" jdbcType="DECIMAL" property="buyStock" />
        <result column="sale_days_before" jdbcType="DECIMAL" property="saleDaysBefore" />
        <result column="sale_days_after" jdbcType="DECIMAL" property="saleDaysAfter" />
        <result column="thirty_days_sales" jdbcType="DECIMAL" property="thirtyDaysSales" />
        <result column="ninety_days_sales" jdbcType="DECIMAL" property="ninetyDaysSales" />
        <result column="company_apply_total" jdbcType="BIGINT" property="companyApplyTotal" />
        <result column="store_apply_total" jdbcType="BIGINT" property="storeApplyTotal" />
        <result column="middle_package_switch" jdbcType="VARCHAR" property="middlePackageSwitch" />
        <result column="middle_package_qty" jdbcType="DECIMAL" property="middlePackageQty" />
        <result column="middle_code_flag" jdbcType="TINYINT" property="middleCodeFlag" />
        <result column="apply_ratio" jdbcType="DECIMAL" property="applyRatio" />
        <result column="category_id" jdbcType="BIGINT" property="categoryId" />
        <result column="purchase_type" jdbcType="INTEGER" property="purchaseType" />
        <result column="purchase_channel" jdbcType="VARCHAR" property="purchaseChannel" />
        <result column="warehouse_code" jdbcType="VARCHAR" property="warehouseCode" />
        <result column="recommend_reason" jdbcType="VARCHAR" property="recommendReason" />
        <result column="promotion_name" jdbcType="VARCHAR" property="promotionName" />
        <result column="promotion_way" jdbcType="VARCHAR" property="promotionWay" />
        <result column="threshold_info" jdbcType="VARCHAR" property="thresholdInfo" />
        <result column="fav_info" jdbcType="VARCHAR" property="favInfo" />
        <result column="composite_new" jdbcType="TINYINT" property="compositeNew" />
        <result column="thirty_sales_quantity" jdbcType="DECIMAL" property="thirtySalesQuantity" />
        <result column="promotion_title" jdbcType="VARCHAR" property="promotionTitle" />
        <result column="promotion_start_date" jdbcType="VARCHAR" property="promotionStartDate" />
        <result column="promotion_end_date" jdbcType="VARCHAR" property="promotionEndDate" />
        <result column="deal_suggest" jdbcType="TINYINT" property="dealSuggest" />
        <result column="store_attr" jdbcType="TINYINT" property="storeAttr" />
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
        <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
        <result column="reason" jdbcType="VARCHAR" property="reason" />
    </resultMap>
  <sql id="Base_Column_List">
      id, apply_no, apply_line, company_code, store_code, apply_date, data_origin_type,
    goods_no, apply_goods_type, apply_total, unqualified_await_stock, transit_stock,
    stock, lock_stock, unqualified_stock, apply_transit_stock, in_transit_stock, distr_transit_stock,
    special_ctrl, special_thirty_days_qty, goods_level, stock_upper_limit_days, stock_lower_limit_days,
    bdp_average_daily_sales, min_display_qty, stock_upper_limit, stock_lower_limit, buy_stock,
    sale_days_before, sale_days_after, thirty_days_sales, ninety_days_sales, company_apply_total,
    store_apply_total, middle_package_switch, middle_package_qty, middle_code_flag, apply_ratio,
    category_id, purchase_type, purchase_channel, warehouse_code, recommend_reason, promotion_name,
    promotion_way, threshold_info, fav_info, composite_new, thirty_sales_quantity, promotion_title,
    promotion_start_date, promotion_end_date, deal_suggest, store_attr, gmt_create, gmt_update,
    reason
  </sql>
  <update id="batchUpdateReason" parameterType="map">
    update iscm_bdp_apply_info
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="reason=case" suffix="end,">
        <foreach collection="list" item="item" index="index">
          WHEN id=#{item.id,jdbcType=BIGINT} then #{item.reason}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
    <select id="getStoreCodesByCompanyCode" resultType="java.lang.String">
      select distinct store_code from iscm_bdp_apply_info where apply_date = #{applyDate, jdbcType=DATE} and company_code = #{companyCode}
    </select>
    <select id="getCompanyCodes" resultType="java.lang.String">
      select distinct company_code from iscm_bdp_apply_info where apply_date = #{applyDate, jdbcType=DATE}
    </select>
  <select id="getBdpWarnByCompanyCode" resultType="com.cowell.iscm.service.dto.applyParam.BdpWarnReportDTO">
    select store_code as storeCode, max(gmt_create) as bdpPushTime, count(*) as pushNum from iscm_bdp_apply_info
    where apply_date = #{applyDate, jdbcType=DATE} and company_code = #{companyCode}
    <if test="storeCodes != null and storeCodes.size > 0">
      and store_code in
      <foreach collection="storeCodes" item="storeCode" index="index" open="(" close=")" separator=",">
        #{storeCode}
      </foreach>
    </if>
    group by store_code
    ORDER BY bdpPushTime desc
  </select>
</mapper>
