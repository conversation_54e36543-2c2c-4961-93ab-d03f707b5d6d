<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapperTidb.extend.IscmExportBaseInfoExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entityTidb.IscmExportBaseInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="platform_name" jdbcType="VARCHAR" property="platformName" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="store_name" jdbcType="VARCHAR" property="storeName" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="apply_date" jdbcType="DATE" property="applyDate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
  </resultMap>
  <sql id="Base_Column_List">
    id, platform_name, company_name, company_code, store_code, store_name, goods_no, 
    goods_name, apply_date, extend
  </sql>
  <select id="selectAllExportDataList"
          resultType="com.cowell.iscm.service.dto.pushReplenishmentMonitor.MonitorAllDataDTO">
      select
--         a.business_type as businessType,
        a.platform_name as platformName,
        a.company_name as companyName,
        a.company_code as companyCode,
        a.store_code as storeCode,
        a.store_name as storeName,
        a.apply_date as applyDate,
        a.goods_no as goodsNo,
        a.goods_name as goodsName,
        'POS自动请货单'  as businessType1,
        b.specifications as posSpecifications, --  '规格'
        b.manufacturer as posManufacturer, --  '生产厂家'
        b.pos_apply_order_no as posApplyOrderNo, -- 'pos申请单号'
        b.apply_quantity as posApplyQuantity, --  '申请数量'
        b.apporve_quantity as posApporveQuantity, --  'POS审核数量'
        b.modify_result as posModifyResult, --  '修改结果：1.通过；2.改大；3.改小；4.拒绝（改零）,5.新增'
        b.pos_average_daily_sales as posAverageDailySales, --  'pos日均销量'
        b.bdp_average_daily_sales as posBdpAverageDailySales, --  'bdp日均销量'
        b.store_then_month_sales as storeThenMonthSales, --  '门店当时月销量'
        b.store_then_stock_quantity as storeThenStockQuantity, --  '门店当时库存'
        b.store_upper_limit as storeUpperLimit, -- '门店上限'
        b.store_lower_limit as storeLowerLimit, -- '门店下限'
        b.synthesize_average_daily_sales as synthesizeAverageDailySales, --  '综合日均销量'
        b.store_stock_quantity as storeStockQuantity, --  '门店实时库存'
        b.transit_quantity as transitQuantity, -- '在途数量'
        b.lower_display_quantity as lowerDisplayQuantity, -- '最低陈列量'
        b.order_type as posOrderType, --  '订单类型 1：手工 2 ：自动 3：紧急'
        b.order_status as posOrderStatus, -- '订单状态 1：已提交 2：作废'
         'SAP商采请货审核'  as businessType2,
        c.specifications as sapSpecifications, --  '规格'
        c.manufacturer as sapManufacturer, --  '生产厂家'
        c.sap_order_no as sapOrderNo, --  'sap订单号'
        c.apply_quantity as sapApplyQuantity, -- '申请数量'
        c.sap_approve_quantity as sapApproveQuantity, -- 'sap审核数量'
        c.modify_result as sapModifyResult, -- '修改结果：1.通过；2.改大；3.改小；4.拒绝(改零)'
        c.order_type as sapOrderType, --  '订单类型 1：铺货 2：pos'
        'BDP智能分货'  as businessType3,
        d.bdp_order_no as bdpOrderNo, --  'BDP订单号'
        d.apply_quantity as bpdApplyQuantity, --  '申请数量'
        d.bdp_distribute_before_quantity as bdpDistributeBeforeQuantity, --  'BDP分货前大仓库存'
        d.bdp_distribute_quantity as bdpDistributeQuantity, -- 'BDP分货数量'
        d.bdp_distribute_after_quantity as bdpDistributeAfterQuantity, --  'BDP分货后大仓库存'
        d.shipment_dc_code as shipmentDcCode, --  '发货DC代码'
        d.distribute_rate as distributeRate, -- 'BDP分货满足率'
        d.distribute_rate_type as distributeRateType, --  '0,完全不满足行数，10,(0%-85%)满足行数，20,[85%-100%)满足行数，100,100%满足行数'
        'SAP转配送单'  as businessType4,
        e.sap_order_no as sapTransferOrderNo, -- 'SAP订单号'
        e.order_type as sapTransferOrderType, -- '订单类型 1：手工 2 ：自动 3：紧急'
        e.apply_quantity as sapTransferApplyQuantity, --  '申请数量'
        e.sap_transfer_quantity as sapTransferQuantity, --  'SAP转单数量'
        e.sap_transfer_rate as sapTransferRate, --  'SAP转单满足率'
        e.sap_transfer_rate_type as sapTransferRateType, -- '0,完全不满足行数，10,(0%-85%)满足行数，20,[85%-100%)满足行数，100,100%满足行数'
        'WMS出库发货'  as businessType5,
        f.sap_order_no as wmsSapOrderNo, -- 'SAP订单号'
        f.order_type as wmsOrderType, -- '订单类型 1：手工 2 ：自动 3：紧急'
        f.order_quantity as wmsOrderQuantity, --  '订单数量'
        f.leave_warehouse as leaveWarehouse, -- '发货仓'
        f.wms_leave_warehouse_quantity as wmsLeaveWarehouseQuantity, -- 'WMS出库发货数量'
        f.wms_leave_warehouse_rate as wmsLeaveWarehouseRate, --  'WMS出库拣货率'
        f.wms_leave_warehouse_rate_type as wmsLeaveWarehouseRateType, --  '0,完全不满足行数，1,(0%-100%)满足行数，100,100%满足行数'
         'POS门店确认收货'  as businessType6,
        g.store_apply_order as posConfirmStoreApplyOrder, -- '配送申请单编号'
        g.wms_leave_warehouse_quantity as posConfirmWmsLeaveWarehouseQuantity, -- 'WMS出库发货数量也就是配送单数量'
        g.store_confirm_quantity as posConfirmStoreConfirmQuantity, -- '门店确认收货数量'
        g.arrive_rate as posConfirmArriveRate, -- '到货达成率'
        g.apply_date as posConfirmShouldApplyDate, --  '请货日期'
        g.store_confirm_date as posConfirmStoreConfirmDate -- '确认收货日期'
        from (select platform_name,company_name,company_code,store_code,store_name,apply_date,goods_no,goods_name
                        from iscm_export_base_info where apply_date = #{applyDate} and company_code = #{companyCode}
                        and id >=
                        (select id from iscm_export_base_info where apply_date = #{applyDate} and company_code = #{companyCode}
                        order by id asc
                        limit #{start}, 1)
                        order by id asc
                        limit #{pageSize}
        ) as a LEFT JOIN iscm_store_distribute_apply_order b ON a.store_code = b.store_code
          AND a.apply_date = b.apply_date
          AND a.goods_no = b.goods_no
          LEFT JOIN iscm_sap_purchase_approve_order c ON a.store_code = c.store_code
          AND a.apply_date = c.apply_date
          AND a.goods_no = c.goods_no
          LEFT JOIN iscm_bdp_intelligent_distribute d ON a.store_code = d.store_code
          AND a.apply_date = d.apply_date
          AND a.goods_no = d.goods_no
          LEFT JOIN iscm_sap_transfer_order e ON a.store_code = e.store_code
          AND a.apply_date = e.apply_date
          AND a.goods_no = e.goods_no
          LEFT JOIN iscm_wms_leave_warehouse_store_receive f ON a.store_code = f.store_code
          AND a.apply_date = f.apply_date
          AND a.goods_no = f.goods_no
          LEFT JOIN iscm_store_confirm_order g ON a.store_code = g.store_code
          AND a.apply_date = g.apply_date
          AND a.goods_no = g.goods_no
	</select>

</mapper>
