<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapperTidb.extend.IscmBdpDailySuggestExtendMapper">
    <select id="selectMonitorList" resultType="com.cowell.iscm.service.dto.visualcenter.suggestMonitor.MonitorDTO">
        select platform_name as platformName,
               company_code as companyCode,
               company_name as companyName,
               bdp_interface_count as bdpInterfaceCount,
               pos_interface_count as posInterfaceCount,
               pos_business_count as posBusinessCount,
               diff_count as diffCount,
               dt
        from iscm_bdp_daily_suggest_sum
        where dt between #{dtStart} and #{dtEnd}
        <if test="companyCodes != null and companyCodes.size > 0">
            and company_code in
            <foreach collection="companyCodes" item="companyCode" index="index" open="(" close=")" separator=",">
                #{companyCode}
            </foreach>
        </if>
    </select>
    <select id="selectMonitorDtCount" resultType="int">
        select count(*) from
            (
                select 1 from iscm_bdp_daily_suggest_sum
                where dt between #{dtStart} and #{dtEnd}
                <if test="companyCodes != null and companyCodes.size > 0">
                    and company_code in
                    <foreach collection="companyCodes" item="companyCode" index="index" open="(" close=")" separator=",">
                        #{companyCode}
                    </foreach>
                </if>
                group by dt
            ) T
    </select>
</mapper>
