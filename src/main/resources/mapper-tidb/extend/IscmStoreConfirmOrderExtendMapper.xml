<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapperTidb.extend.IscmStoreConfirmOrderExtendMapper">

  <select id="countReceivedStoreQuantity" resultType="java.lang.Integer">
    select count(distinct store_code) from  iscm_store_confirm_order where company_code = #{companyCode} and apply_date = #{applyDate} and store_confirm_quantity &gt;= 0
  </select>

  <select id="countShouldReceiveStoreQuantity" resultType="java.lang.Integer">
    select count(distinct store_code) from  iscm_store_confirm_order where company_code = #{companyCode} and apply_date = #{applyDate}
  </select>

  <select id="countShouldReceiveGoodsQuantity" resultType="java.lang.Integer">
    select count(*) from  iscm_store_confirm_order where company_code = #{companyCode} and apply_date = #{applyDate} and wms_leave_warehouse_quantity &gt; 0
  </select>

  <select id="countReceivedGoodsQuantity" resultType="java.lang.Integer">
    select count(*) from  iscm_store_confirm_order where company_code = #{companyCode} and apply_date = #{applyDate} and store_confirm_quantity &gt; 0
  </select>

  <select id="countWillReceiveGoodsQuantity" resultType="java.lang.Integer">
    select count(*) from  iscm_store_confirm_order
    where company_code = #{companyCode} and apply_date = #{applyDate} and wms_leave_warehouse_quantity &gt; 0 and store_confirm_quantity = 0
  </select>

  <select id="sumArriveRate" resultType="java.math.BigDecimal">
    select sum(arrive_rate) from  iscm_store_confirm_order where company_code = #{companyCode} and apply_date = #{applyDate} and wms_leave_warehouse_quantity  &gt; 0
  </select>

  <select id="selectStoreMinTime" resultType="com.cowell.iscm.service.dto.pushReplenishmentMonitor.StoreTimeDTO">
    select store_code as storeCode,
           min(generate_time) as storeTime,
           platform_name as platformName,
           company_name as companyName,
           store_name as storeName,
           apply_date as applyDate
    from iscm_store_confirm_order
    where company_code = #{companyCode}
    and apply_date = #{applyDate}
    group by store_code
    order by id desc
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>

  <select id="selectStoreMinTimeCount" resultType="java.lang.Long">
    select count(*) from (
        select
            store_code as storeCode,
            min(generate_time) as storeTime,
            platform_name as platformName,
            company_name as companyName,
            store_name as storeName,
            apply_date as applyDate
        from iscm_store_confirm_order
        where company_code = #{companyCode}
        and apply_date = #{applyDate}
        group by store_code
    ) t1
  </select>

  <select id="selectStartAndFinishTime" resultType="com.cowell.iscm.service.dto.pushReplenishmentMonitor.StoreConfirmOrderDataDTO">
    select DATE_FORMAT(min(generate_time),'%Y-%m-%d %H:%i:%s') as startTime,
           DATE_FORMAT(max(generate_time),'%Y-%m-%d %H:%i:%s') as finishTime
    from  iscm_store_confirm_order where company_code = #{companyCode} and apply_date = #{applyDate}
  </select>

  <select id="selectShouldReceiveGoods" resultType="com.cowell.iscm.service.dto.pushReplenishmentMonitor.IscmStoreConfirmOrderDTO">
    select
        t1.platform_name platformName,
        t1.company_code companyCode,
        t1.company_name companyName,
        t1.store_name storeName,
        t1.store_code storeCode,
        t1.goods_no goodsNo,
        t1.goods_name goodsName,
        t1.specifications specifications,
        t1.manufacturer manufacturer,
        t2.store_apply_order storeApplyOrder,
        t1.dn_order_no dnOrderNo,
        t2.wms_leave_warehouse_quantity wmsLeaveWarehouseQuantity,
        t2.store_confirm_quantity storeConfirmQuantity,
        t2.arrive_rate arriveRate,
        DATE_FORMAT(t2.apply_date,'%Y-%m-%d') as applyDate,
        DATE_FORMAT(t2.generate_time,'%Y-%m-%d %H:%i:%s') as generateTime,
        DATE_FORMAT(t2.store_confirm_date,'%Y-%m-%d') as storeConfirmDate
    from iscm_wms_leave_warehouse_store_receive t1 left join iscm_store_confirm_order t2
        on t1.company_code = t2.company_code
        and t1.store_code = t2.store_code
        and t1.apply_date = t2.apply_date
        and t1.goods_no = t2.goods_no
        and t1.sap_order_no = t2.dn_order_no
    where t1.company_code = #{companyCode} and t1.apply_date = #{applyDate}
    and t1.wms_leave_warehouse_rate_type != 0
    <if test="isWillReceive">
      and t2.id is null
    </if>
    group by t1.id -- 避免重复
    order by t1.id desc
  <if test="limit != null">
      <if test="offset != null">
          limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
          limit ${limit}
      </if>
  </if>
  </select>

  <select id="selectShouldReceiveGoodsCount" resultType="java.lang.Long">
      select  count(*) from  (
      select
          1
      from iscm_wms_leave_warehouse_store_receive t1 left join iscm_store_confirm_order t2
                                                     on t1.company_code = t2.company_code
                                                     and t1.store_code = t2.store_code
                                                     and t1.apply_date = t2.apply_date
                                                     and t1.goods_no = t2.goods_no
                                                     and t1.sap_order_no = t2.dn_order_no
      where t1.company_code = #{companyCode} and t1.apply_date = #{applyDate}
      and t1.wms_leave_warehouse_rate_type != 0
      <if test="isWillReceive">
          and t2.id is null
      </if>
      group by t1.id -- 避免重复
      order by t1.id desc
      ) t3
  </select>

</mapper>
