<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapperTidb.extend.IscmSapTransferOrderExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entityTidb.IscmSapTransferOrder">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="platform_name" jdbcType="VARCHAR" property="platformName" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="store_name" jdbcType="VARCHAR" property="storeName" />
    <result column="apply_date" jdbcType="DATE" property="applyDate" />
    <result column="generate_time" jdbcType="TIMESTAMP" property="generateTime" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="specifications" jdbcType="VARCHAR" property="specifications" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="sap_order_no" jdbcType="VARCHAR" property="sapOrderNo" />
    <result column="order_type" jdbcType="TINYINT" property="orderType" />
    <result column="apply_quantity" jdbcType="DECIMAL" property="applyQuantity" />
    <result column="sap_transfer_quantity" jdbcType="DECIMAL" property="sapTransferQuantity" />
    <result column="sap_transfer_rate" jdbcType="DECIMAL" property="sapTransferRate" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Base_Column_List">
    id, platform_name, company_name, company_code, store_code, store_name, apply_date, 
    generate_time, goods_no, goods_name, specifications, manufacturer, sap_order_no, 
    order_type, apply_quantity, sap_transfer_quantity, sap_transfer_rate, `status`, gmt_create, 
    gmt_update, extend, version, created_by, created_name, updated_by, updated_name
  </sql>

  <select id="selectStartAndFinishTime" resultType="com.cowell.iscm.service.dto.pushReplenishmentMonitor.SapTransferOrderDTO">
    select
        DATE_FORMAT(min(generate_time),'%Y-%m-%d %H:%i:%s') as startTime,
        DATE_FORMAT(max(generate_time),'%Y-%m-%d %H:%i:%s') as finishTime
    from iscm_sap_transfer_order where company_code = #{companyCode} and apply_date = #{applyDate}
  </select>


  <select id="countTransferStoreQuantity" resultType="java.lang.Integer">
    select
    count(distinct store_code)
    from iscm_sap_transfer_order
    where company_code = #{companyCode} and apply_date = #{applyDate}
    and sap_transfer_rate &gt; 0
    <if test="orderTypes != null and orderTypes.size > 0">
      and order_type in
      <foreach collection="orderTypes" item="orderType" index="index" open="(" close=")" separator=",">
        #{orderType}
      </foreach>
    </if>
  </select>

  <select id="countTransferOrderQuantity" resultType="java.lang.Integer">
    select
    count(distinct sap_order_no)
    from iscm_sap_transfer_order
    where company_code = #{companyCode} and apply_date = #{applyDate}
    and sap_transfer_rate &gt; 0
    <if test="orderTypes != null and orderTypes.size > 0">
      and order_type in
      <foreach collection="orderTypes" item="orderType" index="index" open="(" close=")" separator=",">
        #{orderType}
      </foreach>
    </if>
  </select>

  <select id="countTransferGoodsQuantity" resultType="java.lang.Integer">
    select
    count(*)
    from iscm_sap_transfer_order
    where company_code = #{companyCode} and apply_date = #{applyDate}
    and sap_transfer_rate &gt;= 0
    <if test="orderTypes != null and orderTypes.size > 0">
      and order_type in
      <foreach collection="orderTypes" item="orderType" index="index" open="(" close=")" separator=",">
        #{orderType}
      </foreach>
    </if>
  </select>

  <select id="countTransferAllSatisfiedGoodsQuantity" resultType="java.lang.Integer">
    select
    count(*)
    from iscm_sap_transfer_order
    where company_code = #{companyCode} and apply_date = #{applyDate}
    and sap_transfer_rate = 1
    <if test="orderTypes != null and orderTypes.size > 0">
      and order_type in
      <foreach collection="orderTypes" item="orderType" index="index" open="(" close=")" separator=",">
        #{orderType}
      </foreach>
    </if>
  </select>


  <select id="countUrgentReceiveStoreQuantity" resultType="java.lang.Integer">
    select
    count(distinct store_code)
    from iscm_sap_transfer_order
    where company_code = #{companyCode} and apply_date = #{applyDate}
    <if test="orderTypes != null and orderTypes.size > 0">
      and order_type in
      <foreach collection="orderTypes" item="orderType" index="index" open="(" close=")" separator=",">
        #{orderType}
      </foreach>
    </if>
  </select>

  <select id="countTransferPartiallySatisfiedGoodsQuantity1" resultType="java.lang.Integer">
    select
    count(*)
    from iscm_sap_transfer_order
    where company_code = #{companyCode} and apply_date = #{applyDate}
    and sap_transfer_rate &gt;= 0.85 and sap_transfer_rate &lt; 1
    <if test="orderTypes != null and orderTypes.size > 0">
      and order_type in
      <foreach collection="orderTypes" item="orderType" index="index" open="(" close=")" separator=",">
        #{orderType}
      </foreach>
    </if>
  </select>


  <select id="countTransferPartiallySatisfiedGoodsQuantity2" resultType="java.lang.Integer">
    select
    count(*)
    from iscm_sap_transfer_order
    where company_code = #{companyCode} and apply_date = #{applyDate}
    and sap_transfer_rate &gt; 0 and sap_transfer_rate &lt;= 0.85
    <if test="orderTypes != null and orderTypes.size > 0">
      and order_type in
      <foreach collection="orderTypes" item="orderType" index="index" open="(" close=")" separator=",">
        #{orderType}
      </foreach>
    </if>
  </select>

  <select id="countTransferNoSatisfiedGoodsQuantity" resultType="java.lang.Integer">
    select
    count(*)
    from iscm_sap_transfer_order
    where company_code = #{companyCode} and apply_date = #{applyDate}
    and sap_transfer_rate = 0
    <if test="orderTypes != null and orderTypes.size > 0">
      and order_type in
      <foreach collection="orderTypes" item="orderType" index="index" open="(" close=")" separator=",">
        #{orderType}
      </foreach>
    </if>
  </select>

  <select id="countGroupByRateType" resultType="com.cowell.iscm.service.dto.pushReplenishmentMonitor.RateCountDTO">
    select
      sap_transfer_rate_type rateType, count(*) count
    from iscm_sap_transfer_order
    where company_code = #{companyCode} and apply_date = #{applyDate}
    <if test="orderTypes != null and orderTypes.size > 0">
      and order_type in
      <foreach collection="orderTypes" item="orderType" index="index" open="(" close=")" separator=",">
        #{orderType}
      </foreach>
    </if>
    group by sap_transfer_rate_type
  </select>

  <select id="selectStoreMinTime" resultType="com.cowell.iscm.service.dto.pushReplenishmentMonitor.StoreTimeDTO">
    select
      store_code as storeCode,
      min(generate_time) as storeTime,
      platform_name as platformName,
      company_name as companyName,
      store_name as storeName,
      apply_date as applyDate
    from iscm_sap_transfer_order
    where company_code = #{companyCode}
      and apply_date = #{applyDate}
    <if test="orderTypes != null and orderTypes.size > 0">
      and order_type in
      <foreach collection="orderTypes" item="orderType" index="index" open="(" close=")" separator=",">
        #{orderType}
      </foreach>
    </if>
    group by store_code
    order by id desc
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>

  <select id="selectStoreMinTimeCount" resultType="java.lang.Long">
    select count(*) from (
    select
      store_code as storeCode,
      min(generate_time) as storeTime,
      platform_name as platformName,
      company_name as companyName,
      store_name as storeName,
      apply_date as applyDate
    from iscm_sap_transfer_order
    where company_code = #{companyCode}
    and apply_date = #{applyDate}
    <if test="orderTypes != null and orderTypes.size > 0">
      and order_type in
      <foreach collection="orderTypes" item="orderType" index="index" open="(" close=")" separator=",">
        #{orderType}
      </foreach>
    </if>
    group by store_code) t1
  </select>

  <select id="countNoSatisfiedStoreQuantity" resultType="java.lang.Integer">
    select count(*) from (
      select
        store_code,
        count(*) total,
        count(if(sap_transfer_rate_type != 0, 1, null)) satisfied
      from iscm_sap_transfer_order
      where company_code = #{companyCode}
      and apply_date = #{applyDate}
      <if test="orderTypes != null and orderTypes.size > 0">
        and order_type in
        <foreach collection="orderTypes" item="orderType" index="index" open="(" close=")" separator=",">
          #{orderType}
        </foreach>
      </if>
      group by store_code
    ) t where satisfied = 0
  </select>

  <select id="selectDiffCount" resultType="java.lang.Long">
    select count(*) from (<include refid="SQL_SelectDiff" />) t
  </select>
  <select id="selectDiffList" resultType="com.cowell.iscm.entityTidb.IscmBdpIntelligentDistribute">
    <include refid="SQL_SelectDiff" />
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <sql id="SQL_SelectDiff">
    <!--
    select
      bdp.platform_name as platformName,
      bdp.company_code as companyCode,
      bdp.company_name as companyName,
      bdp.store_code as storeCode,
      bdp.store_name as storeName,
      bdp.apply_date as applyDate,
      bdp.generate_time as generateTime,
      bdp.goods_no as goodsNo,
      bdp.goods_name as goodsName,
      bdp.specifications,
      bdp.manufacturer,
      bdp.bdp_order_no as bdpOrderNo
    from iscm_bdp_intelligent_distribute bdp
    where concat(bdp.goods_no,bdp.store_code) not in(
      select concat(goods_no,store_code) from iscm_sap_transfer_order sap
      where sap.company_code = #{companyCode}
        and sap.apply_date = #{applyDate}
        <if test="orderTypes != null and orderTypes.size > 0">
          and sap.order_type in
          <foreach collection="orderTypes" item="orderType" index="index" open="(" close=")" separator=",">
            #{orderType}
          </foreach>
        </if>
    )
    and bdp.company_code = #{companyCode}
    and bdp.apply_date = #{applyDate}
    and bdp.distribute_rate_type != 0
    order by bdp.id desc
    -->

    select
      bdp.platform_name as platformName,
      bdp.company_code as companyCode,
      bdp.company_name as companyName,
      bdp.store_code as storeCode,
      bdp.store_name as storeName,
      bdp.apply_date as applyDate,
      bdp.generate_time as generateTime,
      bdp.goods_no as goodsNo,
      bdp.goods_name as goodsName,
      bdp.specifications,
      bdp.manufacturer,
      bdp.bdp_order_no as bdpOrderNo
    from iscm_bdp_intelligent_distribute bdp
    left join iscm_sap_transfer_order sap
    on sap.company_code = bdp.company_code and sap.apply_date = bdp.apply_date and sap.goods_no = bdp.goods_no and sap.store_code = bdp.store_code
    <if test="orderTypes != null and orderTypes.size > 0">
      and sap.order_type in
      <foreach collection="orderTypes" item="orderType" index="index" open="(" close=")" separator=",">
        #{orderType}
      </foreach>
    </if>
    where bdp.distribute_rate_type != 0
      and bdp.company_code = #{companyCode}
      and bdp.apply_date = #{applyDate}
      and sap.id is null
    group by bdp.id -- 避免重复
    order by bdp.id desc
  </sql>

</mapper>
