<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapperTidb.extend.IscmStoreDistributeApplyOrderExtendMapper">
    <resultMap id="BaseResultMap" type="com.cowell.iscm.entityTidb.IscmStoreDistributeApplyOrder">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="platform_name" jdbcType="VARCHAR" property="platformName" />
        <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
        <result column="company_name" jdbcType="VARCHAR" property="companyName" />
        <result column="store_name" jdbcType="VARCHAR" property="storeName" />
        <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
        <result column="apply_date" jdbcType="DATE" property="applyDate" />
        <result column="generate_time" jdbcType="TIMESTAMP" property="generateTime" />
        <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
        <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
        <result column="specifications" jdbcType="VARCHAR" property="specifications" />
        <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
        <result column="pos_apply_order_no" jdbcType="VARCHAR" property="posApplyOrderNo" />
        <result column="apply_quantity" jdbcType="DECIMAL" property="applyQuantity" />
        <result column="apporve_quantity" jdbcType="DECIMAL" property="apporveQuantity" />
        <result column="modify_result" jdbcType="VARCHAR" property="modifyResult" />
        <result column="pos_average_daily_sales" jdbcType="DECIMAL" property="posAverageDailySales" />
        <result column="bdp_average_daily_sales" jdbcType="DECIMAL" property="bdpAverageDailySales" />
        <result column="store_then_month_sales" jdbcType="DECIMAL" property="storeThenMonthSales" />
        <result column="store_then_stock_quantity" jdbcType="DECIMAL" property="storeThenStockQuantity" />
        <result column="store_upper_limit" jdbcType="DECIMAL" property="storeUpperLimit" />
        <result column="store_lower_limit" jdbcType="DECIMAL" property="storeLowerLimit" />
        <result column="synthesize_average_daily_sales" jdbcType="DECIMAL" property="synthesizeAverageDailySales" />
        <result column="store_stock_quantity" jdbcType="DECIMAL" property="storeStockQuantity" />
        <result column="transit_quantity" jdbcType="DECIMAL" property="transitQuantity" />
        <result column="lower_display_quantity" jdbcType="DECIMAL" property="lowerDisplayQuantity" />
        <result column="order_type" jdbcType="TINYINT" property="orderType" />
        <result column="order_status" jdbcType="TINYINT" property="orderStatus" />
        <result column="status" jdbcType="TINYINT" property="status" />
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
        <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
        <result column="extend" jdbcType="VARCHAR" property="extend" />
        <result column="version" jdbcType="INTEGER" property="version" />
        <result column="created_by" jdbcType="BIGINT" property="createdBy" />
        <result column="created_name" jdbcType="VARCHAR" property="createdName" />
        <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
        <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
    </resultMap>
  <sql id="Base_Column_List">
    id, platform_name, company_code, company_name, store_name, store_code, apply_date, 
    generate_time, goods_no, goods_name, specifications, manufacturer, pos_apply_order_no, 
    apply_quantity, apporve_quantity, modify_result, pos_average_daily_sales, bdp_average_daily_sales, 
    store_then_month_sales, store_then_stock_quantity, store_upper_limit, store_lower_limit, 
    synthesize_average_daily_sales, store_stock_quantity, transit_quantity, lower_display_quantity, 
    order_type, order_status, `status`, gmt_create, gmt_update, extend, version, created_by, 
    created_name, updated_by, updated_name
  </sql>

    <select id="countPosDataByOrderType"
          resultType="com.cowell.iscm.service.dto.pushReplenishmentMonitor.PosAutoApplyDataDTO">
        select count(distinct store_code) as applyStoreQuantity,
               count(distinct pos_apply_order_no) as genOrderQuantity,
               count(goods_no) as goodsQuantity,
               date_format(min(generate_time),'%Y-%m-%d %H:%i:%s') as startTime,
               date_format(max(generate_time),'%Y-%m-%d %H:%i:%s') as finishTime
        from iscm_store_distribute_apply_order
        where company_code = #{companyCode}
        and apply_date = #{applyDate}
        <if test="orderType != null">
            and order_type = #{orderType}
        </if>
        <if test="orderStatus != null">
            and order_status = #{orderStatus}
        </if>
  </select>
  <select id="countForecastQuantity" resultType="java.lang.Integer">
    select count(*)
    from iscm_store_distribute_apply_order
    where company_code = #{companyCode}
    and apply_date = #{applyDate}
    <if test="orderType != null">
      and order_type = #{orderType}
    </if>
    <if test="orderStatus != null">
      and order_status = #{orderStatus}
    </if>
    and bdp_average_daily_sales &gt; 0
  </select>
    <select id="countDataGroupByOrderType"
            resultType="com.cowell.iscm.service.dto.pushReplenishmentMonitor.CountDataDTO">
        select order_type as orderType,
               count(distinct store_code) as storeQuantity,
               count(goods_no) as goodsQuantity,
               count(distinct modify_result) as modifyQuantity,
               count(distinct pos_apply_order_no) as orderQuantity,
               min(generate_time) as gmtCreateMin,
               max(generate_time) as gmtCreateMax
        from iscm_store_distribute_apply_order
        where company_code = #{companyCode}
          and apply_date = #{applyDate}
        <if test="orderType != null">
            and order_type = #{orderType}
        </if>
        <if test="orderStatus != null">
            and order_status = #{orderStatus}
        </if>
        group by order_type
    </select>
    <select id="countDataGroupByModifyResult"
            resultType="com.cowell.iscm.service.dto.pushReplenishmentMonitor.CountDataDTO">
        select
            modify_result as modifyResult,
            count(*) as goodsQuantity
        from iscm_store_distribute_apply_order
        where company_code = #{companyCode}
        and apply_date = #{applyDate}
        <if test="orderType != null">
            and order_type = #{orderType}
        </if>
        <if test="orderStatus != null">
            and order_status = #{orderStatus}
        </if>
        group by modify_result
    </select>

    <select id="selectStoreMinTime" resultType="com.cowell.iscm.service.dto.pushReplenishmentMonitor.StoreTimeDTO">
        select
            store_code as storeCode,
            min(generate_time) as storeTime,
            platform_name as platformName,
            company_name as companyName,
            store_name as storeName,
            apply_date as applyDate,
            group_concat(distinct order_type) as orderTypes,
            group_concat(distinct modify_result) as results
        from iscm_store_distribute_apply_order
        where company_code = #{companyCode}
        and apply_date = #{applyDate}
        <if test="orderTypes != null and orderTypes.size > 0">
            and order_type in
            <foreach collection="orderTypes" item="orderType" index="index" open="(" close=")" separator=",">
                #{orderType}
            </foreach>
        </if>
        <if test="orderStatus != null">
            and order_status = #{orderStatus}
        </if>
        group by store_code
    </select>

    <select id="selectStoreMinTimeByComCodeAndType"
            resultType="com.cowell.iscm.service.dto.pushReplenishmentMonitor.MonitorStoreDTO">
        select platform_name as platformName, company_code as companyCode, company_name as companyName, store_code as storeCode, store_name as storeName, GROUP_CONCAT(DISTINCT order_type) as orderTypes, DATE_FORMAT(apply_date,'%Y-%m-%d') as applyDate, DATE_FORMAT(min(generate_time),'%Y-%m-%d  %H:%i:%s') as dealTime
        from iscm_store_distribute_apply_order
        where company_code = #{companyCode}
        and apply_date = #{applyDate}
        <if test="orderType != null">
            and order_type = #{orderType}
        </if>
        <if test="orderStatus != null">
            and order_status = #{orderStatus}
        </if>
        group by store_code
        order by id desc
        limit #{offset}, #{limit}
    </select>

    <select id="countStoreMinTimeByComCodeAndType" resultType="java.lang.Integer">
        select count(distinct store_code)
        from iscm_store_distribute_apply_order
        where company_code = #{companyCode}
        and apply_date = #{applyDate}
        <if test="orderType != null">
            and order_type = #{orderType}
        </if>
        <if test="orderStatus != null">
            and order_status = #{orderStatus}
        </if>
    </select>

    <select id="countPosDataGroupByCompany"
            resultType="com.cowell.iscm.service.dto.pushReplenishmentMonitor.PosAutoApplyDataDTO">
        select
            count(distinct store_code) as applyStoreQuantity,
            count(distinct pos_apply_order_no) as genOrderQuantity,
            count(goods_no) as goodsQuantity,
            min(generate_time) as startTime,
            max(generate_time) as finishTime,
            company_code as companyCode
        from iscm_store_distribute_apply_order
        where company_code in
        <foreach collection="companyCodes" item="companyCode" index="index" open="(" close=")" separator="," >
            #{companyCode}
        </foreach>
        and apply_date = #{applyDate}
        <if test="orderType != null">
            and order_type = #{orderType}
        </if>
        <if test="orderStatus != null">
            and order_status = #{orderStatus}
        </if>
        group by company_code
    </select>
    <select id="countForecastQuantityGroupByCompany"
            resultType="com.cowell.iscm.service.dto.pushReplenishmentMonitor.StoreQuantityDTO">
        select company_code as companyCode, count(*) as quantity
        from iscm_store_distribute_apply_order
        where company_code in
        <foreach collection="companyCodes" item="companyCode" index="index" open="(" close=")" separator="," >
            #{companyCode}
        </foreach>
        and apply_date = #{applyDate}
        <if test="orderType != null">
            and order_type = #{orderType}
        </if>
        <if test="orderStatus != null">
            and order_status = #{orderStatus}
        </if>
        and bdp_average_daily_sales &gt; 0
        group by company_code
    </select>

  <select id="countRefuseStoreQuantity" resultType="java.lang.Integer">
    select count(*) from (
    select
    store_code,
    count(*) total,
    count(if(modify_result != 4, 1, null)) accept
    from iscm_store_distribute_apply_order
    where company_code = #{companyCode}
    and apply_date = #{applyDate}
    <if test="orderTypes != null and orderTypes.size > 0">
      and order_type in
      <foreach collection="orderTypes" item="orderType" index="index" open="(" close=")" separator=",">
        #{orderType}
      </foreach>
    </if>
    <if test="orderStatus != null">
      and order_status = #{orderStatus}
    </if>
    group by store_code
    ) t where accept = 0
  </select>

  <select id="countStoreQuantityByOrderTypes" resultType="java.lang.Integer">
    select count(distinct store_code) as storeQuantity
    from iscm_store_distribute_apply_order
    where company_code = #{companyCode}
    and apply_date = #{applyDate}
    <if test="orderTypes != null and orderTypes.size > 0">
      and order_type in
      <foreach collection="orderTypes" item="orderType" index="index" open="(" close=")" separator=",">
        #{orderType}
      </foreach>
    </if>
    <if test="orderStatus != null">
      and order_status = #{orderStatus}
    </if>
  </select>

  <select id="selectStoreMinTimeByOrderTypes"
          resultType="com.cowell.iscm.service.dto.pushReplenishmentMonitor.MonitorStoreDTO">
    select platform_name as platformName, company_code as companyCode, company_name as companyName, store_code as storeCode, store_name as storeName, GROUP_CONCAT(DISTINCT order_type) as orderTypes, DATE_FORMAT(apply_date,'%Y-%m-%d') as applyDate, DATE_FORMAT(min(generate_time),'%Y-%m-%d  %H:%i:%s') as dealTime
    from iscm_store_distribute_apply_order
    where company_code = #{companyCode}
    and apply_date = #{applyDate}
    <if test="orderTypes != null and orderTypes.size > 0">
      and order_type in
      <foreach collection="orderTypes" item="orderType" index="index" open="(" close=")" separator=",">
        #{orderType}
      </foreach>
    </if>
    <if test="orderStatus != null">
      and order_status = #{orderStatus}
    </if>
    group by store_code
    order by id desc
    limit #{offset}, #{limit}
  </select>

  <select id="countStoreMinTimeByOrderTypes" resultType="java.lang.Integer">
    select count(distinct store_code)
    from iscm_store_distribute_apply_order
    where company_code = #{companyCode}
    and apply_date = #{applyDate}
    <if test="orderTypes != null and orderTypes.size > 0">
      and order_type in
      <foreach collection="orderTypes" item="orderType" index="index" open="(" close=")" separator=",">
        #{orderType}
      </foreach>
    </if>
    <if test="orderStatus != null">
      and order_status = #{orderStatus}
    </if>
  </select>

</mapper>
