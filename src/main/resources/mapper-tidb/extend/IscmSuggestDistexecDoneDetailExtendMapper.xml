<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapperTidb.extend.IscmSuggestDistexecDoneDetailExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entityTidb.IscmSuggestDistexecDoneDetail">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="allot_date" jdbcType="TIMESTAMP" property="allotDate" />
    <result column="allot_month" jdbcType="INTEGER" property="allotMonth" />
    <result column="register_no" jdbcType="VARCHAR" property="registerNo" />
    <result column="register_month" jdbcType="INTEGER" property="registerMonth" />
    <result column="allot_type" jdbcType="TINYINT" property="allotType" />
    <result column="pos_allot_no" jdbcType="VARCHAR" property="posAllotNo" />
    <result column="platform_org_id" jdbcType="BIGINT" property="platformOrgId" />
    <result column="platform_org_name" jdbcType="VARCHAR" property="platformOrgName" />
    <result column="out_company_id" jdbcType="BIGINT" property="outCompanyId" />
    <result column="out_company_code" jdbcType="VARCHAR" property="outCompanyCode" />
    <result column="out_company_name" jdbcType="VARCHAR" property="outCompanyName" />
    <result column="in_company_id" jdbcType="BIGINT" property="inCompanyId" />
    <result column="in_company_code" jdbcType="VARCHAR" property="inCompanyCode" />
    <result column="in_company_name" jdbcType="VARCHAR" property="inCompanyName" />
    <result column="out_store_id" jdbcType="BIGINT" property="outStoreId" />
    <result column="out_store_code" jdbcType="VARCHAR" property="outStoreCode" />
    <result column="out_store_name" jdbcType="VARCHAR" property="outStoreName" />
    <result column="out_store_attr" jdbcType="VARCHAR" property="outStoreAttr" />
    <result column="out_store_sales_level" jdbcType="VARCHAR" property="outStoreSalesLevel" />
    <result column="in_store_id" jdbcType="BIGINT" property="inStoreId" />
    <result column="in_store_code" jdbcType="VARCHAR" property="inStoreCode" />
    <result column="in_store_name" jdbcType="VARCHAR" property="inStoreName" />
    <result column="in_store_attr" jdbcType="VARCHAR" property="inStoreAttr" />
    <result column="in_store_sales_level" jdbcType="VARCHAR" property="inStoreSalesLevel" />
    <result column="allot_group_code" jdbcType="VARCHAR" property="allotGroupCode" />
    <result column="allot_group_name" jdbcType="VARCHAR" property="allotGroupName" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="goods_desc" jdbcType="VARCHAR" property="goodsDesc" />
    <result column="goods_common_name" jdbcType="VARCHAR" property="goodsCommonName" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="real_allot_quantity" jdbcType="DECIMAL" property="realAllotQuantity" />
    <result column="real_cost_amount" jdbcType="DECIMAL" property="realCostAmount" />
    <result column="allot_approve_time" jdbcType="TIMESTAMP" property="allotApproveTime" />
    <result column="in_approve_status" jdbcType="TINYINT" property="inApproveStatus" />
    <result column="in_approve_time" jdbcType="TIMESTAMP" property="inApproveTime" />
    <result column="in_allot_quantity" jdbcType="DECIMAL" property="inAllotQuantity" />
    <result column="in_allot_cost_amount" jdbcType="DECIMAL" property="inAllotCostAmount" />
    <result column="in_stock_time" jdbcType="TIMESTAMP" property="inStockTime" />
    <result column="in_store_sales_7" jdbcType="DECIMAL" property="inStoreSales7" />
    <result column="in_store_puramount_7" jdbcType="DECIMAL" property="inStorePuramount7" />
    <result column="in_store_sales_14" jdbcType="DECIMAL" property="inStoreSales14" />
    <result column="in_store_puramount_14" jdbcType="DECIMAL" property="inStorePuramount14" />
    <result column="in_store_sales_30" jdbcType="DECIMAL" property="inStoreSales30" />
    <result column="in_store_puramount_30" jdbcType="DECIMAL" property="inStorePuramount30" />
    <result column="in_store_sales_60" jdbcType="DECIMAL" property="inStoreSales60" />
    <result column="in_store_puramount_60" jdbcType="DECIMAL" property="inStorePuramount60" />
    <result column="in_store_sales_90" jdbcType="DECIMAL" property="inStoreSales90" />
    <result column="in_store_puramount_90" jdbcType="DECIMAL" property="inStorePuramount90" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Base_Column_List">
    id, allot_date, allot_month, register_no, register_month, allot_type, pos_allot_no, 
    platform_org_id, platform_org_name, out_company_id, out_company_code, out_company_name, 
    in_company_id, in_company_code, in_company_name, out_store_id, out_store_code, out_store_name, 
    out_store_attr, out_store_sales_level, in_store_id, in_store_code, in_store_name,
    in_store_attr, in_store_sales_level, allot_group_code, allot_group_name, goods_no,
    goods_desc, goods_common_name, manufacturer, unit, real_allot_quantity, real_cost_amount,
    allot_approve_time, in_approve_status, in_approve_time, in_allot_quantity, in_allot_cost_amount,
    in_stock_time, in_store_sales_7, in_store_puramount_7, in_store_sales_14, in_store_puramount_14,
    in_store_sales_30, in_store_puramount_30, in_store_sales_60, in_store_puramount_60,
    in_store_sales_90, in_store_puramount_90, `status`, gmt_create, gmt_update, extend,
    version, created_by, created_name, updated_by, updated_name
  </sql>
  <select id="selectAllotExecutedByOutStoreOrgIdsAndAllotTypes"
          resultType="com.cowell.iscm.entityTidb.IscmSuggestDistexecDoneDetail">
    select out_store_id as outStoreId, in_store_id as inStoreId, real_cost_amount as realCostAmount, pos_allot_no as posAllotNo
    from iscm_suggest_distexec_done_detail
    where out_store_id in
    <foreach collection="outStoreOrgIds" item="storeOrgId" index="index" open="(" close=")" separator="," >
      #{storeOrgId}
    </foreach>
    and in_store_id in
    <foreach collection="inStoreOrgIds" item="storeOrgId" index="index" open="(" close=")" separator="," >
      #{storeOrgId}
    </foreach>
    and register_month = #{registerMonth}
    and allot_type in
    <foreach collection="allotTypes" item="allotType" index="index" open="(" close=")" separator="," >
      #{allotType}
    </foreach>
    order by id desc
    limit ${start}, ${pageSize}

  </select>
</mapper>
