<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapperTidb.extend.IscmAutoRegisterExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entityTidb.IscmAutoRegister">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="data_type" jdbcType="TINYINT" property="dataType" />
    <result column="register_type" jdbcType="TINYINT" property="registerType" />
    <result column="register_order_no" jdbcType="VARCHAR" property="registerOrderNo" />
    <result column="company_bdp_code" jdbcType="VARCHAR" property="companyBdpCode" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="register_quantity" jdbcType="DECIMAL" property="registerQuantity" />
    <result column="non_validity_stock_quantity" jdbcType="DECIMAL" property="nonValidityStockQuantity" />
    <result column="stock_upper_limit" jdbcType="DECIMAL" property="stockUpperLimit" />
    <result column="stock_lower_limit" jdbcType="DECIMAL" property="stockLowerLimit" />
    <result column="no_tax_inventory_cost_amount" jdbcType="DECIMAL" property="noTaxInventoryCostAmount" />
    <result column="non_sale_days" jdbcType="INTEGER" property="nonSaleDays" />
    <result column="synthesize_average_daily_sales" jdbcType="DECIMAL" property="synthesizeAverageDailySales" />
    <result column="thirty_sales_quantity" jdbcType="DECIMAL" property="thirtySalesQuantity" />
    <result column="thirty_sales_count" jdbcType="INTEGER" property="thirtySalesCount" />
    <result column="stock_quantity" jdbcType="DECIMAL" property="stockQuantity" />
    <result column="min_display_quantity" jdbcType="DECIMAL" property="minDisplayQuantity" />
    <result column="expect_sale_days" jdbcType="DECIMAL" property="expectSaleDays" />
    <result column="deal_status" jdbcType="TINYINT" property="dealStatus" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Base_Column_List">
    id, data_type, register_type, register_order_no, company_bdp_code, store_code, goods_no, 
    batch_no, manufacturer, register_quantity, non_validity_stock_quantity, stock_upper_limit, 
    stock_lower_limit, no_tax_inventory_cost_amount, non_sale_days, synthesize_average_daily_sales, 
    thirty_sales_quantity, thirty_sales_count, stock_quantity, min_display_quantity, 
    expect_sale_days, deal_status, status, gmt_create, gmt_update, extend, version, created_by, 
    created_name, updated_by, updated_name
  </sql>
  <select id="findBdpCodes" resultType="java.lang.String">
    select distinct company_bdp_code from iscm_auto_register
  </select>
  <select id="findStoreNosByBdpCode" resultType="java.lang.String">
    select distinct store_code from iscm_auto_register where company_bdp_code = #{bdpCode}
  </select>
</mapper>
