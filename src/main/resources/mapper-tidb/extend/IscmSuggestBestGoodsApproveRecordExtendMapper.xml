<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapperTidb.extend.IscmSuggestBestGoodsApproveRecordExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entityTidb.IscmSuggestBestGoodsApproveRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="suggest_date" jdbcType="DATE" property="suggestDate" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="push_hd_status" jdbcType="TINYINT" property="pushHdStatus" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="specifications" jdbcType="VARCHAR" property="specifications" />
    <result column="dosage_form" jdbcType="VARCHAR" property="dosageForm" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="component" jdbcType="VARCHAR" property="component" />
    <result column="thirty_sales_quantity" jdbcType="DECIMAL" property="thirtySalesQuantity" />
    <result column="refer_retail_price" jdbcType="DECIMAL" property="referRetailPrice" />
    <result column="refer_gross_profit" jdbcType="DECIMAL" property="referGrossProfit" />
    <result column="sales_rank" jdbcType="INTEGER" property="salesRank" />
    <result column="push_level" jdbcType="VARCHAR" property="pushLevel" />
    <result column="medicine_people" jdbcType="VARCHAR" property="medicinePeople" />
    <result column="otc_able" jdbcType="TINYINT" property="otcAble" />
    <result column="rx_able" jdbcType="TINYINT" property="rxAble" />
    <result column="yb_able" jdbcType="TINYINT" property="ybAble" />
    <result column="sensitive_able" jdbcType="TINYINT" property="sensitiveAble" />
    <result column="category_id" jdbcType="BIGINT" property="categoryId" />
    <result column="category" jdbcType="VARCHAR" property="category" />
    <result column="promotion_tag" jdbcType="VARCHAR" property="promotionTag" />
    <result column="promotion_name" jdbcType="VARCHAR" property="promotionName" />
    <result column="promotion_way" jdbcType="VARCHAR" property="promotionWay" />
    <result column="threshold_info" jdbcType="VARCHAR" property="thresholdInfo" />
    <result column="fav_info" jdbcType="VARCHAR" property="favInfo" />
    <result column="recommend_reason" jdbcType="VARCHAR" property="recommendReason" />
    <result column="composite_new" jdbcType="TINYINT" property="compositeNew" />
    <result column="indications" jdbcType="VARCHAR" property="indications" />
    <result column="picture_url" jdbcType="VARCHAR" property="pictureUrl" />
    <result column="suggest_type" jdbcType="TINYINT" property="suggestType" />
    <result column="deal_suggest" jdbcType="TINYINT" property="dealSuggest" />
    <result column="complement_qty" jdbcType="INTEGER" property="complementQty" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, suggest_date, company_code, store_code, push_hd_status, goods_no, goods_name, 
    specifications, dosage_form, manufacturer, component, thirty_sales_quantity, refer_retail_price, 
    refer_gross_profit, sales_rank, push_level, medicine_people, otc_able, rx_able, yb_able, 
    sensitive_able, category_id, category, promotion_tag, promotion_name, promotion_way, 
    threshold_info, fav_info, recommend_reason, composite_new, indications, picture_url,
    suggest_type, deal_suggest, complement_qty, `status`, gmt_create, gmt_update, extend,
    version, created_by, created_name, updated_by, updated_name
  </sql>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entityTidb.IscmSuggestBestGoodsApproveRecord" useGeneratedKeys="true">
    insert into iscm_suggest_best_goods_approve_record (suggest_date, company_code, store_code, 
      push_hd_status, goods_no, goods_name, 
      specifications, dosage_form, manufacturer, 
      component, thirty_sales_quantity, refer_retail_price, 
      refer_gross_profit, sales_rank, push_level, 
      medicine_people, otc_able, rx_able, 
      yb_able, sensitive_able, category_id, 
      category, promotion_tag, promotion_name, 
      promotion_way, threshold_info, fav_info, recommend_reason,
      composite_new, indications, picture_url, 
      suggest_type, deal_suggest, complement_qty, 
      gmt_create, created_by,
      created_name, updated_by, updated_name
      )
    values
    <foreach collection="list" item="item" index="index" separator="," >
    (#{item.suggestDate,jdbcType=DATE}, #{item.companyCode,jdbcType=VARCHAR}, #{item.storeCode,jdbcType=VARCHAR},
      #{item.pushHdStatus,jdbcType=TINYINT}, #{item.goodsNo,jdbcType=VARCHAR}, #{item.goodsName,jdbcType=VARCHAR},
      #{item.specifications,jdbcType=VARCHAR}, #{item.dosageForm,jdbcType=VARCHAR}, #{item.manufacturer,jdbcType=VARCHAR},
      #{item.component,jdbcType=VARCHAR}, #{item.thirtySalesQuantity,jdbcType=DECIMAL}, #{item.referRetailPrice,jdbcType=DECIMAL},
      #{item.referGrossProfit,jdbcType=DECIMAL}, #{item.salesRank,jdbcType=INTEGER}, #{item.pushLevel,jdbcType=VARCHAR},
      #{item.medicinePeople,jdbcType=VARCHAR}, #{item.otcAble,jdbcType=TINYINT}, #{item.rxAble,jdbcType=TINYINT},
      #{item.ybAble,jdbcType=TINYINT}, #{item.sensitiveAble,jdbcType=TINYINT}, #{item.categoryId,jdbcType=BIGINT},
      #{item.category,jdbcType=VARCHAR}, #{item.promotionTag,jdbcType=VARCHAR}, #{item.promotionName,jdbcType=VARCHAR},
      #{item.promotionWay,jdbcType=VARCHAR}, #{item.thresholdInfo,jdbcType=VARCHAR}, #{item.favInfo,jdbcType=VARCHAR}, #{item.recommendReason,jdbcType=VARCHAR},
      #{item.compositeNew,jdbcType=TINYINT}, #{item.indications,jdbcType=VARCHAR}, #{item.pictureUrl,jdbcType=VARCHAR},
      #{item.suggestType,jdbcType=TINYINT}, #{item.dealSuggest,jdbcType=TINYINT}, #{item.complementQty,jdbcType=INTEGER},
      #{item.gmtCreate,jdbcType=TIMESTAMP}, #{item.createdBy,jdbcType=BIGINT},
      #{item.createdName,jdbcType=VARCHAR}, #{item.updatedBy,jdbcType=BIGINT}, #{item.updatedName,jdbcType=VARCHAR}
    )</foreach>
  </insert>
</mapper>
