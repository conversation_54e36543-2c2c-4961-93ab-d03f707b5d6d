<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapperTidb.extend.IscmGaojiDcBuhuoResultToSapExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entityTidb.IscmGaojiDcBuhuoResultToSap">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="dt" jdbcType="VARCHAR" property="dt" />
    <result column="werks" jdbcType="VARCHAR" property="werks" />
    <result column="matnr" jdbcType="VARCHAR" property="matnr" />
    <result column="avg_qty" jdbcType="VARCHAR" property="avgQty" />
    <result column="inactive_dc_stock" jdbcType="DECIMAL" property="inactiveDcStock" />
    <result column="inactive_store_stock" jdbcType="DECIMAL" property="inactiveStoreStock" />
    <result column="revise_total_block_stock2" jdbcType="DECIMAL" property="reviseTotalBlockStock2" />
    <result column="weiqingcaigou_7" jdbcType="DECIMAL" property="weiqingcaigou7" />
    <result column="inv_upper" jdbcType="DECIMAL" property="invUpper" />
    <result column="suggest_dhl" jdbcType="DECIMAL" property="suggestDhl" />
    <result column="store_cnts_in_stock" jdbcType="DECIMAL" property="storeCntsInStock" />
    <result column="total_applyqty" jdbcType="DECIMAL" property="totalApplyqty" />
    <result column="max_applyqty" jdbcType="DECIMAL" property="maxApplyqty" />
    <result column="midqty" jdbcType="DECIMAL" property="midqty" />
    <result column="maxqty" jdbcType="DECIMAL" property="maxqty" />
    <result column="stock_to_use_ratio" jdbcType="VARCHAR" property="stockToUseRatio" />
    <result column="lifnr" jdbcType="VARCHAR" property="lifnr" />
    <result column="dhd_b" jdbcType="DECIMAL" property="dhdB" />
    <result column="dc_s" jdbcType="DECIMAL" property="dcS" />
    <result column="dc_l" jdbcType="DECIMAL" property="dcL" />
    <result column="goods_level" jdbcType="VARCHAR" property="goodsLevel" />
    <result column="safety_stock" jdbcType="DECIMAL" property="safetyStock" />
    <result column="sum_r_v" jdbcType="DECIMAL" property="sumRV" />
    <result column="revise_total_block_stock1" jdbcType="DECIMAL" property="reviseTotalBlockStock1" />
    <result column="raw_suggest_dhl" jdbcType="DECIMAL" property="rawSuggestDhl" />
    <result column="znoncxb" jdbcType="VARCHAR" property="znoncxb" />
    <result column="total_dc_stock" jdbcType="VARCHAR" property="totalDcStock" />
    <result column="total_dc_disable_stock" jdbcType="VARCHAR" property="totalDcDisableStock" />
    <result column="total_store_stock" jdbcType="VARCHAR" property="totalStoreStock" />
    <result column="qty_before_30" jdbcType="VARCHAR" property="qtyBefore30" />
    <result column="hb_sale_rate" jdbcType="VARCHAR" property="hbSaleRate" />
    <result column="tb_sale_rate" jdbcType="VARCHAR" property="tbSaleRate" />
    <result column="zdxmds" jdbcType="DECIMAL" property="zdxmds" />
    <result column="dc_sale_days" jdbcType="DECIMAL" property="dcSaleDays" />
    <result column="dc_inv_sale_days" jdbcType="DECIMAL" property="dcInvSaleDays" />
    <result column="store_sale_days" jdbcType="DECIMAL" property="storeSaleDays" />
    <result column="store_inv_sale_days" jdbcType="DECIMAL" property="storeInvSaleDays" />
    <result column="puhuo_stores" jdbcType="DECIMAL" property="puhuoStores" />
    <result column="qty_before_7" jdbcType="DECIMAL" property="qtyBefore7" />
    <result column="avg_qty_before_7" jdbcType="VARCHAR" property="avgQtyBefore7" />
    <result column="qty_before_14" jdbcType="DECIMAL" property="qtyBefore14" />
    <result column="avg_qty_before_14" jdbcType="VARCHAR" property="avgQtyBefore14" />
    <result column="avg_qty_before_30" jdbcType="VARCHAR" property="avgQtyBefore30" />
    <result column="qty_before_90" jdbcType="DECIMAL" property="qtyBefore90" />
    <result column="avg_qty_before_90" jdbcType="VARCHAR" property="avgQtyBefore90" />
    <result column="distqty_before_7" jdbcType="DECIMAL" property="distqtyBefore7" />
    <result column="avg_distqty_before_7" jdbcType="VARCHAR" property="avgDistqtyBefore7" />
    <result column="distqty_before_14" jdbcType="DECIMAL" property="distqtyBefore14" />
    <result column="avg_distqty_before_14" jdbcType="VARCHAR" property="avgDistqtyBefore14" />
    <result column="distqty_30" jdbcType="DECIMAL" property="distqty30" />
    <result column="avg_distqty_before_30" jdbcType="VARCHAR" property="avgDistqtyBefore30" />
    <result column="distqty_before_90" jdbcType="DECIMAL" property="distqtyBefore90" />
    <result column="avg_distqty_before_90" jdbcType="VARCHAR" property="avgDistqtyBefore90" />
    <result column="qty_30_60" jdbcType="DECIMAL" property="qty3060" />
    <result column="qty_60_90" jdbcType="DECIMAL" property="qty6090" />
    <result column="distqty_30_60" jdbcType="DECIMAL" property="distqty3060" />
    <result column="distqty_60_90" jdbcType="DECIMAL" property="distqty6090" />
    <result column="seasonal_factor" jdbcType="VARCHAR" property="seasonalFactor" />
    <result column="jm_store_stock" jdbcType="DECIMAL" property="jmStoreStock" />
    <result column="dc_stock" jdbcType="DECIMAL" property="dcStock" />
    <result column="dc_disable_stock" jdbcType="DECIMAL" property="dcDisableStock" />
    <result column="zc_inactive_dc_stock" jdbcType="DECIMAL" property="zcInactiveDcStock" />
    <result column="dc_inv_upper" jdbcType="DECIMAL" property="dcInvUpper" />
    <result column="dc_block_stock1" jdbcType="DECIMAL" property="dcBlockStock1" />
    <result column="dc_block_stock2" jdbcType="DECIMAL" property="dcBlockStock2" />
    <result column="dist_days" jdbcType="DECIMAL" property="distDays" />
    <result column="sent_time" jdbcType="VARCHAR" property="sentTime" />
    <result column="amount_before_30" jdbcType="DECIMAL" property="amountBefore30" />
    <result column="unsatisfied_delivery_sku" jdbcType="DECIMAL" property="unsatisfiedDeliverySku" />
    <result column="unsatisfied_delivery_cnt" jdbcType="DECIMAL" property="unsatisfiedDeliveryCnt" />
    <result column="weiqingtuicang" jdbcType="VARCHAR" property="weiqingtuicang" />
    <result column="weiqingjisuan" jdbcType="VARCHAR" property="weiqingjisuan" />
    <result column="min_purchase_type" jdbcType="VARCHAR" property="minPurchaseType" />
    <result column="min_purchase_price" jdbcType="DECIMAL" property="minPurchasePrice" />
    <result column="min_purchase_date" jdbcType="VARCHAR" property="minPurchaseDate" />
    <result column="ZDCJE1" jdbcType="DECIMAL" property="zdcje1" />
    <result column="ZDCJE2" jdbcType="DECIMAL" property="zdcje2" />
    <result column="ZDCJE3" jdbcType="DECIMAL" property="zdcje3" />
    <result column="ZDCCB1" jdbcType="DECIMAL" property="zdccb1" />
    <result column="ZDCCB2" jdbcType="DECIMAL" property="zdccb2" />
    <result column="ZMDJE1" jdbcType="DECIMAL" property="zmdje1" />
    <result column="ZMDJE2" jdbcType="DECIMAL" property="zmdje2" />
    <result column="ZMDJE3" jdbcType="DECIMAL" property="zmdje3" />
    <result column="ZMDCB" jdbcType="DECIMAL" property="zmdcb" />
    <result column="ZCDZZTS" jdbcType="DECIMAL" property="zcdzzts" />
    <result column="ZCDKCJEPM" jdbcType="VARCHAR" property="zcdkcjepm" />
    <result column="ZDPT" jdbcType="VARCHAR" property="zdpt" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, dt, werks, matnr, avg_qty, inactive_dc_stock, inactive_store_stock, revise_total_block_stock2, 
    weiqingcaigou_7, inv_upper, suggest_dhl, store_cnts_in_stock, total_applyqty, max_applyqty, 
    midqty, maxqty, stock_to_use_ratio, lifnr, dhd_b, dc_s, dc_l, goods_level, safety_stock, 
    sum_r_v, revise_total_block_stock1, raw_suggest_dhl, znoncxb, total_dc_stock, total_dc_disable_stock, 
    total_store_stock, qty_before_30, hb_sale_rate, tb_sale_rate, zdxmds, dc_sale_days, 
    dc_inv_sale_days, store_sale_days, store_inv_sale_days, puhuo_stores, qty_before_7, 
    avg_qty_before_7, qty_before_14, avg_qty_before_14, avg_qty_before_30, qty_before_90, 
    avg_qty_before_90, distqty_before_7, avg_distqty_before_7, distqty_before_14, avg_distqty_before_14, 
    distqty_30, avg_distqty_before_30, distqty_before_90, avg_distqty_before_90, qty_30_60, 
    qty_60_90, distqty_30_60, distqty_60_90, seasonal_factor, jm_store_stock, dc_stock, 
    dc_disable_stock, zc_inactive_dc_stock, dc_inv_upper, dc_block_stock1, dc_block_stock2, 
    dist_days, sent_time, amount_before_30, unsatisfied_delivery_sku, unsatisfied_delivery_cnt, 
    weiqingtuicang, weiqingjisuan, min_purchase_type, min_purchase_price, min_purchase_date, 
    ZDCJE1, ZDCJE2, ZDCJE3, ZDCCB1, ZDCCB2, ZMDJE1, ZMDJE2, ZMDJE3, ZMDCB, ZCDZZTS, ZCDKCJEPM, 
    ZDPT
  </sql>
  <sql id="Query_Where">
    <where>
      <if test="goodsNos != null and goodsNos.size >0">
        and matnr in
        <foreach collection="goodsNos" item="goodsNo" index="index" open="(" close=")" separator=",">
          #{goodsNo}
        </foreach>
      </if>
      <if test="warehouseCodes != null and warehouseCodes.size >0">
        and werks in
        <foreach collection="warehouseCodes" item="warehouseCode" index="index" open="(" close=")" separator=",">
          #{warehouseCode}
        </foreach>
      </if>
    </where>

  </sql>

  <select id="selectNonPurchaseList" resultType="com.cowell.iscm.service.dto.controlTower.NonPurchaseDTO">
    select werks, matnr, suggest_dhl as suggestDhl, avg_qty as avgQty, goods_level as goodsLevel, inv_upper as invUpper,
    weiqingcaigou_7 as weiqingcaigou7, total_dc_stock + inactive_dc_stock as totalDcStock, total_dc_disable_stock as totalDcDisableStock,
    dc_stock + zc_inactive_dc_stock as dcStock, dc_disable_stock as dcDisableStock, total_store_stock + inactive_store_stock as totalStoreStock, jm_store_stock as jmStoreStock,
    lifnr, qty_before_7 as qtyBefore7, qty_before_14 as qtyBefore14, qty_before_30 as qtyBefore30, hb_sale_rate as hbSaleRate, tb_sale_rate as tbSaleRate, zdxmds
    from iscm_gaoji_dc_buhuo_result_to_sap
    <include refid="Query_Where" />
    order by id desc
    limit #{start,jdbcType=INTEGER}, #{pageSize,jdbcType=INTEGER}
  </select>

  <select id="countNonPurchaseList" resultType="java.lang.Long">
    select count(*)
    from iscm_gaoji_dc_buhuo_result_to_sap
    <include refid="Query_Where" />
  </select>
</mapper>
