<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapperTidb.extend.IscmBdpStoreGoodsInfoExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entityTidb.IscmBdpStoreGoodsInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="company_bdp_code" jdbcType="VARCHAR" property="companyBdpCode" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="storage_days" jdbcType="INTEGER" property="storageDays" />
    <result column="non_sales_days" jdbcType="INTEGER" property="nonSalesDays" />
    <result column="thirty_sales_quantity" jdbcType="DECIMAL" property="thirtySalesQuantity" />
    <result column="thirty_sales_count" jdbcType="INTEGER" property="thirtySalesCount" />
    <result column="min_display_quantity" jdbcType="DECIMAL" property="minDisplayQuantity" />
    <result column="expect_sale_days" jdbcType="DECIMAL" property="expectSaleDays" />
    <result column="register_quantity" jdbcType="DECIMAL" property="registerQuantity" />
    <result column="warehouse_code" jdbcType="VARCHAR" property="warehouseCode" />
    <result column="warehouse_name" jdbcType="VARCHAR" property="warehouseName" />
    <result column="goodsline" jdbcType="VARCHAR" property="goodsline" />
    <result column="pushlevel" jdbcType="VARCHAR" property="pushlevel" />
    <result column="goods_level" jdbcType="INTEGER" property="goodsLevel" />
    <result column="forbid_distribute" jdbcType="VARCHAR" property="forbidDistribute" />
    <result column="forbid_return_warehouse" jdbcType="VARCHAR" property="forbidReturnWarehouse" />
    <result column="forbid_apply" jdbcType="VARCHAR" property="forbidApply" />
    <result column="forbid_allot" jdbcType="VARCHAR" property="forbidAllot" />
    <result column="stock_upper_limit_days" jdbcType="INTEGER" property="stockUpperLimitDays" />
    <result column="stock_lower_limit_days" jdbcType="INTEGER" property="stockLowerLimitDays" />
    <result column="hd_synthesize_average_daily_sales" jdbcType="DECIMAL" property="hdSynthesizeAverageDailySales" />
    <result column="stock_upper_limit" jdbcType="DECIMAL" property="stockUpperLimit" />
    <result column="stock_lower_limit" jdbcType="DECIMAL" property="stockLowerLimit" />
    <result column="bdp_synthesize_average_daily_sales" jdbcType="DECIMAL" property="bdpSynthesizeAverageDailySales" />
    <result column="stock_quantity" jdbcType="DECIMAL" property="stockQuantity" />
    <result column="cost_amount" jdbcType="DECIMAL" property="costAmount" />
    <result column="return_status" jdbcType="TINYINT" property="returnStatus" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, company_code, company_bdp_code, store_code, goods_no, manufacturer, storage_days,
    non_sales_days, thirty_sales_quantity, thirty_sales_count, min_display_quantity,
    expect_sale_days, register_quantity, warehouse_code, warehouse_name, goodsline, pushlevel,
    goods_level, forbid_distribute, forbid_return_warehouse, forbid_apply, forbid_allot,
    stock_upper_limit_days, stock_lower_limit_days, hd_synthesize_average_daily_sales,
    stock_upper_limit, stock_lower_limit, bdp_synthesize_average_daily_sales, stock_quantity,
    cost_amount, return_status
  </sql>
  <select id="findWarehouseRelations" resultType="com.cowell.iscm.service.dto.returnWarehouse.StoreWarehouseRelationDTO">
    select store_code, warehouse_code, warehouse_name from iscm_bdp_store_goods_info
    where company_code in
    <foreach collection="companyCodes" item="companyCode" index="index" open="(" close=")" separator="," >
      #{companyCode}
    </foreach>
  </select>
  <select id="countReturnExample" resultType="java.lang.Long">
    select
    count(*) from (
    select 1 from iscm_bdp_store_goods_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    group by warehouse_code, goods_no ) as a
  </select>
  <select id="selectReturnByExample"
          resultType="com.cowell.iscm.service.dto.returnWarehouse.StoreReturnWarehouseConfirmDTO">
    select
    warehouse_code as warehouseCode, warehouse_name as warehouseName, goods_no as goodsNo, count(distinct company_code) as companyQuantity,
    count(distinct store_code) as storeQuantity, sum(stock_quantity) as totalRegisterQuantity, sum(cost_amount / register_quantity * stock_quantity) as totalCostAmount, group_concat(distinct company_code) as companyCodeStr
    from iscm_bdp_store_goods_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    group by warehouse_code, goods_no
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
</mapper>
