<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapperTidb.extend.IscmSqlOperateExtendMapper">

    <select id="query" parameterType="String" resultType="java.util.LinkedHashMap">
         <![CDATA[
        SELECT *  FROM (${sql}) obj LIMIT 1000
        ]]>
    </select>


    <select id="count" parameterType="String" resultType="java.lang.Integer">
         <![CDATA[
        SELECT count(*) FROM (${sql}) obj
        ]]>
    </select>

</mapper>
