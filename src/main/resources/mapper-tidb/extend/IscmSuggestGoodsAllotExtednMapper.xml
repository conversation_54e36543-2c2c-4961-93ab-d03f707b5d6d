<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapperTidb.extend.IscmSuggestGoodsAllotExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entityTidb.IscmSuggestGoodsAllot">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="business_date" jdbcType="TIMESTAMP" property="businessDate" />
    <result column="allot_no" jdbcType="VARCHAR" property="allotNo" />
    <result column="allot_detail_no" jdbcType="VARCHAR" property="allotDetailNo" />
    <result column="register_no" jdbcType="VARCHAR" property="registerNo" />
    <result column="allot_type" jdbcType="TINYINT" property="allotType" />
    <result column="register_source" jdbcType="TINYINT" property="registerSource" />
    <result column="out_company_code" jdbcType="VARCHAR" property="outCompanyCode" />
    <result column="in_company_code" jdbcType="VARCHAR" property="inCompanyCode" />
    <result column="out_store_code" jdbcType="VARCHAR" property="outStoreCode" />
    <result column="in_store_code" jdbcType="VARCHAR" property="inStoreCode" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
    <result column="suggest_allot_quantity" jdbcType="DECIMAL" property="suggestAllotQuantity" />
    <result column="real_allot_quantity" jdbcType="DECIMAL" property="realAllotQuantity" />
    <result column="expect_sale_days" jdbcType="DECIMAL" property="expectSaleDays" />
    <result column="approve_status" jdbcType="TINYINT" property="approveStatus" />
    <result column="approve_by" jdbcType="BIGINT" property="approveBy" />
    <result column="approve_name" jdbcType="VARCHAR" property="approveName" />
    <result column="approve_time" jdbcType="TIMESTAMP" property="approveTime" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, business_date, allot_no, allot_detail_no, register_no, allot_type, register_source, 
    out_company_code, in_company_code, out_store_code, in_store_code, goods_no, batch_no, 
    suggest_allot_quantity, real_allot_quantity, expect_sale_days, approve_status, approve_by, 
    approve_name, approve_time, status, gmt_create, gmt_update, extend, version, created_by, 
    created_name, updated_by, updated_name
  </sql>
  <update id="updateApproveStatusByIds">
    update iscm_suggest_goods_allot set approve_status = #{apporveStatus} , approve_by= 0, approve_name='自动', approve_time = #{apporveDate}
    where id in
    <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
      #{id}
    </foreach>
  </update>
    <update id="updateAllotNoByIds">
      update iscm_suggest_goods_allot set allot_no = #{allotNo}
      where id in
      <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
        #{id}
      </foreach>
    </update>
    <select id="selectIdsByCompanyCodeByDay" resultType="java.lang.Long">
    select id from iscm_suggest_goods_allot
    where
    out_company_code = #{companyCode}
    and gmt_create between #{startDate} and #{endDate}
  </select>
  <select id="selectGoodsNoByStoreNoAndGoodsNosAndApproveStatus" resultType="java.lang.String">
    select goods_no from iscm_suggest_goods_allot where out_store_code = #{storeCode}
    and goods_no in
    <foreach collection="goodsNos" item="goodsNo" index="index" open="(" close=")" separator=",">
      #{goodsNo}
    </foreach>
    and approve_status = #{approveStatus}
  </select>
</mapper>
