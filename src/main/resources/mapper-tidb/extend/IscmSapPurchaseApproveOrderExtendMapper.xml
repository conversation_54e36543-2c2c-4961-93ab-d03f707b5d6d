<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapperTidb.extend.IscmSapPurchaseApproveOrderExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entityTidb.IscmSapPurchaseApproveOrder">
      <id column="id" jdbcType="BIGINT" property="id" />
      <result column="platform_name" jdbcType="VARCHAR" property="platformName" />
      <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
      <result column="company_name" jdbcType="VARCHAR" property="companyName" />
      <result column="store_name" jdbcType="VARCHAR" property="storeName" />
      <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
      <result column="apply_date" jdbcType="DATE" property="applyDate" />
      <result column="generate_time" jdbcType="TIMESTAMP" property="generateTime" />
      <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
      <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
      <result column="specifications" jdbcType="VARCHAR" property="specifications" />
      <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
      <result column="sap_order_no" jdbcType="VARCHAR" property="sapOrderNo" />
      <result column="apply_quantity" jdbcType="DECIMAL" property="applyQuantity" />
      <result column="sap_approve_quantity" jdbcType="DECIMAL" property="sapApproveQuantity" />
      <result column="modify_result" jdbcType="TINYINT" property="modifyResult" />
      <result column="order_type" jdbcType="TINYINT" property="orderType" />
      <result column="check_status" jdbcType="TINYINT" property="checkStatus" />
      <result column="status" jdbcType="TINYINT" property="status" />
      <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
      <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
      <result column="extend" jdbcType="VARCHAR" property="extend" />
      <result column="version" jdbcType="INTEGER" property="version" />
      <result column="created_by" jdbcType="BIGINT" property="createdBy" />
      <result column="created_name" jdbcType="VARCHAR" property="createdName" />
      <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
      <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Base_Column_List">
    id, platform_name, company_code, company_name, store_name, store_code, apply_date, 
    generate_time, goods_no, goods_name, specifications, manufacturer, sap_order_no, 
    apply_quantity, sap_approve_quantity, modify_result, order_type, check_status, `status`,
    gmt_create, gmt_update, extend, version, created_by, created_name, updated_by, updated_name
  </sql>
  <select id="countDataGroupByCheckStatus"
          resultType="com.cowell.iscm.service.dto.pushReplenishmentMonitor.CountDataDTO">
    select
    count(distinct store_code) as storeQuantity,
    count(distinct sap_order_no) as orderQuantity,
    count(goods_no) as goodsQuantity,
    min(generate_time) as gmtCreateMin,
    max(generate_time) as gmtCreateMax,
    check_status as checkStatus
    from iscm_sap_purchase_approve_order
    where company_code = #{companyCode}
    and apply_date = #{applyDate}
    <if test="orderType != null">
      and order_type = #{orderType}
    </if>
    <if test="checkStatus != null">
      and check_status = #{checkStatus}
    </if>
    group by check_status
  </select>
  <select id="countDataGroupByModifyResult"
          resultType="com.cowell.iscm.service.dto.pushReplenishmentMonitor.CountDataDTO">
    select
    count(*) as goodsQuantity,
    modify_result as modifyResult
    from iscm_sap_purchase_approve_order
    where company_code = #{companyCode}
    and apply_date = #{applyDate}
    <if test="orderType != null">
      and order_type = #{orderType}
    </if>
    <if test="checkStatus != null">
      and check_status = #{checkStatus}
    </if>
    group by modify_result
  </select>

    <select id="selectStoreMinTime" resultType="com.cowell.iscm.service.dto.pushReplenishmentMonitor.StoreTimeDTO">
        select
            store_code as storeCode,
            min(generate_time) as storeTime,
            platform_name as platformName,
            company_name as companyName,
            store_name as storeName,
            apply_date as applyDate,
            group_concat(distinct order_type) as orderTypes,
            group_concat(distinct modify_result) as results
        from iscm_sap_purchase_approve_order
        where company_code = #{companyCode}
        and apply_date = #{applyDate}
        <if test="orderType != null">
            and order_type = #{orderType}
        </if>
        <if test="checkStatus != null">
            and check_status = #{checkStatus}
        </if>
        <if test="modifyResultList != null and modifyResultList.size > 0">
            and modify_result in
            <foreach collection="modifyResultList" item="modifyResult" index="index" open="(" close=")" separator=",">
                #{modifyResult}
            </foreach>
        </if>
        group by store_code
        order by id desc
        <if test="limit != null">
            <if test="offset != null">
                limit ${offset}, ${limit}
            </if>
            <if test="offset == null">
                limit ${limit}
            </if>
        </if>
    </select>

  <select id="selectStoreMinTimeCount" resultType="java.lang.Long">
    select count(*) from (
        select
            store_code as storeCode,
            min(generate_time) as storeTime,
            platform_name as platformName,
            company_name as companyName,
            store_name as storeName,
            apply_date as applyDate
         from iscm_sap_purchase_approve_order
        where company_code = #{companyCode}
          and apply_date = #{applyDate}
        <if test="orderType != null">
          and order_type = #{orderType}
        </if>
        <if test="checkStatus != null">
          and check_status = #{checkStatus}
        </if>
        <if test="modifyResultList != null and modifyResultList.size > 0">
          and modify_result in
          <foreach collection="modifyResultList" item="modifyResult" index="index" open="(" close=")" separator=",">
              #{modifyResult}
          </foreach>
        </if>
        group by store_code
    ) t1
  </select>

  <select id="countRefuseStoreQuantity" resultType="java.lang.Integer">
    select count(*) from (
    select
    store_code,
    count(*) total,
    count(if(modify_result != 4, 1, null)) accept
    from iscm_sap_purchase_approve_order
    where company_code = #{companyCode}
    and apply_date = #{applyDate}
    <if test="orderType != null">
      and order_type = #{orderType}
    </if>
    <if test="checkStatus != null">
      and check_status = #{checkStatus}
    </if>
    group by store_code
    ) t where accept = 0
  </select>

  <select id="countStoreQuantity" resultType="java.lang.Integer">
    select count(distinct store_code) as storeQuantity
    from iscm_sap_purchase_approve_order
    where company_code = #{companyCode}
      and apply_date = #{applyDate}
      <if test="orderType != null">
        and order_type = #{orderType}
      </if>
      <if test="checkStatus != null">
        and check_status = #{checkStatus}
      </if>
  </select>

  <select id="countCheckedErrorStoreQuantity" resultType="java.lang.Integer">
    <include refid="SQL_CountCheckedErrorStoreQuantity" />
  </select>

  <select id="selectCheckedErrorStoreMinTime"
            resultType="com.cowell.iscm.service.dto.pushReplenishmentMonitor.StoreTimeDTO">
    select
      store_code as storeCode,
      min(generate_time) as storeTime,
      platform_name as platformName,
      company_name as companyName,
      store_name as storeName,
      apply_date as applyDate
    from (
      select
        store_code,
        generate_time,
        platform_name,
        company_name,
        store_name,
        apply_date,
        count(*) total,
        count(if(check_status = 1, 1, null)) passed
      from iscm_sap_purchase_approve_order
      where company_code = #{companyCode}
      and apply_date = #{applyDate}
      <if test="orderType != null">
        and order_type = #{orderType}
      </if>
      group by store_code
    ) t where passed = 0
  </select>
  <select id="selectCheckedErrorStoreMinTimeCount" resultType="java.lang.Long">
    <include refid="SQL_CountCheckedErrorStoreQuantity" />
  </select>

  <select id="selectDiffCount" resultType="java.lang.Long">
    select count(*) from (<include refid="SQL_SelectDiff" />) t
  </select>
  <select id="selectDiffList" resultType="com.cowell.iscm.entityTidb.IscmSapPurchaseApproveOrder">
    <include refid="SQL_SelectDiff" />
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>

  <sql id="SQL_CountCheckedErrorStoreQuantity">
    select count(*) from (
    select
      store_code,
      count(*) total,
      count(if(check_status = 1, 1, null)) passed
    from iscm_sap_purchase_approve_order
    where company_code = #{companyCode}
    and apply_date = #{applyDate}
    <if test="orderType != null">
      and order_type = #{orderType}
    </if>
    group by store_code
    ) t where passed = 0
  </sql>

  <sql id="SQL_SelectDiff">
    select
      pos.platform_name as platformName,
      pos.company_code as companyCode,
      pos.company_name as companyName,
      pos.store_code as storeCode,
      pos.store_name as storeName,
      ifnull(sap.apply_date, pos.apply_date) as applyDate,
      ifnull(sap.generate_time, pos.generate_time) as generateTime,
      pos.goods_no as goodsNo,
      pos.goods_name as goodsName,
      pos.specifications,
      pos.manufacturer,
      sap.sap_order_no as sapOrderNo,
      sap.apply_quantity as applyQuantity,
      sap.sap_approve_quantity as sapApproveQuantity,
      sap.modify_result as modifyResult,
      sap.order_type as orderType
    from iscm_store_distribute_apply_order pos
    left join iscm_sap_purchase_approve_order sap
    on sap.company_code = pos.company_code and sap.apply_date = pos.apply_date and sap.goods_no = pos.goods_no and sap.store_code = pos.store_code
    <if test="orderType != null">
      and sap.order_type = #{orderType}
    </if>
    <if test="checkStatus != null">
      and sap.check_status = #{checkStatus}
    </if>
    where pos.order_type in(1,2)
      and pos.order_status = 2
      and pos.modify_result != 4
      and pos.company_code = #{companyCode}
      and pos.apply_date = #{applyDate}
      and sap.id is null
    group by pos.id -- 避免重复
    order by pos.id desc
  </sql>
</mapper>
