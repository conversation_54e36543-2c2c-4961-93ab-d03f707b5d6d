<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapperTidb.extend.IscmWmsLeaveWarehouseStoreReceiveExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entityTidb.IscmWmsLeaveWarehouseStoreReceive">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="platform_name" jdbcType="VARCHAR" property="platformName" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="store_name" jdbcType="VARCHAR" property="storeName" />
    <result column="apply_date" jdbcType="DATE" property="applyDate" />
    <result column="generate_time" jdbcType="TIMESTAMP" property="generateTime" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="specifications" jdbcType="VARCHAR" property="specifications" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="sap_order_no" jdbcType="VARCHAR" property="sapOrderNo" />
    <result column="dn_order_no" jdbcType="VARCHAR" property="dnOrderNo" />
    <result column="order_type" jdbcType="TINYINT" property="orderType" />
    <result column="order_quantity" jdbcType="DECIMAL" property="orderQuantity" />
    <result column="leave_warehouse" jdbcType="VARCHAR" property="leaveWarehouse" />
    <result column="wms_leave_warehouse_quantity" jdbcType="DECIMAL" property="wmsLeaveWarehouseQuantity" />
    <result column="wms_leave_warehouse_rate" jdbcType="DECIMAL" property="wmsLeaveWarehouseRate" />
    <result column="wms_leave_warehouse_rate_type" jdbcType="TINYINT" property="wmsLeaveWarehouseRateType" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Base_Column_List">
    id, platform_name, company_name, company_code, store_code, store_name, apply_date, 
    generate_time, goods_no, goods_name, specifications, manufacturer, sap_order_no, 
    order_type, order_quantity, leave_warehouse, wms_leave_warehouse_quantity, wms_leave_warehouse_rate, 
    store_confirm_quantity, arrive_rate, `status`, gmt_create, gmt_update, extend, version, 
    created_by, created_name, updated_by, updated_name
  </sql>

  <select id="selectStartAndFinishTime" resultType="com.cowell.iscm.service.dto.pushReplenishmentMonitor.WmsLeaveWarehouseStoreReceiveDTO">
    select DATE_FORMAT(min(generate_time),'%Y-%m-%d %H:%i:%s') as startTime,
           DATE_FORMAT(max(generate_time),'%Y-%m-%d %H:%i:%s') as finishTime,
           count(distinct store_code) as allStoreQuantity
    from  iscm_wms_leave_warehouse_store_receive where company_code = #{companyCode} and apply_date = #{applyDate}
  </select>


  <select id="countLeaveWarehouseStoreQuantity" resultType="java.lang.Integer">
    select
        count(distinct store_code)
    from iscm_wms_leave_warehouse_store_receive
    where company_code = #{companyCode} and apply_date = #{applyDate}
    and wms_leave_warehouse_rate &gt; 0
    <if test="orderTypes != null and orderTypes.size > 0">
      and order_type in
      <foreach collection="orderTypes" item="orderType" index="index" open="(" close=")" separator=",">
        #{orderType}
      </foreach>
    </if>
  </select>

  <select id="countLeaveWarehouseOrderQuantity" resultType="java.lang.Integer">
    select
    count(distinct sap_order_no)
    from iscm_wms_leave_warehouse_store_receive
    where company_code = #{companyCode} and apply_date = #{applyDate}
    and wms_leave_warehouse_rate &gt; 0
    <if test="orderTypes != null and orderTypes.size > 0">
      and order_type in
      <foreach collection="orderTypes" item="orderType" index="index" open="(" close=")" separator=",">
        #{orderType}
      </foreach>
    </if>
  </select>

  <select id="countAllStoreQuantity" resultType="java.lang.Integer">
    select
    count(distinct store_code)
    from iscm_wms_leave_warehouse_store_receive
    where company_code = #{companyCode} and apply_date = #{applyDate}
  </select>


  <select id="countReceiveStoreQuantity" resultType="java.lang.Integer">
    select
        count(distinct store_code)
    from
        iscm_wms_leave_warehouse_store_receive
    where company_code = #{companyCode} and apply_date = #{applyDate}
    <if test="orderTypes != null and orderTypes.size > 0">
      and order_type in
      <foreach collection="orderTypes" item="orderType" index="index" open="(" close=")" separator=",">
        #{orderType}
      </foreach>
    </if>
  </select>


  <select id="countLeaveWarehouseGoodsQuantity" resultType="java.lang.Integer">
    select
      count(*)
    from iscm_wms_leave_warehouse_store_receive
    where company_code = #{companyCode} and apply_date = #{applyDate} and wms_leave_warehouse_rate &gt;= 0
    <if test="orderTypes != null and orderTypes.size > 0">
      and order_type in
      <foreach collection="orderTypes" item="orderType" index="index" open="(" close=")" separator=",">
        #{orderType}
      </foreach>
    </if>
  </select>

  <select id="countAllSatisfiedGoodsQuantity" resultType="java.lang.Integer">
    select
        count(*)
    from iscm_wms_leave_warehouse_store_receive
    where company_code = #{companyCode} and apply_date = #{applyDate} and wms_leave_warehouse_rate = 1
    <if test="orderTypes != null and orderTypes.size > 0">
      and order_type in
      <foreach collection="orderTypes" item="orderType" index="index" open="(" close=")" separator=",">
        #{orderType}
      </foreach>
    </if>
  </select>

  <select id="countPartiallySatisfiedGoodsQuantity" resultType="java.lang.Integer">
    select
        count(*)
    from iscm_wms_leave_warehouse_store_receive
    where company_code = #{companyCode} and apply_date = #{applyDate} and wms_leave_warehouse_rate &gt; 0 and wms_leave_warehouse_rate &lt; 1
    <if test="orderTypes != null and orderTypes.size > 0">
      and order_type in
      <foreach collection="orderTypes" item="orderType" index="index" open="(" close=")" separator=",">
        #{orderType}
      </foreach>
    </if>
  </select>

  <select id="countNoSatisfiedGoodsQuantity" resultType="java.lang.Integer">
    select
        count(*)
    from iscm_wms_leave_warehouse_store_receive
    where company_code = #{companyCode} and apply_date = #{applyDate} and wms_leave_warehouse_rate = 0
    <if test="orderTypes != null and orderTypes.size > 0">
      and order_type in
      <foreach collection="orderTypes" item="orderType" index="index" open="(" close=")" separator=",">
        #{orderType}
      </foreach>
    </if>
  </select>

  <select id="countGroupByRateType" resultType="com.cowell.iscm.service.dto.pushReplenishmentMonitor.RateCountDTO">
    select
      wms_leave_warehouse_rate_type rateType ,count(*) count
    from iscm_wms_leave_warehouse_store_receive
    where company_code = #{companyCode} and apply_date = #{applyDate}
    <if test="orderTypes != null and orderTypes.size > 0">
      and order_type in
      <foreach collection="orderTypes" item="orderType" index="index" open="(" close=")" separator=",">
        #{orderType}
      </foreach>
    </if>
    group by wms_leave_warehouse_rate_type
  </select>

  <select id="selectStoreMinTime" resultType="com.cowell.iscm.service.dto.pushReplenishmentMonitor.StoreTimeDTO">
    SELECT store_code as storeCode, min(generate_time) as storeTime,
          platform_name as platformName,
          company_name as companyName,
          store_name as storeName,
          apply_date as applyDate
    from iscm_wms_leave_warehouse_store_receive
    where company_code = #{companyCode}
    and apply_date = #{applyDate}
    <if test="orderTypes != null and orderTypes.size > 0">
      and order_type in
      <foreach collection="orderTypes" item="orderType" index="index" open="(" close=")" separator=",">
        #{orderType}
      </foreach>
    </if>
    group by store_code
    order by id desc
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>

  <select id="selectStoreMinTimeCount" resultType="java.lang.Long">
    SELECT count(*) FROM (
        SELECT store_code as storeCode, min(generate_time) as storeTime,
        platform_name as platformName,
        company_name as companyName,
        store_name as storeName,
        apply_date as applyDate
        from iscm_wms_leave_warehouse_store_receive
        where company_code = #{companyCode}
        and apply_date = #{applyDate}
        <if test="orderTypes != null and orderTypes.size > 0">
          and order_type in
          <foreach collection="orderTypes" item="orderType" index="index" open="(" close=")" separator=",">
            #{orderType}
          </foreach>
        </if>
        group by store_code ) t1
  </select>

  <select id="countNoSatisfiedStoreQuantity" resultType="java.lang.Integer">
    select count(*) from (
    select
    store_code,
    count(*) total,
    count(if(wms_leave_warehouse_rate_type != 0, 1, null)) satisfied
    from iscm_wms_leave_warehouse_store_receive
    where company_code = #{companyCode}
    and apply_date = #{applyDate}
    <if test="orderTypes != null and orderTypes.size > 0">
      and order_type in
      <foreach collection="orderTypes" item="orderType" index="index" open="(" close=")" separator=",">
        #{orderType}
      </foreach>
    </if>
    group by store_code
    ) t where satisfied = 0
  </select>

</mapper>
