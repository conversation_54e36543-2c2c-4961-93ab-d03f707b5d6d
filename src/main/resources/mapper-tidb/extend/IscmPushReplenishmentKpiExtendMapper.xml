<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapperTidb.extend.IscmPushReplenishmentKpiExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entityTidb.IscmPushReplenishmentKpi">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="bdp_company_code" jdbcType="VARCHAR" property="bdpCompanyCode" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="model_version" jdbcType="VARCHAR" property="modelVersion" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="real_sales" jdbcType="DECIMAL" property="realSales" />
    <result column="wmape" jdbcType="DECIMAL" property="wmape" />
    <result column="forecast_sales" jdbcType="DECIMAL" property="forecastSales" />
    <result column="forecast_date" jdbcType="TIMESTAMP" property="forecastDate" />
    <result column="bar_code" jdbcType="VARCHAR" property="barCode" />
    <result column="goods_common_name" jdbcType="VARCHAR" property="goodsCommonName" />
    <result column="goods_unit" jdbcType="VARCHAR" property="goodsUnit" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="specifications" jdbcType="VARCHAR" property="specifications" />
    <result column="dosage_form" jdbcType="VARCHAR" property="dosageForm" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="approval_number" jdbcType="VARCHAR" property="approvalNumber" />
    <result column="habitat" jdbcType="VARCHAR" property="habitat" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Base_Column_List">
    id, bdp_company_code, company_code, company_name, model_version, goods_no, goods_name,
    real_sales, wmape, forecast_sales, forecast_date, bar_code, goods_common_name, goods_unit,
    description, specifications, dosage_form, manufacturer, approval_number, habitat,
    status, gmt_create, gmt_update, extend, version, created_by, created_name, updated_by,
    updated_name
  </sql>
    <select id="selectBdpCompanyCodes" resultType="java.lang.String">
      select distinct bdp_company_code from iscm_push_replenishment_kpi
    </select>
    <select id="selectStoreCodeByBdpCompanyCode" resultType="java.lang.String">
        select distinct store_code from iscm_push_replenishment_kpi
        where bdp_company_code = #{bdpCompanyCode}
    </select>
    <select id="selectModelVersions" resultType="java.lang.String">
      select distinct model_version from iscm_push_replenishment_kpi
    </select>
</mapper>
