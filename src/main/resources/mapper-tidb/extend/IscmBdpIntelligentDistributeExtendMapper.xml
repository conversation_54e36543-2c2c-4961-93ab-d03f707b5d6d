<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapperTidb.extend.IscmBdpIntelligentDistributeExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entityTidb.IscmBdpIntelligentDistribute">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="platform_name" jdbcType="VARCHAR" property="platformName" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="store_name" jdbcType="VARCHAR" property="storeName" />
    <result column="apply_date" jdbcType="DATE" property="applyDate" />
    <result column="generate_time" jdbcType="TIMESTAMP" property="generateTime" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="specifications" jdbcType="VARCHAR" property="specifications" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="bdp_order_no" jdbcType="VARCHAR" property="bdpOrderNo" />
    <result column="apply_quantity" jdbcType="DECIMAL" property="applyQuantity" />
    <result column="bdp_distribute_before_quantity" jdbcType="DECIMAL" property="bdpDistributeBeforeQuantity" />
    <result column="bdp_distribute_quantity" jdbcType="DECIMAL" property="bdpDistributeQuantity" />
    <result column="bdp_distribute_after_quantity" jdbcType="DECIMAL" property="bdpDistributeAfterQuantity" />
    <result column="shipment_dc_code" jdbcType="VARCHAR" property="shipmentDcCode" />
    <result column="distribute_rate" jdbcType="DECIMAL" property="distributeRate" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Base_Column_List">
    id, platform_name, company_name, company_code, store_code, store_name, apply_date, 
    generate_time, goods_no, goods_name, specifications, manufacturer, bdp_order_no, 
    apply_quantity, bdp_distribute_before_quantity, bdp_distribute_quantity, bdp_distribute_after_quantity, 
    shipment_dc_code, distribute_rate, `status`, gmt_create, gmt_update, extend, version, 
    created_by, created_name, updated_by, updated_name
  </sql>


  <select id="selectStartAndFinishTime" resultType="com.cowell.iscm.service.dto.pushReplenishmentMonitor.BdpIntelligentDistributeDTO">
    select DATE_FORMAT(min(generate_time),'%Y-%m-%d %H:%i:%s') as startTime,
           DATE_FORMAT(max(generate_time),'%Y-%m-%d %H:%i:%s') as finishTime
    from  iscm_bdp_intelligent_distribute where company_code = #{companyCode} and apply_date = #{applyDate}
  </select>

  <select id="countDistributeStoreQuantity" resultType="java.lang.Integer">
    select
    count(distinct store_code)
    from iscm_bdp_intelligent_distribute
    where company_code = #{companyCode} and apply_date = #{applyDate}
    and bdp_distribute_quantity &gt;= 0
  </select>

  <select id="countDistributeGoodsQuantity" resultType="java.lang.Integer">
    select
      count(*)
    from iscm_bdp_intelligent_distribute
    where company_code = #{companyCode} and apply_date = #{applyDate}
      and bdp_distribute_quantity &gt;= 0
  </select>

  <select id="countAllSatisfiedGoodsQuantity" resultType="java.lang.Integer">
    select
      count(*)
    from iscm_bdp_intelligent_distribute
    where company_code = #{companyCode} and apply_date = #{applyDate}
      and distribute_rate = 1
  </select>

  <select id="countPartiallySatisfiedGoodsQuantity1" resultType="java.lang.Integer">
    select
      count(*)
    from iscm_bdp_intelligent_distribute
    where company_code = #{companyCode} and apply_date = #{applyDate}
      and distribute_rate &gt;= 0.85 and distribute_rate &lt; 1
  </select>

  <select id="countPartiallySatisfiedGoodsQuantity2" resultType="java.lang.Integer">
    select
      count(*)
    from iscm_bdp_intelligent_distribute
    where company_code = #{companyCode} and apply_date = #{applyDate}
      and distribute_rate &gt; 0 and distribute_rate &lt; 0.85
  </select>

  <select id="countNoSatisfiedGoodsQuantity" resultType="java.lang.Integer">
    select
      count(*)
    from iscm_bdp_intelligent_distribute
    where company_code = #{companyCode} and apply_date = #{applyDate}
      and distribute_rate = 0
  </select>

  <select id="countGroupByRateType" resultType="com.cowell.iscm.service.dto.pushReplenishmentMonitor.RateCountDTO">
    select
      distribute_rate_type rateType, count(*) count
    from iscm_bdp_intelligent_distribute
    where company_code = #{companyCode} and apply_date = #{applyDate}
    group by distribute_rate_type
  </select>

  <select id="selectStoreMinTime" resultType="com.cowell.iscm.service.dto.pushReplenishmentMonitor.StoreTimeDTO">
    select store_code as storeCode, min(generate_time) as storeTime,
           platform_name as platformName,
           company_name as companyName,
           store_name as storeName,
           apply_date as applyDate,
           group_concat(distinct distribute_rate_type) as results
    from iscm_bdp_intelligent_distribute
    where company_code = #{companyCode}
      and apply_date = #{applyDate}
    group by store_code
    order by id desc
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>

  <select id="selectStoreMinTimeCount" resultType="java.lang.Long">
    select count(*) from (
      select store_code as storeCode, min(generate_time) as storeTime,
             platform_name as platformName,
             company_name as companyName,
             store_name as storeName,
             apply_date as applyDate
      from iscm_bdp_intelligent_distribute
      where company_code = #{companyCode}
        and apply_date = #{applyDate}
      group by store_code ) t1
  </select>

  <select id="countNoSatisfiedStoreQuantity" resultType="java.lang.Integer">
    select count(*) from (
    select
    store_code,
    count(*) total,
    count(if(distribute_rate_type != 0, 1, null)) satisfied
    from iscm_bdp_intelligent_distribute
    where company_code = #{companyCode}
    and apply_date = #{applyDate}
    group by store_code
    ) t where satisfied = 0
  </select>

</mapper>
