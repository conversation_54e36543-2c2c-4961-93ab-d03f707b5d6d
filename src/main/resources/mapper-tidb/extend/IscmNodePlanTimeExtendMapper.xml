<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapperTidb.extend.IscmNodePlanTimeExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entityTidb.IscmNodePlanTime">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="pos_genorder_time" jdbcType="TIME" property="posGenorderTime" />
    <result column="pos_mergeorder_time" jdbcType="TIME" property="posMergeorderTime" />
    <result column="sap_sap_purchase_approve_time" jdbcType="TIME" property="sapSapPurchaseApproveTime" />
    <result column="bdp_distribute_time" jdbcType="TIME" property="bdpDistributeTime" />
    <result column="sap_sap_transfer_time" jdbcType="TIME" property="sapSapTransferTime" />
    <result column="dt" jdbcType="VARCHAR" property="dt" />
  </resultMap>
  <sql id="Base_Column_List">
    id, company_code, company_name, pos_genorder_time, pos_mergeorder_time, sap_sap_purchase_approve_time, 
    bdp_distribute_time, sap_sap_transfer_time, dt
  </sql>
    <insert id="batchInsert" parameterType="java.util.List">
      insert into iscm_node_plan_time (company_code, company_name, pos_genorder_time, pos_mergeorder_time, sap_sap_purchase_approve_time,
    bdp_distribute_time, sap_sap_transfer_time)
    values
      <foreach collection="list" item="item" index="index" separator="," >
        (#{item.companyCode,jdbcType=VARCHAR}, #{item.companyName,jdbcType=VARCHAR},
        #{item.posGenorderTime,jdbcType=TIME}, #{item.posMergeorderTime,jdbcType=TIME}, #{item.sapSapPurchaseApproveTime,jdbcType=TIME},
        #{item.bdpDistributeTime,jdbcType=TIME}, #{item.sapSapTransferTime,jdbcType=TIME})
      </foreach>
    </insert>

  <select id="selectCompanyCode" resultType="java.lang.String">
      select distinct company_code from iscm_node_plan_time
      where company_code in
      <foreach collection="companyCodes" item="companyCode" index="index" open="(" close=")" separator="," >
        #{companyCode}
      </foreach>
      and dt = #{dt}
    </select>
</mapper>
