<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapperTidb.extend.StoreApplyDateExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entityTidb.StoreApplyDate">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="store_attr" jdbcType="VARCHAR" property="storeAttr" />
    <result column="open_date" jdbcType="DATE" property="openDate" />
    <result column="apply_date" jdbcType="DATE" property="applyDate" />
    <result column="store_status" jdbcType="VARCHAR" property="storeStatus" />
    <result column="store_electric_able" jdbcType="VARCHAR" property="storeElectricAble" />
    <result column="distr_circle" jdbcType="VARCHAR" property="distrCircle" />
    <result column="distr_transit_day" jdbcType="INTEGER" property="distrTransitDay" />
    <result column="month_sales_level" jdbcType="VARCHAR" property="monthSalesLevel" />
    <result column="store_count" jdbcType="INTEGER" property="storeCount" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, company_code, store_code, store_attr, open_date, apply_date, store_status, store_electric_able, 
    distr_circle, distr_transit_day, month_sales_level, store_count, gmt_create, gmt_update
  </sql>
  <select id="selectStoreCount" resultType="com.cowell.iscm.service.dto.applyParam.StoreApplyCountDTO">
    select company_code as companyCode, apply_date as applyDate, store_count as shouldCount, count(*) as realCount
    from store_apply_date
    group by company_code, apply_date
  </select>
</mapper>
