<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapperTidb.extend.IscmStoreGoodsStockCostTopExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entityTidb.IscmStoreGoodsStockCostTop">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="company_bdp_code" jdbcType="VARCHAR" property="companyBdpCode" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="store_rank" jdbcType="INTEGER" property="storeRank" />
    <result column="storage_days" jdbcType="INTEGER" property="storageDays" />
    <result column="non_sales_days" jdbcType="INTEGER" property="nonSalesDays" />
    <result column="thirty_sales_quantity" jdbcType="DECIMAL" property="thirtySalesQuantity" />
    <result column="thirty_sales_count" jdbcType="INTEGER" property="thirtySalesCount" />
    <result column="min_display_quantity" jdbcType="DECIMAL" property="minDisplayQuantity" />
    <result column="warehouse_code" jdbcType="VARCHAR" property="warehouseCode" />
    <result column="warehouse_name" jdbcType="VARCHAR" property="warehouseName" />
    <result column="goodsline" jdbcType="VARCHAR" property="goodsline" />
    <result column="pushlevel" jdbcType="VARCHAR" property="pushlevel" />
    <result column="goods_level" jdbcType="INTEGER" property="goodsLevel" />
    <result column="forbid_distribute" jdbcType="VARCHAR" property="forbidDistribute" />
    <result column="forbid_return_warehouse" jdbcType="VARCHAR" property="forbidReturnWarehouse" />
    <result column="forbid_apply" jdbcType="VARCHAR" property="forbidApply" />
    <result column="forbid_allot" jdbcType="VARCHAR" property="forbidAllot" />
    <result column="stock_quantity" jdbcType="DECIMAL" property="stockQuantity" />
    <result column="cost_amount" jdbcType="DECIMAL" property="costAmount" />
    <result column="stock_upper_limit_days" jdbcType="INTEGER" property="stockUpperLimitDays" />
    <result column="stock_lower_limit_days" jdbcType="INTEGER" property="stockLowerLimitDays" />
    <result column="hd_synthesize_average_daily_sales" jdbcType="DECIMAL" property="hdSynthesizeAverageDailySales" />
    <result column="stock_upper_limit" jdbcType="DECIMAL" property="stockUpperLimit" />
    <result column="stock_lower_limit" jdbcType="DECIMAL" property="stockLowerLimit" />
    <result column="bdp_synthesize_average_daily_sales" jdbcType="DECIMAL" property="bdpSynthesizeAverageDailySales" />
    <result column="expect_sale_days" jdbcType="DECIMAL" property="expectSaleDays" />
    <result column="deal_status" jdbcType="TINYINT" property="dealStatus" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, company_code, company_bdp_code, store_code, goods_no, store_rank, storage_days,
    non_sales_days, thirty_sales_quantity, thirty_sales_count, min_display_quantity,
    warehouse_code, warehouse_name, goodsline, pushlevel, goods_level, forbid_distribute,
    forbid_return_warehouse, forbid_apply, forbid_allot, stock_quantity, cost_amount,
    stock_upper_limit_days, stock_lower_limit_days, hd_synthesize_average_daily_sales,
    stock_upper_limit, stock_lower_limit, bdp_synthesize_average_daily_sales, expect_sale_days,
    deal_status, `status`, gmt_create, gmt_update, extend, version, created_by, created_name,
    updated_by, updated_name
  </sql>

  <select id="countReturnExample" resultType="java.lang.Long">
    select
    count(*) from (select 1
    from iscm_store_goods_stock_cost_top
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    group by warehouse_code, goods_no ) as a
  </select>

  <select id="selectReturnByExample"
          resultType="com.cowell.iscm.service.dto.returnWarehouse.StoreReturnWarehouseConfirmDTO">
    select
    warehouse_code as warehouseCode, warehouse_name as warehouseName, goods_no as goodsNo, count(distinct company_code) as companyQuantity,
    count(distinct store_code) as storeQuantity, sum(stock_quantity) as totalRegisterQuantity, sum(cost_amount) as totalCostAmount, group_concat(distinct company_code) as companyCodeStr
    from iscm_store_goods_stock_cost_top
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    group by warehouse_code, goods_no
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>

</mapper>
