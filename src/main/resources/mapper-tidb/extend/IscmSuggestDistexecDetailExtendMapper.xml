<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapperTidb.extend.IscmSuggestDistexecDetailExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entityTidb.IscmSuggestDistexecDetail">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="allot_date" jdbcType="TIMESTAMP" property="allotDate" />
    <result column="allot_month" jdbcType="INTEGER" property="allotMonth" />
    <result column="register_no" jdbcType="VARCHAR" property="registerNo" />
    <result column="register_month" jdbcType="INTEGER" property="registerMonth" />
    <result column="allot_type" jdbcType="TINYINT" property="allotType" />
    <result column="pos_allot_no" jdbcType="VARCHAR" property="posAllotNo" />
    <result column="allot_approve_time" jdbcType="TIMESTAMP" property="allotApproveTime" />
    <result column="pos_allot_detail_id" jdbcType="BIGINT" property="posAllotDetailId" />
    <result column="platform_org_id" jdbcType="BIGINT" property="platformOrgId" />
    <result column="platform_org_name" jdbcType="VARCHAR" property="platformOrgName" />
    <result column="out_company_id" jdbcType="BIGINT" property="outCompanyId" />
    <result column="out_company_code" jdbcType="VARCHAR" property="outCompanyCode" />
    <result column="out_company_name" jdbcType="VARCHAR" property="outCompanyName" />
    <result column="in_company_id" jdbcType="BIGINT" property="inCompanyId" />
    <result column="in_company_code" jdbcType="VARCHAR" property="inCompanyCode" />
    <result column="in_company_name" jdbcType="VARCHAR" property="inCompanyName" />
    <result column="out_store_id" jdbcType="BIGINT" property="outStoreId" />
    <result column="out_store_code" jdbcType="VARCHAR" property="outStoreCode" />
    <result column="out_store_name" jdbcType="VARCHAR" property="outStoreName" />
    <result column="out_store_attr" jdbcType="VARCHAR" property="outStoreAttr" />
    <result column="out_store_sales_level" jdbcType="VARCHAR" property="outStoreSalesLevel" />
    <result column="in_store_id" jdbcType="BIGINT" property="inStoreId" />
    <result column="in_store_code" jdbcType="VARCHAR" property="inStoreCode" />
    <result column="in_store_name" jdbcType="VARCHAR" property="inStoreName" />
    <result column="in_store_attr" jdbcType="VARCHAR" property="inStoreAttr" />
    <result column="in_store_sales_level" jdbcType="VARCHAR" property="inStoreSalesLevel" />
    <result column="allot_group_code" jdbcType="VARCHAR" property="allotGroupCode" />
    <result column="allot_group_name" jdbcType="VARCHAR" property="allotGroupName" />
    <result column="approve_name" jdbcType="VARCHAR" property="approveName" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="goods_desc" jdbcType="VARCHAR" property="goodsDesc" />
    <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
    <result column="produce_date" jdbcType="VARCHAR" property="produceDate" />
    <result column="validity_date" jdbcType="VARCHAR" property="validityDate" />
    <result column="hd_batch_no" jdbcType="VARCHAR" property="hdBatchNo" />
    <result column="sap_batch_no" jdbcType="VARCHAR" property="sapBatchNo" />
    <result column="notes" jdbcType="VARCHAR" property="notes" />
    <result column="void_reason" jdbcType="VARCHAR" property="voidReason" />
    <result column="goods_common_name" jdbcType="VARCHAR" property="goodsCommonName" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="suggest_allot_quantity" jdbcType="DECIMAL" property="suggestAllotQuantity" />
    <result column="suggest_cost_amount" jdbcType="DECIMAL" property="suggestCostAmount" />
    <result column="real_allot_quantity" jdbcType="DECIMAL" property="realAllotQuantity" />
    <result column="real_cost_amount" jdbcType="DECIMAL" property="realCostAmount" />
    <result column="out_approve_status" jdbcType="TINYINT" property="outApproveStatus" />
    <result column="out_approve_time" jdbcType="TIMESTAMP" property="outApproveTime" />
    <result column="out_allot_quantity" jdbcType="DECIMAL" property="outAllotQuantity" />
    <result column="in_approve_status" jdbcType="TINYINT" property="inApproveStatus" />
    <result column="in_approve_time" jdbcType="TIMESTAMP" property="inApproveTime" />
    <result column="in_allot_quantity" jdbcType="DECIMAL" property="inAllotQuantity" />
    <result column="in_stock_time" jdbcType="TIMESTAMP" property="inStockTime" />
    <result column="show_status" jdbcType="TINYINT" property="showStatus" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Base_Column_List">
    id, allot_date, allot_month, register_no, register_month, allot_type, pos_allot_no, 
    allot_approve_time, pos_allot_detail_id, platform_org_id, platform_org_name, out_company_id,
    out_company_code, out_company_name, in_company_id, in_company_code, in_company_name,
    out_store_id, out_store_code, out_store_name, out_store_attr, out_store_sales_level,
    in_store_id, in_store_code, in_store_name, in_store_attr, in_store_sales_level, allot_group_code,
    allot_group_name, approve_name, goods_no, goods_desc, batch_no, produce_date, validity_date,
    hd_batch_no, sap_batch_no, notes, void_reason, goods_common_name, manufacturer, unit,
    suggest_allot_quantity, suggest_cost_amount, real_allot_quantity, real_cost_amount,
    out_approve_status, out_approve_time, out_allot_quantity, in_approve_status, in_approve_time,
    in_allot_quantity, in_stock_time, show_status, `status`, gmt_create, gmt_update,
    extend, version, created_by, created_name, updated_by, updated_name
  </sql>
  <select id="selectAllotExecByOutStoreOrgIdsAndAllotTypes"
          resultType="com.cowell.iscm.entityTidb.IscmSuggestDistexecDetail">
    select out_store_id as outStoreId, in_store_id as inStoreId, real_cost_amount as realCostAmount,
    in_approve_status as inApproveStatus, out_approve_status as outApproveStatus, real_allot_quantity as realAllotQuantity,
    in_allot_quantity as inAllotQuantity, out_allot_quantity as outAllotQuantity, pos_allot_no as posAllotNo
    from iscm_suggest_distexec_detail
    where 1=1
    <if test="companyOrgIds != null and companyOrgIds.size > 0">
      and out_company_id in
      <foreach collection="companyOrgIds" item="companyOrgId" index="index" open="(" close=")" separator="," >
        #{companyOrgId}
      </foreach>
    </if>
    <if test="storeOrgIds != null and storeOrgIds.size > 0">
            and out_store_id in
            <foreach collection="storeOrgIds" item="storeOrgId" index="index" open="(" close=")" separator="," >
              #{storeOrgId}
            </foreach>
            and in_store_id in
            <foreach collection="storeOrgIds" item="storeOrgId" index="index" open="(" close=")" separator="," >
              #{storeOrgId}
            </foreach>
      </if>
    <if test="storeAttrs != null and storeAttrs.size > 0">
            and out_store_attr in
            <foreach collection="storeAttrs" item="storeAttr" index="index" open="(" close=")" separator="," >
              #{storeAttr}
            </foreach>
            and in_store_attr in
            <foreach collection="storeAttrs" item="storeAttr" index="index" open="(" close=")" separator="," >
              #{storeAttr}
            </foreach>
      </if>
    and register_month = #{registerMonth}
    and `status` = 0
    and allot_type in
    <foreach collection="allotTypes" item="allotType" index="index" open="(" close=")" separator="," >
      #{allotType}
    </foreach>
    order by id desc
    limit ${start}, ${pageSize}
  </select>



  <update id="updateBatch"  parameterType="java.util.List">
    <foreach collection="list" item="record" index="index" open="" close="" separator=";">
      update iscm_suggest_distexec_detail
      <set>
        pos_allot_no = #{record.posAllotNo,jdbcType=VARCHAR},
        pos_allot_detail_id = #{record.posAllotDetailId,jdbcType=BIGINT},
        hd_batch_no = #{record.hdBatchNo,jdbcType=VARCHAR},
        sap_batch_no = #{record.sapBatchNo,jdbcType=VARCHAR},
        out_approve_status = #{record.outApproveStatus,jdbcType=TINYINT},
        out_approve_time = #{record.outApproveTime,jdbcType=TIMESTAMP},
        out_allot_quantity = #{record.outAllotQuantity,jdbcType=DECIMAL},
        in_approve_status = #{record.inApproveStatus,jdbcType=TINYINT},
        in_approve_time = #{record.inApproveTime,jdbcType=TIMESTAMP},
        in_allot_quantity = #{record.inAllotQuantity,jdbcType=DECIMAL},
        in_stock_time = #{record.inStockTime,jdbcType=TIMESTAMP},
        show_status = #{record.showStatus,jdbcType=TINYINT},
        status = #{record.status,jdbcType=TINYINT}
      </set>
      where id = ${record.id}
    </foreach>
  </update>

  <insert id="insertBatch" parameterType="java.util.List">
    insert into iscm_suggest_distexec_detail (allot_date, allot_month,
    register_no, register_month, allot_type,
    pos_allot_no, allot_approve_time, pos_allot_detail_id,
    platform_org_id, platform_org_name, out_company_id,
    out_company_code, out_company_name, in_company_id,
    in_company_code, in_company_name, out_store_id,
    out_store_code, out_store_name, out_store_attr, out_store_sales_level, in_store_id,
    in_store_code, in_store_name, in_store_attr, in_store_sales_level, goods_no,
    goods_desc, batch_no, produce_date,
    validity_date, hd_batch_no, sap_batch_no,
    goods_common_name, manufacturer, unit,
    suggest_allot_quantity, suggest_cost_amount,
    real_allot_quantity, real_cost_amount, out_approve_status,
    out_approve_time, out_allot_quantity, in_approve_status,
    in_approve_time, in_allot_quantity, in_stock_time,
    show_status)
    values
    <foreach collection="list" item="item" index="index" separator="," >
    ( #{item.allotDate,jdbcType=TIMESTAMP}, #{item.allotMonth,jdbcType=INTEGER},
    #{item.registerNo,jdbcType=VARCHAR}, #{item.registerMonth,jdbcType=INTEGER}, #{item.allotType,jdbcType=TINYINT},
    #{item.posAllotNo,jdbcType=VARCHAR}, #{item.allotApproveTime,jdbcType=TIMESTAMP}, #{item.posAllotDetailId,jdbcType=BIGINT},
    #{item.platformOrgId,jdbcType=BIGINT}, #{item.platformOrgName,jdbcType=VARCHAR}, #{item.outCompanyId,jdbcType=BIGINT},
    #{item.outCompanyCode,jdbcType=VARCHAR}, #{item.outCompanyName,jdbcType=VARCHAR}, #{item.inCompanyId,jdbcType=BIGINT},
    #{item.inCompanyCode,jdbcType=VARCHAR}, #{item.inCompanyName,jdbcType=VARCHAR}, #{item.outStoreId,jdbcType=BIGINT},
    #{item.outStoreCode,jdbcType=VARCHAR}, #{item.outStoreName,jdbcType=VARCHAR}, #{item.outStoreAttr,jdbcType=VARCHAR}, #{item.outStoreSalesLevel,jdbcType=VARCHAR}, #{item.inStoreId,jdbcType=BIGINT},
    #{item.inStoreCode,jdbcType=VARCHAR}, #{item.inStoreName,jdbcType=VARCHAR}, #{item.inStoreAttr,jdbcType=VARCHAR}, #{item.inStoreSalesLevel,jdbcType=VARCHAR}, #{item.goodsNo,jdbcType=VARCHAR},
    #{item.goodsDesc,jdbcType=VARCHAR}, #{item.batchNo,jdbcType=VARCHAR}, #{item.produceDate,jdbcType=VARCHAR},
    #{item.validityDate,jdbcType=VARCHAR}, #{item.hdBatchNo,jdbcType=VARCHAR}, #{item.sapBatchNo,jdbcType=VARCHAR},
    #{item.goodsCommonName,jdbcType=VARCHAR}, #{item.manufacturer,jdbcType=VARCHAR}, #{item.unit,jdbcType=VARCHAR},
    #{item.suggestAllotQuantity,jdbcType=DECIMAL}, #{item.suggestCostAmount,jdbcType=DECIMAL},
    #{item.realAllotQuantity,jdbcType=DECIMAL}, #{item.realCostAmount,jdbcType=DECIMAL}, #{item.outApproveStatus,jdbcType=TINYINT},
    #{item.outApproveTime,jdbcType=TIMESTAMP}, #{item.outAllotQuantity,jdbcType=DECIMAL}, #{item.inApproveStatus,jdbcType=TINYINT},
    #{item.inApproveTime,jdbcType=TIMESTAMP}, #{item.inAllotQuantity,jdbcType=DECIMAL}, #{item.inStockTime,jdbcType=TIMESTAMP},
    #{item.showStatus,jdbcType=TINYINT}
      )
    </foreach>


  </insert>


  <select id="selectLastDate" resultType="java.util.Date">
    SELECT gmt_update FROM iscm_suggest_distexec_detail ORDER BY gmt_update desc LIMIT 1
  </select>


  <select id="selectDistinctPosAllotNos" resultType="java.lang.String">
    select distinct pos_allot_no from iscm_suggest_distexec
  </select>

</mapper>
