<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapperTidb.IscmSuggestDistexecDoneDetailMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entityTidb.IscmSuggestDistexecDoneDetail">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="allot_date" jdbcType="TIMESTAMP" property="allotDate" />
    <result column="allot_month" jdbcType="INTEGER" property="allotMonth" />
    <result column="register_no" jdbcType="VARCHAR" property="registerNo" />
    <result column="register_month" jdbcType="INTEGER" property="registerMonth" />
    <result column="allot_type" jdbcType="TINYINT" property="allotType" />
    <result column="pos_allot_no" jdbcType="VARCHAR" property="posAllotNo" />
    <result column="platform_org_id" jdbcType="BIGINT" property="platformOrgId" />
    <result column="platform_org_name" jdbcType="VARCHAR" property="platformOrgName" />
    <result column="out_company_id" jdbcType="BIGINT" property="outCompanyId" />
    <result column="out_company_code" jdbcType="VARCHAR" property="outCompanyCode" />
    <result column="out_company_name" jdbcType="VARCHAR" property="outCompanyName" />
    <result column="in_company_id" jdbcType="BIGINT" property="inCompanyId" />
    <result column="in_company_code" jdbcType="VARCHAR" property="inCompanyCode" />
    <result column="in_company_name" jdbcType="VARCHAR" property="inCompanyName" />
    <result column="out_store_id" jdbcType="BIGINT" property="outStoreId" />
    <result column="out_store_code" jdbcType="VARCHAR" property="outStoreCode" />
    <result column="out_store_name" jdbcType="VARCHAR" property="outStoreName" />
    <result column="out_store_attr" jdbcType="VARCHAR" property="outStoreAttr" />
    <result column="out_store_sales_level" jdbcType="VARCHAR" property="outStoreSalesLevel" />
    <result column="in_store_id" jdbcType="BIGINT" property="inStoreId" />
    <result column="in_store_code" jdbcType="VARCHAR" property="inStoreCode" />
    <result column="in_store_name" jdbcType="VARCHAR" property="inStoreName" />
    <result column="in_store_attr" jdbcType="VARCHAR" property="inStoreAttr" />
    <result column="in_store_sales_level" jdbcType="VARCHAR" property="inStoreSalesLevel" />
    <result column="allot_group_code" jdbcType="VARCHAR" property="allotGroupCode" />
    <result column="allot_group_name" jdbcType="VARCHAR" property="allotGroupName" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="goods_desc" jdbcType="VARCHAR" property="goodsDesc" />
    <result column="goods_common_name" jdbcType="VARCHAR" property="goodsCommonName" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="real_allot_quantity" jdbcType="DECIMAL" property="realAllotQuantity" />
    <result column="real_cost_amount" jdbcType="DECIMAL" property="realCostAmount" />
    <result column="allot_approve_time" jdbcType="TIMESTAMP" property="allotApproveTime" />
    <result column="in_approve_status" jdbcType="TINYINT" property="inApproveStatus" />
    <result column="in_approve_time" jdbcType="TIMESTAMP" property="inApproveTime" />
    <result column="in_allot_quantity" jdbcType="DECIMAL" property="inAllotQuantity" />
    <result column="in_allot_cost_amount" jdbcType="DECIMAL" property="inAllotCostAmount" />
    <result column="in_stock_time" jdbcType="TIMESTAMP" property="inStockTime" />
    <result column="in_store_sales_7" jdbcType="DECIMAL" property="inStoreSales7" />
    <result column="in_store_puramount_7" jdbcType="DECIMAL" property="inStorePuramount7" />
    <result column="in_store_sales_14" jdbcType="DECIMAL" property="inStoreSales14" />
    <result column="in_store_puramount_14" jdbcType="DECIMAL" property="inStorePuramount14" />
    <result column="in_store_sales_30" jdbcType="DECIMAL" property="inStoreSales30" />
    <result column="in_store_puramount_30" jdbcType="DECIMAL" property="inStorePuramount30" />
    <result column="in_store_sales_60" jdbcType="DECIMAL" property="inStoreSales60" />
    <result column="in_store_puramount_60" jdbcType="DECIMAL" property="inStorePuramount60" />
    <result column="in_store_sales_90" jdbcType="DECIMAL" property="inStoreSales90" />
    <result column="in_store_puramount_90" jdbcType="DECIMAL" property="inStorePuramount90" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, allot_date, allot_month, register_no, register_month, allot_type, pos_allot_no, 
    platform_org_id, platform_org_name, out_company_id, out_company_code, out_company_name, 
    in_company_id, in_company_code, in_company_name, out_store_id, out_store_code, out_store_name, 
    out_store_attr, out_store_sales_level, in_store_id, in_store_code, in_store_name, 
    in_store_attr, in_store_sales_level, allot_group_code, allot_group_name, goods_no, 
    goods_desc, goods_common_name, manufacturer, unit, real_allot_quantity, real_cost_amount, 
    allot_approve_time, in_approve_status, in_approve_time, in_allot_quantity, in_allot_cost_amount, 
    in_stock_time, in_store_sales_7, in_store_puramount_7, in_store_sales_14, in_store_puramount_14, 
    in_store_sales_30, in_store_puramount_30, in_store_sales_60, in_store_puramount_60, 
    in_store_sales_90, in_store_puramount_90, `status`, gmt_create, gmt_update, extend, 
    version, created_by, created_name, updated_by, updated_name
  </sql>
  <select id="selectByExample" parameterType="com.cowell.iscm.entityTidb.IscmSuggestDistexecDoneDetailExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from iscm_suggest_distexec_done_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from iscm_suggest_distexec_done_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from iscm_suggest_distexec_done_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.iscm.entityTidb.IscmSuggestDistexecDoneDetailExample">
    delete from iscm_suggest_distexec_done_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entityTidb.IscmSuggestDistexecDoneDetail" useGeneratedKeys="true">
    insert into iscm_suggest_distexec_done_detail (allot_date, allot_month, register_no, 
      register_month, allot_type, pos_allot_no, 
      platform_org_id, platform_org_name, out_company_id, 
      out_company_code, out_company_name, in_company_id, 
      in_company_code, in_company_name, out_store_id, 
      out_store_code, out_store_name, out_store_attr, 
      out_store_sales_level, in_store_id, in_store_code, 
      in_store_name, in_store_attr, in_store_sales_level, 
      allot_group_code, allot_group_name, goods_no, 
      goods_desc, goods_common_name, manufacturer, 
      unit, real_allot_quantity, real_cost_amount, 
      allot_approve_time, in_approve_status, in_approve_time, 
      in_allot_quantity, in_allot_cost_amount, in_stock_time, 
      in_store_sales_7, in_store_puramount_7, in_store_sales_14, 
      in_store_puramount_14, in_store_sales_30, in_store_puramount_30, 
      in_store_sales_60, in_store_puramount_60, in_store_sales_90, 
      in_store_puramount_90, `status`, gmt_create, 
      gmt_update, extend, version, 
      created_by, created_name, updated_by, 
      updated_name)
    values (#{allotDate,jdbcType=TIMESTAMP}, #{allotMonth,jdbcType=INTEGER}, #{registerNo,jdbcType=VARCHAR}, 
      #{registerMonth,jdbcType=INTEGER}, #{allotType,jdbcType=TINYINT}, #{posAllotNo,jdbcType=VARCHAR}, 
      #{platformOrgId,jdbcType=BIGINT}, #{platformOrgName,jdbcType=VARCHAR}, #{outCompanyId,jdbcType=BIGINT}, 
      #{outCompanyCode,jdbcType=VARCHAR}, #{outCompanyName,jdbcType=VARCHAR}, #{inCompanyId,jdbcType=BIGINT}, 
      #{inCompanyCode,jdbcType=VARCHAR}, #{inCompanyName,jdbcType=VARCHAR}, #{outStoreId,jdbcType=BIGINT}, 
      #{outStoreCode,jdbcType=VARCHAR}, #{outStoreName,jdbcType=VARCHAR}, #{outStoreAttr,jdbcType=VARCHAR}, 
      #{outStoreSalesLevel,jdbcType=VARCHAR}, #{inStoreId,jdbcType=BIGINT}, #{inStoreCode,jdbcType=VARCHAR}, 
      #{inStoreName,jdbcType=VARCHAR}, #{inStoreAttr,jdbcType=VARCHAR}, #{inStoreSalesLevel,jdbcType=VARCHAR}, 
      #{allotGroupCode,jdbcType=VARCHAR}, #{allotGroupName,jdbcType=VARCHAR}, #{goodsNo,jdbcType=VARCHAR}, 
      #{goodsDesc,jdbcType=VARCHAR}, #{goodsCommonName,jdbcType=VARCHAR}, #{manufacturer,jdbcType=VARCHAR}, 
      #{unit,jdbcType=VARCHAR}, #{realAllotQuantity,jdbcType=DECIMAL}, #{realCostAmount,jdbcType=DECIMAL}, 
      #{allotApproveTime,jdbcType=TIMESTAMP}, #{inApproveStatus,jdbcType=TINYINT}, #{inApproveTime,jdbcType=TIMESTAMP}, 
      #{inAllotQuantity,jdbcType=DECIMAL}, #{inAllotCostAmount,jdbcType=DECIMAL}, #{inStockTime,jdbcType=TIMESTAMP}, 
      #{inStoreSales7,jdbcType=DECIMAL}, #{inStorePuramount7,jdbcType=DECIMAL}, #{inStoreSales14,jdbcType=DECIMAL}, 
      #{inStorePuramount14,jdbcType=DECIMAL}, #{inStoreSales30,jdbcType=DECIMAL}, #{inStorePuramount30,jdbcType=DECIMAL}, 
      #{inStoreSales60,jdbcType=DECIMAL}, #{inStorePuramount60,jdbcType=DECIMAL}, #{inStoreSales90,jdbcType=DECIMAL}, 
      #{inStorePuramount90,jdbcType=DECIMAL}, #{status,jdbcType=TINYINT}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtUpdate,jdbcType=TIMESTAMP}, #{extend,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER}, 
      #{createdBy,jdbcType=BIGINT}, #{createdName,jdbcType=VARCHAR}, #{updatedBy,jdbcType=BIGINT}, 
      #{updatedName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entityTidb.IscmSuggestDistexecDoneDetail" useGeneratedKeys="true">
    insert into iscm_suggest_distexec_done_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="allotDate != null">
        allot_date,
      </if>
      <if test="allotMonth != null">
        allot_month,
      </if>
      <if test="registerNo != null">
        register_no,
      </if>
      <if test="registerMonth != null">
        register_month,
      </if>
      <if test="allotType != null">
        allot_type,
      </if>
      <if test="posAllotNo != null">
        pos_allot_no,
      </if>
      <if test="platformOrgId != null">
        platform_org_id,
      </if>
      <if test="platformOrgName != null">
        platform_org_name,
      </if>
      <if test="outCompanyId != null">
        out_company_id,
      </if>
      <if test="outCompanyCode != null">
        out_company_code,
      </if>
      <if test="outCompanyName != null">
        out_company_name,
      </if>
      <if test="inCompanyId != null">
        in_company_id,
      </if>
      <if test="inCompanyCode != null">
        in_company_code,
      </if>
      <if test="inCompanyName != null">
        in_company_name,
      </if>
      <if test="outStoreId != null">
        out_store_id,
      </if>
      <if test="outStoreCode != null">
        out_store_code,
      </if>
      <if test="outStoreName != null">
        out_store_name,
      </if>
      <if test="outStoreAttr != null">
        out_store_attr,
      </if>
      <if test="outStoreSalesLevel != null">
        out_store_sales_level,
      </if>
      <if test="inStoreId != null">
        in_store_id,
      </if>
      <if test="inStoreCode != null">
        in_store_code,
      </if>
      <if test="inStoreName != null">
        in_store_name,
      </if>
      <if test="inStoreAttr != null">
        in_store_attr,
      </if>
      <if test="inStoreSalesLevel != null">
        in_store_sales_level,
      </if>
      <if test="allotGroupCode != null">
        allot_group_code,
      </if>
      <if test="allotGroupName != null">
        allot_group_name,
      </if>
      <if test="goodsNo != null">
        goods_no,
      </if>
      <if test="goodsDesc != null">
        goods_desc,
      </if>
      <if test="goodsCommonName != null">
        goods_common_name,
      </if>
      <if test="manufacturer != null">
        manufacturer,
      </if>
      <if test="unit != null">
        unit,
      </if>
      <if test="realAllotQuantity != null">
        real_allot_quantity,
      </if>
      <if test="realCostAmount != null">
        real_cost_amount,
      </if>
      <if test="allotApproveTime != null">
        allot_approve_time,
      </if>
      <if test="inApproveStatus != null">
        in_approve_status,
      </if>
      <if test="inApproveTime != null">
        in_approve_time,
      </if>
      <if test="inAllotQuantity != null">
        in_allot_quantity,
      </if>
      <if test="inAllotCostAmount != null">
        in_allot_cost_amount,
      </if>
      <if test="inStockTime != null">
        in_stock_time,
      </if>
      <if test="inStoreSales7 != null">
        in_store_sales_7,
      </if>
      <if test="inStorePuramount7 != null">
        in_store_puramount_7,
      </if>
      <if test="inStoreSales14 != null">
        in_store_sales_14,
      </if>
      <if test="inStorePuramount14 != null">
        in_store_puramount_14,
      </if>
      <if test="inStoreSales30 != null">
        in_store_sales_30,
      </if>
      <if test="inStorePuramount30 != null">
        in_store_puramount_30,
      </if>
      <if test="inStoreSales60 != null">
        in_store_sales_60,
      </if>
      <if test="inStorePuramount60 != null">
        in_store_puramount_60,
      </if>
      <if test="inStoreSales90 != null">
        in_store_sales_90,
      </if>
      <if test="inStorePuramount90 != null">
        in_store_puramount_90,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedName != null">
        updated_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="allotDate != null">
        #{allotDate,jdbcType=TIMESTAMP},
      </if>
      <if test="allotMonth != null">
        #{allotMonth,jdbcType=INTEGER},
      </if>
      <if test="registerNo != null">
        #{registerNo,jdbcType=VARCHAR},
      </if>
      <if test="registerMonth != null">
        #{registerMonth,jdbcType=INTEGER},
      </if>
      <if test="allotType != null">
        #{allotType,jdbcType=TINYINT},
      </if>
      <if test="posAllotNo != null">
        #{posAllotNo,jdbcType=VARCHAR},
      </if>
      <if test="platformOrgId != null">
        #{platformOrgId,jdbcType=BIGINT},
      </if>
      <if test="platformOrgName != null">
        #{platformOrgName,jdbcType=VARCHAR},
      </if>
      <if test="outCompanyId != null">
        #{outCompanyId,jdbcType=BIGINT},
      </if>
      <if test="outCompanyCode != null">
        #{outCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="outCompanyName != null">
        #{outCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="inCompanyId != null">
        #{inCompanyId,jdbcType=BIGINT},
      </if>
      <if test="inCompanyCode != null">
        #{inCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="inCompanyName != null">
        #{inCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="outStoreId != null">
        #{outStoreId,jdbcType=BIGINT},
      </if>
      <if test="outStoreCode != null">
        #{outStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="outStoreName != null">
        #{outStoreName,jdbcType=VARCHAR},
      </if>
      <if test="outStoreAttr != null">
        #{outStoreAttr,jdbcType=VARCHAR},
      </if>
      <if test="outStoreSalesLevel != null">
        #{outStoreSalesLevel,jdbcType=VARCHAR},
      </if>
      <if test="inStoreId != null">
        #{inStoreId,jdbcType=BIGINT},
      </if>
      <if test="inStoreCode != null">
        #{inStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="inStoreName != null">
        #{inStoreName,jdbcType=VARCHAR},
      </if>
      <if test="inStoreAttr != null">
        #{inStoreAttr,jdbcType=VARCHAR},
      </if>
      <if test="inStoreSalesLevel != null">
        #{inStoreSalesLevel,jdbcType=VARCHAR},
      </if>
      <if test="allotGroupCode != null">
        #{allotGroupCode,jdbcType=VARCHAR},
      </if>
      <if test="allotGroupName != null">
        #{allotGroupName,jdbcType=VARCHAR},
      </if>
      <if test="goodsNo != null">
        #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="goodsDesc != null">
        #{goodsDesc,jdbcType=VARCHAR},
      </if>
      <if test="goodsCommonName != null">
        #{goodsCommonName,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="unit != null">
        #{unit,jdbcType=VARCHAR},
      </if>
      <if test="realAllotQuantity != null">
        #{realAllotQuantity,jdbcType=DECIMAL},
      </if>
      <if test="realCostAmount != null">
        #{realCostAmount,jdbcType=DECIMAL},
      </if>
      <if test="allotApproveTime != null">
        #{allotApproveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="inApproveStatus != null">
        #{inApproveStatus,jdbcType=TINYINT},
      </if>
      <if test="inApproveTime != null">
        #{inApproveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="inAllotQuantity != null">
        #{inAllotQuantity,jdbcType=DECIMAL},
      </if>
      <if test="inAllotCostAmount != null">
        #{inAllotCostAmount,jdbcType=DECIMAL},
      </if>
      <if test="inStockTime != null">
        #{inStockTime,jdbcType=TIMESTAMP},
      </if>
      <if test="inStoreSales7 != null">
        #{inStoreSales7,jdbcType=DECIMAL},
      </if>
      <if test="inStorePuramount7 != null">
        #{inStorePuramount7,jdbcType=DECIMAL},
      </if>
      <if test="inStoreSales14 != null">
        #{inStoreSales14,jdbcType=DECIMAL},
      </if>
      <if test="inStorePuramount14 != null">
        #{inStorePuramount14,jdbcType=DECIMAL},
      </if>
      <if test="inStoreSales30 != null">
        #{inStoreSales30,jdbcType=DECIMAL},
      </if>
      <if test="inStorePuramount30 != null">
        #{inStorePuramount30,jdbcType=DECIMAL},
      </if>
      <if test="inStoreSales60 != null">
        #{inStoreSales60,jdbcType=DECIMAL},
      </if>
      <if test="inStorePuramount60 != null">
        #{inStorePuramount60,jdbcType=DECIMAL},
      </if>
      <if test="inStoreSales90 != null">
        #{inStoreSales90,jdbcType=DECIMAL},
      </if>
      <if test="inStorePuramount90 != null">
        #{inStorePuramount90,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        #{updatedName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.iscm.entityTidb.IscmSuggestDistexecDoneDetailExample" resultType="java.lang.Long">
    select count(*) from iscm_suggest_distexec_done_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update iscm_suggest_distexec_done_detail
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.allotDate != null">
        allot_date = #{record.allotDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.allotMonth != null">
        allot_month = #{record.allotMonth,jdbcType=INTEGER},
      </if>
      <if test="record.registerNo != null">
        register_no = #{record.registerNo,jdbcType=VARCHAR},
      </if>
      <if test="record.registerMonth != null">
        register_month = #{record.registerMonth,jdbcType=INTEGER},
      </if>
      <if test="record.allotType != null">
        allot_type = #{record.allotType,jdbcType=TINYINT},
      </if>
      <if test="record.posAllotNo != null">
        pos_allot_no = #{record.posAllotNo,jdbcType=VARCHAR},
      </if>
      <if test="record.platformOrgId != null">
        platform_org_id = #{record.platformOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.platformOrgName != null">
        platform_org_name = #{record.platformOrgName,jdbcType=VARCHAR},
      </if>
      <if test="record.outCompanyId != null">
        out_company_id = #{record.outCompanyId,jdbcType=BIGINT},
      </if>
      <if test="record.outCompanyCode != null">
        out_company_code = #{record.outCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.outCompanyName != null">
        out_company_name = #{record.outCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="record.inCompanyId != null">
        in_company_id = #{record.inCompanyId,jdbcType=BIGINT},
      </if>
      <if test="record.inCompanyCode != null">
        in_company_code = #{record.inCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.inCompanyName != null">
        in_company_name = #{record.inCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="record.outStoreId != null">
        out_store_id = #{record.outStoreId,jdbcType=BIGINT},
      </if>
      <if test="record.outStoreCode != null">
        out_store_code = #{record.outStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="record.outStoreName != null">
        out_store_name = #{record.outStoreName,jdbcType=VARCHAR},
      </if>
      <if test="record.outStoreAttr != null">
        out_store_attr = #{record.outStoreAttr,jdbcType=VARCHAR},
      </if>
      <if test="record.outStoreSalesLevel != null">
        out_store_sales_level = #{record.outStoreSalesLevel,jdbcType=VARCHAR},
      </if>
      <if test="record.inStoreId != null">
        in_store_id = #{record.inStoreId,jdbcType=BIGINT},
      </if>
      <if test="record.inStoreCode != null">
        in_store_code = #{record.inStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="record.inStoreName != null">
        in_store_name = #{record.inStoreName,jdbcType=VARCHAR},
      </if>
      <if test="record.inStoreAttr != null">
        in_store_attr = #{record.inStoreAttr,jdbcType=VARCHAR},
      </if>
      <if test="record.inStoreSalesLevel != null">
        in_store_sales_level = #{record.inStoreSalesLevel,jdbcType=VARCHAR},
      </if>
      <if test="record.allotGroupCode != null">
        allot_group_code = #{record.allotGroupCode,jdbcType=VARCHAR},
      </if>
      <if test="record.allotGroupName != null">
        allot_group_name = #{record.allotGroupName,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsNo != null">
        goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsDesc != null">
        goods_desc = #{record.goodsDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsCommonName != null">
        goods_common_name = #{record.goodsCommonName,jdbcType=VARCHAR},
      </if>
      <if test="record.manufacturer != null">
        manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="record.unit != null">
        unit = #{record.unit,jdbcType=VARCHAR},
      </if>
      <if test="record.realAllotQuantity != null">
        real_allot_quantity = #{record.realAllotQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.realCostAmount != null">
        real_cost_amount = #{record.realCostAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.allotApproveTime != null">
        allot_approve_time = #{record.allotApproveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.inApproveStatus != null">
        in_approve_status = #{record.inApproveStatus,jdbcType=TINYINT},
      </if>
      <if test="record.inApproveTime != null">
        in_approve_time = #{record.inApproveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.inAllotQuantity != null">
        in_allot_quantity = #{record.inAllotQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.inAllotCostAmount != null">
        in_allot_cost_amount = #{record.inAllotCostAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.inStockTime != null">
        in_stock_time = #{record.inStockTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.inStoreSales7 != null">
        in_store_sales_7 = #{record.inStoreSales7,jdbcType=DECIMAL},
      </if>
      <if test="record.inStorePuramount7 != null">
        in_store_puramount_7 = #{record.inStorePuramount7,jdbcType=DECIMAL},
      </if>
      <if test="record.inStoreSales14 != null">
        in_store_sales_14 = #{record.inStoreSales14,jdbcType=DECIMAL},
      </if>
      <if test="record.inStorePuramount14 != null">
        in_store_puramount_14 = #{record.inStorePuramount14,jdbcType=DECIMAL},
      </if>
      <if test="record.inStoreSales30 != null">
        in_store_sales_30 = #{record.inStoreSales30,jdbcType=DECIMAL},
      </if>
      <if test="record.inStorePuramount30 != null">
        in_store_puramount_30 = #{record.inStorePuramount30,jdbcType=DECIMAL},
      </if>
      <if test="record.inStoreSales60 != null">
        in_store_sales_60 = #{record.inStoreSales60,jdbcType=DECIMAL},
      </if>
      <if test="record.inStorePuramount60 != null">
        in_store_puramount_60 = #{record.inStorePuramount60,jdbcType=DECIMAL},
      </if>
      <if test="record.inStoreSales90 != null">
        in_store_sales_90 = #{record.inStoreSales90,jdbcType=DECIMAL},
      </if>
      <if test="record.inStorePuramount90 != null">
        in_store_puramount_90 = #{record.inStorePuramount90,jdbcType=DECIMAL},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedName != null">
        updated_name = #{record.updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update iscm_suggest_distexec_done_detail
    set id = #{record.id,jdbcType=BIGINT},
      allot_date = #{record.allotDate,jdbcType=TIMESTAMP},
      allot_month = #{record.allotMonth,jdbcType=INTEGER},
      register_no = #{record.registerNo,jdbcType=VARCHAR},
      register_month = #{record.registerMonth,jdbcType=INTEGER},
      allot_type = #{record.allotType,jdbcType=TINYINT},
      pos_allot_no = #{record.posAllotNo,jdbcType=VARCHAR},
      platform_org_id = #{record.platformOrgId,jdbcType=BIGINT},
      platform_org_name = #{record.platformOrgName,jdbcType=VARCHAR},
      out_company_id = #{record.outCompanyId,jdbcType=BIGINT},
      out_company_code = #{record.outCompanyCode,jdbcType=VARCHAR},
      out_company_name = #{record.outCompanyName,jdbcType=VARCHAR},
      in_company_id = #{record.inCompanyId,jdbcType=BIGINT},
      in_company_code = #{record.inCompanyCode,jdbcType=VARCHAR},
      in_company_name = #{record.inCompanyName,jdbcType=VARCHAR},
      out_store_id = #{record.outStoreId,jdbcType=BIGINT},
      out_store_code = #{record.outStoreCode,jdbcType=VARCHAR},
      out_store_name = #{record.outStoreName,jdbcType=VARCHAR},
      out_store_attr = #{record.outStoreAttr,jdbcType=VARCHAR},
      out_store_sales_level = #{record.outStoreSalesLevel,jdbcType=VARCHAR},
      in_store_id = #{record.inStoreId,jdbcType=BIGINT},
      in_store_code = #{record.inStoreCode,jdbcType=VARCHAR},
      in_store_name = #{record.inStoreName,jdbcType=VARCHAR},
      in_store_attr = #{record.inStoreAttr,jdbcType=VARCHAR},
      in_store_sales_level = #{record.inStoreSalesLevel,jdbcType=VARCHAR},
      allot_group_code = #{record.allotGroupCode,jdbcType=VARCHAR},
      allot_group_name = #{record.allotGroupName,jdbcType=VARCHAR},
      goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      goods_desc = #{record.goodsDesc,jdbcType=VARCHAR},
      goods_common_name = #{record.goodsCommonName,jdbcType=VARCHAR},
      manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      unit = #{record.unit,jdbcType=VARCHAR},
      real_allot_quantity = #{record.realAllotQuantity,jdbcType=DECIMAL},
      real_cost_amount = #{record.realCostAmount,jdbcType=DECIMAL},
      allot_approve_time = #{record.allotApproveTime,jdbcType=TIMESTAMP},
      in_approve_status = #{record.inApproveStatus,jdbcType=TINYINT},
      in_approve_time = #{record.inApproveTime,jdbcType=TIMESTAMP},
      in_allot_quantity = #{record.inAllotQuantity,jdbcType=DECIMAL},
      in_allot_cost_amount = #{record.inAllotCostAmount,jdbcType=DECIMAL},
      in_stock_time = #{record.inStockTime,jdbcType=TIMESTAMP},
      in_store_sales_7 = #{record.inStoreSales7,jdbcType=DECIMAL},
      in_store_puramount_7 = #{record.inStorePuramount7,jdbcType=DECIMAL},
      in_store_sales_14 = #{record.inStoreSales14,jdbcType=DECIMAL},
      in_store_puramount_14 = #{record.inStorePuramount14,jdbcType=DECIMAL},
      in_store_sales_30 = #{record.inStoreSales30,jdbcType=DECIMAL},
      in_store_puramount_30 = #{record.inStorePuramount30,jdbcType=DECIMAL},
      in_store_sales_60 = #{record.inStoreSales60,jdbcType=DECIMAL},
      in_store_puramount_60 = #{record.inStorePuramount60,jdbcType=DECIMAL},
      in_store_sales_90 = #{record.inStoreSales90,jdbcType=DECIMAL},
      in_store_puramount_90 = #{record.inStorePuramount90,jdbcType=DECIMAL},
      `status` = #{record.status,jdbcType=TINYINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_name = #{record.createdName,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_name = #{record.updatedName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.iscm.entityTidb.IscmSuggestDistexecDoneDetail">
    update iscm_suggest_distexec_done_detail
    <set>
      <if test="allotDate != null">
        allot_date = #{allotDate,jdbcType=TIMESTAMP},
      </if>
      <if test="allotMonth != null">
        allot_month = #{allotMonth,jdbcType=INTEGER},
      </if>
      <if test="registerNo != null">
        register_no = #{registerNo,jdbcType=VARCHAR},
      </if>
      <if test="registerMonth != null">
        register_month = #{registerMonth,jdbcType=INTEGER},
      </if>
      <if test="allotType != null">
        allot_type = #{allotType,jdbcType=TINYINT},
      </if>
      <if test="posAllotNo != null">
        pos_allot_no = #{posAllotNo,jdbcType=VARCHAR},
      </if>
      <if test="platformOrgId != null">
        platform_org_id = #{platformOrgId,jdbcType=BIGINT},
      </if>
      <if test="platformOrgName != null">
        platform_org_name = #{platformOrgName,jdbcType=VARCHAR},
      </if>
      <if test="outCompanyId != null">
        out_company_id = #{outCompanyId,jdbcType=BIGINT},
      </if>
      <if test="outCompanyCode != null">
        out_company_code = #{outCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="outCompanyName != null">
        out_company_name = #{outCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="inCompanyId != null">
        in_company_id = #{inCompanyId,jdbcType=BIGINT},
      </if>
      <if test="inCompanyCode != null">
        in_company_code = #{inCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="inCompanyName != null">
        in_company_name = #{inCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="outStoreId != null">
        out_store_id = #{outStoreId,jdbcType=BIGINT},
      </if>
      <if test="outStoreCode != null">
        out_store_code = #{outStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="outStoreName != null">
        out_store_name = #{outStoreName,jdbcType=VARCHAR},
      </if>
      <if test="outStoreAttr != null">
        out_store_attr = #{outStoreAttr,jdbcType=VARCHAR},
      </if>
      <if test="outStoreSalesLevel != null">
        out_store_sales_level = #{outStoreSalesLevel,jdbcType=VARCHAR},
      </if>
      <if test="inStoreId != null">
        in_store_id = #{inStoreId,jdbcType=BIGINT},
      </if>
      <if test="inStoreCode != null">
        in_store_code = #{inStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="inStoreName != null">
        in_store_name = #{inStoreName,jdbcType=VARCHAR},
      </if>
      <if test="inStoreAttr != null">
        in_store_attr = #{inStoreAttr,jdbcType=VARCHAR},
      </if>
      <if test="inStoreSalesLevel != null">
        in_store_sales_level = #{inStoreSalesLevel,jdbcType=VARCHAR},
      </if>
      <if test="allotGroupCode != null">
        allot_group_code = #{allotGroupCode,jdbcType=VARCHAR},
      </if>
      <if test="allotGroupName != null">
        allot_group_name = #{allotGroupName,jdbcType=VARCHAR},
      </if>
      <if test="goodsNo != null">
        goods_no = #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="goodsDesc != null">
        goods_desc = #{goodsDesc,jdbcType=VARCHAR},
      </if>
      <if test="goodsCommonName != null">
        goods_common_name = #{goodsCommonName,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        manufacturer = #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="unit != null">
        unit = #{unit,jdbcType=VARCHAR},
      </if>
      <if test="realAllotQuantity != null">
        real_allot_quantity = #{realAllotQuantity,jdbcType=DECIMAL},
      </if>
      <if test="realCostAmount != null">
        real_cost_amount = #{realCostAmount,jdbcType=DECIMAL},
      </if>
      <if test="allotApproveTime != null">
        allot_approve_time = #{allotApproveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="inApproveStatus != null">
        in_approve_status = #{inApproveStatus,jdbcType=TINYINT},
      </if>
      <if test="inApproveTime != null">
        in_approve_time = #{inApproveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="inAllotQuantity != null">
        in_allot_quantity = #{inAllotQuantity,jdbcType=DECIMAL},
      </if>
      <if test="inAllotCostAmount != null">
        in_allot_cost_amount = #{inAllotCostAmount,jdbcType=DECIMAL},
      </if>
      <if test="inStockTime != null">
        in_stock_time = #{inStockTime,jdbcType=TIMESTAMP},
      </if>
      <if test="inStoreSales7 != null">
        in_store_sales_7 = #{inStoreSales7,jdbcType=DECIMAL},
      </if>
      <if test="inStorePuramount7 != null">
        in_store_puramount_7 = #{inStorePuramount7,jdbcType=DECIMAL},
      </if>
      <if test="inStoreSales14 != null">
        in_store_sales_14 = #{inStoreSales14,jdbcType=DECIMAL},
      </if>
      <if test="inStorePuramount14 != null">
        in_store_puramount_14 = #{inStorePuramount14,jdbcType=DECIMAL},
      </if>
      <if test="inStoreSales30 != null">
        in_store_sales_30 = #{inStoreSales30,jdbcType=DECIMAL},
      </if>
      <if test="inStorePuramount30 != null">
        in_store_puramount_30 = #{inStorePuramount30,jdbcType=DECIMAL},
      </if>
      <if test="inStoreSales60 != null">
        in_store_sales_60 = #{inStoreSales60,jdbcType=DECIMAL},
      </if>
      <if test="inStorePuramount60 != null">
        in_store_puramount_60 = #{inStorePuramount60,jdbcType=DECIMAL},
      </if>
      <if test="inStoreSales90 != null">
        in_store_sales_90 = #{inStoreSales90,jdbcType=DECIMAL},
      </if>
      <if test="inStorePuramount90 != null">
        in_store_puramount_90 = #{inStorePuramount90,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        updated_name = #{updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.iscm.entityTidb.IscmSuggestDistexecDoneDetail">
    update iscm_suggest_distexec_done_detail
    set allot_date = #{allotDate,jdbcType=TIMESTAMP},
      allot_month = #{allotMonth,jdbcType=INTEGER},
      register_no = #{registerNo,jdbcType=VARCHAR},
      register_month = #{registerMonth,jdbcType=INTEGER},
      allot_type = #{allotType,jdbcType=TINYINT},
      pos_allot_no = #{posAllotNo,jdbcType=VARCHAR},
      platform_org_id = #{platformOrgId,jdbcType=BIGINT},
      platform_org_name = #{platformOrgName,jdbcType=VARCHAR},
      out_company_id = #{outCompanyId,jdbcType=BIGINT},
      out_company_code = #{outCompanyCode,jdbcType=VARCHAR},
      out_company_name = #{outCompanyName,jdbcType=VARCHAR},
      in_company_id = #{inCompanyId,jdbcType=BIGINT},
      in_company_code = #{inCompanyCode,jdbcType=VARCHAR},
      in_company_name = #{inCompanyName,jdbcType=VARCHAR},
      out_store_id = #{outStoreId,jdbcType=BIGINT},
      out_store_code = #{outStoreCode,jdbcType=VARCHAR},
      out_store_name = #{outStoreName,jdbcType=VARCHAR},
      out_store_attr = #{outStoreAttr,jdbcType=VARCHAR},
      out_store_sales_level = #{outStoreSalesLevel,jdbcType=VARCHAR},
      in_store_id = #{inStoreId,jdbcType=BIGINT},
      in_store_code = #{inStoreCode,jdbcType=VARCHAR},
      in_store_name = #{inStoreName,jdbcType=VARCHAR},
      in_store_attr = #{inStoreAttr,jdbcType=VARCHAR},
      in_store_sales_level = #{inStoreSalesLevel,jdbcType=VARCHAR},
      allot_group_code = #{allotGroupCode,jdbcType=VARCHAR},
      allot_group_name = #{allotGroupName,jdbcType=VARCHAR},
      goods_no = #{goodsNo,jdbcType=VARCHAR},
      goods_desc = #{goodsDesc,jdbcType=VARCHAR},
      goods_common_name = #{goodsCommonName,jdbcType=VARCHAR},
      manufacturer = #{manufacturer,jdbcType=VARCHAR},
      unit = #{unit,jdbcType=VARCHAR},
      real_allot_quantity = #{realAllotQuantity,jdbcType=DECIMAL},
      real_cost_amount = #{realCostAmount,jdbcType=DECIMAL},
      allot_approve_time = #{allotApproveTime,jdbcType=TIMESTAMP},
      in_approve_status = #{inApproveStatus,jdbcType=TINYINT},
      in_approve_time = #{inApproveTime,jdbcType=TIMESTAMP},
      in_allot_quantity = #{inAllotQuantity,jdbcType=DECIMAL},
      in_allot_cost_amount = #{inAllotCostAmount,jdbcType=DECIMAL},
      in_stock_time = #{inStockTime,jdbcType=TIMESTAMP},
      in_store_sales_7 = #{inStoreSales7,jdbcType=DECIMAL},
      in_store_puramount_7 = #{inStorePuramount7,jdbcType=DECIMAL},
      in_store_sales_14 = #{inStoreSales14,jdbcType=DECIMAL},
      in_store_puramount_14 = #{inStorePuramount14,jdbcType=DECIMAL},
      in_store_sales_30 = #{inStoreSales30,jdbcType=DECIMAL},
      in_store_puramount_30 = #{inStorePuramount30,jdbcType=DECIMAL},
      in_store_sales_60 = #{inStoreSales60,jdbcType=DECIMAL},
      in_store_puramount_60 = #{inStorePuramount60,jdbcType=DECIMAL},
      in_store_sales_90 = #{inStoreSales90,jdbcType=DECIMAL},
      in_store_puramount_90 = #{inStorePuramount90,jdbcType=DECIMAL},
      `status` = #{status,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_name = #{createdName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_name = #{updatedName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>