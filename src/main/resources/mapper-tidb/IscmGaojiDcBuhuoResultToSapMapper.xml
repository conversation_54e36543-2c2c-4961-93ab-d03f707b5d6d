<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapperTidb.IscmGaojiDcBuhuoResultToSapMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entityTidb.IscmGaojiDcBuhuoResultToSap">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="dt" jdbcType="VARCHAR" property="dt" />
    <result column="werks" jdbcType="VARCHAR" property="werks" />
    <result column="matnr" jdbcType="VARCHAR" property="matnr" />
    <result column="avg_qty" jdbcType="VARCHAR" property="avgQty" />
    <result column="inactive_dc_stock" jdbcType="DECIMAL" property="inactiveDcStock" />
    <result column="inactive_store_stock" jdbcType="DECIMAL" property="inactiveStoreStock" />
    <result column="revise_total_block_stock2" jdbcType="DECIMAL" property="reviseTotalBlockStock2" />
    <result column="weiqingcaigou_7" jdbcType="DECIMAL" property="weiqingcaigou7" />
    <result column="inv_upper" jdbcType="DECIMAL" property="invUpper" />
    <result column="suggest_dhl" jdbcType="DECIMAL" property="suggestDhl" />
    <result column="store_cnts_in_stock" jdbcType="DECIMAL" property="storeCntsInStock" />
    <result column="total_applyqty" jdbcType="DECIMAL" property="totalApplyqty" />
    <result column="max_applyqty" jdbcType="DECIMAL" property="maxApplyqty" />
    <result column="midqty" jdbcType="DECIMAL" property="midqty" />
    <result column="maxqty" jdbcType="DECIMAL" property="maxqty" />
    <result column="stock_to_use_ratio" jdbcType="VARCHAR" property="stockToUseRatio" />
    <result column="lifnr" jdbcType="VARCHAR" property="lifnr" />
    <result column="dhd_b" jdbcType="DECIMAL" property="dhdB" />
    <result column="dc_s" jdbcType="DECIMAL" property="dcS" />
    <result column="dc_l" jdbcType="DECIMAL" property="dcL" />
    <result column="goods_level" jdbcType="VARCHAR" property="goodsLevel" />
    <result column="safety_stock" jdbcType="DECIMAL" property="safetyStock" />
    <result column="sum_r_v" jdbcType="DECIMAL" property="sumRV" />
    <result column="revise_total_block_stock1" jdbcType="DECIMAL" property="reviseTotalBlockStock1" />
    <result column="raw_suggest_dhl" jdbcType="DECIMAL" property="rawSuggestDhl" />
    <result column="znoncxb" jdbcType="VARCHAR" property="znoncxb" />
    <result column="total_dc_stock" jdbcType="VARCHAR" property="totalDcStock" />
    <result column="total_dc_disable_stock" jdbcType="VARCHAR" property="totalDcDisableStock" />
    <result column="total_store_stock" jdbcType="VARCHAR" property="totalStoreStock" />
    <result column="qty_before_30" jdbcType="VARCHAR" property="qtyBefore30" />
    <result column="hb_sale_rate" jdbcType="VARCHAR" property="hbSaleRate" />
    <result column="tb_sale_rate" jdbcType="VARCHAR" property="tbSaleRate" />
    <result column="zdxmds" jdbcType="DECIMAL" property="zdxmds" />
    <result column="dc_sale_days" jdbcType="DECIMAL" property="dcSaleDays" />
    <result column="dc_inv_sale_days" jdbcType="DECIMAL" property="dcInvSaleDays" />
    <result column="store_sale_days" jdbcType="DECIMAL" property="storeSaleDays" />
    <result column="store_inv_sale_days" jdbcType="DECIMAL" property="storeInvSaleDays" />
    <result column="puhuo_stores" jdbcType="DECIMAL" property="puhuoStores" />
    <result column="qty_before_7" jdbcType="DECIMAL" property="qtyBefore7" />
    <result column="avg_qty_before_7" jdbcType="VARCHAR" property="avgQtyBefore7" />
    <result column="qty_before_14" jdbcType="DECIMAL" property="qtyBefore14" />
    <result column="avg_qty_before_14" jdbcType="VARCHAR" property="avgQtyBefore14" />
    <result column="avg_qty_before_30" jdbcType="VARCHAR" property="avgQtyBefore30" />
    <result column="qty_before_90" jdbcType="DECIMAL" property="qtyBefore90" />
    <result column="avg_qty_before_90" jdbcType="VARCHAR" property="avgQtyBefore90" />
    <result column="distqty_before_7" jdbcType="DECIMAL" property="distqtyBefore7" />
    <result column="avg_distqty_before_7" jdbcType="VARCHAR" property="avgDistqtyBefore7" />
    <result column="distqty_before_14" jdbcType="DECIMAL" property="distqtyBefore14" />
    <result column="avg_distqty_before_14" jdbcType="VARCHAR" property="avgDistqtyBefore14" />
    <result column="distqty_30" jdbcType="DECIMAL" property="distqty30" />
    <result column="avg_distqty_before_30" jdbcType="VARCHAR" property="avgDistqtyBefore30" />
    <result column="distqty_before_90" jdbcType="DECIMAL" property="distqtyBefore90" />
    <result column="avg_distqty_before_90" jdbcType="VARCHAR" property="avgDistqtyBefore90" />
    <result column="qty_30_60" jdbcType="DECIMAL" property="qty3060" />
    <result column="qty_60_90" jdbcType="DECIMAL" property="qty6090" />
    <result column="distqty_30_60" jdbcType="DECIMAL" property="distqty3060" />
    <result column="distqty_60_90" jdbcType="DECIMAL" property="distqty6090" />
    <result column="seasonal_factor" jdbcType="VARCHAR" property="seasonalFactor" />
    <result column="jm_store_stock" jdbcType="DECIMAL" property="jmStoreStock" />
    <result column="dc_stock" jdbcType="DECIMAL" property="dcStock" />
    <result column="dc_disable_stock" jdbcType="DECIMAL" property="dcDisableStock" />
    <result column="zc_inactive_dc_stock" jdbcType="DECIMAL" property="zcInactiveDcStock" />
    <result column="dc_inv_upper" jdbcType="DECIMAL" property="dcInvUpper" />
    <result column="dc_block_stock1" jdbcType="DECIMAL" property="dcBlockStock1" />
    <result column="dc_block_stock2" jdbcType="DECIMAL" property="dcBlockStock2" />
    <result column="dist_days" jdbcType="DECIMAL" property="distDays" />
    <result column="sent_time" jdbcType="VARCHAR" property="sentTime" />
    <result column="amount_before_30" jdbcType="DECIMAL" property="amountBefore30" />
    <result column="unsatisfied_delivery_sku" jdbcType="DECIMAL" property="unsatisfiedDeliverySku" />
    <result column="unsatisfied_delivery_cnt" jdbcType="DECIMAL" property="unsatisfiedDeliveryCnt" />
    <result column="weiqingtuicang" jdbcType="VARCHAR" property="weiqingtuicang" />
    <result column="weiqingjisuan" jdbcType="VARCHAR" property="weiqingjisuan" />
    <result column="min_purchase_type" jdbcType="VARCHAR" property="minPurchaseType" />
    <result column="min_purchase_price" jdbcType="DECIMAL" property="minPurchasePrice" />
    <result column="min_purchase_date" jdbcType="VARCHAR" property="minPurchaseDate" />
    <result column="ZDCJE1" jdbcType="DECIMAL" property="zdcje1" />
    <result column="ZDCJE2" jdbcType="DECIMAL" property="zdcje2" />
    <result column="ZDCJE3" jdbcType="DECIMAL" property="zdcje3" />
    <result column="ZDCCB1" jdbcType="DECIMAL" property="zdccb1" />
    <result column="ZDCCB2" jdbcType="DECIMAL" property="zdccb2" />
    <result column="ZMDJE1" jdbcType="DECIMAL" property="zmdje1" />
    <result column="ZMDJE2" jdbcType="DECIMAL" property="zmdje2" />
    <result column="ZMDJE3" jdbcType="DECIMAL" property="zmdje3" />
    <result column="ZMDCB" jdbcType="DECIMAL" property="zmdcb" />
    <result column="ZCDZZTS" jdbcType="DECIMAL" property="zcdzzts" />
    <result column="ZCDKCJEPM" jdbcType="VARCHAR" property="zcdkcjepm" />
    <result column="ZDPT" jdbcType="VARCHAR" property="zdpt" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, dt, werks, matnr, avg_qty, inactive_dc_stock, inactive_store_stock, revise_total_block_stock2, 
    weiqingcaigou_7, inv_upper, suggest_dhl, store_cnts_in_stock, total_applyqty, max_applyqty, 
    midqty, maxqty, stock_to_use_ratio, lifnr, dhd_b, dc_s, dc_l, goods_level, safety_stock, 
    sum_r_v, revise_total_block_stock1, raw_suggest_dhl, znoncxb, total_dc_stock, total_dc_disable_stock, 
    total_store_stock, qty_before_30, hb_sale_rate, tb_sale_rate, zdxmds, dc_sale_days, 
    dc_inv_sale_days, store_sale_days, store_inv_sale_days, puhuo_stores, qty_before_7, 
    avg_qty_before_7, qty_before_14, avg_qty_before_14, avg_qty_before_30, qty_before_90, 
    avg_qty_before_90, distqty_before_7, avg_distqty_before_7, distqty_before_14, avg_distqty_before_14, 
    distqty_30, avg_distqty_before_30, distqty_before_90, avg_distqty_before_90, qty_30_60, 
    qty_60_90, distqty_30_60, distqty_60_90, seasonal_factor, jm_store_stock, dc_stock, 
    dc_disable_stock, zc_inactive_dc_stock, dc_inv_upper, dc_block_stock1, dc_block_stock2, 
    dist_days, sent_time, amount_before_30, unsatisfied_delivery_sku, unsatisfied_delivery_cnt, 
    weiqingtuicang, weiqingjisuan, min_purchase_type, min_purchase_price, min_purchase_date, 
    ZDCJE1, ZDCJE2, ZDCJE3, ZDCCB1, ZDCCB2, ZMDJE1, ZMDJE2, ZMDJE3, ZMDCB, ZCDZZTS, ZCDKCJEPM, 
    ZDPT
  </sql>
  <select id="selectByExample" parameterType="com.cowell.iscm.entityTidb.IscmGaojiDcBuhuoResultToSapExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from iscm_gaoji_dc_buhuo_result_to_sap
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from iscm_gaoji_dc_buhuo_result_to_sap
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from iscm_gaoji_dc_buhuo_result_to_sap
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.iscm.entityTidb.IscmGaojiDcBuhuoResultToSapExample">
    delete from iscm_gaoji_dc_buhuo_result_to_sap
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entityTidb.IscmGaojiDcBuhuoResultToSap" useGeneratedKeys="true">
    insert into iscm_gaoji_dc_buhuo_result_to_sap (dt, werks, matnr, 
      avg_qty, inactive_dc_stock, inactive_store_stock, 
      revise_total_block_stock2, weiqingcaigou_7, 
      inv_upper, suggest_dhl, store_cnts_in_stock, 
      total_applyqty, max_applyqty, midqty, 
      maxqty, stock_to_use_ratio, lifnr, 
      dhd_b, dc_s, dc_l, goods_level, 
      safety_stock, sum_r_v, revise_total_block_stock1, 
      raw_suggest_dhl, znoncxb, total_dc_stock, 
      total_dc_disable_stock, total_store_stock, qty_before_30, 
      hb_sale_rate, tb_sale_rate, zdxmds, 
      dc_sale_days, dc_inv_sale_days, store_sale_days, 
      store_inv_sale_days, puhuo_stores, qty_before_7, 
      avg_qty_before_7, qty_before_14, avg_qty_before_14, 
      avg_qty_before_30, qty_before_90, avg_qty_before_90, 
      distqty_before_7, avg_distqty_before_7, distqty_before_14, 
      avg_distqty_before_14, distqty_30, avg_distqty_before_30, 
      distqty_before_90, avg_distqty_before_90, qty_30_60, 
      qty_60_90, distqty_30_60, distqty_60_90, 
      seasonal_factor, jm_store_stock, dc_stock, 
      dc_disable_stock, zc_inactive_dc_stock, dc_inv_upper, 
      dc_block_stock1, dc_block_stock2, dist_days, 
      sent_time, amount_before_30, unsatisfied_delivery_sku, 
      unsatisfied_delivery_cnt, weiqingtuicang, 
      weiqingjisuan, min_purchase_type, min_purchase_price, 
      min_purchase_date, ZDCJE1, ZDCJE2, 
      ZDCJE3, ZDCCB1, ZDCCB2, 
      ZMDJE1, ZMDJE2, ZMDJE3, 
      ZMDCB, ZCDZZTS, ZCDKCJEPM, 
      ZDPT)
    values (#{dt,jdbcType=VARCHAR}, #{werks,jdbcType=VARCHAR}, #{matnr,jdbcType=VARCHAR}, 
      #{avgQty,jdbcType=VARCHAR}, #{inactiveDcStock,jdbcType=DECIMAL}, #{inactiveStoreStock,jdbcType=DECIMAL}, 
      #{reviseTotalBlockStock2,jdbcType=DECIMAL}, #{weiqingcaigou7,jdbcType=DECIMAL}, 
      #{invUpper,jdbcType=DECIMAL}, #{suggestDhl,jdbcType=DECIMAL}, #{storeCntsInStock,jdbcType=DECIMAL}, 
      #{totalApplyqty,jdbcType=DECIMAL}, #{maxApplyqty,jdbcType=DECIMAL}, #{midqty,jdbcType=DECIMAL}, 
      #{maxqty,jdbcType=DECIMAL}, #{stockToUseRatio,jdbcType=VARCHAR}, #{lifnr,jdbcType=VARCHAR}, 
      #{dhdB,jdbcType=DECIMAL}, #{dcS,jdbcType=DECIMAL}, #{dcL,jdbcType=DECIMAL}, #{goodsLevel,jdbcType=VARCHAR}, 
      #{safetyStock,jdbcType=DECIMAL}, #{sumRV,jdbcType=DECIMAL}, #{reviseTotalBlockStock1,jdbcType=DECIMAL}, 
      #{rawSuggestDhl,jdbcType=DECIMAL}, #{znoncxb,jdbcType=VARCHAR}, #{totalDcStock,jdbcType=VARCHAR}, 
      #{totalDcDisableStock,jdbcType=VARCHAR}, #{totalStoreStock,jdbcType=VARCHAR}, #{qtyBefore30,jdbcType=VARCHAR}, 
      #{hbSaleRate,jdbcType=VARCHAR}, #{tbSaleRate,jdbcType=VARCHAR}, #{zdxmds,jdbcType=DECIMAL}, 
      #{dcSaleDays,jdbcType=DECIMAL}, #{dcInvSaleDays,jdbcType=DECIMAL}, #{storeSaleDays,jdbcType=DECIMAL}, 
      #{storeInvSaleDays,jdbcType=DECIMAL}, #{puhuoStores,jdbcType=DECIMAL}, #{qtyBefore7,jdbcType=DECIMAL}, 
      #{avgQtyBefore7,jdbcType=VARCHAR}, #{qtyBefore14,jdbcType=DECIMAL}, #{avgQtyBefore14,jdbcType=VARCHAR}, 
      #{avgQtyBefore30,jdbcType=VARCHAR}, #{qtyBefore90,jdbcType=DECIMAL}, #{avgQtyBefore90,jdbcType=VARCHAR}, 
      #{distqtyBefore7,jdbcType=DECIMAL}, #{avgDistqtyBefore7,jdbcType=VARCHAR}, #{distqtyBefore14,jdbcType=DECIMAL}, 
      #{avgDistqtyBefore14,jdbcType=VARCHAR}, #{distqty30,jdbcType=DECIMAL}, #{avgDistqtyBefore30,jdbcType=VARCHAR}, 
      #{distqtyBefore90,jdbcType=DECIMAL}, #{avgDistqtyBefore90,jdbcType=VARCHAR}, #{qty3060,jdbcType=DECIMAL}, 
      #{qty6090,jdbcType=DECIMAL}, #{distqty3060,jdbcType=DECIMAL}, #{distqty6090,jdbcType=DECIMAL}, 
      #{seasonalFactor,jdbcType=VARCHAR}, #{jmStoreStock,jdbcType=DECIMAL}, #{dcStock,jdbcType=DECIMAL}, 
      #{dcDisableStock,jdbcType=DECIMAL}, #{zcInactiveDcStock,jdbcType=DECIMAL}, #{dcInvUpper,jdbcType=DECIMAL}, 
      #{dcBlockStock1,jdbcType=DECIMAL}, #{dcBlockStock2,jdbcType=DECIMAL}, #{distDays,jdbcType=DECIMAL}, 
      #{sentTime,jdbcType=VARCHAR}, #{amountBefore30,jdbcType=DECIMAL}, #{unsatisfiedDeliverySku,jdbcType=DECIMAL}, 
      #{unsatisfiedDeliveryCnt,jdbcType=DECIMAL}, #{weiqingtuicang,jdbcType=VARCHAR}, 
      #{weiqingjisuan,jdbcType=VARCHAR}, #{minPurchaseType,jdbcType=VARCHAR}, #{minPurchasePrice,jdbcType=DECIMAL}, 
      #{minPurchaseDate,jdbcType=VARCHAR}, #{zdcje1,jdbcType=DECIMAL}, #{zdcje2,jdbcType=DECIMAL}, 
      #{zdcje3,jdbcType=DECIMAL}, #{zdccb1,jdbcType=DECIMAL}, #{zdccb2,jdbcType=DECIMAL}, 
      #{zmdje1,jdbcType=DECIMAL}, #{zmdje2,jdbcType=DECIMAL}, #{zmdje3,jdbcType=DECIMAL}, 
      #{zmdcb,jdbcType=DECIMAL}, #{zcdzzts,jdbcType=DECIMAL}, #{zcdkcjepm,jdbcType=VARCHAR}, 
      #{zdpt,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entityTidb.IscmGaojiDcBuhuoResultToSap" useGeneratedKeys="true">
    insert into iscm_gaoji_dc_buhuo_result_to_sap
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="dt != null">
        dt,
      </if>
      <if test="werks != null">
        werks,
      </if>
      <if test="matnr != null">
        matnr,
      </if>
      <if test="avgQty != null">
        avg_qty,
      </if>
      <if test="inactiveDcStock != null">
        inactive_dc_stock,
      </if>
      <if test="inactiveStoreStock != null">
        inactive_store_stock,
      </if>
      <if test="reviseTotalBlockStock2 != null">
        revise_total_block_stock2,
      </if>
      <if test="weiqingcaigou7 != null">
        weiqingcaigou_7,
      </if>
      <if test="invUpper != null">
        inv_upper,
      </if>
      <if test="suggestDhl != null">
        suggest_dhl,
      </if>
      <if test="storeCntsInStock != null">
        store_cnts_in_stock,
      </if>
      <if test="totalApplyqty != null">
        total_applyqty,
      </if>
      <if test="maxApplyqty != null">
        max_applyqty,
      </if>
      <if test="midqty != null">
        midqty,
      </if>
      <if test="maxqty != null">
        maxqty,
      </if>
      <if test="stockToUseRatio != null">
        stock_to_use_ratio,
      </if>
      <if test="lifnr != null">
        lifnr,
      </if>
      <if test="dhdB != null">
        dhd_b,
      </if>
      <if test="dcS != null">
        dc_s,
      </if>
      <if test="dcL != null">
        dc_l,
      </if>
      <if test="goodsLevel != null">
        goods_level,
      </if>
      <if test="safetyStock != null">
        safety_stock,
      </if>
      <if test="sumRV != null">
        sum_r_v,
      </if>
      <if test="reviseTotalBlockStock1 != null">
        revise_total_block_stock1,
      </if>
      <if test="rawSuggestDhl != null">
        raw_suggest_dhl,
      </if>
      <if test="znoncxb != null">
        znoncxb,
      </if>
      <if test="totalDcStock != null">
        total_dc_stock,
      </if>
      <if test="totalDcDisableStock != null">
        total_dc_disable_stock,
      </if>
      <if test="totalStoreStock != null">
        total_store_stock,
      </if>
      <if test="qtyBefore30 != null">
        qty_before_30,
      </if>
      <if test="hbSaleRate != null">
        hb_sale_rate,
      </if>
      <if test="tbSaleRate != null">
        tb_sale_rate,
      </if>
      <if test="zdxmds != null">
        zdxmds,
      </if>
      <if test="dcSaleDays != null">
        dc_sale_days,
      </if>
      <if test="dcInvSaleDays != null">
        dc_inv_sale_days,
      </if>
      <if test="storeSaleDays != null">
        store_sale_days,
      </if>
      <if test="storeInvSaleDays != null">
        store_inv_sale_days,
      </if>
      <if test="puhuoStores != null">
        puhuo_stores,
      </if>
      <if test="qtyBefore7 != null">
        qty_before_7,
      </if>
      <if test="avgQtyBefore7 != null">
        avg_qty_before_7,
      </if>
      <if test="qtyBefore14 != null">
        qty_before_14,
      </if>
      <if test="avgQtyBefore14 != null">
        avg_qty_before_14,
      </if>
      <if test="avgQtyBefore30 != null">
        avg_qty_before_30,
      </if>
      <if test="qtyBefore90 != null">
        qty_before_90,
      </if>
      <if test="avgQtyBefore90 != null">
        avg_qty_before_90,
      </if>
      <if test="distqtyBefore7 != null">
        distqty_before_7,
      </if>
      <if test="avgDistqtyBefore7 != null">
        avg_distqty_before_7,
      </if>
      <if test="distqtyBefore14 != null">
        distqty_before_14,
      </if>
      <if test="avgDistqtyBefore14 != null">
        avg_distqty_before_14,
      </if>
      <if test="distqty30 != null">
        distqty_30,
      </if>
      <if test="avgDistqtyBefore30 != null">
        avg_distqty_before_30,
      </if>
      <if test="distqtyBefore90 != null">
        distqty_before_90,
      </if>
      <if test="avgDistqtyBefore90 != null">
        avg_distqty_before_90,
      </if>
      <if test="qty3060 != null">
        qty_30_60,
      </if>
      <if test="qty6090 != null">
        qty_60_90,
      </if>
      <if test="distqty3060 != null">
        distqty_30_60,
      </if>
      <if test="distqty6090 != null">
        distqty_60_90,
      </if>
      <if test="seasonalFactor != null">
        seasonal_factor,
      </if>
      <if test="jmStoreStock != null">
        jm_store_stock,
      </if>
      <if test="dcStock != null">
        dc_stock,
      </if>
      <if test="dcDisableStock != null">
        dc_disable_stock,
      </if>
      <if test="zcInactiveDcStock != null">
        zc_inactive_dc_stock,
      </if>
      <if test="dcInvUpper != null">
        dc_inv_upper,
      </if>
      <if test="dcBlockStock1 != null">
        dc_block_stock1,
      </if>
      <if test="dcBlockStock2 != null">
        dc_block_stock2,
      </if>
      <if test="distDays != null">
        dist_days,
      </if>
      <if test="sentTime != null">
        sent_time,
      </if>
      <if test="amountBefore30 != null">
        amount_before_30,
      </if>
      <if test="unsatisfiedDeliverySku != null">
        unsatisfied_delivery_sku,
      </if>
      <if test="unsatisfiedDeliveryCnt != null">
        unsatisfied_delivery_cnt,
      </if>
      <if test="weiqingtuicang != null">
        weiqingtuicang,
      </if>
      <if test="weiqingjisuan != null">
        weiqingjisuan,
      </if>
      <if test="minPurchaseType != null">
        min_purchase_type,
      </if>
      <if test="minPurchasePrice != null">
        min_purchase_price,
      </if>
      <if test="minPurchaseDate != null">
        min_purchase_date,
      </if>
      <if test="zdcje1 != null">
        ZDCJE1,
      </if>
      <if test="zdcje2 != null">
        ZDCJE2,
      </if>
      <if test="zdcje3 != null">
        ZDCJE3,
      </if>
      <if test="zdccb1 != null">
        ZDCCB1,
      </if>
      <if test="zdccb2 != null">
        ZDCCB2,
      </if>
      <if test="zmdje1 != null">
        ZMDJE1,
      </if>
      <if test="zmdje2 != null">
        ZMDJE2,
      </if>
      <if test="zmdje3 != null">
        ZMDJE3,
      </if>
      <if test="zmdcb != null">
        ZMDCB,
      </if>
      <if test="zcdzzts != null">
        ZCDZZTS,
      </if>
      <if test="zcdkcjepm != null">
        ZCDKCJEPM,
      </if>
      <if test="zdpt != null">
        ZDPT,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="dt != null">
        #{dt,jdbcType=VARCHAR},
      </if>
      <if test="werks != null">
        #{werks,jdbcType=VARCHAR},
      </if>
      <if test="matnr != null">
        #{matnr,jdbcType=VARCHAR},
      </if>
      <if test="avgQty != null">
        #{avgQty,jdbcType=VARCHAR},
      </if>
      <if test="inactiveDcStock != null">
        #{inactiveDcStock,jdbcType=DECIMAL},
      </if>
      <if test="inactiveStoreStock != null">
        #{inactiveStoreStock,jdbcType=DECIMAL},
      </if>
      <if test="reviseTotalBlockStock2 != null">
        #{reviseTotalBlockStock2,jdbcType=DECIMAL},
      </if>
      <if test="weiqingcaigou7 != null">
        #{weiqingcaigou7,jdbcType=DECIMAL},
      </if>
      <if test="invUpper != null">
        #{invUpper,jdbcType=DECIMAL},
      </if>
      <if test="suggestDhl != null">
        #{suggestDhl,jdbcType=DECIMAL},
      </if>
      <if test="storeCntsInStock != null">
        #{storeCntsInStock,jdbcType=DECIMAL},
      </if>
      <if test="totalApplyqty != null">
        #{totalApplyqty,jdbcType=DECIMAL},
      </if>
      <if test="maxApplyqty != null">
        #{maxApplyqty,jdbcType=DECIMAL},
      </if>
      <if test="midqty != null">
        #{midqty,jdbcType=DECIMAL},
      </if>
      <if test="maxqty != null">
        #{maxqty,jdbcType=DECIMAL},
      </if>
      <if test="stockToUseRatio != null">
        #{stockToUseRatio,jdbcType=VARCHAR},
      </if>
      <if test="lifnr != null">
        #{lifnr,jdbcType=VARCHAR},
      </if>
      <if test="dhdB != null">
        #{dhdB,jdbcType=DECIMAL},
      </if>
      <if test="dcS != null">
        #{dcS,jdbcType=DECIMAL},
      </if>
      <if test="dcL != null">
        #{dcL,jdbcType=DECIMAL},
      </if>
      <if test="goodsLevel != null">
        #{goodsLevel,jdbcType=VARCHAR},
      </if>
      <if test="safetyStock != null">
        #{safetyStock,jdbcType=DECIMAL},
      </if>
      <if test="sumRV != null">
        #{sumRV,jdbcType=DECIMAL},
      </if>
      <if test="reviseTotalBlockStock1 != null">
        #{reviseTotalBlockStock1,jdbcType=DECIMAL},
      </if>
      <if test="rawSuggestDhl != null">
        #{rawSuggestDhl,jdbcType=DECIMAL},
      </if>
      <if test="znoncxb != null">
        #{znoncxb,jdbcType=VARCHAR},
      </if>
      <if test="totalDcStock != null">
        #{totalDcStock,jdbcType=VARCHAR},
      </if>
      <if test="totalDcDisableStock != null">
        #{totalDcDisableStock,jdbcType=VARCHAR},
      </if>
      <if test="totalStoreStock != null">
        #{totalStoreStock,jdbcType=VARCHAR},
      </if>
      <if test="qtyBefore30 != null">
        #{qtyBefore30,jdbcType=VARCHAR},
      </if>
      <if test="hbSaleRate != null">
        #{hbSaleRate,jdbcType=VARCHAR},
      </if>
      <if test="tbSaleRate != null">
        #{tbSaleRate,jdbcType=VARCHAR},
      </if>
      <if test="zdxmds != null">
        #{zdxmds,jdbcType=DECIMAL},
      </if>
      <if test="dcSaleDays != null">
        #{dcSaleDays,jdbcType=DECIMAL},
      </if>
      <if test="dcInvSaleDays != null">
        #{dcInvSaleDays,jdbcType=DECIMAL},
      </if>
      <if test="storeSaleDays != null">
        #{storeSaleDays,jdbcType=DECIMAL},
      </if>
      <if test="storeInvSaleDays != null">
        #{storeInvSaleDays,jdbcType=DECIMAL},
      </if>
      <if test="puhuoStores != null">
        #{puhuoStores,jdbcType=DECIMAL},
      </if>
      <if test="qtyBefore7 != null">
        #{qtyBefore7,jdbcType=DECIMAL},
      </if>
      <if test="avgQtyBefore7 != null">
        #{avgQtyBefore7,jdbcType=VARCHAR},
      </if>
      <if test="qtyBefore14 != null">
        #{qtyBefore14,jdbcType=DECIMAL},
      </if>
      <if test="avgQtyBefore14 != null">
        #{avgQtyBefore14,jdbcType=VARCHAR},
      </if>
      <if test="avgQtyBefore30 != null">
        #{avgQtyBefore30,jdbcType=VARCHAR},
      </if>
      <if test="qtyBefore90 != null">
        #{qtyBefore90,jdbcType=DECIMAL},
      </if>
      <if test="avgQtyBefore90 != null">
        #{avgQtyBefore90,jdbcType=VARCHAR},
      </if>
      <if test="distqtyBefore7 != null">
        #{distqtyBefore7,jdbcType=DECIMAL},
      </if>
      <if test="avgDistqtyBefore7 != null">
        #{avgDistqtyBefore7,jdbcType=VARCHAR},
      </if>
      <if test="distqtyBefore14 != null">
        #{distqtyBefore14,jdbcType=DECIMAL},
      </if>
      <if test="avgDistqtyBefore14 != null">
        #{avgDistqtyBefore14,jdbcType=VARCHAR},
      </if>
      <if test="distqty30 != null">
        #{distqty30,jdbcType=DECIMAL},
      </if>
      <if test="avgDistqtyBefore30 != null">
        #{avgDistqtyBefore30,jdbcType=VARCHAR},
      </if>
      <if test="distqtyBefore90 != null">
        #{distqtyBefore90,jdbcType=DECIMAL},
      </if>
      <if test="avgDistqtyBefore90 != null">
        #{avgDistqtyBefore90,jdbcType=VARCHAR},
      </if>
      <if test="qty3060 != null">
        #{qty3060,jdbcType=DECIMAL},
      </if>
      <if test="qty6090 != null">
        #{qty6090,jdbcType=DECIMAL},
      </if>
      <if test="distqty3060 != null">
        #{distqty3060,jdbcType=DECIMAL},
      </if>
      <if test="distqty6090 != null">
        #{distqty6090,jdbcType=DECIMAL},
      </if>
      <if test="seasonalFactor != null">
        #{seasonalFactor,jdbcType=VARCHAR},
      </if>
      <if test="jmStoreStock != null">
        #{jmStoreStock,jdbcType=DECIMAL},
      </if>
      <if test="dcStock != null">
        #{dcStock,jdbcType=DECIMAL},
      </if>
      <if test="dcDisableStock != null">
        #{dcDisableStock,jdbcType=DECIMAL},
      </if>
      <if test="zcInactiveDcStock != null">
        #{zcInactiveDcStock,jdbcType=DECIMAL},
      </if>
      <if test="dcInvUpper != null">
        #{dcInvUpper,jdbcType=DECIMAL},
      </if>
      <if test="dcBlockStock1 != null">
        #{dcBlockStock1,jdbcType=DECIMAL},
      </if>
      <if test="dcBlockStock2 != null">
        #{dcBlockStock2,jdbcType=DECIMAL},
      </if>
      <if test="distDays != null">
        #{distDays,jdbcType=DECIMAL},
      </if>
      <if test="sentTime != null">
        #{sentTime,jdbcType=VARCHAR},
      </if>
      <if test="amountBefore30 != null">
        #{amountBefore30,jdbcType=DECIMAL},
      </if>
      <if test="unsatisfiedDeliverySku != null">
        #{unsatisfiedDeliverySku,jdbcType=DECIMAL},
      </if>
      <if test="unsatisfiedDeliveryCnt != null">
        #{unsatisfiedDeliveryCnt,jdbcType=DECIMAL},
      </if>
      <if test="weiqingtuicang != null">
        #{weiqingtuicang,jdbcType=VARCHAR},
      </if>
      <if test="weiqingjisuan != null">
        #{weiqingjisuan,jdbcType=VARCHAR},
      </if>
      <if test="minPurchaseType != null">
        #{minPurchaseType,jdbcType=VARCHAR},
      </if>
      <if test="minPurchasePrice != null">
        #{minPurchasePrice,jdbcType=DECIMAL},
      </if>
      <if test="minPurchaseDate != null">
        #{minPurchaseDate,jdbcType=VARCHAR},
      </if>
      <if test="zdcje1 != null">
        #{zdcje1,jdbcType=DECIMAL},
      </if>
      <if test="zdcje2 != null">
        #{zdcje2,jdbcType=DECIMAL},
      </if>
      <if test="zdcje3 != null">
        #{zdcje3,jdbcType=DECIMAL},
      </if>
      <if test="zdccb1 != null">
        #{zdccb1,jdbcType=DECIMAL},
      </if>
      <if test="zdccb2 != null">
        #{zdccb2,jdbcType=DECIMAL},
      </if>
      <if test="zmdje1 != null">
        #{zmdje1,jdbcType=DECIMAL},
      </if>
      <if test="zmdje2 != null">
        #{zmdje2,jdbcType=DECIMAL},
      </if>
      <if test="zmdje3 != null">
        #{zmdje3,jdbcType=DECIMAL},
      </if>
      <if test="zmdcb != null">
        #{zmdcb,jdbcType=DECIMAL},
      </if>
      <if test="zcdzzts != null">
        #{zcdzzts,jdbcType=DECIMAL},
      </if>
      <if test="zcdkcjepm != null">
        #{zcdkcjepm,jdbcType=VARCHAR},
      </if>
      <if test="zdpt != null">
        #{zdpt,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.iscm.entityTidb.IscmGaojiDcBuhuoResultToSapExample" resultType="java.lang.Long">
    select count(*) from iscm_gaoji_dc_buhuo_result_to_sap
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update iscm_gaoji_dc_buhuo_result_to_sap
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.dt != null">
        dt = #{record.dt,jdbcType=VARCHAR},
      </if>
      <if test="record.werks != null">
        werks = #{record.werks,jdbcType=VARCHAR},
      </if>
      <if test="record.matnr != null">
        matnr = #{record.matnr,jdbcType=VARCHAR},
      </if>
      <if test="record.avgQty != null">
        avg_qty = #{record.avgQty,jdbcType=VARCHAR},
      </if>
      <if test="record.inactiveDcStock != null">
        inactive_dc_stock = #{record.inactiveDcStock,jdbcType=DECIMAL},
      </if>
      <if test="record.inactiveStoreStock != null">
        inactive_store_stock = #{record.inactiveStoreStock,jdbcType=DECIMAL},
      </if>
      <if test="record.reviseTotalBlockStock2 != null">
        revise_total_block_stock2 = #{record.reviseTotalBlockStock2,jdbcType=DECIMAL},
      </if>
      <if test="record.weiqingcaigou7 != null">
        weiqingcaigou_7 = #{record.weiqingcaigou7,jdbcType=DECIMAL},
      </if>
      <if test="record.invUpper != null">
        inv_upper = #{record.invUpper,jdbcType=DECIMAL},
      </if>
      <if test="record.suggestDhl != null">
        suggest_dhl = #{record.suggestDhl,jdbcType=DECIMAL},
      </if>
      <if test="record.storeCntsInStock != null">
        store_cnts_in_stock = #{record.storeCntsInStock,jdbcType=DECIMAL},
      </if>
      <if test="record.totalApplyqty != null">
        total_applyqty = #{record.totalApplyqty,jdbcType=DECIMAL},
      </if>
      <if test="record.maxApplyqty != null">
        max_applyqty = #{record.maxApplyqty,jdbcType=DECIMAL},
      </if>
      <if test="record.midqty != null">
        midqty = #{record.midqty,jdbcType=DECIMAL},
      </if>
      <if test="record.maxqty != null">
        maxqty = #{record.maxqty,jdbcType=DECIMAL},
      </if>
      <if test="record.stockToUseRatio != null">
        stock_to_use_ratio = #{record.stockToUseRatio,jdbcType=VARCHAR},
      </if>
      <if test="record.lifnr != null">
        lifnr = #{record.lifnr,jdbcType=VARCHAR},
      </if>
      <if test="record.dhdB != null">
        dhd_b = #{record.dhdB,jdbcType=DECIMAL},
      </if>
      <if test="record.dcS != null">
        dc_s = #{record.dcS,jdbcType=DECIMAL},
      </if>
      <if test="record.dcL != null">
        dc_l = #{record.dcL,jdbcType=DECIMAL},
      </if>
      <if test="record.goodsLevel != null">
        goods_level = #{record.goodsLevel,jdbcType=VARCHAR},
      </if>
      <if test="record.safetyStock != null">
        safety_stock = #{record.safetyStock,jdbcType=DECIMAL},
      </if>
      <if test="record.sumRV != null">
        sum_r_v = #{record.sumRV,jdbcType=DECIMAL},
      </if>
      <if test="record.reviseTotalBlockStock1 != null">
        revise_total_block_stock1 = #{record.reviseTotalBlockStock1,jdbcType=DECIMAL},
      </if>
      <if test="record.rawSuggestDhl != null">
        raw_suggest_dhl = #{record.rawSuggestDhl,jdbcType=DECIMAL},
      </if>
      <if test="record.znoncxb != null">
        znoncxb = #{record.znoncxb,jdbcType=VARCHAR},
      </if>
      <if test="record.totalDcStock != null">
        total_dc_stock = #{record.totalDcStock,jdbcType=VARCHAR},
      </if>
      <if test="record.totalDcDisableStock != null">
        total_dc_disable_stock = #{record.totalDcDisableStock,jdbcType=VARCHAR},
      </if>
      <if test="record.totalStoreStock != null">
        total_store_stock = #{record.totalStoreStock,jdbcType=VARCHAR},
      </if>
      <if test="record.qtyBefore30 != null">
        qty_before_30 = #{record.qtyBefore30,jdbcType=VARCHAR},
      </if>
      <if test="record.hbSaleRate != null">
        hb_sale_rate = #{record.hbSaleRate,jdbcType=VARCHAR},
      </if>
      <if test="record.tbSaleRate != null">
        tb_sale_rate = #{record.tbSaleRate,jdbcType=VARCHAR},
      </if>
      <if test="record.zdxmds != null">
        zdxmds = #{record.zdxmds,jdbcType=DECIMAL},
      </if>
      <if test="record.dcSaleDays != null">
        dc_sale_days = #{record.dcSaleDays,jdbcType=DECIMAL},
      </if>
      <if test="record.dcInvSaleDays != null">
        dc_inv_sale_days = #{record.dcInvSaleDays,jdbcType=DECIMAL},
      </if>
      <if test="record.storeSaleDays != null">
        store_sale_days = #{record.storeSaleDays,jdbcType=DECIMAL},
      </if>
      <if test="record.storeInvSaleDays != null">
        store_inv_sale_days = #{record.storeInvSaleDays,jdbcType=DECIMAL},
      </if>
      <if test="record.puhuoStores != null">
        puhuo_stores = #{record.puhuoStores,jdbcType=DECIMAL},
      </if>
      <if test="record.qtyBefore7 != null">
        qty_before_7 = #{record.qtyBefore7,jdbcType=DECIMAL},
      </if>
      <if test="record.avgQtyBefore7 != null">
        avg_qty_before_7 = #{record.avgQtyBefore7,jdbcType=VARCHAR},
      </if>
      <if test="record.qtyBefore14 != null">
        qty_before_14 = #{record.qtyBefore14,jdbcType=DECIMAL},
      </if>
      <if test="record.avgQtyBefore14 != null">
        avg_qty_before_14 = #{record.avgQtyBefore14,jdbcType=VARCHAR},
      </if>
      <if test="record.avgQtyBefore30 != null">
        avg_qty_before_30 = #{record.avgQtyBefore30,jdbcType=VARCHAR},
      </if>
      <if test="record.qtyBefore90 != null">
        qty_before_90 = #{record.qtyBefore90,jdbcType=DECIMAL},
      </if>
      <if test="record.avgQtyBefore90 != null">
        avg_qty_before_90 = #{record.avgQtyBefore90,jdbcType=VARCHAR},
      </if>
      <if test="record.distqtyBefore7 != null">
        distqty_before_7 = #{record.distqtyBefore7,jdbcType=DECIMAL},
      </if>
      <if test="record.avgDistqtyBefore7 != null">
        avg_distqty_before_7 = #{record.avgDistqtyBefore7,jdbcType=VARCHAR},
      </if>
      <if test="record.distqtyBefore14 != null">
        distqty_before_14 = #{record.distqtyBefore14,jdbcType=DECIMAL},
      </if>
      <if test="record.avgDistqtyBefore14 != null">
        avg_distqty_before_14 = #{record.avgDistqtyBefore14,jdbcType=VARCHAR},
      </if>
      <if test="record.distqty30 != null">
        distqty_30 = #{record.distqty30,jdbcType=DECIMAL},
      </if>
      <if test="record.avgDistqtyBefore30 != null">
        avg_distqty_before_30 = #{record.avgDistqtyBefore30,jdbcType=VARCHAR},
      </if>
      <if test="record.distqtyBefore90 != null">
        distqty_before_90 = #{record.distqtyBefore90,jdbcType=DECIMAL},
      </if>
      <if test="record.avgDistqtyBefore90 != null">
        avg_distqty_before_90 = #{record.avgDistqtyBefore90,jdbcType=VARCHAR},
      </if>
      <if test="record.qty3060 != null">
        qty_30_60 = #{record.qty3060,jdbcType=DECIMAL},
      </if>
      <if test="record.qty6090 != null">
        qty_60_90 = #{record.qty6090,jdbcType=DECIMAL},
      </if>
      <if test="record.distqty3060 != null">
        distqty_30_60 = #{record.distqty3060,jdbcType=DECIMAL},
      </if>
      <if test="record.distqty6090 != null">
        distqty_60_90 = #{record.distqty6090,jdbcType=DECIMAL},
      </if>
      <if test="record.seasonalFactor != null">
        seasonal_factor = #{record.seasonalFactor,jdbcType=VARCHAR},
      </if>
      <if test="record.jmStoreStock != null">
        jm_store_stock = #{record.jmStoreStock,jdbcType=DECIMAL},
      </if>
      <if test="record.dcStock != null">
        dc_stock = #{record.dcStock,jdbcType=DECIMAL},
      </if>
      <if test="record.dcDisableStock != null">
        dc_disable_stock = #{record.dcDisableStock,jdbcType=DECIMAL},
      </if>
      <if test="record.zcInactiveDcStock != null">
        zc_inactive_dc_stock = #{record.zcInactiveDcStock,jdbcType=DECIMAL},
      </if>
      <if test="record.dcInvUpper != null">
        dc_inv_upper = #{record.dcInvUpper,jdbcType=DECIMAL},
      </if>
      <if test="record.dcBlockStock1 != null">
        dc_block_stock1 = #{record.dcBlockStock1,jdbcType=DECIMAL},
      </if>
      <if test="record.dcBlockStock2 != null">
        dc_block_stock2 = #{record.dcBlockStock2,jdbcType=DECIMAL},
      </if>
      <if test="record.distDays != null">
        dist_days = #{record.distDays,jdbcType=DECIMAL},
      </if>
      <if test="record.sentTime != null">
        sent_time = #{record.sentTime,jdbcType=VARCHAR},
      </if>
      <if test="record.amountBefore30 != null">
        amount_before_30 = #{record.amountBefore30,jdbcType=DECIMAL},
      </if>
      <if test="record.unsatisfiedDeliverySku != null">
        unsatisfied_delivery_sku = #{record.unsatisfiedDeliverySku,jdbcType=DECIMAL},
      </if>
      <if test="record.unsatisfiedDeliveryCnt != null">
        unsatisfied_delivery_cnt = #{record.unsatisfiedDeliveryCnt,jdbcType=DECIMAL},
      </if>
      <if test="record.weiqingtuicang != null">
        weiqingtuicang = #{record.weiqingtuicang,jdbcType=VARCHAR},
      </if>
      <if test="record.weiqingjisuan != null">
        weiqingjisuan = #{record.weiqingjisuan,jdbcType=VARCHAR},
      </if>
      <if test="record.minPurchaseType != null">
        min_purchase_type = #{record.minPurchaseType,jdbcType=VARCHAR},
      </if>
      <if test="record.minPurchasePrice != null">
        min_purchase_price = #{record.minPurchasePrice,jdbcType=DECIMAL},
      </if>
      <if test="record.minPurchaseDate != null">
        min_purchase_date = #{record.minPurchaseDate,jdbcType=VARCHAR},
      </if>
      <if test="record.zdcje1 != null">
        ZDCJE1 = #{record.zdcje1,jdbcType=DECIMAL},
      </if>
      <if test="record.zdcje2 != null">
        ZDCJE2 = #{record.zdcje2,jdbcType=DECIMAL},
      </if>
      <if test="record.zdcje3 != null">
        ZDCJE3 = #{record.zdcje3,jdbcType=DECIMAL},
      </if>
      <if test="record.zdccb1 != null">
        ZDCCB1 = #{record.zdccb1,jdbcType=DECIMAL},
      </if>
      <if test="record.zdccb2 != null">
        ZDCCB2 = #{record.zdccb2,jdbcType=DECIMAL},
      </if>
      <if test="record.zmdje1 != null">
        ZMDJE1 = #{record.zmdje1,jdbcType=DECIMAL},
      </if>
      <if test="record.zmdje2 != null">
        ZMDJE2 = #{record.zmdje2,jdbcType=DECIMAL},
      </if>
      <if test="record.zmdje3 != null">
        ZMDJE3 = #{record.zmdje3,jdbcType=DECIMAL},
      </if>
      <if test="record.zmdcb != null">
        ZMDCB = #{record.zmdcb,jdbcType=DECIMAL},
      </if>
      <if test="record.zcdzzts != null">
        ZCDZZTS = #{record.zcdzzts,jdbcType=DECIMAL},
      </if>
      <if test="record.zcdkcjepm != null">
        ZCDKCJEPM = #{record.zcdkcjepm,jdbcType=VARCHAR},
      </if>
      <if test="record.zdpt != null">
        ZDPT = #{record.zdpt,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update iscm_gaoji_dc_buhuo_result_to_sap
    set id = #{record.id,jdbcType=BIGINT},
      dt = #{record.dt,jdbcType=VARCHAR},
      werks = #{record.werks,jdbcType=VARCHAR},
      matnr = #{record.matnr,jdbcType=VARCHAR},
      avg_qty = #{record.avgQty,jdbcType=VARCHAR},
      inactive_dc_stock = #{record.inactiveDcStock,jdbcType=DECIMAL},
      inactive_store_stock = #{record.inactiveStoreStock,jdbcType=DECIMAL},
      revise_total_block_stock2 = #{record.reviseTotalBlockStock2,jdbcType=DECIMAL},
      weiqingcaigou_7 = #{record.weiqingcaigou7,jdbcType=DECIMAL},
      inv_upper = #{record.invUpper,jdbcType=DECIMAL},
      suggest_dhl = #{record.suggestDhl,jdbcType=DECIMAL},
      store_cnts_in_stock = #{record.storeCntsInStock,jdbcType=DECIMAL},
      total_applyqty = #{record.totalApplyqty,jdbcType=DECIMAL},
      max_applyqty = #{record.maxApplyqty,jdbcType=DECIMAL},
      midqty = #{record.midqty,jdbcType=DECIMAL},
      maxqty = #{record.maxqty,jdbcType=DECIMAL},
      stock_to_use_ratio = #{record.stockToUseRatio,jdbcType=VARCHAR},
      lifnr = #{record.lifnr,jdbcType=VARCHAR},
      dhd_b = #{record.dhdB,jdbcType=DECIMAL},
      dc_s = #{record.dcS,jdbcType=DECIMAL},
      dc_l = #{record.dcL,jdbcType=DECIMAL},
      goods_level = #{record.goodsLevel,jdbcType=VARCHAR},
      safety_stock = #{record.safetyStock,jdbcType=DECIMAL},
      sum_r_v = #{record.sumRV,jdbcType=DECIMAL},
      revise_total_block_stock1 = #{record.reviseTotalBlockStock1,jdbcType=DECIMAL},
      raw_suggest_dhl = #{record.rawSuggestDhl,jdbcType=DECIMAL},
      znoncxb = #{record.znoncxb,jdbcType=VARCHAR},
      total_dc_stock = #{record.totalDcStock,jdbcType=VARCHAR},
      total_dc_disable_stock = #{record.totalDcDisableStock,jdbcType=VARCHAR},
      total_store_stock = #{record.totalStoreStock,jdbcType=VARCHAR},
      qty_before_30 = #{record.qtyBefore30,jdbcType=VARCHAR},
      hb_sale_rate = #{record.hbSaleRate,jdbcType=VARCHAR},
      tb_sale_rate = #{record.tbSaleRate,jdbcType=VARCHAR},
      zdxmds = #{record.zdxmds,jdbcType=DECIMAL},
      dc_sale_days = #{record.dcSaleDays,jdbcType=DECIMAL},
      dc_inv_sale_days = #{record.dcInvSaleDays,jdbcType=DECIMAL},
      store_sale_days = #{record.storeSaleDays,jdbcType=DECIMAL},
      store_inv_sale_days = #{record.storeInvSaleDays,jdbcType=DECIMAL},
      puhuo_stores = #{record.puhuoStores,jdbcType=DECIMAL},
      qty_before_7 = #{record.qtyBefore7,jdbcType=DECIMAL},
      avg_qty_before_7 = #{record.avgQtyBefore7,jdbcType=VARCHAR},
      qty_before_14 = #{record.qtyBefore14,jdbcType=DECIMAL},
      avg_qty_before_14 = #{record.avgQtyBefore14,jdbcType=VARCHAR},
      avg_qty_before_30 = #{record.avgQtyBefore30,jdbcType=VARCHAR},
      qty_before_90 = #{record.qtyBefore90,jdbcType=DECIMAL},
      avg_qty_before_90 = #{record.avgQtyBefore90,jdbcType=VARCHAR},
      distqty_before_7 = #{record.distqtyBefore7,jdbcType=DECIMAL},
      avg_distqty_before_7 = #{record.avgDistqtyBefore7,jdbcType=VARCHAR},
      distqty_before_14 = #{record.distqtyBefore14,jdbcType=DECIMAL},
      avg_distqty_before_14 = #{record.avgDistqtyBefore14,jdbcType=VARCHAR},
      distqty_30 = #{record.distqty30,jdbcType=DECIMAL},
      avg_distqty_before_30 = #{record.avgDistqtyBefore30,jdbcType=VARCHAR},
      distqty_before_90 = #{record.distqtyBefore90,jdbcType=DECIMAL},
      avg_distqty_before_90 = #{record.avgDistqtyBefore90,jdbcType=VARCHAR},
      qty_30_60 = #{record.qty3060,jdbcType=DECIMAL},
      qty_60_90 = #{record.qty6090,jdbcType=DECIMAL},
      distqty_30_60 = #{record.distqty3060,jdbcType=DECIMAL},
      distqty_60_90 = #{record.distqty6090,jdbcType=DECIMAL},
      seasonal_factor = #{record.seasonalFactor,jdbcType=VARCHAR},
      jm_store_stock = #{record.jmStoreStock,jdbcType=DECIMAL},
      dc_stock = #{record.dcStock,jdbcType=DECIMAL},
      dc_disable_stock = #{record.dcDisableStock,jdbcType=DECIMAL},
      zc_inactive_dc_stock = #{record.zcInactiveDcStock,jdbcType=DECIMAL},
      dc_inv_upper = #{record.dcInvUpper,jdbcType=DECIMAL},
      dc_block_stock1 = #{record.dcBlockStock1,jdbcType=DECIMAL},
      dc_block_stock2 = #{record.dcBlockStock2,jdbcType=DECIMAL},
      dist_days = #{record.distDays,jdbcType=DECIMAL},
      sent_time = #{record.sentTime,jdbcType=VARCHAR},
      amount_before_30 = #{record.amountBefore30,jdbcType=DECIMAL},
      unsatisfied_delivery_sku = #{record.unsatisfiedDeliverySku,jdbcType=DECIMAL},
      unsatisfied_delivery_cnt = #{record.unsatisfiedDeliveryCnt,jdbcType=DECIMAL},
      weiqingtuicang = #{record.weiqingtuicang,jdbcType=VARCHAR},
      weiqingjisuan = #{record.weiqingjisuan,jdbcType=VARCHAR},
      min_purchase_type = #{record.minPurchaseType,jdbcType=VARCHAR},
      min_purchase_price = #{record.minPurchasePrice,jdbcType=DECIMAL},
      min_purchase_date = #{record.minPurchaseDate,jdbcType=VARCHAR},
      ZDCJE1 = #{record.zdcje1,jdbcType=DECIMAL},
      ZDCJE2 = #{record.zdcje2,jdbcType=DECIMAL},
      ZDCJE3 = #{record.zdcje3,jdbcType=DECIMAL},
      ZDCCB1 = #{record.zdccb1,jdbcType=DECIMAL},
      ZDCCB2 = #{record.zdccb2,jdbcType=DECIMAL},
      ZMDJE1 = #{record.zmdje1,jdbcType=DECIMAL},
      ZMDJE2 = #{record.zmdje2,jdbcType=DECIMAL},
      ZMDJE3 = #{record.zmdje3,jdbcType=DECIMAL},
      ZMDCB = #{record.zmdcb,jdbcType=DECIMAL},
      ZCDZZTS = #{record.zcdzzts,jdbcType=DECIMAL},
      ZCDKCJEPM = #{record.zcdkcjepm,jdbcType=VARCHAR},
      ZDPT = #{record.zdpt,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.iscm.entityTidb.IscmGaojiDcBuhuoResultToSap">
    update iscm_gaoji_dc_buhuo_result_to_sap
    <set>
      <if test="dt != null">
        dt = #{dt,jdbcType=VARCHAR},
      </if>
      <if test="werks != null">
        werks = #{werks,jdbcType=VARCHAR},
      </if>
      <if test="matnr != null">
        matnr = #{matnr,jdbcType=VARCHAR},
      </if>
      <if test="avgQty != null">
        avg_qty = #{avgQty,jdbcType=VARCHAR},
      </if>
      <if test="inactiveDcStock != null">
        inactive_dc_stock = #{inactiveDcStock,jdbcType=DECIMAL},
      </if>
      <if test="inactiveStoreStock != null">
        inactive_store_stock = #{inactiveStoreStock,jdbcType=DECIMAL},
      </if>
      <if test="reviseTotalBlockStock2 != null">
        revise_total_block_stock2 = #{reviseTotalBlockStock2,jdbcType=DECIMAL},
      </if>
      <if test="weiqingcaigou7 != null">
        weiqingcaigou_7 = #{weiqingcaigou7,jdbcType=DECIMAL},
      </if>
      <if test="invUpper != null">
        inv_upper = #{invUpper,jdbcType=DECIMAL},
      </if>
      <if test="suggestDhl != null">
        suggest_dhl = #{suggestDhl,jdbcType=DECIMAL},
      </if>
      <if test="storeCntsInStock != null">
        store_cnts_in_stock = #{storeCntsInStock,jdbcType=DECIMAL},
      </if>
      <if test="totalApplyqty != null">
        total_applyqty = #{totalApplyqty,jdbcType=DECIMAL},
      </if>
      <if test="maxApplyqty != null">
        max_applyqty = #{maxApplyqty,jdbcType=DECIMAL},
      </if>
      <if test="midqty != null">
        midqty = #{midqty,jdbcType=DECIMAL},
      </if>
      <if test="maxqty != null">
        maxqty = #{maxqty,jdbcType=DECIMAL},
      </if>
      <if test="stockToUseRatio != null">
        stock_to_use_ratio = #{stockToUseRatio,jdbcType=VARCHAR},
      </if>
      <if test="lifnr != null">
        lifnr = #{lifnr,jdbcType=VARCHAR},
      </if>
      <if test="dhdB != null">
        dhd_b = #{dhdB,jdbcType=DECIMAL},
      </if>
      <if test="dcS != null">
        dc_s = #{dcS,jdbcType=DECIMAL},
      </if>
      <if test="dcL != null">
        dc_l = #{dcL,jdbcType=DECIMAL},
      </if>
      <if test="goodsLevel != null">
        goods_level = #{goodsLevel,jdbcType=VARCHAR},
      </if>
      <if test="safetyStock != null">
        safety_stock = #{safetyStock,jdbcType=DECIMAL},
      </if>
      <if test="sumRV != null">
        sum_r_v = #{sumRV,jdbcType=DECIMAL},
      </if>
      <if test="reviseTotalBlockStock1 != null">
        revise_total_block_stock1 = #{reviseTotalBlockStock1,jdbcType=DECIMAL},
      </if>
      <if test="rawSuggestDhl != null">
        raw_suggest_dhl = #{rawSuggestDhl,jdbcType=DECIMAL},
      </if>
      <if test="znoncxb != null">
        znoncxb = #{znoncxb,jdbcType=VARCHAR},
      </if>
      <if test="totalDcStock != null">
        total_dc_stock = #{totalDcStock,jdbcType=VARCHAR},
      </if>
      <if test="totalDcDisableStock != null">
        total_dc_disable_stock = #{totalDcDisableStock,jdbcType=VARCHAR},
      </if>
      <if test="totalStoreStock != null">
        total_store_stock = #{totalStoreStock,jdbcType=VARCHAR},
      </if>
      <if test="qtyBefore30 != null">
        qty_before_30 = #{qtyBefore30,jdbcType=VARCHAR},
      </if>
      <if test="hbSaleRate != null">
        hb_sale_rate = #{hbSaleRate,jdbcType=VARCHAR},
      </if>
      <if test="tbSaleRate != null">
        tb_sale_rate = #{tbSaleRate,jdbcType=VARCHAR},
      </if>
      <if test="zdxmds != null">
        zdxmds = #{zdxmds,jdbcType=DECIMAL},
      </if>
      <if test="dcSaleDays != null">
        dc_sale_days = #{dcSaleDays,jdbcType=DECIMAL},
      </if>
      <if test="dcInvSaleDays != null">
        dc_inv_sale_days = #{dcInvSaleDays,jdbcType=DECIMAL},
      </if>
      <if test="storeSaleDays != null">
        store_sale_days = #{storeSaleDays,jdbcType=DECIMAL},
      </if>
      <if test="storeInvSaleDays != null">
        store_inv_sale_days = #{storeInvSaleDays,jdbcType=DECIMAL},
      </if>
      <if test="puhuoStores != null">
        puhuo_stores = #{puhuoStores,jdbcType=DECIMAL},
      </if>
      <if test="qtyBefore7 != null">
        qty_before_7 = #{qtyBefore7,jdbcType=DECIMAL},
      </if>
      <if test="avgQtyBefore7 != null">
        avg_qty_before_7 = #{avgQtyBefore7,jdbcType=VARCHAR},
      </if>
      <if test="qtyBefore14 != null">
        qty_before_14 = #{qtyBefore14,jdbcType=DECIMAL},
      </if>
      <if test="avgQtyBefore14 != null">
        avg_qty_before_14 = #{avgQtyBefore14,jdbcType=VARCHAR},
      </if>
      <if test="avgQtyBefore30 != null">
        avg_qty_before_30 = #{avgQtyBefore30,jdbcType=VARCHAR},
      </if>
      <if test="qtyBefore90 != null">
        qty_before_90 = #{qtyBefore90,jdbcType=DECIMAL},
      </if>
      <if test="avgQtyBefore90 != null">
        avg_qty_before_90 = #{avgQtyBefore90,jdbcType=VARCHAR},
      </if>
      <if test="distqtyBefore7 != null">
        distqty_before_7 = #{distqtyBefore7,jdbcType=DECIMAL},
      </if>
      <if test="avgDistqtyBefore7 != null">
        avg_distqty_before_7 = #{avgDistqtyBefore7,jdbcType=VARCHAR},
      </if>
      <if test="distqtyBefore14 != null">
        distqty_before_14 = #{distqtyBefore14,jdbcType=DECIMAL},
      </if>
      <if test="avgDistqtyBefore14 != null">
        avg_distqty_before_14 = #{avgDistqtyBefore14,jdbcType=VARCHAR},
      </if>
      <if test="distqty30 != null">
        distqty_30 = #{distqty30,jdbcType=DECIMAL},
      </if>
      <if test="avgDistqtyBefore30 != null">
        avg_distqty_before_30 = #{avgDistqtyBefore30,jdbcType=VARCHAR},
      </if>
      <if test="distqtyBefore90 != null">
        distqty_before_90 = #{distqtyBefore90,jdbcType=DECIMAL},
      </if>
      <if test="avgDistqtyBefore90 != null">
        avg_distqty_before_90 = #{avgDistqtyBefore90,jdbcType=VARCHAR},
      </if>
      <if test="qty3060 != null">
        qty_30_60 = #{qty3060,jdbcType=DECIMAL},
      </if>
      <if test="qty6090 != null">
        qty_60_90 = #{qty6090,jdbcType=DECIMAL},
      </if>
      <if test="distqty3060 != null">
        distqty_30_60 = #{distqty3060,jdbcType=DECIMAL},
      </if>
      <if test="distqty6090 != null">
        distqty_60_90 = #{distqty6090,jdbcType=DECIMAL},
      </if>
      <if test="seasonalFactor != null">
        seasonal_factor = #{seasonalFactor,jdbcType=VARCHAR},
      </if>
      <if test="jmStoreStock != null">
        jm_store_stock = #{jmStoreStock,jdbcType=DECIMAL},
      </if>
      <if test="dcStock != null">
        dc_stock = #{dcStock,jdbcType=DECIMAL},
      </if>
      <if test="dcDisableStock != null">
        dc_disable_stock = #{dcDisableStock,jdbcType=DECIMAL},
      </if>
      <if test="zcInactiveDcStock != null">
        zc_inactive_dc_stock = #{zcInactiveDcStock,jdbcType=DECIMAL},
      </if>
      <if test="dcInvUpper != null">
        dc_inv_upper = #{dcInvUpper,jdbcType=DECIMAL},
      </if>
      <if test="dcBlockStock1 != null">
        dc_block_stock1 = #{dcBlockStock1,jdbcType=DECIMAL},
      </if>
      <if test="dcBlockStock2 != null">
        dc_block_stock2 = #{dcBlockStock2,jdbcType=DECIMAL},
      </if>
      <if test="distDays != null">
        dist_days = #{distDays,jdbcType=DECIMAL},
      </if>
      <if test="sentTime != null">
        sent_time = #{sentTime,jdbcType=VARCHAR},
      </if>
      <if test="amountBefore30 != null">
        amount_before_30 = #{amountBefore30,jdbcType=DECIMAL},
      </if>
      <if test="unsatisfiedDeliverySku != null">
        unsatisfied_delivery_sku = #{unsatisfiedDeliverySku,jdbcType=DECIMAL},
      </if>
      <if test="unsatisfiedDeliveryCnt != null">
        unsatisfied_delivery_cnt = #{unsatisfiedDeliveryCnt,jdbcType=DECIMAL},
      </if>
      <if test="weiqingtuicang != null">
        weiqingtuicang = #{weiqingtuicang,jdbcType=VARCHAR},
      </if>
      <if test="weiqingjisuan != null">
        weiqingjisuan = #{weiqingjisuan,jdbcType=VARCHAR},
      </if>
      <if test="minPurchaseType != null">
        min_purchase_type = #{minPurchaseType,jdbcType=VARCHAR},
      </if>
      <if test="minPurchasePrice != null">
        min_purchase_price = #{minPurchasePrice,jdbcType=DECIMAL},
      </if>
      <if test="minPurchaseDate != null">
        min_purchase_date = #{minPurchaseDate,jdbcType=VARCHAR},
      </if>
      <if test="zdcje1 != null">
        ZDCJE1 = #{zdcje1,jdbcType=DECIMAL},
      </if>
      <if test="zdcje2 != null">
        ZDCJE2 = #{zdcje2,jdbcType=DECIMAL},
      </if>
      <if test="zdcje3 != null">
        ZDCJE3 = #{zdcje3,jdbcType=DECIMAL},
      </if>
      <if test="zdccb1 != null">
        ZDCCB1 = #{zdccb1,jdbcType=DECIMAL},
      </if>
      <if test="zdccb2 != null">
        ZDCCB2 = #{zdccb2,jdbcType=DECIMAL},
      </if>
      <if test="zmdje1 != null">
        ZMDJE1 = #{zmdje1,jdbcType=DECIMAL},
      </if>
      <if test="zmdje2 != null">
        ZMDJE2 = #{zmdje2,jdbcType=DECIMAL},
      </if>
      <if test="zmdje3 != null">
        ZMDJE3 = #{zmdje3,jdbcType=DECIMAL},
      </if>
      <if test="zmdcb != null">
        ZMDCB = #{zmdcb,jdbcType=DECIMAL},
      </if>
      <if test="zcdzzts != null">
        ZCDZZTS = #{zcdzzts,jdbcType=DECIMAL},
      </if>
      <if test="zcdkcjepm != null">
        ZCDKCJEPM = #{zcdkcjepm,jdbcType=VARCHAR},
      </if>
      <if test="zdpt != null">
        ZDPT = #{zdpt,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.iscm.entityTidb.IscmGaojiDcBuhuoResultToSap">
    update iscm_gaoji_dc_buhuo_result_to_sap
    set dt = #{dt,jdbcType=VARCHAR},
      werks = #{werks,jdbcType=VARCHAR},
      matnr = #{matnr,jdbcType=VARCHAR},
      avg_qty = #{avgQty,jdbcType=VARCHAR},
      inactive_dc_stock = #{inactiveDcStock,jdbcType=DECIMAL},
      inactive_store_stock = #{inactiveStoreStock,jdbcType=DECIMAL},
      revise_total_block_stock2 = #{reviseTotalBlockStock2,jdbcType=DECIMAL},
      weiqingcaigou_7 = #{weiqingcaigou7,jdbcType=DECIMAL},
      inv_upper = #{invUpper,jdbcType=DECIMAL},
      suggest_dhl = #{suggestDhl,jdbcType=DECIMAL},
      store_cnts_in_stock = #{storeCntsInStock,jdbcType=DECIMAL},
      total_applyqty = #{totalApplyqty,jdbcType=DECIMAL},
      max_applyqty = #{maxApplyqty,jdbcType=DECIMAL},
      midqty = #{midqty,jdbcType=DECIMAL},
      maxqty = #{maxqty,jdbcType=DECIMAL},
      stock_to_use_ratio = #{stockToUseRatio,jdbcType=VARCHAR},
      lifnr = #{lifnr,jdbcType=VARCHAR},
      dhd_b = #{dhdB,jdbcType=DECIMAL},
      dc_s = #{dcS,jdbcType=DECIMAL},
      dc_l = #{dcL,jdbcType=DECIMAL},
      goods_level = #{goodsLevel,jdbcType=VARCHAR},
      safety_stock = #{safetyStock,jdbcType=DECIMAL},
      sum_r_v = #{sumRV,jdbcType=DECIMAL},
      revise_total_block_stock1 = #{reviseTotalBlockStock1,jdbcType=DECIMAL},
      raw_suggest_dhl = #{rawSuggestDhl,jdbcType=DECIMAL},
      znoncxb = #{znoncxb,jdbcType=VARCHAR},
      total_dc_stock = #{totalDcStock,jdbcType=VARCHAR},
      total_dc_disable_stock = #{totalDcDisableStock,jdbcType=VARCHAR},
      total_store_stock = #{totalStoreStock,jdbcType=VARCHAR},
      qty_before_30 = #{qtyBefore30,jdbcType=VARCHAR},
      hb_sale_rate = #{hbSaleRate,jdbcType=VARCHAR},
      tb_sale_rate = #{tbSaleRate,jdbcType=VARCHAR},
      zdxmds = #{zdxmds,jdbcType=DECIMAL},
      dc_sale_days = #{dcSaleDays,jdbcType=DECIMAL},
      dc_inv_sale_days = #{dcInvSaleDays,jdbcType=DECIMAL},
      store_sale_days = #{storeSaleDays,jdbcType=DECIMAL},
      store_inv_sale_days = #{storeInvSaleDays,jdbcType=DECIMAL},
      puhuo_stores = #{puhuoStores,jdbcType=DECIMAL},
      qty_before_7 = #{qtyBefore7,jdbcType=DECIMAL},
      avg_qty_before_7 = #{avgQtyBefore7,jdbcType=VARCHAR},
      qty_before_14 = #{qtyBefore14,jdbcType=DECIMAL},
      avg_qty_before_14 = #{avgQtyBefore14,jdbcType=VARCHAR},
      avg_qty_before_30 = #{avgQtyBefore30,jdbcType=VARCHAR},
      qty_before_90 = #{qtyBefore90,jdbcType=DECIMAL},
      avg_qty_before_90 = #{avgQtyBefore90,jdbcType=VARCHAR},
      distqty_before_7 = #{distqtyBefore7,jdbcType=DECIMAL},
      avg_distqty_before_7 = #{avgDistqtyBefore7,jdbcType=VARCHAR},
      distqty_before_14 = #{distqtyBefore14,jdbcType=DECIMAL},
      avg_distqty_before_14 = #{avgDistqtyBefore14,jdbcType=VARCHAR},
      distqty_30 = #{distqty30,jdbcType=DECIMAL},
      avg_distqty_before_30 = #{avgDistqtyBefore30,jdbcType=VARCHAR},
      distqty_before_90 = #{distqtyBefore90,jdbcType=DECIMAL},
      avg_distqty_before_90 = #{avgDistqtyBefore90,jdbcType=VARCHAR},
      qty_30_60 = #{qty3060,jdbcType=DECIMAL},
      qty_60_90 = #{qty6090,jdbcType=DECIMAL},
      distqty_30_60 = #{distqty3060,jdbcType=DECIMAL},
      distqty_60_90 = #{distqty6090,jdbcType=DECIMAL},
      seasonal_factor = #{seasonalFactor,jdbcType=VARCHAR},
      jm_store_stock = #{jmStoreStock,jdbcType=DECIMAL},
      dc_stock = #{dcStock,jdbcType=DECIMAL},
      dc_disable_stock = #{dcDisableStock,jdbcType=DECIMAL},
      zc_inactive_dc_stock = #{zcInactiveDcStock,jdbcType=DECIMAL},
      dc_inv_upper = #{dcInvUpper,jdbcType=DECIMAL},
      dc_block_stock1 = #{dcBlockStock1,jdbcType=DECIMAL},
      dc_block_stock2 = #{dcBlockStock2,jdbcType=DECIMAL},
      dist_days = #{distDays,jdbcType=DECIMAL},
      sent_time = #{sentTime,jdbcType=VARCHAR},
      amount_before_30 = #{amountBefore30,jdbcType=DECIMAL},
      unsatisfied_delivery_sku = #{unsatisfiedDeliverySku,jdbcType=DECIMAL},
      unsatisfied_delivery_cnt = #{unsatisfiedDeliveryCnt,jdbcType=DECIMAL},
      weiqingtuicang = #{weiqingtuicang,jdbcType=VARCHAR},
      weiqingjisuan = #{weiqingjisuan,jdbcType=VARCHAR},
      min_purchase_type = #{minPurchaseType,jdbcType=VARCHAR},
      min_purchase_price = #{minPurchasePrice,jdbcType=DECIMAL},
      min_purchase_date = #{minPurchaseDate,jdbcType=VARCHAR},
      ZDCJE1 = #{zdcje1,jdbcType=DECIMAL},
      ZDCJE2 = #{zdcje2,jdbcType=DECIMAL},
      ZDCJE3 = #{zdcje3,jdbcType=DECIMAL},
      ZDCCB1 = #{zdccb1,jdbcType=DECIMAL},
      ZDCCB2 = #{zdccb2,jdbcType=DECIMAL},
      ZMDJE1 = #{zmdje1,jdbcType=DECIMAL},
      ZMDJE2 = #{zmdje2,jdbcType=DECIMAL},
      ZMDJE3 = #{zmdje3,jdbcType=DECIMAL},
      ZMDCB = #{zmdcb,jdbcType=DECIMAL},
      ZCDZZTS = #{zcdzzts,jdbcType=DECIMAL},
      ZCDKCJEPM = #{zcdkcjepm,jdbcType=VARCHAR},
      ZDPT = #{zdpt,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>