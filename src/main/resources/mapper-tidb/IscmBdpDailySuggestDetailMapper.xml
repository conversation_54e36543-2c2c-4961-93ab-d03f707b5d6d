<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapperTidb.IscmBdpDailySuggestDetailMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entityTidb.IscmBdpDailySuggestDetail">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="platform_name" jdbcType="VARCHAR" property="platformName" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="store_name" jdbcType="VARCHAR" property="storeName" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="bdp_interface_quantity" jdbcType="DECIMAL" property="bdpInterfaceQuantity" />
    <result column="pos_interface_quantity" jdbcType="DECIMAL" property="posInterfaceQuantity" />
    <result column="pos_business_quantity" jdbcType="DECIMAL" property="posBusinessQuantity" />
    <result column="exec_status" jdbcType="TINYINT" property="execStatus" />
    <result column="diff_msg" jdbcType="VARCHAR" property="diffMsg" />
    <result column="dt" jdbcType="VARCHAR" property="dt" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, platform_name, company_code, company_name, store_code, store_name, goods_no, 
    bdp_interface_quantity, pos_interface_quantity, pos_business_quantity, exec_status, 
    diff_msg, dt
  </sql>
  <select id="selectByExample" parameterType="com.cowell.iscm.entityTidb.IscmBdpDailySuggestDetailExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from iscm_bdp_daily_suggest_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from iscm_bdp_daily_suggest_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from iscm_bdp_daily_suggest_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.iscm.entityTidb.IscmBdpDailySuggestDetailExample">
    delete from iscm_bdp_daily_suggest_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.iscm.entityTidb.IscmBdpDailySuggestDetail">
    insert into iscm_bdp_daily_suggest_detail (id, platform_name, company_code, 
      company_name, store_code, store_name, 
      goods_no, bdp_interface_quantity, pos_interface_quantity, 
      pos_business_quantity, exec_status, diff_msg, 
      dt)
    values (#{id,jdbcType=BIGINT}, #{platformName,jdbcType=VARCHAR}, #{companyCode,jdbcType=VARCHAR}, 
      #{companyName,jdbcType=VARCHAR}, #{storeCode,jdbcType=VARCHAR}, #{storeName,jdbcType=VARCHAR}, 
      #{goodsNo,jdbcType=VARCHAR}, #{bdpInterfaceQuantity,jdbcType=DECIMAL}, #{posInterfaceQuantity,jdbcType=DECIMAL}, 
      #{posBusinessQuantity,jdbcType=DECIMAL}, #{execStatus,jdbcType=TINYINT}, #{diffMsg,jdbcType=VARCHAR}, 
      #{dt,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.iscm.entityTidb.IscmBdpDailySuggestDetail">
    insert into iscm_bdp_daily_suggest_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="platformName != null">
        platform_name,
      </if>
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="companyName != null">
        company_name,
      </if>
      <if test="storeCode != null">
        store_code,
      </if>
      <if test="storeName != null">
        store_name,
      </if>
      <if test="goodsNo != null">
        goods_no,
      </if>
      <if test="bdpInterfaceQuantity != null">
        bdp_interface_quantity,
      </if>
      <if test="posInterfaceQuantity != null">
        pos_interface_quantity,
      </if>
      <if test="posBusinessQuantity != null">
        pos_business_quantity,
      </if>
      <if test="execStatus != null">
        exec_status,
      </if>
      <if test="diffMsg != null">
        diff_msg,
      </if>
      <if test="dt != null">
        dt,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="platformName != null">
        #{platformName,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="storeCode != null">
        #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="storeName != null">
        #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="goodsNo != null">
        #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="bdpInterfaceQuantity != null">
        #{bdpInterfaceQuantity,jdbcType=DECIMAL},
      </if>
      <if test="posInterfaceQuantity != null">
        #{posInterfaceQuantity,jdbcType=DECIMAL},
      </if>
      <if test="posBusinessQuantity != null">
        #{posBusinessQuantity,jdbcType=DECIMAL},
      </if>
      <if test="execStatus != null">
        #{execStatus,jdbcType=TINYINT},
      </if>
      <if test="diffMsg != null">
        #{diffMsg,jdbcType=VARCHAR},
      </if>
      <if test="dt != null">
        #{dt,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.iscm.entityTidb.IscmBdpDailySuggestDetailExample" resultType="java.lang.Long">
    select count(*) from iscm_bdp_daily_suggest_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update iscm_bdp_daily_suggest_detail
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.platformName != null">
        platform_name = #{record.platformName,jdbcType=VARCHAR},
      </if>
      <if test="record.companyCode != null">
        company_code = #{record.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.companyName != null">
        company_name = #{record.companyName,jdbcType=VARCHAR},
      </if>
      <if test="record.storeCode != null">
        store_code = #{record.storeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.storeName != null">
        store_name = #{record.storeName,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsNo != null">
        goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="record.bdpInterfaceQuantity != null">
        bdp_interface_quantity = #{record.bdpInterfaceQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.posInterfaceQuantity != null">
        pos_interface_quantity = #{record.posInterfaceQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.posBusinessQuantity != null">
        pos_business_quantity = #{record.posBusinessQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.execStatus != null">
        exec_status = #{record.execStatus,jdbcType=TINYINT},
      </if>
      <if test="record.diffMsg != null">
        diff_msg = #{record.diffMsg,jdbcType=VARCHAR},
      </if>
      <if test="record.dt != null">
        dt = #{record.dt,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update iscm_bdp_daily_suggest_detail
    set id = #{record.id,jdbcType=BIGINT},
      platform_name = #{record.platformName,jdbcType=VARCHAR},
      company_code = #{record.companyCode,jdbcType=VARCHAR},
      company_name = #{record.companyName,jdbcType=VARCHAR},
      store_code = #{record.storeCode,jdbcType=VARCHAR},
      store_name = #{record.storeName,jdbcType=VARCHAR},
      goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      bdp_interface_quantity = #{record.bdpInterfaceQuantity,jdbcType=DECIMAL},
      pos_interface_quantity = #{record.posInterfaceQuantity,jdbcType=DECIMAL},
      pos_business_quantity = #{record.posBusinessQuantity,jdbcType=DECIMAL},
      exec_status = #{record.execStatus,jdbcType=TINYINT},
      diff_msg = #{record.diffMsg,jdbcType=VARCHAR},
      dt = #{record.dt,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.iscm.entityTidb.IscmBdpDailySuggestDetail">
    update iscm_bdp_daily_suggest_detail
    <set>
      <if test="platformName != null">
        platform_name = #{platformName,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="storeCode != null">
        store_code = #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="storeName != null">
        store_name = #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="goodsNo != null">
        goods_no = #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="bdpInterfaceQuantity != null">
        bdp_interface_quantity = #{bdpInterfaceQuantity,jdbcType=DECIMAL},
      </if>
      <if test="posInterfaceQuantity != null">
        pos_interface_quantity = #{posInterfaceQuantity,jdbcType=DECIMAL},
      </if>
      <if test="posBusinessQuantity != null">
        pos_business_quantity = #{posBusinessQuantity,jdbcType=DECIMAL},
      </if>
      <if test="execStatus != null">
        exec_status = #{execStatus,jdbcType=TINYINT},
      </if>
      <if test="diffMsg != null">
        diff_msg = #{diffMsg,jdbcType=VARCHAR},
      </if>
      <if test="dt != null">
        dt = #{dt,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.iscm.entityTidb.IscmBdpDailySuggestDetail">
    update iscm_bdp_daily_suggest_detail
    set platform_name = #{platformName,jdbcType=VARCHAR},
      company_code = #{companyCode,jdbcType=VARCHAR},
      company_name = #{companyName,jdbcType=VARCHAR},
      store_code = #{storeCode,jdbcType=VARCHAR},
      store_name = #{storeName,jdbcType=VARCHAR},
      goods_no = #{goodsNo,jdbcType=VARCHAR},
      bdp_interface_quantity = #{bdpInterfaceQuantity,jdbcType=DECIMAL},
      pos_interface_quantity = #{posInterfaceQuantity,jdbcType=DECIMAL},
      pos_business_quantity = #{posBusinessQuantity,jdbcType=DECIMAL},
      exec_status = #{execStatus,jdbcType=TINYINT},
      diff_msg = #{diffMsg,jdbcType=VARCHAR},
      dt = #{dt,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>