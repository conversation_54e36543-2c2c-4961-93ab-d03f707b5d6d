<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapperTidb.IscmSuggestDistexecDetailMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entityTidb.IscmSuggestDistexecDetail">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="allot_date" jdbcType="TIMESTAMP" property="allotDate" />
    <result column="allot_month" jdbcType="INTEGER" property="allotMonth" />
    <result column="register_no" jdbcType="VARCHAR" property="registerNo" />
    <result column="register_month" jdbcType="INTEGER" property="registerMonth" />
    <result column="allot_type" jdbcType="TINYINT" property="allotType" />
    <result column="pos_allot_no" jdbcType="VARCHAR" property="posAllotNo" />
    <result column="allot_approve_time" jdbcType="TIMESTAMP" property="allotApproveTime" />
    <result column="pos_allot_detail_id" jdbcType="BIGINT" property="posAllotDetailId" />
    <result column="platform_org_id" jdbcType="BIGINT" property="platformOrgId" />
    <result column="platform_org_name" jdbcType="VARCHAR" property="platformOrgName" />
    <result column="out_company_id" jdbcType="BIGINT" property="outCompanyId" />
    <result column="out_company_code" jdbcType="VARCHAR" property="outCompanyCode" />
    <result column="out_company_name" jdbcType="VARCHAR" property="outCompanyName" />
    <result column="in_company_id" jdbcType="BIGINT" property="inCompanyId" />
    <result column="in_company_code" jdbcType="VARCHAR" property="inCompanyCode" />
    <result column="in_company_name" jdbcType="VARCHAR" property="inCompanyName" />
    <result column="out_store_id" jdbcType="BIGINT" property="outStoreId" />
    <result column="out_store_code" jdbcType="VARCHAR" property="outStoreCode" />
    <result column="out_store_name" jdbcType="VARCHAR" property="outStoreName" />
    <result column="out_store_attr" jdbcType="VARCHAR" property="outStoreAttr" />
    <result column="out_store_sales_level" jdbcType="VARCHAR" property="outStoreSalesLevel" />
    <result column="in_store_id" jdbcType="BIGINT" property="inStoreId" />
    <result column="in_store_code" jdbcType="VARCHAR" property="inStoreCode" />
    <result column="in_store_name" jdbcType="VARCHAR" property="inStoreName" />
    <result column="in_store_attr" jdbcType="VARCHAR" property="inStoreAttr" />
    <result column="in_store_sales_level" jdbcType="VARCHAR" property="inStoreSalesLevel" />
    <result column="allot_group_code" jdbcType="VARCHAR" property="allotGroupCode" />
    <result column="allot_group_name" jdbcType="VARCHAR" property="allotGroupName" />
    <result column="approve_name" jdbcType="VARCHAR" property="approveName" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="goods_desc" jdbcType="VARCHAR" property="goodsDesc" />
    <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
    <result column="produce_date" jdbcType="VARCHAR" property="produceDate" />
    <result column="validity_date" jdbcType="VARCHAR" property="validityDate" />
    <result column="hd_batch_no" jdbcType="VARCHAR" property="hdBatchNo" />
    <result column="sap_batch_no" jdbcType="VARCHAR" property="sapBatchNo" />
    <result column="notes" jdbcType="VARCHAR" property="notes" />
    <result column="void_reason" jdbcType="VARCHAR" property="voidReason" />
    <result column="goods_common_name" jdbcType="VARCHAR" property="goodsCommonName" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="suggest_allot_quantity" jdbcType="DECIMAL" property="suggestAllotQuantity" />
    <result column="suggest_cost_amount" jdbcType="DECIMAL" property="suggestCostAmount" />
    <result column="real_allot_quantity" jdbcType="DECIMAL" property="realAllotQuantity" />
    <result column="real_cost_amount" jdbcType="DECIMAL" property="realCostAmount" />
    <result column="out_approve_status" jdbcType="TINYINT" property="outApproveStatus" />
    <result column="out_approve_time" jdbcType="TIMESTAMP" property="outApproveTime" />
    <result column="out_allot_quantity" jdbcType="DECIMAL" property="outAllotQuantity" />
    <result column="in_approve_status" jdbcType="TINYINT" property="inApproveStatus" />
    <result column="in_approve_time" jdbcType="TIMESTAMP" property="inApproveTime" />
    <result column="in_allot_quantity" jdbcType="DECIMAL" property="inAllotQuantity" />
    <result column="in_stock_time" jdbcType="TIMESTAMP" property="inStockTime" />
    <result column="show_status" jdbcType="TINYINT" property="showStatus" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, allot_date, allot_month, register_no, register_month, allot_type, pos_allot_no, 
    allot_approve_time, pos_allot_detail_id, platform_org_id, platform_org_name, out_company_id, 
    out_company_code, out_company_name, in_company_id, in_company_code, in_company_name, 
    out_store_id, out_store_code, out_store_name, out_store_attr, out_store_sales_level, 
    in_store_id, in_store_code, in_store_name, in_store_attr, in_store_sales_level, allot_group_code, 
    allot_group_name, approve_name, goods_no, goods_desc, batch_no, produce_date, validity_date, 
    hd_batch_no, sap_batch_no, notes, void_reason, goods_common_name, manufacturer, unit, 
    suggest_allot_quantity, suggest_cost_amount, real_allot_quantity, real_cost_amount, 
    out_approve_status, out_approve_time, out_allot_quantity, in_approve_status, in_approve_time, 
    in_allot_quantity, in_stock_time, show_status, `status`, gmt_create, gmt_update, 
    extend, version, created_by, created_name, updated_by, updated_name
  </sql>
  <select id="selectByExample" parameterType="com.cowell.iscm.entityTidb.IscmSuggestDistexecDetailExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from iscm_suggest_distexec_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from iscm_suggest_distexec_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from iscm_suggest_distexec_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.iscm.entityTidb.IscmSuggestDistexecDetailExample">
    delete from iscm_suggest_distexec_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entityTidb.IscmSuggestDistexecDetail" useGeneratedKeys="true">
    insert into iscm_suggest_distexec_detail (allot_date, allot_month, register_no, 
      register_month, allot_type, pos_allot_no, 
      allot_approve_time, pos_allot_detail_id, platform_org_id, 
      platform_org_name, out_company_id, out_company_code, 
      out_company_name, in_company_id, in_company_code, 
      in_company_name, out_store_id, out_store_code, 
      out_store_name, out_store_attr, out_store_sales_level, 
      in_store_id, in_store_code, in_store_name, 
      in_store_attr, in_store_sales_level, allot_group_code, 
      allot_group_name, approve_name, goods_no, 
      goods_desc, batch_no, produce_date, 
      validity_date, hd_batch_no, sap_batch_no, 
      notes, void_reason, goods_common_name, 
      manufacturer, unit, suggest_allot_quantity, 
      suggest_cost_amount, real_allot_quantity, real_cost_amount, 
      out_approve_status, out_approve_time, out_allot_quantity, 
      in_approve_status, in_approve_time, in_allot_quantity, 
      in_stock_time, show_status, `status`, 
      gmt_create, gmt_update, extend, 
      version, created_by, created_name, 
      updated_by, updated_name)
    values (#{allotDate,jdbcType=TIMESTAMP}, #{allotMonth,jdbcType=INTEGER}, #{registerNo,jdbcType=VARCHAR}, 
      #{registerMonth,jdbcType=INTEGER}, #{allotType,jdbcType=TINYINT}, #{posAllotNo,jdbcType=VARCHAR}, 
      #{allotApproveTime,jdbcType=TIMESTAMP}, #{posAllotDetailId,jdbcType=BIGINT}, #{platformOrgId,jdbcType=BIGINT}, 
      #{platformOrgName,jdbcType=VARCHAR}, #{outCompanyId,jdbcType=BIGINT}, #{outCompanyCode,jdbcType=VARCHAR}, 
      #{outCompanyName,jdbcType=VARCHAR}, #{inCompanyId,jdbcType=BIGINT}, #{inCompanyCode,jdbcType=VARCHAR}, 
      #{inCompanyName,jdbcType=VARCHAR}, #{outStoreId,jdbcType=BIGINT}, #{outStoreCode,jdbcType=VARCHAR}, 
      #{outStoreName,jdbcType=VARCHAR}, #{outStoreAttr,jdbcType=VARCHAR}, #{outStoreSalesLevel,jdbcType=VARCHAR}, 
      #{inStoreId,jdbcType=BIGINT}, #{inStoreCode,jdbcType=VARCHAR}, #{inStoreName,jdbcType=VARCHAR}, 
      #{inStoreAttr,jdbcType=VARCHAR}, #{inStoreSalesLevel,jdbcType=VARCHAR}, #{allotGroupCode,jdbcType=VARCHAR}, 
      #{allotGroupName,jdbcType=VARCHAR}, #{approveName,jdbcType=VARCHAR}, #{goodsNo,jdbcType=VARCHAR}, 
      #{goodsDesc,jdbcType=VARCHAR}, #{batchNo,jdbcType=VARCHAR}, #{produceDate,jdbcType=VARCHAR}, 
      #{validityDate,jdbcType=VARCHAR}, #{hdBatchNo,jdbcType=VARCHAR}, #{sapBatchNo,jdbcType=VARCHAR}, 
      #{notes,jdbcType=VARCHAR}, #{voidReason,jdbcType=VARCHAR}, #{goodsCommonName,jdbcType=VARCHAR}, 
      #{manufacturer,jdbcType=VARCHAR}, #{unit,jdbcType=VARCHAR}, #{suggestAllotQuantity,jdbcType=DECIMAL}, 
      #{suggestCostAmount,jdbcType=DECIMAL}, #{realAllotQuantity,jdbcType=DECIMAL}, #{realCostAmount,jdbcType=DECIMAL}, 
      #{outApproveStatus,jdbcType=TINYINT}, #{outApproveTime,jdbcType=TIMESTAMP}, #{outAllotQuantity,jdbcType=DECIMAL}, 
      #{inApproveStatus,jdbcType=TINYINT}, #{inApproveTime,jdbcType=TIMESTAMP}, #{inAllotQuantity,jdbcType=DECIMAL}, 
      #{inStockTime,jdbcType=TIMESTAMP}, #{showStatus,jdbcType=TINYINT}, #{status,jdbcType=TINYINT}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}, #{extend,jdbcType=VARCHAR}, 
      #{version,jdbcType=INTEGER}, #{createdBy,jdbcType=BIGINT}, #{createdName,jdbcType=VARCHAR}, 
      #{updatedBy,jdbcType=BIGINT}, #{updatedName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entityTidb.IscmSuggestDistexecDetail" useGeneratedKeys="true">
    insert into iscm_suggest_distexec_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="allotDate != null">
        allot_date,
      </if>
      <if test="allotMonth != null">
        allot_month,
      </if>
      <if test="registerNo != null">
        register_no,
      </if>
      <if test="registerMonth != null">
        register_month,
      </if>
      <if test="allotType != null">
        allot_type,
      </if>
      <if test="posAllotNo != null">
        pos_allot_no,
      </if>
      <if test="allotApproveTime != null">
        allot_approve_time,
      </if>
      <if test="posAllotDetailId != null">
        pos_allot_detail_id,
      </if>
      <if test="platformOrgId != null">
        platform_org_id,
      </if>
      <if test="platformOrgName != null">
        platform_org_name,
      </if>
      <if test="outCompanyId != null">
        out_company_id,
      </if>
      <if test="outCompanyCode != null">
        out_company_code,
      </if>
      <if test="outCompanyName != null">
        out_company_name,
      </if>
      <if test="inCompanyId != null">
        in_company_id,
      </if>
      <if test="inCompanyCode != null">
        in_company_code,
      </if>
      <if test="inCompanyName != null">
        in_company_name,
      </if>
      <if test="outStoreId != null">
        out_store_id,
      </if>
      <if test="outStoreCode != null">
        out_store_code,
      </if>
      <if test="outStoreName != null">
        out_store_name,
      </if>
      <if test="outStoreAttr != null">
        out_store_attr,
      </if>
      <if test="outStoreSalesLevel != null">
        out_store_sales_level,
      </if>
      <if test="inStoreId != null">
        in_store_id,
      </if>
      <if test="inStoreCode != null">
        in_store_code,
      </if>
      <if test="inStoreName != null">
        in_store_name,
      </if>
      <if test="inStoreAttr != null">
        in_store_attr,
      </if>
      <if test="inStoreSalesLevel != null">
        in_store_sales_level,
      </if>
      <if test="allotGroupCode != null">
        allot_group_code,
      </if>
      <if test="allotGroupName != null">
        allot_group_name,
      </if>
      <if test="approveName != null">
        approve_name,
      </if>
      <if test="goodsNo != null">
        goods_no,
      </if>
      <if test="goodsDesc != null">
        goods_desc,
      </if>
      <if test="batchNo != null">
        batch_no,
      </if>
      <if test="produceDate != null">
        produce_date,
      </if>
      <if test="validityDate != null">
        validity_date,
      </if>
      <if test="hdBatchNo != null">
        hd_batch_no,
      </if>
      <if test="sapBatchNo != null">
        sap_batch_no,
      </if>
      <if test="notes != null">
        notes,
      </if>
      <if test="voidReason != null">
        void_reason,
      </if>
      <if test="goodsCommonName != null">
        goods_common_name,
      </if>
      <if test="manufacturer != null">
        manufacturer,
      </if>
      <if test="unit != null">
        unit,
      </if>
      <if test="suggestAllotQuantity != null">
        suggest_allot_quantity,
      </if>
      <if test="suggestCostAmount != null">
        suggest_cost_amount,
      </if>
      <if test="realAllotQuantity != null">
        real_allot_quantity,
      </if>
      <if test="realCostAmount != null">
        real_cost_amount,
      </if>
      <if test="outApproveStatus != null">
        out_approve_status,
      </if>
      <if test="outApproveTime != null">
        out_approve_time,
      </if>
      <if test="outAllotQuantity != null">
        out_allot_quantity,
      </if>
      <if test="inApproveStatus != null">
        in_approve_status,
      </if>
      <if test="inApproveTime != null">
        in_approve_time,
      </if>
      <if test="inAllotQuantity != null">
        in_allot_quantity,
      </if>
      <if test="inStockTime != null">
        in_stock_time,
      </if>
      <if test="showStatus != null">
        show_status,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedName != null">
        updated_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="allotDate != null">
        #{allotDate,jdbcType=TIMESTAMP},
      </if>
      <if test="allotMonth != null">
        #{allotMonth,jdbcType=INTEGER},
      </if>
      <if test="registerNo != null">
        #{registerNo,jdbcType=VARCHAR},
      </if>
      <if test="registerMonth != null">
        #{registerMonth,jdbcType=INTEGER},
      </if>
      <if test="allotType != null">
        #{allotType,jdbcType=TINYINT},
      </if>
      <if test="posAllotNo != null">
        #{posAllotNo,jdbcType=VARCHAR},
      </if>
      <if test="allotApproveTime != null">
        #{allotApproveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="posAllotDetailId != null">
        #{posAllotDetailId,jdbcType=BIGINT},
      </if>
      <if test="platformOrgId != null">
        #{platformOrgId,jdbcType=BIGINT},
      </if>
      <if test="platformOrgName != null">
        #{platformOrgName,jdbcType=VARCHAR},
      </if>
      <if test="outCompanyId != null">
        #{outCompanyId,jdbcType=BIGINT},
      </if>
      <if test="outCompanyCode != null">
        #{outCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="outCompanyName != null">
        #{outCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="inCompanyId != null">
        #{inCompanyId,jdbcType=BIGINT},
      </if>
      <if test="inCompanyCode != null">
        #{inCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="inCompanyName != null">
        #{inCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="outStoreId != null">
        #{outStoreId,jdbcType=BIGINT},
      </if>
      <if test="outStoreCode != null">
        #{outStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="outStoreName != null">
        #{outStoreName,jdbcType=VARCHAR},
      </if>
      <if test="outStoreAttr != null">
        #{outStoreAttr,jdbcType=VARCHAR},
      </if>
      <if test="outStoreSalesLevel != null">
        #{outStoreSalesLevel,jdbcType=VARCHAR},
      </if>
      <if test="inStoreId != null">
        #{inStoreId,jdbcType=BIGINT},
      </if>
      <if test="inStoreCode != null">
        #{inStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="inStoreName != null">
        #{inStoreName,jdbcType=VARCHAR},
      </if>
      <if test="inStoreAttr != null">
        #{inStoreAttr,jdbcType=VARCHAR},
      </if>
      <if test="inStoreSalesLevel != null">
        #{inStoreSalesLevel,jdbcType=VARCHAR},
      </if>
      <if test="allotGroupCode != null">
        #{allotGroupCode,jdbcType=VARCHAR},
      </if>
      <if test="allotGroupName != null">
        #{allotGroupName,jdbcType=VARCHAR},
      </if>
      <if test="approveName != null">
        #{approveName,jdbcType=VARCHAR},
      </if>
      <if test="goodsNo != null">
        #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="goodsDesc != null">
        #{goodsDesc,jdbcType=VARCHAR},
      </if>
      <if test="batchNo != null">
        #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="produceDate != null">
        #{produceDate,jdbcType=VARCHAR},
      </if>
      <if test="validityDate != null">
        #{validityDate,jdbcType=VARCHAR},
      </if>
      <if test="hdBatchNo != null">
        #{hdBatchNo,jdbcType=VARCHAR},
      </if>
      <if test="sapBatchNo != null">
        #{sapBatchNo,jdbcType=VARCHAR},
      </if>
      <if test="notes != null">
        #{notes,jdbcType=VARCHAR},
      </if>
      <if test="voidReason != null">
        #{voidReason,jdbcType=VARCHAR},
      </if>
      <if test="goodsCommonName != null">
        #{goodsCommonName,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="unit != null">
        #{unit,jdbcType=VARCHAR},
      </if>
      <if test="suggestAllotQuantity != null">
        #{suggestAllotQuantity,jdbcType=DECIMAL},
      </if>
      <if test="suggestCostAmount != null">
        #{suggestCostAmount,jdbcType=DECIMAL},
      </if>
      <if test="realAllotQuantity != null">
        #{realAllotQuantity,jdbcType=DECIMAL},
      </if>
      <if test="realCostAmount != null">
        #{realCostAmount,jdbcType=DECIMAL},
      </if>
      <if test="outApproveStatus != null">
        #{outApproveStatus,jdbcType=TINYINT},
      </if>
      <if test="outApproveTime != null">
        #{outApproveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="outAllotQuantity != null">
        #{outAllotQuantity,jdbcType=DECIMAL},
      </if>
      <if test="inApproveStatus != null">
        #{inApproveStatus,jdbcType=TINYINT},
      </if>
      <if test="inApproveTime != null">
        #{inApproveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="inAllotQuantity != null">
        #{inAllotQuantity,jdbcType=DECIMAL},
      </if>
      <if test="inStockTime != null">
        #{inStockTime,jdbcType=TIMESTAMP},
      </if>
      <if test="showStatus != null">
        #{showStatus,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        #{updatedName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.iscm.entityTidb.IscmSuggestDistexecDetailExample" resultType="java.lang.Long">
    select count(*) from iscm_suggest_distexec_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update iscm_suggest_distexec_detail
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.allotDate != null">
        allot_date = #{record.allotDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.allotMonth != null">
        allot_month = #{record.allotMonth,jdbcType=INTEGER},
      </if>
      <if test="record.registerNo != null">
        register_no = #{record.registerNo,jdbcType=VARCHAR},
      </if>
      <if test="record.registerMonth != null">
        register_month = #{record.registerMonth,jdbcType=INTEGER},
      </if>
      <if test="record.allotType != null">
        allot_type = #{record.allotType,jdbcType=TINYINT},
      </if>
      <if test="record.posAllotNo != null">
        pos_allot_no = #{record.posAllotNo,jdbcType=VARCHAR},
      </if>
      <if test="record.allotApproveTime != null">
        allot_approve_time = #{record.allotApproveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.posAllotDetailId != null">
        pos_allot_detail_id = #{record.posAllotDetailId,jdbcType=BIGINT},
      </if>
      <if test="record.platformOrgId != null">
        platform_org_id = #{record.platformOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.platformOrgName != null">
        platform_org_name = #{record.platformOrgName,jdbcType=VARCHAR},
      </if>
      <if test="record.outCompanyId != null">
        out_company_id = #{record.outCompanyId,jdbcType=BIGINT},
      </if>
      <if test="record.outCompanyCode != null">
        out_company_code = #{record.outCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.outCompanyName != null">
        out_company_name = #{record.outCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="record.inCompanyId != null">
        in_company_id = #{record.inCompanyId,jdbcType=BIGINT},
      </if>
      <if test="record.inCompanyCode != null">
        in_company_code = #{record.inCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.inCompanyName != null">
        in_company_name = #{record.inCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="record.outStoreId != null">
        out_store_id = #{record.outStoreId,jdbcType=BIGINT},
      </if>
      <if test="record.outStoreCode != null">
        out_store_code = #{record.outStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="record.outStoreName != null">
        out_store_name = #{record.outStoreName,jdbcType=VARCHAR},
      </if>
      <if test="record.outStoreAttr != null">
        out_store_attr = #{record.outStoreAttr,jdbcType=VARCHAR},
      </if>
      <if test="record.outStoreSalesLevel != null">
        out_store_sales_level = #{record.outStoreSalesLevel,jdbcType=VARCHAR},
      </if>
      <if test="record.inStoreId != null">
        in_store_id = #{record.inStoreId,jdbcType=BIGINT},
      </if>
      <if test="record.inStoreCode != null">
        in_store_code = #{record.inStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="record.inStoreName != null">
        in_store_name = #{record.inStoreName,jdbcType=VARCHAR},
      </if>
      <if test="record.inStoreAttr != null">
        in_store_attr = #{record.inStoreAttr,jdbcType=VARCHAR},
      </if>
      <if test="record.inStoreSalesLevel != null">
        in_store_sales_level = #{record.inStoreSalesLevel,jdbcType=VARCHAR},
      </if>
      <if test="record.allotGroupCode != null">
        allot_group_code = #{record.allotGroupCode,jdbcType=VARCHAR},
      </if>
      <if test="record.allotGroupName != null">
        allot_group_name = #{record.allotGroupName,jdbcType=VARCHAR},
      </if>
      <if test="record.approveName != null">
        approve_name = #{record.approveName,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsNo != null">
        goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsDesc != null">
        goods_desc = #{record.goodsDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.batchNo != null">
        batch_no = #{record.batchNo,jdbcType=VARCHAR},
      </if>
      <if test="record.produceDate != null">
        produce_date = #{record.produceDate,jdbcType=VARCHAR},
      </if>
      <if test="record.validityDate != null">
        validity_date = #{record.validityDate,jdbcType=VARCHAR},
      </if>
      <if test="record.hdBatchNo != null">
        hd_batch_no = #{record.hdBatchNo,jdbcType=VARCHAR},
      </if>
      <if test="record.sapBatchNo != null">
        sap_batch_no = #{record.sapBatchNo,jdbcType=VARCHAR},
      </if>
      <if test="record.notes != null">
        notes = #{record.notes,jdbcType=VARCHAR},
      </if>
      <if test="record.voidReason != null">
        void_reason = #{record.voidReason,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsCommonName != null">
        goods_common_name = #{record.goodsCommonName,jdbcType=VARCHAR},
      </if>
      <if test="record.manufacturer != null">
        manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="record.unit != null">
        unit = #{record.unit,jdbcType=VARCHAR},
      </if>
      <if test="record.suggestAllotQuantity != null">
        suggest_allot_quantity = #{record.suggestAllotQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.suggestCostAmount != null">
        suggest_cost_amount = #{record.suggestCostAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.realAllotQuantity != null">
        real_allot_quantity = #{record.realAllotQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.realCostAmount != null">
        real_cost_amount = #{record.realCostAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.outApproveStatus != null">
        out_approve_status = #{record.outApproveStatus,jdbcType=TINYINT},
      </if>
      <if test="record.outApproveTime != null">
        out_approve_time = #{record.outApproveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.outAllotQuantity != null">
        out_allot_quantity = #{record.outAllotQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.inApproveStatus != null">
        in_approve_status = #{record.inApproveStatus,jdbcType=TINYINT},
      </if>
      <if test="record.inApproveTime != null">
        in_approve_time = #{record.inApproveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.inAllotQuantity != null">
        in_allot_quantity = #{record.inAllotQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.inStockTime != null">
        in_stock_time = #{record.inStockTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.showStatus != null">
        show_status = #{record.showStatus,jdbcType=TINYINT},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedName != null">
        updated_name = #{record.updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update iscm_suggest_distexec_detail
    set id = #{record.id,jdbcType=BIGINT},
      allot_date = #{record.allotDate,jdbcType=TIMESTAMP},
      allot_month = #{record.allotMonth,jdbcType=INTEGER},
      register_no = #{record.registerNo,jdbcType=VARCHAR},
      register_month = #{record.registerMonth,jdbcType=INTEGER},
      allot_type = #{record.allotType,jdbcType=TINYINT},
      pos_allot_no = #{record.posAllotNo,jdbcType=VARCHAR},
      allot_approve_time = #{record.allotApproveTime,jdbcType=TIMESTAMP},
      pos_allot_detail_id = #{record.posAllotDetailId,jdbcType=BIGINT},
      platform_org_id = #{record.platformOrgId,jdbcType=BIGINT},
      platform_org_name = #{record.platformOrgName,jdbcType=VARCHAR},
      out_company_id = #{record.outCompanyId,jdbcType=BIGINT},
      out_company_code = #{record.outCompanyCode,jdbcType=VARCHAR},
      out_company_name = #{record.outCompanyName,jdbcType=VARCHAR},
      in_company_id = #{record.inCompanyId,jdbcType=BIGINT},
      in_company_code = #{record.inCompanyCode,jdbcType=VARCHAR},
      in_company_name = #{record.inCompanyName,jdbcType=VARCHAR},
      out_store_id = #{record.outStoreId,jdbcType=BIGINT},
      out_store_code = #{record.outStoreCode,jdbcType=VARCHAR},
      out_store_name = #{record.outStoreName,jdbcType=VARCHAR},
      out_store_attr = #{record.outStoreAttr,jdbcType=VARCHAR},
      out_store_sales_level = #{record.outStoreSalesLevel,jdbcType=VARCHAR},
      in_store_id = #{record.inStoreId,jdbcType=BIGINT},
      in_store_code = #{record.inStoreCode,jdbcType=VARCHAR},
      in_store_name = #{record.inStoreName,jdbcType=VARCHAR},
      in_store_attr = #{record.inStoreAttr,jdbcType=VARCHAR},
      in_store_sales_level = #{record.inStoreSalesLevel,jdbcType=VARCHAR},
      allot_group_code = #{record.allotGroupCode,jdbcType=VARCHAR},
      allot_group_name = #{record.allotGroupName,jdbcType=VARCHAR},
      approve_name = #{record.approveName,jdbcType=VARCHAR},
      goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      goods_desc = #{record.goodsDesc,jdbcType=VARCHAR},
      batch_no = #{record.batchNo,jdbcType=VARCHAR},
      produce_date = #{record.produceDate,jdbcType=VARCHAR},
      validity_date = #{record.validityDate,jdbcType=VARCHAR},
      hd_batch_no = #{record.hdBatchNo,jdbcType=VARCHAR},
      sap_batch_no = #{record.sapBatchNo,jdbcType=VARCHAR},
      notes = #{record.notes,jdbcType=VARCHAR},
      void_reason = #{record.voidReason,jdbcType=VARCHAR},
      goods_common_name = #{record.goodsCommonName,jdbcType=VARCHAR},
      manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      unit = #{record.unit,jdbcType=VARCHAR},
      suggest_allot_quantity = #{record.suggestAllotQuantity,jdbcType=DECIMAL},
      suggest_cost_amount = #{record.suggestCostAmount,jdbcType=DECIMAL},
      real_allot_quantity = #{record.realAllotQuantity,jdbcType=DECIMAL},
      real_cost_amount = #{record.realCostAmount,jdbcType=DECIMAL},
      out_approve_status = #{record.outApproveStatus,jdbcType=TINYINT},
      out_approve_time = #{record.outApproveTime,jdbcType=TIMESTAMP},
      out_allot_quantity = #{record.outAllotQuantity,jdbcType=DECIMAL},
      in_approve_status = #{record.inApproveStatus,jdbcType=TINYINT},
      in_approve_time = #{record.inApproveTime,jdbcType=TIMESTAMP},
      in_allot_quantity = #{record.inAllotQuantity,jdbcType=DECIMAL},
      in_stock_time = #{record.inStockTime,jdbcType=TIMESTAMP},
      show_status = #{record.showStatus,jdbcType=TINYINT},
      `status` = #{record.status,jdbcType=TINYINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_name = #{record.createdName,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_name = #{record.updatedName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.iscm.entityTidb.IscmSuggestDistexecDetail">
    update iscm_suggest_distexec_detail
    <set>
      <if test="allotDate != null">
        allot_date = #{allotDate,jdbcType=TIMESTAMP},
      </if>
      <if test="allotMonth != null">
        allot_month = #{allotMonth,jdbcType=INTEGER},
      </if>
      <if test="registerNo != null">
        register_no = #{registerNo,jdbcType=VARCHAR},
      </if>
      <if test="registerMonth != null">
        register_month = #{registerMonth,jdbcType=INTEGER},
      </if>
      <if test="allotType != null">
        allot_type = #{allotType,jdbcType=TINYINT},
      </if>
      <if test="posAllotNo != null">
        pos_allot_no = #{posAllotNo,jdbcType=VARCHAR},
      </if>
      <if test="allotApproveTime != null">
        allot_approve_time = #{allotApproveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="posAllotDetailId != null">
        pos_allot_detail_id = #{posAllotDetailId,jdbcType=BIGINT},
      </if>
      <if test="platformOrgId != null">
        platform_org_id = #{platformOrgId,jdbcType=BIGINT},
      </if>
      <if test="platformOrgName != null">
        platform_org_name = #{platformOrgName,jdbcType=VARCHAR},
      </if>
      <if test="outCompanyId != null">
        out_company_id = #{outCompanyId,jdbcType=BIGINT},
      </if>
      <if test="outCompanyCode != null">
        out_company_code = #{outCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="outCompanyName != null">
        out_company_name = #{outCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="inCompanyId != null">
        in_company_id = #{inCompanyId,jdbcType=BIGINT},
      </if>
      <if test="inCompanyCode != null">
        in_company_code = #{inCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="inCompanyName != null">
        in_company_name = #{inCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="outStoreId != null">
        out_store_id = #{outStoreId,jdbcType=BIGINT},
      </if>
      <if test="outStoreCode != null">
        out_store_code = #{outStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="outStoreName != null">
        out_store_name = #{outStoreName,jdbcType=VARCHAR},
      </if>
      <if test="outStoreAttr != null">
        out_store_attr = #{outStoreAttr,jdbcType=VARCHAR},
      </if>
      <if test="outStoreSalesLevel != null">
        out_store_sales_level = #{outStoreSalesLevel,jdbcType=VARCHAR},
      </if>
      <if test="inStoreId != null">
        in_store_id = #{inStoreId,jdbcType=BIGINT},
      </if>
      <if test="inStoreCode != null">
        in_store_code = #{inStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="inStoreName != null">
        in_store_name = #{inStoreName,jdbcType=VARCHAR},
      </if>
      <if test="inStoreAttr != null">
        in_store_attr = #{inStoreAttr,jdbcType=VARCHAR},
      </if>
      <if test="inStoreSalesLevel != null">
        in_store_sales_level = #{inStoreSalesLevel,jdbcType=VARCHAR},
      </if>
      <if test="allotGroupCode != null">
        allot_group_code = #{allotGroupCode,jdbcType=VARCHAR},
      </if>
      <if test="allotGroupName != null">
        allot_group_name = #{allotGroupName,jdbcType=VARCHAR},
      </if>
      <if test="approveName != null">
        approve_name = #{approveName,jdbcType=VARCHAR},
      </if>
      <if test="goodsNo != null">
        goods_no = #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="goodsDesc != null">
        goods_desc = #{goodsDesc,jdbcType=VARCHAR},
      </if>
      <if test="batchNo != null">
        batch_no = #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="produceDate != null">
        produce_date = #{produceDate,jdbcType=VARCHAR},
      </if>
      <if test="validityDate != null">
        validity_date = #{validityDate,jdbcType=VARCHAR},
      </if>
      <if test="hdBatchNo != null">
        hd_batch_no = #{hdBatchNo,jdbcType=VARCHAR},
      </if>
      <if test="sapBatchNo != null">
        sap_batch_no = #{sapBatchNo,jdbcType=VARCHAR},
      </if>
      <if test="notes != null">
        notes = #{notes,jdbcType=VARCHAR},
      </if>
      <if test="voidReason != null">
        void_reason = #{voidReason,jdbcType=VARCHAR},
      </if>
      <if test="goodsCommonName != null">
        goods_common_name = #{goodsCommonName,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        manufacturer = #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="unit != null">
        unit = #{unit,jdbcType=VARCHAR},
      </if>
      <if test="suggestAllotQuantity != null">
        suggest_allot_quantity = #{suggestAllotQuantity,jdbcType=DECIMAL},
      </if>
      <if test="suggestCostAmount != null">
        suggest_cost_amount = #{suggestCostAmount,jdbcType=DECIMAL},
      </if>
      <if test="realAllotQuantity != null">
        real_allot_quantity = #{realAllotQuantity,jdbcType=DECIMAL},
      </if>
      <if test="realCostAmount != null">
        real_cost_amount = #{realCostAmount,jdbcType=DECIMAL},
      </if>
      <if test="outApproveStatus != null">
        out_approve_status = #{outApproveStatus,jdbcType=TINYINT},
      </if>
      <if test="outApproveTime != null">
        out_approve_time = #{outApproveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="outAllotQuantity != null">
        out_allot_quantity = #{outAllotQuantity,jdbcType=DECIMAL},
      </if>
      <if test="inApproveStatus != null">
        in_approve_status = #{inApproveStatus,jdbcType=TINYINT},
      </if>
      <if test="inApproveTime != null">
        in_approve_time = #{inApproveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="inAllotQuantity != null">
        in_allot_quantity = #{inAllotQuantity,jdbcType=DECIMAL},
      </if>
      <if test="inStockTime != null">
        in_stock_time = #{inStockTime,jdbcType=TIMESTAMP},
      </if>
      <if test="showStatus != null">
        show_status = #{showStatus,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        updated_name = #{updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.iscm.entityTidb.IscmSuggestDistexecDetail">
    update iscm_suggest_distexec_detail
    set allot_date = #{allotDate,jdbcType=TIMESTAMP},
      allot_month = #{allotMonth,jdbcType=INTEGER},
      register_no = #{registerNo,jdbcType=VARCHAR},
      register_month = #{registerMonth,jdbcType=INTEGER},
      allot_type = #{allotType,jdbcType=TINYINT},
      pos_allot_no = #{posAllotNo,jdbcType=VARCHAR},
      allot_approve_time = #{allotApproveTime,jdbcType=TIMESTAMP},
      pos_allot_detail_id = #{posAllotDetailId,jdbcType=BIGINT},
      platform_org_id = #{platformOrgId,jdbcType=BIGINT},
      platform_org_name = #{platformOrgName,jdbcType=VARCHAR},
      out_company_id = #{outCompanyId,jdbcType=BIGINT},
      out_company_code = #{outCompanyCode,jdbcType=VARCHAR},
      out_company_name = #{outCompanyName,jdbcType=VARCHAR},
      in_company_id = #{inCompanyId,jdbcType=BIGINT},
      in_company_code = #{inCompanyCode,jdbcType=VARCHAR},
      in_company_name = #{inCompanyName,jdbcType=VARCHAR},
      out_store_id = #{outStoreId,jdbcType=BIGINT},
      out_store_code = #{outStoreCode,jdbcType=VARCHAR},
      out_store_name = #{outStoreName,jdbcType=VARCHAR},
      out_store_attr = #{outStoreAttr,jdbcType=VARCHAR},
      out_store_sales_level = #{outStoreSalesLevel,jdbcType=VARCHAR},
      in_store_id = #{inStoreId,jdbcType=BIGINT},
      in_store_code = #{inStoreCode,jdbcType=VARCHAR},
      in_store_name = #{inStoreName,jdbcType=VARCHAR},
      in_store_attr = #{inStoreAttr,jdbcType=VARCHAR},
      in_store_sales_level = #{inStoreSalesLevel,jdbcType=VARCHAR},
      allot_group_code = #{allotGroupCode,jdbcType=VARCHAR},
      allot_group_name = #{allotGroupName,jdbcType=VARCHAR},
      approve_name = #{approveName,jdbcType=VARCHAR},
      goods_no = #{goodsNo,jdbcType=VARCHAR},
      goods_desc = #{goodsDesc,jdbcType=VARCHAR},
      batch_no = #{batchNo,jdbcType=VARCHAR},
      produce_date = #{produceDate,jdbcType=VARCHAR},
      validity_date = #{validityDate,jdbcType=VARCHAR},
      hd_batch_no = #{hdBatchNo,jdbcType=VARCHAR},
      sap_batch_no = #{sapBatchNo,jdbcType=VARCHAR},
      notes = #{notes,jdbcType=VARCHAR},
      void_reason = #{voidReason,jdbcType=VARCHAR},
      goods_common_name = #{goodsCommonName,jdbcType=VARCHAR},
      manufacturer = #{manufacturer,jdbcType=VARCHAR},
      unit = #{unit,jdbcType=VARCHAR},
      suggest_allot_quantity = #{suggestAllotQuantity,jdbcType=DECIMAL},
      suggest_cost_amount = #{suggestCostAmount,jdbcType=DECIMAL},
      real_allot_quantity = #{realAllotQuantity,jdbcType=DECIMAL},
      real_cost_amount = #{realCostAmount,jdbcType=DECIMAL},
      out_approve_status = #{outApproveStatus,jdbcType=TINYINT},
      out_approve_time = #{outApproveTime,jdbcType=TIMESTAMP},
      out_allot_quantity = #{outAllotQuantity,jdbcType=DECIMAL},
      in_approve_status = #{inApproveStatus,jdbcType=TINYINT},
      in_approve_time = #{inApproveTime,jdbcType=TIMESTAMP},
      in_allot_quantity = #{inAllotQuantity,jdbcType=DECIMAL},
      in_stock_time = #{inStockTime,jdbcType=TIMESTAMP},
      show_status = #{showStatus,jdbcType=TINYINT},
      `status` = #{status,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_name = #{createdName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_name = #{updatedName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>