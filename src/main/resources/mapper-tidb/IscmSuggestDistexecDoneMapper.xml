<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapperTidb.IscmSuggestDistexecDoneMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entityTidb.IscmSuggestDistexecDone">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="allot_date" jdbcType="TIMESTAMP" property="allotDate" />
    <result column="register_no" jdbcType="VARCHAR" property="registerNo" />
    <result column="pos_allot_no" jdbcType="VARCHAR" property="posAllotNo" />
    <result column="out_company_code" jdbcType="VARCHAR" property="outCompanyCode" />
    <result column="out_store_code" jdbcType="VARCHAR" property="outStoreCode" />
    <result column="in_company_code" jdbcType="VARCHAR" property="inCompanyCode" />
    <result column="in_store_code" jdbcType="VARCHAR" property="inStoreCode" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="in_allot_quantity" jdbcType="DECIMAL" property="inAllotQuantity" />
    <result column="in_store_sales_7" jdbcType="DECIMAL" property="inStoreSales7" />
    <result column="in_store_puramount_7" jdbcType="DECIMAL" property="inStorePuramount7" />
    <result column="in_store_sales_14" jdbcType="DECIMAL" property="inStoreSales14" />
    <result column="in_store_puramount_14" jdbcType="DECIMAL" property="inStorePuramount14" />
    <result column="in_store_sales_30" jdbcType="DECIMAL" property="inStoreSales30" />
    <result column="in_store_puramount_30" jdbcType="DECIMAL" property="inStorePuramount30" />
    <result column="in_store_sales_60" jdbcType="DECIMAL" property="inStoreSales60" />
    <result column="in_store_puramount_60" jdbcType="DECIMAL" property="inStorePuramount60" />
    <result column="in_store_sales_90" jdbcType="DECIMAL" property="inStoreSales90" />
    <result column="in_store_puramount_90" jdbcType="DECIMAL" property="inStorePuramount90" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, allot_date, register_no, pos_allot_no, out_company_code, out_store_code, in_company_code, 
    in_store_code, goods_no, in_allot_quantity, in_store_sales_7, in_store_puramount_7, 
    in_store_sales_14, in_store_puramount_14, in_store_sales_30, in_store_puramount_30, 
    in_store_sales_60, in_store_puramount_60, in_store_sales_90, in_store_puramount_90, 
    `status`, gmt_create, gmt_update, extend, version, created_by, created_name, updated_by, 
    updated_name
  </sql>
  <select id="selectByExample" parameterType="com.cowell.iscm.entityTidb.IscmSuggestDistexecDoneExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from iscm_suggest_distexec_done
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from iscm_suggest_distexec_done
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from iscm_suggest_distexec_done
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.iscm.entityTidb.IscmSuggestDistexecDoneExample">
    delete from iscm_suggest_distexec_done
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entityTidb.IscmSuggestDistexecDone" useGeneratedKeys="true">
    insert into iscm_suggest_distexec_done (allot_date, register_no, pos_allot_no, 
      out_company_code, out_store_code, in_company_code, 
      in_store_code, goods_no, in_allot_quantity, 
      in_store_sales_7, in_store_puramount_7, in_store_sales_14, 
      in_store_puramount_14, in_store_sales_30, in_store_puramount_30, 
      in_store_sales_60, in_store_puramount_60, in_store_sales_90, 
      in_store_puramount_90, `status`, gmt_create, 
      gmt_update, extend, version, 
      created_by, created_name, updated_by, 
      updated_name)
    values (#{allotDate,jdbcType=TIMESTAMP}, #{registerNo,jdbcType=VARCHAR}, #{posAllotNo,jdbcType=VARCHAR}, 
      #{outCompanyCode,jdbcType=VARCHAR}, #{outStoreCode,jdbcType=VARCHAR}, #{inCompanyCode,jdbcType=VARCHAR}, 
      #{inStoreCode,jdbcType=VARCHAR}, #{goodsNo,jdbcType=VARCHAR}, #{inAllotQuantity,jdbcType=DECIMAL}, 
      #{inStoreSales7,jdbcType=DECIMAL}, #{inStorePuramount7,jdbcType=DECIMAL}, #{inStoreSales14,jdbcType=DECIMAL}, 
      #{inStorePuramount14,jdbcType=DECIMAL}, #{inStoreSales30,jdbcType=DECIMAL}, #{inStorePuramount30,jdbcType=DECIMAL}, 
      #{inStoreSales60,jdbcType=DECIMAL}, #{inStorePuramount60,jdbcType=DECIMAL}, #{inStoreSales90,jdbcType=DECIMAL}, 
      #{inStorePuramount90,jdbcType=DECIMAL}, #{status,jdbcType=TINYINT}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtUpdate,jdbcType=TIMESTAMP}, #{extend,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER}, 
      #{createdBy,jdbcType=BIGINT}, #{createdName,jdbcType=VARCHAR}, #{updatedBy,jdbcType=BIGINT}, 
      #{updatedName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entityTidb.IscmSuggestDistexecDone" useGeneratedKeys="true">
    insert into iscm_suggest_distexec_done
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="allotDate != null">
        allot_date,
      </if>
      <if test="registerNo != null">
        register_no,
      </if>
      <if test="posAllotNo != null">
        pos_allot_no,
      </if>
      <if test="outCompanyCode != null">
        out_company_code,
      </if>
      <if test="outStoreCode != null">
        out_store_code,
      </if>
      <if test="inCompanyCode != null">
        in_company_code,
      </if>
      <if test="inStoreCode != null">
        in_store_code,
      </if>
      <if test="goodsNo != null">
        goods_no,
      </if>
      <if test="inAllotQuantity != null">
        in_allot_quantity,
      </if>
      <if test="inStoreSales7 != null">
        in_store_sales_7,
      </if>
      <if test="inStorePuramount7 != null">
        in_store_puramount_7,
      </if>
      <if test="inStoreSales14 != null">
        in_store_sales_14,
      </if>
      <if test="inStorePuramount14 != null">
        in_store_puramount_14,
      </if>
      <if test="inStoreSales30 != null">
        in_store_sales_30,
      </if>
      <if test="inStorePuramount30 != null">
        in_store_puramount_30,
      </if>
      <if test="inStoreSales60 != null">
        in_store_sales_60,
      </if>
      <if test="inStorePuramount60 != null">
        in_store_puramount_60,
      </if>
      <if test="inStoreSales90 != null">
        in_store_sales_90,
      </if>
      <if test="inStorePuramount90 != null">
        in_store_puramount_90,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedName != null">
        updated_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="allotDate != null">
        #{allotDate,jdbcType=TIMESTAMP},
      </if>
      <if test="registerNo != null">
        #{registerNo,jdbcType=VARCHAR},
      </if>
      <if test="posAllotNo != null">
        #{posAllotNo,jdbcType=VARCHAR},
      </if>
      <if test="outCompanyCode != null">
        #{outCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="outStoreCode != null">
        #{outStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="inCompanyCode != null">
        #{inCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="inStoreCode != null">
        #{inStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsNo != null">
        #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="inAllotQuantity != null">
        #{inAllotQuantity,jdbcType=DECIMAL},
      </if>
      <if test="inStoreSales7 != null">
        #{inStoreSales7,jdbcType=DECIMAL},
      </if>
      <if test="inStorePuramount7 != null">
        #{inStorePuramount7,jdbcType=DECIMAL},
      </if>
      <if test="inStoreSales14 != null">
        #{inStoreSales14,jdbcType=DECIMAL},
      </if>
      <if test="inStorePuramount14 != null">
        #{inStorePuramount14,jdbcType=DECIMAL},
      </if>
      <if test="inStoreSales30 != null">
        #{inStoreSales30,jdbcType=DECIMAL},
      </if>
      <if test="inStorePuramount30 != null">
        #{inStorePuramount30,jdbcType=DECIMAL},
      </if>
      <if test="inStoreSales60 != null">
        #{inStoreSales60,jdbcType=DECIMAL},
      </if>
      <if test="inStorePuramount60 != null">
        #{inStorePuramount60,jdbcType=DECIMAL},
      </if>
      <if test="inStoreSales90 != null">
        #{inStoreSales90,jdbcType=DECIMAL},
      </if>
      <if test="inStorePuramount90 != null">
        #{inStorePuramount90,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        #{updatedName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.iscm.entityTidb.IscmSuggestDistexecDoneExample" resultType="java.lang.Long">
    select count(*) from iscm_suggest_distexec_done
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update iscm_suggest_distexec_done
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.allotDate != null">
        allot_date = #{record.allotDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.registerNo != null">
        register_no = #{record.registerNo,jdbcType=VARCHAR},
      </if>
      <if test="record.posAllotNo != null">
        pos_allot_no = #{record.posAllotNo,jdbcType=VARCHAR},
      </if>
      <if test="record.outCompanyCode != null">
        out_company_code = #{record.outCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.outStoreCode != null">
        out_store_code = #{record.outStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="record.inCompanyCode != null">
        in_company_code = #{record.inCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.inStoreCode != null">
        in_store_code = #{record.inStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsNo != null">
        goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="record.inAllotQuantity != null">
        in_allot_quantity = #{record.inAllotQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.inStoreSales7 != null">
        in_store_sales_7 = #{record.inStoreSales7,jdbcType=DECIMAL},
      </if>
      <if test="record.inStorePuramount7 != null">
        in_store_puramount_7 = #{record.inStorePuramount7,jdbcType=DECIMAL},
      </if>
      <if test="record.inStoreSales14 != null">
        in_store_sales_14 = #{record.inStoreSales14,jdbcType=DECIMAL},
      </if>
      <if test="record.inStorePuramount14 != null">
        in_store_puramount_14 = #{record.inStorePuramount14,jdbcType=DECIMAL},
      </if>
      <if test="record.inStoreSales30 != null">
        in_store_sales_30 = #{record.inStoreSales30,jdbcType=DECIMAL},
      </if>
      <if test="record.inStorePuramount30 != null">
        in_store_puramount_30 = #{record.inStorePuramount30,jdbcType=DECIMAL},
      </if>
      <if test="record.inStoreSales60 != null">
        in_store_sales_60 = #{record.inStoreSales60,jdbcType=DECIMAL},
      </if>
      <if test="record.inStorePuramount60 != null">
        in_store_puramount_60 = #{record.inStorePuramount60,jdbcType=DECIMAL},
      </if>
      <if test="record.inStoreSales90 != null">
        in_store_sales_90 = #{record.inStoreSales90,jdbcType=DECIMAL},
      </if>
      <if test="record.inStorePuramount90 != null">
        in_store_puramount_90 = #{record.inStorePuramount90,jdbcType=DECIMAL},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedName != null">
        updated_name = #{record.updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update iscm_suggest_distexec_done
    set id = #{record.id,jdbcType=BIGINT},
      allot_date = #{record.allotDate,jdbcType=TIMESTAMP},
      register_no = #{record.registerNo,jdbcType=VARCHAR},
      pos_allot_no = #{record.posAllotNo,jdbcType=VARCHAR},
      out_company_code = #{record.outCompanyCode,jdbcType=VARCHAR},
      out_store_code = #{record.outStoreCode,jdbcType=VARCHAR},
      in_company_code = #{record.inCompanyCode,jdbcType=VARCHAR},
      in_store_code = #{record.inStoreCode,jdbcType=VARCHAR},
      goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      in_allot_quantity = #{record.inAllotQuantity,jdbcType=DECIMAL},
      in_store_sales_7 = #{record.inStoreSales7,jdbcType=DECIMAL},
      in_store_puramount_7 = #{record.inStorePuramount7,jdbcType=DECIMAL},
      in_store_sales_14 = #{record.inStoreSales14,jdbcType=DECIMAL},
      in_store_puramount_14 = #{record.inStorePuramount14,jdbcType=DECIMAL},
      in_store_sales_30 = #{record.inStoreSales30,jdbcType=DECIMAL},
      in_store_puramount_30 = #{record.inStorePuramount30,jdbcType=DECIMAL},
      in_store_sales_60 = #{record.inStoreSales60,jdbcType=DECIMAL},
      in_store_puramount_60 = #{record.inStorePuramount60,jdbcType=DECIMAL},
      in_store_sales_90 = #{record.inStoreSales90,jdbcType=DECIMAL},
      in_store_puramount_90 = #{record.inStorePuramount90,jdbcType=DECIMAL},
      `status` = #{record.status,jdbcType=TINYINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_name = #{record.createdName,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_name = #{record.updatedName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.iscm.entityTidb.IscmSuggestDistexecDone">
    update iscm_suggest_distexec_done
    <set>
      <if test="allotDate != null">
        allot_date = #{allotDate,jdbcType=TIMESTAMP},
      </if>
      <if test="registerNo != null">
        register_no = #{registerNo,jdbcType=VARCHAR},
      </if>
      <if test="posAllotNo != null">
        pos_allot_no = #{posAllotNo,jdbcType=VARCHAR},
      </if>
      <if test="outCompanyCode != null">
        out_company_code = #{outCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="outStoreCode != null">
        out_store_code = #{outStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="inCompanyCode != null">
        in_company_code = #{inCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="inStoreCode != null">
        in_store_code = #{inStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsNo != null">
        goods_no = #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="inAllotQuantity != null">
        in_allot_quantity = #{inAllotQuantity,jdbcType=DECIMAL},
      </if>
      <if test="inStoreSales7 != null">
        in_store_sales_7 = #{inStoreSales7,jdbcType=DECIMAL},
      </if>
      <if test="inStorePuramount7 != null">
        in_store_puramount_7 = #{inStorePuramount7,jdbcType=DECIMAL},
      </if>
      <if test="inStoreSales14 != null">
        in_store_sales_14 = #{inStoreSales14,jdbcType=DECIMAL},
      </if>
      <if test="inStorePuramount14 != null">
        in_store_puramount_14 = #{inStorePuramount14,jdbcType=DECIMAL},
      </if>
      <if test="inStoreSales30 != null">
        in_store_sales_30 = #{inStoreSales30,jdbcType=DECIMAL},
      </if>
      <if test="inStorePuramount30 != null">
        in_store_puramount_30 = #{inStorePuramount30,jdbcType=DECIMAL},
      </if>
      <if test="inStoreSales60 != null">
        in_store_sales_60 = #{inStoreSales60,jdbcType=DECIMAL},
      </if>
      <if test="inStorePuramount60 != null">
        in_store_puramount_60 = #{inStorePuramount60,jdbcType=DECIMAL},
      </if>
      <if test="inStoreSales90 != null">
        in_store_sales_90 = #{inStoreSales90,jdbcType=DECIMAL},
      </if>
      <if test="inStorePuramount90 != null">
        in_store_puramount_90 = #{inStorePuramount90,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        updated_name = #{updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.iscm.entityTidb.IscmSuggestDistexecDone">
    update iscm_suggest_distexec_done
    set allot_date = #{allotDate,jdbcType=TIMESTAMP},
      register_no = #{registerNo,jdbcType=VARCHAR},
      pos_allot_no = #{posAllotNo,jdbcType=VARCHAR},
      out_company_code = #{outCompanyCode,jdbcType=VARCHAR},
      out_store_code = #{outStoreCode,jdbcType=VARCHAR},
      in_company_code = #{inCompanyCode,jdbcType=VARCHAR},
      in_store_code = #{inStoreCode,jdbcType=VARCHAR},
      goods_no = #{goodsNo,jdbcType=VARCHAR},
      in_allot_quantity = #{inAllotQuantity,jdbcType=DECIMAL},
      in_store_sales_7 = #{inStoreSales7,jdbcType=DECIMAL},
      in_store_puramount_7 = #{inStorePuramount7,jdbcType=DECIMAL},
      in_store_sales_14 = #{inStoreSales14,jdbcType=DECIMAL},
      in_store_puramount_14 = #{inStorePuramount14,jdbcType=DECIMAL},
      in_store_sales_30 = #{inStoreSales30,jdbcType=DECIMAL},
      in_store_puramount_30 = #{inStorePuramount30,jdbcType=DECIMAL},
      in_store_sales_60 = #{inStoreSales60,jdbcType=DECIMAL},
      in_store_puramount_60 = #{inStorePuramount60,jdbcType=DECIMAL},
      in_store_sales_90 = #{inStoreSales90,jdbcType=DECIMAL},
      in_store_puramount_90 = #{inStorePuramount90,jdbcType=DECIMAL},
      `status` = #{status,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_name = #{createdName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_name = #{updatedName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>