<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapperHana.HanaStoreRealtionMapper">

    <resultMap id="ZMMT0288ResultMap" type="com.cowell.iscm.entity.SapZmmt0288">
        <result column="MANDT" jdbcType="VARCHAR" property="mandt" />
        <result column="ZGUID" jdbcType="VARCHAR" property="zguid" />
        <result column="WERKS" jdbcType="VARCHAR" property="werks" />
        <result column="MATNR" jdbcType="VARCHAR" property="matnr" />
        <result column="EKORG" jdbcType="VARCHAR" property="ekorg" />
        <result column="BSART" jdbcType="VARCHAR" property="bsart" />
        <result column="BUKRS" jdbcType="VARCHAR" property="bukrs" />
        <result column="EKGRP" jdbcType="VARCHAR" property="ekgrp" />
        <result column="NAME1_DC" jdbcType="VARCHAR" property="name1Dc" />
        <result column="LIFNR_XD" jdbcType="VARCHAR" property="lifnrXd" />
        <result column="NAME1_XD" jdbcType="VARCHAR" property="name1Xd" />
        <result column="MENGE_XD" jdbcType="DECIMAL" property="mengeXd" />
        <result column="MEINS_XD" jdbcType="VARCHAR" property="meinsXd" />
        <result column="NETPR_XD" jdbcType="DECIMAL" property="netprXd" />
        <result column="BRTWR_XD" jdbcType="DECIMAL" property="brtwrXd" />
        <result column="NETSUM_XD" jdbcType="DECIMAL" property="netsumXd" />
        <result column="BRTSUM_XD" jdbcType="DECIMAL" property="brtsumXd" />
        <result column="PEINH_XD" jdbcType="DECIMAL" property="peinhXd" />
        <result column="TAX_XD" jdbcType="VARCHAR" property="taxXd" />
        <result column="TAX_XD_T" jdbcType="VARCHAR" property="taxXdT" />
        <result column="KONNR" jdbcType="VARCHAR" property="konnr" />
        <result column="KTPNR" jdbcType="VARCHAR" property="ktpnr" />
        <result column="ZPLCODE" jdbcType="VARCHAR" property="zplcode" />
        <result column="ZPLSUG" jdbcType="VARCHAR" property="zplsug" />
        <result column="ZCJMS" jdbcType="VARCHAR" property="zcjms" />
        <result column="ZTERM" jdbcType="VARCHAR" property="zterm" />
        <result column="ZTERM_T" jdbcType="VARCHAR" property="ztermT" />
        <result column="ZCGBZSZ" jdbcType="VARCHAR" property="zcgbzsz" />
        <result column="MENGE_SUG" jdbcType="DECIMAL" property="mengeSug" />
        <result column="MEINS_SUG" jdbcType="VARCHAR" property="meinsSug" />
        <result column="MENGE_ZBZ" jdbcType="DECIMAL" property="mengeZbz" />
        <result column="MENGE_XBZ" jdbcType="DECIMAL" property="mengeXbz" />
        <result column="MENGE_ZZ" jdbcType="DECIMAL" property="mengeZz" />
        <result column="MEINS_ZZ" jdbcType="VARCHAR" property="meinsZz" />
        <result column="ZZBZL" jdbcType="VARCHAR" property="zzbzl" />
        <result column="ZZCQTY" jdbcType="DECIMAL" property="zzcqty" />
        <result column="ZXSJWXS" jdbcType="DECIMAL" property="zxsjwxs" />
        <result column="ZXXJWXS" jdbcType="DECIMAL" property="zxxjwxs" />
        <result column="KNTTP" jdbcType="VARCHAR" property="knttp" />
        <result column="KOSTL" jdbcType="VARCHAR" property="kostl" />
        <result column="ZUSERID" jdbcType="VARCHAR" property="zuserid" />
        <result column="ZYWXM" jdbcType="VARCHAR" property="zywxm" />
        <result column="ZZHXS" jdbcType="VARCHAR" property="zzhxs" />
        <result column="ZBCDHL" jdbcType="DECIMAL" property="zbcdhl" />
        <result column="ZMDZ" jdbcType="VARCHAR" property="zmdz" />
        <result column="ZMDZ_T" jdbcType="VARCHAR" property="zmdzT" />
        <result column="ZSPJB" jdbcType="DECIMAL" property="zspjb" />
        <result column="EKORG_02" jdbcType="VARCHAR" property="ekorg02" />
        <result column="ZSPCKDDLX" jdbcType="VARCHAR" property="zspckddlx" />
        <result column="ZGHDC" jdbcType="VARCHAR" property="zghdc" />
        <result column="ZZCQTY_BDP" jdbcType="DECIMAL" property="zzcqtyBdp" />
        <result column="ZZBZL_BDP" jdbcType="DECIMAL" property="zzbzlBdp" />
        <result column="ZJHJH" jdbcType="VARCHAR" property="zjhjh" />
        <result column="LGORT" jdbcType="VARCHAR" property="lgort" />
        <result column="ZBZGG" jdbcType="VARCHAR" property="zbzgg" />
        <result column="REMARK" jdbcType="VARCHAR" property="remark" />
        <result column="ZCRDATE" jdbcType="VARCHAR" property="zcrdate" />
        <result column="ZCRTIME" jdbcType="VARCHAR" property="zcrtime" />
        <result column="ZCRNAME" jdbcType="VARCHAR" property="zcrname" />
        <result column="ZCHDATE" jdbcType="VARCHAR" property="zchdate" />
        <result column="ZCHTIME" jdbcType="VARCHAR" property="zchtime" />
        <result column="ZCHNAME" jdbcType="VARCHAR" property="zchname" />
        <result column="EBELN" jdbcType="VARCHAR" property="ebeln" />
        <result column="EBELP" jdbcType="VARCHAR" property="ebelp" />
        <result column="PURREQNO" jdbcType="VARCHAR" property="purreqno" />
        <result column="STORELINENO" jdbcType="VARCHAR" property="storelineno" />
        <result column="ZBYZD2" jdbcType="VARCHAR" property="zbyzd2" />
        <result column="ZSTOCK_USE" jdbcType="DECIMAL" property="zstockUse" />
        <result column="LABST_DC_JS" jdbcType="DECIMAL" property="labstDcJs" />
        <result column="MENGE_WQ_JS" jdbcType="DECIMAL" property="mengeWqJs" />
        <result column="ZZXL_30_JS" jdbcType="DECIMAL" property="zzxl30Js" />
        <result column="ZMDQTY_JS" jdbcType="DECIMAL" property="zmdqtyJs" />
        <result column="ZTSDDBS" jdbcType="VARCHAR" property="ztsddbs" />
        <result column="ZTSDDYDH" jdbcType="VARCHAR" property="ztsddydh" />
        <result column="ZZQXDJH" jdbcType="VARCHAR" property="zzqxdjh" />
        <result column="ZZQXDHH" jdbcType="VARCHAR" property="zzqxdhh" />
        <result column="ZJTNDJTJ" jdbcType="VARCHAR" property="zjtndjtj" />
        <result column="ZCYJSWQ" jdbcType="DECIMAL" property="zcyjswq" />
        <result column="ZTCWQ" jdbcType="DECIMAL" property="ztcwq" />
        <result column="ZDCKYKC" jdbcType="DECIMAL" property="zdckykc" />
        <result column="MENGE_WQ" jdbcType="DECIMAL" property="mengeWq" />
        <result column="TZTS" jdbcType="INTEGER" property="tzts" />
        <result column="TZTS_YXQZ" jdbcType="VARCHAR" property="tztsYxqz" />
        <result column="ZGYSQZQ" jdbcType="INTEGER" property="zgysqzq" />
        <result column="ZSPQZQ" jdbcType="DECIMAL" property="zspqzq" />
        <result column="ZSPQZQQSRQ" jdbcType="VARCHAR" property="zspqzqqsrq" />
        <result column="ZSPQZQZZRQ" jdbcType="VARCHAR" property="zspqzqzzrq" />
        <result column="ZBCKCTS" jdbcType="DECIMAL" property="zbckcts" />
        <result column="ZBCKCTSQSRQ" jdbcType="VARCHAR" property="zbckctsqsrq" />
        <result column="ZBCKCTSZZRQ" jdbcType="VARCHAR" property="zbckctszzrq" />
        <result column="ZBCDHLQSRQ" jdbcType="VARCHAR" property="zbcdhlqsrq" />
        <result column="ZBCDHLZZRQ" jdbcType="VARCHAR" property="zbcdhlzzrq" />
        <result column="ZSFJS" jdbcType="VARCHAR" property="zsfjs" />
        <result column="ZXQHBS" jdbcType="DECIMAL" property="zxqhbs" />
        <result column="ZAQKCBCTS" jdbcType="DECIMAL" property="zaqkcbcts" />
        <result column="ZSFQZ" jdbcType="VARCHAR" property="zsfqz" />
        <result column="ZTSXSQSRQ" jdbcType="VARCHAR" property="ztsxsqsrq" />
        <result column="ZTSXSJSRQ" jdbcType="VARCHAR" property="ztsxsjsrq" />
        <result column="ZTSXSQZZB" jdbcType="DECIMAL" property="ztsxsqzzb" />
        <result column="ZKCSX" jdbcType="DECIMAL" property="zkcsx" />
        <result column="ZKCXX" jdbcType="DECIMAL" property="zkcxx" />
        <result column="ZCGTPJL" jdbcType="VARCHAR" property="zcgtpjl" />
        <result column="ZCGTPJLXM" jdbcType="VARCHAR" property="zcgtpjlxm" />
        <result column="ZYJYWXDTS" jdbcType="INTEGER" property="zyjywxdts" />
        <result column="BRTWR_LAST" jdbcType="DECIMAL" property="brtwrLast" />
        <result column="PEINH_LAST" jdbcType="DECIMAL" property="peinhLast" />
        <result column="ZTZDCKC" jdbcType="DECIMAL" property="ztzdckc" />
        <result column="ZSTOCK_DC" jdbcType="DECIMAL" property="zstockDc" />
        <result column="ZSTOCK_STORE" jdbcType="DECIMAL" property="zstockStore" />
        <result column="ZZC_INACTIVE_DC_STOCK" jdbcType="DECIMAL" property="zzcInactiveDcStock" />
        <result column="ZSTOCK_TOTAL" jdbcType="DECIMAL" property="zstockTotal" />
        <result column="ZFIX_TBS" jdbcType="DECIMAL" property="zfixTbs" />
        <result column="ZSUM_R_V" jdbcType="DECIMAL" property="zsumRV" />
        <result column="ZDC_INV_UPPER" jdbcType="DECIMAL" property="zdcInvUpper" />
        <result column="ZTZDCDKC1" jdbcType="DECIMAL" property="ztzdcdkc1" />
        <result column="ZTZDCDKC2" jdbcType="DECIMAL" property="ztzdcdkc2" />
        <result column="ZMENGE_WQ7" jdbcType="DECIMAL" property="zmengeWq7" />
        <result column="ZMAX_APPLY" jdbcType="DECIMAL" property="zmaxApply" />
        <result column="ZINV_UPPER" jdbcType="DECIMAL" property="zinvUpper" />
        <result column="ZMENGE_SUG" jdbcType="DECIMAL" property="zmengeSug" />
        <result column="ZDHD_B" jdbcType="DECIMAL" property="zdhdB" />
        <result column="ZDC_L" jdbcType="DECIMAL" property="zdcL" />
        <result column="ZGOODS_L" jdbcType="VARCHAR" property="zgoodsL" />
        <result column="ZS_STOCK" jdbcType="DECIMAL" property="zsStock" />
        <result column="ZRAW" jdbcType="DECIMAL" property="zraw" />
        <result column="ZNONCXB" jdbcType="VARCHAR" property="znoncxb" />
        <result column="ZJM_STORE_STOCK" jdbcType="DECIMAL" property="zjmStoreStock" />
        <result column="ZDHD" jdbcType="DECIMAL" property="zdhd" />
        <result column="ZHTLXBS" jdbcType="VARCHAR" property="zhtlxbs" />
        <result column="REASON_TYPE" jdbcType="VARCHAR" property="reasonType" />
        <result column="ZPYYY" jdbcType="VARCHAR" property="zpyyy" />
        <result column="BRTWR_JY" jdbcType="DECIMAL" property="brtwrJy" />
        <result column="ZHTLXBS_JY" jdbcType="VARCHAR" property="zhtlxbsJy" />
        <result column="ZNCSQWQ" jdbcType="DECIMAL" property="zncsqwq" />
    </resultMap>


    <resultMap id="ZMMT0098ResultMap" type="com.cowell.iscm.entity.SapZmmt0098">
        <result column="MANDT" jdbcType="VARCHAR" property="mandt" />
        <result column="WERKS" jdbcType="VARCHAR" property="werks" />
        <result column="MATNR" jdbcType="VARCHAR" property="matnr" />
        <result column="MATNR_DESC" jdbcType="VARCHAR" property="matnrDesc" />
        <result column="ZISAPRAS" jdbcType="VARCHAR" property="zisapras" />
        <result column="ZCGYGH" jdbcType="VARCHAR" property="zcgygh" />
        <result column="ZPURAGT" jdbcType="VARCHAR" property="zpuragt" />
        <result column="ZXSJWXS" jdbcType="DECIMAL" property="zxsjwxs" />
        <result column="ZXXJWXS" jdbcType="DECIMAL" property="zxxjwxs" />
        <result column="ZSPQZQ" jdbcType="DECIMAL" property="zspqzq" />
        <result column="ZSPQZQQSRQ" jdbcType="VARCHAR" property="zspqzqqsrq" />
        <result column="ZSPQZQZZRQ" jdbcType="VARCHAR" property="zspqzqzzrq" />
        <result column="ZBCKCTS" jdbcType="DECIMAL" property="zbckcts" />
        <result column="ZBCKCTSQSRQ" jdbcType="VARCHAR" property="zbckctsqsrq" />
        <result column="ZBCKCTSZZRQ" jdbcType="VARCHAR" property="zbckctszzrq" />
        <result column="ZBCDHL" jdbcType="DECIMAL" property="zbcdhl" />
        <result column="ZBCDHLQSRQ" jdbcType="VARCHAR" property="zbcdhlqsrq" />
        <result column="ZBCDHLZZRQ" jdbcType="VARCHAR" property="zbcdhlzzrq" />
        <result column="ZDYZD1" jdbcType="VARCHAR" property="zdyzd1" />
        <result column="ZDYZD2" jdbcType="VARCHAR" property="zdyzd2" />
        <result column="ZDYZD3" jdbcType="VARCHAR" property="zdyzd3" />
        <result column="ZDYZD4" jdbcType="VARCHAR" property="zdyzd4" />
        <result column="ZDYZD5" jdbcType="VARCHAR" property="zdyzd5" />
        <result column="ZTHFS" jdbcType="VARCHAR" property="zthfs" />
        <result column="ZCGBZSZ" jdbcType="VARCHAR" property="zcgbzsz" />
        <result column="ZZCGSX_PF" jdbcType="VARCHAR" property="zzcgsxPf" />
        <result column="ZBCSX_PF" jdbcType="VARCHAR" property="zbcsxPf" />
        <result column="ZQZQ_PF" jdbcType="INTEGER" property="zqzqPf" />
        <result column="ZJGQ_PF" jdbcType="INTEGER" property="zjgqPf" />
        <result column="ZAQKCTS_PF" jdbcType="INTEGER" property="zaqkctsPf" />
        <result column="ZBCDHL_PF" jdbcType="DECIMAL" property="zbcdhlPf" />
        <result column="ZBCKCTS_PF" jdbcType="INTEGER" property="zbckctsPf" />
        <result column="ZQSRQ_PF" jdbcType="VARCHAR" property="zqsrqPf" />
        <result column="ZZZRQ_PF" jdbcType="VARCHAR" property="zzzrqPf" />
        <result column="CPUDT" jdbcType="VARCHAR" property="cpudt" />
        <result column="CPUTM" jdbcType="VARCHAR" property="cputm" />
        <result column="ANNAM" jdbcType="VARCHAR" property="annam" />
        <result column="AEDAT" jdbcType="VARCHAR" property="aedat" />
        <result column="AEZET" jdbcType="VARCHAR" property="aezet" />
        <result column="AENAM" jdbcType="VARCHAR" property="aenam" />
        <result column="ZZHXS" jdbcType="VARCHAR" property="zzhxs" />
        <result column="ZSHCK" jdbcType="VARCHAR" property="zshck" />
        <result column="ZSFJS" jdbcType="VARCHAR" property="zsfjs" />
        <result column="ZXQHBS" jdbcType="DECIMAL" property="zxqhbs" />
        <result column="ZAQKCBCTS" jdbcType="DECIMAL" property="zaqkcbcts" />
        <result column="ZSFQZ" jdbcType="VARCHAR" property="zsfqz" />
        <result column="ZTSXSQSRQ" jdbcType="VARCHAR" property="ztsxsqsrq" />
        <result column="ZTSXSJSRQ" jdbcType="VARCHAR" property="ztsxsjsrq" />
        <result column="ZTSXSQZZB" jdbcType="DECIMAL" property="ztsxsqzzb" />
        <result column="ZSPCKDDLX" jdbcType="VARCHAR" property="zspckddlx" />
        <result column="ZGHDC" jdbcType="VARCHAR" property="zghdc" />
        <result column="ZKCSX" jdbcType="DECIMAL" property="zkcsx" />
        <result column="ZKCXX" jdbcType="DECIMAL" property="zkcxx" />
        <result column="ZZCQTY" jdbcType="DECIMAL" property="zzcqty" />
        <result column="ZZBZL" jdbcType="DECIMAL" property="zzbzl" />
        <result column="ZCGTPJL" jdbcType="VARCHAR" property="zcgtpjl" />
        <result column="ZCGTPJLXM" jdbcType="VARCHAR" property="zcgtpjlxm" />
    </resultMap>

    <resultMap id="ZMMT0287ResultMap" type="com.cowell.iscm.entity.SapZmmt0287">
        <result column="MANDT" jdbcType="VARCHAR" property="mandt" />
        <result column="PURREQNO" jdbcType="VARCHAR" property="purreqno" />
        <result column="STORELINENO" jdbcType="VARCHAR" property="storelineno" />
        <result column="BUKRS" jdbcType="VARCHAR" property="bukrs" />
        <result column="EKORG" jdbcType="VARCHAR" property="ekorg" />
        <result column="WERKS" jdbcType="VARCHAR" property="werks" />
        <result column="ZZZZSHD" jdbcType="VARCHAR" property="zzzzshd" />
        <result column="BSART" jdbcType="VARCHAR" property="bsart" />
        <result column="MATNR" jdbcType="VARCHAR" property="matnr" />
        <result column="MENGE" jdbcType="DECIMAL" property="menge" />
        <result column="ZSPSL" jdbcType="DECIMAL" property="zspsl" />
        <result column="MEINS" jdbcType="VARCHAR" property="meins" />
        <result column="ZZCXBJS" jdbcType="VARCHAR" property="zzcxbjs" />
        <result column="ZJHBHSL" jdbcType="DECIMAL" property="zjhbhsl" />
        <result column="ZYYBZ" jdbcType="VARCHAR" property="zyybz" />
        <result column="BADAT" jdbcType="VARCHAR" property="badat" />
        <result column="ZSPZT" jdbcType="VARCHAR" property="zspzt" />
        <result column="ZCLZT" jdbcType="VARCHAR" property="zclzt" />
        <result column="LOEKZ" jdbcType="VARCHAR" property="loekz" />
        <result column="ERDAT" jdbcType="VARCHAR" property="erdat" />
        <result column="ERZET" jdbcType="VARCHAR" property="erzet" />
        <result column="ERNAM" jdbcType="VARCHAR" property="ernam" />
        <result column="AEDAT" jdbcType="VARCHAR" property="aedat" />
        <result column="AEZET" jdbcType="VARCHAR" property="aezet" />
        <result column="AENAM" jdbcType="VARCHAR" property="aenam" />
        <result column="ZSHCK" jdbcType="VARCHAR" property="zshck" />
        <result column="ZCGYGH" jdbcType="VARCHAR" property="zcgygh" />
        <result column="ZPURAGT" jdbcType="VARCHAR" property="zpuragt" />
        <result column="CHARG" jdbcType="VARCHAR" property="charg" />
        <result column="LGORT" jdbcType="VARCHAR" property="lgort" />
        <result column="RESLO" jdbcType="VARCHAR" property="reslo" />
        <result column="MSG" jdbcType="VARCHAR" property="msg" />
        <result column="EBELN" jdbcType="VARCHAR" property="ebeln" />
        <result column="EBELP" jdbcType="VARCHAR" property="ebelp" />
        <result column="VBELN" jdbcType="VARCHAR" property="vbeln" />
        <result column="POSNR" jdbcType="VARCHAR" property="posnr" />
        <result column="BRTWR" jdbcType="DECIMAL" property="brtwr" />
        <result column="KPEIN2" jdbcType="DECIMAL" property="kpein2" />
        <result column="ZCGZB" jdbcType="VARCHAR" property="zcgzb" />
        <result column="ZYPURREQNO" jdbcType="VARCHAR" property="zypurreqno" />
        <result column="ZYSTORELINENO" jdbcType="VARCHAR" property="zystorelineno" />
        <result column="ZSFCF" jdbcType="VARCHAR" property="zsfcf" />
        <result column="ZMAIN" jdbcType="VARCHAR" property="zmain" />
        <result column="ZZXL_30_JS" jdbcType="DECIMAL" property="zzxl30Js" />
        <result column="ZMDQTY_JS" jdbcType="DECIMAL" property="zmdqtyJs" />
        <result column="ZDCKYKC" jdbcType="DECIMAL" property="zdckykc" />
        <result column="MENGE_SUG" jdbcType="DECIMAL" property="mengeSug" />
        <result column="ZPLSUG" jdbcType="VARCHAR" property="zplsug" />
        <result column="ZCYJSWQ" jdbcType="DECIMAL" property="zcyjswq" />
        <result column="LABST_DC_JS" jdbcType="DECIMAL" property="labstDcJs" />
    </resultMap>

    <resultMap id="ZMMT0085ResultMap" type="com.cowell.iscm.entity.SapZmmt0085">
        <result column="MANDT" jdbcType="VARCHAR" property="mandt" />
        <result column="PURREQNO" jdbcType="VARCHAR" property="purreqno" />
        <result column="BILLTYPE" jdbcType="VARCHAR" property="billtype" />
        <result column="SOURCETYPE" jdbcType="VARCHAR" property="sourcetype" />
        <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
        <result column="BUKRS" jdbcType="VARCHAR" property="bukrs" />
        <result column="EKORG" jdbcType="VARCHAR" property="ekorg" />
        <result column="ZZZZQHD" jdbcType="VARCHAR" property="zzzzqhd" />
        <result column="BEDAT" jdbcType="VARCHAR" property="bedat" />
        <result column="ZCJSJ" jdbcType="VARCHAR" property="zcjsj" />
        <result column="RETURNREASON" jdbcType="VARCHAR" property="returnreason" />
        <result column="EXPECTEDDATE" jdbcType="VARCHAR" property="expecteddate" />
        <result column="NOTES" jdbcType="VARCHAR" property="notes" />
        <result column="LIFNR" jdbcType="VARCHAR" property="lifnr" />
        <result column="BUKRS_S" jdbcType="VARCHAR" property="bukrsS" />
        <result column="VKORG" jdbcType="VARCHAR" property="vkorg" />
        <result column="RESWK" jdbcType="VARCHAR" property="reswk" />
        <result column="LOGRT" jdbcType="VARCHAR" property="logrt" />
        <result column="ZZZWLMS" jdbcType="VARCHAR" property="zzzwlms" />
        <result column="ZZZZSHD" jdbcType="VARCHAR" property="zzzzshd" />
        <result column="ZZZSHKC" jdbcType="VARCHAR" property="zzzshkc" />
        <result column="ZZZUSERID" jdbcType="VARCHAR" property="zzzuserid" />
        <result column="ZZCGY" jdbcType="VARCHAR" property="zzcgy" />
        <result column="ZZDHY" jdbcType="VARCHAR" property="zzdhy" />
        <result column="EBELN" jdbcType="VARCHAR" property="ebeln" />
        <result column="ZSTATUS" jdbcType="VARCHAR" property="zstatus" />
        <result column="ZSEND" jdbcType="VARCHAR" property="zsend" />
        <result column="MESS" jdbcType="VARCHAR" property="mess" />
        <result column="ZCLOSE" jdbcType="VARCHAR" property="zclose" />
        <result column="ZCLOSE_TEXT" jdbcType="VARCHAR" property="zcloseText" />
        <result column="ZPUCH" jdbcType="VARCHAR" property="zpuch" />
    </resultMap>


    <resultMap id="ZMMT0086ResultMap" type="com.cowell.iscm.entity.SapZmmt0086">
        <result column="MANDT" jdbcType="VARCHAR" property="mandt" />
        <result column="PURREQNO" jdbcType="VARCHAR" property="purreqno" />
        <result column="STORELINENO" jdbcType="VARCHAR" property="storelineno" />
        <result column="MATNR" jdbcType="VARCHAR" property="matnr" />
        <result column="MENGE" jdbcType="DECIMAL" property="menge" />
        <result column="MEINS" jdbcType="VARCHAR" property="meins" />
        <result column="SERIAL" jdbcType="VARCHAR" property="serial" />
        <result column="CHARG" jdbcType="VARCHAR" property="charg" />
        <result column="RETREASON2" jdbcType="VARCHAR" property="retreason2" />
        <result column="ZNETPR" jdbcType="DECIMAL" property="znetpr" />
        <result column="ZZCXBJS" jdbcType="VARCHAR" property="zzcxbjs" />
        <result column="RETPO" jdbcType="VARCHAR" property="retpo" />
        <result column="UMSON" jdbcType="VARCHAR" property="umson" />
        <result column="MENGE2" jdbcType="DECIMAL" property="menge2" />
        <result column="RESLO" jdbcType="VARCHAR" property="reslo" />
        <result column="ZDCQTY" jdbcType="DECIMAL" property="zdcqty" />
        <result column="ZCKCXB1" jdbcType="VARCHAR" property="zckcxb1" />
        <result column="ZCKCXB2" jdbcType="VARCHAR" property="zckcxb2" />
        <result column="ZYDYXL1" jdbcType="DECIMAL" property="zydyxl1" />
        <result column="ZRKCXB2" jdbcType="VARCHAR" property="zrkcxb2" />
        <result column="ZYDYXL2" jdbcType="DECIMAL" property="zydyxl2" />
        <result column="BRTWR" jdbcType="DECIMAL" property="brtwr" />
        <result column="KPEIN2" jdbcType="DECIMAL" property="kpein2" />
        <result column="ZYPURREQNO" jdbcType="VARCHAR" property="zypurreqno" />
        <result column="ZYSTORELINENO" jdbcType="VARCHAR" property="zystorelineno" />
        <result column="ZSFCF" jdbcType="VARCHAR" property="zsfcf" />
        <result column="ZMAIN" jdbcType="VARCHAR" property="zmain" />
    </resultMap>


    <resultMap id="ZMMT0355ResultMap" type="com.cowell.iscm.entity.SapZmmt0355">
        <result column="MANDT" jdbcType="VARCHAR" property="mandt" />
        <result column="ZGUID" jdbcType="VARCHAR" property="zguid" />
        <result column="ZCGJHD" jdbcType="VARCHAR" property="zcgjhd" />
        <result column="ZCGJHDHH" jdbcType="VARCHAR" property="zcgjhdhh" />
        <result column="BSART" jdbcType="VARCHAR" property="bsart" />
        <result column="LOEKZ" jdbcType="VARCHAR" property="loekz" />
        <result column="ZPOFLAG" jdbcType="VARCHAR" property="zpoflag" />
        <result column="ZOAFLAG" jdbcType="VARCHAR" property="zoaflag" />
        <result column="EBELN" jdbcType="VARCHAR" property="ebeln" />
        <result column="EBELP" jdbcType="VARCHAR" property="ebelp" />
        <result column="EKORG" jdbcType="VARCHAR" property="ekorg" />
        <result column="BUKRS" jdbcType="VARCHAR" property="bukrs" />
        <result column="EKGRP" jdbcType="VARCHAR" property="ekgrp" />
        <result column="LIFNR" jdbcType="VARCHAR" property="lifnr" />
        <result column="ZYWCJ" jdbcType="VARCHAR" property="zywcj" />
        <result column="ZZZWLMS" jdbcType="VARCHAR" property="zzzwlms" />
        <result column="ERNAM" jdbcType="VARCHAR" property="ernam" />
        <result column="ERDAT" jdbcType="VARCHAR" property="erdat" />
        <result column="ERZET" jdbcType="VARCHAR" property="erzet" />
        <result column="WERKS" jdbcType="VARCHAR" property="werks" />
        <result column="MATNR" jdbcType="VARCHAR" property="matnr" />
        <result column="MENGE" jdbcType="DECIMAL" property="menge" />
        <result column="ZMENGE" jdbcType="DECIMAL" property="zmenge" />
        <result column="EINDT" jdbcType="VARCHAR" property="eindt" />
        <result column="ZEINDT" jdbcType="VARCHAR" property="zeindt" />
        <result column="MEINS" jdbcType="VARCHAR" property="meins" />
        <result column="ZHSDJ" jdbcType="DECIMAL" property="zhsdj" />
        <result column="ZQRHSDJ" jdbcType="DECIMAL" property="zqrhsdj" />
        <result column="PEINH" jdbcType="INTEGER" property="peinh" />
        <result column="ZPEINH" jdbcType="INTEGER" property="zpeinh" />
        <result column="MWSKZ" jdbcType="VARCHAR" property="mwskz" />
        <result column="ZMWSKZ" jdbcType="VARCHAR" property="zmwskz" />
        <result column="ZCFLAG" jdbcType="VARCHAR" property="zcflag" />
        <result column="ZTERM" jdbcType="VARCHAR" property="zterm" />
        <result column="ZZZUSERID" jdbcType="VARCHAR" property="zzzuserid" />
        <result column="ZSHCK" jdbcType="VARCHAR" property="zshck" />
        <result column="ZSPJB" jdbcType="INTEGER" property="zspjb" />
        <result column="KONNR" jdbcType="VARCHAR" property="konnr" />
        <result column="KTPNR" jdbcType="VARCHAR" property="ktpnr" />
        <result column="ZTZHTBH" jdbcType="VARCHAR" property="ztzhtbh" />
        <result column="ZTZHTHH" jdbcType="VARCHAR" property="ztzhthh" />
        <result column="ZPLCODE" jdbcType="VARCHAR" property="zplcode" />
        <result column="ZPLSUG" jdbcType="VARCHAR" property="zplsug" />
        <result column="ZCJBM" jdbcType="VARCHAR" property="zcjbm" />
        <result column="MENGE_ZZ" jdbcType="DECIMAL" property="mengeZz" />
        <result column="ZSTOCK_USE" jdbcType="DECIMAL" property="zstockUse" />
        <result column="ZSJFD" jdbcType="DECIMAL" property="zsjfd" />
        <result column="REMARK" jdbcType="VARCHAR" property="remark" />
        <result column="USERNAME" jdbcType="VARCHAR" property="username" />
        <result column="UDATE" jdbcType="VARCHAR" property="udate" />
        <result column="UTIME" jdbcType="VARCHAR" property="utime" />
        <result column="ZMDZ" jdbcType="VARCHAR" property="zmdz" />
        <result column="ZSRMFLAG" jdbcType="VARCHAR" property="zsrmflag" />
        <result column="BRTWR_LAST" jdbcType="DECIMAL" property="brtwrLast" />
        <result column="PEINH_LAST" jdbcType="INTEGER" property="peinhLast" />
        <result column="LABST_DC_JS" jdbcType="DECIMAL" property="labstDcJs" />
        <result column="MENGE_WQ_JS" jdbcType="DECIMAL" property="mengeWqJs" />
        <result column="ZZXL_30_JS" jdbcType="DECIMAL" property="zzxl30Js" />
        <result column="ZMDQTY_JS" jdbcType="DECIMAL" property="zmdqtyJs" />
        <result column="ZZZMDSQ" jdbcType="VARCHAR" property="zzzmdsq" />
        <result column="ZZZMDHH" jdbcType="VARCHAR" property="zzzmdhh" />
        <result column="ZJHDAT" jdbcType="VARCHAR" property="zjhdat" />
        <result column="ZJHTIME" jdbcType="VARCHAR" property="zjhtime" />
        <result column="ZSRMDAT" jdbcType="VARCHAR" property="zsrmdat" />
        <result column="ZSRMTIME" jdbcType="VARCHAR" property="zsrmtime" />
        <result column="ZJHZDAT" jdbcType="VARCHAR" property="zjhzdat" />
        <result column="ZJHZTIME" jdbcType="VARCHAR" property="zjhztime" />
        <result column="FRGGR" jdbcType="VARCHAR" property="frggr" />
        <result column="FRGSX" jdbcType="VARCHAR" property="frgsx" />
        <result column="FRGKX" jdbcType="VARCHAR" property="frgkx" />
        <result column="FRGCO" jdbcType="VARCHAR" property="frgco" />
        <result column="SUBMI" jdbcType="VARCHAR" property="submi" />
        <result column="ZRESULT" jdbcType="VARCHAR" property="zresult" />
        <result column="ZLEVEL" jdbcType="VARCHAR" property="zlevel" />
        <result column="ZSPBS" jdbcType="VARCHAR" property="zspbs" />
        <result column="ZMESS" jdbcType="VARCHAR" property="zmess" />
        <result column="LGORT" jdbcType="VARCHAR" property="lgort" />
        <result column="ZDCKYKC" jdbcType="DECIMAL" property="zdckykc" />
        <result column="ZCYJSWQ" jdbcType="DECIMAL" property="zcyjswq" />
        <result column="ZZDGDSC" jdbcType="DECIMAL" property="zzdgdsc" />
    </resultMap>

    <resultMap id="EKKOResultMap" type="com.cowell.iscm.entity.SapEkko">
        <result column="MANDT" jdbcType="VARCHAR" property="mandt" />
        <result column="EBELN" jdbcType="VARCHAR" property="ebeln" />
        <result column="BUKRS" jdbcType="VARCHAR" property="bukrs" />
        <result column="BSTYP" jdbcType="VARCHAR" property="bstyp" />
        <result column="BSART" jdbcType="VARCHAR" property="bsart" />
        <result column="BSAKZ" jdbcType="VARCHAR" property="bsakz" />
        <result column="LOEKZ" jdbcType="VARCHAR" property="loekz" />
        <result column="STATU" jdbcType="VARCHAR" property="statu" />
        <result column="AEDAT" jdbcType="VARCHAR" property="aedat" />
        <result column="ERNAM" jdbcType="VARCHAR" property="ernam" />
        <result column="LASTCHANGEDATETIME" jdbcType="DECIMAL" property="lastchangedatetime" />
        <result column="PINCR" jdbcType="VARCHAR" property="pincr" />
        <result column="LPONR" jdbcType="VARCHAR" property="lponr" />
        <result column="LIFNR" jdbcType="VARCHAR" property="lifnr" />
        <result column="SPRAS" jdbcType="VARCHAR" property="spras" />
        <result column="ZTERM" jdbcType="VARCHAR" property="zterm" />
        <result column="ZBD1T" jdbcType="DECIMAL" property="zbd1t" />
        <result column="ZBD2T" jdbcType="DECIMAL" property="zbd2t" />
        <result column="ZBD3T" jdbcType="DECIMAL" property="zbd3t" />
        <result column="ZBD1P" jdbcType="DECIMAL" property="zbd1p" />
        <result column="ZBD2P" jdbcType="DECIMAL" property="zbd2p" />
        <result column="EKORG" jdbcType="VARCHAR" property="ekorg" />
        <result column="EKGRP" jdbcType="VARCHAR" property="ekgrp" />
        <result column="WAERS" jdbcType="VARCHAR" property="waers" />
        <result column="WKURS" jdbcType="DECIMAL" property="wkurs" />
        <result column="KUFIX" jdbcType="VARCHAR" property="kufix" />
        <result column="BEDAT" jdbcType="VARCHAR" property="bedat" />
        <result column="KDATB" jdbcType="VARCHAR" property="kdatb" />
        <result column="KDATE" jdbcType="VARCHAR" property="kdate" />
        <result column="BWBDT" jdbcType="VARCHAR" property="bwbdt" />
        <result column="ANGDT" jdbcType="VARCHAR" property="angdt" />
        <result column="BNDDT" jdbcType="VARCHAR" property="bnddt" />
        <result column="GWLDT" jdbcType="VARCHAR" property="gwldt" />
        <result column="AUSNR" jdbcType="VARCHAR" property="ausnr" />
        <result column="ANGNR" jdbcType="VARCHAR" property="angnr" />
        <result column="IHRAN" jdbcType="VARCHAR" property="ihran" />
        <result column="IHREZ" jdbcType="VARCHAR" property="ihrez" />
        <result column="VERKF" jdbcType="VARCHAR" property="verkf" />
        <result column="TELF1" jdbcType="VARCHAR" property="telf1" />
        <result column="LLIEF" jdbcType="VARCHAR" property="llief" />
        <result column="KUNNR" jdbcType="VARCHAR" property="kunnr" />
        <result column="KONNR" jdbcType="VARCHAR" property="konnr" />
        <result column="ABGRU" jdbcType="VARCHAR" property="abgru" />
        <result column="AUTLF" jdbcType="VARCHAR" property="autlf" />
        <result column="WEAKT" jdbcType="VARCHAR" property="weakt" />
        <result column="RESWK" jdbcType="VARCHAR" property="reswk" />
        <result column="LBLIF" jdbcType="VARCHAR" property="lblif" />
        <result column="INCO1" jdbcType="VARCHAR" property="inco1" />
        <result column="INCO2" jdbcType="VARCHAR" property="inco2" />
        <result column="KTWRT" jdbcType="DECIMAL" property="ktwrt" />
        <result column="SUBMI" jdbcType="VARCHAR" property="submi" />
        <result column="KNUMV" jdbcType="VARCHAR" property="knumv" />
        <result column="KALSM" jdbcType="VARCHAR" property="kalsm" />
        <result column="STAFO" jdbcType="VARCHAR" property="stafo" />
        <result column="LIFRE" jdbcType="VARCHAR" property="lifre" />
        <result column="EXNUM" jdbcType="VARCHAR" property="exnum" />
        <result column="UNSEZ" jdbcType="VARCHAR" property="unsez" />
        <result column="LOGSY" jdbcType="VARCHAR" property="logsy" />
        <result column="UPINC" jdbcType="VARCHAR" property="upinc" />
        <result column="STAKO" jdbcType="VARCHAR" property="stako" />
        <result column="FRGGR" jdbcType="VARCHAR" property="frggr" />
        <result column="FRGSX" jdbcType="VARCHAR" property="frgsx" />
        <result column="FRGKE" jdbcType="VARCHAR" property="frgke" />
        <result column="FRGZU" jdbcType="VARCHAR" property="frgzu" />
        <result column="FRGRL" jdbcType="VARCHAR" property="frgrl" />
        <result column="LANDS" jdbcType="VARCHAR" property="lands" />
        <result column="LPHIS" jdbcType="VARCHAR" property="lphis" />
        <result column="ADRNR" jdbcType="VARCHAR" property="adrnr" />
        <result column="STCEG_L" jdbcType="VARCHAR" property="stcegL" />
        <result column="STCEG" jdbcType="VARCHAR" property="stceg" />
        <result column="ABSGR" jdbcType="VARCHAR" property="absgr" />
        <result column="ADDNR" jdbcType="VARCHAR" property="addnr" />
        <result column="KORNR" jdbcType="VARCHAR" property="kornr" />
        <result column="MEMORY" jdbcType="VARCHAR" property="memory" />
        <result column="PROCSTAT" jdbcType="VARCHAR" property="procstat" />
        <result column="RLWRT" jdbcType="DECIMAL" property="rlwrt" />
        <result column="REVNO" jdbcType="VARCHAR" property="revno" />
        <result column="SCMPROC" jdbcType="VARCHAR" property="scmproc" />
        <result column="REASON_CODE" jdbcType="VARCHAR" property="reasonCode" />
        <result column="MEMORYTYPE" jdbcType="VARCHAR" property="memorytype" />
        <result column="RETTP" jdbcType="VARCHAR" property="rettp" />
        <result column="RETPC" jdbcType="DECIMAL" property="retpc" />
        <result column="DPTYP" jdbcType="VARCHAR" property="dptyp" />
        <result column="DPPCT" jdbcType="DECIMAL" property="dppct" />
        <result column="DPAMT" jdbcType="DECIMAL" property="dpamt" />
        <result column="DPDAT" jdbcType="VARCHAR" property="dpdat" />
        <result column="MSR_ID" jdbcType="VARCHAR" property="msrId" />
        <result column="HIERARCHY_EXISTS" jdbcType="VARCHAR" property="hierarchyExists" />
        <result column="THRESHOLD_EXISTS" jdbcType="VARCHAR" property="thresholdExists" />
        <result column="LEGAL_CONTRACT" jdbcType="VARCHAR" property="legalContract" />
        <result column="DESCRIPTION" jdbcType="VARCHAR" property="description" />
        <result column="RELEASE_DATE" jdbcType="VARCHAR" property="releaseDate" />
        <result column="VSART" jdbcType="VARCHAR" property="vsart" />
        <result column="HANDOVERLOC" jdbcType="VARCHAR" property="handoverloc" />
        <result column="SHIPCOND" jdbcType="VARCHAR" property="shipcond" />
        <result column="INCOV" jdbcType="VARCHAR" property="incov" />
        <result column="INCO2_L" jdbcType="VARCHAR" property="inco2L" />
        <result column="INCO3_L" jdbcType="VARCHAR" property="inco3L" />
        <result column="GRWCU" jdbcType="VARCHAR" property="grwcu" />
        <result column="INTRA_REL" jdbcType="VARCHAR" property="intraRel" />
        <result column="INTRA_EXCL" jdbcType="VARCHAR" property="intraExcl" />
        <result column="QTN_ERLST_SUBMSN_DATE" jdbcType="VARCHAR" property="qtnErlstSubmsnDate" />
        <result column="FOLLOWON_DOC_CAT" jdbcType="VARCHAR" property="followonDocCat" />
        <result column="FOLLOWON_DOC_TYPE" jdbcType="VARCHAR" property="followonDocType" />
        <result column="DUMMY_EKKO_INCL_EEW_PS" jdbcType="VARCHAR" property="dummyEkkoInclEewPs" />
        <result column="EXTERNALSYSTEM" jdbcType="VARCHAR" property="externalsystem" />
        <result column="EXTERNALREFERENCEID" jdbcType="VARCHAR" property="externalreferenceid" />
        <result column="EXT_REV_TMSTMP" jdbcType="DECIMAL" property="extRevTmstmp" />
        <result column="ISEOPBLOCKED" jdbcType="VARCHAR" property="iseopblocked" />
        <result column="ISAGED" jdbcType="VARCHAR" property="isaged" />
        <result column="FORCE_ID" jdbcType="VARCHAR" property="forceId" />
        <result column="FORCE_CNT" jdbcType="VARCHAR" property="forceCnt" />
        <result column="RELOC_ID" jdbcType="VARCHAR" property="relocId" />
        <result column="RELOC_SEQ_ID" jdbcType="VARCHAR" property="relocSeqId" />
        <result column="SOURCE_LOGSYS" jdbcType="VARCHAR" property="sourceLogsys" />
        <result column="FSH_TRANSACTION" jdbcType="VARCHAR" property="fshTransaction" />
        <result column="FSH_ITEM_GROUP" jdbcType="VARCHAR" property="fshItemGroup" />
        <result column="FSH_VAS_LAST_ITEM" jdbcType="VARCHAR" property="fshVasLastItem" />
        <result column="FSH_OS_STG_CHANGE" jdbcType="VARCHAR" property="fshOsStgChange" />
        <result column="TMS_REF_UUID" jdbcType="VARCHAR" property="tmsRefUuid" />
        <result column="ZZZWLMS" jdbcType="VARCHAR" property="zzzwlms" />
        <result column="ZZZZSHD" jdbcType="VARCHAR" property="zzzzshd" />
        <result column="ZZZSHKC" jdbcType="VARCHAR" property="zzzshkc" />
        <result column="ZZZMDSQ" jdbcType="VARCHAR" property="zzzmdsq" />
        <result column="ZZZBEIZ" jdbcType="VARCHAR" property="zzzbeiz" />
        <result column="ZZZHGZT" jdbcType="VARCHAR" property="zzzhgzt" />
        <result column="ZZQXDJH" jdbcType="VARCHAR" property="zzqxdjh" />
        <result column="ZZZQHBS" jdbcType="VARCHAR" property="zzzqhbs" />
        <result column="ZZZUSERID" jdbcType="VARCHAR" property="zzzuserid" />
        <result column="ZZCGY" jdbcType="VARCHAR" property="zzcgy" />
        <result column="ZZDHY" jdbcType="VARCHAR" property="zzdhy" />
        <result column="ZZCOER" jdbcType="VARCHAR" property="zzcoer" />
        <result column="ZZCOCA" jdbcType="VARCHAR" property="zzcoca" />
        <result column="ZZYCGDD" jdbcType="VARCHAR" property="zzycgdd" />
        <result column="ZAPCGK" jdbcType="VARCHAR" property="zapcgk" />
        <result column="APCGK_EXTEND" jdbcType="VARCHAR" property="apcgkExtend" />
        <result column="ZBAS_DATE" jdbcType="VARCHAR" property="zbasDate" />
        <result column="ZADATTYP" jdbcType="VARCHAR" property="zadattyp" />
        <result column="ZSTART_DAT" jdbcType="VARCHAR" property="zstartDat" />
        <result column="Z_DEV" jdbcType="DECIMAL" property="zDev" />
        <result column="ZINDANX" jdbcType="VARCHAR" property="zindanx" />
        <result column="ZLIMIT_DAT" jdbcType="VARCHAR" property="zlimitDat" />
        <result column="NUMERATOR" jdbcType="VARCHAR" property="numerator" />
        <result column="HASHCAL_BDAT" jdbcType="VARCHAR" property="hashcalBdat" />
        <result column="HASHCAL" jdbcType="VARCHAR" property="hashcal" />
        <result column="NEGATIVE" jdbcType="VARCHAR" property="negative" />
        <result column="HASHCAL_EXISTS" jdbcType="VARCHAR" property="hashcalExists" />
        <result column="KNOWN_INDEX" jdbcType="VARCHAR" property="knownIndex" />
        <result column="POSTAT" jdbcType="VARCHAR" property="postat" />
        <result column="VZSKZ" jdbcType="VARCHAR" property="vzskz" />
        <result column="FSH_SNST_STATUS" jdbcType="VARCHAR" property="fshSnstStatus" />
        <result column="PROCE" jdbcType="VARCHAR" property="proce" />
        <result column="CONC" jdbcType="VARCHAR" property="conc" />
        <result column="CONT" jdbcType="VARCHAR" property="cont" />
        <result column="COMP" jdbcType="VARCHAR" property="comp" />
        <result column="OUTR" jdbcType="VARCHAR" property="outr" />
        <result column="DESP" jdbcType="VARCHAR" property="desp" />
        <result column="DESP_DAT" jdbcType="VARCHAR" property="despDat" />
        <result column="DESP_CARGO" jdbcType="VARCHAR" property="despCargo" />
        <result column="PARE" jdbcType="VARCHAR" property="pare" />
        <result column="PARE_DAT" jdbcType="VARCHAR" property="pareDat" />
        <result column="PARE_CARGO" jdbcType="VARCHAR" property="pareCargo" />
        <result column="PFM_CONTRACT" jdbcType="VARCHAR" property="pfmContract" />
        <result column="POHF_TYPE" jdbcType="VARCHAR" property="pohfType" />
        <result column="EQ_EINDT" jdbcType="VARCHAR" property="eqEindt" />
        <result column="EQ_WERKS" jdbcType="VARCHAR" property="eqWerks" />
        <result column="FIXPO" jdbcType="VARCHAR" property="fixpo" />
        <result column="EKGRP_ALLOW" jdbcType="VARCHAR" property="ekgrpAllow" />
        <result column="WERKS_ALLOW" jdbcType="VARCHAR" property="werksAllow" />
        <result column="CONTRACT_ALLOW" jdbcType="VARCHAR" property="contractAllow" />
        <result column="PSTYP_ALLOW" jdbcType="VARCHAR" property="pstypAllow" />
        <result column="FIXPO_ALLOW" jdbcType="VARCHAR" property="fixpoAllow" />
        <result column="KEY_ID_ALLOW" jdbcType="VARCHAR" property="keyIdAllow" />
        <result column="AUREL_ALLOW" jdbcType="VARCHAR" property="aurelAllow" />
        <result column="DELPER_ALLOW" jdbcType="VARCHAR" property="delperAllow" />
        <result column="EINDT_ALLOW" jdbcType="VARCHAR" property="eindtAllow" />
        <result column="LTSNR_ALLOW" jdbcType="VARCHAR" property="ltsnrAllow" />
        <result column="OTB_LEVEL" jdbcType="VARCHAR" property="otbLevel" />
        <result column="OTB_COND_TYPE" jdbcType="VARCHAR" property="otbCondType" />
        <result column="KEY_ID" jdbcType="VARCHAR" property="keyId" />
        <result column="OTB_VALUE" jdbcType="DECIMAL" property="otbValue" />
        <result column="OTB_CURR" jdbcType="VARCHAR" property="otbCurr" />
        <result column="OTB_RES_VALUE" jdbcType="DECIMAL" property="otbResValue" />
        <result column="OTB_SPEC_VALUE" jdbcType="DECIMAL" property="otbSpecValue" />
        <result column="SPR_RSN_PROFILE" jdbcType="VARCHAR" property="sprRsnProfile" />
        <result column="BUDG_TYPE" jdbcType="VARCHAR" property="budgType" />
        <result column="OTB_STATUS" jdbcType="VARCHAR" property="otbStatus" />
        <result column="OTB_REASON" jdbcType="VARCHAR" property="otbReason" />
        <result column="CHECK_TYPE" jdbcType="VARCHAR" property="checkType" />
        <result column="CON_OTB_REQ" jdbcType="VARCHAR" property="conOtbReq" />
        <result column="CON_PREBOOK_LEV" jdbcType="VARCHAR" property="conPrebookLev" />
        <result column="CON_DISTR_LEV" jdbcType="VARCHAR" property="conDistrLev" />
        <result column="ZZYFYE" jdbcType="VARCHAR" property="zzyfye" />
        <result column="ZSQHZH" jdbcType="VARCHAR" property="zsqhzh" />
        <result column="ZZSRMZT" jdbcType="VARCHAR" property="zzsrmzt" />
        <result column="ZZPAID" jdbcType="VARCHAR" property="zzpaid" />
        <result column="ZZPAMT" jdbcType="DECIMAL" property="zzpamt" />
    </resultMap>

    <resultMap id="EKPOResultMap" type="com.cowell.iscm.entity.SapEkpo">
        <result column="MANDT" jdbcType="VARCHAR" property="mandt" />
        <result column="EBELN" jdbcType="VARCHAR" property="ebeln" />
        <result column="EBELP" jdbcType="VARCHAR" property="ebelp" />
        <result column="LOEKZ" jdbcType="VARCHAR" property="loekz" />
        <result column="STATU" jdbcType="VARCHAR" property="statu" />
        <result column="AEDAT" jdbcType="VARCHAR" property="aedat" />
        <result column="TXZ01" jdbcType="VARCHAR" property="txz01" />
        <result column="MATNR" jdbcType="VARCHAR" property="matnr" />
        <result column="EMATN" jdbcType="VARCHAR" property="ematn" />
        <result column="BUKRS" jdbcType="VARCHAR" property="bukrs" />
        <result column="WERKS" jdbcType="VARCHAR" property="werks" />
        <result column="LGORT" jdbcType="VARCHAR" property="lgort" />
        <result column="BEDNR" jdbcType="VARCHAR" property="bednr" />
        <result column="MATKL" jdbcType="VARCHAR" property="matkl" />
        <result column="INFNR" jdbcType="VARCHAR" property="infnr" />
        <result column="IDNLF" jdbcType="VARCHAR" property="idnlf" />
        <result column="KTMNG" jdbcType="DECIMAL" property="ktmng" />
        <result column="MENGE" jdbcType="DECIMAL" property="menge" />
        <result column="MEINS" jdbcType="VARCHAR" property="meins" />
        <result column="BPRME" jdbcType="VARCHAR" property="bprme" />
        <result column="BPUMZ" jdbcType="DECIMAL" property="bpumz" />
        <result column="BPUMN" jdbcType="DECIMAL" property="bpumn" />
        <result column="UMREZ" jdbcType="DECIMAL" property="umrez" />
        <result column="UMREN" jdbcType="DECIMAL" property="umren" />
        <result column="NETPR" jdbcType="DECIMAL" property="netpr" />
        <result column="PEINH" jdbcType="DECIMAL" property="peinh" />
        <result column="NETWR" jdbcType="DECIMAL" property="netwr" />
        <result column="BRTWR" jdbcType="DECIMAL" property="brtwr" />
        <result column="AGDAT" jdbcType="VARCHAR" property="agdat" />
        <result column="WEBAZ" jdbcType="DECIMAL" property="webaz" />
        <result column="MWSKZ" jdbcType="VARCHAR" property="mwskz" />
        <result column="BONUS" jdbcType="VARCHAR" property="bonus" />
        <result column="INSMK" jdbcType="VARCHAR" property="insmk" />
        <result column="SPINF" jdbcType="VARCHAR" property="spinf" />
        <result column="PRSDR" jdbcType="VARCHAR" property="prsdr" />
        <result column="SCHPR" jdbcType="VARCHAR" property="schpr" />
        <result column="MAHNZ" jdbcType="DECIMAL" property="mahnz" />
        <result column="MAHN1" jdbcType="DECIMAL" property="mahn1" />
        <result column="MAHN2" jdbcType="DECIMAL" property="mahn2" />
        <result column="MAHN3" jdbcType="DECIMAL" property="mahn3" />
        <result column="UEBTO" jdbcType="DECIMAL" property="uebto" />
        <result column="UEBTK" jdbcType="VARCHAR" property="uebtk" />
        <result column="UNTTO" jdbcType="DECIMAL" property="untto" />
        <result column="BWTAR" jdbcType="VARCHAR" property="bwtar" />
        <result column="BWTTY" jdbcType="VARCHAR" property="bwtty" />
        <result column="ABSKZ" jdbcType="VARCHAR" property="abskz" />
        <result column="AGMEM" jdbcType="VARCHAR" property="agmem" />
        <result column="ELIKZ" jdbcType="VARCHAR" property="elikz" />
        <result column="EREKZ" jdbcType="VARCHAR" property="erekz" />
        <result column="PSTYP" jdbcType="VARCHAR" property="pstyp" />
        <result column="KNTTP" jdbcType="VARCHAR" property="knttp" />
        <result column="KZVBR" jdbcType="VARCHAR" property="kzvbr" />
        <result column="VRTKZ" jdbcType="VARCHAR" property="vrtkz" />
        <result column="TWRKZ" jdbcType="VARCHAR" property="twrkz" />
        <result column="WEPOS" jdbcType="VARCHAR" property="wepos" />
        <result column="WEUNB" jdbcType="VARCHAR" property="weunb" />
        <result column="REPOS" jdbcType="VARCHAR" property="repos" />
        <result column="WEBRE" jdbcType="VARCHAR" property="webre" />
        <result column="KZABS" jdbcType="VARCHAR" property="kzabs" />
        <result column="LABNR" jdbcType="VARCHAR" property="labnr" />
        <result column="KONNR" jdbcType="VARCHAR" property="konnr" />
        <result column="KTPNR" jdbcType="VARCHAR" property="ktpnr" />
        <result column="ABDAT" jdbcType="VARCHAR" property="abdat" />
        <result column="ABFTZ" jdbcType="DECIMAL" property="abftz" />
        <result column="ETFZ1" jdbcType="DECIMAL" property="etfz1" />
        <result column="ETFZ2" jdbcType="DECIMAL" property="etfz2" />
        <result column="KZSTU" jdbcType="VARCHAR" property="kzstu" />
        <result column="NOTKZ" jdbcType="VARCHAR" property="notkz" />
        <result column="LMEIN" jdbcType="VARCHAR" property="lmein" />
        <result column="EVERS" jdbcType="VARCHAR" property="evers" />
        <result column="ZWERT" jdbcType="DECIMAL" property="zwert" />
        <result column="NAVNW" jdbcType="DECIMAL" property="navnw" />
        <result column="ABMNG" jdbcType="DECIMAL" property="abmng" />
        <result column="PRDAT" jdbcType="VARCHAR" property="prdat" />
        <result column="BSTYP" jdbcType="VARCHAR" property="bstyp" />
        <result column="EFFWR" jdbcType="DECIMAL" property="effwr" />
        <result column="XOBLR" jdbcType="VARCHAR" property="xoblr" />
        <result column="KUNNR" jdbcType="VARCHAR" property="kunnr" />
        <result column="ADRNR" jdbcType="VARCHAR" property="adrnr" />
        <result column="EKKOL" jdbcType="VARCHAR" property="ekkol" />
        <result column="SKTOF" jdbcType="VARCHAR" property="sktof" />
        <result column="STAFO" jdbcType="VARCHAR" property="stafo" />
        <result column="PLIFZ" jdbcType="DECIMAL" property="plifz" />
        <result column="NTGEW" jdbcType="DECIMAL" property="ntgew" />
        <result column="GEWEI" jdbcType="VARCHAR" property="gewei" />
        <result column="TXJCD" jdbcType="VARCHAR" property="txjcd" />
        <result column="ETDRK" jdbcType="VARCHAR" property="etdrk" />
        <result column="SOBKZ" jdbcType="VARCHAR" property="sobkz" />
        <result column="ARSNR" jdbcType="VARCHAR" property="arsnr" />
        <result column="ARSPS" jdbcType="VARCHAR" property="arsps" />
        <result column="INSNC" jdbcType="VARCHAR" property="insnc" />
        <result column="SSQSS" jdbcType="VARCHAR" property="ssqss" />
        <result column="ZGTYP" jdbcType="VARCHAR" property="zgtyp" />
        <result column="EAN11" jdbcType="VARCHAR" property="ean11" />
        <result column="BSTAE" jdbcType="VARCHAR" property="bstae" />
        <result column="REVLV" jdbcType="VARCHAR" property="revlv" />
        <result column="GEBER" jdbcType="VARCHAR" property="geber" />
        <result column="FISTL" jdbcType="VARCHAR" property="fistl" />
        <result column="FIPOS" jdbcType="VARCHAR" property="fipos" />
        <result column="KO_GSBER" jdbcType="VARCHAR" property="koGsber" />
        <result column="KO_PARGB" jdbcType="VARCHAR" property="koPargb" />
        <result column="KO_PRCTR" jdbcType="VARCHAR" property="koPrctr" />
        <result column="KO_PPRCTR" jdbcType="VARCHAR" property="koPprctr" />
        <result column="MEPRF" jdbcType="VARCHAR" property="meprf" />
        <result column="BRGEW" jdbcType="DECIMAL" property="brgew" />
        <result column="VOLUM" jdbcType="DECIMAL" property="volum" />
        <result column="VOLEH" jdbcType="VARCHAR" property="voleh" />
        <result column="INCO1" jdbcType="VARCHAR" property="inco1" />
        <result column="INCO2" jdbcType="VARCHAR" property="inco2" />
        <result column="VORAB" jdbcType="VARCHAR" property="vorab" />
        <result column="KOLIF" jdbcType="VARCHAR" property="kolif" />
        <result column="LTSNR" jdbcType="VARCHAR" property="ltsnr" />
        <result column="PACKNO" jdbcType="VARCHAR" property="packno" />
        <result column="FPLNR" jdbcType="VARCHAR" property="fplnr" />
        <result column="GNETWR" jdbcType="DECIMAL" property="gnetwr" />
        <result column="STAPO" jdbcType="VARCHAR" property="stapo" />
        <result column="UEBPO" jdbcType="VARCHAR" property="uebpo" />
        <result column="LEWED" jdbcType="VARCHAR" property="lewed" />
        <result column="EMLIF" jdbcType="VARCHAR" property="emlif" />
        <result column="LBLKZ" jdbcType="VARCHAR" property="lblkz" />
        <result column="SATNR" jdbcType="VARCHAR" property="satnr" />
        <result column="ATTYP" jdbcType="VARCHAR" property="attyp" />
        <result column="VSART" jdbcType="VARCHAR" property="vsart" />
        <result column="HANDOVERLOC" jdbcType="VARCHAR" property="handoverloc" />
        <result column="KANBA" jdbcType="VARCHAR" property="kanba" />
        <result column="ADRN2" jdbcType="VARCHAR" property="adrn2" />
        <result column="CUOBJ" jdbcType="VARCHAR" property="cuobj" />
        <result column="XERSY" jdbcType="VARCHAR" property="xersy" />
        <result column="EILDT" jdbcType="VARCHAR" property="eildt" />
        <result column="DRDAT" jdbcType="VARCHAR" property="drdat" />
        <result column="DRUHR" jdbcType="VARCHAR" property="druhr" />
        <result column="DRUNR" jdbcType="VARCHAR" property="drunr" />
        <result column="AKTNR" jdbcType="VARCHAR" property="aktnr" />
        <result column="ABELN" jdbcType="VARCHAR" property="abeln" />
        <result column="ABELP" jdbcType="VARCHAR" property="abelp" />
        <result column="ANZPU" jdbcType="DECIMAL" property="anzpu" />
        <result column="PUNEI" jdbcType="VARCHAR" property="punei" />
        <result column="SAISO" jdbcType="VARCHAR" property="saiso" />
        <result column="SAISJ" jdbcType="VARCHAR" property="saisj" />
        <result column="EBON2" jdbcType="VARCHAR" property="ebon2" />
        <result column="EBON3" jdbcType="VARCHAR" property="ebon3" />
        <result column="EBONF" jdbcType="VARCHAR" property="ebonf" />
        <result column="MLMAA" jdbcType="VARCHAR" property="mlmaa" />
        <result column="MHDRZ" jdbcType="DECIMAL" property="mhdrz" />
        <result column="ANFNR" jdbcType="VARCHAR" property="anfnr" />
        <result column="ANFPS" jdbcType="VARCHAR" property="anfps" />
        <result column="KZKFG" jdbcType="VARCHAR" property="kzkfg" />
        <result column="USEQU" jdbcType="VARCHAR" property="usequ" />
        <result column="UMSOK" jdbcType="VARCHAR" property="umsok" />
        <result column="BANFN" jdbcType="VARCHAR" property="banfn" />
        <result column="BNFPO" jdbcType="VARCHAR" property="bnfpo" />
        <result column="MTART" jdbcType="VARCHAR" property="mtart" />
        <result column="UPTYP" jdbcType="VARCHAR" property="uptyp" />
        <result column="UPVOR" jdbcType="VARCHAR" property="upvor" />
        <result column="KZWI1" jdbcType="DECIMAL" property="kzwi1" />
        <result column="KZWI2" jdbcType="DECIMAL" property="kzwi2" />
        <result column="KZWI3" jdbcType="DECIMAL" property="kzwi3" />
        <result column="KZWI4" jdbcType="DECIMAL" property="kzwi4" />
        <result column="KZWI5" jdbcType="DECIMAL" property="kzwi5" />
        <result column="KZWI6" jdbcType="DECIMAL" property="kzwi6" />
        <result column="SIKGR" jdbcType="VARCHAR" property="sikgr" />
        <result column="MFZHI" jdbcType="DECIMAL" property="mfzhi" />
        <result column="FFZHI" jdbcType="DECIMAL" property="ffzhi" />
        <result column="RETPO" jdbcType="VARCHAR" property="retpo" />
        <result column="AUREL" jdbcType="VARCHAR" property="aurel" />
        <result column="BSGRU" jdbcType="VARCHAR" property="bsgru" />
        <result column="LFRET" jdbcType="VARCHAR" property="lfret" />
        <result column="MFRGR" jdbcType="VARCHAR" property="mfrgr" />
        <result column="NRFHG" jdbcType="VARCHAR" property="nrfhg" />
        <result column="J_1BNBM" jdbcType="VARCHAR" property="j1bnbm" />
        <result column="J_1BMATUSE" jdbcType="VARCHAR" property="j1bmatuse" />
        <result column="J_1BMATORG" jdbcType="VARCHAR" property="j1bmatorg" />
        <result column="J_1BOWNPRO" jdbcType="VARCHAR" property="j1bownpro" />
        <result column="J_1BINDUST" jdbcType="VARCHAR" property="j1bindust" />
        <result column="ABUEB" jdbcType="VARCHAR" property="abueb" />
        <result column="NLABD" jdbcType="VARCHAR" property="nlabd" />
        <result column="NFABD" jdbcType="VARCHAR" property="nfabd" />
        <result column="KZBWS" jdbcType="VARCHAR" property="kzbws" />
        <result column="BONBA" jdbcType="DECIMAL" property="bonba" />
        <result column="FABKZ" jdbcType="VARCHAR" property="fabkz" />
        <result column="J_1AINDXP" jdbcType="VARCHAR" property="j1aindxp" />
        <result column="J_1AIDATEP" jdbcType="VARCHAR" property="j1aidatep" />
        <result column="MPROF" jdbcType="VARCHAR" property="mprof" />
        <result column="EGLKZ" jdbcType="VARCHAR" property="eglkz" />
        <result column="KZTLF" jdbcType="VARCHAR" property="kztlf" />
        <result column="KZFME" jdbcType="VARCHAR" property="kzfme" />
        <result column="RDPRF" jdbcType="VARCHAR" property="rdprf" />
        <result column="TECHS" jdbcType="VARCHAR" property="techs" />
        <result column="CHG_SRV" jdbcType="VARCHAR" property="chgSrv" />
        <result column="CHG_FPLNR" jdbcType="VARCHAR" property="chgFplnr" />
        <result column="MFRPN" jdbcType="VARCHAR" property="mfrpn" />
        <result column="MFRNR" jdbcType="VARCHAR" property="mfrnr" />
        <result column="EMNFR" jdbcType="VARCHAR" property="emnfr" />
        <result column="NOVET" jdbcType="VARCHAR" property="novet" />
        <result column="AFNAM" jdbcType="VARCHAR" property="afnam" />
        <result column="TZONRC" jdbcType="VARCHAR" property="tzonrc" />
        <result column="IPRKZ" jdbcType="VARCHAR" property="iprkz" />
        <result column="LEBRE" jdbcType="VARCHAR" property="lebre" />
        <result column="BERID" jdbcType="VARCHAR" property="berid" />
        <result column="XCONDITIONS" jdbcType="VARCHAR" property="xconditions" />
        <result column="APOMS" jdbcType="VARCHAR" property="apoms" />
        <result column="CCOMP" jdbcType="VARCHAR" property="ccomp" />
        <result column="GRANT_NBR" jdbcType="VARCHAR" property="grantNbr" />
        <result column="FKBER" jdbcType="VARCHAR" property="fkber" />
        <result column="STATUS" jdbcType="VARCHAR" property="status" />
        <result column="RESLO" jdbcType="VARCHAR" property="reslo" />
        <result column="KBLNR" jdbcType="VARCHAR" property="kblnr" />
        <result column="KBLPOS" jdbcType="VARCHAR" property="kblpos" />
        <result column="WEORA" jdbcType="VARCHAR" property="weora" />
        <result column="SRV_BAS_COM" jdbcType="VARCHAR" property="srvBasCom" />
        <result column="PRIO_URG" jdbcType="VARCHAR" property="prioUrg" />
        <result column="PRIO_REQ" jdbcType="VARCHAR" property="prioReq" />
        <result column="EMPST" jdbcType="VARCHAR" property="empst" />
        <result column="DIFF_INVOICE" jdbcType="VARCHAR" property="diffInvoice" />
        <result column="TRMRISK_RELEVANT" jdbcType="VARCHAR" property="trmriskRelevant" />
        <result column="SPE_ABGRU" jdbcType="VARCHAR" property="speAbgru" />
        <result column="SPE_CRM_SO" jdbcType="VARCHAR" property="speCrmSo" />
        <result column="SPE_CRM_SO_ITEM" jdbcType="VARCHAR" property="speCrmSoItem" />
        <result column="SPE_CRM_REF_SO" jdbcType="VARCHAR" property="speCrmRefSo" />
        <result column="SPE_CRM_REF_ITEM" jdbcType="VARCHAR" property="speCrmRefItem" />
        <result column="SPE_CRM_FKREL" jdbcType="VARCHAR" property="speCrmFkrel" />
        <result column="SPE_CHNG_SYS" jdbcType="VARCHAR" property="speChngSys" />
        <result column="SPE_INSMK_SRC" jdbcType="VARCHAR" property="speInsmkSrc" />
        <result column="SPE_CQ_CTRLTYPE" jdbcType="VARCHAR" property="speCqCtrltype" />
        <result column="SPE_CQ_NOCQ" jdbcType="VARCHAR" property="speCqNocq" />
        <result column="REASON_CODE" jdbcType="VARCHAR" property="reasonCode" />
        <result column="CQU_SAR" jdbcType="DECIMAL" property="cquSar" />
        <result column="ANZSN" jdbcType="INTEGER" property="anzsn" />
        <result column="SPE_EWM_DTC" jdbcType="VARCHAR" property="speEwmDtc" />
        <result column="EXLIN" jdbcType="VARCHAR" property="exlin" />
        <result column="EXSNR" jdbcType="VARCHAR" property="exsnr" />
        <result column="EHTYP" jdbcType="VARCHAR" property="ehtyp" />
        <result column="RETPC" jdbcType="DECIMAL" property="retpc" />
        <result column="DPTYP" jdbcType="VARCHAR" property="dptyp" />
        <result column="DPPCT" jdbcType="DECIMAL" property="dppct" />
        <result column="DPAMT" jdbcType="DECIMAL" property="dpamt" />
        <result column="DPDAT" jdbcType="VARCHAR" property="dpdat" />
        <result column="FLS_RSTO" jdbcType="VARCHAR" property="flsRsto" />
        <result column="EXT_RFX_NUMBER" jdbcType="VARCHAR" property="extRfxNumber" />
        <result column="EXT_RFX_ITEM" jdbcType="VARCHAR" property="extRfxItem" />
        <result column="EXT_RFX_SYSTEM" jdbcType="VARCHAR" property="extRfxSystem" />
        <result column="SRM_CONTRACT_ID" jdbcType="VARCHAR" property="srmContractId" />
        <result column="SRM_CONTRACT_ITM" jdbcType="VARCHAR" property="srmContractItm" />
        <result column="BLK_REASON_ID" jdbcType="VARCHAR" property="blkReasonId" />
        <result column="BLK_REASON_TXT" jdbcType="VARCHAR" property="blkReasonTxt" />
        <result column="ITCONS" jdbcType="VARCHAR" property="itcons" />
        <result column="FIXMG" jdbcType="VARCHAR" property="fixmg" />
        <result column="WABWE" jdbcType="VARCHAR" property="wabwe" />
        <result column="CMPL_DLV_ITM" jdbcType="VARCHAR" property="cmplDlvItm" />
        <result column="INCO2_L" jdbcType="VARCHAR" property="inco2L" />
        <result column="INCO3_L" jdbcType="VARCHAR" property="inco3L" />
        <result column="STAWN" jdbcType="VARCHAR" property="stawn" />
        <result column="ISVCO" jdbcType="VARCHAR" property="isvco" />
        <result column="GRWRT" jdbcType="DECIMAL" property="grwrt" />
        <result column="SERVICEPERFORMER" jdbcType="VARCHAR" property="serviceperformer" />
        <result column="PRODUCTTYPE" jdbcType="VARCHAR" property="producttype" />
        <result column="REQUESTFORQUOTATION" jdbcType="VARCHAR" property="requestforquotation" />
        <result column="REQUESTFORQUOTATIONITEM" jdbcType="VARCHAR" property="requestforquotationitem" />
        <result column="EXTERNALREFERENCEID" jdbcType="VARCHAR" property="externalreferenceid" />
        <result column="TC_AUT_DET" jdbcType="VARCHAR" property="tcAutDet" />
        <result column="MANUAL_TC_REASON" jdbcType="VARCHAR" property="manualTcReason" />
        <result column="FISCAL_INCENTIVE" jdbcType="VARCHAR" property="fiscalIncentive" />
        <result column="TAX_SUBJECT_ST" jdbcType="VARCHAR" property="taxSubjectSt" />
        <result column="FISCAL_INCENTIVE_ID" jdbcType="VARCHAR" property="fiscalIncentiveId" />
        <result column="SF_TXJCD" jdbcType="VARCHAR" property="sfTxjcd" />
        <result column="DUMMY_EKPO_INCL_EEW_PS" jdbcType="VARCHAR" property="dummyEkpoInclEewPs" />
        <result column="EXPECTED_VALUE" jdbcType="DECIMAL" property="expectedValue" />
        <result column="LIMIT_AMOUNT" jdbcType="DECIMAL" property="limitAmount" />
        <result column="ENH_DATE1" jdbcType="VARCHAR" property="enhDate1" />
        <result column="ENH_DATE2" jdbcType="VARCHAR" property="enhDate2" />
        <result column="ENH_PERCENT" jdbcType="DECIMAL" property="enhPercent" />
        <result column="ENH_NUMC1" jdbcType="VARCHAR" property="enhNumc1" />
        <result column="DATAAGING" jdbcType="VARCHAR" property="dataaging" />
        <result column="/BEV1/NEGEN_ITEM" jdbcType="VARCHAR" property="bev1NegenItem" />
        <result column="/BEV1/NEDEPFREE" jdbcType="VARCHAR" property="bev1Nedepfree" />
        <result column="/BEV1/NESTRUCCAT" jdbcType="VARCHAR" property="bev1Nestruccat" />
        <result column="ADVCODE" jdbcType="VARCHAR" property="advcode" />
        <result column="BUDGET_PD" jdbcType="VARCHAR" property="budgetPd" />
        <result column="EXCPE" jdbcType="VARCHAR" property="excpe" />
        <result column="FMFGUS_KEY" jdbcType="VARCHAR" property="fmfgusKey" />
        <result column="IUID_RELEVANT" jdbcType="VARCHAR" property="iuidRelevant" />
        <result column="MRPIND" jdbcType="VARCHAR" property="mrpind" />
        <result column="SGT_SCAT" jdbcType="VARCHAR" property="sgtScat" />
        <result column="SGT_RCAT" jdbcType="VARCHAR" property="sgtRcat" />
        <result column="TMS_REF_UUID" jdbcType="VARCHAR" property="tmsRefUuid" />
        <result column="WRF_CHARSTC1" jdbcType="VARCHAR" property="wrfCharstc1" />
        <result column="WRF_CHARSTC2" jdbcType="VARCHAR" property="wrfCharstc2" />
        <result column="WRF_CHARSTC3" jdbcType="VARCHAR" property="wrfCharstc3" />
        <result column="ZZZMDSQ" jdbcType="VARCHAR" property="zzzmdsq" />
        <result column="ZZZMDHH" jdbcType="VARCHAR" property="zzzmdhh" />
        <result column="ZZQXDJH" jdbcType="VARCHAR" property="zzqxdjh" />
        <result column="ZZQXDHH" jdbcType="VARCHAR" property="zzqxdhh" />
        <result column="ZZCXBJS" jdbcType="VARCHAR" property="zzcxbjs" />
        <result column="ZZQTYS" jdbcType="VARCHAR" property="zzqtys" />
        <result column="ZZSPCD" jdbcType="VARCHAR" property="zzspcd" />
        <result column="ZZSCCJ" jdbcType="VARCHAR" property="zzsccj" />
        <result column="ZZZSCPH" jdbcType="VARCHAR" property="zzzscph" />
        <result column="ZZZGJJ" jdbcType="VARCHAR" property="zzzgjj" />
        <result column="REFSITE" jdbcType="VARCHAR" property="refsite" />
        <result column="ZAPCGK" jdbcType="VARCHAR" property="zapcgk" />
        <result column="APCGK_EXTEND" jdbcType="VARCHAR" property="apcgkExtend" />
        <result column="ZBAS_DATE" jdbcType="VARCHAR" property="zbasDate" />
        <result column="ZADATTYP" jdbcType="VARCHAR" property="zadattyp" />
        <result column="ZSTART_DAT" jdbcType="VARCHAR" property="zstartDat" />
        <result column="Z_DEV" jdbcType="DECIMAL" property="zDev" />
        <result column="ZINDANX" jdbcType="VARCHAR" property="zindanx" />
        <result column="ZLIMIT_DAT" jdbcType="VARCHAR" property="zlimitDat" />
        <result column="NUMERATOR" jdbcType="VARCHAR" property="numerator" />
        <result column="HASHCAL_BDAT" jdbcType="VARCHAR" property="hashcalBdat" />
        <result column="HASHCAL" jdbcType="VARCHAR" property="hashcal" />
        <result column="NEGATIVE" jdbcType="VARCHAR" property="negative" />
        <result column="HASHCAL_EXISTS" jdbcType="VARCHAR" property="hashcalExists" />
        <result column="KNOWN_INDEX" jdbcType="VARCHAR" property="knownIndex" />
        <result column="/SAPMP/GPOSE" jdbcType="VARCHAR" property="sapmpGpose" />
        <result column="ANGPN" jdbcType="VARCHAR" property="angpn" />
        <result column="ADMOI" jdbcType="VARCHAR" property="admoi" />
        <result column="ADPRI" jdbcType="VARCHAR" property="adpri" />
        <result column="LPRIO" jdbcType="VARCHAR" property="lprio" />
        <result column="ADACN" jdbcType="VARCHAR" property="adacn" />
        <result column="AFPNR" jdbcType="VARCHAR" property="afpnr" />
        <result column="BSARK" jdbcType="VARCHAR" property="bsark" />
        <result column="AUDAT" jdbcType="VARCHAR" property="audat" />
        <result column="ANGNR" jdbcType="VARCHAR" property="angnr" />
        <result column="PNSTAT" jdbcType="VARCHAR" property="pnstat" />
        <result column="ADDNS" jdbcType="VARCHAR" property="addns" />
        <result column="SERRU" jdbcType="VARCHAR" property="serru" />
        <result column="SERNP" jdbcType="VARCHAR" property="sernp" />
        <result column="DISUB_SOBKZ" jdbcType="VARCHAR" property="disubSobkz" />
        <result column="DISUB_PSPNR" jdbcType="VARCHAR" property="disubPspnr" />
        <result column="DISUB_KUNNR" jdbcType="VARCHAR" property="disubKunnr" />
        <result column="DISUB_VBELN" jdbcType="VARCHAR" property="disubVbeln" />
        <result column="DISUB_POSNR" jdbcType="VARCHAR" property="disubPosnr" />
        <result column="DISUB_OWNER" jdbcType="VARCHAR" property="disubOwner" />
        <result column="FSH_SEASON_YEAR" jdbcType="VARCHAR" property="fshSeasonYear" />
        <result column="FSH_SEASON" jdbcType="VARCHAR" property="fshSeason" />
        <result column="FSH_COLLECTION" jdbcType="VARCHAR" property="fshCollection" />
        <result column="FSH_THEME" jdbcType="VARCHAR" property="fshTheme" />
        <result column="FSH_ATP_DATE" jdbcType="VARCHAR" property="fshAtpDate" />
        <result column="FSH_VAS_REL" jdbcType="VARCHAR" property="fshVasRel" />
        <result column="FSH_VAS_PRNT_ID" jdbcType="VARCHAR" property="fshVasPrntId" />
        <result column="FSH_TRANSACTION" jdbcType="VARCHAR" property="fshTransaction" />
        <result column="FSH_ITEM_GROUP" jdbcType="VARCHAR" property="fshItemGroup" />
        <result column="FSH_ITEM" jdbcType="VARCHAR" property="fshItem" />
        <result column="FSH_SS" jdbcType="VARCHAR" property="fshSs" />
        <result column="FSH_GRID_COND_REC" jdbcType="VARCHAR" property="fshGridCondRec" />
        <result column="FSH_PSM_PFM_SPLIT" jdbcType="VARCHAR" property="fshPsmPfmSplit" />
        <result column="CNFM_QTY" jdbcType="DECIMAL" property="cnfmQty" />
        <result column="STPAC" jdbcType="VARCHAR" property="stpac" />
        <result column="LGBZO" jdbcType="VARCHAR" property="lgbzo" />
        <result column="LGBZO_B" jdbcType="VARCHAR" property="lgbzoB" />
        <result column="ADDRNUM" jdbcType="VARCHAR" property="addrnum" />
        <result column="CONSNUM" jdbcType="VARCHAR" property="consnum" />
        <result column="BORGR_MISS" jdbcType="VARCHAR" property="borgrMiss" />
        <result column="DEP_ID" jdbcType="VARCHAR" property="depId" />
        <result column="BELNR" jdbcType="VARCHAR" property="belnr" />
        <result column="KBLPOS_CAB" jdbcType="VARCHAR" property="kblposCab" />
        <result column="KBLNR_COMP" jdbcType="VARCHAR" property="kblnrComp" />
        <result column="KBLPOS_COMP" jdbcType="VARCHAR" property="kblposComp" />
        <result column="WBS_ELEMENT" jdbcType="VARCHAR" property="wbsElement" />
        <result column="RFM_PSST_RULE" jdbcType="VARCHAR" property="rfmPsstRule" />
        <result column="RFM_PSST_GROUP" jdbcType="VARCHAR" property="rfmPsstGroup" />
        <result column="REF_ITEM" jdbcType="VARCHAR" property="refItem" />
        <result column="SOURCE_ID" jdbcType="VARCHAR" property="sourceId" />
        <result column="SOURCE_KEY" jdbcType="VARCHAR" property="sourceKey" />
        <result column="PUT_BACK" jdbcType="VARCHAR" property="putBack" />
        <result column="POL_ID" jdbcType="VARCHAR" property="polId" />
        <result column="CONS_ORDER" jdbcType="VARCHAR" property="consOrder" />
        <result column="ZZPAID" jdbcType="VARCHAR" property="zzpaid" />
        <result column="ZZPAMT" jdbcType="DECIMAL" property="zzpamt" />
        <result column="ZZSIGN" jdbcType="DECIMAL" property="zzsign" />
        <result column="ZZDATE" jdbcType="VARCHAR" property="zzdate" />
        <result column="ZZTIME" jdbcType="VARCHAR" property="zztime" />
        <result column="ZZPRICE" jdbcType="VARCHAR" property="zzprice" />
        <result column="ZZPEINH" jdbcType="VARCHAR" property="zzpeinh" />
        <result column="ZZMWSKZ" jdbcType="VARCHAR" property="zzmwskz" />
    </resultMap>

    <resultMap id="EKBEResultMap" type="com.cowell.iscm.entity.SapEkbe">
        <result column="MANDT" jdbcType="VARCHAR" property="mandt" />
        <result column="EBELN" jdbcType="VARCHAR" property="ebeln" />
        <result column="EBELP" jdbcType="VARCHAR" property="ebelp" />
        <result column="ZEKKN" jdbcType="VARCHAR" property="zekkn" />
        <result column="VGABE" jdbcType="VARCHAR" property="vgabe" />
        <result column="GJAHR" jdbcType="VARCHAR" property="gjahr" />
        <result column="BELNR" jdbcType="VARCHAR" property="belnr" />
        <result column="BUZEI" jdbcType="VARCHAR" property="buzei" />
        <result column="BEWTP" jdbcType="VARCHAR" property="bewtp" />
        <result column="BWART" jdbcType="VARCHAR" property="bwart" />
        <result column="BUDAT" jdbcType="VARCHAR" property="budat" />
        <result column="MENGE" jdbcType="DECIMAL" property="menge" />
        <result column="BPMNG" jdbcType="DECIMAL" property="bpmng" />
        <result column="DMBTR" jdbcType="DECIMAL" property="dmbtr" />
        <result column="WRBTR" jdbcType="DECIMAL" property="wrbtr" />
        <result column="WAERS" jdbcType="VARCHAR" property="waers" />
        <result column="AREWR" jdbcType="DECIMAL" property="arewr" />
        <result column="WESBS" jdbcType="DECIMAL" property="wesbs" />
        <result column="BPWES" jdbcType="DECIMAL" property="bpwes" />
        <result column="SHKZG" jdbcType="VARCHAR" property="shkzg" />
        <result column="BWTAR" jdbcType="VARCHAR" property="bwtar" />
        <result column="ELIKZ" jdbcType="VARCHAR" property="elikz" />
        <result column="XBLNR" jdbcType="VARCHAR" property="xblnr" />
        <result column="LFGJA" jdbcType="VARCHAR" property="lfgja" />
        <result column="LFBNR" jdbcType="VARCHAR" property="lfbnr" />
        <result column="LFPOS" jdbcType="VARCHAR" property="lfpos" />
        <result column="GRUND" jdbcType="VARCHAR" property="grund" />
        <result column="CPUDT" jdbcType="VARCHAR" property="cpudt" />
        <result column="CPUTM" jdbcType="VARCHAR" property="cputm" />
        <result column="REEWR" jdbcType="DECIMAL" property="reewr" />
        <result column="EVERE" jdbcType="VARCHAR" property="evere" />
        <result column="REFWR" jdbcType="DECIMAL" property="refwr" />
        <result column="MATNR" jdbcType="VARCHAR" property="matnr" />
        <result column="WERKS" jdbcType="VARCHAR" property="werks" />
        <result column="XWSBR" jdbcType="VARCHAR" property="xwsbr" />
        <result column="ETENS" jdbcType="VARCHAR" property="etens" />
        <result column="KNUMV" jdbcType="VARCHAR" property="knumv" />
        <result column="MWSKZ" jdbcType="VARCHAR" property="mwskz" />
        <result column="LSMNG" jdbcType="DECIMAL" property="lsmng" />
        <result column="LSMEH" jdbcType="VARCHAR" property="lsmeh" />
        <result column="EMATN" jdbcType="VARCHAR" property="ematn" />
        <result column="AREWW" jdbcType="DECIMAL" property="areww" />
        <result column="HSWAE" jdbcType="VARCHAR" property="hswae" />
        <result column="BAMNG" jdbcType="DECIMAL" property="bamng" />
        <result column="CHARG" jdbcType="VARCHAR" property="charg" />
        <result column="BLDAT" jdbcType="VARCHAR" property="bldat" />
        <result column="XWOFF" jdbcType="VARCHAR" property="xwoff" />
        <result column="XUNPL" jdbcType="VARCHAR" property="xunpl" />
        <result column="ERNAM" jdbcType="VARCHAR" property="ernam" />
        <result column="SRVPOS" jdbcType="VARCHAR" property="srvpos" />
        <result column="PACKNO" jdbcType="VARCHAR" property="packno" />
        <result column="INTROW" jdbcType="VARCHAR" property="introw" />
        <result column="BEKKN" jdbcType="VARCHAR" property="bekkn" />
        <result column="LEMIN" jdbcType="VARCHAR" property="lemin" />
        <result column="AREWB" jdbcType="DECIMAL" property="arewb" />
        <result column="REWRB" jdbcType="DECIMAL" property="rewrb" />
        <result column="SAPRL" jdbcType="VARCHAR" property="saprl" />
        <result column="MENGE_POP" jdbcType="DECIMAL" property="mengePop" />
        <result column="BPMNG_POP" jdbcType="DECIMAL" property="bpmngPop" />
        <result column="DMBTR_POP" jdbcType="DECIMAL" property="dmbtrPop" />
        <result column="WRBTR_POP" jdbcType="DECIMAL" property="wrbtrPop" />
        <result column="WESBB" jdbcType="DECIMAL" property="wesbb" />
        <result column="BPWEB" jdbcType="DECIMAL" property="bpweb" />
        <result column="WEORA" jdbcType="VARCHAR" property="weora" />
        <result column="AREWR_POP" jdbcType="DECIMAL" property="arewrPop" />
        <result column="KUDIF" jdbcType="DECIMAL" property="kudif" />
        <result column="RETAMT_FC" jdbcType="DECIMAL" property="retamtFc" />
        <result column="RETAMT_LC" jdbcType="DECIMAL" property="retamtLc" />
        <result column="RETAMTP_FC" jdbcType="DECIMAL" property="retamtpFc" />
        <result column="RETAMTP_LC" jdbcType="DECIMAL" property="retamtpLc" />
        <result column="XMACC" jdbcType="VARCHAR" property="xmacc" />
        <result column="WKURS" jdbcType="DECIMAL" property="wkurs" />
        <result column="INV_ITEM_ORIGIN" jdbcType="VARCHAR" property="invItemOrigin" />
        <result column="VBELN_ST" jdbcType="VARCHAR" property="vbelnSt" />
        <result column="VBELP_ST" jdbcType="VARCHAR" property="vbelpSt" />
        <result column="SGT_SCAT" jdbcType="VARCHAR" property="sgtScat" />
        <result column="DATAAGING" jdbcType="VARCHAR" property="dataaging" />
        <result column="SESUOM" jdbcType="VARCHAR" property="sesuom" />
        <result column="ET_UPD" jdbcType="VARCHAR" property="etUpd" />
        <result column="/CWM/BAMNG" jdbcType="DECIMAL" property="cwmBamng" />
        <result column="/CWM/WESBS" jdbcType="DECIMAL" property="cwmWesbs" />
        <result column="/CWM/TY2TQ" jdbcType="VARCHAR" property="cwmTy2tq" />
        <result column="/CWM/WESBB" jdbcType="DECIMAL" property="cwmWesbb" />
        <result column="J_SC_DIE_COMP_F" jdbcType="VARCHAR" property="jScDieCompF" />
        <result column="FSH_SEASON_YEAR" jdbcType="VARCHAR" property="fshSeasonYear" />
        <result column="FSH_SEASON" jdbcType="VARCHAR" property="fshSeason" />
        <result column="FSH_COLLECTION" jdbcType="VARCHAR" property="fshCollection" />
        <result column="FSH_THEME" jdbcType="VARCHAR" property="fshTheme" />
        <result column="QTY_DIFF" jdbcType="DECIMAL" property="qtyDiff" />
        <result column="WRF_CHARSTC1" jdbcType="VARCHAR" property="wrfCharstc1" />
        <result column="WRF_CHARSTC2" jdbcType="VARCHAR" property="wrfCharstc2" />
        <result column="WRF_CHARSTC3" jdbcType="VARCHAR" property="wrfCharstc3" />
        <result column="ZZDATE" jdbcType="VARCHAR" property="zzdate" />
        <result column="ZZTIME" jdbcType="VARCHAR" property="zztime" />
    </resultMap>
    <!-- select distinct BUKRS
    from zsdt0055
    (发货DC逻辑判断）
    where ZDCLJPD = 'Z1'
   （POS接口类型)
    and ZJKLX（POS接口类型） =  'HC'
    MANDT sap环境 600预发 800线上
    and MANDT = #{dev} -->

    <select id="get55CompanyCodes" resultType="java.lang.String">
        select distinct BUKRS
        from zsdt0055
        where ZDCLJPD = 'Z1'
        and ZJKLX =  'HC'
        and MANDT = #{dev}
        WITH HINT (RESULT_LAG('hana_sr' , 600))
    </select>

    <select id="getStoreWarehousRelations"
            resultType="com.cowell.iscm.service.dto.returnWarehouse.StoreWarehouseRelationDTO">
        select  a.LOCNR as storeCode, a.BUKRS as companyCode, a.ZSJBM as warehouseCode, b.NAME1 as warehouseName
        from zsdt0014 a, T001W b
        where a.ZSJBM = b. WERKS
        and a.MANDT = #{dev}
        and b.MANDT = #{dev}
        and a.LOCNR in
        <foreach collection="storeCodes" item="storeCode" index="index" open="(" close=")" separator=",">
            #{storeCode}
        </foreach>
        WITH HINT (RESULT_LAG('hana_sr' , 600))
    </select>

    <select id="getWarehouseCodes" resultType="java.lang.String">
        select ZPFQY from zmmt0258 where MANDT = #{dev} WITH HINT (RESULT_LAG('hana_sr' , 60))
    </select>
    <select id="getSuggestAcceptQuantiay"
            resultType="com.cowell.iscm.service.dto.returnWarehouse.SuggestAcceptDTO">
        select ZAVG_QTY,ZSUM_R_V,ZDC_INV_UPPER,ZSTOCK_DC,ZTZDCKC,ZZC_INACTIVE_DC_STOCK,ZMDQTY,ZSTOCK_STORE,ZSTOCK_TOTAL,ZTZDCDKC2,ZDCKYQTY,ZMENGE_WQ7,ZTCWQ from zmmt0225
        where ERDAT = #{date}
        and WERKS = #{warehouseCode}
        and MATNR = #{goodsNo}
        and MANDT = #{dev}
        WITH HINT (RESULT_LAG('hana_sr' , 600))
    </select>
    <select id="batchGetSuggestAcceptQuantiay"
            resultType="com.cowell.iscm.service.dto.returnWarehouse.SuggestAcceptDTO">
        select ZAVG_QTY,ZSUM_R_V,ZDC_INV_UPPER,ZSTOCK_DC,ZTZDCKC,ZZC_INACTIVE_DC_STOCK,ZMDQTY,ZSTOCK_STORE,ZSTOCK_TOTAL,ZTZDCDKC2,MATNR,ZDCKYQTY,ZMENGE_WQ7,ZTCWQ from zmmt0225
        where ERDAT = #{date}
        and WERKS = #{warehouseCode}
        and MATNR in
        <foreach collection="goodsNos" item="goodsNo" index="index" separator="," open="(" close=")">
            #{goodsNo}
        </foreach>
        and MANDT = #{dev}
        WITH HINT (RESULT_LAG('hana_sr' , 600))
    </select>

    <select id="getSapZmmt0288List" resultMap="ZMMT0288ResultMap">
        select *  from ZMMT0288
        where MANDT = #{dev}
        <if test="initDate != null">
            and ZCRDATE >= #{initDate}
        </if>
        <if test="syncDate != null  and syncTime != null ">
            and ( (ZCRDATE = #{syncDate}  and  ZCRTIME  >= #{syncTime} ) or  (ZCHDATE= #{syncDate} and  ZCHTIME>= #{syncTime}  ))
        </if>
        ORDER BY ZCRDATE DESC , ZCRTIME DESC
        LIMIT #{pageSize} OFFSET #{offset}
        WITH HINT (RESULT_LAG('hana_sr' , 600))
    </select>


    <select id="getSapZmmt0098List" resultMap="ZMMT0098ResultMap">
        select *  from ZMMT0098
        where MANDT = #{dev}
        <if test="initDate != null">
            and CPUDT >= #{initDate}
        </if>
        <if test="syncDate != null  and syncTime != null ">
            and ( (CPUDT = #{syncDate}  and  CPUTM  >= #{syncTime} ) or  (AEDAT= #{syncDate} and  AEZET>= #{syncTime}  ))
        </if>
        ORDER BY CPUDT DESC , CPUTM DESC
        LIMIT #{pageSize} OFFSET #{offset}
        WITH HINT (RESULT_LAG('hana_sr' , 600))
    </select>

    <select id="getSapZmmt0287List" resultMap="ZMMT0287ResultMap">
        select *  from ZMMT0287
        where MANDT = #{dev}
        <if test="initDate != null">
            and ERDAT >= #{initDate}
        </if>
        <if test="syncDate != null  and syncTime != null ">
            and ( (ERDAT = #{syncDate}  and  ERZET  >= #{syncTime} ) or  (AEDAT= #{syncDate} and  AEZET>= #{syncTime}  ))
        </if>
        ORDER BY ERDAT DESC , ERZET DESC
        LIMIT #{pageSize} OFFSET #{offset}
        WITH HINT (RESULT_LAG('hana_sr' , 600))
    </select>

    <select id="getSapZmmt0085List" resultMap="ZMMT0085ResultMap">
        select *  from ZMMT0085
        where MANDT = #{dev}
        <if test="initDate != null">
            and BEDAT >= #{initDate}
        </if>
        <if test="syncDate != null  and syncTime != null ">
            and (BEDAT = #{syncDate}  and  ZCJSJ  >= #{syncTime})
        </if>
        ORDER BY BEDAT DESC , ZCJSJ DESC
        LIMIT #{pageSize} OFFSET #{offset}
        WITH HINT (RESULT_LAG('hana_sr' , 600))
    </select>

    <select id="getSapZmmt0086List" resultMap="ZMMT0086ResultMap">
        select *  from ZMMT0086
        where MANDT = #{dev}
        <if test="reqNoList != null and reqNoList.size > 0">
            AND PURREQNO IN
            <foreach collection="reqNoList" item="reqNo" index="index" open="(" close=")" separator=",">
                #{reqNo}
            </foreach>
        </if>
        WITH HINT (RESULT_LAG('hana_sr' , 600))
    </select>

    <select id="getSapZmmt0355List" resultMap="ZMMT0355ResultMap">
        select *  from ZMMT0355
        where MANDT = #{dev}
        <if test="initDate != null">
            and ERDAT >= #{initDate}
        </if>
        <if test="syncDate != null  and syncTime != null ">
            and ( (ERDAT = #{syncDate}  and  ERZET  >= #{syncTime} ) or  (UDATE= #{syncDate} and  UTIME>= #{syncTime}  ))
        </if>
        ORDER BY ERDAT DESC , ERZET DESC
        LIMIT #{pageSize} OFFSET #{offset}
        WITH HINT (RESULT_LAG('hana_sr' , 600))
    </select>

    <select id="getSapEKKOList" resultMap="EKKOResultMap">
        select  *  from EKKO
        where MANDT = #{dev}
        AND
        BSTYP='F'
        and
        ZZZWLMS = ''
        and
        UNSEZ  = ''
        <if test="initDate != null">
            and AEDAT >= #{initDate}
        </if>
        <if test="syncDate != null and syncDateTime != null  ">
            and ( AEDAT = #{syncDate}  or  LASTCHANGEDATETIME >=  #{syncDateTime} )
        </if>
        ORDER BY AEDAT DESC
        LIMIT #{pageSize} OFFSET #{offset}
        WITH HINT (RESULT_LAG('hana_sr' , 600))
    </select>

    <select id="getSapEKPOList" resultMap="EKPOResultMap">
        select *  from EKPO
        where MANDT = #{dev}
        <if test="reqNoList != null and reqNoList.size > 0">
            AND EBELN IN
            <foreach collection="reqNoList" item="reqNo" index="index" open="(" close=")" separator=",">
                #{reqNo}
            </foreach>
        </if>
        ORDER BY AEDAT DESC
        LIMIT #{pageSize} OFFSET #{offset}
        WITH HINT (RESULT_LAG('hana_sr' , 600))
    </select>

    <select id="getSapEKBEList" resultMap="EKBEResultMap">
        select *  from EKBE
        where MANDT = #{dev}
        <if test="reqNoList != null and reqNoList.size > 0">
            AND EBELN IN
            <foreach collection="reqNoList" item="reqNo" index="index" open="(" close=")" separator=",">
                #{reqNo}
            </foreach>
        </if>
        ORDER BY CPUDT DESC , CPUTM DESC
        LIMIT #{pageSize} OFFSET #{offset}
        WITH HINT (RESULT_LAG('hana_sr' , 600))
    </select>


</mapper>
