package com.cowell.iscm.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 菜单权限
 * <AUTHOR>
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface MenuSecurity {
    /**
     * 需要校验的菜单权限名,仅提示用,
     * @return
     */
    String name();

    /**
     * 菜单权限action,在perm系统里查看
     */
    String[] actions();

    /**
     * 是否校验所有action
     * false : 只要有一个action符合即成功
     * true : 所有action符合才成功
     */
    boolean checkAll() default false;
}
