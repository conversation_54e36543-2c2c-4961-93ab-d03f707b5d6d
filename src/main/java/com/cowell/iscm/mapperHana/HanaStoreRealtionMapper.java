package com.cowell.iscm.mapperHana;

import com.cowell.iscm.entity.*;
import com.cowell.iscm.service.dto.returnWarehouse.StoreWarehouseRelationDTO;
import com.cowell.iscm.service.dto.returnWarehouse.SuggestAcceptDTO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

public interface HanaStoreRealtionMapper {

    List<String> get55CompanyCodes(@Param("dev") Integer dev);

    List<StoreWarehouseRelationDTO> getStoreWarehousRelations(@Param("storeCodes") List<String> storeCodes, @Param("dev") Integer dev);

    List<String> getWarehouseCodes(@Param("dev") Integer dev);

    /**
     * 获取建议可接受数量
     */
    SuggestAcceptDTO getSuggestAcceptQuantiay(@Param("warehouseCode") String warehouseCode, @Param("date") String date, @Param("goodsNo") String goodsNo, @Param("dev") Integer dev);

    /**
     * 批量获取建议可接受数量
     */
    List<SuggestAcceptDTO> batchGetSuggestAcceptQuantiay(@Param("warehouseCode") String warehouseCode, @Param("date") String date, @Param("goodsNos") List<String> goodsNos, @Param("dev") Integer dev);


    /**
     * 大仓补货55下单历史表
     * @param offset    起始idx
     * @param pageSize  取多少个
     * @param initDate  初始化取数日期
     * @param syncDate  指定日期
     * @param syncTime  指定时间 xxl同步计算出的查询时间范围
     * @param dev       环境 600 800
     * @return
     */
    List<SapZmmt0288> getSapZmmt0288List(@Param("offset") Long offset, @Param("pageSize") Long pageSize, @Param("initDate") String initDate, @Param("syncDate") String syncDate, @Param("syncTime") String syncTime, @Param("dev") Integer dev);

    /**
     * 大仓补货采购员信息维护表
     */
    List<SapZmmt0098> getSapZmmt0098List(@Param("offset") Long offset, @Param("pageSize") Long pageSize,@Param("initDate") String initDate, @Param("syncDate") String syncDate, @Param("syncTime") String syncTime, @Param("dev") Integer dev);

    /**
     * 内采要货申请明细表
     */
    List<SapZmmt0287> getSapZmmt0287List(@Param("offset") Long offset, @Param("pageSize") Long pageSize, @Param("initDate") String initDate, @Param("syncDate") String syncDate, @Param("syncTime") String syncTime, @Param("dev") Integer dev);

    /**
     * 内采申请处理表(抬头)
     */
    List<SapZmmt0085> getSapZmmt0085List(@Param("offset") Long offset, @Param("pageSize") Long pageSize, @Param("initDate") String initDate, @Param("syncDate") String syncDate, @Param("syncTime") String syncTime, @Param("dev") Integer dev);

    /**
     * 内采申请处理表(明细) 采购计划表内采申请处理表
     */
    List<SapZmmt0086> getSapZmmt0086List(@Param("reqNoList") List<String> reqNoList, @Param("dev") Integer dev);

    /**
     * 采购计划表
     */
    List<SapZmmt0355> getSapZmmt0355List(@Param("offset") Long offset, @Param("pageSize") Long pageSize, @Param("initDate") String initDate, @Param("syncDate") String syncDate, @Param("syncTime") String syncTime, @Param("dev") Integer dev);

    /**
     * 采购订单抬头表
     */
    List<SapEkko> getSapEKKOList(@Param("offset") Long offset, @Param("pageSize") Long pageSize, @Param("initDate") String initDate,@Param("syncDate") String syncDate, @Param("syncDateTime") BigDecimal syncDateTime, @Param("dev") Integer dev);

    /**
     * 采购订单明细表
     */
    List<SapEkpo> getSapEKPOList(@Param("reqNoList") List<String> reqNoList, @Param("dev") Integer dev, @Param("offset") Long offset, @Param("pageSize") Long pageSize);

    /**
     * 采购订单历史
     */
    List<SapEkbe> getSapEKBEList(@Param("reqNoList") List<String> reqNoList, @Param("dev") Integer dev, @Param("offset") Long offset, @Param("pageSize") Long pageSize);
}
