package com.cowell.iscm.entityTidb;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> bdp日均销
 */
public class BdpPushAvgDailySales implements Serializable {
    /**
     * 无业务逻辑主键 分布式id
     */
    private Long id;

    /**
     * 公司编码
     */
    private String companyCode;

    /**
     * 门店编码
     */
    private String storeCode;

    /**
     * 门店属性 0 直营店 1加盟店
     */
    private Byte storeAttr;

    /**
     * 商品编码
     */
    private String goodsNo;

    /**
     * 是否特管0:否 1:是
     */
    private Byte specialCtrl;

    /**
     * 禁止配送0:否 1:是
     */
    private Byte distrForbid;

    /**
     * 禁止请货0:否 1:是
     */
    private Byte applyForbid;

    /**
     * 是否贵重0:否 1:是
     */
    private Byte valuable;

    /**
     * dtp商品0:否 1:是
     */
    private Byte dtpGoods;

    /**
     * 是否冷链商品0:否 1:是
     */
    private Byte coldchainind;

    /**
     * 特殊销售属性
     */
    private String specialAttr;

    /**
     * 门店采购类型
     */
    private String purchaseType;

    /**
     * 商品等级0: 新一级(g) 1: 一级 2: 二级 3: 三级 4: 四级 5: 五级 6: 六级 7: 七级 8: 新品级 9: 新二级(g) 10: 新三级(g) 11: 新四级(g) 12: 新五级(g) 13: 新六级(g) 14: 新七级(g)
     */
    private Byte goodsLevel;

    /**
     * 商品状态 0:正常 1:废弃
     */
    private Byte goodsStatus;

    /**
     * 是否首营0:否 1:是
     */
    private Byte lawful;

    /**
     * 是否新品0:否 1:是
     */
    private Byte newable;

    /**
     * 大类id
     */
    private Long categoryId;

    /**
     * 中类id
     */
    private Long middleCategoryId;

    /**
     * 小类编码
     */
    private Long smallCategoryId;

    /**
     * 子类编码
     */
    private Long subCategoryId;

    /**
     * 销售属性
     */
    private String pushLevel;

    /**
     * 商品经营属性1:新品(N) 2:订购(DG) 3:淘汰(T) 4:清场(C) 5:作废(Z) 6:拟淘汰 7:非商品(F)
     */
    private Byte goodsLine;

    /**
     * 中包装数量
     */
    private BigDecimal middlePackageQty;

    /**
     * 企业中包装处理方式 1:不处理 2:四舍五入 3:向上取整 4:参考海典GJ9084 5:参考ISCM
     */
    private String middlePackageSwitchBiz;

    /**
     * 一店一目中包装处理方式 1:不处理 2:四舍五入 3:向上取整 4:参考海典GJ9084 5:参考ISCM
     */
    private String middlePackageSwitchStore;

    /**
     * 特管商品前30天请货数量
     */
    private BigDecimal specialThirtyDaysQty;

    /**
     * 库存上限天数
     */
    private Integer stockUpperLimitDays;

    /**
     * 库存下限天数
     */
    private Integer stockLowerLimitDays;

    /**
     * 日均销
     */
    private BigDecimal averageDailySales;

    /**
     * 最小陈列量
     */
    private BigDecimal minDisplayQty;

    /**
     * 库存上限
     */
    private BigDecimal stockUpperLimit;

    /**
     * 库存下限
     */
    private BigDecimal stockLowerLimit;

    /**
     * 近30天销量
     */
    private BigDecimal thirtyDaysSales;

    /**
     * 近60天销量
     */
    private BigDecimal sixtyDaysSales;

    /**
     * 近90天销量
     */
    private BigDecimal ninetyDaysSales;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新时间
     */
    private Date gmtUpdate;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getStoreCode() {
        return storeCode;
    }

    public void setStoreCode(String storeCode) {
        this.storeCode = storeCode;
    }

    public Byte getStoreAttr() {
        return storeAttr;
    }

    public void setStoreAttr(Byte storeAttr) {
        this.storeAttr = storeAttr;
    }

    public String getGoodsNo() {
        return goodsNo;
    }

    public void setGoodsNo(String goodsNo) {
        this.goodsNo = goodsNo;
    }

    public Byte getSpecialCtrl() {
        return specialCtrl;
    }

    public void setSpecialCtrl(Byte specialCtrl) {
        this.specialCtrl = specialCtrl;
    }

    public Byte getDistrForbid() {
        return distrForbid;
    }

    public void setDistrForbid(Byte distrForbid) {
        this.distrForbid = distrForbid;
    }

    public Byte getApplyForbid() {
        return applyForbid;
    }

    public void setApplyForbid(Byte applyForbid) {
        this.applyForbid = applyForbid;
    }

    public Byte getValuable() {
        return valuable;
    }

    public void setValuable(Byte valuable) {
        this.valuable = valuable;
    }

    public Byte getDtpGoods() {
        return dtpGoods;
    }

    public void setDtpGoods(Byte dtpGoods) {
        this.dtpGoods = dtpGoods;
    }

    public Byte getColdchainind() {
        return coldchainind;
    }

    public void setColdchainind(Byte coldchainind) {
        this.coldchainind = coldchainind;
    }

    public String getSpecialAttr() {
        return specialAttr;
    }

    public void setSpecialAttr(String specialAttr) {
        this.specialAttr = specialAttr;
    }

    public String getPurchaseType() {
        return purchaseType;
    }

    public void setPurchaseType(String purchaseType) {
        this.purchaseType = purchaseType;
    }

    public Byte getGoodsLevel() {
        return goodsLevel;
    }

    public void setGoodsLevel(Byte goodsLevel) {
        this.goodsLevel = goodsLevel;
    }

    public Byte getGoodsStatus() {
        return goodsStatus;
    }

    public void setGoodsStatus(Byte goodsStatus) {
        this.goodsStatus = goodsStatus;
    }

    public Byte getLawful() {
        return lawful;
    }

    public void setLawful(Byte lawful) {
        this.lawful = lawful;
    }

    public Byte getNewable() {
        return newable;
    }

    public void setNewable(Byte newable) {
        this.newable = newable;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public Long getMiddleCategoryId() {
        return middleCategoryId;
    }

    public void setMiddleCategoryId(Long middleCategoryId) {
        this.middleCategoryId = middleCategoryId;
    }

    public Long getSmallCategoryId() {
        return smallCategoryId;
    }

    public void setSmallCategoryId(Long smallCategoryId) {
        this.smallCategoryId = smallCategoryId;
    }

    public Long getSubCategoryId() {
        return subCategoryId;
    }

    public void setSubCategoryId(Long subCategoryId) {
        this.subCategoryId = subCategoryId;
    }

    public String getPushLevel() {
        return pushLevel;
    }

    public void setPushLevel(String pushLevel) {
        this.pushLevel = pushLevel;
    }

    public Byte getGoodsLine() {
        return goodsLine;
    }

    public void setGoodsLine(Byte goodsLine) {
        this.goodsLine = goodsLine;
    }

    public BigDecimal getMiddlePackageQty() {
        return middlePackageQty;
    }

    public void setMiddlePackageQty(BigDecimal middlePackageQty) {
        this.middlePackageQty = middlePackageQty;
    }

    public String getMiddlePackageSwitchBiz() {
        return middlePackageSwitchBiz;
    }

    public void setMiddlePackageSwitchBiz(String middlePackageSwitchBiz) {
        this.middlePackageSwitchBiz = middlePackageSwitchBiz;
    }

    public String getMiddlePackageSwitchStore() {
        return middlePackageSwitchStore;
    }

    public void setMiddlePackageSwitchStore(String middlePackageSwitchStore) {
        this.middlePackageSwitchStore = middlePackageSwitchStore;
    }

    public BigDecimal getSpecialThirtyDaysQty() {
        return specialThirtyDaysQty;
    }

    public void setSpecialThirtyDaysQty(BigDecimal specialThirtyDaysQty) {
        this.specialThirtyDaysQty = specialThirtyDaysQty;
    }

    public Integer getStockUpperLimitDays() {
        return stockUpperLimitDays;
    }

    public void setStockUpperLimitDays(Integer stockUpperLimitDays) {
        this.stockUpperLimitDays = stockUpperLimitDays;
    }

    public Integer getStockLowerLimitDays() {
        return stockLowerLimitDays;
    }

    public void setStockLowerLimitDays(Integer stockLowerLimitDays) {
        this.stockLowerLimitDays = stockLowerLimitDays;
    }

    public BigDecimal getAverageDailySales() {
        return averageDailySales;
    }

    public void setAverageDailySales(BigDecimal averageDailySales) {
        this.averageDailySales = averageDailySales;
    }

    public BigDecimal getMinDisplayQty() {
        return minDisplayQty;
    }

    public void setMinDisplayQty(BigDecimal minDisplayQty) {
        this.minDisplayQty = minDisplayQty;
    }

    public BigDecimal getStockUpperLimit() {
        return stockUpperLimit;
    }

    public void setStockUpperLimit(BigDecimal stockUpperLimit) {
        this.stockUpperLimit = stockUpperLimit;
    }

    public BigDecimal getStockLowerLimit() {
        return stockLowerLimit;
    }

    public void setStockLowerLimit(BigDecimal stockLowerLimit) {
        this.stockLowerLimit = stockLowerLimit;
    }

    public BigDecimal getThirtyDaysSales() {
        return thirtyDaysSales;
    }

    public void setThirtyDaysSales(BigDecimal thirtyDaysSales) {
        this.thirtyDaysSales = thirtyDaysSales;
    }

    public BigDecimal getSixtyDaysSales() {
        return sixtyDaysSales;
    }

    public void setSixtyDaysSales(BigDecimal sixtyDaysSales) {
        this.sixtyDaysSales = sixtyDaysSales;
    }

    public BigDecimal getNinetyDaysSales() {
        return ninetyDaysSales;
    }

    public void setNinetyDaysSales(BigDecimal ninetyDaysSales) {
        this.ninetyDaysSales = ninetyDaysSales;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtUpdate() {
        return gmtUpdate;
    }

    public void setGmtUpdate(Date gmtUpdate) {
        this.gmtUpdate = gmtUpdate;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        BdpPushAvgDailySales other = (BdpPushAvgDailySales) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getCompanyCode() == null ? other.getCompanyCode() == null : this.getCompanyCode().equals(other.getCompanyCode()))
            && (this.getStoreCode() == null ? other.getStoreCode() == null : this.getStoreCode().equals(other.getStoreCode()))
            && (this.getStoreAttr() == null ? other.getStoreAttr() == null : this.getStoreAttr().equals(other.getStoreAttr()))
            && (this.getGoodsNo() == null ? other.getGoodsNo() == null : this.getGoodsNo().equals(other.getGoodsNo()))
            && (this.getSpecialCtrl() == null ? other.getSpecialCtrl() == null : this.getSpecialCtrl().equals(other.getSpecialCtrl()))
            && (this.getDistrForbid() == null ? other.getDistrForbid() == null : this.getDistrForbid().equals(other.getDistrForbid()))
            && (this.getApplyForbid() == null ? other.getApplyForbid() == null : this.getApplyForbid().equals(other.getApplyForbid()))
            && (this.getValuable() == null ? other.getValuable() == null : this.getValuable().equals(other.getValuable()))
            && (this.getDtpGoods() == null ? other.getDtpGoods() == null : this.getDtpGoods().equals(other.getDtpGoods()))
            && (this.getColdchainind() == null ? other.getColdchainind() == null : this.getColdchainind().equals(other.getColdchainind()))
            && (this.getSpecialAttr() == null ? other.getSpecialAttr() == null : this.getSpecialAttr().equals(other.getSpecialAttr()))
            && (this.getPurchaseType() == null ? other.getPurchaseType() == null : this.getPurchaseType().equals(other.getPurchaseType()))
            && (this.getGoodsLevel() == null ? other.getGoodsLevel() == null : this.getGoodsLevel().equals(other.getGoodsLevel()))
            && (this.getGoodsStatus() == null ? other.getGoodsStatus() == null : this.getGoodsStatus().equals(other.getGoodsStatus()))
            && (this.getLawful() == null ? other.getLawful() == null : this.getLawful().equals(other.getLawful()))
            && (this.getNewable() == null ? other.getNewable() == null : this.getNewable().equals(other.getNewable()))
            && (this.getCategoryId() == null ? other.getCategoryId() == null : this.getCategoryId().equals(other.getCategoryId()))
            && (this.getMiddleCategoryId() == null ? other.getMiddleCategoryId() == null : this.getMiddleCategoryId().equals(other.getMiddleCategoryId()))
            && (this.getSmallCategoryId() == null ? other.getSmallCategoryId() == null : this.getSmallCategoryId().equals(other.getSmallCategoryId()))
            && (this.getSubCategoryId() == null ? other.getSubCategoryId() == null : this.getSubCategoryId().equals(other.getSubCategoryId()))
            && (this.getPushLevel() == null ? other.getPushLevel() == null : this.getPushLevel().equals(other.getPushLevel()))
            && (this.getGoodsLine() == null ? other.getGoodsLine() == null : this.getGoodsLine().equals(other.getGoodsLine()))
            && (this.getMiddlePackageQty() == null ? other.getMiddlePackageQty() == null : this.getMiddlePackageQty().equals(other.getMiddlePackageQty()))
            && (this.getMiddlePackageSwitchBiz() == null ? other.getMiddlePackageSwitchBiz() == null : this.getMiddlePackageSwitchBiz().equals(other.getMiddlePackageSwitchBiz()))
            && (this.getMiddlePackageSwitchStore() == null ? other.getMiddlePackageSwitchStore() == null : this.getMiddlePackageSwitchStore().equals(other.getMiddlePackageSwitchStore()))
            && (this.getSpecialThirtyDaysQty() == null ? other.getSpecialThirtyDaysQty() == null : this.getSpecialThirtyDaysQty().equals(other.getSpecialThirtyDaysQty()))
            && (this.getStockUpperLimitDays() == null ? other.getStockUpperLimitDays() == null : this.getStockUpperLimitDays().equals(other.getStockUpperLimitDays()))
            && (this.getStockLowerLimitDays() == null ? other.getStockLowerLimitDays() == null : this.getStockLowerLimitDays().equals(other.getStockLowerLimitDays()))
            && (this.getAverageDailySales() == null ? other.getAverageDailySales() == null : this.getAverageDailySales().equals(other.getAverageDailySales()))
            && (this.getMinDisplayQty() == null ? other.getMinDisplayQty() == null : this.getMinDisplayQty().equals(other.getMinDisplayQty()))
            && (this.getStockUpperLimit() == null ? other.getStockUpperLimit() == null : this.getStockUpperLimit().equals(other.getStockUpperLimit()))
            && (this.getStockLowerLimit() == null ? other.getStockLowerLimit() == null : this.getStockLowerLimit().equals(other.getStockLowerLimit()))
            && (this.getThirtyDaysSales() == null ? other.getThirtyDaysSales() == null : this.getThirtyDaysSales().equals(other.getThirtyDaysSales()))
            && (this.getSixtyDaysSales() == null ? other.getSixtyDaysSales() == null : this.getSixtyDaysSales().equals(other.getSixtyDaysSales()))
            && (this.getNinetyDaysSales() == null ? other.getNinetyDaysSales() == null : this.getNinetyDaysSales().equals(other.getNinetyDaysSales()))
            && (this.getGmtCreate() == null ? other.getGmtCreate() == null : this.getGmtCreate().equals(other.getGmtCreate()))
            && (this.getGmtUpdate() == null ? other.getGmtUpdate() == null : this.getGmtUpdate().equals(other.getGmtUpdate()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getCompanyCode() == null) ? 0 : getCompanyCode().hashCode());
        result = prime * result + ((getStoreCode() == null) ? 0 : getStoreCode().hashCode());
        result = prime * result + ((getStoreAttr() == null) ? 0 : getStoreAttr().hashCode());
        result = prime * result + ((getGoodsNo() == null) ? 0 : getGoodsNo().hashCode());
        result = prime * result + ((getSpecialCtrl() == null) ? 0 : getSpecialCtrl().hashCode());
        result = prime * result + ((getDistrForbid() == null) ? 0 : getDistrForbid().hashCode());
        result = prime * result + ((getApplyForbid() == null) ? 0 : getApplyForbid().hashCode());
        result = prime * result + ((getValuable() == null) ? 0 : getValuable().hashCode());
        result = prime * result + ((getDtpGoods() == null) ? 0 : getDtpGoods().hashCode());
        result = prime * result + ((getColdchainind() == null) ? 0 : getColdchainind().hashCode());
        result = prime * result + ((getSpecialAttr() == null) ? 0 : getSpecialAttr().hashCode());
        result = prime * result + ((getPurchaseType() == null) ? 0 : getPurchaseType().hashCode());
        result = prime * result + ((getGoodsLevel() == null) ? 0 : getGoodsLevel().hashCode());
        result = prime * result + ((getGoodsStatus() == null) ? 0 : getGoodsStatus().hashCode());
        result = prime * result + ((getLawful() == null) ? 0 : getLawful().hashCode());
        result = prime * result + ((getNewable() == null) ? 0 : getNewable().hashCode());
        result = prime * result + ((getCategoryId() == null) ? 0 : getCategoryId().hashCode());
        result = prime * result + ((getMiddleCategoryId() == null) ? 0 : getMiddleCategoryId().hashCode());
        result = prime * result + ((getSmallCategoryId() == null) ? 0 : getSmallCategoryId().hashCode());
        result = prime * result + ((getSubCategoryId() == null) ? 0 : getSubCategoryId().hashCode());
        result = prime * result + ((getPushLevel() == null) ? 0 : getPushLevel().hashCode());
        result = prime * result + ((getGoodsLine() == null) ? 0 : getGoodsLine().hashCode());
        result = prime * result + ((getMiddlePackageQty() == null) ? 0 : getMiddlePackageQty().hashCode());
        result = prime * result + ((getMiddlePackageSwitchBiz() == null) ? 0 : getMiddlePackageSwitchBiz().hashCode());
        result = prime * result + ((getMiddlePackageSwitchStore() == null) ? 0 : getMiddlePackageSwitchStore().hashCode());
        result = prime * result + ((getSpecialThirtyDaysQty() == null) ? 0 : getSpecialThirtyDaysQty().hashCode());
        result = prime * result + ((getStockUpperLimitDays() == null) ? 0 : getStockUpperLimitDays().hashCode());
        result = prime * result + ((getStockLowerLimitDays() == null) ? 0 : getStockLowerLimitDays().hashCode());
        result = prime * result + ((getAverageDailySales() == null) ? 0 : getAverageDailySales().hashCode());
        result = prime * result + ((getMinDisplayQty() == null) ? 0 : getMinDisplayQty().hashCode());
        result = prime * result + ((getStockUpperLimit() == null) ? 0 : getStockUpperLimit().hashCode());
        result = prime * result + ((getStockLowerLimit() == null) ? 0 : getStockLowerLimit().hashCode());
        result = prime * result + ((getThirtyDaysSales() == null) ? 0 : getThirtyDaysSales().hashCode());
        result = prime * result + ((getSixtyDaysSales() == null) ? 0 : getSixtyDaysSales().hashCode());
        result = prime * result + ((getNinetyDaysSales() == null) ? 0 : getNinetyDaysSales().hashCode());
        result = prime * result + ((getGmtCreate() == null) ? 0 : getGmtCreate().hashCode());
        result = prime * result + ((getGmtUpdate() == null) ? 0 : getGmtUpdate().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", companyCode=").append(companyCode);
        sb.append(", storeCode=").append(storeCode);
        sb.append(", storeAttr=").append(storeAttr);
        sb.append(", goodsNo=").append(goodsNo);
        sb.append(", specialCtrl=").append(specialCtrl);
        sb.append(", distrForbid=").append(distrForbid);
        sb.append(", applyForbid=").append(applyForbid);
        sb.append(", valuable=").append(valuable);
        sb.append(", dtpGoods=").append(dtpGoods);
        sb.append(", coldchainind=").append(coldchainind);
        sb.append(", specialAttr=").append(specialAttr);
        sb.append(", purchaseType=").append(purchaseType);
        sb.append(", goodsLevel=").append(goodsLevel);
        sb.append(", goodsStatus=").append(goodsStatus);
        sb.append(", lawful=").append(lawful);
        sb.append(", newable=").append(newable);
        sb.append(", categoryId=").append(categoryId);
        sb.append(", middleCategoryId=").append(middleCategoryId);
        sb.append(", smallCategoryId=").append(smallCategoryId);
        sb.append(", subCategoryId=").append(subCategoryId);
        sb.append(", pushLevel=").append(pushLevel);
        sb.append(", goodsLine=").append(goodsLine);
        sb.append(", middlePackageQty=").append(middlePackageQty);
        sb.append(", middlePackageSwitchBiz=").append(middlePackageSwitchBiz);
        sb.append(", middlePackageSwitchStore=").append(middlePackageSwitchStore);
        sb.append(", specialThirtyDaysQty=").append(specialThirtyDaysQty);
        sb.append(", stockUpperLimitDays=").append(stockUpperLimitDays);
        sb.append(", stockLowerLimitDays=").append(stockLowerLimitDays);
        sb.append(", averageDailySales=").append(averageDailySales);
        sb.append(", minDisplayQty=").append(minDisplayQty);
        sb.append(", stockUpperLimit=").append(stockUpperLimit);
        sb.append(", stockLowerLimit=").append(stockLowerLimit);
        sb.append(", thirtyDaysSales=").append(thirtyDaysSales);
        sb.append(", sixtyDaysSales=").append(sixtyDaysSales);
        sb.append(", ninetyDaysSales=").append(ninetyDaysSales);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", gmtUpdate=").append(gmtUpdate);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}