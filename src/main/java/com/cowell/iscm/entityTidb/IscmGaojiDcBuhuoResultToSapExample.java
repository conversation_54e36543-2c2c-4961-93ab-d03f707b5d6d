package com.cowell.iscm.entityTidb;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class IscmGaojiDcBuhuoResultToSapExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Long offset;

    public IscmGaojiDcBuhuoResultToSapExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Long offset) {
        this.offset = offset;
    }

    public Long getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andDtIsNull() {
            addCriterion("dt is null");
            return (Criteria) this;
        }

        public Criteria andDtIsNotNull() {
            addCriterion("dt is not null");
            return (Criteria) this;
        }

        public Criteria andDtEqualTo(String value) {
            addCriterion("dt =", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtNotEqualTo(String value) {
            addCriterion("dt <>", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtGreaterThan(String value) {
            addCriterion("dt >", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtGreaterThanOrEqualTo(String value) {
            addCriterion("dt >=", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtLessThan(String value) {
            addCriterion("dt <", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtLessThanOrEqualTo(String value) {
            addCriterion("dt <=", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtLike(String value) {
            addCriterion("dt like", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtNotLike(String value) {
            addCriterion("dt not like", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtIn(List<String> values) {
            addCriterion("dt in", values, "dt");
            return (Criteria) this;
        }

        public Criteria andDtNotIn(List<String> values) {
            addCriterion("dt not in", values, "dt");
            return (Criteria) this;
        }

        public Criteria andDtBetween(String value1, String value2) {
            addCriterion("dt between", value1, value2, "dt");
            return (Criteria) this;
        }

        public Criteria andDtNotBetween(String value1, String value2) {
            addCriterion("dt not between", value1, value2, "dt");
            return (Criteria) this;
        }

        public Criteria andWerksIsNull() {
            addCriterion("werks is null");
            return (Criteria) this;
        }

        public Criteria andWerksIsNotNull() {
            addCriterion("werks is not null");
            return (Criteria) this;
        }

        public Criteria andWerksEqualTo(String value) {
            addCriterion("werks =", value, "werks");
            return (Criteria) this;
        }

        public Criteria andWerksNotEqualTo(String value) {
            addCriterion("werks <>", value, "werks");
            return (Criteria) this;
        }

        public Criteria andWerksGreaterThan(String value) {
            addCriterion("werks >", value, "werks");
            return (Criteria) this;
        }

        public Criteria andWerksGreaterThanOrEqualTo(String value) {
            addCriterion("werks >=", value, "werks");
            return (Criteria) this;
        }

        public Criteria andWerksLessThan(String value) {
            addCriterion("werks <", value, "werks");
            return (Criteria) this;
        }

        public Criteria andWerksLessThanOrEqualTo(String value) {
            addCriterion("werks <=", value, "werks");
            return (Criteria) this;
        }

        public Criteria andWerksLike(String value) {
            addCriterion("werks like", value, "werks");
            return (Criteria) this;
        }

        public Criteria andWerksNotLike(String value) {
            addCriterion("werks not like", value, "werks");
            return (Criteria) this;
        }

        public Criteria andWerksIn(List<String> values) {
            addCriterion("werks in", values, "werks");
            return (Criteria) this;
        }

        public Criteria andWerksNotIn(List<String> values) {
            addCriterion("werks not in", values, "werks");
            return (Criteria) this;
        }

        public Criteria andWerksBetween(String value1, String value2) {
            addCriterion("werks between", value1, value2, "werks");
            return (Criteria) this;
        }

        public Criteria andWerksNotBetween(String value1, String value2) {
            addCriterion("werks not between", value1, value2, "werks");
            return (Criteria) this;
        }

        public Criteria andMatnrIsNull() {
            addCriterion("matnr is null");
            return (Criteria) this;
        }

        public Criteria andMatnrIsNotNull() {
            addCriterion("matnr is not null");
            return (Criteria) this;
        }

        public Criteria andMatnrEqualTo(String value) {
            addCriterion("matnr =", value, "matnr");
            return (Criteria) this;
        }

        public Criteria andMatnrNotEqualTo(String value) {
            addCriterion("matnr <>", value, "matnr");
            return (Criteria) this;
        }

        public Criteria andMatnrGreaterThan(String value) {
            addCriterion("matnr >", value, "matnr");
            return (Criteria) this;
        }

        public Criteria andMatnrGreaterThanOrEqualTo(String value) {
            addCriterion("matnr >=", value, "matnr");
            return (Criteria) this;
        }

        public Criteria andMatnrLessThan(String value) {
            addCriterion("matnr <", value, "matnr");
            return (Criteria) this;
        }

        public Criteria andMatnrLessThanOrEqualTo(String value) {
            addCriterion("matnr <=", value, "matnr");
            return (Criteria) this;
        }

        public Criteria andMatnrLike(String value) {
            addCriterion("matnr like", value, "matnr");
            return (Criteria) this;
        }

        public Criteria andMatnrNotLike(String value) {
            addCriterion("matnr not like", value, "matnr");
            return (Criteria) this;
        }

        public Criteria andMatnrIn(List<String> values) {
            addCriterion("matnr in", values, "matnr");
            return (Criteria) this;
        }

        public Criteria andMatnrNotIn(List<String> values) {
            addCriterion("matnr not in", values, "matnr");
            return (Criteria) this;
        }

        public Criteria andMatnrBetween(String value1, String value2) {
            addCriterion("matnr between", value1, value2, "matnr");
            return (Criteria) this;
        }

        public Criteria andMatnrNotBetween(String value1, String value2) {
            addCriterion("matnr not between", value1, value2, "matnr");
            return (Criteria) this;
        }

        public Criteria andAvgQtyIsNull() {
            addCriterion("avg_qty is null");
            return (Criteria) this;
        }

        public Criteria andAvgQtyIsNotNull() {
            addCriterion("avg_qty is not null");
            return (Criteria) this;
        }

        public Criteria andAvgQtyEqualTo(String value) {
            addCriterion("avg_qty =", value, "avgQty");
            return (Criteria) this;
        }

        public Criteria andAvgQtyNotEqualTo(String value) {
            addCriterion("avg_qty <>", value, "avgQty");
            return (Criteria) this;
        }

        public Criteria andAvgQtyGreaterThan(String value) {
            addCriterion("avg_qty >", value, "avgQty");
            return (Criteria) this;
        }

        public Criteria andAvgQtyGreaterThanOrEqualTo(String value) {
            addCriterion("avg_qty >=", value, "avgQty");
            return (Criteria) this;
        }

        public Criteria andAvgQtyLessThan(String value) {
            addCriterion("avg_qty <", value, "avgQty");
            return (Criteria) this;
        }

        public Criteria andAvgQtyLessThanOrEqualTo(String value) {
            addCriterion("avg_qty <=", value, "avgQty");
            return (Criteria) this;
        }

        public Criteria andAvgQtyLike(String value) {
            addCriterion("avg_qty like", value, "avgQty");
            return (Criteria) this;
        }

        public Criteria andAvgQtyNotLike(String value) {
            addCriterion("avg_qty not like", value, "avgQty");
            return (Criteria) this;
        }

        public Criteria andAvgQtyIn(List<String> values) {
            addCriterion("avg_qty in", values, "avgQty");
            return (Criteria) this;
        }

        public Criteria andAvgQtyNotIn(List<String> values) {
            addCriterion("avg_qty not in", values, "avgQty");
            return (Criteria) this;
        }

        public Criteria andAvgQtyBetween(String value1, String value2) {
            addCriterion("avg_qty between", value1, value2, "avgQty");
            return (Criteria) this;
        }

        public Criteria andAvgQtyNotBetween(String value1, String value2) {
            addCriterion("avg_qty not between", value1, value2, "avgQty");
            return (Criteria) this;
        }

        public Criteria andInactiveDcStockIsNull() {
            addCriterion("inactive_dc_stock is null");
            return (Criteria) this;
        }

        public Criteria andInactiveDcStockIsNotNull() {
            addCriterion("inactive_dc_stock is not null");
            return (Criteria) this;
        }

        public Criteria andInactiveDcStockEqualTo(BigDecimal value) {
            addCriterion("inactive_dc_stock =", value, "inactiveDcStock");
            return (Criteria) this;
        }

        public Criteria andInactiveDcStockNotEqualTo(BigDecimal value) {
            addCriterion("inactive_dc_stock <>", value, "inactiveDcStock");
            return (Criteria) this;
        }

        public Criteria andInactiveDcStockGreaterThan(BigDecimal value) {
            addCriterion("inactive_dc_stock >", value, "inactiveDcStock");
            return (Criteria) this;
        }

        public Criteria andInactiveDcStockGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("inactive_dc_stock >=", value, "inactiveDcStock");
            return (Criteria) this;
        }

        public Criteria andInactiveDcStockLessThan(BigDecimal value) {
            addCriterion("inactive_dc_stock <", value, "inactiveDcStock");
            return (Criteria) this;
        }

        public Criteria andInactiveDcStockLessThanOrEqualTo(BigDecimal value) {
            addCriterion("inactive_dc_stock <=", value, "inactiveDcStock");
            return (Criteria) this;
        }

        public Criteria andInactiveDcStockIn(List<BigDecimal> values) {
            addCriterion("inactive_dc_stock in", values, "inactiveDcStock");
            return (Criteria) this;
        }

        public Criteria andInactiveDcStockNotIn(List<BigDecimal> values) {
            addCriterion("inactive_dc_stock not in", values, "inactiveDcStock");
            return (Criteria) this;
        }

        public Criteria andInactiveDcStockBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("inactive_dc_stock between", value1, value2, "inactiveDcStock");
            return (Criteria) this;
        }

        public Criteria andInactiveDcStockNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("inactive_dc_stock not between", value1, value2, "inactiveDcStock");
            return (Criteria) this;
        }

        public Criteria andInactiveStoreStockIsNull() {
            addCriterion("inactive_store_stock is null");
            return (Criteria) this;
        }

        public Criteria andInactiveStoreStockIsNotNull() {
            addCriterion("inactive_store_stock is not null");
            return (Criteria) this;
        }

        public Criteria andInactiveStoreStockEqualTo(BigDecimal value) {
            addCriterion("inactive_store_stock =", value, "inactiveStoreStock");
            return (Criteria) this;
        }

        public Criteria andInactiveStoreStockNotEqualTo(BigDecimal value) {
            addCriterion("inactive_store_stock <>", value, "inactiveStoreStock");
            return (Criteria) this;
        }

        public Criteria andInactiveStoreStockGreaterThan(BigDecimal value) {
            addCriterion("inactive_store_stock >", value, "inactiveStoreStock");
            return (Criteria) this;
        }

        public Criteria andInactiveStoreStockGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("inactive_store_stock >=", value, "inactiveStoreStock");
            return (Criteria) this;
        }

        public Criteria andInactiveStoreStockLessThan(BigDecimal value) {
            addCriterion("inactive_store_stock <", value, "inactiveStoreStock");
            return (Criteria) this;
        }

        public Criteria andInactiveStoreStockLessThanOrEqualTo(BigDecimal value) {
            addCriterion("inactive_store_stock <=", value, "inactiveStoreStock");
            return (Criteria) this;
        }

        public Criteria andInactiveStoreStockIn(List<BigDecimal> values) {
            addCriterion("inactive_store_stock in", values, "inactiveStoreStock");
            return (Criteria) this;
        }

        public Criteria andInactiveStoreStockNotIn(List<BigDecimal> values) {
            addCriterion("inactive_store_stock not in", values, "inactiveStoreStock");
            return (Criteria) this;
        }

        public Criteria andInactiveStoreStockBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("inactive_store_stock between", value1, value2, "inactiveStoreStock");
            return (Criteria) this;
        }

        public Criteria andInactiveStoreStockNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("inactive_store_stock not between", value1, value2, "inactiveStoreStock");
            return (Criteria) this;
        }

        public Criteria andReviseTotalBlockStock2IsNull() {
            addCriterion("revise_total_block_stock2 is null");
            return (Criteria) this;
        }

        public Criteria andReviseTotalBlockStock2IsNotNull() {
            addCriterion("revise_total_block_stock2 is not null");
            return (Criteria) this;
        }

        public Criteria andReviseTotalBlockStock2EqualTo(BigDecimal value) {
            addCriterion("revise_total_block_stock2 =", value, "reviseTotalBlockStock2");
            return (Criteria) this;
        }

        public Criteria andReviseTotalBlockStock2NotEqualTo(BigDecimal value) {
            addCriterion("revise_total_block_stock2 <>", value, "reviseTotalBlockStock2");
            return (Criteria) this;
        }

        public Criteria andReviseTotalBlockStock2GreaterThan(BigDecimal value) {
            addCriterion("revise_total_block_stock2 >", value, "reviseTotalBlockStock2");
            return (Criteria) this;
        }

        public Criteria andReviseTotalBlockStock2GreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("revise_total_block_stock2 >=", value, "reviseTotalBlockStock2");
            return (Criteria) this;
        }

        public Criteria andReviseTotalBlockStock2LessThan(BigDecimal value) {
            addCriterion("revise_total_block_stock2 <", value, "reviseTotalBlockStock2");
            return (Criteria) this;
        }

        public Criteria andReviseTotalBlockStock2LessThanOrEqualTo(BigDecimal value) {
            addCriterion("revise_total_block_stock2 <=", value, "reviseTotalBlockStock2");
            return (Criteria) this;
        }

        public Criteria andReviseTotalBlockStock2In(List<BigDecimal> values) {
            addCriterion("revise_total_block_stock2 in", values, "reviseTotalBlockStock2");
            return (Criteria) this;
        }

        public Criteria andReviseTotalBlockStock2NotIn(List<BigDecimal> values) {
            addCriterion("revise_total_block_stock2 not in", values, "reviseTotalBlockStock2");
            return (Criteria) this;
        }

        public Criteria andReviseTotalBlockStock2Between(BigDecimal value1, BigDecimal value2) {
            addCriterion("revise_total_block_stock2 between", value1, value2, "reviseTotalBlockStock2");
            return (Criteria) this;
        }

        public Criteria andReviseTotalBlockStock2NotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("revise_total_block_stock2 not between", value1, value2, "reviseTotalBlockStock2");
            return (Criteria) this;
        }

        public Criteria andWeiqingcaigou7IsNull() {
            addCriterion("weiqingcaigou_7 is null");
            return (Criteria) this;
        }

        public Criteria andWeiqingcaigou7IsNotNull() {
            addCriterion("weiqingcaigou_7 is not null");
            return (Criteria) this;
        }

        public Criteria andWeiqingcaigou7EqualTo(BigDecimal value) {
            addCriterion("weiqingcaigou_7 =", value, "weiqingcaigou7");
            return (Criteria) this;
        }

        public Criteria andWeiqingcaigou7NotEqualTo(BigDecimal value) {
            addCriterion("weiqingcaigou_7 <>", value, "weiqingcaigou7");
            return (Criteria) this;
        }

        public Criteria andWeiqingcaigou7GreaterThan(BigDecimal value) {
            addCriterion("weiqingcaigou_7 >", value, "weiqingcaigou7");
            return (Criteria) this;
        }

        public Criteria andWeiqingcaigou7GreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("weiqingcaigou_7 >=", value, "weiqingcaigou7");
            return (Criteria) this;
        }

        public Criteria andWeiqingcaigou7LessThan(BigDecimal value) {
            addCriterion("weiqingcaigou_7 <", value, "weiqingcaigou7");
            return (Criteria) this;
        }

        public Criteria andWeiqingcaigou7LessThanOrEqualTo(BigDecimal value) {
            addCriterion("weiqingcaigou_7 <=", value, "weiqingcaigou7");
            return (Criteria) this;
        }

        public Criteria andWeiqingcaigou7In(List<BigDecimal> values) {
            addCriterion("weiqingcaigou_7 in", values, "weiqingcaigou7");
            return (Criteria) this;
        }

        public Criteria andWeiqingcaigou7NotIn(List<BigDecimal> values) {
            addCriterion("weiqingcaigou_7 not in", values, "weiqingcaigou7");
            return (Criteria) this;
        }

        public Criteria andWeiqingcaigou7Between(BigDecimal value1, BigDecimal value2) {
            addCriterion("weiqingcaigou_7 between", value1, value2, "weiqingcaigou7");
            return (Criteria) this;
        }

        public Criteria andWeiqingcaigou7NotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("weiqingcaigou_7 not between", value1, value2, "weiqingcaigou7");
            return (Criteria) this;
        }

        public Criteria andInvUpperIsNull() {
            addCriterion("inv_upper is null");
            return (Criteria) this;
        }

        public Criteria andInvUpperIsNotNull() {
            addCriterion("inv_upper is not null");
            return (Criteria) this;
        }

        public Criteria andInvUpperEqualTo(BigDecimal value) {
            addCriterion("inv_upper =", value, "invUpper");
            return (Criteria) this;
        }

        public Criteria andInvUpperNotEqualTo(BigDecimal value) {
            addCriterion("inv_upper <>", value, "invUpper");
            return (Criteria) this;
        }

        public Criteria andInvUpperGreaterThan(BigDecimal value) {
            addCriterion("inv_upper >", value, "invUpper");
            return (Criteria) this;
        }

        public Criteria andInvUpperGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("inv_upper >=", value, "invUpper");
            return (Criteria) this;
        }

        public Criteria andInvUpperLessThan(BigDecimal value) {
            addCriterion("inv_upper <", value, "invUpper");
            return (Criteria) this;
        }

        public Criteria andInvUpperLessThanOrEqualTo(BigDecimal value) {
            addCriterion("inv_upper <=", value, "invUpper");
            return (Criteria) this;
        }

        public Criteria andInvUpperIn(List<BigDecimal> values) {
            addCriterion("inv_upper in", values, "invUpper");
            return (Criteria) this;
        }

        public Criteria andInvUpperNotIn(List<BigDecimal> values) {
            addCriterion("inv_upper not in", values, "invUpper");
            return (Criteria) this;
        }

        public Criteria andInvUpperBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("inv_upper between", value1, value2, "invUpper");
            return (Criteria) this;
        }

        public Criteria andInvUpperNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("inv_upper not between", value1, value2, "invUpper");
            return (Criteria) this;
        }

        public Criteria andSuggestDhlIsNull() {
            addCriterion("suggest_dhl is null");
            return (Criteria) this;
        }

        public Criteria andSuggestDhlIsNotNull() {
            addCriterion("suggest_dhl is not null");
            return (Criteria) this;
        }

        public Criteria andSuggestDhlEqualTo(BigDecimal value) {
            addCriterion("suggest_dhl =", value, "suggestDhl");
            return (Criteria) this;
        }

        public Criteria andSuggestDhlNotEqualTo(BigDecimal value) {
            addCriterion("suggest_dhl <>", value, "suggestDhl");
            return (Criteria) this;
        }

        public Criteria andSuggestDhlGreaterThan(BigDecimal value) {
            addCriterion("suggest_dhl >", value, "suggestDhl");
            return (Criteria) this;
        }

        public Criteria andSuggestDhlGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("suggest_dhl >=", value, "suggestDhl");
            return (Criteria) this;
        }

        public Criteria andSuggestDhlLessThan(BigDecimal value) {
            addCriterion("suggest_dhl <", value, "suggestDhl");
            return (Criteria) this;
        }

        public Criteria andSuggestDhlLessThanOrEqualTo(BigDecimal value) {
            addCriterion("suggest_dhl <=", value, "suggestDhl");
            return (Criteria) this;
        }

        public Criteria andSuggestDhlIn(List<BigDecimal> values) {
            addCriterion("suggest_dhl in", values, "suggestDhl");
            return (Criteria) this;
        }

        public Criteria andSuggestDhlNotIn(List<BigDecimal> values) {
            addCriterion("suggest_dhl not in", values, "suggestDhl");
            return (Criteria) this;
        }

        public Criteria andSuggestDhlBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("suggest_dhl between", value1, value2, "suggestDhl");
            return (Criteria) this;
        }

        public Criteria andSuggestDhlNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("suggest_dhl not between", value1, value2, "suggestDhl");
            return (Criteria) this;
        }

        public Criteria andStoreCntsInStockIsNull() {
            addCriterion("store_cnts_in_stock is null");
            return (Criteria) this;
        }

        public Criteria andStoreCntsInStockIsNotNull() {
            addCriterion("store_cnts_in_stock is not null");
            return (Criteria) this;
        }

        public Criteria andStoreCntsInStockEqualTo(BigDecimal value) {
            addCriterion("store_cnts_in_stock =", value, "storeCntsInStock");
            return (Criteria) this;
        }

        public Criteria andStoreCntsInStockNotEqualTo(BigDecimal value) {
            addCriterion("store_cnts_in_stock <>", value, "storeCntsInStock");
            return (Criteria) this;
        }

        public Criteria andStoreCntsInStockGreaterThan(BigDecimal value) {
            addCriterion("store_cnts_in_stock >", value, "storeCntsInStock");
            return (Criteria) this;
        }

        public Criteria andStoreCntsInStockGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("store_cnts_in_stock >=", value, "storeCntsInStock");
            return (Criteria) this;
        }

        public Criteria andStoreCntsInStockLessThan(BigDecimal value) {
            addCriterion("store_cnts_in_stock <", value, "storeCntsInStock");
            return (Criteria) this;
        }

        public Criteria andStoreCntsInStockLessThanOrEqualTo(BigDecimal value) {
            addCriterion("store_cnts_in_stock <=", value, "storeCntsInStock");
            return (Criteria) this;
        }

        public Criteria andStoreCntsInStockIn(List<BigDecimal> values) {
            addCriterion("store_cnts_in_stock in", values, "storeCntsInStock");
            return (Criteria) this;
        }

        public Criteria andStoreCntsInStockNotIn(List<BigDecimal> values) {
            addCriterion("store_cnts_in_stock not in", values, "storeCntsInStock");
            return (Criteria) this;
        }

        public Criteria andStoreCntsInStockBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("store_cnts_in_stock between", value1, value2, "storeCntsInStock");
            return (Criteria) this;
        }

        public Criteria andStoreCntsInStockNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("store_cnts_in_stock not between", value1, value2, "storeCntsInStock");
            return (Criteria) this;
        }

        public Criteria andTotalApplyqtyIsNull() {
            addCriterion("total_applyqty is null");
            return (Criteria) this;
        }

        public Criteria andTotalApplyqtyIsNotNull() {
            addCriterion("total_applyqty is not null");
            return (Criteria) this;
        }

        public Criteria andTotalApplyqtyEqualTo(BigDecimal value) {
            addCriterion("total_applyqty =", value, "totalApplyqty");
            return (Criteria) this;
        }

        public Criteria andTotalApplyqtyNotEqualTo(BigDecimal value) {
            addCriterion("total_applyqty <>", value, "totalApplyqty");
            return (Criteria) this;
        }

        public Criteria andTotalApplyqtyGreaterThan(BigDecimal value) {
            addCriterion("total_applyqty >", value, "totalApplyqty");
            return (Criteria) this;
        }

        public Criteria andTotalApplyqtyGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("total_applyqty >=", value, "totalApplyqty");
            return (Criteria) this;
        }

        public Criteria andTotalApplyqtyLessThan(BigDecimal value) {
            addCriterion("total_applyqty <", value, "totalApplyqty");
            return (Criteria) this;
        }

        public Criteria andTotalApplyqtyLessThanOrEqualTo(BigDecimal value) {
            addCriterion("total_applyqty <=", value, "totalApplyqty");
            return (Criteria) this;
        }

        public Criteria andTotalApplyqtyIn(List<BigDecimal> values) {
            addCriterion("total_applyqty in", values, "totalApplyqty");
            return (Criteria) this;
        }

        public Criteria andTotalApplyqtyNotIn(List<BigDecimal> values) {
            addCriterion("total_applyqty not in", values, "totalApplyqty");
            return (Criteria) this;
        }

        public Criteria andTotalApplyqtyBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_applyqty between", value1, value2, "totalApplyqty");
            return (Criteria) this;
        }

        public Criteria andTotalApplyqtyNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_applyqty not between", value1, value2, "totalApplyqty");
            return (Criteria) this;
        }

        public Criteria andMaxApplyqtyIsNull() {
            addCriterion("max_applyqty is null");
            return (Criteria) this;
        }

        public Criteria andMaxApplyqtyIsNotNull() {
            addCriterion("max_applyqty is not null");
            return (Criteria) this;
        }

        public Criteria andMaxApplyqtyEqualTo(BigDecimal value) {
            addCriterion("max_applyqty =", value, "maxApplyqty");
            return (Criteria) this;
        }

        public Criteria andMaxApplyqtyNotEqualTo(BigDecimal value) {
            addCriterion("max_applyqty <>", value, "maxApplyqty");
            return (Criteria) this;
        }

        public Criteria andMaxApplyqtyGreaterThan(BigDecimal value) {
            addCriterion("max_applyqty >", value, "maxApplyqty");
            return (Criteria) this;
        }

        public Criteria andMaxApplyqtyGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("max_applyqty >=", value, "maxApplyqty");
            return (Criteria) this;
        }

        public Criteria andMaxApplyqtyLessThan(BigDecimal value) {
            addCriterion("max_applyqty <", value, "maxApplyqty");
            return (Criteria) this;
        }

        public Criteria andMaxApplyqtyLessThanOrEqualTo(BigDecimal value) {
            addCriterion("max_applyqty <=", value, "maxApplyqty");
            return (Criteria) this;
        }

        public Criteria andMaxApplyqtyIn(List<BigDecimal> values) {
            addCriterion("max_applyqty in", values, "maxApplyqty");
            return (Criteria) this;
        }

        public Criteria andMaxApplyqtyNotIn(List<BigDecimal> values) {
            addCriterion("max_applyqty not in", values, "maxApplyqty");
            return (Criteria) this;
        }

        public Criteria andMaxApplyqtyBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("max_applyqty between", value1, value2, "maxApplyqty");
            return (Criteria) this;
        }

        public Criteria andMaxApplyqtyNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("max_applyqty not between", value1, value2, "maxApplyqty");
            return (Criteria) this;
        }

        public Criteria andMidqtyIsNull() {
            addCriterion("midqty is null");
            return (Criteria) this;
        }

        public Criteria andMidqtyIsNotNull() {
            addCriterion("midqty is not null");
            return (Criteria) this;
        }

        public Criteria andMidqtyEqualTo(BigDecimal value) {
            addCriterion("midqty =", value, "midqty");
            return (Criteria) this;
        }

        public Criteria andMidqtyNotEqualTo(BigDecimal value) {
            addCriterion("midqty <>", value, "midqty");
            return (Criteria) this;
        }

        public Criteria andMidqtyGreaterThan(BigDecimal value) {
            addCriterion("midqty >", value, "midqty");
            return (Criteria) this;
        }

        public Criteria andMidqtyGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("midqty >=", value, "midqty");
            return (Criteria) this;
        }

        public Criteria andMidqtyLessThan(BigDecimal value) {
            addCriterion("midqty <", value, "midqty");
            return (Criteria) this;
        }

        public Criteria andMidqtyLessThanOrEqualTo(BigDecimal value) {
            addCriterion("midqty <=", value, "midqty");
            return (Criteria) this;
        }

        public Criteria andMidqtyIn(List<BigDecimal> values) {
            addCriterion("midqty in", values, "midqty");
            return (Criteria) this;
        }

        public Criteria andMidqtyNotIn(List<BigDecimal> values) {
            addCriterion("midqty not in", values, "midqty");
            return (Criteria) this;
        }

        public Criteria andMidqtyBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("midqty between", value1, value2, "midqty");
            return (Criteria) this;
        }

        public Criteria andMidqtyNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("midqty not between", value1, value2, "midqty");
            return (Criteria) this;
        }

        public Criteria andMaxqtyIsNull() {
            addCriterion("maxqty is null");
            return (Criteria) this;
        }

        public Criteria andMaxqtyIsNotNull() {
            addCriterion("maxqty is not null");
            return (Criteria) this;
        }

        public Criteria andMaxqtyEqualTo(BigDecimal value) {
            addCriterion("maxqty =", value, "maxqty");
            return (Criteria) this;
        }

        public Criteria andMaxqtyNotEqualTo(BigDecimal value) {
            addCriterion("maxqty <>", value, "maxqty");
            return (Criteria) this;
        }

        public Criteria andMaxqtyGreaterThan(BigDecimal value) {
            addCriterion("maxqty >", value, "maxqty");
            return (Criteria) this;
        }

        public Criteria andMaxqtyGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("maxqty >=", value, "maxqty");
            return (Criteria) this;
        }

        public Criteria andMaxqtyLessThan(BigDecimal value) {
            addCriterion("maxqty <", value, "maxqty");
            return (Criteria) this;
        }

        public Criteria andMaxqtyLessThanOrEqualTo(BigDecimal value) {
            addCriterion("maxqty <=", value, "maxqty");
            return (Criteria) this;
        }

        public Criteria andMaxqtyIn(List<BigDecimal> values) {
            addCriterion("maxqty in", values, "maxqty");
            return (Criteria) this;
        }

        public Criteria andMaxqtyNotIn(List<BigDecimal> values) {
            addCriterion("maxqty not in", values, "maxqty");
            return (Criteria) this;
        }

        public Criteria andMaxqtyBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("maxqty between", value1, value2, "maxqty");
            return (Criteria) this;
        }

        public Criteria andMaxqtyNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("maxqty not between", value1, value2, "maxqty");
            return (Criteria) this;
        }

        public Criteria andStockToUseRatioIsNull() {
            addCriterion("stock_to_use_ratio is null");
            return (Criteria) this;
        }

        public Criteria andStockToUseRatioIsNotNull() {
            addCriterion("stock_to_use_ratio is not null");
            return (Criteria) this;
        }

        public Criteria andStockToUseRatioEqualTo(String value) {
            addCriterion("stock_to_use_ratio =", value, "stockToUseRatio");
            return (Criteria) this;
        }

        public Criteria andStockToUseRatioNotEqualTo(String value) {
            addCriterion("stock_to_use_ratio <>", value, "stockToUseRatio");
            return (Criteria) this;
        }

        public Criteria andStockToUseRatioGreaterThan(String value) {
            addCriterion("stock_to_use_ratio >", value, "stockToUseRatio");
            return (Criteria) this;
        }

        public Criteria andStockToUseRatioGreaterThanOrEqualTo(String value) {
            addCriterion("stock_to_use_ratio >=", value, "stockToUseRatio");
            return (Criteria) this;
        }

        public Criteria andStockToUseRatioLessThan(String value) {
            addCriterion("stock_to_use_ratio <", value, "stockToUseRatio");
            return (Criteria) this;
        }

        public Criteria andStockToUseRatioLessThanOrEqualTo(String value) {
            addCriterion("stock_to_use_ratio <=", value, "stockToUseRatio");
            return (Criteria) this;
        }

        public Criteria andStockToUseRatioLike(String value) {
            addCriterion("stock_to_use_ratio like", value, "stockToUseRatio");
            return (Criteria) this;
        }

        public Criteria andStockToUseRatioNotLike(String value) {
            addCriterion("stock_to_use_ratio not like", value, "stockToUseRatio");
            return (Criteria) this;
        }

        public Criteria andStockToUseRatioIn(List<String> values) {
            addCriterion("stock_to_use_ratio in", values, "stockToUseRatio");
            return (Criteria) this;
        }

        public Criteria andStockToUseRatioNotIn(List<String> values) {
            addCriterion("stock_to_use_ratio not in", values, "stockToUseRatio");
            return (Criteria) this;
        }

        public Criteria andStockToUseRatioBetween(String value1, String value2) {
            addCriterion("stock_to_use_ratio between", value1, value2, "stockToUseRatio");
            return (Criteria) this;
        }

        public Criteria andStockToUseRatioNotBetween(String value1, String value2) {
            addCriterion("stock_to_use_ratio not between", value1, value2, "stockToUseRatio");
            return (Criteria) this;
        }

        public Criteria andLifnrIsNull() {
            addCriterion("lifnr is null");
            return (Criteria) this;
        }

        public Criteria andLifnrIsNotNull() {
            addCriterion("lifnr is not null");
            return (Criteria) this;
        }

        public Criteria andLifnrEqualTo(String value) {
            addCriterion("lifnr =", value, "lifnr");
            return (Criteria) this;
        }

        public Criteria andLifnrNotEqualTo(String value) {
            addCriterion("lifnr <>", value, "lifnr");
            return (Criteria) this;
        }

        public Criteria andLifnrGreaterThan(String value) {
            addCriterion("lifnr >", value, "lifnr");
            return (Criteria) this;
        }

        public Criteria andLifnrGreaterThanOrEqualTo(String value) {
            addCriterion("lifnr >=", value, "lifnr");
            return (Criteria) this;
        }

        public Criteria andLifnrLessThan(String value) {
            addCriterion("lifnr <", value, "lifnr");
            return (Criteria) this;
        }

        public Criteria andLifnrLessThanOrEqualTo(String value) {
            addCriterion("lifnr <=", value, "lifnr");
            return (Criteria) this;
        }

        public Criteria andLifnrLike(String value) {
            addCriterion("lifnr like", value, "lifnr");
            return (Criteria) this;
        }

        public Criteria andLifnrNotLike(String value) {
            addCriterion("lifnr not like", value, "lifnr");
            return (Criteria) this;
        }

        public Criteria andLifnrIn(List<String> values) {
            addCriterion("lifnr in", values, "lifnr");
            return (Criteria) this;
        }

        public Criteria andLifnrNotIn(List<String> values) {
            addCriterion("lifnr not in", values, "lifnr");
            return (Criteria) this;
        }

        public Criteria andLifnrBetween(String value1, String value2) {
            addCriterion("lifnr between", value1, value2, "lifnr");
            return (Criteria) this;
        }

        public Criteria andLifnrNotBetween(String value1, String value2) {
            addCriterion("lifnr not between", value1, value2, "lifnr");
            return (Criteria) this;
        }

        public Criteria andDhdBIsNull() {
            addCriterion("dhd_b is null");
            return (Criteria) this;
        }

        public Criteria andDhdBIsNotNull() {
            addCriterion("dhd_b is not null");
            return (Criteria) this;
        }

        public Criteria andDhdBEqualTo(BigDecimal value) {
            addCriterion("dhd_b =", value, "dhdB");
            return (Criteria) this;
        }

        public Criteria andDhdBNotEqualTo(BigDecimal value) {
            addCriterion("dhd_b <>", value, "dhdB");
            return (Criteria) this;
        }

        public Criteria andDhdBGreaterThan(BigDecimal value) {
            addCriterion("dhd_b >", value, "dhdB");
            return (Criteria) this;
        }

        public Criteria andDhdBGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("dhd_b >=", value, "dhdB");
            return (Criteria) this;
        }

        public Criteria andDhdBLessThan(BigDecimal value) {
            addCriterion("dhd_b <", value, "dhdB");
            return (Criteria) this;
        }

        public Criteria andDhdBLessThanOrEqualTo(BigDecimal value) {
            addCriterion("dhd_b <=", value, "dhdB");
            return (Criteria) this;
        }

        public Criteria andDhdBIn(List<BigDecimal> values) {
            addCriterion("dhd_b in", values, "dhdB");
            return (Criteria) this;
        }

        public Criteria andDhdBNotIn(List<BigDecimal> values) {
            addCriterion("dhd_b not in", values, "dhdB");
            return (Criteria) this;
        }

        public Criteria andDhdBBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("dhd_b between", value1, value2, "dhdB");
            return (Criteria) this;
        }

        public Criteria andDhdBNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("dhd_b not between", value1, value2, "dhdB");
            return (Criteria) this;
        }

        public Criteria andDcSIsNull() {
            addCriterion("dc_s is null");
            return (Criteria) this;
        }

        public Criteria andDcSIsNotNull() {
            addCriterion("dc_s is not null");
            return (Criteria) this;
        }

        public Criteria andDcSEqualTo(BigDecimal value) {
            addCriterion("dc_s =", value, "dcS");
            return (Criteria) this;
        }

        public Criteria andDcSNotEqualTo(BigDecimal value) {
            addCriterion("dc_s <>", value, "dcS");
            return (Criteria) this;
        }

        public Criteria andDcSGreaterThan(BigDecimal value) {
            addCriterion("dc_s >", value, "dcS");
            return (Criteria) this;
        }

        public Criteria andDcSGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("dc_s >=", value, "dcS");
            return (Criteria) this;
        }

        public Criteria andDcSLessThan(BigDecimal value) {
            addCriterion("dc_s <", value, "dcS");
            return (Criteria) this;
        }

        public Criteria andDcSLessThanOrEqualTo(BigDecimal value) {
            addCriterion("dc_s <=", value, "dcS");
            return (Criteria) this;
        }

        public Criteria andDcSIn(List<BigDecimal> values) {
            addCriterion("dc_s in", values, "dcS");
            return (Criteria) this;
        }

        public Criteria andDcSNotIn(List<BigDecimal> values) {
            addCriterion("dc_s not in", values, "dcS");
            return (Criteria) this;
        }

        public Criteria andDcSBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("dc_s between", value1, value2, "dcS");
            return (Criteria) this;
        }

        public Criteria andDcSNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("dc_s not between", value1, value2, "dcS");
            return (Criteria) this;
        }

        public Criteria andDcLIsNull() {
            addCriterion("dc_l is null");
            return (Criteria) this;
        }

        public Criteria andDcLIsNotNull() {
            addCriterion("dc_l is not null");
            return (Criteria) this;
        }

        public Criteria andDcLEqualTo(BigDecimal value) {
            addCriterion("dc_l =", value, "dcL");
            return (Criteria) this;
        }

        public Criteria andDcLNotEqualTo(BigDecimal value) {
            addCriterion("dc_l <>", value, "dcL");
            return (Criteria) this;
        }

        public Criteria andDcLGreaterThan(BigDecimal value) {
            addCriterion("dc_l >", value, "dcL");
            return (Criteria) this;
        }

        public Criteria andDcLGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("dc_l >=", value, "dcL");
            return (Criteria) this;
        }

        public Criteria andDcLLessThan(BigDecimal value) {
            addCriterion("dc_l <", value, "dcL");
            return (Criteria) this;
        }

        public Criteria andDcLLessThanOrEqualTo(BigDecimal value) {
            addCriterion("dc_l <=", value, "dcL");
            return (Criteria) this;
        }

        public Criteria andDcLIn(List<BigDecimal> values) {
            addCriterion("dc_l in", values, "dcL");
            return (Criteria) this;
        }

        public Criteria andDcLNotIn(List<BigDecimal> values) {
            addCriterion("dc_l not in", values, "dcL");
            return (Criteria) this;
        }

        public Criteria andDcLBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("dc_l between", value1, value2, "dcL");
            return (Criteria) this;
        }

        public Criteria andDcLNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("dc_l not between", value1, value2, "dcL");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelIsNull() {
            addCriterion("goods_level is null");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelIsNotNull() {
            addCriterion("goods_level is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelEqualTo(String value) {
            addCriterion("goods_level =", value, "goodsLevel");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelNotEqualTo(String value) {
            addCriterion("goods_level <>", value, "goodsLevel");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelGreaterThan(String value) {
            addCriterion("goods_level >", value, "goodsLevel");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelGreaterThanOrEqualTo(String value) {
            addCriterion("goods_level >=", value, "goodsLevel");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelLessThan(String value) {
            addCriterion("goods_level <", value, "goodsLevel");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelLessThanOrEqualTo(String value) {
            addCriterion("goods_level <=", value, "goodsLevel");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelLike(String value) {
            addCriterion("goods_level like", value, "goodsLevel");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelNotLike(String value) {
            addCriterion("goods_level not like", value, "goodsLevel");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelIn(List<String> values) {
            addCriterion("goods_level in", values, "goodsLevel");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelNotIn(List<String> values) {
            addCriterion("goods_level not in", values, "goodsLevel");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelBetween(String value1, String value2) {
            addCriterion("goods_level between", value1, value2, "goodsLevel");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelNotBetween(String value1, String value2) {
            addCriterion("goods_level not between", value1, value2, "goodsLevel");
            return (Criteria) this;
        }

        public Criteria andSafetyStockIsNull() {
            addCriterion("safety_stock is null");
            return (Criteria) this;
        }

        public Criteria andSafetyStockIsNotNull() {
            addCriterion("safety_stock is not null");
            return (Criteria) this;
        }

        public Criteria andSafetyStockEqualTo(BigDecimal value) {
            addCriterion("safety_stock =", value, "safetyStock");
            return (Criteria) this;
        }

        public Criteria andSafetyStockNotEqualTo(BigDecimal value) {
            addCriterion("safety_stock <>", value, "safetyStock");
            return (Criteria) this;
        }

        public Criteria andSafetyStockGreaterThan(BigDecimal value) {
            addCriterion("safety_stock >", value, "safetyStock");
            return (Criteria) this;
        }

        public Criteria andSafetyStockGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("safety_stock >=", value, "safetyStock");
            return (Criteria) this;
        }

        public Criteria andSafetyStockLessThan(BigDecimal value) {
            addCriterion("safety_stock <", value, "safetyStock");
            return (Criteria) this;
        }

        public Criteria andSafetyStockLessThanOrEqualTo(BigDecimal value) {
            addCriterion("safety_stock <=", value, "safetyStock");
            return (Criteria) this;
        }

        public Criteria andSafetyStockIn(List<BigDecimal> values) {
            addCriterion("safety_stock in", values, "safetyStock");
            return (Criteria) this;
        }

        public Criteria andSafetyStockNotIn(List<BigDecimal> values) {
            addCriterion("safety_stock not in", values, "safetyStock");
            return (Criteria) this;
        }

        public Criteria andSafetyStockBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("safety_stock between", value1, value2, "safetyStock");
            return (Criteria) this;
        }

        public Criteria andSafetyStockNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("safety_stock not between", value1, value2, "safetyStock");
            return (Criteria) this;
        }

        public Criteria andSumRVIsNull() {
            addCriterion("sum_r_v is null");
            return (Criteria) this;
        }

        public Criteria andSumRVIsNotNull() {
            addCriterion("sum_r_v is not null");
            return (Criteria) this;
        }

        public Criteria andSumRVEqualTo(BigDecimal value) {
            addCriterion("sum_r_v =", value, "sumRV");
            return (Criteria) this;
        }

        public Criteria andSumRVNotEqualTo(BigDecimal value) {
            addCriterion("sum_r_v <>", value, "sumRV");
            return (Criteria) this;
        }

        public Criteria andSumRVGreaterThan(BigDecimal value) {
            addCriterion("sum_r_v >", value, "sumRV");
            return (Criteria) this;
        }

        public Criteria andSumRVGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("sum_r_v >=", value, "sumRV");
            return (Criteria) this;
        }

        public Criteria andSumRVLessThan(BigDecimal value) {
            addCriterion("sum_r_v <", value, "sumRV");
            return (Criteria) this;
        }

        public Criteria andSumRVLessThanOrEqualTo(BigDecimal value) {
            addCriterion("sum_r_v <=", value, "sumRV");
            return (Criteria) this;
        }

        public Criteria andSumRVIn(List<BigDecimal> values) {
            addCriterion("sum_r_v in", values, "sumRV");
            return (Criteria) this;
        }

        public Criteria andSumRVNotIn(List<BigDecimal> values) {
            addCriterion("sum_r_v not in", values, "sumRV");
            return (Criteria) this;
        }

        public Criteria andSumRVBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("sum_r_v between", value1, value2, "sumRV");
            return (Criteria) this;
        }

        public Criteria andSumRVNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("sum_r_v not between", value1, value2, "sumRV");
            return (Criteria) this;
        }

        public Criteria andReviseTotalBlockStock1IsNull() {
            addCriterion("revise_total_block_stock1 is null");
            return (Criteria) this;
        }

        public Criteria andReviseTotalBlockStock1IsNotNull() {
            addCriterion("revise_total_block_stock1 is not null");
            return (Criteria) this;
        }

        public Criteria andReviseTotalBlockStock1EqualTo(BigDecimal value) {
            addCriterion("revise_total_block_stock1 =", value, "reviseTotalBlockStock1");
            return (Criteria) this;
        }

        public Criteria andReviseTotalBlockStock1NotEqualTo(BigDecimal value) {
            addCriterion("revise_total_block_stock1 <>", value, "reviseTotalBlockStock1");
            return (Criteria) this;
        }

        public Criteria andReviseTotalBlockStock1GreaterThan(BigDecimal value) {
            addCriterion("revise_total_block_stock1 >", value, "reviseTotalBlockStock1");
            return (Criteria) this;
        }

        public Criteria andReviseTotalBlockStock1GreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("revise_total_block_stock1 >=", value, "reviseTotalBlockStock1");
            return (Criteria) this;
        }

        public Criteria andReviseTotalBlockStock1LessThan(BigDecimal value) {
            addCriterion("revise_total_block_stock1 <", value, "reviseTotalBlockStock1");
            return (Criteria) this;
        }

        public Criteria andReviseTotalBlockStock1LessThanOrEqualTo(BigDecimal value) {
            addCriterion("revise_total_block_stock1 <=", value, "reviseTotalBlockStock1");
            return (Criteria) this;
        }

        public Criteria andReviseTotalBlockStock1In(List<BigDecimal> values) {
            addCriterion("revise_total_block_stock1 in", values, "reviseTotalBlockStock1");
            return (Criteria) this;
        }

        public Criteria andReviseTotalBlockStock1NotIn(List<BigDecimal> values) {
            addCriterion("revise_total_block_stock1 not in", values, "reviseTotalBlockStock1");
            return (Criteria) this;
        }

        public Criteria andReviseTotalBlockStock1Between(BigDecimal value1, BigDecimal value2) {
            addCriterion("revise_total_block_stock1 between", value1, value2, "reviseTotalBlockStock1");
            return (Criteria) this;
        }

        public Criteria andReviseTotalBlockStock1NotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("revise_total_block_stock1 not between", value1, value2, "reviseTotalBlockStock1");
            return (Criteria) this;
        }

        public Criteria andRawSuggestDhlIsNull() {
            addCriterion("raw_suggest_dhl is null");
            return (Criteria) this;
        }

        public Criteria andRawSuggestDhlIsNotNull() {
            addCriterion("raw_suggest_dhl is not null");
            return (Criteria) this;
        }

        public Criteria andRawSuggestDhlEqualTo(BigDecimal value) {
            addCriterion("raw_suggest_dhl =", value, "rawSuggestDhl");
            return (Criteria) this;
        }

        public Criteria andRawSuggestDhlNotEqualTo(BigDecimal value) {
            addCriterion("raw_suggest_dhl <>", value, "rawSuggestDhl");
            return (Criteria) this;
        }

        public Criteria andRawSuggestDhlGreaterThan(BigDecimal value) {
            addCriterion("raw_suggest_dhl >", value, "rawSuggestDhl");
            return (Criteria) this;
        }

        public Criteria andRawSuggestDhlGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("raw_suggest_dhl >=", value, "rawSuggestDhl");
            return (Criteria) this;
        }

        public Criteria andRawSuggestDhlLessThan(BigDecimal value) {
            addCriterion("raw_suggest_dhl <", value, "rawSuggestDhl");
            return (Criteria) this;
        }

        public Criteria andRawSuggestDhlLessThanOrEqualTo(BigDecimal value) {
            addCriterion("raw_suggest_dhl <=", value, "rawSuggestDhl");
            return (Criteria) this;
        }

        public Criteria andRawSuggestDhlIn(List<BigDecimal> values) {
            addCriterion("raw_suggest_dhl in", values, "rawSuggestDhl");
            return (Criteria) this;
        }

        public Criteria andRawSuggestDhlNotIn(List<BigDecimal> values) {
            addCriterion("raw_suggest_dhl not in", values, "rawSuggestDhl");
            return (Criteria) this;
        }

        public Criteria andRawSuggestDhlBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("raw_suggest_dhl between", value1, value2, "rawSuggestDhl");
            return (Criteria) this;
        }

        public Criteria andRawSuggestDhlNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("raw_suggest_dhl not between", value1, value2, "rawSuggestDhl");
            return (Criteria) this;
        }

        public Criteria andZnoncxbIsNull() {
            addCriterion("znoncxb is null");
            return (Criteria) this;
        }

        public Criteria andZnoncxbIsNotNull() {
            addCriterion("znoncxb is not null");
            return (Criteria) this;
        }

        public Criteria andZnoncxbEqualTo(String value) {
            addCriterion("znoncxb =", value, "znoncxb");
            return (Criteria) this;
        }

        public Criteria andZnoncxbNotEqualTo(String value) {
            addCriterion("znoncxb <>", value, "znoncxb");
            return (Criteria) this;
        }

        public Criteria andZnoncxbGreaterThan(String value) {
            addCriterion("znoncxb >", value, "znoncxb");
            return (Criteria) this;
        }

        public Criteria andZnoncxbGreaterThanOrEqualTo(String value) {
            addCriterion("znoncxb >=", value, "znoncxb");
            return (Criteria) this;
        }

        public Criteria andZnoncxbLessThan(String value) {
            addCriterion("znoncxb <", value, "znoncxb");
            return (Criteria) this;
        }

        public Criteria andZnoncxbLessThanOrEqualTo(String value) {
            addCriterion("znoncxb <=", value, "znoncxb");
            return (Criteria) this;
        }

        public Criteria andZnoncxbLike(String value) {
            addCriterion("znoncxb like", value, "znoncxb");
            return (Criteria) this;
        }

        public Criteria andZnoncxbNotLike(String value) {
            addCriterion("znoncxb not like", value, "znoncxb");
            return (Criteria) this;
        }

        public Criteria andZnoncxbIn(List<String> values) {
            addCriterion("znoncxb in", values, "znoncxb");
            return (Criteria) this;
        }

        public Criteria andZnoncxbNotIn(List<String> values) {
            addCriterion("znoncxb not in", values, "znoncxb");
            return (Criteria) this;
        }

        public Criteria andZnoncxbBetween(String value1, String value2) {
            addCriterion("znoncxb between", value1, value2, "znoncxb");
            return (Criteria) this;
        }

        public Criteria andZnoncxbNotBetween(String value1, String value2) {
            addCriterion("znoncxb not between", value1, value2, "znoncxb");
            return (Criteria) this;
        }

        public Criteria andTotalDcStockIsNull() {
            addCriterion("total_dc_stock is null");
            return (Criteria) this;
        }

        public Criteria andTotalDcStockIsNotNull() {
            addCriterion("total_dc_stock is not null");
            return (Criteria) this;
        }

        public Criteria andTotalDcStockEqualTo(String value) {
            addCriterion("total_dc_stock =", value, "totalDcStock");
            return (Criteria) this;
        }

        public Criteria andTotalDcStockNotEqualTo(String value) {
            addCriterion("total_dc_stock <>", value, "totalDcStock");
            return (Criteria) this;
        }

        public Criteria andTotalDcStockGreaterThan(String value) {
            addCriterion("total_dc_stock >", value, "totalDcStock");
            return (Criteria) this;
        }

        public Criteria andTotalDcStockGreaterThanOrEqualTo(String value) {
            addCriterion("total_dc_stock >=", value, "totalDcStock");
            return (Criteria) this;
        }

        public Criteria andTotalDcStockLessThan(String value) {
            addCriterion("total_dc_stock <", value, "totalDcStock");
            return (Criteria) this;
        }

        public Criteria andTotalDcStockLessThanOrEqualTo(String value) {
            addCriterion("total_dc_stock <=", value, "totalDcStock");
            return (Criteria) this;
        }

        public Criteria andTotalDcStockLike(String value) {
            addCriterion("total_dc_stock like", value, "totalDcStock");
            return (Criteria) this;
        }

        public Criteria andTotalDcStockNotLike(String value) {
            addCriterion("total_dc_stock not like", value, "totalDcStock");
            return (Criteria) this;
        }

        public Criteria andTotalDcStockIn(List<String> values) {
            addCriterion("total_dc_stock in", values, "totalDcStock");
            return (Criteria) this;
        }

        public Criteria andTotalDcStockNotIn(List<String> values) {
            addCriterion("total_dc_stock not in", values, "totalDcStock");
            return (Criteria) this;
        }

        public Criteria andTotalDcStockBetween(String value1, String value2) {
            addCriterion("total_dc_stock between", value1, value2, "totalDcStock");
            return (Criteria) this;
        }

        public Criteria andTotalDcStockNotBetween(String value1, String value2) {
            addCriterion("total_dc_stock not between", value1, value2, "totalDcStock");
            return (Criteria) this;
        }

        public Criteria andTotalDcDisableStockIsNull() {
            addCriterion("total_dc_disable_stock is null");
            return (Criteria) this;
        }

        public Criteria andTotalDcDisableStockIsNotNull() {
            addCriterion("total_dc_disable_stock is not null");
            return (Criteria) this;
        }

        public Criteria andTotalDcDisableStockEqualTo(String value) {
            addCriterion("total_dc_disable_stock =", value, "totalDcDisableStock");
            return (Criteria) this;
        }

        public Criteria andTotalDcDisableStockNotEqualTo(String value) {
            addCriterion("total_dc_disable_stock <>", value, "totalDcDisableStock");
            return (Criteria) this;
        }

        public Criteria andTotalDcDisableStockGreaterThan(String value) {
            addCriterion("total_dc_disable_stock >", value, "totalDcDisableStock");
            return (Criteria) this;
        }

        public Criteria andTotalDcDisableStockGreaterThanOrEqualTo(String value) {
            addCriterion("total_dc_disable_stock >=", value, "totalDcDisableStock");
            return (Criteria) this;
        }

        public Criteria andTotalDcDisableStockLessThan(String value) {
            addCriterion("total_dc_disable_stock <", value, "totalDcDisableStock");
            return (Criteria) this;
        }

        public Criteria andTotalDcDisableStockLessThanOrEqualTo(String value) {
            addCriterion("total_dc_disable_stock <=", value, "totalDcDisableStock");
            return (Criteria) this;
        }

        public Criteria andTotalDcDisableStockLike(String value) {
            addCriterion("total_dc_disable_stock like", value, "totalDcDisableStock");
            return (Criteria) this;
        }

        public Criteria andTotalDcDisableStockNotLike(String value) {
            addCriterion("total_dc_disable_stock not like", value, "totalDcDisableStock");
            return (Criteria) this;
        }

        public Criteria andTotalDcDisableStockIn(List<String> values) {
            addCriterion("total_dc_disable_stock in", values, "totalDcDisableStock");
            return (Criteria) this;
        }

        public Criteria andTotalDcDisableStockNotIn(List<String> values) {
            addCriterion("total_dc_disable_stock not in", values, "totalDcDisableStock");
            return (Criteria) this;
        }

        public Criteria andTotalDcDisableStockBetween(String value1, String value2) {
            addCriterion("total_dc_disable_stock between", value1, value2, "totalDcDisableStock");
            return (Criteria) this;
        }

        public Criteria andTotalDcDisableStockNotBetween(String value1, String value2) {
            addCriterion("total_dc_disable_stock not between", value1, value2, "totalDcDisableStock");
            return (Criteria) this;
        }

        public Criteria andTotalStoreStockIsNull() {
            addCriterion("total_store_stock is null");
            return (Criteria) this;
        }

        public Criteria andTotalStoreStockIsNotNull() {
            addCriterion("total_store_stock is not null");
            return (Criteria) this;
        }

        public Criteria andTotalStoreStockEqualTo(String value) {
            addCriterion("total_store_stock =", value, "totalStoreStock");
            return (Criteria) this;
        }

        public Criteria andTotalStoreStockNotEqualTo(String value) {
            addCriterion("total_store_stock <>", value, "totalStoreStock");
            return (Criteria) this;
        }

        public Criteria andTotalStoreStockGreaterThan(String value) {
            addCriterion("total_store_stock >", value, "totalStoreStock");
            return (Criteria) this;
        }

        public Criteria andTotalStoreStockGreaterThanOrEqualTo(String value) {
            addCriterion("total_store_stock >=", value, "totalStoreStock");
            return (Criteria) this;
        }

        public Criteria andTotalStoreStockLessThan(String value) {
            addCriterion("total_store_stock <", value, "totalStoreStock");
            return (Criteria) this;
        }

        public Criteria andTotalStoreStockLessThanOrEqualTo(String value) {
            addCriterion("total_store_stock <=", value, "totalStoreStock");
            return (Criteria) this;
        }

        public Criteria andTotalStoreStockLike(String value) {
            addCriterion("total_store_stock like", value, "totalStoreStock");
            return (Criteria) this;
        }

        public Criteria andTotalStoreStockNotLike(String value) {
            addCriterion("total_store_stock not like", value, "totalStoreStock");
            return (Criteria) this;
        }

        public Criteria andTotalStoreStockIn(List<String> values) {
            addCriterion("total_store_stock in", values, "totalStoreStock");
            return (Criteria) this;
        }

        public Criteria andTotalStoreStockNotIn(List<String> values) {
            addCriterion("total_store_stock not in", values, "totalStoreStock");
            return (Criteria) this;
        }

        public Criteria andTotalStoreStockBetween(String value1, String value2) {
            addCriterion("total_store_stock between", value1, value2, "totalStoreStock");
            return (Criteria) this;
        }

        public Criteria andTotalStoreStockNotBetween(String value1, String value2) {
            addCriterion("total_store_stock not between", value1, value2, "totalStoreStock");
            return (Criteria) this;
        }

        public Criteria andQtyBefore30IsNull() {
            addCriterion("qty_before_30 is null");
            return (Criteria) this;
        }

        public Criteria andQtyBefore30IsNotNull() {
            addCriterion("qty_before_30 is not null");
            return (Criteria) this;
        }

        public Criteria andQtyBefore30EqualTo(String value) {
            addCriterion("qty_before_30 =", value, "qtyBefore30");
            return (Criteria) this;
        }

        public Criteria andQtyBefore30NotEqualTo(String value) {
            addCriterion("qty_before_30 <>", value, "qtyBefore30");
            return (Criteria) this;
        }

        public Criteria andQtyBefore30GreaterThan(String value) {
            addCriterion("qty_before_30 >", value, "qtyBefore30");
            return (Criteria) this;
        }

        public Criteria andQtyBefore30GreaterThanOrEqualTo(String value) {
            addCriterion("qty_before_30 >=", value, "qtyBefore30");
            return (Criteria) this;
        }

        public Criteria andQtyBefore30LessThan(String value) {
            addCriterion("qty_before_30 <", value, "qtyBefore30");
            return (Criteria) this;
        }

        public Criteria andQtyBefore30LessThanOrEqualTo(String value) {
            addCriterion("qty_before_30 <=", value, "qtyBefore30");
            return (Criteria) this;
        }

        public Criteria andQtyBefore30Like(String value) {
            addCriterion("qty_before_30 like", value, "qtyBefore30");
            return (Criteria) this;
        }

        public Criteria andQtyBefore30NotLike(String value) {
            addCriterion("qty_before_30 not like", value, "qtyBefore30");
            return (Criteria) this;
        }

        public Criteria andQtyBefore30In(List<String> values) {
            addCriterion("qty_before_30 in", values, "qtyBefore30");
            return (Criteria) this;
        }

        public Criteria andQtyBefore30NotIn(List<String> values) {
            addCriterion("qty_before_30 not in", values, "qtyBefore30");
            return (Criteria) this;
        }

        public Criteria andQtyBefore30Between(String value1, String value2) {
            addCriterion("qty_before_30 between", value1, value2, "qtyBefore30");
            return (Criteria) this;
        }

        public Criteria andQtyBefore30NotBetween(String value1, String value2) {
            addCriterion("qty_before_30 not between", value1, value2, "qtyBefore30");
            return (Criteria) this;
        }

        public Criteria andHbSaleRateIsNull() {
            addCriterion("hb_sale_rate is null");
            return (Criteria) this;
        }

        public Criteria andHbSaleRateIsNotNull() {
            addCriterion("hb_sale_rate is not null");
            return (Criteria) this;
        }

        public Criteria andHbSaleRateEqualTo(String value) {
            addCriterion("hb_sale_rate =", value, "hbSaleRate");
            return (Criteria) this;
        }

        public Criteria andHbSaleRateNotEqualTo(String value) {
            addCriterion("hb_sale_rate <>", value, "hbSaleRate");
            return (Criteria) this;
        }

        public Criteria andHbSaleRateGreaterThan(String value) {
            addCriterion("hb_sale_rate >", value, "hbSaleRate");
            return (Criteria) this;
        }

        public Criteria andHbSaleRateGreaterThanOrEqualTo(String value) {
            addCriterion("hb_sale_rate >=", value, "hbSaleRate");
            return (Criteria) this;
        }

        public Criteria andHbSaleRateLessThan(String value) {
            addCriterion("hb_sale_rate <", value, "hbSaleRate");
            return (Criteria) this;
        }

        public Criteria andHbSaleRateLessThanOrEqualTo(String value) {
            addCriterion("hb_sale_rate <=", value, "hbSaleRate");
            return (Criteria) this;
        }

        public Criteria andHbSaleRateLike(String value) {
            addCriterion("hb_sale_rate like", value, "hbSaleRate");
            return (Criteria) this;
        }

        public Criteria andHbSaleRateNotLike(String value) {
            addCriterion("hb_sale_rate not like", value, "hbSaleRate");
            return (Criteria) this;
        }

        public Criteria andHbSaleRateIn(List<String> values) {
            addCriterion("hb_sale_rate in", values, "hbSaleRate");
            return (Criteria) this;
        }

        public Criteria andHbSaleRateNotIn(List<String> values) {
            addCriterion("hb_sale_rate not in", values, "hbSaleRate");
            return (Criteria) this;
        }

        public Criteria andHbSaleRateBetween(String value1, String value2) {
            addCriterion("hb_sale_rate between", value1, value2, "hbSaleRate");
            return (Criteria) this;
        }

        public Criteria andHbSaleRateNotBetween(String value1, String value2) {
            addCriterion("hb_sale_rate not between", value1, value2, "hbSaleRate");
            return (Criteria) this;
        }

        public Criteria andTbSaleRateIsNull() {
            addCriterion("tb_sale_rate is null");
            return (Criteria) this;
        }

        public Criteria andTbSaleRateIsNotNull() {
            addCriterion("tb_sale_rate is not null");
            return (Criteria) this;
        }

        public Criteria andTbSaleRateEqualTo(String value) {
            addCriterion("tb_sale_rate =", value, "tbSaleRate");
            return (Criteria) this;
        }

        public Criteria andTbSaleRateNotEqualTo(String value) {
            addCriterion("tb_sale_rate <>", value, "tbSaleRate");
            return (Criteria) this;
        }

        public Criteria andTbSaleRateGreaterThan(String value) {
            addCriterion("tb_sale_rate >", value, "tbSaleRate");
            return (Criteria) this;
        }

        public Criteria andTbSaleRateGreaterThanOrEqualTo(String value) {
            addCriterion("tb_sale_rate >=", value, "tbSaleRate");
            return (Criteria) this;
        }

        public Criteria andTbSaleRateLessThan(String value) {
            addCriterion("tb_sale_rate <", value, "tbSaleRate");
            return (Criteria) this;
        }

        public Criteria andTbSaleRateLessThanOrEqualTo(String value) {
            addCriterion("tb_sale_rate <=", value, "tbSaleRate");
            return (Criteria) this;
        }

        public Criteria andTbSaleRateLike(String value) {
            addCriterion("tb_sale_rate like", value, "tbSaleRate");
            return (Criteria) this;
        }

        public Criteria andTbSaleRateNotLike(String value) {
            addCriterion("tb_sale_rate not like", value, "tbSaleRate");
            return (Criteria) this;
        }

        public Criteria andTbSaleRateIn(List<String> values) {
            addCriterion("tb_sale_rate in", values, "tbSaleRate");
            return (Criteria) this;
        }

        public Criteria andTbSaleRateNotIn(List<String> values) {
            addCriterion("tb_sale_rate not in", values, "tbSaleRate");
            return (Criteria) this;
        }

        public Criteria andTbSaleRateBetween(String value1, String value2) {
            addCriterion("tb_sale_rate between", value1, value2, "tbSaleRate");
            return (Criteria) this;
        }

        public Criteria andTbSaleRateNotBetween(String value1, String value2) {
            addCriterion("tb_sale_rate not between", value1, value2, "tbSaleRate");
            return (Criteria) this;
        }

        public Criteria andZdxmdsIsNull() {
            addCriterion("zdxmds is null");
            return (Criteria) this;
        }

        public Criteria andZdxmdsIsNotNull() {
            addCriterion("zdxmds is not null");
            return (Criteria) this;
        }

        public Criteria andZdxmdsEqualTo(BigDecimal value) {
            addCriterion("zdxmds =", value, "zdxmds");
            return (Criteria) this;
        }

        public Criteria andZdxmdsNotEqualTo(BigDecimal value) {
            addCriterion("zdxmds <>", value, "zdxmds");
            return (Criteria) this;
        }

        public Criteria andZdxmdsGreaterThan(BigDecimal value) {
            addCriterion("zdxmds >", value, "zdxmds");
            return (Criteria) this;
        }

        public Criteria andZdxmdsGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("zdxmds >=", value, "zdxmds");
            return (Criteria) this;
        }

        public Criteria andZdxmdsLessThan(BigDecimal value) {
            addCriterion("zdxmds <", value, "zdxmds");
            return (Criteria) this;
        }

        public Criteria andZdxmdsLessThanOrEqualTo(BigDecimal value) {
            addCriterion("zdxmds <=", value, "zdxmds");
            return (Criteria) this;
        }

        public Criteria andZdxmdsIn(List<BigDecimal> values) {
            addCriterion("zdxmds in", values, "zdxmds");
            return (Criteria) this;
        }

        public Criteria andZdxmdsNotIn(List<BigDecimal> values) {
            addCriterion("zdxmds not in", values, "zdxmds");
            return (Criteria) this;
        }

        public Criteria andZdxmdsBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("zdxmds between", value1, value2, "zdxmds");
            return (Criteria) this;
        }

        public Criteria andZdxmdsNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("zdxmds not between", value1, value2, "zdxmds");
            return (Criteria) this;
        }

        public Criteria andDcSaleDaysIsNull() {
            addCriterion("dc_sale_days is null");
            return (Criteria) this;
        }

        public Criteria andDcSaleDaysIsNotNull() {
            addCriterion("dc_sale_days is not null");
            return (Criteria) this;
        }

        public Criteria andDcSaleDaysEqualTo(BigDecimal value) {
            addCriterion("dc_sale_days =", value, "dcSaleDays");
            return (Criteria) this;
        }

        public Criteria andDcSaleDaysNotEqualTo(BigDecimal value) {
            addCriterion("dc_sale_days <>", value, "dcSaleDays");
            return (Criteria) this;
        }

        public Criteria andDcSaleDaysGreaterThan(BigDecimal value) {
            addCriterion("dc_sale_days >", value, "dcSaleDays");
            return (Criteria) this;
        }

        public Criteria andDcSaleDaysGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("dc_sale_days >=", value, "dcSaleDays");
            return (Criteria) this;
        }

        public Criteria andDcSaleDaysLessThan(BigDecimal value) {
            addCriterion("dc_sale_days <", value, "dcSaleDays");
            return (Criteria) this;
        }

        public Criteria andDcSaleDaysLessThanOrEqualTo(BigDecimal value) {
            addCriterion("dc_sale_days <=", value, "dcSaleDays");
            return (Criteria) this;
        }

        public Criteria andDcSaleDaysIn(List<BigDecimal> values) {
            addCriterion("dc_sale_days in", values, "dcSaleDays");
            return (Criteria) this;
        }

        public Criteria andDcSaleDaysNotIn(List<BigDecimal> values) {
            addCriterion("dc_sale_days not in", values, "dcSaleDays");
            return (Criteria) this;
        }

        public Criteria andDcSaleDaysBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("dc_sale_days between", value1, value2, "dcSaleDays");
            return (Criteria) this;
        }

        public Criteria andDcSaleDaysNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("dc_sale_days not between", value1, value2, "dcSaleDays");
            return (Criteria) this;
        }

        public Criteria andDcInvSaleDaysIsNull() {
            addCriterion("dc_inv_sale_days is null");
            return (Criteria) this;
        }

        public Criteria andDcInvSaleDaysIsNotNull() {
            addCriterion("dc_inv_sale_days is not null");
            return (Criteria) this;
        }

        public Criteria andDcInvSaleDaysEqualTo(BigDecimal value) {
            addCriterion("dc_inv_sale_days =", value, "dcInvSaleDays");
            return (Criteria) this;
        }

        public Criteria andDcInvSaleDaysNotEqualTo(BigDecimal value) {
            addCriterion("dc_inv_sale_days <>", value, "dcInvSaleDays");
            return (Criteria) this;
        }

        public Criteria andDcInvSaleDaysGreaterThan(BigDecimal value) {
            addCriterion("dc_inv_sale_days >", value, "dcInvSaleDays");
            return (Criteria) this;
        }

        public Criteria andDcInvSaleDaysGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("dc_inv_sale_days >=", value, "dcInvSaleDays");
            return (Criteria) this;
        }

        public Criteria andDcInvSaleDaysLessThan(BigDecimal value) {
            addCriterion("dc_inv_sale_days <", value, "dcInvSaleDays");
            return (Criteria) this;
        }

        public Criteria andDcInvSaleDaysLessThanOrEqualTo(BigDecimal value) {
            addCriterion("dc_inv_sale_days <=", value, "dcInvSaleDays");
            return (Criteria) this;
        }

        public Criteria andDcInvSaleDaysIn(List<BigDecimal> values) {
            addCriterion("dc_inv_sale_days in", values, "dcInvSaleDays");
            return (Criteria) this;
        }

        public Criteria andDcInvSaleDaysNotIn(List<BigDecimal> values) {
            addCriterion("dc_inv_sale_days not in", values, "dcInvSaleDays");
            return (Criteria) this;
        }

        public Criteria andDcInvSaleDaysBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("dc_inv_sale_days between", value1, value2, "dcInvSaleDays");
            return (Criteria) this;
        }

        public Criteria andDcInvSaleDaysNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("dc_inv_sale_days not between", value1, value2, "dcInvSaleDays");
            return (Criteria) this;
        }

        public Criteria andStoreSaleDaysIsNull() {
            addCriterion("store_sale_days is null");
            return (Criteria) this;
        }

        public Criteria andStoreSaleDaysIsNotNull() {
            addCriterion("store_sale_days is not null");
            return (Criteria) this;
        }

        public Criteria andStoreSaleDaysEqualTo(BigDecimal value) {
            addCriterion("store_sale_days =", value, "storeSaleDays");
            return (Criteria) this;
        }

        public Criteria andStoreSaleDaysNotEqualTo(BigDecimal value) {
            addCriterion("store_sale_days <>", value, "storeSaleDays");
            return (Criteria) this;
        }

        public Criteria andStoreSaleDaysGreaterThan(BigDecimal value) {
            addCriterion("store_sale_days >", value, "storeSaleDays");
            return (Criteria) this;
        }

        public Criteria andStoreSaleDaysGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("store_sale_days >=", value, "storeSaleDays");
            return (Criteria) this;
        }

        public Criteria andStoreSaleDaysLessThan(BigDecimal value) {
            addCriterion("store_sale_days <", value, "storeSaleDays");
            return (Criteria) this;
        }

        public Criteria andStoreSaleDaysLessThanOrEqualTo(BigDecimal value) {
            addCriterion("store_sale_days <=", value, "storeSaleDays");
            return (Criteria) this;
        }

        public Criteria andStoreSaleDaysIn(List<BigDecimal> values) {
            addCriterion("store_sale_days in", values, "storeSaleDays");
            return (Criteria) this;
        }

        public Criteria andStoreSaleDaysNotIn(List<BigDecimal> values) {
            addCriterion("store_sale_days not in", values, "storeSaleDays");
            return (Criteria) this;
        }

        public Criteria andStoreSaleDaysBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("store_sale_days between", value1, value2, "storeSaleDays");
            return (Criteria) this;
        }

        public Criteria andStoreSaleDaysNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("store_sale_days not between", value1, value2, "storeSaleDays");
            return (Criteria) this;
        }

        public Criteria andStoreInvSaleDaysIsNull() {
            addCriterion("store_inv_sale_days is null");
            return (Criteria) this;
        }

        public Criteria andStoreInvSaleDaysIsNotNull() {
            addCriterion("store_inv_sale_days is not null");
            return (Criteria) this;
        }

        public Criteria andStoreInvSaleDaysEqualTo(BigDecimal value) {
            addCriterion("store_inv_sale_days =", value, "storeInvSaleDays");
            return (Criteria) this;
        }

        public Criteria andStoreInvSaleDaysNotEqualTo(BigDecimal value) {
            addCriterion("store_inv_sale_days <>", value, "storeInvSaleDays");
            return (Criteria) this;
        }

        public Criteria andStoreInvSaleDaysGreaterThan(BigDecimal value) {
            addCriterion("store_inv_sale_days >", value, "storeInvSaleDays");
            return (Criteria) this;
        }

        public Criteria andStoreInvSaleDaysGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("store_inv_sale_days >=", value, "storeInvSaleDays");
            return (Criteria) this;
        }

        public Criteria andStoreInvSaleDaysLessThan(BigDecimal value) {
            addCriterion("store_inv_sale_days <", value, "storeInvSaleDays");
            return (Criteria) this;
        }

        public Criteria andStoreInvSaleDaysLessThanOrEqualTo(BigDecimal value) {
            addCriterion("store_inv_sale_days <=", value, "storeInvSaleDays");
            return (Criteria) this;
        }

        public Criteria andStoreInvSaleDaysIn(List<BigDecimal> values) {
            addCriterion("store_inv_sale_days in", values, "storeInvSaleDays");
            return (Criteria) this;
        }

        public Criteria andStoreInvSaleDaysNotIn(List<BigDecimal> values) {
            addCriterion("store_inv_sale_days not in", values, "storeInvSaleDays");
            return (Criteria) this;
        }

        public Criteria andStoreInvSaleDaysBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("store_inv_sale_days between", value1, value2, "storeInvSaleDays");
            return (Criteria) this;
        }

        public Criteria andStoreInvSaleDaysNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("store_inv_sale_days not between", value1, value2, "storeInvSaleDays");
            return (Criteria) this;
        }

        public Criteria andPuhuoStoresIsNull() {
            addCriterion("puhuo_stores is null");
            return (Criteria) this;
        }

        public Criteria andPuhuoStoresIsNotNull() {
            addCriterion("puhuo_stores is not null");
            return (Criteria) this;
        }

        public Criteria andPuhuoStoresEqualTo(BigDecimal value) {
            addCriterion("puhuo_stores =", value, "puhuoStores");
            return (Criteria) this;
        }

        public Criteria andPuhuoStoresNotEqualTo(BigDecimal value) {
            addCriterion("puhuo_stores <>", value, "puhuoStores");
            return (Criteria) this;
        }

        public Criteria andPuhuoStoresGreaterThan(BigDecimal value) {
            addCriterion("puhuo_stores >", value, "puhuoStores");
            return (Criteria) this;
        }

        public Criteria andPuhuoStoresGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("puhuo_stores >=", value, "puhuoStores");
            return (Criteria) this;
        }

        public Criteria andPuhuoStoresLessThan(BigDecimal value) {
            addCriterion("puhuo_stores <", value, "puhuoStores");
            return (Criteria) this;
        }

        public Criteria andPuhuoStoresLessThanOrEqualTo(BigDecimal value) {
            addCriterion("puhuo_stores <=", value, "puhuoStores");
            return (Criteria) this;
        }

        public Criteria andPuhuoStoresIn(List<BigDecimal> values) {
            addCriterion("puhuo_stores in", values, "puhuoStores");
            return (Criteria) this;
        }

        public Criteria andPuhuoStoresNotIn(List<BigDecimal> values) {
            addCriterion("puhuo_stores not in", values, "puhuoStores");
            return (Criteria) this;
        }

        public Criteria andPuhuoStoresBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("puhuo_stores between", value1, value2, "puhuoStores");
            return (Criteria) this;
        }

        public Criteria andPuhuoStoresNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("puhuo_stores not between", value1, value2, "puhuoStores");
            return (Criteria) this;
        }

        public Criteria andQtyBefore7IsNull() {
            addCriterion("qty_before_7 is null");
            return (Criteria) this;
        }

        public Criteria andQtyBefore7IsNotNull() {
            addCriterion("qty_before_7 is not null");
            return (Criteria) this;
        }

        public Criteria andQtyBefore7EqualTo(BigDecimal value) {
            addCriterion("qty_before_7 =", value, "qtyBefore7");
            return (Criteria) this;
        }

        public Criteria andQtyBefore7NotEqualTo(BigDecimal value) {
            addCriterion("qty_before_7 <>", value, "qtyBefore7");
            return (Criteria) this;
        }

        public Criteria andQtyBefore7GreaterThan(BigDecimal value) {
            addCriterion("qty_before_7 >", value, "qtyBefore7");
            return (Criteria) this;
        }

        public Criteria andQtyBefore7GreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("qty_before_7 >=", value, "qtyBefore7");
            return (Criteria) this;
        }

        public Criteria andQtyBefore7LessThan(BigDecimal value) {
            addCriterion("qty_before_7 <", value, "qtyBefore7");
            return (Criteria) this;
        }

        public Criteria andQtyBefore7LessThanOrEqualTo(BigDecimal value) {
            addCriterion("qty_before_7 <=", value, "qtyBefore7");
            return (Criteria) this;
        }

        public Criteria andQtyBefore7In(List<BigDecimal> values) {
            addCriterion("qty_before_7 in", values, "qtyBefore7");
            return (Criteria) this;
        }

        public Criteria andQtyBefore7NotIn(List<BigDecimal> values) {
            addCriterion("qty_before_7 not in", values, "qtyBefore7");
            return (Criteria) this;
        }

        public Criteria andQtyBefore7Between(BigDecimal value1, BigDecimal value2) {
            addCriterion("qty_before_7 between", value1, value2, "qtyBefore7");
            return (Criteria) this;
        }

        public Criteria andQtyBefore7NotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("qty_before_7 not between", value1, value2, "qtyBefore7");
            return (Criteria) this;
        }

        public Criteria andAvgQtyBefore7IsNull() {
            addCriterion("avg_qty_before_7 is null");
            return (Criteria) this;
        }

        public Criteria andAvgQtyBefore7IsNotNull() {
            addCriterion("avg_qty_before_7 is not null");
            return (Criteria) this;
        }

        public Criteria andAvgQtyBefore7EqualTo(String value) {
            addCriterion("avg_qty_before_7 =", value, "avgQtyBefore7");
            return (Criteria) this;
        }

        public Criteria andAvgQtyBefore7NotEqualTo(String value) {
            addCriterion("avg_qty_before_7 <>", value, "avgQtyBefore7");
            return (Criteria) this;
        }

        public Criteria andAvgQtyBefore7GreaterThan(String value) {
            addCriterion("avg_qty_before_7 >", value, "avgQtyBefore7");
            return (Criteria) this;
        }

        public Criteria andAvgQtyBefore7GreaterThanOrEqualTo(String value) {
            addCriterion("avg_qty_before_7 >=", value, "avgQtyBefore7");
            return (Criteria) this;
        }

        public Criteria andAvgQtyBefore7LessThan(String value) {
            addCriterion("avg_qty_before_7 <", value, "avgQtyBefore7");
            return (Criteria) this;
        }

        public Criteria andAvgQtyBefore7LessThanOrEqualTo(String value) {
            addCriterion("avg_qty_before_7 <=", value, "avgQtyBefore7");
            return (Criteria) this;
        }

        public Criteria andAvgQtyBefore7Like(String value) {
            addCriterion("avg_qty_before_7 like", value, "avgQtyBefore7");
            return (Criteria) this;
        }

        public Criteria andAvgQtyBefore7NotLike(String value) {
            addCriterion("avg_qty_before_7 not like", value, "avgQtyBefore7");
            return (Criteria) this;
        }

        public Criteria andAvgQtyBefore7In(List<String> values) {
            addCriterion("avg_qty_before_7 in", values, "avgQtyBefore7");
            return (Criteria) this;
        }

        public Criteria andAvgQtyBefore7NotIn(List<String> values) {
            addCriterion("avg_qty_before_7 not in", values, "avgQtyBefore7");
            return (Criteria) this;
        }

        public Criteria andAvgQtyBefore7Between(String value1, String value2) {
            addCriterion("avg_qty_before_7 between", value1, value2, "avgQtyBefore7");
            return (Criteria) this;
        }

        public Criteria andAvgQtyBefore7NotBetween(String value1, String value2) {
            addCriterion("avg_qty_before_7 not between", value1, value2, "avgQtyBefore7");
            return (Criteria) this;
        }

        public Criteria andQtyBefore14IsNull() {
            addCriterion("qty_before_14 is null");
            return (Criteria) this;
        }

        public Criteria andQtyBefore14IsNotNull() {
            addCriterion("qty_before_14 is not null");
            return (Criteria) this;
        }

        public Criteria andQtyBefore14EqualTo(BigDecimal value) {
            addCriterion("qty_before_14 =", value, "qtyBefore14");
            return (Criteria) this;
        }

        public Criteria andQtyBefore14NotEqualTo(BigDecimal value) {
            addCriterion("qty_before_14 <>", value, "qtyBefore14");
            return (Criteria) this;
        }

        public Criteria andQtyBefore14GreaterThan(BigDecimal value) {
            addCriterion("qty_before_14 >", value, "qtyBefore14");
            return (Criteria) this;
        }

        public Criteria andQtyBefore14GreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("qty_before_14 >=", value, "qtyBefore14");
            return (Criteria) this;
        }

        public Criteria andQtyBefore14LessThan(BigDecimal value) {
            addCriterion("qty_before_14 <", value, "qtyBefore14");
            return (Criteria) this;
        }

        public Criteria andQtyBefore14LessThanOrEqualTo(BigDecimal value) {
            addCriterion("qty_before_14 <=", value, "qtyBefore14");
            return (Criteria) this;
        }

        public Criteria andQtyBefore14In(List<BigDecimal> values) {
            addCriterion("qty_before_14 in", values, "qtyBefore14");
            return (Criteria) this;
        }

        public Criteria andQtyBefore14NotIn(List<BigDecimal> values) {
            addCriterion("qty_before_14 not in", values, "qtyBefore14");
            return (Criteria) this;
        }

        public Criteria andQtyBefore14Between(BigDecimal value1, BigDecimal value2) {
            addCriterion("qty_before_14 between", value1, value2, "qtyBefore14");
            return (Criteria) this;
        }

        public Criteria andQtyBefore14NotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("qty_before_14 not between", value1, value2, "qtyBefore14");
            return (Criteria) this;
        }

        public Criteria andAvgQtyBefore14IsNull() {
            addCriterion("avg_qty_before_14 is null");
            return (Criteria) this;
        }

        public Criteria andAvgQtyBefore14IsNotNull() {
            addCriterion("avg_qty_before_14 is not null");
            return (Criteria) this;
        }

        public Criteria andAvgQtyBefore14EqualTo(String value) {
            addCriterion("avg_qty_before_14 =", value, "avgQtyBefore14");
            return (Criteria) this;
        }

        public Criteria andAvgQtyBefore14NotEqualTo(String value) {
            addCriterion("avg_qty_before_14 <>", value, "avgQtyBefore14");
            return (Criteria) this;
        }

        public Criteria andAvgQtyBefore14GreaterThan(String value) {
            addCriterion("avg_qty_before_14 >", value, "avgQtyBefore14");
            return (Criteria) this;
        }

        public Criteria andAvgQtyBefore14GreaterThanOrEqualTo(String value) {
            addCriterion("avg_qty_before_14 >=", value, "avgQtyBefore14");
            return (Criteria) this;
        }

        public Criteria andAvgQtyBefore14LessThan(String value) {
            addCriterion("avg_qty_before_14 <", value, "avgQtyBefore14");
            return (Criteria) this;
        }

        public Criteria andAvgQtyBefore14LessThanOrEqualTo(String value) {
            addCriterion("avg_qty_before_14 <=", value, "avgQtyBefore14");
            return (Criteria) this;
        }

        public Criteria andAvgQtyBefore14Like(String value) {
            addCriterion("avg_qty_before_14 like", value, "avgQtyBefore14");
            return (Criteria) this;
        }

        public Criteria andAvgQtyBefore14NotLike(String value) {
            addCriterion("avg_qty_before_14 not like", value, "avgQtyBefore14");
            return (Criteria) this;
        }

        public Criteria andAvgQtyBefore14In(List<String> values) {
            addCriterion("avg_qty_before_14 in", values, "avgQtyBefore14");
            return (Criteria) this;
        }

        public Criteria andAvgQtyBefore14NotIn(List<String> values) {
            addCriterion("avg_qty_before_14 not in", values, "avgQtyBefore14");
            return (Criteria) this;
        }

        public Criteria andAvgQtyBefore14Between(String value1, String value2) {
            addCriterion("avg_qty_before_14 between", value1, value2, "avgQtyBefore14");
            return (Criteria) this;
        }

        public Criteria andAvgQtyBefore14NotBetween(String value1, String value2) {
            addCriterion("avg_qty_before_14 not between", value1, value2, "avgQtyBefore14");
            return (Criteria) this;
        }

        public Criteria andAvgQtyBefore30IsNull() {
            addCriterion("avg_qty_before_30 is null");
            return (Criteria) this;
        }

        public Criteria andAvgQtyBefore30IsNotNull() {
            addCriterion("avg_qty_before_30 is not null");
            return (Criteria) this;
        }

        public Criteria andAvgQtyBefore30EqualTo(String value) {
            addCriterion("avg_qty_before_30 =", value, "avgQtyBefore30");
            return (Criteria) this;
        }

        public Criteria andAvgQtyBefore30NotEqualTo(String value) {
            addCriterion("avg_qty_before_30 <>", value, "avgQtyBefore30");
            return (Criteria) this;
        }

        public Criteria andAvgQtyBefore30GreaterThan(String value) {
            addCriterion("avg_qty_before_30 >", value, "avgQtyBefore30");
            return (Criteria) this;
        }

        public Criteria andAvgQtyBefore30GreaterThanOrEqualTo(String value) {
            addCriterion("avg_qty_before_30 >=", value, "avgQtyBefore30");
            return (Criteria) this;
        }

        public Criteria andAvgQtyBefore30LessThan(String value) {
            addCriterion("avg_qty_before_30 <", value, "avgQtyBefore30");
            return (Criteria) this;
        }

        public Criteria andAvgQtyBefore30LessThanOrEqualTo(String value) {
            addCriterion("avg_qty_before_30 <=", value, "avgQtyBefore30");
            return (Criteria) this;
        }

        public Criteria andAvgQtyBefore30Like(String value) {
            addCriterion("avg_qty_before_30 like", value, "avgQtyBefore30");
            return (Criteria) this;
        }

        public Criteria andAvgQtyBefore30NotLike(String value) {
            addCriterion("avg_qty_before_30 not like", value, "avgQtyBefore30");
            return (Criteria) this;
        }

        public Criteria andAvgQtyBefore30In(List<String> values) {
            addCriterion("avg_qty_before_30 in", values, "avgQtyBefore30");
            return (Criteria) this;
        }

        public Criteria andAvgQtyBefore30NotIn(List<String> values) {
            addCriterion("avg_qty_before_30 not in", values, "avgQtyBefore30");
            return (Criteria) this;
        }

        public Criteria andAvgQtyBefore30Between(String value1, String value2) {
            addCriterion("avg_qty_before_30 between", value1, value2, "avgQtyBefore30");
            return (Criteria) this;
        }

        public Criteria andAvgQtyBefore30NotBetween(String value1, String value2) {
            addCriterion("avg_qty_before_30 not between", value1, value2, "avgQtyBefore30");
            return (Criteria) this;
        }

        public Criteria andQtyBefore90IsNull() {
            addCriterion("qty_before_90 is null");
            return (Criteria) this;
        }

        public Criteria andQtyBefore90IsNotNull() {
            addCriterion("qty_before_90 is not null");
            return (Criteria) this;
        }

        public Criteria andQtyBefore90EqualTo(BigDecimal value) {
            addCriterion("qty_before_90 =", value, "qtyBefore90");
            return (Criteria) this;
        }

        public Criteria andQtyBefore90NotEqualTo(BigDecimal value) {
            addCriterion("qty_before_90 <>", value, "qtyBefore90");
            return (Criteria) this;
        }

        public Criteria andQtyBefore90GreaterThan(BigDecimal value) {
            addCriterion("qty_before_90 >", value, "qtyBefore90");
            return (Criteria) this;
        }

        public Criteria andQtyBefore90GreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("qty_before_90 >=", value, "qtyBefore90");
            return (Criteria) this;
        }

        public Criteria andQtyBefore90LessThan(BigDecimal value) {
            addCriterion("qty_before_90 <", value, "qtyBefore90");
            return (Criteria) this;
        }

        public Criteria andQtyBefore90LessThanOrEqualTo(BigDecimal value) {
            addCriterion("qty_before_90 <=", value, "qtyBefore90");
            return (Criteria) this;
        }

        public Criteria andQtyBefore90In(List<BigDecimal> values) {
            addCriterion("qty_before_90 in", values, "qtyBefore90");
            return (Criteria) this;
        }

        public Criteria andQtyBefore90NotIn(List<BigDecimal> values) {
            addCriterion("qty_before_90 not in", values, "qtyBefore90");
            return (Criteria) this;
        }

        public Criteria andQtyBefore90Between(BigDecimal value1, BigDecimal value2) {
            addCriterion("qty_before_90 between", value1, value2, "qtyBefore90");
            return (Criteria) this;
        }

        public Criteria andQtyBefore90NotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("qty_before_90 not between", value1, value2, "qtyBefore90");
            return (Criteria) this;
        }

        public Criteria andAvgQtyBefore90IsNull() {
            addCriterion("avg_qty_before_90 is null");
            return (Criteria) this;
        }

        public Criteria andAvgQtyBefore90IsNotNull() {
            addCriterion("avg_qty_before_90 is not null");
            return (Criteria) this;
        }

        public Criteria andAvgQtyBefore90EqualTo(String value) {
            addCriterion("avg_qty_before_90 =", value, "avgQtyBefore90");
            return (Criteria) this;
        }

        public Criteria andAvgQtyBefore90NotEqualTo(String value) {
            addCriterion("avg_qty_before_90 <>", value, "avgQtyBefore90");
            return (Criteria) this;
        }

        public Criteria andAvgQtyBefore90GreaterThan(String value) {
            addCriterion("avg_qty_before_90 >", value, "avgQtyBefore90");
            return (Criteria) this;
        }

        public Criteria andAvgQtyBefore90GreaterThanOrEqualTo(String value) {
            addCriterion("avg_qty_before_90 >=", value, "avgQtyBefore90");
            return (Criteria) this;
        }

        public Criteria andAvgQtyBefore90LessThan(String value) {
            addCriterion("avg_qty_before_90 <", value, "avgQtyBefore90");
            return (Criteria) this;
        }

        public Criteria andAvgQtyBefore90LessThanOrEqualTo(String value) {
            addCriterion("avg_qty_before_90 <=", value, "avgQtyBefore90");
            return (Criteria) this;
        }

        public Criteria andAvgQtyBefore90Like(String value) {
            addCriterion("avg_qty_before_90 like", value, "avgQtyBefore90");
            return (Criteria) this;
        }

        public Criteria andAvgQtyBefore90NotLike(String value) {
            addCriterion("avg_qty_before_90 not like", value, "avgQtyBefore90");
            return (Criteria) this;
        }

        public Criteria andAvgQtyBefore90In(List<String> values) {
            addCriterion("avg_qty_before_90 in", values, "avgQtyBefore90");
            return (Criteria) this;
        }

        public Criteria andAvgQtyBefore90NotIn(List<String> values) {
            addCriterion("avg_qty_before_90 not in", values, "avgQtyBefore90");
            return (Criteria) this;
        }

        public Criteria andAvgQtyBefore90Between(String value1, String value2) {
            addCriterion("avg_qty_before_90 between", value1, value2, "avgQtyBefore90");
            return (Criteria) this;
        }

        public Criteria andAvgQtyBefore90NotBetween(String value1, String value2) {
            addCriterion("avg_qty_before_90 not between", value1, value2, "avgQtyBefore90");
            return (Criteria) this;
        }

        public Criteria andDistqtyBefore7IsNull() {
            addCriterion("distqty_before_7 is null");
            return (Criteria) this;
        }

        public Criteria andDistqtyBefore7IsNotNull() {
            addCriterion("distqty_before_7 is not null");
            return (Criteria) this;
        }

        public Criteria andDistqtyBefore7EqualTo(BigDecimal value) {
            addCriterion("distqty_before_7 =", value, "distqtyBefore7");
            return (Criteria) this;
        }

        public Criteria andDistqtyBefore7NotEqualTo(BigDecimal value) {
            addCriterion("distqty_before_7 <>", value, "distqtyBefore7");
            return (Criteria) this;
        }

        public Criteria andDistqtyBefore7GreaterThan(BigDecimal value) {
            addCriterion("distqty_before_7 >", value, "distqtyBefore7");
            return (Criteria) this;
        }

        public Criteria andDistqtyBefore7GreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("distqty_before_7 >=", value, "distqtyBefore7");
            return (Criteria) this;
        }

        public Criteria andDistqtyBefore7LessThan(BigDecimal value) {
            addCriterion("distqty_before_7 <", value, "distqtyBefore7");
            return (Criteria) this;
        }

        public Criteria andDistqtyBefore7LessThanOrEqualTo(BigDecimal value) {
            addCriterion("distqty_before_7 <=", value, "distqtyBefore7");
            return (Criteria) this;
        }

        public Criteria andDistqtyBefore7In(List<BigDecimal> values) {
            addCriterion("distqty_before_7 in", values, "distqtyBefore7");
            return (Criteria) this;
        }

        public Criteria andDistqtyBefore7NotIn(List<BigDecimal> values) {
            addCriterion("distqty_before_7 not in", values, "distqtyBefore7");
            return (Criteria) this;
        }

        public Criteria andDistqtyBefore7Between(BigDecimal value1, BigDecimal value2) {
            addCriterion("distqty_before_7 between", value1, value2, "distqtyBefore7");
            return (Criteria) this;
        }

        public Criteria andDistqtyBefore7NotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("distqty_before_7 not between", value1, value2, "distqtyBefore7");
            return (Criteria) this;
        }

        public Criteria andAvgDistqtyBefore7IsNull() {
            addCriterion("avg_distqty_before_7 is null");
            return (Criteria) this;
        }

        public Criteria andAvgDistqtyBefore7IsNotNull() {
            addCriterion("avg_distqty_before_7 is not null");
            return (Criteria) this;
        }

        public Criteria andAvgDistqtyBefore7EqualTo(String value) {
            addCriterion("avg_distqty_before_7 =", value, "avgDistqtyBefore7");
            return (Criteria) this;
        }

        public Criteria andAvgDistqtyBefore7NotEqualTo(String value) {
            addCriterion("avg_distqty_before_7 <>", value, "avgDistqtyBefore7");
            return (Criteria) this;
        }

        public Criteria andAvgDistqtyBefore7GreaterThan(String value) {
            addCriterion("avg_distqty_before_7 >", value, "avgDistqtyBefore7");
            return (Criteria) this;
        }

        public Criteria andAvgDistqtyBefore7GreaterThanOrEqualTo(String value) {
            addCriterion("avg_distqty_before_7 >=", value, "avgDistqtyBefore7");
            return (Criteria) this;
        }

        public Criteria andAvgDistqtyBefore7LessThan(String value) {
            addCriterion("avg_distqty_before_7 <", value, "avgDistqtyBefore7");
            return (Criteria) this;
        }

        public Criteria andAvgDistqtyBefore7LessThanOrEqualTo(String value) {
            addCriterion("avg_distqty_before_7 <=", value, "avgDistqtyBefore7");
            return (Criteria) this;
        }

        public Criteria andAvgDistqtyBefore7Like(String value) {
            addCriterion("avg_distqty_before_7 like", value, "avgDistqtyBefore7");
            return (Criteria) this;
        }

        public Criteria andAvgDistqtyBefore7NotLike(String value) {
            addCriterion("avg_distqty_before_7 not like", value, "avgDistqtyBefore7");
            return (Criteria) this;
        }

        public Criteria andAvgDistqtyBefore7In(List<String> values) {
            addCriterion("avg_distqty_before_7 in", values, "avgDistqtyBefore7");
            return (Criteria) this;
        }

        public Criteria andAvgDistqtyBefore7NotIn(List<String> values) {
            addCriterion("avg_distqty_before_7 not in", values, "avgDistqtyBefore7");
            return (Criteria) this;
        }

        public Criteria andAvgDistqtyBefore7Between(String value1, String value2) {
            addCriterion("avg_distqty_before_7 between", value1, value2, "avgDistqtyBefore7");
            return (Criteria) this;
        }

        public Criteria andAvgDistqtyBefore7NotBetween(String value1, String value2) {
            addCriterion("avg_distqty_before_7 not between", value1, value2, "avgDistqtyBefore7");
            return (Criteria) this;
        }

        public Criteria andDistqtyBefore14IsNull() {
            addCriterion("distqty_before_14 is null");
            return (Criteria) this;
        }

        public Criteria andDistqtyBefore14IsNotNull() {
            addCriterion("distqty_before_14 is not null");
            return (Criteria) this;
        }

        public Criteria andDistqtyBefore14EqualTo(BigDecimal value) {
            addCriterion("distqty_before_14 =", value, "distqtyBefore14");
            return (Criteria) this;
        }

        public Criteria andDistqtyBefore14NotEqualTo(BigDecimal value) {
            addCriterion("distqty_before_14 <>", value, "distqtyBefore14");
            return (Criteria) this;
        }

        public Criteria andDistqtyBefore14GreaterThan(BigDecimal value) {
            addCriterion("distqty_before_14 >", value, "distqtyBefore14");
            return (Criteria) this;
        }

        public Criteria andDistqtyBefore14GreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("distqty_before_14 >=", value, "distqtyBefore14");
            return (Criteria) this;
        }

        public Criteria andDistqtyBefore14LessThan(BigDecimal value) {
            addCriterion("distqty_before_14 <", value, "distqtyBefore14");
            return (Criteria) this;
        }

        public Criteria andDistqtyBefore14LessThanOrEqualTo(BigDecimal value) {
            addCriterion("distqty_before_14 <=", value, "distqtyBefore14");
            return (Criteria) this;
        }

        public Criteria andDistqtyBefore14In(List<BigDecimal> values) {
            addCriterion("distqty_before_14 in", values, "distqtyBefore14");
            return (Criteria) this;
        }

        public Criteria andDistqtyBefore14NotIn(List<BigDecimal> values) {
            addCriterion("distqty_before_14 not in", values, "distqtyBefore14");
            return (Criteria) this;
        }

        public Criteria andDistqtyBefore14Between(BigDecimal value1, BigDecimal value2) {
            addCriterion("distqty_before_14 between", value1, value2, "distqtyBefore14");
            return (Criteria) this;
        }

        public Criteria andDistqtyBefore14NotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("distqty_before_14 not between", value1, value2, "distqtyBefore14");
            return (Criteria) this;
        }

        public Criteria andAvgDistqtyBefore14IsNull() {
            addCriterion("avg_distqty_before_14 is null");
            return (Criteria) this;
        }

        public Criteria andAvgDistqtyBefore14IsNotNull() {
            addCriterion("avg_distqty_before_14 is not null");
            return (Criteria) this;
        }

        public Criteria andAvgDistqtyBefore14EqualTo(String value) {
            addCriterion("avg_distqty_before_14 =", value, "avgDistqtyBefore14");
            return (Criteria) this;
        }

        public Criteria andAvgDistqtyBefore14NotEqualTo(String value) {
            addCriterion("avg_distqty_before_14 <>", value, "avgDistqtyBefore14");
            return (Criteria) this;
        }

        public Criteria andAvgDistqtyBefore14GreaterThan(String value) {
            addCriterion("avg_distqty_before_14 >", value, "avgDistqtyBefore14");
            return (Criteria) this;
        }

        public Criteria andAvgDistqtyBefore14GreaterThanOrEqualTo(String value) {
            addCriterion("avg_distqty_before_14 >=", value, "avgDistqtyBefore14");
            return (Criteria) this;
        }

        public Criteria andAvgDistqtyBefore14LessThan(String value) {
            addCriterion("avg_distqty_before_14 <", value, "avgDistqtyBefore14");
            return (Criteria) this;
        }

        public Criteria andAvgDistqtyBefore14LessThanOrEqualTo(String value) {
            addCriterion("avg_distqty_before_14 <=", value, "avgDistqtyBefore14");
            return (Criteria) this;
        }

        public Criteria andAvgDistqtyBefore14Like(String value) {
            addCriterion("avg_distqty_before_14 like", value, "avgDistqtyBefore14");
            return (Criteria) this;
        }

        public Criteria andAvgDistqtyBefore14NotLike(String value) {
            addCriterion("avg_distqty_before_14 not like", value, "avgDistqtyBefore14");
            return (Criteria) this;
        }

        public Criteria andAvgDistqtyBefore14In(List<String> values) {
            addCriterion("avg_distqty_before_14 in", values, "avgDistqtyBefore14");
            return (Criteria) this;
        }

        public Criteria andAvgDistqtyBefore14NotIn(List<String> values) {
            addCriterion("avg_distqty_before_14 not in", values, "avgDistqtyBefore14");
            return (Criteria) this;
        }

        public Criteria andAvgDistqtyBefore14Between(String value1, String value2) {
            addCriterion("avg_distqty_before_14 between", value1, value2, "avgDistqtyBefore14");
            return (Criteria) this;
        }

        public Criteria andAvgDistqtyBefore14NotBetween(String value1, String value2) {
            addCriterion("avg_distqty_before_14 not between", value1, value2, "avgDistqtyBefore14");
            return (Criteria) this;
        }

        public Criteria andDistqty30IsNull() {
            addCriterion("distqty_30 is null");
            return (Criteria) this;
        }

        public Criteria andDistqty30IsNotNull() {
            addCriterion("distqty_30 is not null");
            return (Criteria) this;
        }

        public Criteria andDistqty30EqualTo(BigDecimal value) {
            addCriterion("distqty_30 =", value, "distqty30");
            return (Criteria) this;
        }

        public Criteria andDistqty30NotEqualTo(BigDecimal value) {
            addCriterion("distqty_30 <>", value, "distqty30");
            return (Criteria) this;
        }

        public Criteria andDistqty30GreaterThan(BigDecimal value) {
            addCriterion("distqty_30 >", value, "distqty30");
            return (Criteria) this;
        }

        public Criteria andDistqty30GreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("distqty_30 >=", value, "distqty30");
            return (Criteria) this;
        }

        public Criteria andDistqty30LessThan(BigDecimal value) {
            addCriterion("distqty_30 <", value, "distqty30");
            return (Criteria) this;
        }

        public Criteria andDistqty30LessThanOrEqualTo(BigDecimal value) {
            addCriterion("distqty_30 <=", value, "distqty30");
            return (Criteria) this;
        }

        public Criteria andDistqty30In(List<BigDecimal> values) {
            addCriterion("distqty_30 in", values, "distqty30");
            return (Criteria) this;
        }

        public Criteria andDistqty30NotIn(List<BigDecimal> values) {
            addCriterion("distqty_30 not in", values, "distqty30");
            return (Criteria) this;
        }

        public Criteria andDistqty30Between(BigDecimal value1, BigDecimal value2) {
            addCriterion("distqty_30 between", value1, value2, "distqty30");
            return (Criteria) this;
        }

        public Criteria andDistqty30NotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("distqty_30 not between", value1, value2, "distqty30");
            return (Criteria) this;
        }

        public Criteria andAvgDistqtyBefore30IsNull() {
            addCriterion("avg_distqty_before_30 is null");
            return (Criteria) this;
        }

        public Criteria andAvgDistqtyBefore30IsNotNull() {
            addCriterion("avg_distqty_before_30 is not null");
            return (Criteria) this;
        }

        public Criteria andAvgDistqtyBefore30EqualTo(String value) {
            addCriterion("avg_distqty_before_30 =", value, "avgDistqtyBefore30");
            return (Criteria) this;
        }

        public Criteria andAvgDistqtyBefore30NotEqualTo(String value) {
            addCriterion("avg_distqty_before_30 <>", value, "avgDistqtyBefore30");
            return (Criteria) this;
        }

        public Criteria andAvgDistqtyBefore30GreaterThan(String value) {
            addCriterion("avg_distqty_before_30 >", value, "avgDistqtyBefore30");
            return (Criteria) this;
        }

        public Criteria andAvgDistqtyBefore30GreaterThanOrEqualTo(String value) {
            addCriterion("avg_distqty_before_30 >=", value, "avgDistqtyBefore30");
            return (Criteria) this;
        }

        public Criteria andAvgDistqtyBefore30LessThan(String value) {
            addCriterion("avg_distqty_before_30 <", value, "avgDistqtyBefore30");
            return (Criteria) this;
        }

        public Criteria andAvgDistqtyBefore30LessThanOrEqualTo(String value) {
            addCriterion("avg_distqty_before_30 <=", value, "avgDistqtyBefore30");
            return (Criteria) this;
        }

        public Criteria andAvgDistqtyBefore30Like(String value) {
            addCriterion("avg_distqty_before_30 like", value, "avgDistqtyBefore30");
            return (Criteria) this;
        }

        public Criteria andAvgDistqtyBefore30NotLike(String value) {
            addCriterion("avg_distqty_before_30 not like", value, "avgDistqtyBefore30");
            return (Criteria) this;
        }

        public Criteria andAvgDistqtyBefore30In(List<String> values) {
            addCriterion("avg_distqty_before_30 in", values, "avgDistqtyBefore30");
            return (Criteria) this;
        }

        public Criteria andAvgDistqtyBefore30NotIn(List<String> values) {
            addCriterion("avg_distqty_before_30 not in", values, "avgDistqtyBefore30");
            return (Criteria) this;
        }

        public Criteria andAvgDistqtyBefore30Between(String value1, String value2) {
            addCriterion("avg_distqty_before_30 between", value1, value2, "avgDistqtyBefore30");
            return (Criteria) this;
        }

        public Criteria andAvgDistqtyBefore30NotBetween(String value1, String value2) {
            addCriterion("avg_distqty_before_30 not between", value1, value2, "avgDistqtyBefore30");
            return (Criteria) this;
        }

        public Criteria andDistqtyBefore90IsNull() {
            addCriterion("distqty_before_90 is null");
            return (Criteria) this;
        }

        public Criteria andDistqtyBefore90IsNotNull() {
            addCriterion("distqty_before_90 is not null");
            return (Criteria) this;
        }

        public Criteria andDistqtyBefore90EqualTo(BigDecimal value) {
            addCriterion("distqty_before_90 =", value, "distqtyBefore90");
            return (Criteria) this;
        }

        public Criteria andDistqtyBefore90NotEqualTo(BigDecimal value) {
            addCriterion("distqty_before_90 <>", value, "distqtyBefore90");
            return (Criteria) this;
        }

        public Criteria andDistqtyBefore90GreaterThan(BigDecimal value) {
            addCriterion("distqty_before_90 >", value, "distqtyBefore90");
            return (Criteria) this;
        }

        public Criteria andDistqtyBefore90GreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("distqty_before_90 >=", value, "distqtyBefore90");
            return (Criteria) this;
        }

        public Criteria andDistqtyBefore90LessThan(BigDecimal value) {
            addCriterion("distqty_before_90 <", value, "distqtyBefore90");
            return (Criteria) this;
        }

        public Criteria andDistqtyBefore90LessThanOrEqualTo(BigDecimal value) {
            addCriterion("distqty_before_90 <=", value, "distqtyBefore90");
            return (Criteria) this;
        }

        public Criteria andDistqtyBefore90In(List<BigDecimal> values) {
            addCriterion("distqty_before_90 in", values, "distqtyBefore90");
            return (Criteria) this;
        }

        public Criteria andDistqtyBefore90NotIn(List<BigDecimal> values) {
            addCriterion("distqty_before_90 not in", values, "distqtyBefore90");
            return (Criteria) this;
        }

        public Criteria andDistqtyBefore90Between(BigDecimal value1, BigDecimal value2) {
            addCriterion("distqty_before_90 between", value1, value2, "distqtyBefore90");
            return (Criteria) this;
        }

        public Criteria andDistqtyBefore90NotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("distqty_before_90 not between", value1, value2, "distqtyBefore90");
            return (Criteria) this;
        }

        public Criteria andAvgDistqtyBefore90IsNull() {
            addCriterion("avg_distqty_before_90 is null");
            return (Criteria) this;
        }

        public Criteria andAvgDistqtyBefore90IsNotNull() {
            addCriterion("avg_distqty_before_90 is not null");
            return (Criteria) this;
        }

        public Criteria andAvgDistqtyBefore90EqualTo(String value) {
            addCriterion("avg_distqty_before_90 =", value, "avgDistqtyBefore90");
            return (Criteria) this;
        }

        public Criteria andAvgDistqtyBefore90NotEqualTo(String value) {
            addCriterion("avg_distqty_before_90 <>", value, "avgDistqtyBefore90");
            return (Criteria) this;
        }

        public Criteria andAvgDistqtyBefore90GreaterThan(String value) {
            addCriterion("avg_distqty_before_90 >", value, "avgDistqtyBefore90");
            return (Criteria) this;
        }

        public Criteria andAvgDistqtyBefore90GreaterThanOrEqualTo(String value) {
            addCriterion("avg_distqty_before_90 >=", value, "avgDistqtyBefore90");
            return (Criteria) this;
        }

        public Criteria andAvgDistqtyBefore90LessThan(String value) {
            addCriterion("avg_distqty_before_90 <", value, "avgDistqtyBefore90");
            return (Criteria) this;
        }

        public Criteria andAvgDistqtyBefore90LessThanOrEqualTo(String value) {
            addCriterion("avg_distqty_before_90 <=", value, "avgDistqtyBefore90");
            return (Criteria) this;
        }

        public Criteria andAvgDistqtyBefore90Like(String value) {
            addCriterion("avg_distqty_before_90 like", value, "avgDistqtyBefore90");
            return (Criteria) this;
        }

        public Criteria andAvgDistqtyBefore90NotLike(String value) {
            addCriterion("avg_distqty_before_90 not like", value, "avgDistqtyBefore90");
            return (Criteria) this;
        }

        public Criteria andAvgDistqtyBefore90In(List<String> values) {
            addCriterion("avg_distqty_before_90 in", values, "avgDistqtyBefore90");
            return (Criteria) this;
        }

        public Criteria andAvgDistqtyBefore90NotIn(List<String> values) {
            addCriterion("avg_distqty_before_90 not in", values, "avgDistqtyBefore90");
            return (Criteria) this;
        }

        public Criteria andAvgDistqtyBefore90Between(String value1, String value2) {
            addCriterion("avg_distqty_before_90 between", value1, value2, "avgDistqtyBefore90");
            return (Criteria) this;
        }

        public Criteria andAvgDistqtyBefore90NotBetween(String value1, String value2) {
            addCriterion("avg_distqty_before_90 not between", value1, value2, "avgDistqtyBefore90");
            return (Criteria) this;
        }

        public Criteria andQty3060IsNull() {
            addCriterion("qty_30_60 is null");
            return (Criteria) this;
        }

        public Criteria andQty3060IsNotNull() {
            addCriterion("qty_30_60 is not null");
            return (Criteria) this;
        }

        public Criteria andQty3060EqualTo(BigDecimal value) {
            addCriterion("qty_30_60 =", value, "qty3060");
            return (Criteria) this;
        }

        public Criteria andQty3060NotEqualTo(BigDecimal value) {
            addCriterion("qty_30_60 <>", value, "qty3060");
            return (Criteria) this;
        }

        public Criteria andQty3060GreaterThan(BigDecimal value) {
            addCriterion("qty_30_60 >", value, "qty3060");
            return (Criteria) this;
        }

        public Criteria andQty3060GreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("qty_30_60 >=", value, "qty3060");
            return (Criteria) this;
        }

        public Criteria andQty3060LessThan(BigDecimal value) {
            addCriterion("qty_30_60 <", value, "qty3060");
            return (Criteria) this;
        }

        public Criteria andQty3060LessThanOrEqualTo(BigDecimal value) {
            addCriterion("qty_30_60 <=", value, "qty3060");
            return (Criteria) this;
        }

        public Criteria andQty3060In(List<BigDecimal> values) {
            addCriterion("qty_30_60 in", values, "qty3060");
            return (Criteria) this;
        }

        public Criteria andQty3060NotIn(List<BigDecimal> values) {
            addCriterion("qty_30_60 not in", values, "qty3060");
            return (Criteria) this;
        }

        public Criteria andQty3060Between(BigDecimal value1, BigDecimal value2) {
            addCriterion("qty_30_60 between", value1, value2, "qty3060");
            return (Criteria) this;
        }

        public Criteria andQty3060NotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("qty_30_60 not between", value1, value2, "qty3060");
            return (Criteria) this;
        }

        public Criteria andQty6090IsNull() {
            addCriterion("qty_60_90 is null");
            return (Criteria) this;
        }

        public Criteria andQty6090IsNotNull() {
            addCriterion("qty_60_90 is not null");
            return (Criteria) this;
        }

        public Criteria andQty6090EqualTo(BigDecimal value) {
            addCriterion("qty_60_90 =", value, "qty6090");
            return (Criteria) this;
        }

        public Criteria andQty6090NotEqualTo(BigDecimal value) {
            addCriterion("qty_60_90 <>", value, "qty6090");
            return (Criteria) this;
        }

        public Criteria andQty6090GreaterThan(BigDecimal value) {
            addCriterion("qty_60_90 >", value, "qty6090");
            return (Criteria) this;
        }

        public Criteria andQty6090GreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("qty_60_90 >=", value, "qty6090");
            return (Criteria) this;
        }

        public Criteria andQty6090LessThan(BigDecimal value) {
            addCriterion("qty_60_90 <", value, "qty6090");
            return (Criteria) this;
        }

        public Criteria andQty6090LessThanOrEqualTo(BigDecimal value) {
            addCriterion("qty_60_90 <=", value, "qty6090");
            return (Criteria) this;
        }

        public Criteria andQty6090In(List<BigDecimal> values) {
            addCriterion("qty_60_90 in", values, "qty6090");
            return (Criteria) this;
        }

        public Criteria andQty6090NotIn(List<BigDecimal> values) {
            addCriterion("qty_60_90 not in", values, "qty6090");
            return (Criteria) this;
        }

        public Criteria andQty6090Between(BigDecimal value1, BigDecimal value2) {
            addCriterion("qty_60_90 between", value1, value2, "qty6090");
            return (Criteria) this;
        }

        public Criteria andQty6090NotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("qty_60_90 not between", value1, value2, "qty6090");
            return (Criteria) this;
        }

        public Criteria andDistqty3060IsNull() {
            addCriterion("distqty_30_60 is null");
            return (Criteria) this;
        }

        public Criteria andDistqty3060IsNotNull() {
            addCriterion("distqty_30_60 is not null");
            return (Criteria) this;
        }

        public Criteria andDistqty3060EqualTo(BigDecimal value) {
            addCriterion("distqty_30_60 =", value, "distqty3060");
            return (Criteria) this;
        }

        public Criteria andDistqty3060NotEqualTo(BigDecimal value) {
            addCriterion("distqty_30_60 <>", value, "distqty3060");
            return (Criteria) this;
        }

        public Criteria andDistqty3060GreaterThan(BigDecimal value) {
            addCriterion("distqty_30_60 >", value, "distqty3060");
            return (Criteria) this;
        }

        public Criteria andDistqty3060GreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("distqty_30_60 >=", value, "distqty3060");
            return (Criteria) this;
        }

        public Criteria andDistqty3060LessThan(BigDecimal value) {
            addCriterion("distqty_30_60 <", value, "distqty3060");
            return (Criteria) this;
        }

        public Criteria andDistqty3060LessThanOrEqualTo(BigDecimal value) {
            addCriterion("distqty_30_60 <=", value, "distqty3060");
            return (Criteria) this;
        }

        public Criteria andDistqty3060In(List<BigDecimal> values) {
            addCriterion("distqty_30_60 in", values, "distqty3060");
            return (Criteria) this;
        }

        public Criteria andDistqty3060NotIn(List<BigDecimal> values) {
            addCriterion("distqty_30_60 not in", values, "distqty3060");
            return (Criteria) this;
        }

        public Criteria andDistqty3060Between(BigDecimal value1, BigDecimal value2) {
            addCriterion("distqty_30_60 between", value1, value2, "distqty3060");
            return (Criteria) this;
        }

        public Criteria andDistqty3060NotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("distqty_30_60 not between", value1, value2, "distqty3060");
            return (Criteria) this;
        }

        public Criteria andDistqty6090IsNull() {
            addCriterion("distqty_60_90 is null");
            return (Criteria) this;
        }

        public Criteria andDistqty6090IsNotNull() {
            addCriterion("distqty_60_90 is not null");
            return (Criteria) this;
        }

        public Criteria andDistqty6090EqualTo(BigDecimal value) {
            addCriterion("distqty_60_90 =", value, "distqty6090");
            return (Criteria) this;
        }

        public Criteria andDistqty6090NotEqualTo(BigDecimal value) {
            addCriterion("distqty_60_90 <>", value, "distqty6090");
            return (Criteria) this;
        }

        public Criteria andDistqty6090GreaterThan(BigDecimal value) {
            addCriterion("distqty_60_90 >", value, "distqty6090");
            return (Criteria) this;
        }

        public Criteria andDistqty6090GreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("distqty_60_90 >=", value, "distqty6090");
            return (Criteria) this;
        }

        public Criteria andDistqty6090LessThan(BigDecimal value) {
            addCriterion("distqty_60_90 <", value, "distqty6090");
            return (Criteria) this;
        }

        public Criteria andDistqty6090LessThanOrEqualTo(BigDecimal value) {
            addCriterion("distqty_60_90 <=", value, "distqty6090");
            return (Criteria) this;
        }

        public Criteria andDistqty6090In(List<BigDecimal> values) {
            addCriterion("distqty_60_90 in", values, "distqty6090");
            return (Criteria) this;
        }

        public Criteria andDistqty6090NotIn(List<BigDecimal> values) {
            addCriterion("distqty_60_90 not in", values, "distqty6090");
            return (Criteria) this;
        }

        public Criteria andDistqty6090Between(BigDecimal value1, BigDecimal value2) {
            addCriterion("distqty_60_90 between", value1, value2, "distqty6090");
            return (Criteria) this;
        }

        public Criteria andDistqty6090NotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("distqty_60_90 not between", value1, value2, "distqty6090");
            return (Criteria) this;
        }

        public Criteria andSeasonalFactorIsNull() {
            addCriterion("seasonal_factor is null");
            return (Criteria) this;
        }

        public Criteria andSeasonalFactorIsNotNull() {
            addCriterion("seasonal_factor is not null");
            return (Criteria) this;
        }

        public Criteria andSeasonalFactorEqualTo(String value) {
            addCriterion("seasonal_factor =", value, "seasonalFactor");
            return (Criteria) this;
        }

        public Criteria andSeasonalFactorNotEqualTo(String value) {
            addCriterion("seasonal_factor <>", value, "seasonalFactor");
            return (Criteria) this;
        }

        public Criteria andSeasonalFactorGreaterThan(String value) {
            addCriterion("seasonal_factor >", value, "seasonalFactor");
            return (Criteria) this;
        }

        public Criteria andSeasonalFactorGreaterThanOrEqualTo(String value) {
            addCriterion("seasonal_factor >=", value, "seasonalFactor");
            return (Criteria) this;
        }

        public Criteria andSeasonalFactorLessThan(String value) {
            addCriterion("seasonal_factor <", value, "seasonalFactor");
            return (Criteria) this;
        }

        public Criteria andSeasonalFactorLessThanOrEqualTo(String value) {
            addCriterion("seasonal_factor <=", value, "seasonalFactor");
            return (Criteria) this;
        }

        public Criteria andSeasonalFactorLike(String value) {
            addCriterion("seasonal_factor like", value, "seasonalFactor");
            return (Criteria) this;
        }

        public Criteria andSeasonalFactorNotLike(String value) {
            addCriterion("seasonal_factor not like", value, "seasonalFactor");
            return (Criteria) this;
        }

        public Criteria andSeasonalFactorIn(List<String> values) {
            addCriterion("seasonal_factor in", values, "seasonalFactor");
            return (Criteria) this;
        }

        public Criteria andSeasonalFactorNotIn(List<String> values) {
            addCriterion("seasonal_factor not in", values, "seasonalFactor");
            return (Criteria) this;
        }

        public Criteria andSeasonalFactorBetween(String value1, String value2) {
            addCriterion("seasonal_factor between", value1, value2, "seasonalFactor");
            return (Criteria) this;
        }

        public Criteria andSeasonalFactorNotBetween(String value1, String value2) {
            addCriterion("seasonal_factor not between", value1, value2, "seasonalFactor");
            return (Criteria) this;
        }

        public Criteria andJmStoreStockIsNull() {
            addCriterion("jm_store_stock is null");
            return (Criteria) this;
        }

        public Criteria andJmStoreStockIsNotNull() {
            addCriterion("jm_store_stock is not null");
            return (Criteria) this;
        }

        public Criteria andJmStoreStockEqualTo(BigDecimal value) {
            addCriterion("jm_store_stock =", value, "jmStoreStock");
            return (Criteria) this;
        }

        public Criteria andJmStoreStockNotEqualTo(BigDecimal value) {
            addCriterion("jm_store_stock <>", value, "jmStoreStock");
            return (Criteria) this;
        }

        public Criteria andJmStoreStockGreaterThan(BigDecimal value) {
            addCriterion("jm_store_stock >", value, "jmStoreStock");
            return (Criteria) this;
        }

        public Criteria andJmStoreStockGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("jm_store_stock >=", value, "jmStoreStock");
            return (Criteria) this;
        }

        public Criteria andJmStoreStockLessThan(BigDecimal value) {
            addCriterion("jm_store_stock <", value, "jmStoreStock");
            return (Criteria) this;
        }

        public Criteria andJmStoreStockLessThanOrEqualTo(BigDecimal value) {
            addCriterion("jm_store_stock <=", value, "jmStoreStock");
            return (Criteria) this;
        }

        public Criteria andJmStoreStockIn(List<BigDecimal> values) {
            addCriterion("jm_store_stock in", values, "jmStoreStock");
            return (Criteria) this;
        }

        public Criteria andJmStoreStockNotIn(List<BigDecimal> values) {
            addCriterion("jm_store_stock not in", values, "jmStoreStock");
            return (Criteria) this;
        }

        public Criteria andJmStoreStockBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("jm_store_stock between", value1, value2, "jmStoreStock");
            return (Criteria) this;
        }

        public Criteria andJmStoreStockNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("jm_store_stock not between", value1, value2, "jmStoreStock");
            return (Criteria) this;
        }

        public Criteria andDcStockIsNull() {
            addCriterion("dc_stock is null");
            return (Criteria) this;
        }

        public Criteria andDcStockIsNotNull() {
            addCriterion("dc_stock is not null");
            return (Criteria) this;
        }

        public Criteria andDcStockEqualTo(BigDecimal value) {
            addCriterion("dc_stock =", value, "dcStock");
            return (Criteria) this;
        }

        public Criteria andDcStockNotEqualTo(BigDecimal value) {
            addCriterion("dc_stock <>", value, "dcStock");
            return (Criteria) this;
        }

        public Criteria andDcStockGreaterThan(BigDecimal value) {
            addCriterion("dc_stock >", value, "dcStock");
            return (Criteria) this;
        }

        public Criteria andDcStockGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("dc_stock >=", value, "dcStock");
            return (Criteria) this;
        }

        public Criteria andDcStockLessThan(BigDecimal value) {
            addCriterion("dc_stock <", value, "dcStock");
            return (Criteria) this;
        }

        public Criteria andDcStockLessThanOrEqualTo(BigDecimal value) {
            addCriterion("dc_stock <=", value, "dcStock");
            return (Criteria) this;
        }

        public Criteria andDcStockIn(List<BigDecimal> values) {
            addCriterion("dc_stock in", values, "dcStock");
            return (Criteria) this;
        }

        public Criteria andDcStockNotIn(List<BigDecimal> values) {
            addCriterion("dc_stock not in", values, "dcStock");
            return (Criteria) this;
        }

        public Criteria andDcStockBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("dc_stock between", value1, value2, "dcStock");
            return (Criteria) this;
        }

        public Criteria andDcStockNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("dc_stock not between", value1, value2, "dcStock");
            return (Criteria) this;
        }

        public Criteria andDcDisableStockIsNull() {
            addCriterion("dc_disable_stock is null");
            return (Criteria) this;
        }

        public Criteria andDcDisableStockIsNotNull() {
            addCriterion("dc_disable_stock is not null");
            return (Criteria) this;
        }

        public Criteria andDcDisableStockEqualTo(BigDecimal value) {
            addCriterion("dc_disable_stock =", value, "dcDisableStock");
            return (Criteria) this;
        }

        public Criteria andDcDisableStockNotEqualTo(BigDecimal value) {
            addCriterion("dc_disable_stock <>", value, "dcDisableStock");
            return (Criteria) this;
        }

        public Criteria andDcDisableStockGreaterThan(BigDecimal value) {
            addCriterion("dc_disable_stock >", value, "dcDisableStock");
            return (Criteria) this;
        }

        public Criteria andDcDisableStockGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("dc_disable_stock >=", value, "dcDisableStock");
            return (Criteria) this;
        }

        public Criteria andDcDisableStockLessThan(BigDecimal value) {
            addCriterion("dc_disable_stock <", value, "dcDisableStock");
            return (Criteria) this;
        }

        public Criteria andDcDisableStockLessThanOrEqualTo(BigDecimal value) {
            addCriterion("dc_disable_stock <=", value, "dcDisableStock");
            return (Criteria) this;
        }

        public Criteria andDcDisableStockIn(List<BigDecimal> values) {
            addCriterion("dc_disable_stock in", values, "dcDisableStock");
            return (Criteria) this;
        }

        public Criteria andDcDisableStockNotIn(List<BigDecimal> values) {
            addCriterion("dc_disable_stock not in", values, "dcDisableStock");
            return (Criteria) this;
        }

        public Criteria andDcDisableStockBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("dc_disable_stock between", value1, value2, "dcDisableStock");
            return (Criteria) this;
        }

        public Criteria andDcDisableStockNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("dc_disable_stock not between", value1, value2, "dcDisableStock");
            return (Criteria) this;
        }

        public Criteria andZcInactiveDcStockIsNull() {
            addCriterion("zc_inactive_dc_stock is null");
            return (Criteria) this;
        }

        public Criteria andZcInactiveDcStockIsNotNull() {
            addCriterion("zc_inactive_dc_stock is not null");
            return (Criteria) this;
        }

        public Criteria andZcInactiveDcStockEqualTo(BigDecimal value) {
            addCriterion("zc_inactive_dc_stock =", value, "zcInactiveDcStock");
            return (Criteria) this;
        }

        public Criteria andZcInactiveDcStockNotEqualTo(BigDecimal value) {
            addCriterion("zc_inactive_dc_stock <>", value, "zcInactiveDcStock");
            return (Criteria) this;
        }

        public Criteria andZcInactiveDcStockGreaterThan(BigDecimal value) {
            addCriterion("zc_inactive_dc_stock >", value, "zcInactiveDcStock");
            return (Criteria) this;
        }

        public Criteria andZcInactiveDcStockGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("zc_inactive_dc_stock >=", value, "zcInactiveDcStock");
            return (Criteria) this;
        }

        public Criteria andZcInactiveDcStockLessThan(BigDecimal value) {
            addCriterion("zc_inactive_dc_stock <", value, "zcInactiveDcStock");
            return (Criteria) this;
        }

        public Criteria andZcInactiveDcStockLessThanOrEqualTo(BigDecimal value) {
            addCriterion("zc_inactive_dc_stock <=", value, "zcInactiveDcStock");
            return (Criteria) this;
        }

        public Criteria andZcInactiveDcStockIn(List<BigDecimal> values) {
            addCriterion("zc_inactive_dc_stock in", values, "zcInactiveDcStock");
            return (Criteria) this;
        }

        public Criteria andZcInactiveDcStockNotIn(List<BigDecimal> values) {
            addCriterion("zc_inactive_dc_stock not in", values, "zcInactiveDcStock");
            return (Criteria) this;
        }

        public Criteria andZcInactiveDcStockBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("zc_inactive_dc_stock between", value1, value2, "zcInactiveDcStock");
            return (Criteria) this;
        }

        public Criteria andZcInactiveDcStockNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("zc_inactive_dc_stock not between", value1, value2, "zcInactiveDcStock");
            return (Criteria) this;
        }

        public Criteria andDcInvUpperIsNull() {
            addCriterion("dc_inv_upper is null");
            return (Criteria) this;
        }

        public Criteria andDcInvUpperIsNotNull() {
            addCriterion("dc_inv_upper is not null");
            return (Criteria) this;
        }

        public Criteria andDcInvUpperEqualTo(BigDecimal value) {
            addCriterion("dc_inv_upper =", value, "dcInvUpper");
            return (Criteria) this;
        }

        public Criteria andDcInvUpperNotEqualTo(BigDecimal value) {
            addCriterion("dc_inv_upper <>", value, "dcInvUpper");
            return (Criteria) this;
        }

        public Criteria andDcInvUpperGreaterThan(BigDecimal value) {
            addCriterion("dc_inv_upper >", value, "dcInvUpper");
            return (Criteria) this;
        }

        public Criteria andDcInvUpperGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("dc_inv_upper >=", value, "dcInvUpper");
            return (Criteria) this;
        }

        public Criteria andDcInvUpperLessThan(BigDecimal value) {
            addCriterion("dc_inv_upper <", value, "dcInvUpper");
            return (Criteria) this;
        }

        public Criteria andDcInvUpperLessThanOrEqualTo(BigDecimal value) {
            addCriterion("dc_inv_upper <=", value, "dcInvUpper");
            return (Criteria) this;
        }

        public Criteria andDcInvUpperIn(List<BigDecimal> values) {
            addCriterion("dc_inv_upper in", values, "dcInvUpper");
            return (Criteria) this;
        }

        public Criteria andDcInvUpperNotIn(List<BigDecimal> values) {
            addCriterion("dc_inv_upper not in", values, "dcInvUpper");
            return (Criteria) this;
        }

        public Criteria andDcInvUpperBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("dc_inv_upper between", value1, value2, "dcInvUpper");
            return (Criteria) this;
        }

        public Criteria andDcInvUpperNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("dc_inv_upper not between", value1, value2, "dcInvUpper");
            return (Criteria) this;
        }

        public Criteria andDcBlockStock1IsNull() {
            addCriterion("dc_block_stock1 is null");
            return (Criteria) this;
        }

        public Criteria andDcBlockStock1IsNotNull() {
            addCriterion("dc_block_stock1 is not null");
            return (Criteria) this;
        }

        public Criteria andDcBlockStock1EqualTo(BigDecimal value) {
            addCriterion("dc_block_stock1 =", value, "dcBlockStock1");
            return (Criteria) this;
        }

        public Criteria andDcBlockStock1NotEqualTo(BigDecimal value) {
            addCriterion("dc_block_stock1 <>", value, "dcBlockStock1");
            return (Criteria) this;
        }

        public Criteria andDcBlockStock1GreaterThan(BigDecimal value) {
            addCriterion("dc_block_stock1 >", value, "dcBlockStock1");
            return (Criteria) this;
        }

        public Criteria andDcBlockStock1GreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("dc_block_stock1 >=", value, "dcBlockStock1");
            return (Criteria) this;
        }

        public Criteria andDcBlockStock1LessThan(BigDecimal value) {
            addCriterion("dc_block_stock1 <", value, "dcBlockStock1");
            return (Criteria) this;
        }

        public Criteria andDcBlockStock1LessThanOrEqualTo(BigDecimal value) {
            addCriterion("dc_block_stock1 <=", value, "dcBlockStock1");
            return (Criteria) this;
        }

        public Criteria andDcBlockStock1In(List<BigDecimal> values) {
            addCriterion("dc_block_stock1 in", values, "dcBlockStock1");
            return (Criteria) this;
        }

        public Criteria andDcBlockStock1NotIn(List<BigDecimal> values) {
            addCriterion("dc_block_stock1 not in", values, "dcBlockStock1");
            return (Criteria) this;
        }

        public Criteria andDcBlockStock1Between(BigDecimal value1, BigDecimal value2) {
            addCriterion("dc_block_stock1 between", value1, value2, "dcBlockStock1");
            return (Criteria) this;
        }

        public Criteria andDcBlockStock1NotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("dc_block_stock1 not between", value1, value2, "dcBlockStock1");
            return (Criteria) this;
        }

        public Criteria andDcBlockStock2IsNull() {
            addCriterion("dc_block_stock2 is null");
            return (Criteria) this;
        }

        public Criteria andDcBlockStock2IsNotNull() {
            addCriterion("dc_block_stock2 is not null");
            return (Criteria) this;
        }

        public Criteria andDcBlockStock2EqualTo(BigDecimal value) {
            addCriterion("dc_block_stock2 =", value, "dcBlockStock2");
            return (Criteria) this;
        }

        public Criteria andDcBlockStock2NotEqualTo(BigDecimal value) {
            addCriterion("dc_block_stock2 <>", value, "dcBlockStock2");
            return (Criteria) this;
        }

        public Criteria andDcBlockStock2GreaterThan(BigDecimal value) {
            addCriterion("dc_block_stock2 >", value, "dcBlockStock2");
            return (Criteria) this;
        }

        public Criteria andDcBlockStock2GreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("dc_block_stock2 >=", value, "dcBlockStock2");
            return (Criteria) this;
        }

        public Criteria andDcBlockStock2LessThan(BigDecimal value) {
            addCriterion("dc_block_stock2 <", value, "dcBlockStock2");
            return (Criteria) this;
        }

        public Criteria andDcBlockStock2LessThanOrEqualTo(BigDecimal value) {
            addCriterion("dc_block_stock2 <=", value, "dcBlockStock2");
            return (Criteria) this;
        }

        public Criteria andDcBlockStock2In(List<BigDecimal> values) {
            addCriterion("dc_block_stock2 in", values, "dcBlockStock2");
            return (Criteria) this;
        }

        public Criteria andDcBlockStock2NotIn(List<BigDecimal> values) {
            addCriterion("dc_block_stock2 not in", values, "dcBlockStock2");
            return (Criteria) this;
        }

        public Criteria andDcBlockStock2Between(BigDecimal value1, BigDecimal value2) {
            addCriterion("dc_block_stock2 between", value1, value2, "dcBlockStock2");
            return (Criteria) this;
        }

        public Criteria andDcBlockStock2NotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("dc_block_stock2 not between", value1, value2, "dcBlockStock2");
            return (Criteria) this;
        }

        public Criteria andDistDaysIsNull() {
            addCriterion("dist_days is null");
            return (Criteria) this;
        }

        public Criteria andDistDaysIsNotNull() {
            addCriterion("dist_days is not null");
            return (Criteria) this;
        }

        public Criteria andDistDaysEqualTo(BigDecimal value) {
            addCriterion("dist_days =", value, "distDays");
            return (Criteria) this;
        }

        public Criteria andDistDaysNotEqualTo(BigDecimal value) {
            addCriterion("dist_days <>", value, "distDays");
            return (Criteria) this;
        }

        public Criteria andDistDaysGreaterThan(BigDecimal value) {
            addCriterion("dist_days >", value, "distDays");
            return (Criteria) this;
        }

        public Criteria andDistDaysGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("dist_days >=", value, "distDays");
            return (Criteria) this;
        }

        public Criteria andDistDaysLessThan(BigDecimal value) {
            addCriterion("dist_days <", value, "distDays");
            return (Criteria) this;
        }

        public Criteria andDistDaysLessThanOrEqualTo(BigDecimal value) {
            addCriterion("dist_days <=", value, "distDays");
            return (Criteria) this;
        }

        public Criteria andDistDaysIn(List<BigDecimal> values) {
            addCriterion("dist_days in", values, "distDays");
            return (Criteria) this;
        }

        public Criteria andDistDaysNotIn(List<BigDecimal> values) {
            addCriterion("dist_days not in", values, "distDays");
            return (Criteria) this;
        }

        public Criteria andDistDaysBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("dist_days between", value1, value2, "distDays");
            return (Criteria) this;
        }

        public Criteria andDistDaysNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("dist_days not between", value1, value2, "distDays");
            return (Criteria) this;
        }

        public Criteria andSentTimeIsNull() {
            addCriterion("sent_time is null");
            return (Criteria) this;
        }

        public Criteria andSentTimeIsNotNull() {
            addCriterion("sent_time is not null");
            return (Criteria) this;
        }

        public Criteria andSentTimeEqualTo(String value) {
            addCriterion("sent_time =", value, "sentTime");
            return (Criteria) this;
        }

        public Criteria andSentTimeNotEqualTo(String value) {
            addCriterion("sent_time <>", value, "sentTime");
            return (Criteria) this;
        }

        public Criteria andSentTimeGreaterThan(String value) {
            addCriterion("sent_time >", value, "sentTime");
            return (Criteria) this;
        }

        public Criteria andSentTimeGreaterThanOrEqualTo(String value) {
            addCriterion("sent_time >=", value, "sentTime");
            return (Criteria) this;
        }

        public Criteria andSentTimeLessThan(String value) {
            addCriterion("sent_time <", value, "sentTime");
            return (Criteria) this;
        }

        public Criteria andSentTimeLessThanOrEqualTo(String value) {
            addCriterion("sent_time <=", value, "sentTime");
            return (Criteria) this;
        }

        public Criteria andSentTimeLike(String value) {
            addCriterion("sent_time like", value, "sentTime");
            return (Criteria) this;
        }

        public Criteria andSentTimeNotLike(String value) {
            addCriterion("sent_time not like", value, "sentTime");
            return (Criteria) this;
        }

        public Criteria andSentTimeIn(List<String> values) {
            addCriterion("sent_time in", values, "sentTime");
            return (Criteria) this;
        }

        public Criteria andSentTimeNotIn(List<String> values) {
            addCriterion("sent_time not in", values, "sentTime");
            return (Criteria) this;
        }

        public Criteria andSentTimeBetween(String value1, String value2) {
            addCriterion("sent_time between", value1, value2, "sentTime");
            return (Criteria) this;
        }

        public Criteria andSentTimeNotBetween(String value1, String value2) {
            addCriterion("sent_time not between", value1, value2, "sentTime");
            return (Criteria) this;
        }

        public Criteria andAmountBefore30IsNull() {
            addCriterion("amount_before_30 is null");
            return (Criteria) this;
        }

        public Criteria andAmountBefore30IsNotNull() {
            addCriterion("amount_before_30 is not null");
            return (Criteria) this;
        }

        public Criteria andAmountBefore30EqualTo(BigDecimal value) {
            addCriterion("amount_before_30 =", value, "amountBefore30");
            return (Criteria) this;
        }

        public Criteria andAmountBefore30NotEqualTo(BigDecimal value) {
            addCriterion("amount_before_30 <>", value, "amountBefore30");
            return (Criteria) this;
        }

        public Criteria andAmountBefore30GreaterThan(BigDecimal value) {
            addCriterion("amount_before_30 >", value, "amountBefore30");
            return (Criteria) this;
        }

        public Criteria andAmountBefore30GreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("amount_before_30 >=", value, "amountBefore30");
            return (Criteria) this;
        }

        public Criteria andAmountBefore30LessThan(BigDecimal value) {
            addCriterion("amount_before_30 <", value, "amountBefore30");
            return (Criteria) this;
        }

        public Criteria andAmountBefore30LessThanOrEqualTo(BigDecimal value) {
            addCriterion("amount_before_30 <=", value, "amountBefore30");
            return (Criteria) this;
        }

        public Criteria andAmountBefore30In(List<BigDecimal> values) {
            addCriterion("amount_before_30 in", values, "amountBefore30");
            return (Criteria) this;
        }

        public Criteria andAmountBefore30NotIn(List<BigDecimal> values) {
            addCriterion("amount_before_30 not in", values, "amountBefore30");
            return (Criteria) this;
        }

        public Criteria andAmountBefore30Between(BigDecimal value1, BigDecimal value2) {
            addCriterion("amount_before_30 between", value1, value2, "amountBefore30");
            return (Criteria) this;
        }

        public Criteria andAmountBefore30NotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("amount_before_30 not between", value1, value2, "amountBefore30");
            return (Criteria) this;
        }

        public Criteria andUnsatisfiedDeliverySkuIsNull() {
            addCriterion("unsatisfied_delivery_sku is null");
            return (Criteria) this;
        }

        public Criteria andUnsatisfiedDeliverySkuIsNotNull() {
            addCriterion("unsatisfied_delivery_sku is not null");
            return (Criteria) this;
        }

        public Criteria andUnsatisfiedDeliverySkuEqualTo(BigDecimal value) {
            addCriterion("unsatisfied_delivery_sku =", value, "unsatisfiedDeliverySku");
            return (Criteria) this;
        }

        public Criteria andUnsatisfiedDeliverySkuNotEqualTo(BigDecimal value) {
            addCriterion("unsatisfied_delivery_sku <>", value, "unsatisfiedDeliverySku");
            return (Criteria) this;
        }

        public Criteria andUnsatisfiedDeliverySkuGreaterThan(BigDecimal value) {
            addCriterion("unsatisfied_delivery_sku >", value, "unsatisfiedDeliverySku");
            return (Criteria) this;
        }

        public Criteria andUnsatisfiedDeliverySkuGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("unsatisfied_delivery_sku >=", value, "unsatisfiedDeliverySku");
            return (Criteria) this;
        }

        public Criteria andUnsatisfiedDeliverySkuLessThan(BigDecimal value) {
            addCriterion("unsatisfied_delivery_sku <", value, "unsatisfiedDeliverySku");
            return (Criteria) this;
        }

        public Criteria andUnsatisfiedDeliverySkuLessThanOrEqualTo(BigDecimal value) {
            addCriterion("unsatisfied_delivery_sku <=", value, "unsatisfiedDeliverySku");
            return (Criteria) this;
        }

        public Criteria andUnsatisfiedDeliverySkuIn(List<BigDecimal> values) {
            addCriterion("unsatisfied_delivery_sku in", values, "unsatisfiedDeliverySku");
            return (Criteria) this;
        }

        public Criteria andUnsatisfiedDeliverySkuNotIn(List<BigDecimal> values) {
            addCriterion("unsatisfied_delivery_sku not in", values, "unsatisfiedDeliverySku");
            return (Criteria) this;
        }

        public Criteria andUnsatisfiedDeliverySkuBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("unsatisfied_delivery_sku between", value1, value2, "unsatisfiedDeliverySku");
            return (Criteria) this;
        }

        public Criteria andUnsatisfiedDeliverySkuNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("unsatisfied_delivery_sku not between", value1, value2, "unsatisfiedDeliverySku");
            return (Criteria) this;
        }

        public Criteria andUnsatisfiedDeliveryCntIsNull() {
            addCriterion("unsatisfied_delivery_cnt is null");
            return (Criteria) this;
        }

        public Criteria andUnsatisfiedDeliveryCntIsNotNull() {
            addCriterion("unsatisfied_delivery_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andUnsatisfiedDeliveryCntEqualTo(BigDecimal value) {
            addCriterion("unsatisfied_delivery_cnt =", value, "unsatisfiedDeliveryCnt");
            return (Criteria) this;
        }

        public Criteria andUnsatisfiedDeliveryCntNotEqualTo(BigDecimal value) {
            addCriterion("unsatisfied_delivery_cnt <>", value, "unsatisfiedDeliveryCnt");
            return (Criteria) this;
        }

        public Criteria andUnsatisfiedDeliveryCntGreaterThan(BigDecimal value) {
            addCriterion("unsatisfied_delivery_cnt >", value, "unsatisfiedDeliveryCnt");
            return (Criteria) this;
        }

        public Criteria andUnsatisfiedDeliveryCntGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("unsatisfied_delivery_cnt >=", value, "unsatisfiedDeliveryCnt");
            return (Criteria) this;
        }

        public Criteria andUnsatisfiedDeliveryCntLessThan(BigDecimal value) {
            addCriterion("unsatisfied_delivery_cnt <", value, "unsatisfiedDeliveryCnt");
            return (Criteria) this;
        }

        public Criteria andUnsatisfiedDeliveryCntLessThanOrEqualTo(BigDecimal value) {
            addCriterion("unsatisfied_delivery_cnt <=", value, "unsatisfiedDeliveryCnt");
            return (Criteria) this;
        }

        public Criteria andUnsatisfiedDeliveryCntIn(List<BigDecimal> values) {
            addCriterion("unsatisfied_delivery_cnt in", values, "unsatisfiedDeliveryCnt");
            return (Criteria) this;
        }

        public Criteria andUnsatisfiedDeliveryCntNotIn(List<BigDecimal> values) {
            addCriterion("unsatisfied_delivery_cnt not in", values, "unsatisfiedDeliveryCnt");
            return (Criteria) this;
        }

        public Criteria andUnsatisfiedDeliveryCntBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("unsatisfied_delivery_cnt between", value1, value2, "unsatisfiedDeliveryCnt");
            return (Criteria) this;
        }

        public Criteria andUnsatisfiedDeliveryCntNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("unsatisfied_delivery_cnt not between", value1, value2, "unsatisfiedDeliveryCnt");
            return (Criteria) this;
        }

        public Criteria andWeiqingtuicangIsNull() {
            addCriterion("weiqingtuicang is null");
            return (Criteria) this;
        }

        public Criteria andWeiqingtuicangIsNotNull() {
            addCriterion("weiqingtuicang is not null");
            return (Criteria) this;
        }

        public Criteria andWeiqingtuicangEqualTo(String value) {
            addCriterion("weiqingtuicang =", value, "weiqingtuicang");
            return (Criteria) this;
        }

        public Criteria andWeiqingtuicangNotEqualTo(String value) {
            addCriterion("weiqingtuicang <>", value, "weiqingtuicang");
            return (Criteria) this;
        }

        public Criteria andWeiqingtuicangGreaterThan(String value) {
            addCriterion("weiqingtuicang >", value, "weiqingtuicang");
            return (Criteria) this;
        }

        public Criteria andWeiqingtuicangGreaterThanOrEqualTo(String value) {
            addCriterion("weiqingtuicang >=", value, "weiqingtuicang");
            return (Criteria) this;
        }

        public Criteria andWeiqingtuicangLessThan(String value) {
            addCriterion("weiqingtuicang <", value, "weiqingtuicang");
            return (Criteria) this;
        }

        public Criteria andWeiqingtuicangLessThanOrEqualTo(String value) {
            addCriterion("weiqingtuicang <=", value, "weiqingtuicang");
            return (Criteria) this;
        }

        public Criteria andWeiqingtuicangLike(String value) {
            addCriterion("weiqingtuicang like", value, "weiqingtuicang");
            return (Criteria) this;
        }

        public Criteria andWeiqingtuicangNotLike(String value) {
            addCriterion("weiqingtuicang not like", value, "weiqingtuicang");
            return (Criteria) this;
        }

        public Criteria andWeiqingtuicangIn(List<String> values) {
            addCriterion("weiqingtuicang in", values, "weiqingtuicang");
            return (Criteria) this;
        }

        public Criteria andWeiqingtuicangNotIn(List<String> values) {
            addCriterion("weiqingtuicang not in", values, "weiqingtuicang");
            return (Criteria) this;
        }

        public Criteria andWeiqingtuicangBetween(String value1, String value2) {
            addCriterion("weiqingtuicang between", value1, value2, "weiqingtuicang");
            return (Criteria) this;
        }

        public Criteria andWeiqingtuicangNotBetween(String value1, String value2) {
            addCriterion("weiqingtuicang not between", value1, value2, "weiqingtuicang");
            return (Criteria) this;
        }

        public Criteria andWeiqingjisuanIsNull() {
            addCriterion("weiqingjisuan is null");
            return (Criteria) this;
        }

        public Criteria andWeiqingjisuanIsNotNull() {
            addCriterion("weiqingjisuan is not null");
            return (Criteria) this;
        }

        public Criteria andWeiqingjisuanEqualTo(String value) {
            addCriterion("weiqingjisuan =", value, "weiqingjisuan");
            return (Criteria) this;
        }

        public Criteria andWeiqingjisuanNotEqualTo(String value) {
            addCriterion("weiqingjisuan <>", value, "weiqingjisuan");
            return (Criteria) this;
        }

        public Criteria andWeiqingjisuanGreaterThan(String value) {
            addCriterion("weiqingjisuan >", value, "weiqingjisuan");
            return (Criteria) this;
        }

        public Criteria andWeiqingjisuanGreaterThanOrEqualTo(String value) {
            addCriterion("weiqingjisuan >=", value, "weiqingjisuan");
            return (Criteria) this;
        }

        public Criteria andWeiqingjisuanLessThan(String value) {
            addCriterion("weiqingjisuan <", value, "weiqingjisuan");
            return (Criteria) this;
        }

        public Criteria andWeiqingjisuanLessThanOrEqualTo(String value) {
            addCriterion("weiqingjisuan <=", value, "weiqingjisuan");
            return (Criteria) this;
        }

        public Criteria andWeiqingjisuanLike(String value) {
            addCriterion("weiqingjisuan like", value, "weiqingjisuan");
            return (Criteria) this;
        }

        public Criteria andWeiqingjisuanNotLike(String value) {
            addCriterion("weiqingjisuan not like", value, "weiqingjisuan");
            return (Criteria) this;
        }

        public Criteria andWeiqingjisuanIn(List<String> values) {
            addCriterion("weiqingjisuan in", values, "weiqingjisuan");
            return (Criteria) this;
        }

        public Criteria andWeiqingjisuanNotIn(List<String> values) {
            addCriterion("weiqingjisuan not in", values, "weiqingjisuan");
            return (Criteria) this;
        }

        public Criteria andWeiqingjisuanBetween(String value1, String value2) {
            addCriterion("weiqingjisuan between", value1, value2, "weiqingjisuan");
            return (Criteria) this;
        }

        public Criteria andWeiqingjisuanNotBetween(String value1, String value2) {
            addCriterion("weiqingjisuan not between", value1, value2, "weiqingjisuan");
            return (Criteria) this;
        }

        public Criteria andMinPurchaseTypeIsNull() {
            addCriterion("min_purchase_type is null");
            return (Criteria) this;
        }

        public Criteria andMinPurchaseTypeIsNotNull() {
            addCriterion("min_purchase_type is not null");
            return (Criteria) this;
        }

        public Criteria andMinPurchaseTypeEqualTo(String value) {
            addCriterion("min_purchase_type =", value, "minPurchaseType");
            return (Criteria) this;
        }

        public Criteria andMinPurchaseTypeNotEqualTo(String value) {
            addCriterion("min_purchase_type <>", value, "minPurchaseType");
            return (Criteria) this;
        }

        public Criteria andMinPurchaseTypeGreaterThan(String value) {
            addCriterion("min_purchase_type >", value, "minPurchaseType");
            return (Criteria) this;
        }

        public Criteria andMinPurchaseTypeGreaterThanOrEqualTo(String value) {
            addCriterion("min_purchase_type >=", value, "minPurchaseType");
            return (Criteria) this;
        }

        public Criteria andMinPurchaseTypeLessThan(String value) {
            addCriterion("min_purchase_type <", value, "minPurchaseType");
            return (Criteria) this;
        }

        public Criteria andMinPurchaseTypeLessThanOrEqualTo(String value) {
            addCriterion("min_purchase_type <=", value, "minPurchaseType");
            return (Criteria) this;
        }

        public Criteria andMinPurchaseTypeLike(String value) {
            addCriterion("min_purchase_type like", value, "minPurchaseType");
            return (Criteria) this;
        }

        public Criteria andMinPurchaseTypeNotLike(String value) {
            addCriterion("min_purchase_type not like", value, "minPurchaseType");
            return (Criteria) this;
        }

        public Criteria andMinPurchaseTypeIn(List<String> values) {
            addCriterion("min_purchase_type in", values, "minPurchaseType");
            return (Criteria) this;
        }

        public Criteria andMinPurchaseTypeNotIn(List<String> values) {
            addCriterion("min_purchase_type not in", values, "minPurchaseType");
            return (Criteria) this;
        }

        public Criteria andMinPurchaseTypeBetween(String value1, String value2) {
            addCriterion("min_purchase_type between", value1, value2, "minPurchaseType");
            return (Criteria) this;
        }

        public Criteria andMinPurchaseTypeNotBetween(String value1, String value2) {
            addCriterion("min_purchase_type not between", value1, value2, "minPurchaseType");
            return (Criteria) this;
        }

        public Criteria andMinPurchasePriceIsNull() {
            addCriterion("min_purchase_price is null");
            return (Criteria) this;
        }

        public Criteria andMinPurchasePriceIsNotNull() {
            addCriterion("min_purchase_price is not null");
            return (Criteria) this;
        }

        public Criteria andMinPurchasePriceEqualTo(BigDecimal value) {
            addCriterion("min_purchase_price =", value, "minPurchasePrice");
            return (Criteria) this;
        }

        public Criteria andMinPurchasePriceNotEqualTo(BigDecimal value) {
            addCriterion("min_purchase_price <>", value, "minPurchasePrice");
            return (Criteria) this;
        }

        public Criteria andMinPurchasePriceGreaterThan(BigDecimal value) {
            addCriterion("min_purchase_price >", value, "minPurchasePrice");
            return (Criteria) this;
        }

        public Criteria andMinPurchasePriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("min_purchase_price >=", value, "minPurchasePrice");
            return (Criteria) this;
        }

        public Criteria andMinPurchasePriceLessThan(BigDecimal value) {
            addCriterion("min_purchase_price <", value, "minPurchasePrice");
            return (Criteria) this;
        }

        public Criteria andMinPurchasePriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("min_purchase_price <=", value, "minPurchasePrice");
            return (Criteria) this;
        }

        public Criteria andMinPurchasePriceIn(List<BigDecimal> values) {
            addCriterion("min_purchase_price in", values, "minPurchasePrice");
            return (Criteria) this;
        }

        public Criteria andMinPurchasePriceNotIn(List<BigDecimal> values) {
            addCriterion("min_purchase_price not in", values, "minPurchasePrice");
            return (Criteria) this;
        }

        public Criteria andMinPurchasePriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("min_purchase_price between", value1, value2, "minPurchasePrice");
            return (Criteria) this;
        }

        public Criteria andMinPurchasePriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("min_purchase_price not between", value1, value2, "minPurchasePrice");
            return (Criteria) this;
        }

        public Criteria andMinPurchaseDateIsNull() {
            addCriterion("min_purchase_date is null");
            return (Criteria) this;
        }

        public Criteria andMinPurchaseDateIsNotNull() {
            addCriterion("min_purchase_date is not null");
            return (Criteria) this;
        }

        public Criteria andMinPurchaseDateEqualTo(String value) {
            addCriterion("min_purchase_date =", value, "minPurchaseDate");
            return (Criteria) this;
        }

        public Criteria andMinPurchaseDateNotEqualTo(String value) {
            addCriterion("min_purchase_date <>", value, "minPurchaseDate");
            return (Criteria) this;
        }

        public Criteria andMinPurchaseDateGreaterThan(String value) {
            addCriterion("min_purchase_date >", value, "minPurchaseDate");
            return (Criteria) this;
        }

        public Criteria andMinPurchaseDateGreaterThanOrEqualTo(String value) {
            addCriterion("min_purchase_date >=", value, "minPurchaseDate");
            return (Criteria) this;
        }

        public Criteria andMinPurchaseDateLessThan(String value) {
            addCriterion("min_purchase_date <", value, "minPurchaseDate");
            return (Criteria) this;
        }

        public Criteria andMinPurchaseDateLessThanOrEqualTo(String value) {
            addCriterion("min_purchase_date <=", value, "minPurchaseDate");
            return (Criteria) this;
        }

        public Criteria andMinPurchaseDateLike(String value) {
            addCriterion("min_purchase_date like", value, "minPurchaseDate");
            return (Criteria) this;
        }

        public Criteria andMinPurchaseDateNotLike(String value) {
            addCriterion("min_purchase_date not like", value, "minPurchaseDate");
            return (Criteria) this;
        }

        public Criteria andMinPurchaseDateIn(List<String> values) {
            addCriterion("min_purchase_date in", values, "minPurchaseDate");
            return (Criteria) this;
        }

        public Criteria andMinPurchaseDateNotIn(List<String> values) {
            addCriterion("min_purchase_date not in", values, "minPurchaseDate");
            return (Criteria) this;
        }

        public Criteria andMinPurchaseDateBetween(String value1, String value2) {
            addCriterion("min_purchase_date between", value1, value2, "minPurchaseDate");
            return (Criteria) this;
        }

        public Criteria andMinPurchaseDateNotBetween(String value1, String value2) {
            addCriterion("min_purchase_date not between", value1, value2, "minPurchaseDate");
            return (Criteria) this;
        }

        public Criteria andZdcje1IsNull() {
            addCriterion("ZDCJE1 is null");
            return (Criteria) this;
        }

        public Criteria andZdcje1IsNotNull() {
            addCriterion("ZDCJE1 is not null");
            return (Criteria) this;
        }

        public Criteria andZdcje1EqualTo(BigDecimal value) {
            addCriterion("ZDCJE1 =", value, "zdcje1");
            return (Criteria) this;
        }

        public Criteria andZdcje1NotEqualTo(BigDecimal value) {
            addCriterion("ZDCJE1 <>", value, "zdcje1");
            return (Criteria) this;
        }

        public Criteria andZdcje1GreaterThan(BigDecimal value) {
            addCriterion("ZDCJE1 >", value, "zdcje1");
            return (Criteria) this;
        }

        public Criteria andZdcje1GreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("ZDCJE1 >=", value, "zdcje1");
            return (Criteria) this;
        }

        public Criteria andZdcje1LessThan(BigDecimal value) {
            addCriterion("ZDCJE1 <", value, "zdcje1");
            return (Criteria) this;
        }

        public Criteria andZdcje1LessThanOrEqualTo(BigDecimal value) {
            addCriterion("ZDCJE1 <=", value, "zdcje1");
            return (Criteria) this;
        }

        public Criteria andZdcje1In(List<BigDecimal> values) {
            addCriterion("ZDCJE1 in", values, "zdcje1");
            return (Criteria) this;
        }

        public Criteria andZdcje1NotIn(List<BigDecimal> values) {
            addCriterion("ZDCJE1 not in", values, "zdcje1");
            return (Criteria) this;
        }

        public Criteria andZdcje1Between(BigDecimal value1, BigDecimal value2) {
            addCriterion("ZDCJE1 between", value1, value2, "zdcje1");
            return (Criteria) this;
        }

        public Criteria andZdcje1NotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("ZDCJE1 not between", value1, value2, "zdcje1");
            return (Criteria) this;
        }

        public Criteria andZdcje2IsNull() {
            addCriterion("ZDCJE2 is null");
            return (Criteria) this;
        }

        public Criteria andZdcje2IsNotNull() {
            addCriterion("ZDCJE2 is not null");
            return (Criteria) this;
        }

        public Criteria andZdcje2EqualTo(BigDecimal value) {
            addCriterion("ZDCJE2 =", value, "zdcje2");
            return (Criteria) this;
        }

        public Criteria andZdcje2NotEqualTo(BigDecimal value) {
            addCriterion("ZDCJE2 <>", value, "zdcje2");
            return (Criteria) this;
        }

        public Criteria andZdcje2GreaterThan(BigDecimal value) {
            addCriterion("ZDCJE2 >", value, "zdcje2");
            return (Criteria) this;
        }

        public Criteria andZdcje2GreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("ZDCJE2 >=", value, "zdcje2");
            return (Criteria) this;
        }

        public Criteria andZdcje2LessThan(BigDecimal value) {
            addCriterion("ZDCJE2 <", value, "zdcje2");
            return (Criteria) this;
        }

        public Criteria andZdcje2LessThanOrEqualTo(BigDecimal value) {
            addCriterion("ZDCJE2 <=", value, "zdcje2");
            return (Criteria) this;
        }

        public Criteria andZdcje2In(List<BigDecimal> values) {
            addCriterion("ZDCJE2 in", values, "zdcje2");
            return (Criteria) this;
        }

        public Criteria andZdcje2NotIn(List<BigDecimal> values) {
            addCriterion("ZDCJE2 not in", values, "zdcje2");
            return (Criteria) this;
        }

        public Criteria andZdcje2Between(BigDecimal value1, BigDecimal value2) {
            addCriterion("ZDCJE2 between", value1, value2, "zdcje2");
            return (Criteria) this;
        }

        public Criteria andZdcje2NotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("ZDCJE2 not between", value1, value2, "zdcje2");
            return (Criteria) this;
        }

        public Criteria andZdcje3IsNull() {
            addCriterion("ZDCJE3 is null");
            return (Criteria) this;
        }

        public Criteria andZdcje3IsNotNull() {
            addCriterion("ZDCJE3 is not null");
            return (Criteria) this;
        }

        public Criteria andZdcje3EqualTo(BigDecimal value) {
            addCriterion("ZDCJE3 =", value, "zdcje3");
            return (Criteria) this;
        }

        public Criteria andZdcje3NotEqualTo(BigDecimal value) {
            addCriterion("ZDCJE3 <>", value, "zdcje3");
            return (Criteria) this;
        }

        public Criteria andZdcje3GreaterThan(BigDecimal value) {
            addCriterion("ZDCJE3 >", value, "zdcje3");
            return (Criteria) this;
        }

        public Criteria andZdcje3GreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("ZDCJE3 >=", value, "zdcje3");
            return (Criteria) this;
        }

        public Criteria andZdcje3LessThan(BigDecimal value) {
            addCriterion("ZDCJE3 <", value, "zdcje3");
            return (Criteria) this;
        }

        public Criteria andZdcje3LessThanOrEqualTo(BigDecimal value) {
            addCriterion("ZDCJE3 <=", value, "zdcje3");
            return (Criteria) this;
        }

        public Criteria andZdcje3In(List<BigDecimal> values) {
            addCriterion("ZDCJE3 in", values, "zdcje3");
            return (Criteria) this;
        }

        public Criteria andZdcje3NotIn(List<BigDecimal> values) {
            addCriterion("ZDCJE3 not in", values, "zdcje3");
            return (Criteria) this;
        }

        public Criteria andZdcje3Between(BigDecimal value1, BigDecimal value2) {
            addCriterion("ZDCJE3 between", value1, value2, "zdcje3");
            return (Criteria) this;
        }

        public Criteria andZdcje3NotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("ZDCJE3 not between", value1, value2, "zdcje3");
            return (Criteria) this;
        }

        public Criteria andZdccb1IsNull() {
            addCriterion("ZDCCB1 is null");
            return (Criteria) this;
        }

        public Criteria andZdccb1IsNotNull() {
            addCriterion("ZDCCB1 is not null");
            return (Criteria) this;
        }

        public Criteria andZdccb1EqualTo(BigDecimal value) {
            addCriterion("ZDCCB1 =", value, "zdccb1");
            return (Criteria) this;
        }

        public Criteria andZdccb1NotEqualTo(BigDecimal value) {
            addCriterion("ZDCCB1 <>", value, "zdccb1");
            return (Criteria) this;
        }

        public Criteria andZdccb1GreaterThan(BigDecimal value) {
            addCriterion("ZDCCB1 >", value, "zdccb1");
            return (Criteria) this;
        }

        public Criteria andZdccb1GreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("ZDCCB1 >=", value, "zdccb1");
            return (Criteria) this;
        }

        public Criteria andZdccb1LessThan(BigDecimal value) {
            addCriterion("ZDCCB1 <", value, "zdccb1");
            return (Criteria) this;
        }

        public Criteria andZdccb1LessThanOrEqualTo(BigDecimal value) {
            addCriterion("ZDCCB1 <=", value, "zdccb1");
            return (Criteria) this;
        }

        public Criteria andZdccb1In(List<BigDecimal> values) {
            addCriterion("ZDCCB1 in", values, "zdccb1");
            return (Criteria) this;
        }

        public Criteria andZdccb1NotIn(List<BigDecimal> values) {
            addCriterion("ZDCCB1 not in", values, "zdccb1");
            return (Criteria) this;
        }

        public Criteria andZdccb1Between(BigDecimal value1, BigDecimal value2) {
            addCriterion("ZDCCB1 between", value1, value2, "zdccb1");
            return (Criteria) this;
        }

        public Criteria andZdccb1NotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("ZDCCB1 not between", value1, value2, "zdccb1");
            return (Criteria) this;
        }

        public Criteria andZdccb2IsNull() {
            addCriterion("ZDCCB2 is null");
            return (Criteria) this;
        }

        public Criteria andZdccb2IsNotNull() {
            addCriterion("ZDCCB2 is not null");
            return (Criteria) this;
        }

        public Criteria andZdccb2EqualTo(BigDecimal value) {
            addCriterion("ZDCCB2 =", value, "zdccb2");
            return (Criteria) this;
        }

        public Criteria andZdccb2NotEqualTo(BigDecimal value) {
            addCriterion("ZDCCB2 <>", value, "zdccb2");
            return (Criteria) this;
        }

        public Criteria andZdccb2GreaterThan(BigDecimal value) {
            addCriterion("ZDCCB2 >", value, "zdccb2");
            return (Criteria) this;
        }

        public Criteria andZdccb2GreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("ZDCCB2 >=", value, "zdccb2");
            return (Criteria) this;
        }

        public Criteria andZdccb2LessThan(BigDecimal value) {
            addCriterion("ZDCCB2 <", value, "zdccb2");
            return (Criteria) this;
        }

        public Criteria andZdccb2LessThanOrEqualTo(BigDecimal value) {
            addCriterion("ZDCCB2 <=", value, "zdccb2");
            return (Criteria) this;
        }

        public Criteria andZdccb2In(List<BigDecimal> values) {
            addCriterion("ZDCCB2 in", values, "zdccb2");
            return (Criteria) this;
        }

        public Criteria andZdccb2NotIn(List<BigDecimal> values) {
            addCriterion("ZDCCB2 not in", values, "zdccb2");
            return (Criteria) this;
        }

        public Criteria andZdccb2Between(BigDecimal value1, BigDecimal value2) {
            addCriterion("ZDCCB2 between", value1, value2, "zdccb2");
            return (Criteria) this;
        }

        public Criteria andZdccb2NotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("ZDCCB2 not between", value1, value2, "zdccb2");
            return (Criteria) this;
        }

        public Criteria andZmdje1IsNull() {
            addCriterion("ZMDJE1 is null");
            return (Criteria) this;
        }

        public Criteria andZmdje1IsNotNull() {
            addCriterion("ZMDJE1 is not null");
            return (Criteria) this;
        }

        public Criteria andZmdje1EqualTo(BigDecimal value) {
            addCriterion("ZMDJE1 =", value, "zmdje1");
            return (Criteria) this;
        }

        public Criteria andZmdje1NotEqualTo(BigDecimal value) {
            addCriterion("ZMDJE1 <>", value, "zmdje1");
            return (Criteria) this;
        }

        public Criteria andZmdje1GreaterThan(BigDecimal value) {
            addCriterion("ZMDJE1 >", value, "zmdje1");
            return (Criteria) this;
        }

        public Criteria andZmdje1GreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("ZMDJE1 >=", value, "zmdje1");
            return (Criteria) this;
        }

        public Criteria andZmdje1LessThan(BigDecimal value) {
            addCriterion("ZMDJE1 <", value, "zmdje1");
            return (Criteria) this;
        }

        public Criteria andZmdje1LessThanOrEqualTo(BigDecimal value) {
            addCriterion("ZMDJE1 <=", value, "zmdje1");
            return (Criteria) this;
        }

        public Criteria andZmdje1In(List<BigDecimal> values) {
            addCriterion("ZMDJE1 in", values, "zmdje1");
            return (Criteria) this;
        }

        public Criteria andZmdje1NotIn(List<BigDecimal> values) {
            addCriterion("ZMDJE1 not in", values, "zmdje1");
            return (Criteria) this;
        }

        public Criteria andZmdje1Between(BigDecimal value1, BigDecimal value2) {
            addCriterion("ZMDJE1 between", value1, value2, "zmdje1");
            return (Criteria) this;
        }

        public Criteria andZmdje1NotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("ZMDJE1 not between", value1, value2, "zmdje1");
            return (Criteria) this;
        }

        public Criteria andZmdje2IsNull() {
            addCriterion("ZMDJE2 is null");
            return (Criteria) this;
        }

        public Criteria andZmdje2IsNotNull() {
            addCriterion("ZMDJE2 is not null");
            return (Criteria) this;
        }

        public Criteria andZmdje2EqualTo(BigDecimal value) {
            addCriterion("ZMDJE2 =", value, "zmdje2");
            return (Criteria) this;
        }

        public Criteria andZmdje2NotEqualTo(BigDecimal value) {
            addCriterion("ZMDJE2 <>", value, "zmdje2");
            return (Criteria) this;
        }

        public Criteria andZmdje2GreaterThan(BigDecimal value) {
            addCriterion("ZMDJE2 >", value, "zmdje2");
            return (Criteria) this;
        }

        public Criteria andZmdje2GreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("ZMDJE2 >=", value, "zmdje2");
            return (Criteria) this;
        }

        public Criteria andZmdje2LessThan(BigDecimal value) {
            addCriterion("ZMDJE2 <", value, "zmdje2");
            return (Criteria) this;
        }

        public Criteria andZmdje2LessThanOrEqualTo(BigDecimal value) {
            addCriterion("ZMDJE2 <=", value, "zmdje2");
            return (Criteria) this;
        }

        public Criteria andZmdje2In(List<BigDecimal> values) {
            addCriterion("ZMDJE2 in", values, "zmdje2");
            return (Criteria) this;
        }

        public Criteria andZmdje2NotIn(List<BigDecimal> values) {
            addCriterion("ZMDJE2 not in", values, "zmdje2");
            return (Criteria) this;
        }

        public Criteria andZmdje2Between(BigDecimal value1, BigDecimal value2) {
            addCriterion("ZMDJE2 between", value1, value2, "zmdje2");
            return (Criteria) this;
        }

        public Criteria andZmdje2NotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("ZMDJE2 not between", value1, value2, "zmdje2");
            return (Criteria) this;
        }

        public Criteria andZmdje3IsNull() {
            addCriterion("ZMDJE3 is null");
            return (Criteria) this;
        }

        public Criteria andZmdje3IsNotNull() {
            addCriterion("ZMDJE3 is not null");
            return (Criteria) this;
        }

        public Criteria andZmdje3EqualTo(BigDecimal value) {
            addCriterion("ZMDJE3 =", value, "zmdje3");
            return (Criteria) this;
        }

        public Criteria andZmdje3NotEqualTo(BigDecimal value) {
            addCriterion("ZMDJE3 <>", value, "zmdje3");
            return (Criteria) this;
        }

        public Criteria andZmdje3GreaterThan(BigDecimal value) {
            addCriterion("ZMDJE3 >", value, "zmdje3");
            return (Criteria) this;
        }

        public Criteria andZmdje3GreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("ZMDJE3 >=", value, "zmdje3");
            return (Criteria) this;
        }

        public Criteria andZmdje3LessThan(BigDecimal value) {
            addCriterion("ZMDJE3 <", value, "zmdje3");
            return (Criteria) this;
        }

        public Criteria andZmdje3LessThanOrEqualTo(BigDecimal value) {
            addCriterion("ZMDJE3 <=", value, "zmdje3");
            return (Criteria) this;
        }

        public Criteria andZmdje3In(List<BigDecimal> values) {
            addCriterion("ZMDJE3 in", values, "zmdje3");
            return (Criteria) this;
        }

        public Criteria andZmdje3NotIn(List<BigDecimal> values) {
            addCriterion("ZMDJE3 not in", values, "zmdje3");
            return (Criteria) this;
        }

        public Criteria andZmdje3Between(BigDecimal value1, BigDecimal value2) {
            addCriterion("ZMDJE3 between", value1, value2, "zmdje3");
            return (Criteria) this;
        }

        public Criteria andZmdje3NotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("ZMDJE3 not between", value1, value2, "zmdje3");
            return (Criteria) this;
        }

        public Criteria andZmdcbIsNull() {
            addCriterion("ZMDCB is null");
            return (Criteria) this;
        }

        public Criteria andZmdcbIsNotNull() {
            addCriterion("ZMDCB is not null");
            return (Criteria) this;
        }

        public Criteria andZmdcbEqualTo(BigDecimal value) {
            addCriterion("ZMDCB =", value, "zmdcb");
            return (Criteria) this;
        }

        public Criteria andZmdcbNotEqualTo(BigDecimal value) {
            addCriterion("ZMDCB <>", value, "zmdcb");
            return (Criteria) this;
        }

        public Criteria andZmdcbGreaterThan(BigDecimal value) {
            addCriterion("ZMDCB >", value, "zmdcb");
            return (Criteria) this;
        }

        public Criteria andZmdcbGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("ZMDCB >=", value, "zmdcb");
            return (Criteria) this;
        }

        public Criteria andZmdcbLessThan(BigDecimal value) {
            addCriterion("ZMDCB <", value, "zmdcb");
            return (Criteria) this;
        }

        public Criteria andZmdcbLessThanOrEqualTo(BigDecimal value) {
            addCriterion("ZMDCB <=", value, "zmdcb");
            return (Criteria) this;
        }

        public Criteria andZmdcbIn(List<BigDecimal> values) {
            addCriterion("ZMDCB in", values, "zmdcb");
            return (Criteria) this;
        }

        public Criteria andZmdcbNotIn(List<BigDecimal> values) {
            addCriterion("ZMDCB not in", values, "zmdcb");
            return (Criteria) this;
        }

        public Criteria andZmdcbBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("ZMDCB between", value1, value2, "zmdcb");
            return (Criteria) this;
        }

        public Criteria andZmdcbNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("ZMDCB not between", value1, value2, "zmdcb");
            return (Criteria) this;
        }

        public Criteria andZcdzztsIsNull() {
            addCriterion("ZCDZZTS is null");
            return (Criteria) this;
        }

        public Criteria andZcdzztsIsNotNull() {
            addCriterion("ZCDZZTS is not null");
            return (Criteria) this;
        }

        public Criteria andZcdzztsEqualTo(BigDecimal value) {
            addCriterion("ZCDZZTS =", value, "zcdzzts");
            return (Criteria) this;
        }

        public Criteria andZcdzztsNotEqualTo(BigDecimal value) {
            addCriterion("ZCDZZTS <>", value, "zcdzzts");
            return (Criteria) this;
        }

        public Criteria andZcdzztsGreaterThan(BigDecimal value) {
            addCriterion("ZCDZZTS >", value, "zcdzzts");
            return (Criteria) this;
        }

        public Criteria andZcdzztsGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("ZCDZZTS >=", value, "zcdzzts");
            return (Criteria) this;
        }

        public Criteria andZcdzztsLessThan(BigDecimal value) {
            addCriterion("ZCDZZTS <", value, "zcdzzts");
            return (Criteria) this;
        }

        public Criteria andZcdzztsLessThanOrEqualTo(BigDecimal value) {
            addCriterion("ZCDZZTS <=", value, "zcdzzts");
            return (Criteria) this;
        }

        public Criteria andZcdzztsIn(List<BigDecimal> values) {
            addCriterion("ZCDZZTS in", values, "zcdzzts");
            return (Criteria) this;
        }

        public Criteria andZcdzztsNotIn(List<BigDecimal> values) {
            addCriterion("ZCDZZTS not in", values, "zcdzzts");
            return (Criteria) this;
        }

        public Criteria andZcdzztsBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("ZCDZZTS between", value1, value2, "zcdzzts");
            return (Criteria) this;
        }

        public Criteria andZcdzztsNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("ZCDZZTS not between", value1, value2, "zcdzzts");
            return (Criteria) this;
        }

        public Criteria andZcdkcjepmIsNull() {
            addCriterion("ZCDKCJEPM is null");
            return (Criteria) this;
        }

        public Criteria andZcdkcjepmIsNotNull() {
            addCriterion("ZCDKCJEPM is not null");
            return (Criteria) this;
        }

        public Criteria andZcdkcjepmEqualTo(String value) {
            addCriterion("ZCDKCJEPM =", value, "zcdkcjepm");
            return (Criteria) this;
        }

        public Criteria andZcdkcjepmNotEqualTo(String value) {
            addCriterion("ZCDKCJEPM <>", value, "zcdkcjepm");
            return (Criteria) this;
        }

        public Criteria andZcdkcjepmGreaterThan(String value) {
            addCriterion("ZCDKCJEPM >", value, "zcdkcjepm");
            return (Criteria) this;
        }

        public Criteria andZcdkcjepmGreaterThanOrEqualTo(String value) {
            addCriterion("ZCDKCJEPM >=", value, "zcdkcjepm");
            return (Criteria) this;
        }

        public Criteria andZcdkcjepmLessThan(String value) {
            addCriterion("ZCDKCJEPM <", value, "zcdkcjepm");
            return (Criteria) this;
        }

        public Criteria andZcdkcjepmLessThanOrEqualTo(String value) {
            addCriterion("ZCDKCJEPM <=", value, "zcdkcjepm");
            return (Criteria) this;
        }

        public Criteria andZcdkcjepmLike(String value) {
            addCriterion("ZCDKCJEPM like", value, "zcdkcjepm");
            return (Criteria) this;
        }

        public Criteria andZcdkcjepmNotLike(String value) {
            addCriterion("ZCDKCJEPM not like", value, "zcdkcjepm");
            return (Criteria) this;
        }

        public Criteria andZcdkcjepmIn(List<String> values) {
            addCriterion("ZCDKCJEPM in", values, "zcdkcjepm");
            return (Criteria) this;
        }

        public Criteria andZcdkcjepmNotIn(List<String> values) {
            addCriterion("ZCDKCJEPM not in", values, "zcdkcjepm");
            return (Criteria) this;
        }

        public Criteria andZcdkcjepmBetween(String value1, String value2) {
            addCriterion("ZCDKCJEPM between", value1, value2, "zcdkcjepm");
            return (Criteria) this;
        }

        public Criteria andZcdkcjepmNotBetween(String value1, String value2) {
            addCriterion("ZCDKCJEPM not between", value1, value2, "zcdkcjepm");
            return (Criteria) this;
        }

        public Criteria andZdptIsNull() {
            addCriterion("ZDPT is null");
            return (Criteria) this;
        }

        public Criteria andZdptIsNotNull() {
            addCriterion("ZDPT is not null");
            return (Criteria) this;
        }

        public Criteria andZdptEqualTo(String value) {
            addCriterion("ZDPT =", value, "zdpt");
            return (Criteria) this;
        }

        public Criteria andZdptNotEqualTo(String value) {
            addCriterion("ZDPT <>", value, "zdpt");
            return (Criteria) this;
        }

        public Criteria andZdptGreaterThan(String value) {
            addCriterion("ZDPT >", value, "zdpt");
            return (Criteria) this;
        }

        public Criteria andZdptGreaterThanOrEqualTo(String value) {
            addCriterion("ZDPT >=", value, "zdpt");
            return (Criteria) this;
        }

        public Criteria andZdptLessThan(String value) {
            addCriterion("ZDPT <", value, "zdpt");
            return (Criteria) this;
        }

        public Criteria andZdptLessThanOrEqualTo(String value) {
            addCriterion("ZDPT <=", value, "zdpt");
            return (Criteria) this;
        }

        public Criteria andZdptLike(String value) {
            addCriterion("ZDPT like", value, "zdpt");
            return (Criteria) this;
        }

        public Criteria andZdptNotLike(String value) {
            addCriterion("ZDPT not like", value, "zdpt");
            return (Criteria) this;
        }

        public Criteria andZdptIn(List<String> values) {
            addCriterion("ZDPT in", values, "zdpt");
            return (Criteria) this;
        }

        public Criteria andZdptNotIn(List<String> values) {
            addCriterion("ZDPT not in", values, "zdpt");
            return (Criteria) this;
        }

        public Criteria andZdptBetween(String value1, String value2) {
            addCriterion("ZDPT between", value1, value2, "zdpt");
            return (Criteria) this;
        }

        public Criteria andZdptNotBetween(String value1, String value2) {
            addCriterion("ZDPT not between", value1, value2, "zdpt");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}