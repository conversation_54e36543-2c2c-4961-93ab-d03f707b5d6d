package com.cowell.iscm.entityTidb;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class IscmSuggestDistexecDetailExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Long offset;

    public IscmSuggestDistexecDetailExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Long offset) {
        this.offset = offset;
    }

    public Long getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAllotDateIsNull() {
            addCriterion("allot_date is null");
            return (Criteria) this;
        }

        public Criteria andAllotDateIsNotNull() {
            addCriterion("allot_date is not null");
            return (Criteria) this;
        }

        public Criteria andAllotDateEqualTo(Date value) {
            addCriterion("allot_date =", value, "allotDate");
            return (Criteria) this;
        }

        public Criteria andAllotDateNotEqualTo(Date value) {
            addCriterion("allot_date <>", value, "allotDate");
            return (Criteria) this;
        }

        public Criteria andAllotDateGreaterThan(Date value) {
            addCriterion("allot_date >", value, "allotDate");
            return (Criteria) this;
        }

        public Criteria andAllotDateGreaterThanOrEqualTo(Date value) {
            addCriterion("allot_date >=", value, "allotDate");
            return (Criteria) this;
        }

        public Criteria andAllotDateLessThan(Date value) {
            addCriterion("allot_date <", value, "allotDate");
            return (Criteria) this;
        }

        public Criteria andAllotDateLessThanOrEqualTo(Date value) {
            addCriterion("allot_date <=", value, "allotDate");
            return (Criteria) this;
        }

        public Criteria andAllotDateIn(List<Date> values) {
            addCriterion("allot_date in", values, "allotDate");
            return (Criteria) this;
        }

        public Criteria andAllotDateNotIn(List<Date> values) {
            addCriterion("allot_date not in", values, "allotDate");
            return (Criteria) this;
        }

        public Criteria andAllotDateBetween(Date value1, Date value2) {
            addCriterion("allot_date between", value1, value2, "allotDate");
            return (Criteria) this;
        }

        public Criteria andAllotDateNotBetween(Date value1, Date value2) {
            addCriterion("allot_date not between", value1, value2, "allotDate");
            return (Criteria) this;
        }

        public Criteria andAllotMonthIsNull() {
            addCriterion("allot_month is null");
            return (Criteria) this;
        }

        public Criteria andAllotMonthIsNotNull() {
            addCriterion("allot_month is not null");
            return (Criteria) this;
        }

        public Criteria andAllotMonthEqualTo(Integer value) {
            addCriterion("allot_month =", value, "allotMonth");
            return (Criteria) this;
        }

        public Criteria andAllotMonthNotEqualTo(Integer value) {
            addCriterion("allot_month <>", value, "allotMonth");
            return (Criteria) this;
        }

        public Criteria andAllotMonthGreaterThan(Integer value) {
            addCriterion("allot_month >", value, "allotMonth");
            return (Criteria) this;
        }

        public Criteria andAllotMonthGreaterThanOrEqualTo(Integer value) {
            addCriterion("allot_month >=", value, "allotMonth");
            return (Criteria) this;
        }

        public Criteria andAllotMonthLessThan(Integer value) {
            addCriterion("allot_month <", value, "allotMonth");
            return (Criteria) this;
        }

        public Criteria andAllotMonthLessThanOrEqualTo(Integer value) {
            addCriterion("allot_month <=", value, "allotMonth");
            return (Criteria) this;
        }

        public Criteria andAllotMonthIn(List<Integer> values) {
            addCriterion("allot_month in", values, "allotMonth");
            return (Criteria) this;
        }

        public Criteria andAllotMonthNotIn(List<Integer> values) {
            addCriterion("allot_month not in", values, "allotMonth");
            return (Criteria) this;
        }

        public Criteria andAllotMonthBetween(Integer value1, Integer value2) {
            addCriterion("allot_month between", value1, value2, "allotMonth");
            return (Criteria) this;
        }

        public Criteria andAllotMonthNotBetween(Integer value1, Integer value2) {
            addCriterion("allot_month not between", value1, value2, "allotMonth");
            return (Criteria) this;
        }

        public Criteria andRegisterNoIsNull() {
            addCriterion("register_no is null");
            return (Criteria) this;
        }

        public Criteria andRegisterNoIsNotNull() {
            addCriterion("register_no is not null");
            return (Criteria) this;
        }

        public Criteria andRegisterNoEqualTo(String value) {
            addCriterion("register_no =", value, "registerNo");
            return (Criteria) this;
        }

        public Criteria andRegisterNoNotEqualTo(String value) {
            addCriterion("register_no <>", value, "registerNo");
            return (Criteria) this;
        }

        public Criteria andRegisterNoGreaterThan(String value) {
            addCriterion("register_no >", value, "registerNo");
            return (Criteria) this;
        }

        public Criteria andRegisterNoGreaterThanOrEqualTo(String value) {
            addCriterion("register_no >=", value, "registerNo");
            return (Criteria) this;
        }

        public Criteria andRegisterNoLessThan(String value) {
            addCriterion("register_no <", value, "registerNo");
            return (Criteria) this;
        }

        public Criteria andRegisterNoLessThanOrEqualTo(String value) {
            addCriterion("register_no <=", value, "registerNo");
            return (Criteria) this;
        }

        public Criteria andRegisterNoLike(String value) {
            addCriterion("register_no like", value, "registerNo");
            return (Criteria) this;
        }

        public Criteria andRegisterNoNotLike(String value) {
            addCriterion("register_no not like", value, "registerNo");
            return (Criteria) this;
        }

        public Criteria andRegisterNoIn(List<String> values) {
            addCriterion("register_no in", values, "registerNo");
            return (Criteria) this;
        }

        public Criteria andRegisterNoNotIn(List<String> values) {
            addCriterion("register_no not in", values, "registerNo");
            return (Criteria) this;
        }

        public Criteria andRegisterNoBetween(String value1, String value2) {
            addCriterion("register_no between", value1, value2, "registerNo");
            return (Criteria) this;
        }

        public Criteria andRegisterNoNotBetween(String value1, String value2) {
            addCriterion("register_no not between", value1, value2, "registerNo");
            return (Criteria) this;
        }

        public Criteria andRegisterMonthIsNull() {
            addCriterion("register_month is null");
            return (Criteria) this;
        }

        public Criteria andRegisterMonthIsNotNull() {
            addCriterion("register_month is not null");
            return (Criteria) this;
        }

        public Criteria andRegisterMonthEqualTo(Integer value) {
            addCriterion("register_month =", value, "registerMonth");
            return (Criteria) this;
        }

        public Criteria andRegisterMonthNotEqualTo(Integer value) {
            addCriterion("register_month <>", value, "registerMonth");
            return (Criteria) this;
        }

        public Criteria andRegisterMonthGreaterThan(Integer value) {
            addCriterion("register_month >", value, "registerMonth");
            return (Criteria) this;
        }

        public Criteria andRegisterMonthGreaterThanOrEqualTo(Integer value) {
            addCriterion("register_month >=", value, "registerMonth");
            return (Criteria) this;
        }

        public Criteria andRegisterMonthLessThan(Integer value) {
            addCriterion("register_month <", value, "registerMonth");
            return (Criteria) this;
        }

        public Criteria andRegisterMonthLessThanOrEqualTo(Integer value) {
            addCriterion("register_month <=", value, "registerMonth");
            return (Criteria) this;
        }

        public Criteria andRegisterMonthIn(List<Integer> values) {
            addCriterion("register_month in", values, "registerMonth");
            return (Criteria) this;
        }

        public Criteria andRegisterMonthNotIn(List<Integer> values) {
            addCriterion("register_month not in", values, "registerMonth");
            return (Criteria) this;
        }

        public Criteria andRegisterMonthBetween(Integer value1, Integer value2) {
            addCriterion("register_month between", value1, value2, "registerMonth");
            return (Criteria) this;
        }

        public Criteria andRegisterMonthNotBetween(Integer value1, Integer value2) {
            addCriterion("register_month not between", value1, value2, "registerMonth");
            return (Criteria) this;
        }

        public Criteria andAllotTypeIsNull() {
            addCriterion("allot_type is null");
            return (Criteria) this;
        }

        public Criteria andAllotTypeIsNotNull() {
            addCriterion("allot_type is not null");
            return (Criteria) this;
        }

        public Criteria andAllotTypeEqualTo(Byte value) {
            addCriterion("allot_type =", value, "allotType");
            return (Criteria) this;
        }

        public Criteria andAllotTypeNotEqualTo(Byte value) {
            addCriterion("allot_type <>", value, "allotType");
            return (Criteria) this;
        }

        public Criteria andAllotTypeGreaterThan(Byte value) {
            addCriterion("allot_type >", value, "allotType");
            return (Criteria) this;
        }

        public Criteria andAllotTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("allot_type >=", value, "allotType");
            return (Criteria) this;
        }

        public Criteria andAllotTypeLessThan(Byte value) {
            addCriterion("allot_type <", value, "allotType");
            return (Criteria) this;
        }

        public Criteria andAllotTypeLessThanOrEqualTo(Byte value) {
            addCriterion("allot_type <=", value, "allotType");
            return (Criteria) this;
        }

        public Criteria andAllotTypeIn(List<Byte> values) {
            addCriterion("allot_type in", values, "allotType");
            return (Criteria) this;
        }

        public Criteria andAllotTypeNotIn(List<Byte> values) {
            addCriterion("allot_type not in", values, "allotType");
            return (Criteria) this;
        }

        public Criteria andAllotTypeBetween(Byte value1, Byte value2) {
            addCriterion("allot_type between", value1, value2, "allotType");
            return (Criteria) this;
        }

        public Criteria andAllotTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("allot_type not between", value1, value2, "allotType");
            return (Criteria) this;
        }

        public Criteria andPosAllotNoIsNull() {
            addCriterion("pos_allot_no is null");
            return (Criteria) this;
        }

        public Criteria andPosAllotNoIsNotNull() {
            addCriterion("pos_allot_no is not null");
            return (Criteria) this;
        }

        public Criteria andPosAllotNoEqualTo(String value) {
            addCriterion("pos_allot_no =", value, "posAllotNo");
            return (Criteria) this;
        }

        public Criteria andPosAllotNoNotEqualTo(String value) {
            addCriterion("pos_allot_no <>", value, "posAllotNo");
            return (Criteria) this;
        }

        public Criteria andPosAllotNoGreaterThan(String value) {
            addCriterion("pos_allot_no >", value, "posAllotNo");
            return (Criteria) this;
        }

        public Criteria andPosAllotNoGreaterThanOrEqualTo(String value) {
            addCriterion("pos_allot_no >=", value, "posAllotNo");
            return (Criteria) this;
        }

        public Criteria andPosAllotNoLessThan(String value) {
            addCriterion("pos_allot_no <", value, "posAllotNo");
            return (Criteria) this;
        }

        public Criteria andPosAllotNoLessThanOrEqualTo(String value) {
            addCriterion("pos_allot_no <=", value, "posAllotNo");
            return (Criteria) this;
        }

        public Criteria andPosAllotNoLike(String value) {
            addCriterion("pos_allot_no like", value, "posAllotNo");
            return (Criteria) this;
        }

        public Criteria andPosAllotNoNotLike(String value) {
            addCriterion("pos_allot_no not like", value, "posAllotNo");
            return (Criteria) this;
        }

        public Criteria andPosAllotNoIn(List<String> values) {
            addCriterion("pos_allot_no in", values, "posAllotNo");
            return (Criteria) this;
        }

        public Criteria andPosAllotNoNotIn(List<String> values) {
            addCriterion("pos_allot_no not in", values, "posAllotNo");
            return (Criteria) this;
        }

        public Criteria andPosAllotNoBetween(String value1, String value2) {
            addCriterion("pos_allot_no between", value1, value2, "posAllotNo");
            return (Criteria) this;
        }

        public Criteria andPosAllotNoNotBetween(String value1, String value2) {
            addCriterion("pos_allot_no not between", value1, value2, "posAllotNo");
            return (Criteria) this;
        }

        public Criteria andAllotApproveTimeIsNull() {
            addCriterion("allot_approve_time is null");
            return (Criteria) this;
        }

        public Criteria andAllotApproveTimeIsNotNull() {
            addCriterion("allot_approve_time is not null");
            return (Criteria) this;
        }

        public Criteria andAllotApproveTimeEqualTo(Date value) {
            addCriterion("allot_approve_time =", value, "allotApproveTime");
            return (Criteria) this;
        }

        public Criteria andAllotApproveTimeNotEqualTo(Date value) {
            addCriterion("allot_approve_time <>", value, "allotApproveTime");
            return (Criteria) this;
        }

        public Criteria andAllotApproveTimeGreaterThan(Date value) {
            addCriterion("allot_approve_time >", value, "allotApproveTime");
            return (Criteria) this;
        }

        public Criteria andAllotApproveTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("allot_approve_time >=", value, "allotApproveTime");
            return (Criteria) this;
        }

        public Criteria andAllotApproveTimeLessThan(Date value) {
            addCriterion("allot_approve_time <", value, "allotApproveTime");
            return (Criteria) this;
        }

        public Criteria andAllotApproveTimeLessThanOrEqualTo(Date value) {
            addCriterion("allot_approve_time <=", value, "allotApproveTime");
            return (Criteria) this;
        }

        public Criteria andAllotApproveTimeIn(List<Date> values) {
            addCriterion("allot_approve_time in", values, "allotApproveTime");
            return (Criteria) this;
        }

        public Criteria andAllotApproveTimeNotIn(List<Date> values) {
            addCriterion("allot_approve_time not in", values, "allotApproveTime");
            return (Criteria) this;
        }

        public Criteria andAllotApproveTimeBetween(Date value1, Date value2) {
            addCriterion("allot_approve_time between", value1, value2, "allotApproveTime");
            return (Criteria) this;
        }

        public Criteria andAllotApproveTimeNotBetween(Date value1, Date value2) {
            addCriterion("allot_approve_time not between", value1, value2, "allotApproveTime");
            return (Criteria) this;
        }

        public Criteria andPosAllotDetailIdIsNull() {
            addCriterion("pos_allot_detail_id is null");
            return (Criteria) this;
        }

        public Criteria andPosAllotDetailIdIsNotNull() {
            addCriterion("pos_allot_detail_id is not null");
            return (Criteria) this;
        }

        public Criteria andPosAllotDetailIdEqualTo(Long value) {
            addCriterion("pos_allot_detail_id =", value, "posAllotDetailId");
            return (Criteria) this;
        }

        public Criteria andPosAllotDetailIdNotEqualTo(Long value) {
            addCriterion("pos_allot_detail_id <>", value, "posAllotDetailId");
            return (Criteria) this;
        }

        public Criteria andPosAllotDetailIdGreaterThan(Long value) {
            addCriterion("pos_allot_detail_id >", value, "posAllotDetailId");
            return (Criteria) this;
        }

        public Criteria andPosAllotDetailIdGreaterThanOrEqualTo(Long value) {
            addCriterion("pos_allot_detail_id >=", value, "posAllotDetailId");
            return (Criteria) this;
        }

        public Criteria andPosAllotDetailIdLessThan(Long value) {
            addCriterion("pos_allot_detail_id <", value, "posAllotDetailId");
            return (Criteria) this;
        }

        public Criteria andPosAllotDetailIdLessThanOrEqualTo(Long value) {
            addCriterion("pos_allot_detail_id <=", value, "posAllotDetailId");
            return (Criteria) this;
        }

        public Criteria andPosAllotDetailIdIn(List<Long> values) {
            addCriterion("pos_allot_detail_id in", values, "posAllotDetailId");
            return (Criteria) this;
        }

        public Criteria andPosAllotDetailIdNotIn(List<Long> values) {
            addCriterion("pos_allot_detail_id not in", values, "posAllotDetailId");
            return (Criteria) this;
        }

        public Criteria andPosAllotDetailIdBetween(Long value1, Long value2) {
            addCriterion("pos_allot_detail_id between", value1, value2, "posAllotDetailId");
            return (Criteria) this;
        }

        public Criteria andPosAllotDetailIdNotBetween(Long value1, Long value2) {
            addCriterion("pos_allot_detail_id not between", value1, value2, "posAllotDetailId");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgIdIsNull() {
            addCriterion("platform_org_id is null");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgIdIsNotNull() {
            addCriterion("platform_org_id is not null");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgIdEqualTo(Long value) {
            addCriterion("platform_org_id =", value, "platformOrgId");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgIdNotEqualTo(Long value) {
            addCriterion("platform_org_id <>", value, "platformOrgId");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgIdGreaterThan(Long value) {
            addCriterion("platform_org_id >", value, "platformOrgId");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgIdGreaterThanOrEqualTo(Long value) {
            addCriterion("platform_org_id >=", value, "platformOrgId");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgIdLessThan(Long value) {
            addCriterion("platform_org_id <", value, "platformOrgId");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgIdLessThanOrEqualTo(Long value) {
            addCriterion("platform_org_id <=", value, "platformOrgId");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgIdIn(List<Long> values) {
            addCriterion("platform_org_id in", values, "platformOrgId");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgIdNotIn(List<Long> values) {
            addCriterion("platform_org_id not in", values, "platformOrgId");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgIdBetween(Long value1, Long value2) {
            addCriterion("platform_org_id between", value1, value2, "platformOrgId");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgIdNotBetween(Long value1, Long value2) {
            addCriterion("platform_org_id not between", value1, value2, "platformOrgId");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgNameIsNull() {
            addCriterion("platform_org_name is null");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgNameIsNotNull() {
            addCriterion("platform_org_name is not null");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgNameEqualTo(String value) {
            addCriterion("platform_org_name =", value, "platformOrgName");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgNameNotEqualTo(String value) {
            addCriterion("platform_org_name <>", value, "platformOrgName");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgNameGreaterThan(String value) {
            addCriterion("platform_org_name >", value, "platformOrgName");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgNameGreaterThanOrEqualTo(String value) {
            addCriterion("platform_org_name >=", value, "platformOrgName");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgNameLessThan(String value) {
            addCriterion("platform_org_name <", value, "platformOrgName");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgNameLessThanOrEqualTo(String value) {
            addCriterion("platform_org_name <=", value, "platformOrgName");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgNameLike(String value) {
            addCriterion("platform_org_name like", value, "platformOrgName");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgNameNotLike(String value) {
            addCriterion("platform_org_name not like", value, "platformOrgName");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgNameIn(List<String> values) {
            addCriterion("platform_org_name in", values, "platformOrgName");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgNameNotIn(List<String> values) {
            addCriterion("platform_org_name not in", values, "platformOrgName");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgNameBetween(String value1, String value2) {
            addCriterion("platform_org_name between", value1, value2, "platformOrgName");
            return (Criteria) this;
        }

        public Criteria andPlatformOrgNameNotBetween(String value1, String value2) {
            addCriterion("platform_org_name not between", value1, value2, "platformOrgName");
            return (Criteria) this;
        }

        public Criteria andOutCompanyIdIsNull() {
            addCriterion("out_company_id is null");
            return (Criteria) this;
        }

        public Criteria andOutCompanyIdIsNotNull() {
            addCriterion("out_company_id is not null");
            return (Criteria) this;
        }

        public Criteria andOutCompanyIdEqualTo(Long value) {
            addCriterion("out_company_id =", value, "outCompanyId");
            return (Criteria) this;
        }

        public Criteria andOutCompanyIdNotEqualTo(Long value) {
            addCriterion("out_company_id <>", value, "outCompanyId");
            return (Criteria) this;
        }

        public Criteria andOutCompanyIdGreaterThan(Long value) {
            addCriterion("out_company_id >", value, "outCompanyId");
            return (Criteria) this;
        }

        public Criteria andOutCompanyIdGreaterThanOrEqualTo(Long value) {
            addCriterion("out_company_id >=", value, "outCompanyId");
            return (Criteria) this;
        }

        public Criteria andOutCompanyIdLessThan(Long value) {
            addCriterion("out_company_id <", value, "outCompanyId");
            return (Criteria) this;
        }

        public Criteria andOutCompanyIdLessThanOrEqualTo(Long value) {
            addCriterion("out_company_id <=", value, "outCompanyId");
            return (Criteria) this;
        }

        public Criteria andOutCompanyIdIn(List<Long> values) {
            addCriterion("out_company_id in", values, "outCompanyId");
            return (Criteria) this;
        }

        public Criteria andOutCompanyIdNotIn(List<Long> values) {
            addCriterion("out_company_id not in", values, "outCompanyId");
            return (Criteria) this;
        }

        public Criteria andOutCompanyIdBetween(Long value1, Long value2) {
            addCriterion("out_company_id between", value1, value2, "outCompanyId");
            return (Criteria) this;
        }

        public Criteria andOutCompanyIdNotBetween(Long value1, Long value2) {
            addCriterion("out_company_id not between", value1, value2, "outCompanyId");
            return (Criteria) this;
        }

        public Criteria andOutCompanyCodeIsNull() {
            addCriterion("out_company_code is null");
            return (Criteria) this;
        }

        public Criteria andOutCompanyCodeIsNotNull() {
            addCriterion("out_company_code is not null");
            return (Criteria) this;
        }

        public Criteria andOutCompanyCodeEqualTo(String value) {
            addCriterion("out_company_code =", value, "outCompanyCode");
            return (Criteria) this;
        }

        public Criteria andOutCompanyCodeNotEqualTo(String value) {
            addCriterion("out_company_code <>", value, "outCompanyCode");
            return (Criteria) this;
        }

        public Criteria andOutCompanyCodeGreaterThan(String value) {
            addCriterion("out_company_code >", value, "outCompanyCode");
            return (Criteria) this;
        }

        public Criteria andOutCompanyCodeGreaterThanOrEqualTo(String value) {
            addCriterion("out_company_code >=", value, "outCompanyCode");
            return (Criteria) this;
        }

        public Criteria andOutCompanyCodeLessThan(String value) {
            addCriterion("out_company_code <", value, "outCompanyCode");
            return (Criteria) this;
        }

        public Criteria andOutCompanyCodeLessThanOrEqualTo(String value) {
            addCriterion("out_company_code <=", value, "outCompanyCode");
            return (Criteria) this;
        }

        public Criteria andOutCompanyCodeLike(String value) {
            addCriterion("out_company_code like", value, "outCompanyCode");
            return (Criteria) this;
        }

        public Criteria andOutCompanyCodeNotLike(String value) {
            addCriterion("out_company_code not like", value, "outCompanyCode");
            return (Criteria) this;
        }

        public Criteria andOutCompanyCodeIn(List<String> values) {
            addCriterion("out_company_code in", values, "outCompanyCode");
            return (Criteria) this;
        }

        public Criteria andOutCompanyCodeNotIn(List<String> values) {
            addCriterion("out_company_code not in", values, "outCompanyCode");
            return (Criteria) this;
        }

        public Criteria andOutCompanyCodeBetween(String value1, String value2) {
            addCriterion("out_company_code between", value1, value2, "outCompanyCode");
            return (Criteria) this;
        }

        public Criteria andOutCompanyCodeNotBetween(String value1, String value2) {
            addCriterion("out_company_code not between", value1, value2, "outCompanyCode");
            return (Criteria) this;
        }

        public Criteria andOutCompanyNameIsNull() {
            addCriterion("out_company_name is null");
            return (Criteria) this;
        }

        public Criteria andOutCompanyNameIsNotNull() {
            addCriterion("out_company_name is not null");
            return (Criteria) this;
        }

        public Criteria andOutCompanyNameEqualTo(String value) {
            addCriterion("out_company_name =", value, "outCompanyName");
            return (Criteria) this;
        }

        public Criteria andOutCompanyNameNotEqualTo(String value) {
            addCriterion("out_company_name <>", value, "outCompanyName");
            return (Criteria) this;
        }

        public Criteria andOutCompanyNameGreaterThan(String value) {
            addCriterion("out_company_name >", value, "outCompanyName");
            return (Criteria) this;
        }

        public Criteria andOutCompanyNameGreaterThanOrEqualTo(String value) {
            addCriterion("out_company_name >=", value, "outCompanyName");
            return (Criteria) this;
        }

        public Criteria andOutCompanyNameLessThan(String value) {
            addCriterion("out_company_name <", value, "outCompanyName");
            return (Criteria) this;
        }

        public Criteria andOutCompanyNameLessThanOrEqualTo(String value) {
            addCriterion("out_company_name <=", value, "outCompanyName");
            return (Criteria) this;
        }

        public Criteria andOutCompanyNameLike(String value) {
            addCriterion("out_company_name like", value, "outCompanyName");
            return (Criteria) this;
        }

        public Criteria andOutCompanyNameNotLike(String value) {
            addCriterion("out_company_name not like", value, "outCompanyName");
            return (Criteria) this;
        }

        public Criteria andOutCompanyNameIn(List<String> values) {
            addCriterion("out_company_name in", values, "outCompanyName");
            return (Criteria) this;
        }

        public Criteria andOutCompanyNameNotIn(List<String> values) {
            addCriterion("out_company_name not in", values, "outCompanyName");
            return (Criteria) this;
        }

        public Criteria andOutCompanyNameBetween(String value1, String value2) {
            addCriterion("out_company_name between", value1, value2, "outCompanyName");
            return (Criteria) this;
        }

        public Criteria andOutCompanyNameNotBetween(String value1, String value2) {
            addCriterion("out_company_name not between", value1, value2, "outCompanyName");
            return (Criteria) this;
        }

        public Criteria andInCompanyIdIsNull() {
            addCriterion("in_company_id is null");
            return (Criteria) this;
        }

        public Criteria andInCompanyIdIsNotNull() {
            addCriterion("in_company_id is not null");
            return (Criteria) this;
        }

        public Criteria andInCompanyIdEqualTo(Long value) {
            addCriterion("in_company_id =", value, "inCompanyId");
            return (Criteria) this;
        }

        public Criteria andInCompanyIdNotEqualTo(Long value) {
            addCriterion("in_company_id <>", value, "inCompanyId");
            return (Criteria) this;
        }

        public Criteria andInCompanyIdGreaterThan(Long value) {
            addCriterion("in_company_id >", value, "inCompanyId");
            return (Criteria) this;
        }

        public Criteria andInCompanyIdGreaterThanOrEqualTo(Long value) {
            addCriterion("in_company_id >=", value, "inCompanyId");
            return (Criteria) this;
        }

        public Criteria andInCompanyIdLessThan(Long value) {
            addCriterion("in_company_id <", value, "inCompanyId");
            return (Criteria) this;
        }

        public Criteria andInCompanyIdLessThanOrEqualTo(Long value) {
            addCriterion("in_company_id <=", value, "inCompanyId");
            return (Criteria) this;
        }

        public Criteria andInCompanyIdIn(List<Long> values) {
            addCriterion("in_company_id in", values, "inCompanyId");
            return (Criteria) this;
        }

        public Criteria andInCompanyIdNotIn(List<Long> values) {
            addCriterion("in_company_id not in", values, "inCompanyId");
            return (Criteria) this;
        }

        public Criteria andInCompanyIdBetween(Long value1, Long value2) {
            addCriterion("in_company_id between", value1, value2, "inCompanyId");
            return (Criteria) this;
        }

        public Criteria andInCompanyIdNotBetween(Long value1, Long value2) {
            addCriterion("in_company_id not between", value1, value2, "inCompanyId");
            return (Criteria) this;
        }

        public Criteria andInCompanyCodeIsNull() {
            addCriterion("in_company_code is null");
            return (Criteria) this;
        }

        public Criteria andInCompanyCodeIsNotNull() {
            addCriterion("in_company_code is not null");
            return (Criteria) this;
        }

        public Criteria andInCompanyCodeEqualTo(String value) {
            addCriterion("in_company_code =", value, "inCompanyCode");
            return (Criteria) this;
        }

        public Criteria andInCompanyCodeNotEqualTo(String value) {
            addCriterion("in_company_code <>", value, "inCompanyCode");
            return (Criteria) this;
        }

        public Criteria andInCompanyCodeGreaterThan(String value) {
            addCriterion("in_company_code >", value, "inCompanyCode");
            return (Criteria) this;
        }

        public Criteria andInCompanyCodeGreaterThanOrEqualTo(String value) {
            addCriterion("in_company_code >=", value, "inCompanyCode");
            return (Criteria) this;
        }

        public Criteria andInCompanyCodeLessThan(String value) {
            addCriterion("in_company_code <", value, "inCompanyCode");
            return (Criteria) this;
        }

        public Criteria andInCompanyCodeLessThanOrEqualTo(String value) {
            addCriterion("in_company_code <=", value, "inCompanyCode");
            return (Criteria) this;
        }

        public Criteria andInCompanyCodeLike(String value) {
            addCriterion("in_company_code like", value, "inCompanyCode");
            return (Criteria) this;
        }

        public Criteria andInCompanyCodeNotLike(String value) {
            addCriterion("in_company_code not like", value, "inCompanyCode");
            return (Criteria) this;
        }

        public Criteria andInCompanyCodeIn(List<String> values) {
            addCriterion("in_company_code in", values, "inCompanyCode");
            return (Criteria) this;
        }

        public Criteria andInCompanyCodeNotIn(List<String> values) {
            addCriterion("in_company_code not in", values, "inCompanyCode");
            return (Criteria) this;
        }

        public Criteria andInCompanyCodeBetween(String value1, String value2) {
            addCriterion("in_company_code between", value1, value2, "inCompanyCode");
            return (Criteria) this;
        }

        public Criteria andInCompanyCodeNotBetween(String value1, String value2) {
            addCriterion("in_company_code not between", value1, value2, "inCompanyCode");
            return (Criteria) this;
        }

        public Criteria andInCompanyNameIsNull() {
            addCriterion("in_company_name is null");
            return (Criteria) this;
        }

        public Criteria andInCompanyNameIsNotNull() {
            addCriterion("in_company_name is not null");
            return (Criteria) this;
        }

        public Criteria andInCompanyNameEqualTo(String value) {
            addCriterion("in_company_name =", value, "inCompanyName");
            return (Criteria) this;
        }

        public Criteria andInCompanyNameNotEqualTo(String value) {
            addCriterion("in_company_name <>", value, "inCompanyName");
            return (Criteria) this;
        }

        public Criteria andInCompanyNameGreaterThan(String value) {
            addCriterion("in_company_name >", value, "inCompanyName");
            return (Criteria) this;
        }

        public Criteria andInCompanyNameGreaterThanOrEqualTo(String value) {
            addCriterion("in_company_name >=", value, "inCompanyName");
            return (Criteria) this;
        }

        public Criteria andInCompanyNameLessThan(String value) {
            addCriterion("in_company_name <", value, "inCompanyName");
            return (Criteria) this;
        }

        public Criteria andInCompanyNameLessThanOrEqualTo(String value) {
            addCriterion("in_company_name <=", value, "inCompanyName");
            return (Criteria) this;
        }

        public Criteria andInCompanyNameLike(String value) {
            addCriterion("in_company_name like", value, "inCompanyName");
            return (Criteria) this;
        }

        public Criteria andInCompanyNameNotLike(String value) {
            addCriterion("in_company_name not like", value, "inCompanyName");
            return (Criteria) this;
        }

        public Criteria andInCompanyNameIn(List<String> values) {
            addCriterion("in_company_name in", values, "inCompanyName");
            return (Criteria) this;
        }

        public Criteria andInCompanyNameNotIn(List<String> values) {
            addCriterion("in_company_name not in", values, "inCompanyName");
            return (Criteria) this;
        }

        public Criteria andInCompanyNameBetween(String value1, String value2) {
            addCriterion("in_company_name between", value1, value2, "inCompanyName");
            return (Criteria) this;
        }

        public Criteria andInCompanyNameNotBetween(String value1, String value2) {
            addCriterion("in_company_name not between", value1, value2, "inCompanyName");
            return (Criteria) this;
        }

        public Criteria andOutStoreIdIsNull() {
            addCriterion("out_store_id is null");
            return (Criteria) this;
        }

        public Criteria andOutStoreIdIsNotNull() {
            addCriterion("out_store_id is not null");
            return (Criteria) this;
        }

        public Criteria andOutStoreIdEqualTo(Long value) {
            addCriterion("out_store_id =", value, "outStoreId");
            return (Criteria) this;
        }

        public Criteria andOutStoreIdNotEqualTo(Long value) {
            addCriterion("out_store_id <>", value, "outStoreId");
            return (Criteria) this;
        }

        public Criteria andOutStoreIdGreaterThan(Long value) {
            addCriterion("out_store_id >", value, "outStoreId");
            return (Criteria) this;
        }

        public Criteria andOutStoreIdGreaterThanOrEqualTo(Long value) {
            addCriterion("out_store_id >=", value, "outStoreId");
            return (Criteria) this;
        }

        public Criteria andOutStoreIdLessThan(Long value) {
            addCriterion("out_store_id <", value, "outStoreId");
            return (Criteria) this;
        }

        public Criteria andOutStoreIdLessThanOrEqualTo(Long value) {
            addCriterion("out_store_id <=", value, "outStoreId");
            return (Criteria) this;
        }

        public Criteria andOutStoreIdIn(List<Long> values) {
            addCriterion("out_store_id in", values, "outStoreId");
            return (Criteria) this;
        }

        public Criteria andOutStoreIdNotIn(List<Long> values) {
            addCriterion("out_store_id not in", values, "outStoreId");
            return (Criteria) this;
        }

        public Criteria andOutStoreIdBetween(Long value1, Long value2) {
            addCriterion("out_store_id between", value1, value2, "outStoreId");
            return (Criteria) this;
        }

        public Criteria andOutStoreIdNotBetween(Long value1, Long value2) {
            addCriterion("out_store_id not between", value1, value2, "outStoreId");
            return (Criteria) this;
        }

        public Criteria andOutStoreCodeIsNull() {
            addCriterion("out_store_code is null");
            return (Criteria) this;
        }

        public Criteria andOutStoreCodeIsNotNull() {
            addCriterion("out_store_code is not null");
            return (Criteria) this;
        }

        public Criteria andOutStoreCodeEqualTo(String value) {
            addCriterion("out_store_code =", value, "outStoreCode");
            return (Criteria) this;
        }

        public Criteria andOutStoreCodeNotEqualTo(String value) {
            addCriterion("out_store_code <>", value, "outStoreCode");
            return (Criteria) this;
        }

        public Criteria andOutStoreCodeGreaterThan(String value) {
            addCriterion("out_store_code >", value, "outStoreCode");
            return (Criteria) this;
        }

        public Criteria andOutStoreCodeGreaterThanOrEqualTo(String value) {
            addCriterion("out_store_code >=", value, "outStoreCode");
            return (Criteria) this;
        }

        public Criteria andOutStoreCodeLessThan(String value) {
            addCriterion("out_store_code <", value, "outStoreCode");
            return (Criteria) this;
        }

        public Criteria andOutStoreCodeLessThanOrEqualTo(String value) {
            addCriterion("out_store_code <=", value, "outStoreCode");
            return (Criteria) this;
        }

        public Criteria andOutStoreCodeLike(String value) {
            addCriterion("out_store_code like", value, "outStoreCode");
            return (Criteria) this;
        }

        public Criteria andOutStoreCodeNotLike(String value) {
            addCriterion("out_store_code not like", value, "outStoreCode");
            return (Criteria) this;
        }

        public Criteria andOutStoreCodeIn(List<String> values) {
            addCriterion("out_store_code in", values, "outStoreCode");
            return (Criteria) this;
        }

        public Criteria andOutStoreCodeNotIn(List<String> values) {
            addCriterion("out_store_code not in", values, "outStoreCode");
            return (Criteria) this;
        }

        public Criteria andOutStoreCodeBetween(String value1, String value2) {
            addCriterion("out_store_code between", value1, value2, "outStoreCode");
            return (Criteria) this;
        }

        public Criteria andOutStoreCodeNotBetween(String value1, String value2) {
            addCriterion("out_store_code not between", value1, value2, "outStoreCode");
            return (Criteria) this;
        }

        public Criteria andOutStoreNameIsNull() {
            addCriterion("out_store_name is null");
            return (Criteria) this;
        }

        public Criteria andOutStoreNameIsNotNull() {
            addCriterion("out_store_name is not null");
            return (Criteria) this;
        }

        public Criteria andOutStoreNameEqualTo(String value) {
            addCriterion("out_store_name =", value, "outStoreName");
            return (Criteria) this;
        }

        public Criteria andOutStoreNameNotEqualTo(String value) {
            addCriterion("out_store_name <>", value, "outStoreName");
            return (Criteria) this;
        }

        public Criteria andOutStoreNameGreaterThan(String value) {
            addCriterion("out_store_name >", value, "outStoreName");
            return (Criteria) this;
        }

        public Criteria andOutStoreNameGreaterThanOrEqualTo(String value) {
            addCriterion("out_store_name >=", value, "outStoreName");
            return (Criteria) this;
        }

        public Criteria andOutStoreNameLessThan(String value) {
            addCriterion("out_store_name <", value, "outStoreName");
            return (Criteria) this;
        }

        public Criteria andOutStoreNameLessThanOrEqualTo(String value) {
            addCriterion("out_store_name <=", value, "outStoreName");
            return (Criteria) this;
        }

        public Criteria andOutStoreNameLike(String value) {
            addCriterion("out_store_name like", value, "outStoreName");
            return (Criteria) this;
        }

        public Criteria andOutStoreNameNotLike(String value) {
            addCriterion("out_store_name not like", value, "outStoreName");
            return (Criteria) this;
        }

        public Criteria andOutStoreNameIn(List<String> values) {
            addCriterion("out_store_name in", values, "outStoreName");
            return (Criteria) this;
        }

        public Criteria andOutStoreNameNotIn(List<String> values) {
            addCriterion("out_store_name not in", values, "outStoreName");
            return (Criteria) this;
        }

        public Criteria andOutStoreNameBetween(String value1, String value2) {
            addCriterion("out_store_name between", value1, value2, "outStoreName");
            return (Criteria) this;
        }

        public Criteria andOutStoreNameNotBetween(String value1, String value2) {
            addCriterion("out_store_name not between", value1, value2, "outStoreName");
            return (Criteria) this;
        }

        public Criteria andOutStoreAttrIsNull() {
            addCriterion("out_store_attr is null");
            return (Criteria) this;
        }

        public Criteria andOutStoreAttrIsNotNull() {
            addCriterion("out_store_attr is not null");
            return (Criteria) this;
        }

        public Criteria andOutStoreAttrEqualTo(String value) {
            addCriterion("out_store_attr =", value, "outStoreAttr");
            return (Criteria) this;
        }

        public Criteria andOutStoreAttrNotEqualTo(String value) {
            addCriterion("out_store_attr <>", value, "outStoreAttr");
            return (Criteria) this;
        }

        public Criteria andOutStoreAttrGreaterThan(String value) {
            addCriterion("out_store_attr >", value, "outStoreAttr");
            return (Criteria) this;
        }

        public Criteria andOutStoreAttrGreaterThanOrEqualTo(String value) {
            addCriterion("out_store_attr >=", value, "outStoreAttr");
            return (Criteria) this;
        }

        public Criteria andOutStoreAttrLessThan(String value) {
            addCriterion("out_store_attr <", value, "outStoreAttr");
            return (Criteria) this;
        }

        public Criteria andOutStoreAttrLessThanOrEqualTo(String value) {
            addCriterion("out_store_attr <=", value, "outStoreAttr");
            return (Criteria) this;
        }

        public Criteria andOutStoreAttrLike(String value) {
            addCriterion("out_store_attr like", value, "outStoreAttr");
            return (Criteria) this;
        }

        public Criteria andOutStoreAttrNotLike(String value) {
            addCriterion("out_store_attr not like", value, "outStoreAttr");
            return (Criteria) this;
        }

        public Criteria andOutStoreAttrIn(List<String> values) {
            addCriterion("out_store_attr in", values, "outStoreAttr");
            return (Criteria) this;
        }

        public Criteria andOutStoreAttrNotIn(List<String> values) {
            addCriterion("out_store_attr not in", values, "outStoreAttr");
            return (Criteria) this;
        }

        public Criteria andOutStoreAttrBetween(String value1, String value2) {
            addCriterion("out_store_attr between", value1, value2, "outStoreAttr");
            return (Criteria) this;
        }

        public Criteria andOutStoreAttrNotBetween(String value1, String value2) {
            addCriterion("out_store_attr not between", value1, value2, "outStoreAttr");
            return (Criteria) this;
        }

        public Criteria andOutStoreSalesLevelIsNull() {
            addCriterion("out_store_sales_level is null");
            return (Criteria) this;
        }

        public Criteria andOutStoreSalesLevelIsNotNull() {
            addCriterion("out_store_sales_level is not null");
            return (Criteria) this;
        }

        public Criteria andOutStoreSalesLevelEqualTo(String value) {
            addCriterion("out_store_sales_level =", value, "outStoreSalesLevel");
            return (Criteria) this;
        }

        public Criteria andOutStoreSalesLevelNotEqualTo(String value) {
            addCriterion("out_store_sales_level <>", value, "outStoreSalesLevel");
            return (Criteria) this;
        }

        public Criteria andOutStoreSalesLevelGreaterThan(String value) {
            addCriterion("out_store_sales_level >", value, "outStoreSalesLevel");
            return (Criteria) this;
        }

        public Criteria andOutStoreSalesLevelGreaterThanOrEqualTo(String value) {
            addCriterion("out_store_sales_level >=", value, "outStoreSalesLevel");
            return (Criteria) this;
        }

        public Criteria andOutStoreSalesLevelLessThan(String value) {
            addCriterion("out_store_sales_level <", value, "outStoreSalesLevel");
            return (Criteria) this;
        }

        public Criteria andOutStoreSalesLevelLessThanOrEqualTo(String value) {
            addCriterion("out_store_sales_level <=", value, "outStoreSalesLevel");
            return (Criteria) this;
        }

        public Criteria andOutStoreSalesLevelLike(String value) {
            addCriterion("out_store_sales_level like", value, "outStoreSalesLevel");
            return (Criteria) this;
        }

        public Criteria andOutStoreSalesLevelNotLike(String value) {
            addCriterion("out_store_sales_level not like", value, "outStoreSalesLevel");
            return (Criteria) this;
        }

        public Criteria andOutStoreSalesLevelIn(List<String> values) {
            addCriterion("out_store_sales_level in", values, "outStoreSalesLevel");
            return (Criteria) this;
        }

        public Criteria andOutStoreSalesLevelNotIn(List<String> values) {
            addCriterion("out_store_sales_level not in", values, "outStoreSalesLevel");
            return (Criteria) this;
        }

        public Criteria andOutStoreSalesLevelBetween(String value1, String value2) {
            addCriterion("out_store_sales_level between", value1, value2, "outStoreSalesLevel");
            return (Criteria) this;
        }

        public Criteria andOutStoreSalesLevelNotBetween(String value1, String value2) {
            addCriterion("out_store_sales_level not between", value1, value2, "outStoreSalesLevel");
            return (Criteria) this;
        }

        public Criteria andInStoreIdIsNull() {
            addCriterion("in_store_id is null");
            return (Criteria) this;
        }

        public Criteria andInStoreIdIsNotNull() {
            addCriterion("in_store_id is not null");
            return (Criteria) this;
        }

        public Criteria andInStoreIdEqualTo(Long value) {
            addCriterion("in_store_id =", value, "inStoreId");
            return (Criteria) this;
        }

        public Criteria andInStoreIdNotEqualTo(Long value) {
            addCriterion("in_store_id <>", value, "inStoreId");
            return (Criteria) this;
        }

        public Criteria andInStoreIdGreaterThan(Long value) {
            addCriterion("in_store_id >", value, "inStoreId");
            return (Criteria) this;
        }

        public Criteria andInStoreIdGreaterThanOrEqualTo(Long value) {
            addCriterion("in_store_id >=", value, "inStoreId");
            return (Criteria) this;
        }

        public Criteria andInStoreIdLessThan(Long value) {
            addCriterion("in_store_id <", value, "inStoreId");
            return (Criteria) this;
        }

        public Criteria andInStoreIdLessThanOrEqualTo(Long value) {
            addCriterion("in_store_id <=", value, "inStoreId");
            return (Criteria) this;
        }

        public Criteria andInStoreIdIn(List<Long> values) {
            addCriterion("in_store_id in", values, "inStoreId");
            return (Criteria) this;
        }

        public Criteria andInStoreIdNotIn(List<Long> values) {
            addCriterion("in_store_id not in", values, "inStoreId");
            return (Criteria) this;
        }

        public Criteria andInStoreIdBetween(Long value1, Long value2) {
            addCriterion("in_store_id between", value1, value2, "inStoreId");
            return (Criteria) this;
        }

        public Criteria andInStoreIdNotBetween(Long value1, Long value2) {
            addCriterion("in_store_id not between", value1, value2, "inStoreId");
            return (Criteria) this;
        }

        public Criteria andInStoreCodeIsNull() {
            addCriterion("in_store_code is null");
            return (Criteria) this;
        }

        public Criteria andInStoreCodeIsNotNull() {
            addCriterion("in_store_code is not null");
            return (Criteria) this;
        }

        public Criteria andInStoreCodeEqualTo(String value) {
            addCriterion("in_store_code =", value, "inStoreCode");
            return (Criteria) this;
        }

        public Criteria andInStoreCodeNotEqualTo(String value) {
            addCriterion("in_store_code <>", value, "inStoreCode");
            return (Criteria) this;
        }

        public Criteria andInStoreCodeGreaterThan(String value) {
            addCriterion("in_store_code >", value, "inStoreCode");
            return (Criteria) this;
        }

        public Criteria andInStoreCodeGreaterThanOrEqualTo(String value) {
            addCriterion("in_store_code >=", value, "inStoreCode");
            return (Criteria) this;
        }

        public Criteria andInStoreCodeLessThan(String value) {
            addCriterion("in_store_code <", value, "inStoreCode");
            return (Criteria) this;
        }

        public Criteria andInStoreCodeLessThanOrEqualTo(String value) {
            addCriterion("in_store_code <=", value, "inStoreCode");
            return (Criteria) this;
        }

        public Criteria andInStoreCodeLike(String value) {
            addCriterion("in_store_code like", value, "inStoreCode");
            return (Criteria) this;
        }

        public Criteria andInStoreCodeNotLike(String value) {
            addCriterion("in_store_code not like", value, "inStoreCode");
            return (Criteria) this;
        }

        public Criteria andInStoreCodeIn(List<String> values) {
            addCriterion("in_store_code in", values, "inStoreCode");
            return (Criteria) this;
        }

        public Criteria andInStoreCodeNotIn(List<String> values) {
            addCriterion("in_store_code not in", values, "inStoreCode");
            return (Criteria) this;
        }

        public Criteria andInStoreCodeBetween(String value1, String value2) {
            addCriterion("in_store_code between", value1, value2, "inStoreCode");
            return (Criteria) this;
        }

        public Criteria andInStoreCodeNotBetween(String value1, String value2) {
            addCriterion("in_store_code not between", value1, value2, "inStoreCode");
            return (Criteria) this;
        }

        public Criteria andInStoreNameIsNull() {
            addCriterion("in_store_name is null");
            return (Criteria) this;
        }

        public Criteria andInStoreNameIsNotNull() {
            addCriterion("in_store_name is not null");
            return (Criteria) this;
        }

        public Criteria andInStoreNameEqualTo(String value) {
            addCriterion("in_store_name =", value, "inStoreName");
            return (Criteria) this;
        }

        public Criteria andInStoreNameNotEqualTo(String value) {
            addCriterion("in_store_name <>", value, "inStoreName");
            return (Criteria) this;
        }

        public Criteria andInStoreNameGreaterThan(String value) {
            addCriterion("in_store_name >", value, "inStoreName");
            return (Criteria) this;
        }

        public Criteria andInStoreNameGreaterThanOrEqualTo(String value) {
            addCriterion("in_store_name >=", value, "inStoreName");
            return (Criteria) this;
        }

        public Criteria andInStoreNameLessThan(String value) {
            addCriterion("in_store_name <", value, "inStoreName");
            return (Criteria) this;
        }

        public Criteria andInStoreNameLessThanOrEqualTo(String value) {
            addCriterion("in_store_name <=", value, "inStoreName");
            return (Criteria) this;
        }

        public Criteria andInStoreNameLike(String value) {
            addCriterion("in_store_name like", value, "inStoreName");
            return (Criteria) this;
        }

        public Criteria andInStoreNameNotLike(String value) {
            addCriterion("in_store_name not like", value, "inStoreName");
            return (Criteria) this;
        }

        public Criteria andInStoreNameIn(List<String> values) {
            addCriterion("in_store_name in", values, "inStoreName");
            return (Criteria) this;
        }

        public Criteria andInStoreNameNotIn(List<String> values) {
            addCriterion("in_store_name not in", values, "inStoreName");
            return (Criteria) this;
        }

        public Criteria andInStoreNameBetween(String value1, String value2) {
            addCriterion("in_store_name between", value1, value2, "inStoreName");
            return (Criteria) this;
        }

        public Criteria andInStoreNameNotBetween(String value1, String value2) {
            addCriterion("in_store_name not between", value1, value2, "inStoreName");
            return (Criteria) this;
        }

        public Criteria andInStoreAttrIsNull() {
            addCriterion("in_store_attr is null");
            return (Criteria) this;
        }

        public Criteria andInStoreAttrIsNotNull() {
            addCriterion("in_store_attr is not null");
            return (Criteria) this;
        }

        public Criteria andInStoreAttrEqualTo(String value) {
            addCriterion("in_store_attr =", value, "inStoreAttr");
            return (Criteria) this;
        }

        public Criteria andInStoreAttrNotEqualTo(String value) {
            addCriterion("in_store_attr <>", value, "inStoreAttr");
            return (Criteria) this;
        }

        public Criteria andInStoreAttrGreaterThan(String value) {
            addCriterion("in_store_attr >", value, "inStoreAttr");
            return (Criteria) this;
        }

        public Criteria andInStoreAttrGreaterThanOrEqualTo(String value) {
            addCriterion("in_store_attr >=", value, "inStoreAttr");
            return (Criteria) this;
        }

        public Criteria andInStoreAttrLessThan(String value) {
            addCriterion("in_store_attr <", value, "inStoreAttr");
            return (Criteria) this;
        }

        public Criteria andInStoreAttrLessThanOrEqualTo(String value) {
            addCriterion("in_store_attr <=", value, "inStoreAttr");
            return (Criteria) this;
        }

        public Criteria andInStoreAttrLike(String value) {
            addCriterion("in_store_attr like", value, "inStoreAttr");
            return (Criteria) this;
        }

        public Criteria andInStoreAttrNotLike(String value) {
            addCriterion("in_store_attr not like", value, "inStoreAttr");
            return (Criteria) this;
        }

        public Criteria andInStoreAttrIn(List<String> values) {
            addCriterion("in_store_attr in", values, "inStoreAttr");
            return (Criteria) this;
        }

        public Criteria andInStoreAttrNotIn(List<String> values) {
            addCriterion("in_store_attr not in", values, "inStoreAttr");
            return (Criteria) this;
        }

        public Criteria andInStoreAttrBetween(String value1, String value2) {
            addCriterion("in_store_attr between", value1, value2, "inStoreAttr");
            return (Criteria) this;
        }

        public Criteria andInStoreAttrNotBetween(String value1, String value2) {
            addCriterion("in_store_attr not between", value1, value2, "inStoreAttr");
            return (Criteria) this;
        }

        public Criteria andInStoreSalesLevelIsNull() {
            addCriterion("in_store_sales_level is null");
            return (Criteria) this;
        }

        public Criteria andInStoreSalesLevelIsNotNull() {
            addCriterion("in_store_sales_level is not null");
            return (Criteria) this;
        }

        public Criteria andInStoreSalesLevelEqualTo(String value) {
            addCriterion("in_store_sales_level =", value, "inStoreSalesLevel");
            return (Criteria) this;
        }

        public Criteria andInStoreSalesLevelNotEqualTo(String value) {
            addCriterion("in_store_sales_level <>", value, "inStoreSalesLevel");
            return (Criteria) this;
        }

        public Criteria andInStoreSalesLevelGreaterThan(String value) {
            addCriterion("in_store_sales_level >", value, "inStoreSalesLevel");
            return (Criteria) this;
        }

        public Criteria andInStoreSalesLevelGreaterThanOrEqualTo(String value) {
            addCriterion("in_store_sales_level >=", value, "inStoreSalesLevel");
            return (Criteria) this;
        }

        public Criteria andInStoreSalesLevelLessThan(String value) {
            addCriterion("in_store_sales_level <", value, "inStoreSalesLevel");
            return (Criteria) this;
        }

        public Criteria andInStoreSalesLevelLessThanOrEqualTo(String value) {
            addCriterion("in_store_sales_level <=", value, "inStoreSalesLevel");
            return (Criteria) this;
        }

        public Criteria andInStoreSalesLevelLike(String value) {
            addCriterion("in_store_sales_level like", value, "inStoreSalesLevel");
            return (Criteria) this;
        }

        public Criteria andInStoreSalesLevelNotLike(String value) {
            addCriterion("in_store_sales_level not like", value, "inStoreSalesLevel");
            return (Criteria) this;
        }

        public Criteria andInStoreSalesLevelIn(List<String> values) {
            addCriterion("in_store_sales_level in", values, "inStoreSalesLevel");
            return (Criteria) this;
        }

        public Criteria andInStoreSalesLevelNotIn(List<String> values) {
            addCriterion("in_store_sales_level not in", values, "inStoreSalesLevel");
            return (Criteria) this;
        }

        public Criteria andInStoreSalesLevelBetween(String value1, String value2) {
            addCriterion("in_store_sales_level between", value1, value2, "inStoreSalesLevel");
            return (Criteria) this;
        }

        public Criteria andInStoreSalesLevelNotBetween(String value1, String value2) {
            addCriterion("in_store_sales_level not between", value1, value2, "inStoreSalesLevel");
            return (Criteria) this;
        }

        public Criteria andAllotGroupCodeIsNull() {
            addCriterion("allot_group_code is null");
            return (Criteria) this;
        }

        public Criteria andAllotGroupCodeIsNotNull() {
            addCriterion("allot_group_code is not null");
            return (Criteria) this;
        }

        public Criteria andAllotGroupCodeEqualTo(String value) {
            addCriterion("allot_group_code =", value, "allotGroupCode");
            return (Criteria) this;
        }

        public Criteria andAllotGroupCodeNotEqualTo(String value) {
            addCriterion("allot_group_code <>", value, "allotGroupCode");
            return (Criteria) this;
        }

        public Criteria andAllotGroupCodeGreaterThan(String value) {
            addCriterion("allot_group_code >", value, "allotGroupCode");
            return (Criteria) this;
        }

        public Criteria andAllotGroupCodeGreaterThanOrEqualTo(String value) {
            addCriterion("allot_group_code >=", value, "allotGroupCode");
            return (Criteria) this;
        }

        public Criteria andAllotGroupCodeLessThan(String value) {
            addCriterion("allot_group_code <", value, "allotGroupCode");
            return (Criteria) this;
        }

        public Criteria andAllotGroupCodeLessThanOrEqualTo(String value) {
            addCriterion("allot_group_code <=", value, "allotGroupCode");
            return (Criteria) this;
        }

        public Criteria andAllotGroupCodeLike(String value) {
            addCriterion("allot_group_code like", value, "allotGroupCode");
            return (Criteria) this;
        }

        public Criteria andAllotGroupCodeNotLike(String value) {
            addCriterion("allot_group_code not like", value, "allotGroupCode");
            return (Criteria) this;
        }

        public Criteria andAllotGroupCodeIn(List<String> values) {
            addCriterion("allot_group_code in", values, "allotGroupCode");
            return (Criteria) this;
        }

        public Criteria andAllotGroupCodeNotIn(List<String> values) {
            addCriterion("allot_group_code not in", values, "allotGroupCode");
            return (Criteria) this;
        }

        public Criteria andAllotGroupCodeBetween(String value1, String value2) {
            addCriterion("allot_group_code between", value1, value2, "allotGroupCode");
            return (Criteria) this;
        }

        public Criteria andAllotGroupCodeNotBetween(String value1, String value2) {
            addCriterion("allot_group_code not between", value1, value2, "allotGroupCode");
            return (Criteria) this;
        }

        public Criteria andAllotGroupNameIsNull() {
            addCriterion("allot_group_name is null");
            return (Criteria) this;
        }

        public Criteria andAllotGroupNameIsNotNull() {
            addCriterion("allot_group_name is not null");
            return (Criteria) this;
        }

        public Criteria andAllotGroupNameEqualTo(String value) {
            addCriterion("allot_group_name =", value, "allotGroupName");
            return (Criteria) this;
        }

        public Criteria andAllotGroupNameNotEqualTo(String value) {
            addCriterion("allot_group_name <>", value, "allotGroupName");
            return (Criteria) this;
        }

        public Criteria andAllotGroupNameGreaterThan(String value) {
            addCriterion("allot_group_name >", value, "allotGroupName");
            return (Criteria) this;
        }

        public Criteria andAllotGroupNameGreaterThanOrEqualTo(String value) {
            addCriterion("allot_group_name >=", value, "allotGroupName");
            return (Criteria) this;
        }

        public Criteria andAllotGroupNameLessThan(String value) {
            addCriterion("allot_group_name <", value, "allotGroupName");
            return (Criteria) this;
        }

        public Criteria andAllotGroupNameLessThanOrEqualTo(String value) {
            addCriterion("allot_group_name <=", value, "allotGroupName");
            return (Criteria) this;
        }

        public Criteria andAllotGroupNameLike(String value) {
            addCriterion("allot_group_name like", value, "allotGroupName");
            return (Criteria) this;
        }

        public Criteria andAllotGroupNameNotLike(String value) {
            addCriterion("allot_group_name not like", value, "allotGroupName");
            return (Criteria) this;
        }

        public Criteria andAllotGroupNameIn(List<String> values) {
            addCriterion("allot_group_name in", values, "allotGroupName");
            return (Criteria) this;
        }

        public Criteria andAllotGroupNameNotIn(List<String> values) {
            addCriterion("allot_group_name not in", values, "allotGroupName");
            return (Criteria) this;
        }

        public Criteria andAllotGroupNameBetween(String value1, String value2) {
            addCriterion("allot_group_name between", value1, value2, "allotGroupName");
            return (Criteria) this;
        }

        public Criteria andAllotGroupNameNotBetween(String value1, String value2) {
            addCriterion("allot_group_name not between", value1, value2, "allotGroupName");
            return (Criteria) this;
        }

        public Criteria andApproveNameIsNull() {
            addCriterion("approve_name is null");
            return (Criteria) this;
        }

        public Criteria andApproveNameIsNotNull() {
            addCriterion("approve_name is not null");
            return (Criteria) this;
        }

        public Criteria andApproveNameEqualTo(String value) {
            addCriterion("approve_name =", value, "approveName");
            return (Criteria) this;
        }

        public Criteria andApproveNameNotEqualTo(String value) {
            addCriterion("approve_name <>", value, "approveName");
            return (Criteria) this;
        }

        public Criteria andApproveNameGreaterThan(String value) {
            addCriterion("approve_name >", value, "approveName");
            return (Criteria) this;
        }

        public Criteria andApproveNameGreaterThanOrEqualTo(String value) {
            addCriterion("approve_name >=", value, "approveName");
            return (Criteria) this;
        }

        public Criteria andApproveNameLessThan(String value) {
            addCriterion("approve_name <", value, "approveName");
            return (Criteria) this;
        }

        public Criteria andApproveNameLessThanOrEqualTo(String value) {
            addCriterion("approve_name <=", value, "approveName");
            return (Criteria) this;
        }

        public Criteria andApproveNameLike(String value) {
            addCriterion("approve_name like", value, "approveName");
            return (Criteria) this;
        }

        public Criteria andApproveNameNotLike(String value) {
            addCriterion("approve_name not like", value, "approveName");
            return (Criteria) this;
        }

        public Criteria andApproveNameIn(List<String> values) {
            addCriterion("approve_name in", values, "approveName");
            return (Criteria) this;
        }

        public Criteria andApproveNameNotIn(List<String> values) {
            addCriterion("approve_name not in", values, "approveName");
            return (Criteria) this;
        }

        public Criteria andApproveNameBetween(String value1, String value2) {
            addCriterion("approve_name between", value1, value2, "approveName");
            return (Criteria) this;
        }

        public Criteria andApproveNameNotBetween(String value1, String value2) {
            addCriterion("approve_name not between", value1, value2, "approveName");
            return (Criteria) this;
        }

        public Criteria andGoodsNoIsNull() {
            addCriterion("goods_no is null");
            return (Criteria) this;
        }

        public Criteria andGoodsNoIsNotNull() {
            addCriterion("goods_no is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsNoEqualTo(String value) {
            addCriterion("goods_no =", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotEqualTo(String value) {
            addCriterion("goods_no <>", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoGreaterThan(String value) {
            addCriterion("goods_no >", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoGreaterThanOrEqualTo(String value) {
            addCriterion("goods_no >=", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoLessThan(String value) {
            addCriterion("goods_no <", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoLessThanOrEqualTo(String value) {
            addCriterion("goods_no <=", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoLike(String value) {
            addCriterion("goods_no like", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotLike(String value) {
            addCriterion("goods_no not like", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoIn(List<String> values) {
            addCriterion("goods_no in", values, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotIn(List<String> values) {
            addCriterion("goods_no not in", values, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoBetween(String value1, String value2) {
            addCriterion("goods_no between", value1, value2, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotBetween(String value1, String value2) {
            addCriterion("goods_no not between", value1, value2, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsDescIsNull() {
            addCriterion("goods_desc is null");
            return (Criteria) this;
        }

        public Criteria andGoodsDescIsNotNull() {
            addCriterion("goods_desc is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsDescEqualTo(String value) {
            addCriterion("goods_desc =", value, "goodsDesc");
            return (Criteria) this;
        }

        public Criteria andGoodsDescNotEqualTo(String value) {
            addCriterion("goods_desc <>", value, "goodsDesc");
            return (Criteria) this;
        }

        public Criteria andGoodsDescGreaterThan(String value) {
            addCriterion("goods_desc >", value, "goodsDesc");
            return (Criteria) this;
        }

        public Criteria andGoodsDescGreaterThanOrEqualTo(String value) {
            addCriterion("goods_desc >=", value, "goodsDesc");
            return (Criteria) this;
        }

        public Criteria andGoodsDescLessThan(String value) {
            addCriterion("goods_desc <", value, "goodsDesc");
            return (Criteria) this;
        }

        public Criteria andGoodsDescLessThanOrEqualTo(String value) {
            addCriterion("goods_desc <=", value, "goodsDesc");
            return (Criteria) this;
        }

        public Criteria andGoodsDescLike(String value) {
            addCriterion("goods_desc like", value, "goodsDesc");
            return (Criteria) this;
        }

        public Criteria andGoodsDescNotLike(String value) {
            addCriterion("goods_desc not like", value, "goodsDesc");
            return (Criteria) this;
        }

        public Criteria andGoodsDescIn(List<String> values) {
            addCriterion("goods_desc in", values, "goodsDesc");
            return (Criteria) this;
        }

        public Criteria andGoodsDescNotIn(List<String> values) {
            addCriterion("goods_desc not in", values, "goodsDesc");
            return (Criteria) this;
        }

        public Criteria andGoodsDescBetween(String value1, String value2) {
            addCriterion("goods_desc between", value1, value2, "goodsDesc");
            return (Criteria) this;
        }

        public Criteria andGoodsDescNotBetween(String value1, String value2) {
            addCriterion("goods_desc not between", value1, value2, "goodsDesc");
            return (Criteria) this;
        }

        public Criteria andBatchNoIsNull() {
            addCriterion("batch_no is null");
            return (Criteria) this;
        }

        public Criteria andBatchNoIsNotNull() {
            addCriterion("batch_no is not null");
            return (Criteria) this;
        }

        public Criteria andBatchNoEqualTo(String value) {
            addCriterion("batch_no =", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoNotEqualTo(String value) {
            addCriterion("batch_no <>", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoGreaterThan(String value) {
            addCriterion("batch_no >", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoGreaterThanOrEqualTo(String value) {
            addCriterion("batch_no >=", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoLessThan(String value) {
            addCriterion("batch_no <", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoLessThanOrEqualTo(String value) {
            addCriterion("batch_no <=", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoLike(String value) {
            addCriterion("batch_no like", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoNotLike(String value) {
            addCriterion("batch_no not like", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoIn(List<String> values) {
            addCriterion("batch_no in", values, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoNotIn(List<String> values) {
            addCriterion("batch_no not in", values, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoBetween(String value1, String value2) {
            addCriterion("batch_no between", value1, value2, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoNotBetween(String value1, String value2) {
            addCriterion("batch_no not between", value1, value2, "batchNo");
            return (Criteria) this;
        }

        public Criteria andProduceDateIsNull() {
            addCriterion("produce_date is null");
            return (Criteria) this;
        }

        public Criteria andProduceDateIsNotNull() {
            addCriterion("produce_date is not null");
            return (Criteria) this;
        }

        public Criteria andProduceDateEqualTo(String value) {
            addCriterion("produce_date =", value, "produceDate");
            return (Criteria) this;
        }

        public Criteria andProduceDateNotEqualTo(String value) {
            addCriterion("produce_date <>", value, "produceDate");
            return (Criteria) this;
        }

        public Criteria andProduceDateGreaterThan(String value) {
            addCriterion("produce_date >", value, "produceDate");
            return (Criteria) this;
        }

        public Criteria andProduceDateGreaterThanOrEqualTo(String value) {
            addCriterion("produce_date >=", value, "produceDate");
            return (Criteria) this;
        }

        public Criteria andProduceDateLessThan(String value) {
            addCriterion("produce_date <", value, "produceDate");
            return (Criteria) this;
        }

        public Criteria andProduceDateLessThanOrEqualTo(String value) {
            addCriterion("produce_date <=", value, "produceDate");
            return (Criteria) this;
        }

        public Criteria andProduceDateLike(String value) {
            addCriterion("produce_date like", value, "produceDate");
            return (Criteria) this;
        }

        public Criteria andProduceDateNotLike(String value) {
            addCriterion("produce_date not like", value, "produceDate");
            return (Criteria) this;
        }

        public Criteria andProduceDateIn(List<String> values) {
            addCriterion("produce_date in", values, "produceDate");
            return (Criteria) this;
        }

        public Criteria andProduceDateNotIn(List<String> values) {
            addCriterion("produce_date not in", values, "produceDate");
            return (Criteria) this;
        }

        public Criteria andProduceDateBetween(String value1, String value2) {
            addCriterion("produce_date between", value1, value2, "produceDate");
            return (Criteria) this;
        }

        public Criteria andProduceDateNotBetween(String value1, String value2) {
            addCriterion("produce_date not between", value1, value2, "produceDate");
            return (Criteria) this;
        }

        public Criteria andValidityDateIsNull() {
            addCriterion("validity_date is null");
            return (Criteria) this;
        }

        public Criteria andValidityDateIsNotNull() {
            addCriterion("validity_date is not null");
            return (Criteria) this;
        }

        public Criteria andValidityDateEqualTo(String value) {
            addCriterion("validity_date =", value, "validityDate");
            return (Criteria) this;
        }

        public Criteria andValidityDateNotEqualTo(String value) {
            addCriterion("validity_date <>", value, "validityDate");
            return (Criteria) this;
        }

        public Criteria andValidityDateGreaterThan(String value) {
            addCriterion("validity_date >", value, "validityDate");
            return (Criteria) this;
        }

        public Criteria andValidityDateGreaterThanOrEqualTo(String value) {
            addCriterion("validity_date >=", value, "validityDate");
            return (Criteria) this;
        }

        public Criteria andValidityDateLessThan(String value) {
            addCriterion("validity_date <", value, "validityDate");
            return (Criteria) this;
        }

        public Criteria andValidityDateLessThanOrEqualTo(String value) {
            addCriterion("validity_date <=", value, "validityDate");
            return (Criteria) this;
        }

        public Criteria andValidityDateLike(String value) {
            addCriterion("validity_date like", value, "validityDate");
            return (Criteria) this;
        }

        public Criteria andValidityDateNotLike(String value) {
            addCriterion("validity_date not like", value, "validityDate");
            return (Criteria) this;
        }

        public Criteria andValidityDateIn(List<String> values) {
            addCriterion("validity_date in", values, "validityDate");
            return (Criteria) this;
        }

        public Criteria andValidityDateNotIn(List<String> values) {
            addCriterion("validity_date not in", values, "validityDate");
            return (Criteria) this;
        }

        public Criteria andValidityDateBetween(String value1, String value2) {
            addCriterion("validity_date between", value1, value2, "validityDate");
            return (Criteria) this;
        }

        public Criteria andValidityDateNotBetween(String value1, String value2) {
            addCriterion("validity_date not between", value1, value2, "validityDate");
            return (Criteria) this;
        }

        public Criteria andHdBatchNoIsNull() {
            addCriterion("hd_batch_no is null");
            return (Criteria) this;
        }

        public Criteria andHdBatchNoIsNotNull() {
            addCriterion("hd_batch_no is not null");
            return (Criteria) this;
        }

        public Criteria andHdBatchNoEqualTo(String value) {
            addCriterion("hd_batch_no =", value, "hdBatchNo");
            return (Criteria) this;
        }

        public Criteria andHdBatchNoNotEqualTo(String value) {
            addCriterion("hd_batch_no <>", value, "hdBatchNo");
            return (Criteria) this;
        }

        public Criteria andHdBatchNoGreaterThan(String value) {
            addCriterion("hd_batch_no >", value, "hdBatchNo");
            return (Criteria) this;
        }

        public Criteria andHdBatchNoGreaterThanOrEqualTo(String value) {
            addCriterion("hd_batch_no >=", value, "hdBatchNo");
            return (Criteria) this;
        }

        public Criteria andHdBatchNoLessThan(String value) {
            addCriterion("hd_batch_no <", value, "hdBatchNo");
            return (Criteria) this;
        }

        public Criteria andHdBatchNoLessThanOrEqualTo(String value) {
            addCriterion("hd_batch_no <=", value, "hdBatchNo");
            return (Criteria) this;
        }

        public Criteria andHdBatchNoLike(String value) {
            addCriterion("hd_batch_no like", value, "hdBatchNo");
            return (Criteria) this;
        }

        public Criteria andHdBatchNoNotLike(String value) {
            addCriterion("hd_batch_no not like", value, "hdBatchNo");
            return (Criteria) this;
        }

        public Criteria andHdBatchNoIn(List<String> values) {
            addCriterion("hd_batch_no in", values, "hdBatchNo");
            return (Criteria) this;
        }

        public Criteria andHdBatchNoNotIn(List<String> values) {
            addCriterion("hd_batch_no not in", values, "hdBatchNo");
            return (Criteria) this;
        }

        public Criteria andHdBatchNoBetween(String value1, String value2) {
            addCriterion("hd_batch_no between", value1, value2, "hdBatchNo");
            return (Criteria) this;
        }

        public Criteria andHdBatchNoNotBetween(String value1, String value2) {
            addCriterion("hd_batch_no not between", value1, value2, "hdBatchNo");
            return (Criteria) this;
        }

        public Criteria andSapBatchNoIsNull() {
            addCriterion("sap_batch_no is null");
            return (Criteria) this;
        }

        public Criteria andSapBatchNoIsNotNull() {
            addCriterion("sap_batch_no is not null");
            return (Criteria) this;
        }

        public Criteria andSapBatchNoEqualTo(String value) {
            addCriterion("sap_batch_no =", value, "sapBatchNo");
            return (Criteria) this;
        }

        public Criteria andSapBatchNoNotEqualTo(String value) {
            addCriterion("sap_batch_no <>", value, "sapBatchNo");
            return (Criteria) this;
        }

        public Criteria andSapBatchNoGreaterThan(String value) {
            addCriterion("sap_batch_no >", value, "sapBatchNo");
            return (Criteria) this;
        }

        public Criteria andSapBatchNoGreaterThanOrEqualTo(String value) {
            addCriterion("sap_batch_no >=", value, "sapBatchNo");
            return (Criteria) this;
        }

        public Criteria andSapBatchNoLessThan(String value) {
            addCriterion("sap_batch_no <", value, "sapBatchNo");
            return (Criteria) this;
        }

        public Criteria andSapBatchNoLessThanOrEqualTo(String value) {
            addCriterion("sap_batch_no <=", value, "sapBatchNo");
            return (Criteria) this;
        }

        public Criteria andSapBatchNoLike(String value) {
            addCriterion("sap_batch_no like", value, "sapBatchNo");
            return (Criteria) this;
        }

        public Criteria andSapBatchNoNotLike(String value) {
            addCriterion("sap_batch_no not like", value, "sapBatchNo");
            return (Criteria) this;
        }

        public Criteria andSapBatchNoIn(List<String> values) {
            addCriterion("sap_batch_no in", values, "sapBatchNo");
            return (Criteria) this;
        }

        public Criteria andSapBatchNoNotIn(List<String> values) {
            addCriterion("sap_batch_no not in", values, "sapBatchNo");
            return (Criteria) this;
        }

        public Criteria andSapBatchNoBetween(String value1, String value2) {
            addCriterion("sap_batch_no between", value1, value2, "sapBatchNo");
            return (Criteria) this;
        }

        public Criteria andSapBatchNoNotBetween(String value1, String value2) {
            addCriterion("sap_batch_no not between", value1, value2, "sapBatchNo");
            return (Criteria) this;
        }

        public Criteria andNotesIsNull() {
            addCriterion("notes is null");
            return (Criteria) this;
        }

        public Criteria andNotesIsNotNull() {
            addCriterion("notes is not null");
            return (Criteria) this;
        }

        public Criteria andNotesEqualTo(String value) {
            addCriterion("notes =", value, "notes");
            return (Criteria) this;
        }

        public Criteria andNotesNotEqualTo(String value) {
            addCriterion("notes <>", value, "notes");
            return (Criteria) this;
        }

        public Criteria andNotesGreaterThan(String value) {
            addCriterion("notes >", value, "notes");
            return (Criteria) this;
        }

        public Criteria andNotesGreaterThanOrEqualTo(String value) {
            addCriterion("notes >=", value, "notes");
            return (Criteria) this;
        }

        public Criteria andNotesLessThan(String value) {
            addCriterion("notes <", value, "notes");
            return (Criteria) this;
        }

        public Criteria andNotesLessThanOrEqualTo(String value) {
            addCriterion("notes <=", value, "notes");
            return (Criteria) this;
        }

        public Criteria andNotesLike(String value) {
            addCriterion("notes like", value, "notes");
            return (Criteria) this;
        }

        public Criteria andNotesNotLike(String value) {
            addCriterion("notes not like", value, "notes");
            return (Criteria) this;
        }

        public Criteria andNotesIn(List<String> values) {
            addCriterion("notes in", values, "notes");
            return (Criteria) this;
        }

        public Criteria andNotesNotIn(List<String> values) {
            addCriterion("notes not in", values, "notes");
            return (Criteria) this;
        }

        public Criteria andNotesBetween(String value1, String value2) {
            addCriterion("notes between", value1, value2, "notes");
            return (Criteria) this;
        }

        public Criteria andNotesNotBetween(String value1, String value2) {
            addCriterion("notes not between", value1, value2, "notes");
            return (Criteria) this;
        }

        public Criteria andVoidReasonIsNull() {
            addCriterion("void_reason is null");
            return (Criteria) this;
        }

        public Criteria andVoidReasonIsNotNull() {
            addCriterion("void_reason is not null");
            return (Criteria) this;
        }

        public Criteria andVoidReasonEqualTo(String value) {
            addCriterion("void_reason =", value, "voidReason");
            return (Criteria) this;
        }

        public Criteria andVoidReasonNotEqualTo(String value) {
            addCriterion("void_reason <>", value, "voidReason");
            return (Criteria) this;
        }

        public Criteria andVoidReasonGreaterThan(String value) {
            addCriterion("void_reason >", value, "voidReason");
            return (Criteria) this;
        }

        public Criteria andVoidReasonGreaterThanOrEqualTo(String value) {
            addCriterion("void_reason >=", value, "voidReason");
            return (Criteria) this;
        }

        public Criteria andVoidReasonLessThan(String value) {
            addCriterion("void_reason <", value, "voidReason");
            return (Criteria) this;
        }

        public Criteria andVoidReasonLessThanOrEqualTo(String value) {
            addCriterion("void_reason <=", value, "voidReason");
            return (Criteria) this;
        }

        public Criteria andVoidReasonLike(String value) {
            addCriterion("void_reason like", value, "voidReason");
            return (Criteria) this;
        }

        public Criteria andVoidReasonNotLike(String value) {
            addCriterion("void_reason not like", value, "voidReason");
            return (Criteria) this;
        }

        public Criteria andVoidReasonIn(List<String> values) {
            addCriterion("void_reason in", values, "voidReason");
            return (Criteria) this;
        }

        public Criteria andVoidReasonNotIn(List<String> values) {
            addCriterion("void_reason not in", values, "voidReason");
            return (Criteria) this;
        }

        public Criteria andVoidReasonBetween(String value1, String value2) {
            addCriterion("void_reason between", value1, value2, "voidReason");
            return (Criteria) this;
        }

        public Criteria andVoidReasonNotBetween(String value1, String value2) {
            addCriterion("void_reason not between", value1, value2, "voidReason");
            return (Criteria) this;
        }

        public Criteria andGoodsCommonNameIsNull() {
            addCriterion("goods_common_name is null");
            return (Criteria) this;
        }

        public Criteria andGoodsCommonNameIsNotNull() {
            addCriterion("goods_common_name is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsCommonNameEqualTo(String value) {
            addCriterion("goods_common_name =", value, "goodsCommonName");
            return (Criteria) this;
        }

        public Criteria andGoodsCommonNameNotEqualTo(String value) {
            addCriterion("goods_common_name <>", value, "goodsCommonName");
            return (Criteria) this;
        }

        public Criteria andGoodsCommonNameGreaterThan(String value) {
            addCriterion("goods_common_name >", value, "goodsCommonName");
            return (Criteria) this;
        }

        public Criteria andGoodsCommonNameGreaterThanOrEqualTo(String value) {
            addCriterion("goods_common_name >=", value, "goodsCommonName");
            return (Criteria) this;
        }

        public Criteria andGoodsCommonNameLessThan(String value) {
            addCriterion("goods_common_name <", value, "goodsCommonName");
            return (Criteria) this;
        }

        public Criteria andGoodsCommonNameLessThanOrEqualTo(String value) {
            addCriterion("goods_common_name <=", value, "goodsCommonName");
            return (Criteria) this;
        }

        public Criteria andGoodsCommonNameLike(String value) {
            addCriterion("goods_common_name like", value, "goodsCommonName");
            return (Criteria) this;
        }

        public Criteria andGoodsCommonNameNotLike(String value) {
            addCriterion("goods_common_name not like", value, "goodsCommonName");
            return (Criteria) this;
        }

        public Criteria andGoodsCommonNameIn(List<String> values) {
            addCriterion("goods_common_name in", values, "goodsCommonName");
            return (Criteria) this;
        }

        public Criteria andGoodsCommonNameNotIn(List<String> values) {
            addCriterion("goods_common_name not in", values, "goodsCommonName");
            return (Criteria) this;
        }

        public Criteria andGoodsCommonNameBetween(String value1, String value2) {
            addCriterion("goods_common_name between", value1, value2, "goodsCommonName");
            return (Criteria) this;
        }

        public Criteria andGoodsCommonNameNotBetween(String value1, String value2) {
            addCriterion("goods_common_name not between", value1, value2, "goodsCommonName");
            return (Criteria) this;
        }

        public Criteria andManufacturerIsNull() {
            addCriterion("manufacturer is null");
            return (Criteria) this;
        }

        public Criteria andManufacturerIsNotNull() {
            addCriterion("manufacturer is not null");
            return (Criteria) this;
        }

        public Criteria andManufacturerEqualTo(String value) {
            addCriterion("manufacturer =", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerNotEqualTo(String value) {
            addCriterion("manufacturer <>", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerGreaterThan(String value) {
            addCriterion("manufacturer >", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerGreaterThanOrEqualTo(String value) {
            addCriterion("manufacturer >=", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerLessThan(String value) {
            addCriterion("manufacturer <", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerLessThanOrEqualTo(String value) {
            addCriterion("manufacturer <=", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerLike(String value) {
            addCriterion("manufacturer like", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerNotLike(String value) {
            addCriterion("manufacturer not like", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerIn(List<String> values) {
            addCriterion("manufacturer in", values, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerNotIn(List<String> values) {
            addCriterion("manufacturer not in", values, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerBetween(String value1, String value2) {
            addCriterion("manufacturer between", value1, value2, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerNotBetween(String value1, String value2) {
            addCriterion("manufacturer not between", value1, value2, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andUnitIsNull() {
            addCriterion("unit is null");
            return (Criteria) this;
        }

        public Criteria andUnitIsNotNull() {
            addCriterion("unit is not null");
            return (Criteria) this;
        }

        public Criteria andUnitEqualTo(String value) {
            addCriterion("unit =", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotEqualTo(String value) {
            addCriterion("unit <>", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitGreaterThan(String value) {
            addCriterion("unit >", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitGreaterThanOrEqualTo(String value) {
            addCriterion("unit >=", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLessThan(String value) {
            addCriterion("unit <", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLessThanOrEqualTo(String value) {
            addCriterion("unit <=", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLike(String value) {
            addCriterion("unit like", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotLike(String value) {
            addCriterion("unit not like", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitIn(List<String> values) {
            addCriterion("unit in", values, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotIn(List<String> values) {
            addCriterion("unit not in", values, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitBetween(String value1, String value2) {
            addCriterion("unit between", value1, value2, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotBetween(String value1, String value2) {
            addCriterion("unit not between", value1, value2, "unit");
            return (Criteria) this;
        }

        public Criteria andSuggestAllotQuantityIsNull() {
            addCriterion("suggest_allot_quantity is null");
            return (Criteria) this;
        }

        public Criteria andSuggestAllotQuantityIsNotNull() {
            addCriterion("suggest_allot_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andSuggestAllotQuantityEqualTo(BigDecimal value) {
            addCriterion("suggest_allot_quantity =", value, "suggestAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andSuggestAllotQuantityNotEqualTo(BigDecimal value) {
            addCriterion("suggest_allot_quantity <>", value, "suggestAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andSuggestAllotQuantityGreaterThan(BigDecimal value) {
            addCriterion("suggest_allot_quantity >", value, "suggestAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andSuggestAllotQuantityGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("suggest_allot_quantity >=", value, "suggestAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andSuggestAllotQuantityLessThan(BigDecimal value) {
            addCriterion("suggest_allot_quantity <", value, "suggestAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andSuggestAllotQuantityLessThanOrEqualTo(BigDecimal value) {
            addCriterion("suggest_allot_quantity <=", value, "suggestAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andSuggestAllotQuantityIn(List<BigDecimal> values) {
            addCriterion("suggest_allot_quantity in", values, "suggestAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andSuggestAllotQuantityNotIn(List<BigDecimal> values) {
            addCriterion("suggest_allot_quantity not in", values, "suggestAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andSuggestAllotQuantityBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("suggest_allot_quantity between", value1, value2, "suggestAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andSuggestAllotQuantityNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("suggest_allot_quantity not between", value1, value2, "suggestAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andSuggestCostAmountIsNull() {
            addCriterion("suggest_cost_amount is null");
            return (Criteria) this;
        }

        public Criteria andSuggestCostAmountIsNotNull() {
            addCriterion("suggest_cost_amount is not null");
            return (Criteria) this;
        }

        public Criteria andSuggestCostAmountEqualTo(BigDecimal value) {
            addCriterion("suggest_cost_amount =", value, "suggestCostAmount");
            return (Criteria) this;
        }

        public Criteria andSuggestCostAmountNotEqualTo(BigDecimal value) {
            addCriterion("suggest_cost_amount <>", value, "suggestCostAmount");
            return (Criteria) this;
        }

        public Criteria andSuggestCostAmountGreaterThan(BigDecimal value) {
            addCriterion("suggest_cost_amount >", value, "suggestCostAmount");
            return (Criteria) this;
        }

        public Criteria andSuggestCostAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("suggest_cost_amount >=", value, "suggestCostAmount");
            return (Criteria) this;
        }

        public Criteria andSuggestCostAmountLessThan(BigDecimal value) {
            addCriterion("suggest_cost_amount <", value, "suggestCostAmount");
            return (Criteria) this;
        }

        public Criteria andSuggestCostAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("suggest_cost_amount <=", value, "suggestCostAmount");
            return (Criteria) this;
        }

        public Criteria andSuggestCostAmountIn(List<BigDecimal> values) {
            addCriterion("suggest_cost_amount in", values, "suggestCostAmount");
            return (Criteria) this;
        }

        public Criteria andSuggestCostAmountNotIn(List<BigDecimal> values) {
            addCriterion("suggest_cost_amount not in", values, "suggestCostAmount");
            return (Criteria) this;
        }

        public Criteria andSuggestCostAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("suggest_cost_amount between", value1, value2, "suggestCostAmount");
            return (Criteria) this;
        }

        public Criteria andSuggestCostAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("suggest_cost_amount not between", value1, value2, "suggestCostAmount");
            return (Criteria) this;
        }

        public Criteria andRealAllotQuantityIsNull() {
            addCriterion("real_allot_quantity is null");
            return (Criteria) this;
        }

        public Criteria andRealAllotQuantityIsNotNull() {
            addCriterion("real_allot_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andRealAllotQuantityEqualTo(BigDecimal value) {
            addCriterion("real_allot_quantity =", value, "realAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andRealAllotQuantityNotEqualTo(BigDecimal value) {
            addCriterion("real_allot_quantity <>", value, "realAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andRealAllotQuantityGreaterThan(BigDecimal value) {
            addCriterion("real_allot_quantity >", value, "realAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andRealAllotQuantityGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("real_allot_quantity >=", value, "realAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andRealAllotQuantityLessThan(BigDecimal value) {
            addCriterion("real_allot_quantity <", value, "realAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andRealAllotQuantityLessThanOrEqualTo(BigDecimal value) {
            addCriterion("real_allot_quantity <=", value, "realAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andRealAllotQuantityIn(List<BigDecimal> values) {
            addCriterion("real_allot_quantity in", values, "realAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andRealAllotQuantityNotIn(List<BigDecimal> values) {
            addCriterion("real_allot_quantity not in", values, "realAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andRealAllotQuantityBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("real_allot_quantity between", value1, value2, "realAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andRealAllotQuantityNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("real_allot_quantity not between", value1, value2, "realAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andRealCostAmountIsNull() {
            addCriterion("real_cost_amount is null");
            return (Criteria) this;
        }

        public Criteria andRealCostAmountIsNotNull() {
            addCriterion("real_cost_amount is not null");
            return (Criteria) this;
        }

        public Criteria andRealCostAmountEqualTo(BigDecimal value) {
            addCriterion("real_cost_amount =", value, "realCostAmount");
            return (Criteria) this;
        }

        public Criteria andRealCostAmountNotEqualTo(BigDecimal value) {
            addCriterion("real_cost_amount <>", value, "realCostAmount");
            return (Criteria) this;
        }

        public Criteria andRealCostAmountGreaterThan(BigDecimal value) {
            addCriterion("real_cost_amount >", value, "realCostAmount");
            return (Criteria) this;
        }

        public Criteria andRealCostAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("real_cost_amount >=", value, "realCostAmount");
            return (Criteria) this;
        }

        public Criteria andRealCostAmountLessThan(BigDecimal value) {
            addCriterion("real_cost_amount <", value, "realCostAmount");
            return (Criteria) this;
        }

        public Criteria andRealCostAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("real_cost_amount <=", value, "realCostAmount");
            return (Criteria) this;
        }

        public Criteria andRealCostAmountIn(List<BigDecimal> values) {
            addCriterion("real_cost_amount in", values, "realCostAmount");
            return (Criteria) this;
        }

        public Criteria andRealCostAmountNotIn(List<BigDecimal> values) {
            addCriterion("real_cost_amount not in", values, "realCostAmount");
            return (Criteria) this;
        }

        public Criteria andRealCostAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("real_cost_amount between", value1, value2, "realCostAmount");
            return (Criteria) this;
        }

        public Criteria andRealCostAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("real_cost_amount not between", value1, value2, "realCostAmount");
            return (Criteria) this;
        }

        public Criteria andOutApproveStatusIsNull() {
            addCriterion("out_approve_status is null");
            return (Criteria) this;
        }

        public Criteria andOutApproveStatusIsNotNull() {
            addCriterion("out_approve_status is not null");
            return (Criteria) this;
        }

        public Criteria andOutApproveStatusEqualTo(Byte value) {
            addCriterion("out_approve_status =", value, "outApproveStatus");
            return (Criteria) this;
        }

        public Criteria andOutApproveStatusNotEqualTo(Byte value) {
            addCriterion("out_approve_status <>", value, "outApproveStatus");
            return (Criteria) this;
        }

        public Criteria andOutApproveStatusGreaterThan(Byte value) {
            addCriterion("out_approve_status >", value, "outApproveStatus");
            return (Criteria) this;
        }

        public Criteria andOutApproveStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("out_approve_status >=", value, "outApproveStatus");
            return (Criteria) this;
        }

        public Criteria andOutApproveStatusLessThan(Byte value) {
            addCriterion("out_approve_status <", value, "outApproveStatus");
            return (Criteria) this;
        }

        public Criteria andOutApproveStatusLessThanOrEqualTo(Byte value) {
            addCriterion("out_approve_status <=", value, "outApproveStatus");
            return (Criteria) this;
        }

        public Criteria andOutApproveStatusIn(List<Byte> values) {
            addCriterion("out_approve_status in", values, "outApproveStatus");
            return (Criteria) this;
        }

        public Criteria andOutApproveStatusNotIn(List<Byte> values) {
            addCriterion("out_approve_status not in", values, "outApproveStatus");
            return (Criteria) this;
        }

        public Criteria andOutApproveStatusBetween(Byte value1, Byte value2) {
            addCriterion("out_approve_status between", value1, value2, "outApproveStatus");
            return (Criteria) this;
        }

        public Criteria andOutApproveStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("out_approve_status not between", value1, value2, "outApproveStatus");
            return (Criteria) this;
        }

        public Criteria andOutApproveTimeIsNull() {
            addCriterion("out_approve_time is null");
            return (Criteria) this;
        }

        public Criteria andOutApproveTimeIsNotNull() {
            addCriterion("out_approve_time is not null");
            return (Criteria) this;
        }

        public Criteria andOutApproveTimeEqualTo(Date value) {
            addCriterion("out_approve_time =", value, "outApproveTime");
            return (Criteria) this;
        }

        public Criteria andOutApproveTimeNotEqualTo(Date value) {
            addCriterion("out_approve_time <>", value, "outApproveTime");
            return (Criteria) this;
        }

        public Criteria andOutApproveTimeGreaterThan(Date value) {
            addCriterion("out_approve_time >", value, "outApproveTime");
            return (Criteria) this;
        }

        public Criteria andOutApproveTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("out_approve_time >=", value, "outApproveTime");
            return (Criteria) this;
        }

        public Criteria andOutApproveTimeLessThan(Date value) {
            addCriterion("out_approve_time <", value, "outApproveTime");
            return (Criteria) this;
        }

        public Criteria andOutApproveTimeLessThanOrEqualTo(Date value) {
            addCriterion("out_approve_time <=", value, "outApproveTime");
            return (Criteria) this;
        }

        public Criteria andOutApproveTimeIn(List<Date> values) {
            addCriterion("out_approve_time in", values, "outApproveTime");
            return (Criteria) this;
        }

        public Criteria andOutApproveTimeNotIn(List<Date> values) {
            addCriterion("out_approve_time not in", values, "outApproveTime");
            return (Criteria) this;
        }

        public Criteria andOutApproveTimeBetween(Date value1, Date value2) {
            addCriterion("out_approve_time between", value1, value2, "outApproveTime");
            return (Criteria) this;
        }

        public Criteria andOutApproveTimeNotBetween(Date value1, Date value2) {
            addCriterion("out_approve_time not between", value1, value2, "outApproveTime");
            return (Criteria) this;
        }

        public Criteria andOutAllotQuantityIsNull() {
            addCriterion("out_allot_quantity is null");
            return (Criteria) this;
        }

        public Criteria andOutAllotQuantityIsNotNull() {
            addCriterion("out_allot_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andOutAllotQuantityEqualTo(BigDecimal value) {
            addCriterion("out_allot_quantity =", value, "outAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andOutAllotQuantityNotEqualTo(BigDecimal value) {
            addCriterion("out_allot_quantity <>", value, "outAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andOutAllotQuantityGreaterThan(BigDecimal value) {
            addCriterion("out_allot_quantity >", value, "outAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andOutAllotQuantityGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("out_allot_quantity >=", value, "outAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andOutAllotQuantityLessThan(BigDecimal value) {
            addCriterion("out_allot_quantity <", value, "outAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andOutAllotQuantityLessThanOrEqualTo(BigDecimal value) {
            addCriterion("out_allot_quantity <=", value, "outAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andOutAllotQuantityIn(List<BigDecimal> values) {
            addCriterion("out_allot_quantity in", values, "outAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andOutAllotQuantityNotIn(List<BigDecimal> values) {
            addCriterion("out_allot_quantity not in", values, "outAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andOutAllotQuantityBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("out_allot_quantity between", value1, value2, "outAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andOutAllotQuantityNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("out_allot_quantity not between", value1, value2, "outAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andInApproveStatusIsNull() {
            addCriterion("in_approve_status is null");
            return (Criteria) this;
        }

        public Criteria andInApproveStatusIsNotNull() {
            addCriterion("in_approve_status is not null");
            return (Criteria) this;
        }

        public Criteria andInApproveStatusEqualTo(Byte value) {
            addCriterion("in_approve_status =", value, "inApproveStatus");
            return (Criteria) this;
        }

        public Criteria andInApproveStatusNotEqualTo(Byte value) {
            addCriterion("in_approve_status <>", value, "inApproveStatus");
            return (Criteria) this;
        }

        public Criteria andInApproveStatusGreaterThan(Byte value) {
            addCriterion("in_approve_status >", value, "inApproveStatus");
            return (Criteria) this;
        }

        public Criteria andInApproveStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("in_approve_status >=", value, "inApproveStatus");
            return (Criteria) this;
        }

        public Criteria andInApproveStatusLessThan(Byte value) {
            addCriterion("in_approve_status <", value, "inApproveStatus");
            return (Criteria) this;
        }

        public Criteria andInApproveStatusLessThanOrEqualTo(Byte value) {
            addCriterion("in_approve_status <=", value, "inApproveStatus");
            return (Criteria) this;
        }

        public Criteria andInApproveStatusIn(List<Byte> values) {
            addCriterion("in_approve_status in", values, "inApproveStatus");
            return (Criteria) this;
        }

        public Criteria andInApproveStatusNotIn(List<Byte> values) {
            addCriterion("in_approve_status not in", values, "inApproveStatus");
            return (Criteria) this;
        }

        public Criteria andInApproveStatusBetween(Byte value1, Byte value2) {
            addCriterion("in_approve_status between", value1, value2, "inApproveStatus");
            return (Criteria) this;
        }

        public Criteria andInApproveStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("in_approve_status not between", value1, value2, "inApproveStatus");
            return (Criteria) this;
        }

        public Criteria andInApproveTimeIsNull() {
            addCriterion("in_approve_time is null");
            return (Criteria) this;
        }

        public Criteria andInApproveTimeIsNotNull() {
            addCriterion("in_approve_time is not null");
            return (Criteria) this;
        }

        public Criteria andInApproveTimeEqualTo(Date value) {
            addCriterion("in_approve_time =", value, "inApproveTime");
            return (Criteria) this;
        }

        public Criteria andInApproveTimeNotEqualTo(Date value) {
            addCriterion("in_approve_time <>", value, "inApproveTime");
            return (Criteria) this;
        }

        public Criteria andInApproveTimeGreaterThan(Date value) {
            addCriterion("in_approve_time >", value, "inApproveTime");
            return (Criteria) this;
        }

        public Criteria andInApproveTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("in_approve_time >=", value, "inApproveTime");
            return (Criteria) this;
        }

        public Criteria andInApproveTimeLessThan(Date value) {
            addCriterion("in_approve_time <", value, "inApproveTime");
            return (Criteria) this;
        }

        public Criteria andInApproveTimeLessThanOrEqualTo(Date value) {
            addCriterion("in_approve_time <=", value, "inApproveTime");
            return (Criteria) this;
        }

        public Criteria andInApproveTimeIn(List<Date> values) {
            addCriterion("in_approve_time in", values, "inApproveTime");
            return (Criteria) this;
        }

        public Criteria andInApproveTimeNotIn(List<Date> values) {
            addCriterion("in_approve_time not in", values, "inApproveTime");
            return (Criteria) this;
        }

        public Criteria andInApproveTimeBetween(Date value1, Date value2) {
            addCriterion("in_approve_time between", value1, value2, "inApproveTime");
            return (Criteria) this;
        }

        public Criteria andInApproveTimeNotBetween(Date value1, Date value2) {
            addCriterion("in_approve_time not between", value1, value2, "inApproveTime");
            return (Criteria) this;
        }

        public Criteria andInAllotQuantityIsNull() {
            addCriterion("in_allot_quantity is null");
            return (Criteria) this;
        }

        public Criteria andInAllotQuantityIsNotNull() {
            addCriterion("in_allot_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andInAllotQuantityEqualTo(BigDecimal value) {
            addCriterion("in_allot_quantity =", value, "inAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andInAllotQuantityNotEqualTo(BigDecimal value) {
            addCriterion("in_allot_quantity <>", value, "inAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andInAllotQuantityGreaterThan(BigDecimal value) {
            addCriterion("in_allot_quantity >", value, "inAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andInAllotQuantityGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("in_allot_quantity >=", value, "inAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andInAllotQuantityLessThan(BigDecimal value) {
            addCriterion("in_allot_quantity <", value, "inAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andInAllotQuantityLessThanOrEqualTo(BigDecimal value) {
            addCriterion("in_allot_quantity <=", value, "inAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andInAllotQuantityIn(List<BigDecimal> values) {
            addCriterion("in_allot_quantity in", values, "inAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andInAllotQuantityNotIn(List<BigDecimal> values) {
            addCriterion("in_allot_quantity not in", values, "inAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andInAllotQuantityBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("in_allot_quantity between", value1, value2, "inAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andInAllotQuantityNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("in_allot_quantity not between", value1, value2, "inAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andInStockTimeIsNull() {
            addCriterion("in_stock_time is null");
            return (Criteria) this;
        }

        public Criteria andInStockTimeIsNotNull() {
            addCriterion("in_stock_time is not null");
            return (Criteria) this;
        }

        public Criteria andInStockTimeEqualTo(Date value) {
            addCriterion("in_stock_time =", value, "inStockTime");
            return (Criteria) this;
        }

        public Criteria andInStockTimeNotEqualTo(Date value) {
            addCriterion("in_stock_time <>", value, "inStockTime");
            return (Criteria) this;
        }

        public Criteria andInStockTimeGreaterThan(Date value) {
            addCriterion("in_stock_time >", value, "inStockTime");
            return (Criteria) this;
        }

        public Criteria andInStockTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("in_stock_time >=", value, "inStockTime");
            return (Criteria) this;
        }

        public Criteria andInStockTimeLessThan(Date value) {
            addCriterion("in_stock_time <", value, "inStockTime");
            return (Criteria) this;
        }

        public Criteria andInStockTimeLessThanOrEqualTo(Date value) {
            addCriterion("in_stock_time <=", value, "inStockTime");
            return (Criteria) this;
        }

        public Criteria andInStockTimeIn(List<Date> values) {
            addCriterion("in_stock_time in", values, "inStockTime");
            return (Criteria) this;
        }

        public Criteria andInStockTimeNotIn(List<Date> values) {
            addCriterion("in_stock_time not in", values, "inStockTime");
            return (Criteria) this;
        }

        public Criteria andInStockTimeBetween(Date value1, Date value2) {
            addCriterion("in_stock_time between", value1, value2, "inStockTime");
            return (Criteria) this;
        }

        public Criteria andInStockTimeNotBetween(Date value1, Date value2) {
            addCriterion("in_stock_time not between", value1, value2, "inStockTime");
            return (Criteria) this;
        }

        public Criteria andShowStatusIsNull() {
            addCriterion("show_status is null");
            return (Criteria) this;
        }

        public Criteria andShowStatusIsNotNull() {
            addCriterion("show_status is not null");
            return (Criteria) this;
        }

        public Criteria andShowStatusEqualTo(Byte value) {
            addCriterion("show_status =", value, "showStatus");
            return (Criteria) this;
        }

        public Criteria andShowStatusNotEqualTo(Byte value) {
            addCriterion("show_status <>", value, "showStatus");
            return (Criteria) this;
        }

        public Criteria andShowStatusGreaterThan(Byte value) {
            addCriterion("show_status >", value, "showStatus");
            return (Criteria) this;
        }

        public Criteria andShowStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("show_status >=", value, "showStatus");
            return (Criteria) this;
        }

        public Criteria andShowStatusLessThan(Byte value) {
            addCriterion("show_status <", value, "showStatus");
            return (Criteria) this;
        }

        public Criteria andShowStatusLessThanOrEqualTo(Byte value) {
            addCriterion("show_status <=", value, "showStatus");
            return (Criteria) this;
        }

        public Criteria andShowStatusIn(List<Byte> values) {
            addCriterion("show_status in", values, "showStatus");
            return (Criteria) this;
        }

        public Criteria andShowStatusNotIn(List<Byte> values) {
            addCriterion("show_status not in", values, "showStatus");
            return (Criteria) this;
        }

        public Criteria andShowStatusBetween(Byte value1, Byte value2) {
            addCriterion("show_status between", value1, value2, "showStatus");
            return (Criteria) this;
        }

        public Criteria andShowStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("show_status not between", value1, value2, "showStatus");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("`status` is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("`status` is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Byte value) {
            addCriterion("`status` =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Byte value) {
            addCriterion("`status` <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Byte value) {
            addCriterion("`status` >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("`status` >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Byte value) {
            addCriterion("`status` <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Byte value) {
            addCriterion("`status` <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Byte> values) {
            addCriterion("`status` in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Byte> values) {
            addCriterion("`status` not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Byte value1, Byte value2) {
            addCriterion("`status` between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("`status` not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIsNull() {
            addCriterion("gmt_update is null");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIsNotNull() {
            addCriterion("gmt_update is not null");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateEqualTo(Date value) {
            addCriterion("gmt_update =", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotEqualTo(Date value) {
            addCriterion("gmt_update <>", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateGreaterThan(Date value) {
            addCriterion("gmt_update >", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_update >=", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateLessThan(Date value) {
            addCriterion("gmt_update <", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_update <=", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIn(List<Date> values) {
            addCriterion("gmt_update in", values, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotIn(List<Date> values) {
            addCriterion("gmt_update not in", values, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateBetween(Date value1, Date value2) {
            addCriterion("gmt_update between", value1, value2, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_update not between", value1, value2, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andExtendIsNull() {
            addCriterion("extend is null");
            return (Criteria) this;
        }

        public Criteria andExtendIsNotNull() {
            addCriterion("extend is not null");
            return (Criteria) this;
        }

        public Criteria andExtendEqualTo(String value) {
            addCriterion("extend =", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotEqualTo(String value) {
            addCriterion("extend <>", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendGreaterThan(String value) {
            addCriterion("extend >", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendGreaterThanOrEqualTo(String value) {
            addCriterion("extend >=", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLessThan(String value) {
            addCriterion("extend <", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLessThanOrEqualTo(String value) {
            addCriterion("extend <=", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLike(String value) {
            addCriterion("extend like", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotLike(String value) {
            addCriterion("extend not like", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendIn(List<String> values) {
            addCriterion("extend in", values, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotIn(List<String> values) {
            addCriterion("extend not in", values, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendBetween(String value1, String value2) {
            addCriterion("extend between", value1, value2, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotBetween(String value1, String value2) {
            addCriterion("extend not between", value1, value2, "extend");
            return (Criteria) this;
        }

        public Criteria andVersionIsNull() {
            addCriterion("version is null");
            return (Criteria) this;
        }

        public Criteria andVersionIsNotNull() {
            addCriterion("version is not null");
            return (Criteria) this;
        }

        public Criteria andVersionEqualTo(Integer value) {
            addCriterion("version =", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotEqualTo(Integer value) {
            addCriterion("version <>", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThan(Integer value) {
            addCriterion("version >", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThanOrEqualTo(Integer value) {
            addCriterion("version >=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThan(Integer value) {
            addCriterion("version <", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThanOrEqualTo(Integer value) {
            addCriterion("version <=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionIn(List<Integer> values) {
            addCriterion("version in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotIn(List<Integer> values) {
            addCriterion("version not in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionBetween(Integer value1, Integer value2) {
            addCriterion("version between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotBetween(Integer value1, Integer value2) {
            addCriterion("version not between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("created_by is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("created_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(Long value) {
            addCriterion("created_by =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(Long value) {
            addCriterion("created_by <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(Long value) {
            addCriterion("created_by >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(Long value) {
            addCriterion("created_by >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(Long value) {
            addCriterion("created_by <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(Long value) {
            addCriterion("created_by <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<Long> values) {
            addCriterion("created_by in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<Long> values) {
            addCriterion("created_by not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(Long value1, Long value2) {
            addCriterion("created_by between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(Long value1, Long value2) {
            addCriterion("created_by not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedNameIsNull() {
            addCriterion("created_name is null");
            return (Criteria) this;
        }

        public Criteria andCreatedNameIsNotNull() {
            addCriterion("created_name is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedNameEqualTo(String value) {
            addCriterion("created_name =", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotEqualTo(String value) {
            addCriterion("created_name <>", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameGreaterThan(String value) {
            addCriterion("created_name >", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameGreaterThanOrEqualTo(String value) {
            addCriterion("created_name >=", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameLessThan(String value) {
            addCriterion("created_name <", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameLessThanOrEqualTo(String value) {
            addCriterion("created_name <=", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameLike(String value) {
            addCriterion("created_name like", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotLike(String value) {
            addCriterion("created_name not like", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameIn(List<String> values) {
            addCriterion("created_name in", values, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotIn(List<String> values) {
            addCriterion("created_name not in", values, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameBetween(String value1, String value2) {
            addCriterion("created_name between", value1, value2, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotBetween(String value1, String value2) {
            addCriterion("created_name not between", value1, value2, "createdName");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNull() {
            addCriterion("updated_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNotNull() {
            addCriterion("updated_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByEqualTo(Long value) {
            addCriterion("updated_by =", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotEqualTo(Long value) {
            addCriterion("updated_by <>", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThan(Long value) {
            addCriterion("updated_by >", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThanOrEqualTo(Long value) {
            addCriterion("updated_by >=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThan(Long value) {
            addCriterion("updated_by <", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThanOrEqualTo(Long value) {
            addCriterion("updated_by <=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIn(List<Long> values) {
            addCriterion("updated_by in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotIn(List<Long> values) {
            addCriterion("updated_by not in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByBetween(Long value1, Long value2) {
            addCriterion("updated_by between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotBetween(Long value1, Long value2) {
            addCriterion("updated_by not between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameIsNull() {
            addCriterion("updated_name is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameIsNotNull() {
            addCriterion("updated_name is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameEqualTo(String value) {
            addCriterion("updated_name =", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotEqualTo(String value) {
            addCriterion("updated_name <>", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameGreaterThan(String value) {
            addCriterion("updated_name >", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameGreaterThanOrEqualTo(String value) {
            addCriterion("updated_name >=", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameLessThan(String value) {
            addCriterion("updated_name <", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameLessThanOrEqualTo(String value) {
            addCriterion("updated_name <=", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameLike(String value) {
            addCriterion("updated_name like", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotLike(String value) {
            addCriterion("updated_name not like", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameIn(List<String> values) {
            addCriterion("updated_name in", values, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotIn(List<String> values) {
            addCriterion("updated_name not in", values, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameBetween(String value1, String value2) {
            addCriterion("updated_name between", value1, value2, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotBetween(String value1, String value2) {
            addCriterion("updated_name not between", value1, value2, "updatedName");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}