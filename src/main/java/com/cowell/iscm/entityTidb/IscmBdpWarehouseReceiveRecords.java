package com.cowell.iscm.entityTidb;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> 仓库收货记录
 */
public class IscmBdpWarehouseReceiveRecords implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 公司编码
     */
    private String companyCode;

    /**
     * 公司bdp编码
     */
    private String companyBdpCode;

    /**
     * 门店编码
     */
    private String storeCode;

    /**
     * 门店bdp编码
     */
    private String storeBdpCode;

    /**
     * 商品编码
     */
    private String goodsNo;

    /**
     * 海典商品编码
     */
    private String goodsHdNo;

    /**
     * 配送单号
     */
    private String distrNo;

    /**
     * 配送单行号
     */
    private String distrLineNo;

    /**
     * 配送时间
     */
    private Date distrDate;

    /**
     * 批号
     */
    private String batchNo;

    /**
     * 有效期至
     */
    private Date validityDate;

    /**
     * sap批次号
     */
    private String sapBatchCode;

    /**
     * sap批次号
     */
    private String hdBatchCode;

    /**
     * 配送数量
     */
    private BigDecimal distrQuantity;

    /**
     * 可退仓数量
     */
    private BigDecimal shouldReturnQuantity;

    /**
     * 已退仓数量
     */
    private BigDecimal returnQuantity;

    /**
     * 仓库编码
     */
    private String warehouseCode;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getCompanyBdpCode() {
        return companyBdpCode;
    }

    public void setCompanyBdpCode(String companyBdpCode) {
        this.companyBdpCode = companyBdpCode;
    }

    public String getStoreCode() {
        return storeCode;
    }

    public void setStoreCode(String storeCode) {
        this.storeCode = storeCode;
    }

    public String getStoreBdpCode() {
        return storeBdpCode;
    }

    public void setStoreBdpCode(String storeBdpCode) {
        this.storeBdpCode = storeBdpCode;
    }

    public String getGoodsNo() {
        return goodsNo;
    }

    public void setGoodsNo(String goodsNo) {
        this.goodsNo = goodsNo;
    }

    public String getGoodsHdNo() {
        return goodsHdNo;
    }

    public void setGoodsHdNo(String goodsHdNo) {
        this.goodsHdNo = goodsHdNo;
    }

    public String getDistrNo() {
        return distrNo;
    }

    public void setDistrNo(String distrNo) {
        this.distrNo = distrNo;
    }

    public String getDistrLineNo() {
        return distrLineNo;
    }

    public void setDistrLineNo(String distrLineNo) {
        this.distrLineNo = distrLineNo;
    }

    public Date getDistrDate() {
        return distrDate;
    }

    public void setDistrDate(Date distrDate) {
        this.distrDate = distrDate;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public Date getValidityDate() {
        return validityDate;
    }

    public void setValidityDate(Date validityDate) {
        this.validityDate = validityDate;
    }

    public String getSapBatchCode() {
        return sapBatchCode;
    }

    public void setSapBatchCode(String sapBatchCode) {
        this.sapBatchCode = sapBatchCode;
    }

    public String getHdBatchCode() {
        return hdBatchCode;
    }

    public void setHdBatchCode(String hdBatchCode) {
        this.hdBatchCode = hdBatchCode;
    }

    public BigDecimal getDistrQuantity() {
        return distrQuantity;
    }

    public void setDistrQuantity(BigDecimal distrQuantity) {
        this.distrQuantity = distrQuantity;
    }

    public BigDecimal getShouldReturnQuantity() {
        return shouldReturnQuantity;
    }

    public void setShouldReturnQuantity(BigDecimal shouldReturnQuantity) {
        this.shouldReturnQuantity = shouldReturnQuantity;
    }

    public BigDecimal getReturnQuantity() {
        return returnQuantity;
    }

    public void setReturnQuantity(BigDecimal returnQuantity) {
        this.returnQuantity = returnQuantity;
    }

    public String getWarehouseCode() {
        return warehouseCode;
    }

    public void setWarehouseCode(String warehouseCode) {
        this.warehouseCode = warehouseCode;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        IscmBdpWarehouseReceiveRecords other = (IscmBdpWarehouseReceiveRecords) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getCompanyCode() == null ? other.getCompanyCode() == null : this.getCompanyCode().equals(other.getCompanyCode()))
            && (this.getCompanyBdpCode() == null ? other.getCompanyBdpCode() == null : this.getCompanyBdpCode().equals(other.getCompanyBdpCode()))
            && (this.getStoreCode() == null ? other.getStoreCode() == null : this.getStoreCode().equals(other.getStoreCode()))
            && (this.getStoreBdpCode() == null ? other.getStoreBdpCode() == null : this.getStoreBdpCode().equals(other.getStoreBdpCode()))
            && (this.getGoodsNo() == null ? other.getGoodsNo() == null : this.getGoodsNo().equals(other.getGoodsNo()))
            && (this.getGoodsHdNo() == null ? other.getGoodsHdNo() == null : this.getGoodsHdNo().equals(other.getGoodsHdNo()))
            && (this.getDistrNo() == null ? other.getDistrNo() == null : this.getDistrNo().equals(other.getDistrNo()))
            && (this.getDistrLineNo() == null ? other.getDistrLineNo() == null : this.getDistrLineNo().equals(other.getDistrLineNo()))
            && (this.getDistrDate() == null ? other.getDistrDate() == null : this.getDistrDate().equals(other.getDistrDate()))
            && (this.getBatchNo() == null ? other.getBatchNo() == null : this.getBatchNo().equals(other.getBatchNo()))
            && (this.getValidityDate() == null ? other.getValidityDate() == null : this.getValidityDate().equals(other.getValidityDate()))
            && (this.getSapBatchCode() == null ? other.getSapBatchCode() == null : this.getSapBatchCode().equals(other.getSapBatchCode()))
            && (this.getHdBatchCode() == null ? other.getHdBatchCode() == null : this.getHdBatchCode().equals(other.getHdBatchCode()))
            && (this.getDistrQuantity() == null ? other.getDistrQuantity() == null : this.getDistrQuantity().equals(other.getDistrQuantity()))
            && (this.getShouldReturnQuantity() == null ? other.getShouldReturnQuantity() == null : this.getShouldReturnQuantity().equals(other.getShouldReturnQuantity()))
            && (this.getReturnQuantity() == null ? other.getReturnQuantity() == null : this.getReturnQuantity().equals(other.getReturnQuantity()))
            && (this.getWarehouseCode() == null ? other.getWarehouseCode() == null : this.getWarehouseCode().equals(other.getWarehouseCode()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getCompanyCode() == null) ? 0 : getCompanyCode().hashCode());
        result = prime * result + ((getCompanyBdpCode() == null) ? 0 : getCompanyBdpCode().hashCode());
        result = prime * result + ((getStoreCode() == null) ? 0 : getStoreCode().hashCode());
        result = prime * result + ((getStoreBdpCode() == null) ? 0 : getStoreBdpCode().hashCode());
        result = prime * result + ((getGoodsNo() == null) ? 0 : getGoodsNo().hashCode());
        result = prime * result + ((getGoodsHdNo() == null) ? 0 : getGoodsHdNo().hashCode());
        result = prime * result + ((getDistrNo() == null) ? 0 : getDistrNo().hashCode());
        result = prime * result + ((getDistrLineNo() == null) ? 0 : getDistrLineNo().hashCode());
        result = prime * result + ((getDistrDate() == null) ? 0 : getDistrDate().hashCode());
        result = prime * result + ((getBatchNo() == null) ? 0 : getBatchNo().hashCode());
        result = prime * result + ((getValidityDate() == null) ? 0 : getValidityDate().hashCode());
        result = prime * result + ((getSapBatchCode() == null) ? 0 : getSapBatchCode().hashCode());
        result = prime * result + ((getHdBatchCode() == null) ? 0 : getHdBatchCode().hashCode());
        result = prime * result + ((getDistrQuantity() == null) ? 0 : getDistrQuantity().hashCode());
        result = prime * result + ((getShouldReturnQuantity() == null) ? 0 : getShouldReturnQuantity().hashCode());
        result = prime * result + ((getReturnQuantity() == null) ? 0 : getReturnQuantity().hashCode());
        result = prime * result + ((getWarehouseCode() == null) ? 0 : getWarehouseCode().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", companyCode=").append(companyCode);
        sb.append(", companyBdpCode=").append(companyBdpCode);
        sb.append(", storeCode=").append(storeCode);
        sb.append(", storeBdpCode=").append(storeBdpCode);
        sb.append(", goodsNo=").append(goodsNo);
        sb.append(", goodsHdNo=").append(goodsHdNo);
        sb.append(", distrNo=").append(distrNo);
        sb.append(", distrLineNo=").append(distrLineNo);
        sb.append(", distrDate=").append(distrDate);
        sb.append(", batchNo=").append(batchNo);
        sb.append(", validityDate=").append(validityDate);
        sb.append(", sapBatchCode=").append(sapBatchCode);
        sb.append(", hdBatchCode=").append(hdBatchCode);
        sb.append(", distrQuantity=").append(distrQuantity);
        sb.append(", shouldReturnQuantity=").append(shouldReturnQuantity);
        sb.append(", returnQuantity=").append(returnQuantity);
        sb.append(", warehouseCode=").append(warehouseCode);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}