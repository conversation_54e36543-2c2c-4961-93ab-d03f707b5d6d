package com.cowell.iscm.entityTidb;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> WMS出库及门店收货
 */
public class IscmWmsLeaveWarehouseStoreReceive implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 平台名称
     */
    private String platformName;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 公司MDM编码
     */
    private String companyCode;

    /**
     * 门店编号
     */
    private String storeCode;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 请货日期
     */
    private Date applyDate;

    /**
     * 生成时间
     */
    private Date generateTime;

    /**
     * 商品编码
     */
    private String goodsNo;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 规格
     */
    private String specifications;

    /**
     * 生产企业
     */
    private String manufacturer;

    /**
     * SAP订单号
     */
    private String sapOrderNo;

    /**
     * dn单号
     */
    private String dnOrderNo;

    /**
     * 订单类型 1：手工 2 ：自动 3：紧急
     */
    private Byte orderType;

    /**
     * 订单数量
     */
    private BigDecimal orderQuantity;

    /**
     * 发货仓
     */
    private String leaveWarehouse;

    /**
     * WMS出库发货数量
     */
    private BigDecimal wmsLeaveWarehouseQuantity;

    /**
     * WMS出库拣货率
     */
    private BigDecimal wmsLeaveWarehouseRate;

    /**
     * 0,完全不满足行数，1,(0%-100%)满足行数，100,100%满足行数
     */
    private Byte wmsLeaveWarehouseRateType;

    /**
     * 状态(-1删除，0正常)
     */
    private Byte status;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新时间
     */
    private Date gmtUpdate;

    /**
     * 扩展字段
     */
    private String extend;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 创建人ID
     */
    private Long createdBy;

    /**
     * 创建人
     */
    private String createdName;

    /**
     * 更新人ID
     */
    private Long updatedBy;

    /**
     * 更新人
     */
    private String updatedName;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getPlatformName() {
        return platformName;
    }

    public void setPlatformName(String platformName) {
        this.platformName = platformName;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getStoreCode() {
        return storeCode;
    }

    public void setStoreCode(String storeCode) {
        this.storeCode = storeCode;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public Date getApplyDate() {
        return applyDate;
    }

    public void setApplyDate(Date applyDate) {
        this.applyDate = applyDate;
    }

    public Date getGenerateTime() {
        return generateTime;
    }

    public void setGenerateTime(Date generateTime) {
        this.generateTime = generateTime;
    }

    public String getGoodsNo() {
        return goodsNo;
    }

    public void setGoodsNo(String goodsNo) {
        this.goodsNo = goodsNo;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getSpecifications() {
        return specifications;
    }

    public void setSpecifications(String specifications) {
        this.specifications = specifications;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    public String getSapOrderNo() {
        return sapOrderNo;
    }

    public void setSapOrderNo(String sapOrderNo) {
        this.sapOrderNo = sapOrderNo;
    }

    public String getDnOrderNo() {
        return dnOrderNo;
    }

    public void setDnOrderNo(String dnOrderNo) {
        this.dnOrderNo = dnOrderNo;
    }

    public Byte getOrderType() {
        return orderType;
    }

    public void setOrderType(Byte orderType) {
        this.orderType = orderType;
    }

    public BigDecimal getOrderQuantity() {
        return orderQuantity;
    }

    public void setOrderQuantity(BigDecimal orderQuantity) {
        this.orderQuantity = orderQuantity;
    }

    public String getLeaveWarehouse() {
        return leaveWarehouse;
    }

    public void setLeaveWarehouse(String leaveWarehouse) {
        this.leaveWarehouse = leaveWarehouse;
    }

    public BigDecimal getWmsLeaveWarehouseQuantity() {
        return wmsLeaveWarehouseQuantity;
    }

    public void setWmsLeaveWarehouseQuantity(BigDecimal wmsLeaveWarehouseQuantity) {
        this.wmsLeaveWarehouseQuantity = wmsLeaveWarehouseQuantity;
    }

    public BigDecimal getWmsLeaveWarehouseRate() {
        return wmsLeaveWarehouseRate;
    }

    public void setWmsLeaveWarehouseRate(BigDecimal wmsLeaveWarehouseRate) {
        this.wmsLeaveWarehouseRate = wmsLeaveWarehouseRate;
    }

    public Byte getWmsLeaveWarehouseRateType() {
        return wmsLeaveWarehouseRateType;
    }

    public void setWmsLeaveWarehouseRateType(Byte wmsLeaveWarehouseRateType) {
        this.wmsLeaveWarehouseRateType = wmsLeaveWarehouseRateType;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtUpdate() {
        return gmtUpdate;
    }

    public void setGmtUpdate(Date gmtUpdate) {
        this.gmtUpdate = gmtUpdate;
    }

    public String getExtend() {
        return extend;
    }

    public void setExtend(String extend) {
        this.extend = extend;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Long getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    public String getCreatedName() {
        return createdName;
    }

    public void setCreatedName(String createdName) {
        this.createdName = createdName;
    }

    public Long getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(Long updatedBy) {
        this.updatedBy = updatedBy;
    }

    public String getUpdatedName() {
        return updatedName;
    }

    public void setUpdatedName(String updatedName) {
        this.updatedName = updatedName;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        IscmWmsLeaveWarehouseStoreReceive other = (IscmWmsLeaveWarehouseStoreReceive) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getPlatformName() == null ? other.getPlatformName() == null : this.getPlatformName().equals(other.getPlatformName()))
            && (this.getCompanyName() == null ? other.getCompanyName() == null : this.getCompanyName().equals(other.getCompanyName()))
            && (this.getCompanyCode() == null ? other.getCompanyCode() == null : this.getCompanyCode().equals(other.getCompanyCode()))
            && (this.getStoreCode() == null ? other.getStoreCode() == null : this.getStoreCode().equals(other.getStoreCode()))
            && (this.getStoreName() == null ? other.getStoreName() == null : this.getStoreName().equals(other.getStoreName()))
            && (this.getApplyDate() == null ? other.getApplyDate() == null : this.getApplyDate().equals(other.getApplyDate()))
            && (this.getGenerateTime() == null ? other.getGenerateTime() == null : this.getGenerateTime().equals(other.getGenerateTime()))
            && (this.getGoodsNo() == null ? other.getGoodsNo() == null : this.getGoodsNo().equals(other.getGoodsNo()))
            && (this.getGoodsName() == null ? other.getGoodsName() == null : this.getGoodsName().equals(other.getGoodsName()))
            && (this.getSpecifications() == null ? other.getSpecifications() == null : this.getSpecifications().equals(other.getSpecifications()))
            && (this.getManufacturer() == null ? other.getManufacturer() == null : this.getManufacturer().equals(other.getManufacturer()))
            && (this.getSapOrderNo() == null ? other.getSapOrderNo() == null : this.getSapOrderNo().equals(other.getSapOrderNo()))
            && (this.getDnOrderNo() == null ? other.getDnOrderNo() == null : this.getDnOrderNo().equals(other.getDnOrderNo()))
            && (this.getOrderType() == null ? other.getOrderType() == null : this.getOrderType().equals(other.getOrderType()))
            && (this.getOrderQuantity() == null ? other.getOrderQuantity() == null : this.getOrderQuantity().equals(other.getOrderQuantity()))
            && (this.getLeaveWarehouse() == null ? other.getLeaveWarehouse() == null : this.getLeaveWarehouse().equals(other.getLeaveWarehouse()))
            && (this.getWmsLeaveWarehouseQuantity() == null ? other.getWmsLeaveWarehouseQuantity() == null : this.getWmsLeaveWarehouseQuantity().equals(other.getWmsLeaveWarehouseQuantity()))
            && (this.getWmsLeaveWarehouseRate() == null ? other.getWmsLeaveWarehouseRate() == null : this.getWmsLeaveWarehouseRate().equals(other.getWmsLeaveWarehouseRate()))
            && (this.getWmsLeaveWarehouseRateType() == null ? other.getWmsLeaveWarehouseRateType() == null : this.getWmsLeaveWarehouseRateType().equals(other.getWmsLeaveWarehouseRateType()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getGmtCreate() == null ? other.getGmtCreate() == null : this.getGmtCreate().equals(other.getGmtCreate()))
            && (this.getGmtUpdate() == null ? other.getGmtUpdate() == null : this.getGmtUpdate().equals(other.getGmtUpdate()))
            && (this.getExtend() == null ? other.getExtend() == null : this.getExtend().equals(other.getExtend()))
            && (this.getVersion() == null ? other.getVersion() == null : this.getVersion().equals(other.getVersion()))
            && (this.getCreatedBy() == null ? other.getCreatedBy() == null : this.getCreatedBy().equals(other.getCreatedBy()))
            && (this.getCreatedName() == null ? other.getCreatedName() == null : this.getCreatedName().equals(other.getCreatedName()))
            && (this.getUpdatedBy() == null ? other.getUpdatedBy() == null : this.getUpdatedBy().equals(other.getUpdatedBy()))
            && (this.getUpdatedName() == null ? other.getUpdatedName() == null : this.getUpdatedName().equals(other.getUpdatedName()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getPlatformName() == null) ? 0 : getPlatformName().hashCode());
        result = prime * result + ((getCompanyName() == null) ? 0 : getCompanyName().hashCode());
        result = prime * result + ((getCompanyCode() == null) ? 0 : getCompanyCode().hashCode());
        result = prime * result + ((getStoreCode() == null) ? 0 : getStoreCode().hashCode());
        result = prime * result + ((getStoreName() == null) ? 0 : getStoreName().hashCode());
        result = prime * result + ((getApplyDate() == null) ? 0 : getApplyDate().hashCode());
        result = prime * result + ((getGenerateTime() == null) ? 0 : getGenerateTime().hashCode());
        result = prime * result + ((getGoodsNo() == null) ? 0 : getGoodsNo().hashCode());
        result = prime * result + ((getGoodsName() == null) ? 0 : getGoodsName().hashCode());
        result = prime * result + ((getSpecifications() == null) ? 0 : getSpecifications().hashCode());
        result = prime * result + ((getManufacturer() == null) ? 0 : getManufacturer().hashCode());
        result = prime * result + ((getSapOrderNo() == null) ? 0 : getSapOrderNo().hashCode());
        result = prime * result + ((getDnOrderNo() == null) ? 0 : getDnOrderNo().hashCode());
        result = prime * result + ((getOrderType() == null) ? 0 : getOrderType().hashCode());
        result = prime * result + ((getOrderQuantity() == null) ? 0 : getOrderQuantity().hashCode());
        result = prime * result + ((getLeaveWarehouse() == null) ? 0 : getLeaveWarehouse().hashCode());
        result = prime * result + ((getWmsLeaveWarehouseQuantity() == null) ? 0 : getWmsLeaveWarehouseQuantity().hashCode());
        result = prime * result + ((getWmsLeaveWarehouseRate() == null) ? 0 : getWmsLeaveWarehouseRate().hashCode());
        result = prime * result + ((getWmsLeaveWarehouseRateType() == null) ? 0 : getWmsLeaveWarehouseRateType().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getGmtCreate() == null) ? 0 : getGmtCreate().hashCode());
        result = prime * result + ((getGmtUpdate() == null) ? 0 : getGmtUpdate().hashCode());
        result = prime * result + ((getExtend() == null) ? 0 : getExtend().hashCode());
        result = prime * result + ((getVersion() == null) ? 0 : getVersion().hashCode());
        result = prime * result + ((getCreatedBy() == null) ? 0 : getCreatedBy().hashCode());
        result = prime * result + ((getCreatedName() == null) ? 0 : getCreatedName().hashCode());
        result = prime * result + ((getUpdatedBy() == null) ? 0 : getUpdatedBy().hashCode());
        result = prime * result + ((getUpdatedName() == null) ? 0 : getUpdatedName().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", platformName=").append(platformName);
        sb.append(", companyName=").append(companyName);
        sb.append(", companyCode=").append(companyCode);
        sb.append(", storeCode=").append(storeCode);
        sb.append(", storeName=").append(storeName);
        sb.append(", applyDate=").append(applyDate);
        sb.append(", generateTime=").append(generateTime);
        sb.append(", goodsNo=").append(goodsNo);
        sb.append(", goodsName=").append(goodsName);
        sb.append(", specifications=").append(specifications);
        sb.append(", manufacturer=").append(manufacturer);
        sb.append(", sapOrderNo=").append(sapOrderNo);
        sb.append(", dnOrderNo=").append(dnOrderNo);
        sb.append(", orderType=").append(orderType);
        sb.append(", orderQuantity=").append(orderQuantity);
        sb.append(", leaveWarehouse=").append(leaveWarehouse);
        sb.append(", wmsLeaveWarehouseQuantity=").append(wmsLeaveWarehouseQuantity);
        sb.append(", wmsLeaveWarehouseRate=").append(wmsLeaveWarehouseRate);
        sb.append(", wmsLeaveWarehouseRateType=").append(wmsLeaveWarehouseRateType);
        sb.append(", status=").append(status);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", gmtUpdate=").append(gmtUpdate);
        sb.append(", extend=").append(extend);
        sb.append(", version=").append(version);
        sb.append(", createdBy=").append(createdBy);
        sb.append(", createdName=").append(createdName);
        sb.append(", updatedBy=").append(updatedBy);
        sb.append(", updatedName=").append(updatedName);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}