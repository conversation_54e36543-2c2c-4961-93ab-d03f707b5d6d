package com.cowell.iscm.entityTidb;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class SrmDtpSalesExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public SrmDtpSalesExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        protected void addCriterionForJDBCDate(String condition, Date value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value.getTime()), property);
        }

        protected void addCriterionForJDBCDate(String condition, List<Date> values, String property) {
            if (values == null || values.size() == 0) {
                throw new RuntimeException("Value list for " + property + " cannot be null or empty");
            }
            List<java.sql.Date> dateList = new ArrayList<java.sql.Date>();
            Iterator<Date> iter = values.iterator();
            while (iter.hasNext()) {
                dateList.add(new java.sql.Date(iter.next().getTime()));
            }
            addCriterion(condition, dateList, property);
        }

        protected void addCriterionForJDBCDate(String condition, Date value1, Date value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value1.getTime()), new java.sql.Date(value2.getTime()), property);
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andBusinessDayDateIsNull() {
            addCriterion("business_day_date is null");
            return (Criteria) this;
        }

        public Criteria andBusinessDayDateIsNotNull() {
            addCriterion("business_day_date is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessDayDateEqualTo(Date value) {
            addCriterionForJDBCDate("business_day_date =", value, "businessDayDate");
            return (Criteria) this;
        }

        public Criteria andBusinessDayDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("business_day_date <>", value, "businessDayDate");
            return (Criteria) this;
        }

        public Criteria andBusinessDayDateGreaterThan(Date value) {
            addCriterionForJDBCDate("business_day_date >", value, "businessDayDate");
            return (Criteria) this;
        }

        public Criteria andBusinessDayDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("business_day_date >=", value, "businessDayDate");
            return (Criteria) this;
        }

        public Criteria andBusinessDayDateLessThan(Date value) {
            addCriterionForJDBCDate("business_day_date <", value, "businessDayDate");
            return (Criteria) this;
        }

        public Criteria andBusinessDayDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("business_day_date <=", value, "businessDayDate");
            return (Criteria) this;
        }

        public Criteria andBusinessDayDateIn(List<Date> values) {
            addCriterionForJDBCDate("business_day_date in", values, "businessDayDate");
            return (Criteria) this;
        }

        public Criteria andBusinessDayDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("business_day_date not in", values, "businessDayDate");
            return (Criteria) this;
        }

        public Criteria andBusinessDayDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("business_day_date between", value1, value2, "businessDayDate");
            return (Criteria) this;
        }

        public Criteria andBusinessDayDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("business_day_date not between", value1, value2, "businessDayDate");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIsNull() {
            addCriterion("company_code is null");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIsNotNull() {
            addCriterion("company_code is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeEqualTo(String value) {
            addCriterion("company_code =", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotEqualTo(String value) {
            addCriterion("company_code <>", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeGreaterThan(String value) {
            addCriterion("company_code >", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeGreaterThanOrEqualTo(String value) {
            addCriterion("company_code >=", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLessThan(String value) {
            addCriterion("company_code <", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLessThanOrEqualTo(String value) {
            addCriterion("company_code <=", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLike(String value) {
            addCriterion("company_code like", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotLike(String value) {
            addCriterion("company_code not like", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIn(List<String> values) {
            addCriterion("company_code in", values, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotIn(List<String> values) {
            addCriterion("company_code not in", values, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeBetween(String value1, String value2) {
            addCriterion("company_code between", value1, value2, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotBetween(String value1, String value2) {
            addCriterion("company_code not between", value1, value2, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyNameIsNull() {
            addCriterion("company_name is null");
            return (Criteria) this;
        }

        public Criteria andCompanyNameIsNotNull() {
            addCriterion("company_name is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyNameEqualTo(String value) {
            addCriterion("company_name =", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotEqualTo(String value) {
            addCriterion("company_name <>", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameGreaterThan(String value) {
            addCriterion("company_name >", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameGreaterThanOrEqualTo(String value) {
            addCriterion("company_name >=", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameLessThan(String value) {
            addCriterion("company_name <", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameLessThanOrEqualTo(String value) {
            addCriterion("company_name <=", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameLike(String value) {
            addCriterion("company_name like", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotLike(String value) {
            addCriterion("company_name not like", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameIn(List<String> values) {
            addCriterion("company_name in", values, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotIn(List<String> values) {
            addCriterion("company_name not in", values, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameBetween(String value1, String value2) {
            addCriterion("company_name between", value1, value2, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotBetween(String value1, String value2) {
            addCriterion("company_name not between", value1, value2, "companyName");
            return (Criteria) this;
        }

        public Criteria andLifnrIsNull() {
            addCriterion("lifnr is null");
            return (Criteria) this;
        }

        public Criteria andLifnrIsNotNull() {
            addCriterion("lifnr is not null");
            return (Criteria) this;
        }

        public Criteria andLifnrEqualTo(String value) {
            addCriterion("lifnr =", value, "lifnr");
            return (Criteria) this;
        }

        public Criteria andLifnrNotEqualTo(String value) {
            addCriterion("lifnr <>", value, "lifnr");
            return (Criteria) this;
        }

        public Criteria andLifnrGreaterThan(String value) {
            addCriterion("lifnr >", value, "lifnr");
            return (Criteria) this;
        }

        public Criteria andLifnrGreaterThanOrEqualTo(String value) {
            addCriterion("lifnr >=", value, "lifnr");
            return (Criteria) this;
        }

        public Criteria andLifnrLessThan(String value) {
            addCriterion("lifnr <", value, "lifnr");
            return (Criteria) this;
        }

        public Criteria andLifnrLessThanOrEqualTo(String value) {
            addCriterion("lifnr <=", value, "lifnr");
            return (Criteria) this;
        }

        public Criteria andLifnrLike(String value) {
            addCriterion("lifnr like", value, "lifnr");
            return (Criteria) this;
        }

        public Criteria andLifnrNotLike(String value) {
            addCriterion("lifnr not like", value, "lifnr");
            return (Criteria) this;
        }

        public Criteria andLifnrIn(List<String> values) {
            addCriterion("lifnr in", values, "lifnr");
            return (Criteria) this;
        }

        public Criteria andLifnrNotIn(List<String> values) {
            addCriterion("lifnr not in", values, "lifnr");
            return (Criteria) this;
        }

        public Criteria andLifnrBetween(String value1, String value2) {
            addCriterion("lifnr between", value1, value2, "lifnr");
            return (Criteria) this;
        }

        public Criteria andLifnrNotBetween(String value1, String value2) {
            addCriterion("lifnr not between", value1, value2, "lifnr");
            return (Criteria) this;
        }

        public Criteria andLifnrNameIsNull() {
            addCriterion("lifnr_name is null");
            return (Criteria) this;
        }

        public Criteria andLifnrNameIsNotNull() {
            addCriterion("lifnr_name is not null");
            return (Criteria) this;
        }

        public Criteria andLifnrNameEqualTo(String value) {
            addCriterion("lifnr_name =", value, "lifnrName");
            return (Criteria) this;
        }

        public Criteria andLifnrNameNotEqualTo(String value) {
            addCriterion("lifnr_name <>", value, "lifnrName");
            return (Criteria) this;
        }

        public Criteria andLifnrNameGreaterThan(String value) {
            addCriterion("lifnr_name >", value, "lifnrName");
            return (Criteria) this;
        }

        public Criteria andLifnrNameGreaterThanOrEqualTo(String value) {
            addCriterion("lifnr_name >=", value, "lifnrName");
            return (Criteria) this;
        }

        public Criteria andLifnrNameLessThan(String value) {
            addCriterion("lifnr_name <", value, "lifnrName");
            return (Criteria) this;
        }

        public Criteria andLifnrNameLessThanOrEqualTo(String value) {
            addCriterion("lifnr_name <=", value, "lifnrName");
            return (Criteria) this;
        }

        public Criteria andLifnrNameLike(String value) {
            addCriterion("lifnr_name like", value, "lifnrName");
            return (Criteria) this;
        }

        public Criteria andLifnrNameNotLike(String value) {
            addCriterion("lifnr_name not like", value, "lifnrName");
            return (Criteria) this;
        }

        public Criteria andLifnrNameIn(List<String> values) {
            addCriterion("lifnr_name in", values, "lifnrName");
            return (Criteria) this;
        }

        public Criteria andLifnrNameNotIn(List<String> values) {
            addCriterion("lifnr_name not in", values, "lifnrName");
            return (Criteria) this;
        }

        public Criteria andLifnrNameBetween(String value1, String value2) {
            addCriterion("lifnr_name between", value1, value2, "lifnrName");
            return (Criteria) this;
        }

        public Criteria andLifnrNameNotBetween(String value1, String value2) {
            addCriterion("lifnr_name not between", value1, value2, "lifnrName");
            return (Criteria) this;
        }

        public Criteria andRetailStoreIdIsNull() {
            addCriterion("retail_store_id is null");
            return (Criteria) this;
        }

        public Criteria andRetailStoreIdIsNotNull() {
            addCriterion("retail_store_id is not null");
            return (Criteria) this;
        }

        public Criteria andRetailStoreIdEqualTo(String value) {
            addCriterion("retail_store_id =", value, "retailStoreId");
            return (Criteria) this;
        }

        public Criteria andRetailStoreIdNotEqualTo(String value) {
            addCriterion("retail_store_id <>", value, "retailStoreId");
            return (Criteria) this;
        }

        public Criteria andRetailStoreIdGreaterThan(String value) {
            addCriterion("retail_store_id >", value, "retailStoreId");
            return (Criteria) this;
        }

        public Criteria andRetailStoreIdGreaterThanOrEqualTo(String value) {
            addCriterion("retail_store_id >=", value, "retailStoreId");
            return (Criteria) this;
        }

        public Criteria andRetailStoreIdLessThan(String value) {
            addCriterion("retail_store_id <", value, "retailStoreId");
            return (Criteria) this;
        }

        public Criteria andRetailStoreIdLessThanOrEqualTo(String value) {
            addCriterion("retail_store_id <=", value, "retailStoreId");
            return (Criteria) this;
        }

        public Criteria andRetailStoreIdLike(String value) {
            addCriterion("retail_store_id like", value, "retailStoreId");
            return (Criteria) this;
        }

        public Criteria andRetailStoreIdNotLike(String value) {
            addCriterion("retail_store_id not like", value, "retailStoreId");
            return (Criteria) this;
        }

        public Criteria andRetailStoreIdIn(List<String> values) {
            addCriterion("retail_store_id in", values, "retailStoreId");
            return (Criteria) this;
        }

        public Criteria andRetailStoreIdNotIn(List<String> values) {
            addCriterion("retail_store_id not in", values, "retailStoreId");
            return (Criteria) this;
        }

        public Criteria andRetailStoreIdBetween(String value1, String value2) {
            addCriterion("retail_store_id between", value1, value2, "retailStoreId");
            return (Criteria) this;
        }

        public Criteria andRetailStoreIdNotBetween(String value1, String value2) {
            addCriterion("retail_store_id not between", value1, value2, "retailStoreId");
            return (Criteria) this;
        }

        public Criteria andRetailStoreNameIsNull() {
            addCriterion("retail_store_name is null");
            return (Criteria) this;
        }

        public Criteria andRetailStoreNameIsNotNull() {
            addCriterion("retail_store_name is not null");
            return (Criteria) this;
        }

        public Criteria andRetailStoreNameEqualTo(String value) {
            addCriterion("retail_store_name =", value, "retailStoreName");
            return (Criteria) this;
        }

        public Criteria andRetailStoreNameNotEqualTo(String value) {
            addCriterion("retail_store_name <>", value, "retailStoreName");
            return (Criteria) this;
        }

        public Criteria andRetailStoreNameGreaterThan(String value) {
            addCriterion("retail_store_name >", value, "retailStoreName");
            return (Criteria) this;
        }

        public Criteria andRetailStoreNameGreaterThanOrEqualTo(String value) {
            addCriterion("retail_store_name >=", value, "retailStoreName");
            return (Criteria) this;
        }

        public Criteria andRetailStoreNameLessThan(String value) {
            addCriterion("retail_store_name <", value, "retailStoreName");
            return (Criteria) this;
        }

        public Criteria andRetailStoreNameLessThanOrEqualTo(String value) {
            addCriterion("retail_store_name <=", value, "retailStoreName");
            return (Criteria) this;
        }

        public Criteria andRetailStoreNameLike(String value) {
            addCriterion("retail_store_name like", value, "retailStoreName");
            return (Criteria) this;
        }

        public Criteria andRetailStoreNameNotLike(String value) {
            addCriterion("retail_store_name not like", value, "retailStoreName");
            return (Criteria) this;
        }

        public Criteria andRetailStoreNameIn(List<String> values) {
            addCriterion("retail_store_name in", values, "retailStoreName");
            return (Criteria) this;
        }

        public Criteria andRetailStoreNameNotIn(List<String> values) {
            addCriterion("retail_store_name not in", values, "retailStoreName");
            return (Criteria) this;
        }

        public Criteria andRetailStoreNameBetween(String value1, String value2) {
            addCriterion("retail_store_name between", value1, value2, "retailStoreName");
            return (Criteria) this;
        }

        public Criteria andRetailStoreNameNotBetween(String value1, String value2) {
            addCriterion("retail_store_name not between", value1, value2, "retailStoreName");
            return (Criteria) this;
        }

        public Criteria andTemidIsNull() {
            addCriterion("temid is null");
            return (Criteria) this;
        }

        public Criteria andTemidIsNotNull() {
            addCriterion("temid is not null");
            return (Criteria) this;
        }

        public Criteria andTemidEqualTo(String value) {
            addCriterion("temid =", value, "temid");
            return (Criteria) this;
        }

        public Criteria andTemidNotEqualTo(String value) {
            addCriterion("temid <>", value, "temid");
            return (Criteria) this;
        }

        public Criteria andTemidGreaterThan(String value) {
            addCriterion("temid >", value, "temid");
            return (Criteria) this;
        }

        public Criteria andTemidGreaterThanOrEqualTo(String value) {
            addCriterion("temid >=", value, "temid");
            return (Criteria) this;
        }

        public Criteria andTemidLessThan(String value) {
            addCriterion("temid <", value, "temid");
            return (Criteria) this;
        }

        public Criteria andTemidLessThanOrEqualTo(String value) {
            addCriterion("temid <=", value, "temid");
            return (Criteria) this;
        }

        public Criteria andTemidLike(String value) {
            addCriterion("temid like", value, "temid");
            return (Criteria) this;
        }

        public Criteria andTemidNotLike(String value) {
            addCriterion("temid not like", value, "temid");
            return (Criteria) this;
        }

        public Criteria andTemidIn(List<String> values) {
            addCriterion("temid in", values, "temid");
            return (Criteria) this;
        }

        public Criteria andTemidNotIn(List<String> values) {
            addCriterion("temid not in", values, "temid");
            return (Criteria) this;
        }

        public Criteria andTemidBetween(String value1, String value2) {
            addCriterion("temid between", value1, value2, "temid");
            return (Criteria) this;
        }

        public Criteria andTemidNotBetween(String value1, String value2) {
            addCriterion("temid not between", value1, value2, "temid");
            return (Criteria) this;
        }

        public Criteria andTemidNameIsNull() {
            addCriterion("temid_name is null");
            return (Criteria) this;
        }

        public Criteria andTemidNameIsNotNull() {
            addCriterion("temid_name is not null");
            return (Criteria) this;
        }

        public Criteria andTemidNameEqualTo(String value) {
            addCriterion("temid_name =", value, "temidName");
            return (Criteria) this;
        }

        public Criteria andTemidNameNotEqualTo(String value) {
            addCriterion("temid_name <>", value, "temidName");
            return (Criteria) this;
        }

        public Criteria andTemidNameGreaterThan(String value) {
            addCriterion("temid_name >", value, "temidName");
            return (Criteria) this;
        }

        public Criteria andTemidNameGreaterThanOrEqualTo(String value) {
            addCriterion("temid_name >=", value, "temidName");
            return (Criteria) this;
        }

        public Criteria andTemidNameLessThan(String value) {
            addCriterion("temid_name <", value, "temidName");
            return (Criteria) this;
        }

        public Criteria andTemidNameLessThanOrEqualTo(String value) {
            addCriterion("temid_name <=", value, "temidName");
            return (Criteria) this;
        }

        public Criteria andTemidNameLike(String value) {
            addCriterion("temid_name like", value, "temidName");
            return (Criteria) this;
        }

        public Criteria andTemidNameNotLike(String value) {
            addCriterion("temid_name not like", value, "temidName");
            return (Criteria) this;
        }

        public Criteria andTemidNameIn(List<String> values) {
            addCriterion("temid_name in", values, "temidName");
            return (Criteria) this;
        }

        public Criteria andTemidNameNotIn(List<String> values) {
            addCriterion("temid_name not in", values, "temidName");
            return (Criteria) this;
        }

        public Criteria andTemidNameBetween(String value1, String value2) {
            addCriterion("temid_name between", value1, value2, "temidName");
            return (Criteria) this;
        }

        public Criteria andTemidNameNotBetween(String value1, String value2) {
            addCriterion("temid_name not between", value1, value2, "temidName");
            return (Criteria) this;
        }

        public Criteria andTemidSpecIsNull() {
            addCriterion("temid_spec is null");
            return (Criteria) this;
        }

        public Criteria andTemidSpecIsNotNull() {
            addCriterion("temid_spec is not null");
            return (Criteria) this;
        }

        public Criteria andTemidSpecEqualTo(String value) {
            addCriterion("temid_spec =", value, "temidSpec");
            return (Criteria) this;
        }

        public Criteria andTemidSpecNotEqualTo(String value) {
            addCriterion("temid_spec <>", value, "temidSpec");
            return (Criteria) this;
        }

        public Criteria andTemidSpecGreaterThan(String value) {
            addCriterion("temid_spec >", value, "temidSpec");
            return (Criteria) this;
        }

        public Criteria andTemidSpecGreaterThanOrEqualTo(String value) {
            addCriterion("temid_spec >=", value, "temidSpec");
            return (Criteria) this;
        }

        public Criteria andTemidSpecLessThan(String value) {
            addCriterion("temid_spec <", value, "temidSpec");
            return (Criteria) this;
        }

        public Criteria andTemidSpecLessThanOrEqualTo(String value) {
            addCriterion("temid_spec <=", value, "temidSpec");
            return (Criteria) this;
        }

        public Criteria andTemidSpecLike(String value) {
            addCriterion("temid_spec like", value, "temidSpec");
            return (Criteria) this;
        }

        public Criteria andTemidSpecNotLike(String value) {
            addCriterion("temid_spec not like", value, "temidSpec");
            return (Criteria) this;
        }

        public Criteria andTemidSpecIn(List<String> values) {
            addCriterion("temid_spec in", values, "temidSpec");
            return (Criteria) this;
        }

        public Criteria andTemidSpecNotIn(List<String> values) {
            addCriterion("temid_spec not in", values, "temidSpec");
            return (Criteria) this;
        }

        public Criteria andTemidSpecBetween(String value1, String value2) {
            addCriterion("temid_spec between", value1, value2, "temidSpec");
            return (Criteria) this;
        }

        public Criteria andTemidSpecNotBetween(String value1, String value2) {
            addCriterion("temid_spec not between", value1, value2, "temidSpec");
            return (Criteria) this;
        }

        public Criteria andManuIdIsNull() {
            addCriterion("manu_id is null");
            return (Criteria) this;
        }

        public Criteria andManuIdIsNotNull() {
            addCriterion("manu_id is not null");
            return (Criteria) this;
        }

        public Criteria andManuIdEqualTo(String value) {
            addCriterion("manu_id =", value, "manuId");
            return (Criteria) this;
        }

        public Criteria andManuIdNotEqualTo(String value) {
            addCriterion("manu_id <>", value, "manuId");
            return (Criteria) this;
        }

        public Criteria andManuIdGreaterThan(String value) {
            addCriterion("manu_id >", value, "manuId");
            return (Criteria) this;
        }

        public Criteria andManuIdGreaterThanOrEqualTo(String value) {
            addCriterion("manu_id >=", value, "manuId");
            return (Criteria) this;
        }

        public Criteria andManuIdLessThan(String value) {
            addCriterion("manu_id <", value, "manuId");
            return (Criteria) this;
        }

        public Criteria andManuIdLessThanOrEqualTo(String value) {
            addCriterion("manu_id <=", value, "manuId");
            return (Criteria) this;
        }

        public Criteria andManuIdLike(String value) {
            addCriterion("manu_id like", value, "manuId");
            return (Criteria) this;
        }

        public Criteria andManuIdNotLike(String value) {
            addCriterion("manu_id not like", value, "manuId");
            return (Criteria) this;
        }

        public Criteria andManuIdIn(List<String> values) {
            addCriterion("manu_id in", values, "manuId");
            return (Criteria) this;
        }

        public Criteria andManuIdNotIn(List<String> values) {
            addCriterion("manu_id not in", values, "manuId");
            return (Criteria) this;
        }

        public Criteria andManuIdBetween(String value1, String value2) {
            addCriterion("manu_id between", value1, value2, "manuId");
            return (Criteria) this;
        }

        public Criteria andManuIdNotBetween(String value1, String value2) {
            addCriterion("manu_id not between", value1, value2, "manuId");
            return (Criteria) this;
        }

        public Criteria andManuIsNull() {
            addCriterion("manu is null");
            return (Criteria) this;
        }

        public Criteria andManuIsNotNull() {
            addCriterion("manu is not null");
            return (Criteria) this;
        }

        public Criteria andManuEqualTo(String value) {
            addCriterion("manu =", value, "manu");
            return (Criteria) this;
        }

        public Criteria andManuNotEqualTo(String value) {
            addCriterion("manu <>", value, "manu");
            return (Criteria) this;
        }

        public Criteria andManuGreaterThan(String value) {
            addCriterion("manu >", value, "manu");
            return (Criteria) this;
        }

        public Criteria andManuGreaterThanOrEqualTo(String value) {
            addCriterion("manu >=", value, "manu");
            return (Criteria) this;
        }

        public Criteria andManuLessThan(String value) {
            addCriterion("manu <", value, "manu");
            return (Criteria) this;
        }

        public Criteria andManuLessThanOrEqualTo(String value) {
            addCriterion("manu <=", value, "manu");
            return (Criteria) this;
        }

        public Criteria andManuLike(String value) {
            addCriterion("manu like", value, "manu");
            return (Criteria) this;
        }

        public Criteria andManuNotLike(String value) {
            addCriterion("manu not like", value, "manu");
            return (Criteria) this;
        }

        public Criteria andManuIn(List<String> values) {
            addCriterion("manu in", values, "manu");
            return (Criteria) this;
        }

        public Criteria andManuNotIn(List<String> values) {
            addCriterion("manu not in", values, "manu");
            return (Criteria) this;
        }

        public Criteria andManuBetween(String value1, String value2) {
            addCriterion("manu between", value1, value2, "manu");
            return (Criteria) this;
        }

        public Criteria andManuNotBetween(String value1, String value2) {
            addCriterion("manu not between", value1, value2, "manu");
            return (Criteria) this;
        }

        public Criteria andBatchCodeIsNull() {
            addCriterion("batch_code is null");
            return (Criteria) this;
        }

        public Criteria andBatchCodeIsNotNull() {
            addCriterion("batch_code is not null");
            return (Criteria) this;
        }

        public Criteria andBatchCodeEqualTo(String value) {
            addCriterion("batch_code =", value, "batchCode");
            return (Criteria) this;
        }

        public Criteria andBatchCodeNotEqualTo(String value) {
            addCriterion("batch_code <>", value, "batchCode");
            return (Criteria) this;
        }

        public Criteria andBatchCodeGreaterThan(String value) {
            addCriterion("batch_code >", value, "batchCode");
            return (Criteria) this;
        }

        public Criteria andBatchCodeGreaterThanOrEqualTo(String value) {
            addCriterion("batch_code >=", value, "batchCode");
            return (Criteria) this;
        }

        public Criteria andBatchCodeLessThan(String value) {
            addCriterion("batch_code <", value, "batchCode");
            return (Criteria) this;
        }

        public Criteria andBatchCodeLessThanOrEqualTo(String value) {
            addCriterion("batch_code <=", value, "batchCode");
            return (Criteria) this;
        }

        public Criteria andBatchCodeLike(String value) {
            addCriterion("batch_code like", value, "batchCode");
            return (Criteria) this;
        }

        public Criteria andBatchCodeNotLike(String value) {
            addCriterion("batch_code not like", value, "batchCode");
            return (Criteria) this;
        }

        public Criteria andBatchCodeIn(List<String> values) {
            addCriterion("batch_code in", values, "batchCode");
            return (Criteria) this;
        }

        public Criteria andBatchCodeNotIn(List<String> values) {
            addCriterion("batch_code not in", values, "batchCode");
            return (Criteria) this;
        }

        public Criteria andBatchCodeBetween(String value1, String value2) {
            addCriterion("batch_code between", value1, value2, "batchCode");
            return (Criteria) this;
        }

        public Criteria andBatchCodeNotBetween(String value1, String value2) {
            addCriterion("batch_code not between", value1, value2, "batchCode");
            return (Criteria) this;
        }

        public Criteria andEffDateIsNull() {
            addCriterion("eff_date is null");
            return (Criteria) this;
        }

        public Criteria andEffDateIsNotNull() {
            addCriterion("eff_date is not null");
            return (Criteria) this;
        }

        public Criteria andEffDateEqualTo(Date value) {
            addCriterionForJDBCDate("eff_date =", value, "effDate");
            return (Criteria) this;
        }

        public Criteria andEffDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("eff_date <>", value, "effDate");
            return (Criteria) this;
        }

        public Criteria andEffDateGreaterThan(Date value) {
            addCriterionForJDBCDate("eff_date >", value, "effDate");
            return (Criteria) this;
        }

        public Criteria andEffDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("eff_date >=", value, "effDate");
            return (Criteria) this;
        }

        public Criteria andEffDateLessThan(Date value) {
            addCriterionForJDBCDate("eff_date <", value, "effDate");
            return (Criteria) this;
        }

        public Criteria andEffDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("eff_date <=", value, "effDate");
            return (Criteria) this;
        }

        public Criteria andEffDateIn(List<Date> values) {
            addCriterionForJDBCDate("eff_date in", values, "effDate");
            return (Criteria) this;
        }

        public Criteria andEffDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("eff_date not in", values, "effDate");
            return (Criteria) this;
        }

        public Criteria andEffDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("eff_date between", value1, value2, "effDate");
            return (Criteria) this;
        }

        public Criteria andEffDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("eff_date not between", value1, value2, "effDate");
            return (Criteria) this;
        }

        public Criteria andProductDateIsNull() {
            addCriterion("product_date is null");
            return (Criteria) this;
        }

        public Criteria andProductDateIsNotNull() {
            addCriterion("product_date is not null");
            return (Criteria) this;
        }

        public Criteria andProductDateEqualTo(Date value) {
            addCriterionForJDBCDate("product_date =", value, "productDate");
            return (Criteria) this;
        }

        public Criteria andProductDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("product_date <>", value, "productDate");
            return (Criteria) this;
        }

        public Criteria andProductDateGreaterThan(Date value) {
            addCriterionForJDBCDate("product_date >", value, "productDate");
            return (Criteria) this;
        }

        public Criteria andProductDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("product_date >=", value, "productDate");
            return (Criteria) this;
        }

        public Criteria andProductDateLessThan(Date value) {
            addCriterionForJDBCDate("product_date <", value, "productDate");
            return (Criteria) this;
        }

        public Criteria andProductDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("product_date <=", value, "productDate");
            return (Criteria) this;
        }

        public Criteria andProductDateIn(List<Date> values) {
            addCriterionForJDBCDate("product_date in", values, "productDate");
            return (Criteria) this;
        }

        public Criteria andProductDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("product_date not in", values, "productDate");
            return (Criteria) this;
        }

        public Criteria andProductDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("product_date between", value1, value2, "productDate");
            return (Criteria) this;
        }

        public Criteria andProductDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("product_date not between", value1, value2, "productDate");
            return (Criteria) this;
        }

        public Criteria andUnitIsNull() {
            addCriterion("unit is null");
            return (Criteria) this;
        }

        public Criteria andUnitIsNotNull() {
            addCriterion("unit is not null");
            return (Criteria) this;
        }

        public Criteria andUnitEqualTo(String value) {
            addCriterion("unit =", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotEqualTo(String value) {
            addCriterion("unit <>", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitGreaterThan(String value) {
            addCriterion("unit >", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitGreaterThanOrEqualTo(String value) {
            addCriterion("unit >=", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLessThan(String value) {
            addCriterion("unit <", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLessThanOrEqualTo(String value) {
            addCriterion("unit <=", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLike(String value) {
            addCriterion("unit like", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotLike(String value) {
            addCriterion("unit not like", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitIn(List<String> values) {
            addCriterion("unit in", values, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotIn(List<String> values) {
            addCriterion("unit not in", values, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitBetween(String value1, String value2) {
            addCriterion("unit between", value1, value2, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotBetween(String value1, String value2) {
            addCriterion("unit not between", value1, value2, "unit");
            return (Criteria) this;
        }

        public Criteria andBarCodeIsNull() {
            addCriterion("bar_code is null");
            return (Criteria) this;
        }

        public Criteria andBarCodeIsNotNull() {
            addCriterion("bar_code is not null");
            return (Criteria) this;
        }

        public Criteria andBarCodeEqualTo(String value) {
            addCriterion("bar_code =", value, "barCode");
            return (Criteria) this;
        }

        public Criteria andBarCodeNotEqualTo(String value) {
            addCriterion("bar_code <>", value, "barCode");
            return (Criteria) this;
        }

        public Criteria andBarCodeGreaterThan(String value) {
            addCriterion("bar_code >", value, "barCode");
            return (Criteria) this;
        }

        public Criteria andBarCodeGreaterThanOrEqualTo(String value) {
            addCriterion("bar_code >=", value, "barCode");
            return (Criteria) this;
        }

        public Criteria andBarCodeLessThan(String value) {
            addCriterion("bar_code <", value, "barCode");
            return (Criteria) this;
        }

        public Criteria andBarCodeLessThanOrEqualTo(String value) {
            addCriterion("bar_code <=", value, "barCode");
            return (Criteria) this;
        }

        public Criteria andBarCodeLike(String value) {
            addCriterion("bar_code like", value, "barCode");
            return (Criteria) this;
        }

        public Criteria andBarCodeNotLike(String value) {
            addCriterion("bar_code not like", value, "barCode");
            return (Criteria) this;
        }

        public Criteria andBarCodeIn(List<String> values) {
            addCriterion("bar_code in", values, "barCode");
            return (Criteria) this;
        }

        public Criteria andBarCodeNotIn(List<String> values) {
            addCriterion("bar_code not in", values, "barCode");
            return (Criteria) this;
        }

        public Criteria andBarCodeBetween(String value1, String value2) {
            addCriterion("bar_code between", value1, value2, "barCode");
            return (Criteria) this;
        }

        public Criteria andBarCodeNotBetween(String value1, String value2) {
            addCriterion("bar_code not between", value1, value2, "barCode");
            return (Criteria) this;
        }

        public Criteria andRetailQuantityIsNull() {
            addCriterion("retail_quantity is null");
            return (Criteria) this;
        }

        public Criteria andRetailQuantityIsNotNull() {
            addCriterion("retail_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andRetailQuantityEqualTo(BigDecimal value) {
            addCriterion("retail_quantity =", value, "retailQuantity");
            return (Criteria) this;
        }

        public Criteria andRetailQuantityNotEqualTo(BigDecimal value) {
            addCriterion("retail_quantity <>", value, "retailQuantity");
            return (Criteria) this;
        }

        public Criteria andRetailQuantityGreaterThan(BigDecimal value) {
            addCriterion("retail_quantity >", value, "retailQuantity");
            return (Criteria) this;
        }

        public Criteria andRetailQuantityGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("retail_quantity >=", value, "retailQuantity");
            return (Criteria) this;
        }

        public Criteria andRetailQuantityLessThan(BigDecimal value) {
            addCriterion("retail_quantity <", value, "retailQuantity");
            return (Criteria) this;
        }

        public Criteria andRetailQuantityLessThanOrEqualTo(BigDecimal value) {
            addCriterion("retail_quantity <=", value, "retailQuantity");
            return (Criteria) this;
        }

        public Criteria andRetailQuantityIn(List<BigDecimal> values) {
            addCriterion("retail_quantity in", values, "retailQuantity");
            return (Criteria) this;
        }

        public Criteria andRetailQuantityNotIn(List<BigDecimal> values) {
            addCriterion("retail_quantity not in", values, "retailQuantity");
            return (Criteria) this;
        }

        public Criteria andRetailQuantityBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("retail_quantity between", value1, value2, "retailQuantity");
            return (Criteria) this;
        }

        public Criteria andRetailQuantityNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("retail_quantity not between", value1, value2, "retailQuantity");
            return (Criteria) this;
        }

        public Criteria andRetailAmountIsNull() {
            addCriterion("retail_amount is null");
            return (Criteria) this;
        }

        public Criteria andRetailAmountIsNotNull() {
            addCriterion("retail_amount is not null");
            return (Criteria) this;
        }

        public Criteria andRetailAmountEqualTo(BigDecimal value) {
            addCriterion("retail_amount =", value, "retailAmount");
            return (Criteria) this;
        }

        public Criteria andRetailAmountNotEqualTo(BigDecimal value) {
            addCriterion("retail_amount <>", value, "retailAmount");
            return (Criteria) this;
        }

        public Criteria andRetailAmountGreaterThan(BigDecimal value) {
            addCriterion("retail_amount >", value, "retailAmount");
            return (Criteria) this;
        }

        public Criteria andRetailAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("retail_amount >=", value, "retailAmount");
            return (Criteria) this;
        }

        public Criteria andRetailAmountLessThan(BigDecimal value) {
            addCriterion("retail_amount <", value, "retailAmount");
            return (Criteria) this;
        }

        public Criteria andRetailAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("retail_amount <=", value, "retailAmount");
            return (Criteria) this;
        }

        public Criteria andRetailAmountIn(List<BigDecimal> values) {
            addCriterion("retail_amount in", values, "retailAmount");
            return (Criteria) this;
        }

        public Criteria andRetailAmountNotIn(List<BigDecimal> values) {
            addCriterion("retail_amount not in", values, "retailAmount");
            return (Criteria) this;
        }

        public Criteria andRetailAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("retail_amount between", value1, value2, "retailAmount");
            return (Criteria) this;
        }

        public Criteria andRetailAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("retail_amount not between", value1, value2, "retailAmount");
            return (Criteria) this;
        }

        public Criteria andHospitalIdIsNull() {
            addCriterion("hospital_id is null");
            return (Criteria) this;
        }

        public Criteria andHospitalIdIsNotNull() {
            addCriterion("hospital_id is not null");
            return (Criteria) this;
        }

        public Criteria andHospitalIdEqualTo(String value) {
            addCriterion("hospital_id =", value, "hospitalId");
            return (Criteria) this;
        }

        public Criteria andHospitalIdNotEqualTo(String value) {
            addCriterion("hospital_id <>", value, "hospitalId");
            return (Criteria) this;
        }

        public Criteria andHospitalIdGreaterThan(String value) {
            addCriterion("hospital_id >", value, "hospitalId");
            return (Criteria) this;
        }

        public Criteria andHospitalIdGreaterThanOrEqualTo(String value) {
            addCriterion("hospital_id >=", value, "hospitalId");
            return (Criteria) this;
        }

        public Criteria andHospitalIdLessThan(String value) {
            addCriterion("hospital_id <", value, "hospitalId");
            return (Criteria) this;
        }

        public Criteria andHospitalIdLessThanOrEqualTo(String value) {
            addCriterion("hospital_id <=", value, "hospitalId");
            return (Criteria) this;
        }

        public Criteria andHospitalIdLike(String value) {
            addCriterion("hospital_id like", value, "hospitalId");
            return (Criteria) this;
        }

        public Criteria andHospitalIdNotLike(String value) {
            addCriterion("hospital_id not like", value, "hospitalId");
            return (Criteria) this;
        }

        public Criteria andHospitalIdIn(List<String> values) {
            addCriterion("hospital_id in", values, "hospitalId");
            return (Criteria) this;
        }

        public Criteria andHospitalIdNotIn(List<String> values) {
            addCriterion("hospital_id not in", values, "hospitalId");
            return (Criteria) this;
        }

        public Criteria andHospitalIdBetween(String value1, String value2) {
            addCriterion("hospital_id between", value1, value2, "hospitalId");
            return (Criteria) this;
        }

        public Criteria andHospitalIdNotBetween(String value1, String value2) {
            addCriterion("hospital_id not between", value1, value2, "hospitalId");
            return (Criteria) this;
        }

        public Criteria andHospitalNameIsNull() {
            addCriterion("hospital_name is null");
            return (Criteria) this;
        }

        public Criteria andHospitalNameIsNotNull() {
            addCriterion("hospital_name is not null");
            return (Criteria) this;
        }

        public Criteria andHospitalNameEqualTo(String value) {
            addCriterion("hospital_name =", value, "hospitalName");
            return (Criteria) this;
        }

        public Criteria andHospitalNameNotEqualTo(String value) {
            addCriterion("hospital_name <>", value, "hospitalName");
            return (Criteria) this;
        }

        public Criteria andHospitalNameGreaterThan(String value) {
            addCriterion("hospital_name >", value, "hospitalName");
            return (Criteria) this;
        }

        public Criteria andHospitalNameGreaterThanOrEqualTo(String value) {
            addCriterion("hospital_name >=", value, "hospitalName");
            return (Criteria) this;
        }

        public Criteria andHospitalNameLessThan(String value) {
            addCriterion("hospital_name <", value, "hospitalName");
            return (Criteria) this;
        }

        public Criteria andHospitalNameLessThanOrEqualTo(String value) {
            addCriterion("hospital_name <=", value, "hospitalName");
            return (Criteria) this;
        }

        public Criteria andHospitalNameLike(String value) {
            addCriterion("hospital_name like", value, "hospitalName");
            return (Criteria) this;
        }

        public Criteria andHospitalNameNotLike(String value) {
            addCriterion("hospital_name not like", value, "hospitalName");
            return (Criteria) this;
        }

        public Criteria andHospitalNameIn(List<String> values) {
            addCriterion("hospital_name in", values, "hospitalName");
            return (Criteria) this;
        }

        public Criteria andHospitalNameNotIn(List<String> values) {
            addCriterion("hospital_name not in", values, "hospitalName");
            return (Criteria) this;
        }

        public Criteria andHospitalNameBetween(String value1, String value2) {
            addCriterion("hospital_name between", value1, value2, "hospitalName");
            return (Criteria) this;
        }

        public Criteria andHospitalNameNotBetween(String value1, String value2) {
            addCriterion("hospital_name not between", value1, value2, "hospitalName");
            return (Criteria) this;
        }

        public Criteria andOrderNoIsNull() {
            addCriterion("order_no is null");
            return (Criteria) this;
        }

        public Criteria andOrderNoIsNotNull() {
            addCriterion("order_no is not null");
            return (Criteria) this;
        }

        public Criteria andOrderNoEqualTo(String value) {
            addCriterion("order_no =", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotEqualTo(String value) {
            addCriterion("order_no <>", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoGreaterThan(String value) {
            addCriterion("order_no >", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoGreaterThanOrEqualTo(String value) {
            addCriterion("order_no >=", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLessThan(String value) {
            addCriterion("order_no <", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLessThanOrEqualTo(String value) {
            addCriterion("order_no <=", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLike(String value) {
            addCriterion("order_no like", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotLike(String value) {
            addCriterion("order_no not like", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoIn(List<String> values) {
            addCriterion("order_no in", values, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotIn(List<String> values) {
            addCriterion("order_no not in", values, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoBetween(String value1, String value2) {
            addCriterion("order_no between", value1, value2, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotBetween(String value1, String value2) {
            addCriterion("order_no not between", value1, value2, "orderNo");
            return (Criteria) this;
        }

        public Criteria andIsSapIsNull() {
            addCriterion("is_sap is null");
            return (Criteria) this;
        }

        public Criteria andIsSapIsNotNull() {
            addCriterion("is_sap is not null");
            return (Criteria) this;
        }

        public Criteria andIsSapEqualTo(Byte value) {
            addCriterion("is_sap =", value, "isSap");
            return (Criteria) this;
        }

        public Criteria andIsSapNotEqualTo(Byte value) {
            addCriterion("is_sap <>", value, "isSap");
            return (Criteria) this;
        }

        public Criteria andIsSapGreaterThan(Byte value) {
            addCriterion("is_sap >", value, "isSap");
            return (Criteria) this;
        }

        public Criteria andIsSapGreaterThanOrEqualTo(Byte value) {
            addCriterion("is_sap >=", value, "isSap");
            return (Criteria) this;
        }

        public Criteria andIsSapLessThan(Byte value) {
            addCriterion("is_sap <", value, "isSap");
            return (Criteria) this;
        }

        public Criteria andIsSapLessThanOrEqualTo(Byte value) {
            addCriterion("is_sap <=", value, "isSap");
            return (Criteria) this;
        }

        public Criteria andIsSapIn(List<Byte> values) {
            addCriterion("is_sap in", values, "isSap");
            return (Criteria) this;
        }

        public Criteria andIsSapNotIn(List<Byte> values) {
            addCriterion("is_sap not in", values, "isSap");
            return (Criteria) this;
        }

        public Criteria andIsSapBetween(Byte value1, Byte value2) {
            addCriterion("is_sap between", value1, value2, "isSap");
            return (Criteria) this;
        }

        public Criteria andIsSapNotBetween(Byte value1, Byte value2) {
            addCriterion("is_sap not between", value1, value2, "isSap");
            return (Criteria) this;
        }

        public Criteria andPrescriptionNumberIsNull() {
            addCriterion("prescription_number is null");
            return (Criteria) this;
        }

        public Criteria andPrescriptionNumberIsNotNull() {
            addCriterion("prescription_number is not null");
            return (Criteria) this;
        }

        public Criteria andPrescriptionNumberEqualTo(String value) {
            addCriterion("prescription_number =", value, "prescriptionNumber");
            return (Criteria) this;
        }

        public Criteria andPrescriptionNumberNotEqualTo(String value) {
            addCriterion("prescription_number <>", value, "prescriptionNumber");
            return (Criteria) this;
        }

        public Criteria andPrescriptionNumberGreaterThan(String value) {
            addCriterion("prescription_number >", value, "prescriptionNumber");
            return (Criteria) this;
        }

        public Criteria andPrescriptionNumberGreaterThanOrEqualTo(String value) {
            addCriterion("prescription_number >=", value, "prescriptionNumber");
            return (Criteria) this;
        }

        public Criteria andPrescriptionNumberLessThan(String value) {
            addCriterion("prescription_number <", value, "prescriptionNumber");
            return (Criteria) this;
        }

        public Criteria andPrescriptionNumberLessThanOrEqualTo(String value) {
            addCriterion("prescription_number <=", value, "prescriptionNumber");
            return (Criteria) this;
        }

        public Criteria andPrescriptionNumberLike(String value) {
            addCriterion("prescription_number like", value, "prescriptionNumber");
            return (Criteria) this;
        }

        public Criteria andPrescriptionNumberNotLike(String value) {
            addCriterion("prescription_number not like", value, "prescriptionNumber");
            return (Criteria) this;
        }

        public Criteria andPrescriptionNumberIn(List<String> values) {
            addCriterion("prescription_number in", values, "prescriptionNumber");
            return (Criteria) this;
        }

        public Criteria andPrescriptionNumberNotIn(List<String> values) {
            addCriterion("prescription_number not in", values, "prescriptionNumber");
            return (Criteria) this;
        }

        public Criteria andPrescriptionNumberBetween(String value1, String value2) {
            addCriterion("prescription_number between", value1, value2, "prescriptionNumber");
            return (Criteria) this;
        }

        public Criteria andPrescriptionNumberNotBetween(String value1, String value2) {
            addCriterion("prescription_number not between", value1, value2, "prescriptionNumber");
            return (Criteria) this;
        }

        public Criteria andPrescriptionDateIsNull() {
            addCriterion("prescription_date is null");
            return (Criteria) this;
        }

        public Criteria andPrescriptionDateIsNotNull() {
            addCriterion("prescription_date is not null");
            return (Criteria) this;
        }

        public Criteria andPrescriptionDateEqualTo(Date value) {
            addCriterion("prescription_date =", value, "prescriptionDate");
            return (Criteria) this;
        }

        public Criteria andPrescriptionDateNotEqualTo(Date value) {
            addCriterion("prescription_date <>", value, "prescriptionDate");
            return (Criteria) this;
        }

        public Criteria andPrescriptionDateGreaterThan(Date value) {
            addCriterion("prescription_date >", value, "prescriptionDate");
            return (Criteria) this;
        }

        public Criteria andPrescriptionDateGreaterThanOrEqualTo(Date value) {
            addCriterion("prescription_date >=", value, "prescriptionDate");
            return (Criteria) this;
        }

        public Criteria andPrescriptionDateLessThan(Date value) {
            addCriterion("prescription_date <", value, "prescriptionDate");
            return (Criteria) this;
        }

        public Criteria andPrescriptionDateLessThanOrEqualTo(Date value) {
            addCriterion("prescription_date <=", value, "prescriptionDate");
            return (Criteria) this;
        }

        public Criteria andPrescriptionDateIn(List<Date> values) {
            addCriterion("prescription_date in", values, "prescriptionDate");
            return (Criteria) this;
        }

        public Criteria andPrescriptionDateNotIn(List<Date> values) {
            addCriterion("prescription_date not in", values, "prescriptionDate");
            return (Criteria) this;
        }

        public Criteria andPrescriptionDateBetween(Date value1, Date value2) {
            addCriterion("prescription_date between", value1, value2, "prescriptionDate");
            return (Criteria) this;
        }

        public Criteria andPrescriptionDateNotBetween(Date value1, Date value2) {
            addCriterion("prescription_date not between", value1, value2, "prescriptionDate");
            return (Criteria) this;
        }

        public Criteria andDoctorNameIsNull() {
            addCriterion("doctor_name is null");
            return (Criteria) this;
        }

        public Criteria andDoctorNameIsNotNull() {
            addCriterion("doctor_name is not null");
            return (Criteria) this;
        }

        public Criteria andDoctorNameEqualTo(String value) {
            addCriterion("doctor_name =", value, "doctorName");
            return (Criteria) this;
        }

        public Criteria andDoctorNameNotEqualTo(String value) {
            addCriterion("doctor_name <>", value, "doctorName");
            return (Criteria) this;
        }

        public Criteria andDoctorNameGreaterThan(String value) {
            addCriterion("doctor_name >", value, "doctorName");
            return (Criteria) this;
        }

        public Criteria andDoctorNameGreaterThanOrEqualTo(String value) {
            addCriterion("doctor_name >=", value, "doctorName");
            return (Criteria) this;
        }

        public Criteria andDoctorNameLessThan(String value) {
            addCriterion("doctor_name <", value, "doctorName");
            return (Criteria) this;
        }

        public Criteria andDoctorNameLessThanOrEqualTo(String value) {
            addCriterion("doctor_name <=", value, "doctorName");
            return (Criteria) this;
        }

        public Criteria andDoctorNameLike(String value) {
            addCriterion("doctor_name like", value, "doctorName");
            return (Criteria) this;
        }

        public Criteria andDoctorNameNotLike(String value) {
            addCriterion("doctor_name not like", value, "doctorName");
            return (Criteria) this;
        }

        public Criteria andDoctorNameIn(List<String> values) {
            addCriterion("doctor_name in", values, "doctorName");
            return (Criteria) this;
        }

        public Criteria andDoctorNameNotIn(List<String> values) {
            addCriterion("doctor_name not in", values, "doctorName");
            return (Criteria) this;
        }

        public Criteria andDoctorNameBetween(String value1, String value2) {
            addCriterion("doctor_name between", value1, value2, "doctorName");
            return (Criteria) this;
        }

        public Criteria andDoctorNameNotBetween(String value1, String value2) {
            addCriterion("doctor_name not between", value1, value2, "doctorName");
            return (Criteria) this;
        }

        public Criteria andDepartmentIsNull() {
            addCriterion("department is null");
            return (Criteria) this;
        }

        public Criteria andDepartmentIsNotNull() {
            addCriterion("department is not null");
            return (Criteria) this;
        }

        public Criteria andDepartmentEqualTo(String value) {
            addCriterion("department =", value, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentNotEqualTo(String value) {
            addCriterion("department <>", value, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentGreaterThan(String value) {
            addCriterion("department >", value, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentGreaterThanOrEqualTo(String value) {
            addCriterion("department >=", value, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentLessThan(String value) {
            addCriterion("department <", value, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentLessThanOrEqualTo(String value) {
            addCriterion("department <=", value, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentLike(String value) {
            addCriterion("department like", value, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentNotLike(String value) {
            addCriterion("department not like", value, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentIn(List<String> values) {
            addCriterion("department in", values, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentNotIn(List<String> values) {
            addCriterion("department not in", values, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentBetween(String value1, String value2) {
            addCriterion("department between", value1, value2, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentNotBetween(String value1, String value2) {
            addCriterion("department not between", value1, value2, "department");
            return (Criteria) this;
        }

        public Criteria andDiseaseDescriptionIsNull() {
            addCriterion("disease_description is null");
            return (Criteria) this;
        }

        public Criteria andDiseaseDescriptionIsNotNull() {
            addCriterion("disease_description is not null");
            return (Criteria) this;
        }

        public Criteria andDiseaseDescriptionEqualTo(String value) {
            addCriterion("disease_description =", value, "diseaseDescription");
            return (Criteria) this;
        }

        public Criteria andDiseaseDescriptionNotEqualTo(String value) {
            addCriterion("disease_description <>", value, "diseaseDescription");
            return (Criteria) this;
        }

        public Criteria andDiseaseDescriptionGreaterThan(String value) {
            addCriterion("disease_description >", value, "diseaseDescription");
            return (Criteria) this;
        }

        public Criteria andDiseaseDescriptionGreaterThanOrEqualTo(String value) {
            addCriterion("disease_description >=", value, "diseaseDescription");
            return (Criteria) this;
        }

        public Criteria andDiseaseDescriptionLessThan(String value) {
            addCriterion("disease_description <", value, "diseaseDescription");
            return (Criteria) this;
        }

        public Criteria andDiseaseDescriptionLessThanOrEqualTo(String value) {
            addCriterion("disease_description <=", value, "diseaseDescription");
            return (Criteria) this;
        }

        public Criteria andDiseaseDescriptionLike(String value) {
            addCriterion("disease_description like", value, "diseaseDescription");
            return (Criteria) this;
        }

        public Criteria andDiseaseDescriptionNotLike(String value) {
            addCriterion("disease_description not like", value, "diseaseDescription");
            return (Criteria) this;
        }

        public Criteria andDiseaseDescriptionIn(List<String> values) {
            addCriterion("disease_description in", values, "diseaseDescription");
            return (Criteria) this;
        }

        public Criteria andDiseaseDescriptionNotIn(List<String> values) {
            addCriterion("disease_description not in", values, "diseaseDescription");
            return (Criteria) this;
        }

        public Criteria andDiseaseDescriptionBetween(String value1, String value2) {
            addCriterion("disease_description between", value1, value2, "diseaseDescription");
            return (Criteria) this;
        }

        public Criteria andDiseaseDescriptionNotBetween(String value1, String value2) {
            addCriterion("disease_description not between", value1, value2, "diseaseDescription");
            return (Criteria) this;
        }

        public Criteria andApprovalNumberIsNull() {
            addCriterion("approval_number is null");
            return (Criteria) this;
        }

        public Criteria andApprovalNumberIsNotNull() {
            addCriterion("approval_number is not null");
            return (Criteria) this;
        }

        public Criteria andApprovalNumberEqualTo(String value) {
            addCriterion("approval_number =", value, "approvalNumber");
            return (Criteria) this;
        }

        public Criteria andApprovalNumberNotEqualTo(String value) {
            addCriterion("approval_number <>", value, "approvalNumber");
            return (Criteria) this;
        }

        public Criteria andApprovalNumberGreaterThan(String value) {
            addCriterion("approval_number >", value, "approvalNumber");
            return (Criteria) this;
        }

        public Criteria andApprovalNumberGreaterThanOrEqualTo(String value) {
            addCriterion("approval_number >=", value, "approvalNumber");
            return (Criteria) this;
        }

        public Criteria andApprovalNumberLessThan(String value) {
            addCriterion("approval_number <", value, "approvalNumber");
            return (Criteria) this;
        }

        public Criteria andApprovalNumberLessThanOrEqualTo(String value) {
            addCriterion("approval_number <=", value, "approvalNumber");
            return (Criteria) this;
        }

        public Criteria andApprovalNumberLike(String value) {
            addCriterion("approval_number like", value, "approvalNumber");
            return (Criteria) this;
        }

        public Criteria andApprovalNumberNotLike(String value) {
            addCriterion("approval_number not like", value, "approvalNumber");
            return (Criteria) this;
        }

        public Criteria andApprovalNumberIn(List<String> values) {
            addCriterion("approval_number in", values, "approvalNumber");
            return (Criteria) this;
        }

        public Criteria andApprovalNumberNotIn(List<String> values) {
            addCriterion("approval_number not in", values, "approvalNumber");
            return (Criteria) this;
        }

        public Criteria andApprovalNumberBetween(String value1, String value2) {
            addCriterion("approval_number between", value1, value2, "approvalNumber");
            return (Criteria) this;
        }

        public Criteria andApprovalNumberNotBetween(String value1, String value2) {
            addCriterion("approval_number not between", value1, value2, "approvalNumber");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}