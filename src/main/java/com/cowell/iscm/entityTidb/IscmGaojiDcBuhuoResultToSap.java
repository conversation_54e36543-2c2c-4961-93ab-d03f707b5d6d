package com.cowell.iscm.entityTidb;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR> BDP大仓采购建议
 */
public class IscmGaojiDcBuhuoResultToSap implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 日期
     */
    private String dt;

    /**
     * 大仓编码
     */
    private String werks;

    /**
     * 商品编码
     */
    private String matnr;

    /**
     * 预估日均
     */
    private String avgQty;

    /**
     * 大仓库存(停用编码)
     */
    private BigDecimal inactiveDcStock;

    /**
     * 门店库存(停用编码)
     */
    private BigDecimal inactiveStoreStock;

    /**
     * 门店总冻结库存_订货量_调整
     */
    private BigDecimal reviseTotalBlockStock2;

    /**
     * 近7日未清采购
     */
    private BigDecimal weiqingcaigou7;

    /**
     * 库存上限
     */
    private BigDecimal invUpper;

    /**
     * 建议订货量
     */
    private BigDecimal suggestDhl;

    /**
     * 有库存门店数
     */
    private BigDecimal storeCntsInStock;

    /**
     * 门店未满足请货总量
     */
    private BigDecimal totalApplyqty;

    /**
     * 门店未满足最大请货量
     */
    private BigDecimal maxApplyqty;

    /**
     * 中包装量
     */
    private BigDecimal midqty;

    /**
     * 件装量
     */
    private BigDecimal maxqty;

    /**
     * 存销比
     */
    private String stockToUseRatio;

    /**
     * 供应商
     */
    private String lifnr;

    /**
     * 订货点
     */
    private BigDecimal dhdB;

    /**
     * 安全库存天数
     */
    private BigDecimal dcS;

    /**
     * 订货间隔天数
     */
    private BigDecimal dcL;

    /**
     * 商品等级
     */
    private String goodsLevel;

    /**
     * 安全库存数
     */
    private BigDecimal safetyStock;

    /**
     * 门店预期库存量
     */
    private BigDecimal sumRV;

    /**
     * 门店占用库存_订货点
     */
    private BigDecimal reviseTotalBlockStock1;

    /**
     * 原初补货量
     */
    private BigDecimal rawSuggestDhl;

    /**
     * 不计算存销比标识
     */
    private String znoncxb;

    /**
     * 大仓可用库存
     */
    private String totalDcStock;

    /**
     * 大仓不可用库存
     */
    private String totalDcDisableStock;

    /**
     * 门店库存
     */
    private String totalStoreStock;

    /**
     * 近30日总销量
     */
    private String qtyBefore30;

    /**
     * 近30天销售环比
     */
    private String hbSaleRate;

    /**
     * 近30天销售同比
     */
    private String tbSaleRate;

    /**
     * 动销门店数
     */
    private BigDecimal zdxmds;

    /**
     * DC可卖天数
     */
    private BigDecimal dcSaleDays;

    /**
     * DC库存周转天数
     */
    private BigDecimal dcInvSaleDays;

    /**
     * 门店可卖天数
     */
    private BigDecimal storeSaleDays;

    /**
     * 门店库存周转天数
     */
    private BigDecimal storeInvSaleDays;

    /**
     * 铺货门店数
     */
    private BigDecimal puhuoStores;

    /**
     * 前7天累计销量
     */
    private BigDecimal qtyBefore7;

    /**
     * 前7天日均销量
     */
    private String avgQtyBefore7;

    /**
     * 前14天累计销量
     */
    private BigDecimal qtyBefore14;

    /**
     * 前14天日均销量
     */
    private String avgQtyBefore14;

    /**
     * 前30天日均销量
     */
    private String avgQtyBefore30;

    /**
     * 前90天累计销量
     */
    private BigDecimal qtyBefore90;

    /**
     * 前90天日均销量
     */
    private String avgQtyBefore90;

    /**
     * 前7天累计配送量
     */
    private BigDecimal distqtyBefore7;

    /**
     * 前7天日均配送量
     */
    private String avgDistqtyBefore7;

    /**
     * 前14天累计配送量
     */
    private BigDecimal distqtyBefore14;

    /**
     * 前14天日均配送量
     */
    private String avgDistqtyBefore14;

    /**
     * 前30天累计配送量
     */
    private BigDecimal distqty30;

    /**
     * 前30天日均配送量
     */
    private String avgDistqtyBefore30;

    /**
     * 前90天累计配送量
     */
    private BigDecimal distqtyBefore90;

    /**
     * 前90天日均配送量
     */
    private String avgDistqtyBefore90;

    /**
     * 前30-60天累计销量
     */
    private BigDecimal qty3060;

    /**
     * 前60-90天累计销量
     */
    private BigDecimal qty6090;

    /**
     * 前30-60天累计配送量
     */
    private BigDecimal distqty3060;

    /**
     * 前60-90天累计配送量
     */
    private BigDecimal distqty6090;

    /**
     * 季节调节因子
     */
    private String seasonalFactor;

    /**
     * 加盟店库存
     */
    private BigDecimal jmStoreStock;

    /**
     * 连锁仓可用库存
     */
    private BigDecimal dcStock;

    /**
     * 连锁仓不可用库存
     */
    private BigDecimal dcDisableStock;

    /**
     * 连锁仓库存_停用编码
     */
    private BigDecimal zcInactiveDcStock;

    /**
     * 连锁仓预期库存量
     */
    private BigDecimal dcInvUpper;

    /**
     * 连锁仓占用库存_订货点
     */
    private BigDecimal dcBlockStock1;

    /**
     * 连锁仓占用库存_订货量
     */
    private BigDecimal dcBlockStock2;

    /**
     * 补货后可配送天数
     */
    private BigDecimal distDays;

    /**
     * 发送时间
     */
    private String sentTime;

    /**
     * 前30天累计销量金额
     */
    private BigDecimal amountBefore30;

    /**
     * 未满足请货sku数
     */
    private BigDecimal unsatisfiedDeliverySku;

    /**
     * 未满足请货次数
     */
    private BigDecimal unsatisfiedDeliveryCnt;

    /**
     * 未清退仓
     */
    private String weiqingtuicang;

    /**
     * 未清计算量
     */
    private String weiqingjisuan;

    /**
     * 最低采购类型 1-平台，2-项目公司
     */
    private String minPurchaseType;

    /**
     * 最低采购价格
     */
    private BigDecimal minPurchasePrice;

    /**
     * 最低采购凭证日期
     */
    private String minPurchaseDate;

    /**
     * 大仓当日库存金额
     */
    private BigDecimal zdcje1;

    /**
     * 大仓7日均库存金额
     */
    private BigDecimal zdcje2;

    /**
     * 大仓30日均库存金额
     */
    private BigDecimal zdcje3;

    /**
     * 大仓30日均销售成本
     */
    private BigDecimal zdccb1;

    /**
     * 大仓90日均销售成
     */
    private BigDecimal zdccb2;

    /**
     * 门店日库存金额
     */
    private BigDecimal zmdje1;

    /**
     * 门店7日均库存金额
     */
    private BigDecimal zmdje2;

    /**
     * 门店30日均库存金额
     */
    private BigDecimal zmdje3;

    /**
     * 门店90日均销售成本
     */
    private BigDecimal zmdcb;

    /**
     * 仓+店周转天数仓+店周转天数
     */
    private BigDecimal zcdzzts;

    /**
     * 仓+店库存金额排名（企业级排名）
     */
    private String zcdkcjepm;

    /**
     * 归属部门（域值管理：1全国商采，2中参事业部，3互医事业部，4品牌事业部，5慢病组）
     */
    private String zdpt;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getDt() {
        return dt;
    }

    public void setDt(String dt) {
        this.dt = dt;
    }

    public String getWerks() {
        return werks;
    }

    public void setWerks(String werks) {
        this.werks = werks;
    }

    public String getMatnr() {
        return matnr;
    }

    public void setMatnr(String matnr) {
        this.matnr = matnr;
    }

    public String getAvgQty() {
        return avgQty;
    }

    public void setAvgQty(String avgQty) {
        this.avgQty = avgQty;
    }

    public BigDecimal getInactiveDcStock() {
        return inactiveDcStock;
    }

    public void setInactiveDcStock(BigDecimal inactiveDcStock) {
        this.inactiveDcStock = inactiveDcStock;
    }

    public BigDecimal getInactiveStoreStock() {
        return inactiveStoreStock;
    }

    public void setInactiveStoreStock(BigDecimal inactiveStoreStock) {
        this.inactiveStoreStock = inactiveStoreStock;
    }

    public BigDecimal getReviseTotalBlockStock2() {
        return reviseTotalBlockStock2;
    }

    public void setReviseTotalBlockStock2(BigDecimal reviseTotalBlockStock2) {
        this.reviseTotalBlockStock2 = reviseTotalBlockStock2;
    }

    public BigDecimal getWeiqingcaigou7() {
        return weiqingcaigou7;
    }

    public void setWeiqingcaigou7(BigDecimal weiqingcaigou7) {
        this.weiqingcaigou7 = weiqingcaigou7;
    }

    public BigDecimal getInvUpper() {
        return invUpper;
    }

    public void setInvUpper(BigDecimal invUpper) {
        this.invUpper = invUpper;
    }

    public BigDecimal getSuggestDhl() {
        return suggestDhl;
    }

    public void setSuggestDhl(BigDecimal suggestDhl) {
        this.suggestDhl = suggestDhl;
    }

    public BigDecimal getStoreCntsInStock() {
        return storeCntsInStock;
    }

    public void setStoreCntsInStock(BigDecimal storeCntsInStock) {
        this.storeCntsInStock = storeCntsInStock;
    }

    public BigDecimal getTotalApplyqty() {
        return totalApplyqty;
    }

    public void setTotalApplyqty(BigDecimal totalApplyqty) {
        this.totalApplyqty = totalApplyqty;
    }

    public BigDecimal getMaxApplyqty() {
        return maxApplyqty;
    }

    public void setMaxApplyqty(BigDecimal maxApplyqty) {
        this.maxApplyqty = maxApplyqty;
    }

    public BigDecimal getMidqty() {
        return midqty;
    }

    public void setMidqty(BigDecimal midqty) {
        this.midqty = midqty;
    }

    public BigDecimal getMaxqty() {
        return maxqty;
    }

    public void setMaxqty(BigDecimal maxqty) {
        this.maxqty = maxqty;
    }

    public String getStockToUseRatio() {
        return stockToUseRatio;
    }

    public void setStockToUseRatio(String stockToUseRatio) {
        this.stockToUseRatio = stockToUseRatio;
    }

    public String getLifnr() {
        return lifnr;
    }

    public void setLifnr(String lifnr) {
        this.lifnr = lifnr;
    }

    public BigDecimal getDhdB() {
        return dhdB;
    }

    public void setDhdB(BigDecimal dhdB) {
        this.dhdB = dhdB;
    }

    public BigDecimal getDcS() {
        return dcS;
    }

    public void setDcS(BigDecimal dcS) {
        this.dcS = dcS;
    }

    public BigDecimal getDcL() {
        return dcL;
    }

    public void setDcL(BigDecimal dcL) {
        this.dcL = dcL;
    }

    public String getGoodsLevel() {
        return goodsLevel;
    }

    public void setGoodsLevel(String goodsLevel) {
        this.goodsLevel = goodsLevel;
    }

    public BigDecimal getSafetyStock() {
        return safetyStock;
    }

    public void setSafetyStock(BigDecimal safetyStock) {
        this.safetyStock = safetyStock;
    }

    public BigDecimal getSumRV() {
        return sumRV;
    }

    public void setSumRV(BigDecimal sumRV) {
        this.sumRV = sumRV;
    }

    public BigDecimal getReviseTotalBlockStock1() {
        return reviseTotalBlockStock1;
    }

    public void setReviseTotalBlockStock1(BigDecimal reviseTotalBlockStock1) {
        this.reviseTotalBlockStock1 = reviseTotalBlockStock1;
    }

    public BigDecimal getRawSuggestDhl() {
        return rawSuggestDhl;
    }

    public void setRawSuggestDhl(BigDecimal rawSuggestDhl) {
        this.rawSuggestDhl = rawSuggestDhl;
    }

    public String getZnoncxb() {
        return znoncxb;
    }

    public void setZnoncxb(String znoncxb) {
        this.znoncxb = znoncxb;
    }

    public String getTotalDcStock() {
        return totalDcStock;
    }

    public void setTotalDcStock(String totalDcStock) {
        this.totalDcStock = totalDcStock;
    }

    public String getTotalDcDisableStock() {
        return totalDcDisableStock;
    }

    public void setTotalDcDisableStock(String totalDcDisableStock) {
        this.totalDcDisableStock = totalDcDisableStock;
    }

    public String getTotalStoreStock() {
        return totalStoreStock;
    }

    public void setTotalStoreStock(String totalStoreStock) {
        this.totalStoreStock = totalStoreStock;
    }

    public String getQtyBefore30() {
        return qtyBefore30;
    }

    public void setQtyBefore30(String qtyBefore30) {
        this.qtyBefore30 = qtyBefore30;
    }

    public String getHbSaleRate() {
        return hbSaleRate;
    }

    public void setHbSaleRate(String hbSaleRate) {
        this.hbSaleRate = hbSaleRate;
    }

    public String getTbSaleRate() {
        return tbSaleRate;
    }

    public void setTbSaleRate(String tbSaleRate) {
        this.tbSaleRate = tbSaleRate;
    }

    public BigDecimal getZdxmds() {
        return zdxmds;
    }

    public void setZdxmds(BigDecimal zdxmds) {
        this.zdxmds = zdxmds;
    }

    public BigDecimal getDcSaleDays() {
        return dcSaleDays;
    }

    public void setDcSaleDays(BigDecimal dcSaleDays) {
        this.dcSaleDays = dcSaleDays;
    }

    public BigDecimal getDcInvSaleDays() {
        return dcInvSaleDays;
    }

    public void setDcInvSaleDays(BigDecimal dcInvSaleDays) {
        this.dcInvSaleDays = dcInvSaleDays;
    }

    public BigDecimal getStoreSaleDays() {
        return storeSaleDays;
    }

    public void setStoreSaleDays(BigDecimal storeSaleDays) {
        this.storeSaleDays = storeSaleDays;
    }

    public BigDecimal getStoreInvSaleDays() {
        return storeInvSaleDays;
    }

    public void setStoreInvSaleDays(BigDecimal storeInvSaleDays) {
        this.storeInvSaleDays = storeInvSaleDays;
    }

    public BigDecimal getPuhuoStores() {
        return puhuoStores;
    }

    public void setPuhuoStores(BigDecimal puhuoStores) {
        this.puhuoStores = puhuoStores;
    }

    public BigDecimal getQtyBefore7() {
        return qtyBefore7;
    }

    public void setQtyBefore7(BigDecimal qtyBefore7) {
        this.qtyBefore7 = qtyBefore7;
    }

    public String getAvgQtyBefore7() {
        return avgQtyBefore7;
    }

    public void setAvgQtyBefore7(String avgQtyBefore7) {
        this.avgQtyBefore7 = avgQtyBefore7;
    }

    public BigDecimal getQtyBefore14() {
        return qtyBefore14;
    }

    public void setQtyBefore14(BigDecimal qtyBefore14) {
        this.qtyBefore14 = qtyBefore14;
    }

    public String getAvgQtyBefore14() {
        return avgQtyBefore14;
    }

    public void setAvgQtyBefore14(String avgQtyBefore14) {
        this.avgQtyBefore14 = avgQtyBefore14;
    }

    public String getAvgQtyBefore30() {
        return avgQtyBefore30;
    }

    public void setAvgQtyBefore30(String avgQtyBefore30) {
        this.avgQtyBefore30 = avgQtyBefore30;
    }

    public BigDecimal getQtyBefore90() {
        return qtyBefore90;
    }

    public void setQtyBefore90(BigDecimal qtyBefore90) {
        this.qtyBefore90 = qtyBefore90;
    }

    public String getAvgQtyBefore90() {
        return avgQtyBefore90;
    }

    public void setAvgQtyBefore90(String avgQtyBefore90) {
        this.avgQtyBefore90 = avgQtyBefore90;
    }

    public BigDecimal getDistqtyBefore7() {
        return distqtyBefore7;
    }

    public void setDistqtyBefore7(BigDecimal distqtyBefore7) {
        this.distqtyBefore7 = distqtyBefore7;
    }

    public String getAvgDistqtyBefore7() {
        return avgDistqtyBefore7;
    }

    public void setAvgDistqtyBefore7(String avgDistqtyBefore7) {
        this.avgDistqtyBefore7 = avgDistqtyBefore7;
    }

    public BigDecimal getDistqtyBefore14() {
        return distqtyBefore14;
    }

    public void setDistqtyBefore14(BigDecimal distqtyBefore14) {
        this.distqtyBefore14 = distqtyBefore14;
    }

    public String getAvgDistqtyBefore14() {
        return avgDistqtyBefore14;
    }

    public void setAvgDistqtyBefore14(String avgDistqtyBefore14) {
        this.avgDistqtyBefore14 = avgDistqtyBefore14;
    }

    public BigDecimal getDistqty30() {
        return distqty30;
    }

    public void setDistqty30(BigDecimal distqty30) {
        this.distqty30 = distqty30;
    }

    public String getAvgDistqtyBefore30() {
        return avgDistqtyBefore30;
    }

    public void setAvgDistqtyBefore30(String avgDistqtyBefore30) {
        this.avgDistqtyBefore30 = avgDistqtyBefore30;
    }

    public BigDecimal getDistqtyBefore90() {
        return distqtyBefore90;
    }

    public void setDistqtyBefore90(BigDecimal distqtyBefore90) {
        this.distqtyBefore90 = distqtyBefore90;
    }

    public String getAvgDistqtyBefore90() {
        return avgDistqtyBefore90;
    }

    public void setAvgDistqtyBefore90(String avgDistqtyBefore90) {
        this.avgDistqtyBefore90 = avgDistqtyBefore90;
    }

    public BigDecimal getQty3060() {
        return qty3060;
    }

    public void setQty3060(BigDecimal qty3060) {
        this.qty3060 = qty3060;
    }

    public BigDecimal getQty6090() {
        return qty6090;
    }

    public void setQty6090(BigDecimal qty6090) {
        this.qty6090 = qty6090;
    }

    public BigDecimal getDistqty3060() {
        return distqty3060;
    }

    public void setDistqty3060(BigDecimal distqty3060) {
        this.distqty3060 = distqty3060;
    }

    public BigDecimal getDistqty6090() {
        return distqty6090;
    }

    public void setDistqty6090(BigDecimal distqty6090) {
        this.distqty6090 = distqty6090;
    }

    public String getSeasonalFactor() {
        return seasonalFactor;
    }

    public void setSeasonalFactor(String seasonalFactor) {
        this.seasonalFactor = seasonalFactor;
    }

    public BigDecimal getJmStoreStock() {
        return jmStoreStock;
    }

    public void setJmStoreStock(BigDecimal jmStoreStock) {
        this.jmStoreStock = jmStoreStock;
    }

    public BigDecimal getDcStock() {
        return dcStock;
    }

    public void setDcStock(BigDecimal dcStock) {
        this.dcStock = dcStock;
    }

    public BigDecimal getDcDisableStock() {
        return dcDisableStock;
    }

    public void setDcDisableStock(BigDecimal dcDisableStock) {
        this.dcDisableStock = dcDisableStock;
    }

    public BigDecimal getZcInactiveDcStock() {
        return zcInactiveDcStock;
    }

    public void setZcInactiveDcStock(BigDecimal zcInactiveDcStock) {
        this.zcInactiveDcStock = zcInactiveDcStock;
    }

    public BigDecimal getDcInvUpper() {
        return dcInvUpper;
    }

    public void setDcInvUpper(BigDecimal dcInvUpper) {
        this.dcInvUpper = dcInvUpper;
    }

    public BigDecimal getDcBlockStock1() {
        return dcBlockStock1;
    }

    public void setDcBlockStock1(BigDecimal dcBlockStock1) {
        this.dcBlockStock1 = dcBlockStock1;
    }

    public BigDecimal getDcBlockStock2() {
        return dcBlockStock2;
    }

    public void setDcBlockStock2(BigDecimal dcBlockStock2) {
        this.dcBlockStock2 = dcBlockStock2;
    }

    public BigDecimal getDistDays() {
        return distDays;
    }

    public void setDistDays(BigDecimal distDays) {
        this.distDays = distDays;
    }

    public String getSentTime() {
        return sentTime;
    }

    public void setSentTime(String sentTime) {
        this.sentTime = sentTime;
    }

    public BigDecimal getAmountBefore30() {
        return amountBefore30;
    }

    public void setAmountBefore30(BigDecimal amountBefore30) {
        this.amountBefore30 = amountBefore30;
    }

    public BigDecimal getUnsatisfiedDeliverySku() {
        return unsatisfiedDeliverySku;
    }

    public void setUnsatisfiedDeliverySku(BigDecimal unsatisfiedDeliverySku) {
        this.unsatisfiedDeliverySku = unsatisfiedDeliverySku;
    }

    public BigDecimal getUnsatisfiedDeliveryCnt() {
        return unsatisfiedDeliveryCnt;
    }

    public void setUnsatisfiedDeliveryCnt(BigDecimal unsatisfiedDeliveryCnt) {
        this.unsatisfiedDeliveryCnt = unsatisfiedDeliveryCnt;
    }

    public String getWeiqingtuicang() {
        return weiqingtuicang;
    }

    public void setWeiqingtuicang(String weiqingtuicang) {
        this.weiqingtuicang = weiqingtuicang;
    }

    public String getWeiqingjisuan() {
        return weiqingjisuan;
    }

    public void setWeiqingjisuan(String weiqingjisuan) {
        this.weiqingjisuan = weiqingjisuan;
    }

    public String getMinPurchaseType() {
        return minPurchaseType;
    }

    public void setMinPurchaseType(String minPurchaseType) {
        this.minPurchaseType = minPurchaseType;
    }

    public BigDecimal getMinPurchasePrice() {
        return minPurchasePrice;
    }

    public void setMinPurchasePrice(BigDecimal minPurchasePrice) {
        this.minPurchasePrice = minPurchasePrice;
    }

    public String getMinPurchaseDate() {
        return minPurchaseDate;
    }

    public void setMinPurchaseDate(String minPurchaseDate) {
        this.minPurchaseDate = minPurchaseDate;
    }

    public BigDecimal getZdcje1() {
        return zdcje1;
    }

    public void setZdcje1(BigDecimal zdcje1) {
        this.zdcje1 = zdcje1;
    }

    public BigDecimal getZdcje2() {
        return zdcje2;
    }

    public void setZdcje2(BigDecimal zdcje2) {
        this.zdcje2 = zdcje2;
    }

    public BigDecimal getZdcje3() {
        return zdcje3;
    }

    public void setZdcje3(BigDecimal zdcje3) {
        this.zdcje3 = zdcje3;
    }

    public BigDecimal getZdccb1() {
        return zdccb1;
    }

    public void setZdccb1(BigDecimal zdccb1) {
        this.zdccb1 = zdccb1;
    }

    public BigDecimal getZdccb2() {
        return zdccb2;
    }

    public void setZdccb2(BigDecimal zdccb2) {
        this.zdccb2 = zdccb2;
    }

    public BigDecimal getZmdje1() {
        return zmdje1;
    }

    public void setZmdje1(BigDecimal zmdje1) {
        this.zmdje1 = zmdje1;
    }

    public BigDecimal getZmdje2() {
        return zmdje2;
    }

    public void setZmdje2(BigDecimal zmdje2) {
        this.zmdje2 = zmdje2;
    }

    public BigDecimal getZmdje3() {
        return zmdje3;
    }

    public void setZmdje3(BigDecimal zmdje3) {
        this.zmdje3 = zmdje3;
    }

    public BigDecimal getZmdcb() {
        return zmdcb;
    }

    public void setZmdcb(BigDecimal zmdcb) {
        this.zmdcb = zmdcb;
    }

    public BigDecimal getZcdzzts() {
        return zcdzzts;
    }

    public void setZcdzzts(BigDecimal zcdzzts) {
        this.zcdzzts = zcdzzts;
    }

    public String getZcdkcjepm() {
        return zcdkcjepm;
    }

    public void setZcdkcjepm(String zcdkcjepm) {
        this.zcdkcjepm = zcdkcjepm;
    }

    public String getZdpt() {
        return zdpt;
    }

    public void setZdpt(String zdpt) {
        this.zdpt = zdpt;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        IscmGaojiDcBuhuoResultToSap other = (IscmGaojiDcBuhuoResultToSap) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getDt() == null ? other.getDt() == null : this.getDt().equals(other.getDt()))
            && (this.getWerks() == null ? other.getWerks() == null : this.getWerks().equals(other.getWerks()))
            && (this.getMatnr() == null ? other.getMatnr() == null : this.getMatnr().equals(other.getMatnr()))
            && (this.getAvgQty() == null ? other.getAvgQty() == null : this.getAvgQty().equals(other.getAvgQty()))
            && (this.getInactiveDcStock() == null ? other.getInactiveDcStock() == null : this.getInactiveDcStock().equals(other.getInactiveDcStock()))
            && (this.getInactiveStoreStock() == null ? other.getInactiveStoreStock() == null : this.getInactiveStoreStock().equals(other.getInactiveStoreStock()))
            && (this.getReviseTotalBlockStock2() == null ? other.getReviseTotalBlockStock2() == null : this.getReviseTotalBlockStock2().equals(other.getReviseTotalBlockStock2()))
            && (this.getWeiqingcaigou7() == null ? other.getWeiqingcaigou7() == null : this.getWeiqingcaigou7().equals(other.getWeiqingcaigou7()))
            && (this.getInvUpper() == null ? other.getInvUpper() == null : this.getInvUpper().equals(other.getInvUpper()))
            && (this.getSuggestDhl() == null ? other.getSuggestDhl() == null : this.getSuggestDhl().equals(other.getSuggestDhl()))
            && (this.getStoreCntsInStock() == null ? other.getStoreCntsInStock() == null : this.getStoreCntsInStock().equals(other.getStoreCntsInStock()))
            && (this.getTotalApplyqty() == null ? other.getTotalApplyqty() == null : this.getTotalApplyqty().equals(other.getTotalApplyqty()))
            && (this.getMaxApplyqty() == null ? other.getMaxApplyqty() == null : this.getMaxApplyqty().equals(other.getMaxApplyqty()))
            && (this.getMidqty() == null ? other.getMidqty() == null : this.getMidqty().equals(other.getMidqty()))
            && (this.getMaxqty() == null ? other.getMaxqty() == null : this.getMaxqty().equals(other.getMaxqty()))
            && (this.getStockToUseRatio() == null ? other.getStockToUseRatio() == null : this.getStockToUseRatio().equals(other.getStockToUseRatio()))
            && (this.getLifnr() == null ? other.getLifnr() == null : this.getLifnr().equals(other.getLifnr()))
            && (this.getDhdB() == null ? other.getDhdB() == null : this.getDhdB().equals(other.getDhdB()))
            && (this.getDcS() == null ? other.getDcS() == null : this.getDcS().equals(other.getDcS()))
            && (this.getDcL() == null ? other.getDcL() == null : this.getDcL().equals(other.getDcL()))
            && (this.getGoodsLevel() == null ? other.getGoodsLevel() == null : this.getGoodsLevel().equals(other.getGoodsLevel()))
            && (this.getSafetyStock() == null ? other.getSafetyStock() == null : this.getSafetyStock().equals(other.getSafetyStock()))
            && (this.getSumRV() == null ? other.getSumRV() == null : this.getSumRV().equals(other.getSumRV()))
            && (this.getReviseTotalBlockStock1() == null ? other.getReviseTotalBlockStock1() == null : this.getReviseTotalBlockStock1().equals(other.getReviseTotalBlockStock1()))
            && (this.getRawSuggestDhl() == null ? other.getRawSuggestDhl() == null : this.getRawSuggestDhl().equals(other.getRawSuggestDhl()))
            && (this.getZnoncxb() == null ? other.getZnoncxb() == null : this.getZnoncxb().equals(other.getZnoncxb()))
            && (this.getTotalDcStock() == null ? other.getTotalDcStock() == null : this.getTotalDcStock().equals(other.getTotalDcStock()))
            && (this.getTotalDcDisableStock() == null ? other.getTotalDcDisableStock() == null : this.getTotalDcDisableStock().equals(other.getTotalDcDisableStock()))
            && (this.getTotalStoreStock() == null ? other.getTotalStoreStock() == null : this.getTotalStoreStock().equals(other.getTotalStoreStock()))
            && (this.getQtyBefore30() == null ? other.getQtyBefore30() == null : this.getQtyBefore30().equals(other.getQtyBefore30()))
            && (this.getHbSaleRate() == null ? other.getHbSaleRate() == null : this.getHbSaleRate().equals(other.getHbSaleRate()))
            && (this.getTbSaleRate() == null ? other.getTbSaleRate() == null : this.getTbSaleRate().equals(other.getTbSaleRate()))
            && (this.getZdxmds() == null ? other.getZdxmds() == null : this.getZdxmds().equals(other.getZdxmds()))
            && (this.getDcSaleDays() == null ? other.getDcSaleDays() == null : this.getDcSaleDays().equals(other.getDcSaleDays()))
            && (this.getDcInvSaleDays() == null ? other.getDcInvSaleDays() == null : this.getDcInvSaleDays().equals(other.getDcInvSaleDays()))
            && (this.getStoreSaleDays() == null ? other.getStoreSaleDays() == null : this.getStoreSaleDays().equals(other.getStoreSaleDays()))
            && (this.getStoreInvSaleDays() == null ? other.getStoreInvSaleDays() == null : this.getStoreInvSaleDays().equals(other.getStoreInvSaleDays()))
            && (this.getPuhuoStores() == null ? other.getPuhuoStores() == null : this.getPuhuoStores().equals(other.getPuhuoStores()))
            && (this.getQtyBefore7() == null ? other.getQtyBefore7() == null : this.getQtyBefore7().equals(other.getQtyBefore7()))
            && (this.getAvgQtyBefore7() == null ? other.getAvgQtyBefore7() == null : this.getAvgQtyBefore7().equals(other.getAvgQtyBefore7()))
            && (this.getQtyBefore14() == null ? other.getQtyBefore14() == null : this.getQtyBefore14().equals(other.getQtyBefore14()))
            && (this.getAvgQtyBefore14() == null ? other.getAvgQtyBefore14() == null : this.getAvgQtyBefore14().equals(other.getAvgQtyBefore14()))
            && (this.getAvgQtyBefore30() == null ? other.getAvgQtyBefore30() == null : this.getAvgQtyBefore30().equals(other.getAvgQtyBefore30()))
            && (this.getQtyBefore90() == null ? other.getQtyBefore90() == null : this.getQtyBefore90().equals(other.getQtyBefore90()))
            && (this.getAvgQtyBefore90() == null ? other.getAvgQtyBefore90() == null : this.getAvgQtyBefore90().equals(other.getAvgQtyBefore90()))
            && (this.getDistqtyBefore7() == null ? other.getDistqtyBefore7() == null : this.getDistqtyBefore7().equals(other.getDistqtyBefore7()))
            && (this.getAvgDistqtyBefore7() == null ? other.getAvgDistqtyBefore7() == null : this.getAvgDistqtyBefore7().equals(other.getAvgDistqtyBefore7()))
            && (this.getDistqtyBefore14() == null ? other.getDistqtyBefore14() == null : this.getDistqtyBefore14().equals(other.getDistqtyBefore14()))
            && (this.getAvgDistqtyBefore14() == null ? other.getAvgDistqtyBefore14() == null : this.getAvgDistqtyBefore14().equals(other.getAvgDistqtyBefore14()))
            && (this.getDistqty30() == null ? other.getDistqty30() == null : this.getDistqty30().equals(other.getDistqty30()))
            && (this.getAvgDistqtyBefore30() == null ? other.getAvgDistqtyBefore30() == null : this.getAvgDistqtyBefore30().equals(other.getAvgDistqtyBefore30()))
            && (this.getDistqtyBefore90() == null ? other.getDistqtyBefore90() == null : this.getDistqtyBefore90().equals(other.getDistqtyBefore90()))
            && (this.getAvgDistqtyBefore90() == null ? other.getAvgDistqtyBefore90() == null : this.getAvgDistqtyBefore90().equals(other.getAvgDistqtyBefore90()))
            && (this.getQty3060() == null ? other.getQty3060() == null : this.getQty3060().equals(other.getQty3060()))
            && (this.getQty6090() == null ? other.getQty6090() == null : this.getQty6090().equals(other.getQty6090()))
            && (this.getDistqty3060() == null ? other.getDistqty3060() == null : this.getDistqty3060().equals(other.getDistqty3060()))
            && (this.getDistqty6090() == null ? other.getDistqty6090() == null : this.getDistqty6090().equals(other.getDistqty6090()))
            && (this.getSeasonalFactor() == null ? other.getSeasonalFactor() == null : this.getSeasonalFactor().equals(other.getSeasonalFactor()))
            && (this.getJmStoreStock() == null ? other.getJmStoreStock() == null : this.getJmStoreStock().equals(other.getJmStoreStock()))
            && (this.getDcStock() == null ? other.getDcStock() == null : this.getDcStock().equals(other.getDcStock()))
            && (this.getDcDisableStock() == null ? other.getDcDisableStock() == null : this.getDcDisableStock().equals(other.getDcDisableStock()))
            && (this.getZcInactiveDcStock() == null ? other.getZcInactiveDcStock() == null : this.getZcInactiveDcStock().equals(other.getZcInactiveDcStock()))
            && (this.getDcInvUpper() == null ? other.getDcInvUpper() == null : this.getDcInvUpper().equals(other.getDcInvUpper()))
            && (this.getDcBlockStock1() == null ? other.getDcBlockStock1() == null : this.getDcBlockStock1().equals(other.getDcBlockStock1()))
            && (this.getDcBlockStock2() == null ? other.getDcBlockStock2() == null : this.getDcBlockStock2().equals(other.getDcBlockStock2()))
            && (this.getDistDays() == null ? other.getDistDays() == null : this.getDistDays().equals(other.getDistDays()))
            && (this.getSentTime() == null ? other.getSentTime() == null : this.getSentTime().equals(other.getSentTime()))
            && (this.getAmountBefore30() == null ? other.getAmountBefore30() == null : this.getAmountBefore30().equals(other.getAmountBefore30()))
            && (this.getUnsatisfiedDeliverySku() == null ? other.getUnsatisfiedDeliverySku() == null : this.getUnsatisfiedDeliverySku().equals(other.getUnsatisfiedDeliverySku()))
            && (this.getUnsatisfiedDeliveryCnt() == null ? other.getUnsatisfiedDeliveryCnt() == null : this.getUnsatisfiedDeliveryCnt().equals(other.getUnsatisfiedDeliveryCnt()))
            && (this.getWeiqingtuicang() == null ? other.getWeiqingtuicang() == null : this.getWeiqingtuicang().equals(other.getWeiqingtuicang()))
            && (this.getWeiqingjisuan() == null ? other.getWeiqingjisuan() == null : this.getWeiqingjisuan().equals(other.getWeiqingjisuan()))
            && (this.getMinPurchaseType() == null ? other.getMinPurchaseType() == null : this.getMinPurchaseType().equals(other.getMinPurchaseType()))
            && (this.getMinPurchasePrice() == null ? other.getMinPurchasePrice() == null : this.getMinPurchasePrice().equals(other.getMinPurchasePrice()))
            && (this.getMinPurchaseDate() == null ? other.getMinPurchaseDate() == null : this.getMinPurchaseDate().equals(other.getMinPurchaseDate()))
            && (this.getZdcje1() == null ? other.getZdcje1() == null : this.getZdcje1().equals(other.getZdcje1()))
            && (this.getZdcje2() == null ? other.getZdcje2() == null : this.getZdcje2().equals(other.getZdcje2()))
            && (this.getZdcje3() == null ? other.getZdcje3() == null : this.getZdcje3().equals(other.getZdcje3()))
            && (this.getZdccb1() == null ? other.getZdccb1() == null : this.getZdccb1().equals(other.getZdccb1()))
            && (this.getZdccb2() == null ? other.getZdccb2() == null : this.getZdccb2().equals(other.getZdccb2()))
            && (this.getZmdje1() == null ? other.getZmdje1() == null : this.getZmdje1().equals(other.getZmdje1()))
            && (this.getZmdje2() == null ? other.getZmdje2() == null : this.getZmdje2().equals(other.getZmdje2()))
            && (this.getZmdje3() == null ? other.getZmdje3() == null : this.getZmdje3().equals(other.getZmdje3()))
            && (this.getZmdcb() == null ? other.getZmdcb() == null : this.getZmdcb().equals(other.getZmdcb()))
            && (this.getZcdzzts() == null ? other.getZcdzzts() == null : this.getZcdzzts().equals(other.getZcdzzts()))
            && (this.getZcdkcjepm() == null ? other.getZcdkcjepm() == null : this.getZcdkcjepm().equals(other.getZcdkcjepm()))
            && (this.getZdpt() == null ? other.getZdpt() == null : this.getZdpt().equals(other.getZdpt()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getDt() == null) ? 0 : getDt().hashCode());
        result = prime * result + ((getWerks() == null) ? 0 : getWerks().hashCode());
        result = prime * result + ((getMatnr() == null) ? 0 : getMatnr().hashCode());
        result = prime * result + ((getAvgQty() == null) ? 0 : getAvgQty().hashCode());
        result = prime * result + ((getInactiveDcStock() == null) ? 0 : getInactiveDcStock().hashCode());
        result = prime * result + ((getInactiveStoreStock() == null) ? 0 : getInactiveStoreStock().hashCode());
        result = prime * result + ((getReviseTotalBlockStock2() == null) ? 0 : getReviseTotalBlockStock2().hashCode());
        result = prime * result + ((getWeiqingcaigou7() == null) ? 0 : getWeiqingcaigou7().hashCode());
        result = prime * result + ((getInvUpper() == null) ? 0 : getInvUpper().hashCode());
        result = prime * result + ((getSuggestDhl() == null) ? 0 : getSuggestDhl().hashCode());
        result = prime * result + ((getStoreCntsInStock() == null) ? 0 : getStoreCntsInStock().hashCode());
        result = prime * result + ((getTotalApplyqty() == null) ? 0 : getTotalApplyqty().hashCode());
        result = prime * result + ((getMaxApplyqty() == null) ? 0 : getMaxApplyqty().hashCode());
        result = prime * result + ((getMidqty() == null) ? 0 : getMidqty().hashCode());
        result = prime * result + ((getMaxqty() == null) ? 0 : getMaxqty().hashCode());
        result = prime * result + ((getStockToUseRatio() == null) ? 0 : getStockToUseRatio().hashCode());
        result = prime * result + ((getLifnr() == null) ? 0 : getLifnr().hashCode());
        result = prime * result + ((getDhdB() == null) ? 0 : getDhdB().hashCode());
        result = prime * result + ((getDcS() == null) ? 0 : getDcS().hashCode());
        result = prime * result + ((getDcL() == null) ? 0 : getDcL().hashCode());
        result = prime * result + ((getGoodsLevel() == null) ? 0 : getGoodsLevel().hashCode());
        result = prime * result + ((getSafetyStock() == null) ? 0 : getSafetyStock().hashCode());
        result = prime * result + ((getSumRV() == null) ? 0 : getSumRV().hashCode());
        result = prime * result + ((getReviseTotalBlockStock1() == null) ? 0 : getReviseTotalBlockStock1().hashCode());
        result = prime * result + ((getRawSuggestDhl() == null) ? 0 : getRawSuggestDhl().hashCode());
        result = prime * result + ((getZnoncxb() == null) ? 0 : getZnoncxb().hashCode());
        result = prime * result + ((getTotalDcStock() == null) ? 0 : getTotalDcStock().hashCode());
        result = prime * result + ((getTotalDcDisableStock() == null) ? 0 : getTotalDcDisableStock().hashCode());
        result = prime * result + ((getTotalStoreStock() == null) ? 0 : getTotalStoreStock().hashCode());
        result = prime * result + ((getQtyBefore30() == null) ? 0 : getQtyBefore30().hashCode());
        result = prime * result + ((getHbSaleRate() == null) ? 0 : getHbSaleRate().hashCode());
        result = prime * result + ((getTbSaleRate() == null) ? 0 : getTbSaleRate().hashCode());
        result = prime * result + ((getZdxmds() == null) ? 0 : getZdxmds().hashCode());
        result = prime * result + ((getDcSaleDays() == null) ? 0 : getDcSaleDays().hashCode());
        result = prime * result + ((getDcInvSaleDays() == null) ? 0 : getDcInvSaleDays().hashCode());
        result = prime * result + ((getStoreSaleDays() == null) ? 0 : getStoreSaleDays().hashCode());
        result = prime * result + ((getStoreInvSaleDays() == null) ? 0 : getStoreInvSaleDays().hashCode());
        result = prime * result + ((getPuhuoStores() == null) ? 0 : getPuhuoStores().hashCode());
        result = prime * result + ((getQtyBefore7() == null) ? 0 : getQtyBefore7().hashCode());
        result = prime * result + ((getAvgQtyBefore7() == null) ? 0 : getAvgQtyBefore7().hashCode());
        result = prime * result + ((getQtyBefore14() == null) ? 0 : getQtyBefore14().hashCode());
        result = prime * result + ((getAvgQtyBefore14() == null) ? 0 : getAvgQtyBefore14().hashCode());
        result = prime * result + ((getAvgQtyBefore30() == null) ? 0 : getAvgQtyBefore30().hashCode());
        result = prime * result + ((getQtyBefore90() == null) ? 0 : getQtyBefore90().hashCode());
        result = prime * result + ((getAvgQtyBefore90() == null) ? 0 : getAvgQtyBefore90().hashCode());
        result = prime * result + ((getDistqtyBefore7() == null) ? 0 : getDistqtyBefore7().hashCode());
        result = prime * result + ((getAvgDistqtyBefore7() == null) ? 0 : getAvgDistqtyBefore7().hashCode());
        result = prime * result + ((getDistqtyBefore14() == null) ? 0 : getDistqtyBefore14().hashCode());
        result = prime * result + ((getAvgDistqtyBefore14() == null) ? 0 : getAvgDistqtyBefore14().hashCode());
        result = prime * result + ((getDistqty30() == null) ? 0 : getDistqty30().hashCode());
        result = prime * result + ((getAvgDistqtyBefore30() == null) ? 0 : getAvgDistqtyBefore30().hashCode());
        result = prime * result + ((getDistqtyBefore90() == null) ? 0 : getDistqtyBefore90().hashCode());
        result = prime * result + ((getAvgDistqtyBefore90() == null) ? 0 : getAvgDistqtyBefore90().hashCode());
        result = prime * result + ((getQty3060() == null) ? 0 : getQty3060().hashCode());
        result = prime * result + ((getQty6090() == null) ? 0 : getQty6090().hashCode());
        result = prime * result + ((getDistqty3060() == null) ? 0 : getDistqty3060().hashCode());
        result = prime * result + ((getDistqty6090() == null) ? 0 : getDistqty6090().hashCode());
        result = prime * result + ((getSeasonalFactor() == null) ? 0 : getSeasonalFactor().hashCode());
        result = prime * result + ((getJmStoreStock() == null) ? 0 : getJmStoreStock().hashCode());
        result = prime * result + ((getDcStock() == null) ? 0 : getDcStock().hashCode());
        result = prime * result + ((getDcDisableStock() == null) ? 0 : getDcDisableStock().hashCode());
        result = prime * result + ((getZcInactiveDcStock() == null) ? 0 : getZcInactiveDcStock().hashCode());
        result = prime * result + ((getDcInvUpper() == null) ? 0 : getDcInvUpper().hashCode());
        result = prime * result + ((getDcBlockStock1() == null) ? 0 : getDcBlockStock1().hashCode());
        result = prime * result + ((getDcBlockStock2() == null) ? 0 : getDcBlockStock2().hashCode());
        result = prime * result + ((getDistDays() == null) ? 0 : getDistDays().hashCode());
        result = prime * result + ((getSentTime() == null) ? 0 : getSentTime().hashCode());
        result = prime * result + ((getAmountBefore30() == null) ? 0 : getAmountBefore30().hashCode());
        result = prime * result + ((getUnsatisfiedDeliverySku() == null) ? 0 : getUnsatisfiedDeliverySku().hashCode());
        result = prime * result + ((getUnsatisfiedDeliveryCnt() == null) ? 0 : getUnsatisfiedDeliveryCnt().hashCode());
        result = prime * result + ((getWeiqingtuicang() == null) ? 0 : getWeiqingtuicang().hashCode());
        result = prime * result + ((getWeiqingjisuan() == null) ? 0 : getWeiqingjisuan().hashCode());
        result = prime * result + ((getMinPurchaseType() == null) ? 0 : getMinPurchaseType().hashCode());
        result = prime * result + ((getMinPurchasePrice() == null) ? 0 : getMinPurchasePrice().hashCode());
        result = prime * result + ((getMinPurchaseDate() == null) ? 0 : getMinPurchaseDate().hashCode());
        result = prime * result + ((getZdcje1() == null) ? 0 : getZdcje1().hashCode());
        result = prime * result + ((getZdcje2() == null) ? 0 : getZdcje2().hashCode());
        result = prime * result + ((getZdcje3() == null) ? 0 : getZdcje3().hashCode());
        result = prime * result + ((getZdccb1() == null) ? 0 : getZdccb1().hashCode());
        result = prime * result + ((getZdccb2() == null) ? 0 : getZdccb2().hashCode());
        result = prime * result + ((getZmdje1() == null) ? 0 : getZmdje1().hashCode());
        result = prime * result + ((getZmdje2() == null) ? 0 : getZmdje2().hashCode());
        result = prime * result + ((getZmdje3() == null) ? 0 : getZmdje3().hashCode());
        result = prime * result + ((getZmdcb() == null) ? 0 : getZmdcb().hashCode());
        result = prime * result + ((getZcdzzts() == null) ? 0 : getZcdzzts().hashCode());
        result = prime * result + ((getZcdkcjepm() == null) ? 0 : getZcdkcjepm().hashCode());
        result = prime * result + ((getZdpt() == null) ? 0 : getZdpt().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", dt=").append(dt);
        sb.append(", werks=").append(werks);
        sb.append(", matnr=").append(matnr);
        sb.append(", avgQty=").append(avgQty);
        sb.append(", inactiveDcStock=").append(inactiveDcStock);
        sb.append(", inactiveStoreStock=").append(inactiveStoreStock);
        sb.append(", reviseTotalBlockStock2=").append(reviseTotalBlockStock2);
        sb.append(", weiqingcaigou7=").append(weiqingcaigou7);
        sb.append(", invUpper=").append(invUpper);
        sb.append(", suggestDhl=").append(suggestDhl);
        sb.append(", storeCntsInStock=").append(storeCntsInStock);
        sb.append(", totalApplyqty=").append(totalApplyqty);
        sb.append(", maxApplyqty=").append(maxApplyqty);
        sb.append(", midqty=").append(midqty);
        sb.append(", maxqty=").append(maxqty);
        sb.append(", stockToUseRatio=").append(stockToUseRatio);
        sb.append(", lifnr=").append(lifnr);
        sb.append(", dhdB=").append(dhdB);
        sb.append(", dcS=").append(dcS);
        sb.append(", dcL=").append(dcL);
        sb.append(", goodsLevel=").append(goodsLevel);
        sb.append(", safetyStock=").append(safetyStock);
        sb.append(", sumRV=").append(sumRV);
        sb.append(", reviseTotalBlockStock1=").append(reviseTotalBlockStock1);
        sb.append(", rawSuggestDhl=").append(rawSuggestDhl);
        sb.append(", znoncxb=").append(znoncxb);
        sb.append(", totalDcStock=").append(totalDcStock);
        sb.append(", totalDcDisableStock=").append(totalDcDisableStock);
        sb.append(", totalStoreStock=").append(totalStoreStock);
        sb.append(", qtyBefore30=").append(qtyBefore30);
        sb.append(", hbSaleRate=").append(hbSaleRate);
        sb.append(", tbSaleRate=").append(tbSaleRate);
        sb.append(", zdxmds=").append(zdxmds);
        sb.append(", dcSaleDays=").append(dcSaleDays);
        sb.append(", dcInvSaleDays=").append(dcInvSaleDays);
        sb.append(", storeSaleDays=").append(storeSaleDays);
        sb.append(", storeInvSaleDays=").append(storeInvSaleDays);
        sb.append(", puhuoStores=").append(puhuoStores);
        sb.append(", qtyBefore7=").append(qtyBefore7);
        sb.append(", avgQtyBefore7=").append(avgQtyBefore7);
        sb.append(", qtyBefore14=").append(qtyBefore14);
        sb.append(", avgQtyBefore14=").append(avgQtyBefore14);
        sb.append(", avgQtyBefore30=").append(avgQtyBefore30);
        sb.append(", qtyBefore90=").append(qtyBefore90);
        sb.append(", avgQtyBefore90=").append(avgQtyBefore90);
        sb.append(", distqtyBefore7=").append(distqtyBefore7);
        sb.append(", avgDistqtyBefore7=").append(avgDistqtyBefore7);
        sb.append(", distqtyBefore14=").append(distqtyBefore14);
        sb.append(", avgDistqtyBefore14=").append(avgDistqtyBefore14);
        sb.append(", distqty30=").append(distqty30);
        sb.append(", avgDistqtyBefore30=").append(avgDistqtyBefore30);
        sb.append(", distqtyBefore90=").append(distqtyBefore90);
        sb.append(", avgDistqtyBefore90=").append(avgDistqtyBefore90);
        sb.append(", qty3060=").append(qty3060);
        sb.append(", qty6090=").append(qty6090);
        sb.append(", distqty3060=").append(distqty3060);
        sb.append(", distqty6090=").append(distqty6090);
        sb.append(", seasonalFactor=").append(seasonalFactor);
        sb.append(", jmStoreStock=").append(jmStoreStock);
        sb.append(", dcStock=").append(dcStock);
        sb.append(", dcDisableStock=").append(dcDisableStock);
        sb.append(", zcInactiveDcStock=").append(zcInactiveDcStock);
        sb.append(", dcInvUpper=").append(dcInvUpper);
        sb.append(", dcBlockStock1=").append(dcBlockStock1);
        sb.append(", dcBlockStock2=").append(dcBlockStock2);
        sb.append(", distDays=").append(distDays);
        sb.append(", sentTime=").append(sentTime);
        sb.append(", amountBefore30=").append(amountBefore30);
        sb.append(", unsatisfiedDeliverySku=").append(unsatisfiedDeliverySku);
        sb.append(", unsatisfiedDeliveryCnt=").append(unsatisfiedDeliveryCnt);
        sb.append(", weiqingtuicang=").append(weiqingtuicang);
        sb.append(", weiqingjisuan=").append(weiqingjisuan);
        sb.append(", minPurchaseType=").append(minPurchaseType);
        sb.append(", minPurchasePrice=").append(minPurchasePrice);
        sb.append(", minPurchaseDate=").append(minPurchaseDate);
        sb.append(", zdcje1=").append(zdcje1);
        sb.append(", zdcje2=").append(zdcje2);
        sb.append(", zdcje3=").append(zdcje3);
        sb.append(", zdccb1=").append(zdccb1);
        sb.append(", zdccb2=").append(zdccb2);
        sb.append(", zmdje1=").append(zmdje1);
        sb.append(", zmdje2=").append(zmdje2);
        sb.append(", zmdje3=").append(zmdje3);
        sb.append(", zmdcb=").append(zmdcb);
        sb.append(", zcdzzts=").append(zcdzzts);
        sb.append(", zcdkcjepm=").append(zcdkcjepm);
        sb.append(", zdpt=").append(zdpt);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}