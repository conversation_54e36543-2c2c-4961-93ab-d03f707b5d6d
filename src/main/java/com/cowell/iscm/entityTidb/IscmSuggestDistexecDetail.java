package com.cowell.iscm.entityTidb;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> pos调拨明细跟踪详情
 */
public class IscmSuggestDistexecDetail implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 调拨日期(建议生成日期)
     */
    private Date allotDate;

    /**
     * 调拨月份 格式yyyyMM例如 202103
     */
    private Integer allotMonth;

    /**
     * 登记单号
     */
    private String registerNo;

    /**
     * 登记月份  例:202103
     */
    private Integer registerMonth;

    /**
     * 调拨类型,1:近效期调拨,2:大库存、滞销商品调拨,3:断货商品调拨
     */
    private Byte allotType;

    /**
     * pos调拨单号
     */
    private String posAllotNo;

    /**
     * 审批时间
     */
    private Date allotApproveTime;

    /**
     * pos调拨明细行号
     */
    private Long posAllotDetailId;

    /**
     * 平台orgId
     */
    private Long platformOrgId;

    /**
     * 平台名称
     */
    private String platformOrgName;

    /**
     * 调出公司Id
     */
    private Long outCompanyId;

    /**
     * 调出公司MDM编码
     */
    private String outCompanyCode;

    /**
     * 调出公司名称
     */
    private String outCompanyName;

    /**
     * 调入公司id
     */
    private Long inCompanyId;

    /**
     * 调入公司MDM编码
     */
    private String inCompanyCode;

    /**
     * 调入公司名称
     */
    private String inCompanyName;

    /**
     * 调出门店 id
     */
    private Long outStoreId;

    /**
     * 调出门店MDM编码
     */
    private String outStoreCode;

    /**
     * 调出门店名称
     */
    private String outStoreName;

    /**
     * 调出门店属性
     */
    private String outStoreAttr;

    /**
     * 调出门店月销等级
     */
    private String outStoreSalesLevel;

    /**
     * 调入门店 id
     */
    private Long inStoreId;

    /**
     * 调入门店MDM编码
     */
    private String inStoreCode;

    /**
     * 调入门店名称
     */
    private String inStoreName;

    /**
     * 调入门店属性
     */
    private String inStoreAttr;

    /**
     * 调入门店月销等级
     */
    private String inStoreSalesLevel;

    /**
     * 调拨组编码
     */
    private String allotGroupCode;

    /**
     * 调拨组名称
     */
    private String allotGroupName;

    /**
     * 审批人
     */
    private String approveName;

    /**
     * 商品编码
     */
    private String goodsNo;

    /**
     * 商品描述
     */
    private String goodsDesc;

    /**
     * 调拨批号
     */
    private String batchNo;

    /**
     * 生产日期
     */
    private String produceDate;

    /**
     * 有效期至
     */
    private String validityDate;

    /**
     * 海典批次
     */
    private String hdBatchNo;

    /**
     * sap批次
     */
    private String sapBatchNo;

    /**
     * 备注
     */
    private String notes;

    /**
     * 作废原因
     */
    private String voidReason;

    /**
     * 通用名
     */
    private String goodsCommonName;

    /**
     * 生产厂家
     */
    private String manufacturer;

    /**
     * 单位
     */
    private String unit;

    /**
     * 建议调出数量
     */
    private BigDecimal suggestAllotQuantity;

    /**
     * 建议调拨成本金额
     */
    private BigDecimal suggestCostAmount;

    /**
     * 实际调出数量
     */
    private BigDecimal realAllotQuantity;

    /**
     * 实际调拨成本金额
     */
    private BigDecimal realCostAmount;

    /**
     * 调出门店审批状态 0:未审批 1:审批通过 2:作废
     */
    private Byte outApproveStatus;

    /**
     * 调出门店审批时间
     */
    private Date outApproveTime;

    /**
     * 调出数量
     */
    private BigDecimal outAllotQuantity;

    /**
     * 调入门店审批状态 0:未审批 1:审批通过 2:作废
     */
    private Byte inApproveStatus;

    /**
     * 调入门店审批时间
     */
    private Date inApproveTime;

    /**
     * 调入数量
     */
    private BigDecimal inAllotQuantity;

    /**
     * 调入门店入库时间
     */
    private Date inStockTime;

    /**
     * 调入调出门店 0:有未审批 2:有已拒绝
     */
    private Byte showStatus;

    /**
     * 状态(-1删除，0正常)
     */
    private Byte status;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新时间
     */
    private Date gmtUpdate;

    /**
     * 扩展字段
     */
    private String extend;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 创建人ID
     */
    private Long createdBy;

    /**
     * 创建人
     */
    private String createdName;

    /**
     * 更新人ID
     */
    private Long updatedBy;

    /**
     * 更新人
     */
    private String updatedName;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getAllotDate() {
        return allotDate;
    }

    public void setAllotDate(Date allotDate) {
        this.allotDate = allotDate;
    }

    public Integer getAllotMonth() {
        return allotMonth;
    }

    public void setAllotMonth(Integer allotMonth) {
        this.allotMonth = allotMonth;
    }

    public String getRegisterNo() {
        return registerNo;
    }

    public void setRegisterNo(String registerNo) {
        this.registerNo = registerNo;
    }

    public Integer getRegisterMonth() {
        return registerMonth;
    }

    public void setRegisterMonth(Integer registerMonth) {
        this.registerMonth = registerMonth;
    }

    public Byte getAllotType() {
        return allotType;
    }

    public void setAllotType(Byte allotType) {
        this.allotType = allotType;
    }

    public String getPosAllotNo() {
        return posAllotNo;
    }

    public void setPosAllotNo(String posAllotNo) {
        this.posAllotNo = posAllotNo;
    }

    public Date getAllotApproveTime() {
        return allotApproveTime;
    }

    public void setAllotApproveTime(Date allotApproveTime) {
        this.allotApproveTime = allotApproveTime;
    }

    public Long getPosAllotDetailId() {
        return posAllotDetailId;
    }

    public void setPosAllotDetailId(Long posAllotDetailId) {
        this.posAllotDetailId = posAllotDetailId;
    }

    public Long getPlatformOrgId() {
        return platformOrgId;
    }

    public void setPlatformOrgId(Long platformOrgId) {
        this.platformOrgId = platformOrgId;
    }

    public String getPlatformOrgName() {
        return platformOrgName;
    }

    public void setPlatformOrgName(String platformOrgName) {
        this.platformOrgName = platformOrgName;
    }

    public Long getOutCompanyId() {
        return outCompanyId;
    }

    public void setOutCompanyId(Long outCompanyId) {
        this.outCompanyId = outCompanyId;
    }

    public String getOutCompanyCode() {
        return outCompanyCode;
    }

    public void setOutCompanyCode(String outCompanyCode) {
        this.outCompanyCode = outCompanyCode;
    }

    public String getOutCompanyName() {
        return outCompanyName;
    }

    public void setOutCompanyName(String outCompanyName) {
        this.outCompanyName = outCompanyName;
    }

    public Long getInCompanyId() {
        return inCompanyId;
    }

    public void setInCompanyId(Long inCompanyId) {
        this.inCompanyId = inCompanyId;
    }

    public String getInCompanyCode() {
        return inCompanyCode;
    }

    public void setInCompanyCode(String inCompanyCode) {
        this.inCompanyCode = inCompanyCode;
    }

    public String getInCompanyName() {
        return inCompanyName;
    }

    public void setInCompanyName(String inCompanyName) {
        this.inCompanyName = inCompanyName;
    }

    public Long getOutStoreId() {
        return outStoreId;
    }

    public void setOutStoreId(Long outStoreId) {
        this.outStoreId = outStoreId;
    }

    public String getOutStoreCode() {
        return outStoreCode;
    }

    public void setOutStoreCode(String outStoreCode) {
        this.outStoreCode = outStoreCode;
    }

    public String getOutStoreName() {
        return outStoreName;
    }

    public void setOutStoreName(String outStoreName) {
        this.outStoreName = outStoreName;
    }

    public String getOutStoreAttr() {
        return outStoreAttr;
    }

    public void setOutStoreAttr(String outStoreAttr) {
        this.outStoreAttr = outStoreAttr;
    }

    public String getOutStoreSalesLevel() {
        return outStoreSalesLevel;
    }

    public void setOutStoreSalesLevel(String outStoreSalesLevel) {
        this.outStoreSalesLevel = outStoreSalesLevel;
    }

    public Long getInStoreId() {
        return inStoreId;
    }

    public void setInStoreId(Long inStoreId) {
        this.inStoreId = inStoreId;
    }

    public String getInStoreCode() {
        return inStoreCode;
    }

    public void setInStoreCode(String inStoreCode) {
        this.inStoreCode = inStoreCode;
    }

    public String getInStoreName() {
        return inStoreName;
    }

    public void setInStoreName(String inStoreName) {
        this.inStoreName = inStoreName;
    }

    public String getInStoreAttr() {
        return inStoreAttr;
    }

    public void setInStoreAttr(String inStoreAttr) {
        this.inStoreAttr = inStoreAttr;
    }

    public String getInStoreSalesLevel() {
        return inStoreSalesLevel;
    }

    public void setInStoreSalesLevel(String inStoreSalesLevel) {
        this.inStoreSalesLevel = inStoreSalesLevel;
    }

    public String getAllotGroupCode() {
        return allotGroupCode;
    }

    public void setAllotGroupCode(String allotGroupCode) {
        this.allotGroupCode = allotGroupCode;
    }

    public String getAllotGroupName() {
        return allotGroupName;
    }

    public void setAllotGroupName(String allotGroupName) {
        this.allotGroupName = allotGroupName;
    }

    public String getApproveName() {
        return approveName;
    }

    public void setApproveName(String approveName) {
        this.approveName = approveName;
    }

    public String getGoodsNo() {
        return goodsNo;
    }

    public void setGoodsNo(String goodsNo) {
        this.goodsNo = goodsNo;
    }

    public String getGoodsDesc() {
        return goodsDesc;
    }

    public void setGoodsDesc(String goodsDesc) {
        this.goodsDesc = goodsDesc;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public String getProduceDate() {
        return produceDate;
    }

    public void setProduceDate(String produceDate) {
        this.produceDate = produceDate;
    }

    public String getValidityDate() {
        return validityDate;
    }

    public void setValidityDate(String validityDate) {
        this.validityDate = validityDate;
    }

    public String getHdBatchNo() {
        return hdBatchNo;
    }

    public void setHdBatchNo(String hdBatchNo) {
        this.hdBatchNo = hdBatchNo;
    }

    public String getSapBatchNo() {
        return sapBatchNo;
    }

    public void setSapBatchNo(String sapBatchNo) {
        this.sapBatchNo = sapBatchNo;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public String getVoidReason() {
        return voidReason;
    }

    public void setVoidReason(String voidReason) {
        this.voidReason = voidReason;
    }

    public String getGoodsCommonName() {
        return goodsCommonName;
    }

    public void setGoodsCommonName(String goodsCommonName) {
        this.goodsCommonName = goodsCommonName;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public BigDecimal getSuggestAllotQuantity() {
        return suggestAllotQuantity;
    }

    public void setSuggestAllotQuantity(BigDecimal suggestAllotQuantity) {
        this.suggestAllotQuantity = suggestAllotQuantity;
    }

    public BigDecimal getSuggestCostAmount() {
        return suggestCostAmount;
    }

    public void setSuggestCostAmount(BigDecimal suggestCostAmount) {
        this.suggestCostAmount = suggestCostAmount;
    }

    public BigDecimal getRealAllotQuantity() {
        return realAllotQuantity;
    }

    public void setRealAllotQuantity(BigDecimal realAllotQuantity) {
        this.realAllotQuantity = realAllotQuantity;
    }

    public BigDecimal getRealCostAmount() {
        return realCostAmount;
    }

    public void setRealCostAmount(BigDecimal realCostAmount) {
        this.realCostAmount = realCostAmount;
    }

    public Byte getOutApproveStatus() {
        return outApproveStatus;
    }

    public void setOutApproveStatus(Byte outApproveStatus) {
        this.outApproveStatus = outApproveStatus;
    }

    public Date getOutApproveTime() {
        return outApproveTime;
    }

    public void setOutApproveTime(Date outApproveTime) {
        this.outApproveTime = outApproveTime;
    }

    public BigDecimal getOutAllotQuantity() {
        return outAllotQuantity;
    }

    public void setOutAllotQuantity(BigDecimal outAllotQuantity) {
        this.outAllotQuantity = outAllotQuantity;
    }

    public Byte getInApproveStatus() {
        return inApproveStatus;
    }

    public void setInApproveStatus(Byte inApproveStatus) {
        this.inApproveStatus = inApproveStatus;
    }

    public Date getInApproveTime() {
        return inApproveTime;
    }

    public void setInApproveTime(Date inApproveTime) {
        this.inApproveTime = inApproveTime;
    }

    public BigDecimal getInAllotQuantity() {
        return inAllotQuantity;
    }

    public void setInAllotQuantity(BigDecimal inAllotQuantity) {
        this.inAllotQuantity = inAllotQuantity;
    }

    public Date getInStockTime() {
        return inStockTime;
    }

    public void setInStockTime(Date inStockTime) {
        this.inStockTime = inStockTime;
    }

    public Byte getShowStatus() {
        return showStatus;
    }

    public void setShowStatus(Byte showStatus) {
        this.showStatus = showStatus;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtUpdate() {
        return gmtUpdate;
    }

    public void setGmtUpdate(Date gmtUpdate) {
        this.gmtUpdate = gmtUpdate;
    }

    public String getExtend() {
        return extend;
    }

    public void setExtend(String extend) {
        this.extend = extend;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Long getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    public String getCreatedName() {
        return createdName;
    }

    public void setCreatedName(String createdName) {
        this.createdName = createdName;
    }

    public Long getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(Long updatedBy) {
        this.updatedBy = updatedBy;
    }

    public String getUpdatedName() {
        return updatedName;
    }

    public void setUpdatedName(String updatedName) {
        this.updatedName = updatedName;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        IscmSuggestDistexecDetail other = (IscmSuggestDistexecDetail) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getAllotDate() == null ? other.getAllotDate() == null : this.getAllotDate().equals(other.getAllotDate()))
            && (this.getAllotMonth() == null ? other.getAllotMonth() == null : this.getAllotMonth().equals(other.getAllotMonth()))
            && (this.getRegisterNo() == null ? other.getRegisterNo() == null : this.getRegisterNo().equals(other.getRegisterNo()))
            && (this.getRegisterMonth() == null ? other.getRegisterMonth() == null : this.getRegisterMonth().equals(other.getRegisterMonth()))
            && (this.getAllotType() == null ? other.getAllotType() == null : this.getAllotType().equals(other.getAllotType()))
            && (this.getPosAllotNo() == null ? other.getPosAllotNo() == null : this.getPosAllotNo().equals(other.getPosAllotNo()))
            && (this.getAllotApproveTime() == null ? other.getAllotApproveTime() == null : this.getAllotApproveTime().equals(other.getAllotApproveTime()))
            && (this.getPosAllotDetailId() == null ? other.getPosAllotDetailId() == null : this.getPosAllotDetailId().equals(other.getPosAllotDetailId()))
            && (this.getPlatformOrgId() == null ? other.getPlatformOrgId() == null : this.getPlatformOrgId().equals(other.getPlatformOrgId()))
            && (this.getPlatformOrgName() == null ? other.getPlatformOrgName() == null : this.getPlatformOrgName().equals(other.getPlatformOrgName()))
            && (this.getOutCompanyId() == null ? other.getOutCompanyId() == null : this.getOutCompanyId().equals(other.getOutCompanyId()))
            && (this.getOutCompanyCode() == null ? other.getOutCompanyCode() == null : this.getOutCompanyCode().equals(other.getOutCompanyCode()))
            && (this.getOutCompanyName() == null ? other.getOutCompanyName() == null : this.getOutCompanyName().equals(other.getOutCompanyName()))
            && (this.getInCompanyId() == null ? other.getInCompanyId() == null : this.getInCompanyId().equals(other.getInCompanyId()))
            && (this.getInCompanyCode() == null ? other.getInCompanyCode() == null : this.getInCompanyCode().equals(other.getInCompanyCode()))
            && (this.getInCompanyName() == null ? other.getInCompanyName() == null : this.getInCompanyName().equals(other.getInCompanyName()))
            && (this.getOutStoreId() == null ? other.getOutStoreId() == null : this.getOutStoreId().equals(other.getOutStoreId()))
            && (this.getOutStoreCode() == null ? other.getOutStoreCode() == null : this.getOutStoreCode().equals(other.getOutStoreCode()))
            && (this.getOutStoreName() == null ? other.getOutStoreName() == null : this.getOutStoreName().equals(other.getOutStoreName()))
            && (this.getOutStoreAttr() == null ? other.getOutStoreAttr() == null : this.getOutStoreAttr().equals(other.getOutStoreAttr()))
            && (this.getOutStoreSalesLevel() == null ? other.getOutStoreSalesLevel() == null : this.getOutStoreSalesLevel().equals(other.getOutStoreSalesLevel()))
            && (this.getInStoreId() == null ? other.getInStoreId() == null : this.getInStoreId().equals(other.getInStoreId()))
            && (this.getInStoreCode() == null ? other.getInStoreCode() == null : this.getInStoreCode().equals(other.getInStoreCode()))
            && (this.getInStoreName() == null ? other.getInStoreName() == null : this.getInStoreName().equals(other.getInStoreName()))
            && (this.getInStoreAttr() == null ? other.getInStoreAttr() == null : this.getInStoreAttr().equals(other.getInStoreAttr()))
            && (this.getInStoreSalesLevel() == null ? other.getInStoreSalesLevel() == null : this.getInStoreSalesLevel().equals(other.getInStoreSalesLevel()))
            && (this.getAllotGroupCode() == null ? other.getAllotGroupCode() == null : this.getAllotGroupCode().equals(other.getAllotGroupCode()))
            && (this.getAllotGroupName() == null ? other.getAllotGroupName() == null : this.getAllotGroupName().equals(other.getAllotGroupName()))
            && (this.getApproveName() == null ? other.getApproveName() == null : this.getApproveName().equals(other.getApproveName()))
            && (this.getGoodsNo() == null ? other.getGoodsNo() == null : this.getGoodsNo().equals(other.getGoodsNo()))
            && (this.getGoodsDesc() == null ? other.getGoodsDesc() == null : this.getGoodsDesc().equals(other.getGoodsDesc()))
            && (this.getBatchNo() == null ? other.getBatchNo() == null : this.getBatchNo().equals(other.getBatchNo()))
            && (this.getProduceDate() == null ? other.getProduceDate() == null : this.getProduceDate().equals(other.getProduceDate()))
            && (this.getValidityDate() == null ? other.getValidityDate() == null : this.getValidityDate().equals(other.getValidityDate()))
            && (this.getHdBatchNo() == null ? other.getHdBatchNo() == null : this.getHdBatchNo().equals(other.getHdBatchNo()))
            && (this.getSapBatchNo() == null ? other.getSapBatchNo() == null : this.getSapBatchNo().equals(other.getSapBatchNo()))
            && (this.getNotes() == null ? other.getNotes() == null : this.getNotes().equals(other.getNotes()))
            && (this.getVoidReason() == null ? other.getVoidReason() == null : this.getVoidReason().equals(other.getVoidReason()))
            && (this.getGoodsCommonName() == null ? other.getGoodsCommonName() == null : this.getGoodsCommonName().equals(other.getGoodsCommonName()))
            && (this.getManufacturer() == null ? other.getManufacturer() == null : this.getManufacturer().equals(other.getManufacturer()))
            && (this.getUnit() == null ? other.getUnit() == null : this.getUnit().equals(other.getUnit()))
            && (this.getSuggestAllotQuantity() == null ? other.getSuggestAllotQuantity() == null : this.getSuggestAllotQuantity().equals(other.getSuggestAllotQuantity()))
            && (this.getSuggestCostAmount() == null ? other.getSuggestCostAmount() == null : this.getSuggestCostAmount().equals(other.getSuggestCostAmount()))
            && (this.getRealAllotQuantity() == null ? other.getRealAllotQuantity() == null : this.getRealAllotQuantity().equals(other.getRealAllotQuantity()))
            && (this.getRealCostAmount() == null ? other.getRealCostAmount() == null : this.getRealCostAmount().equals(other.getRealCostAmount()))
            && (this.getOutApproveStatus() == null ? other.getOutApproveStatus() == null : this.getOutApproveStatus().equals(other.getOutApproveStatus()))
            && (this.getOutApproveTime() == null ? other.getOutApproveTime() == null : this.getOutApproveTime().equals(other.getOutApproveTime()))
            && (this.getOutAllotQuantity() == null ? other.getOutAllotQuantity() == null : this.getOutAllotQuantity().equals(other.getOutAllotQuantity()))
            && (this.getInApproveStatus() == null ? other.getInApproveStatus() == null : this.getInApproveStatus().equals(other.getInApproveStatus()))
            && (this.getInApproveTime() == null ? other.getInApproveTime() == null : this.getInApproveTime().equals(other.getInApproveTime()))
            && (this.getInAllotQuantity() == null ? other.getInAllotQuantity() == null : this.getInAllotQuantity().equals(other.getInAllotQuantity()))
            && (this.getInStockTime() == null ? other.getInStockTime() == null : this.getInStockTime().equals(other.getInStockTime()))
            && (this.getShowStatus() == null ? other.getShowStatus() == null : this.getShowStatus().equals(other.getShowStatus()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getGmtCreate() == null ? other.getGmtCreate() == null : this.getGmtCreate().equals(other.getGmtCreate()))
            && (this.getGmtUpdate() == null ? other.getGmtUpdate() == null : this.getGmtUpdate().equals(other.getGmtUpdate()))
            && (this.getExtend() == null ? other.getExtend() == null : this.getExtend().equals(other.getExtend()))
            && (this.getVersion() == null ? other.getVersion() == null : this.getVersion().equals(other.getVersion()))
            && (this.getCreatedBy() == null ? other.getCreatedBy() == null : this.getCreatedBy().equals(other.getCreatedBy()))
            && (this.getCreatedName() == null ? other.getCreatedName() == null : this.getCreatedName().equals(other.getCreatedName()))
            && (this.getUpdatedBy() == null ? other.getUpdatedBy() == null : this.getUpdatedBy().equals(other.getUpdatedBy()))
            && (this.getUpdatedName() == null ? other.getUpdatedName() == null : this.getUpdatedName().equals(other.getUpdatedName()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getAllotDate() == null) ? 0 : getAllotDate().hashCode());
        result = prime * result + ((getAllotMonth() == null) ? 0 : getAllotMonth().hashCode());
        result = prime * result + ((getRegisterNo() == null) ? 0 : getRegisterNo().hashCode());
        result = prime * result + ((getRegisterMonth() == null) ? 0 : getRegisterMonth().hashCode());
        result = prime * result + ((getAllotType() == null) ? 0 : getAllotType().hashCode());
        result = prime * result + ((getPosAllotNo() == null) ? 0 : getPosAllotNo().hashCode());
        result = prime * result + ((getAllotApproveTime() == null) ? 0 : getAllotApproveTime().hashCode());
        result = prime * result + ((getPosAllotDetailId() == null) ? 0 : getPosAllotDetailId().hashCode());
        result = prime * result + ((getPlatformOrgId() == null) ? 0 : getPlatformOrgId().hashCode());
        result = prime * result + ((getPlatformOrgName() == null) ? 0 : getPlatformOrgName().hashCode());
        result = prime * result + ((getOutCompanyId() == null) ? 0 : getOutCompanyId().hashCode());
        result = prime * result + ((getOutCompanyCode() == null) ? 0 : getOutCompanyCode().hashCode());
        result = prime * result + ((getOutCompanyName() == null) ? 0 : getOutCompanyName().hashCode());
        result = prime * result + ((getInCompanyId() == null) ? 0 : getInCompanyId().hashCode());
        result = prime * result + ((getInCompanyCode() == null) ? 0 : getInCompanyCode().hashCode());
        result = prime * result + ((getInCompanyName() == null) ? 0 : getInCompanyName().hashCode());
        result = prime * result + ((getOutStoreId() == null) ? 0 : getOutStoreId().hashCode());
        result = prime * result + ((getOutStoreCode() == null) ? 0 : getOutStoreCode().hashCode());
        result = prime * result + ((getOutStoreName() == null) ? 0 : getOutStoreName().hashCode());
        result = prime * result + ((getOutStoreAttr() == null) ? 0 : getOutStoreAttr().hashCode());
        result = prime * result + ((getOutStoreSalesLevel() == null) ? 0 : getOutStoreSalesLevel().hashCode());
        result = prime * result + ((getInStoreId() == null) ? 0 : getInStoreId().hashCode());
        result = prime * result + ((getInStoreCode() == null) ? 0 : getInStoreCode().hashCode());
        result = prime * result + ((getInStoreName() == null) ? 0 : getInStoreName().hashCode());
        result = prime * result + ((getInStoreAttr() == null) ? 0 : getInStoreAttr().hashCode());
        result = prime * result + ((getInStoreSalesLevel() == null) ? 0 : getInStoreSalesLevel().hashCode());
        result = prime * result + ((getAllotGroupCode() == null) ? 0 : getAllotGroupCode().hashCode());
        result = prime * result + ((getAllotGroupName() == null) ? 0 : getAllotGroupName().hashCode());
        result = prime * result + ((getApproveName() == null) ? 0 : getApproveName().hashCode());
        result = prime * result + ((getGoodsNo() == null) ? 0 : getGoodsNo().hashCode());
        result = prime * result + ((getGoodsDesc() == null) ? 0 : getGoodsDesc().hashCode());
        result = prime * result + ((getBatchNo() == null) ? 0 : getBatchNo().hashCode());
        result = prime * result + ((getProduceDate() == null) ? 0 : getProduceDate().hashCode());
        result = prime * result + ((getValidityDate() == null) ? 0 : getValidityDate().hashCode());
        result = prime * result + ((getHdBatchNo() == null) ? 0 : getHdBatchNo().hashCode());
        result = prime * result + ((getSapBatchNo() == null) ? 0 : getSapBatchNo().hashCode());
        result = prime * result + ((getNotes() == null) ? 0 : getNotes().hashCode());
        result = prime * result + ((getVoidReason() == null) ? 0 : getVoidReason().hashCode());
        result = prime * result + ((getGoodsCommonName() == null) ? 0 : getGoodsCommonName().hashCode());
        result = prime * result + ((getManufacturer() == null) ? 0 : getManufacturer().hashCode());
        result = prime * result + ((getUnit() == null) ? 0 : getUnit().hashCode());
        result = prime * result + ((getSuggestAllotQuantity() == null) ? 0 : getSuggestAllotQuantity().hashCode());
        result = prime * result + ((getSuggestCostAmount() == null) ? 0 : getSuggestCostAmount().hashCode());
        result = prime * result + ((getRealAllotQuantity() == null) ? 0 : getRealAllotQuantity().hashCode());
        result = prime * result + ((getRealCostAmount() == null) ? 0 : getRealCostAmount().hashCode());
        result = prime * result + ((getOutApproveStatus() == null) ? 0 : getOutApproveStatus().hashCode());
        result = prime * result + ((getOutApproveTime() == null) ? 0 : getOutApproveTime().hashCode());
        result = prime * result + ((getOutAllotQuantity() == null) ? 0 : getOutAllotQuantity().hashCode());
        result = prime * result + ((getInApproveStatus() == null) ? 0 : getInApproveStatus().hashCode());
        result = prime * result + ((getInApproveTime() == null) ? 0 : getInApproveTime().hashCode());
        result = prime * result + ((getInAllotQuantity() == null) ? 0 : getInAllotQuantity().hashCode());
        result = prime * result + ((getInStockTime() == null) ? 0 : getInStockTime().hashCode());
        result = prime * result + ((getShowStatus() == null) ? 0 : getShowStatus().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getGmtCreate() == null) ? 0 : getGmtCreate().hashCode());
        result = prime * result + ((getGmtUpdate() == null) ? 0 : getGmtUpdate().hashCode());
        result = prime * result + ((getExtend() == null) ? 0 : getExtend().hashCode());
        result = prime * result + ((getVersion() == null) ? 0 : getVersion().hashCode());
        result = prime * result + ((getCreatedBy() == null) ? 0 : getCreatedBy().hashCode());
        result = prime * result + ((getCreatedName() == null) ? 0 : getCreatedName().hashCode());
        result = prime * result + ((getUpdatedBy() == null) ? 0 : getUpdatedBy().hashCode());
        result = prime * result + ((getUpdatedName() == null) ? 0 : getUpdatedName().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", allotDate=").append(allotDate);
        sb.append(", allotMonth=").append(allotMonth);
        sb.append(", registerNo=").append(registerNo);
        sb.append(", registerMonth=").append(registerMonth);
        sb.append(", allotType=").append(allotType);
        sb.append(", posAllotNo=").append(posAllotNo);
        sb.append(", allotApproveTime=").append(allotApproveTime);
        sb.append(", posAllotDetailId=").append(posAllotDetailId);
        sb.append(", platformOrgId=").append(platformOrgId);
        sb.append(", platformOrgName=").append(platformOrgName);
        sb.append(", outCompanyId=").append(outCompanyId);
        sb.append(", outCompanyCode=").append(outCompanyCode);
        sb.append(", outCompanyName=").append(outCompanyName);
        sb.append(", inCompanyId=").append(inCompanyId);
        sb.append(", inCompanyCode=").append(inCompanyCode);
        sb.append(", inCompanyName=").append(inCompanyName);
        sb.append(", outStoreId=").append(outStoreId);
        sb.append(", outStoreCode=").append(outStoreCode);
        sb.append(", outStoreName=").append(outStoreName);
        sb.append(", outStoreAttr=").append(outStoreAttr);
        sb.append(", outStoreSalesLevel=").append(outStoreSalesLevel);
        sb.append(", inStoreId=").append(inStoreId);
        sb.append(", inStoreCode=").append(inStoreCode);
        sb.append(", inStoreName=").append(inStoreName);
        sb.append(", inStoreAttr=").append(inStoreAttr);
        sb.append(", inStoreSalesLevel=").append(inStoreSalesLevel);
        sb.append(", allotGroupCode=").append(allotGroupCode);
        sb.append(", allotGroupName=").append(allotGroupName);
        sb.append(", approveName=").append(approveName);
        sb.append(", goodsNo=").append(goodsNo);
        sb.append(", goodsDesc=").append(goodsDesc);
        sb.append(", batchNo=").append(batchNo);
        sb.append(", produceDate=").append(produceDate);
        sb.append(", validityDate=").append(validityDate);
        sb.append(", hdBatchNo=").append(hdBatchNo);
        sb.append(", sapBatchNo=").append(sapBatchNo);
        sb.append(", notes=").append(notes);
        sb.append(", voidReason=").append(voidReason);
        sb.append(", goodsCommonName=").append(goodsCommonName);
        sb.append(", manufacturer=").append(manufacturer);
        sb.append(", unit=").append(unit);
        sb.append(", suggestAllotQuantity=").append(suggestAllotQuantity);
        sb.append(", suggestCostAmount=").append(suggestCostAmount);
        sb.append(", realAllotQuantity=").append(realAllotQuantity);
        sb.append(", realCostAmount=").append(realCostAmount);
        sb.append(", outApproveStatus=").append(outApproveStatus);
        sb.append(", outApproveTime=").append(outApproveTime);
        sb.append(", outAllotQuantity=").append(outAllotQuantity);
        sb.append(", inApproveStatus=").append(inApproveStatus);
        sb.append(", inApproveTime=").append(inApproveTime);
        sb.append(", inAllotQuantity=").append(inAllotQuantity);
        sb.append(", inStockTime=").append(inStockTime);
        sb.append(", showStatus=").append(showStatus);
        sb.append(", status=").append(status);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", gmtUpdate=").append(gmtUpdate);
        sb.append(", extend=").append(extend);
        sb.append(", version=").append(version);
        sb.append(", createdBy=").append(createdBy);
        sb.append(", createdName=").append(createdName);
        sb.append(", updatedBy=").append(updatedBy);
        sb.append(", updatedName=").append(updatedName);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}