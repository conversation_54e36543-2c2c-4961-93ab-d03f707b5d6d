package com.cowell.iscm.entityTidb;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class IscmSuggestDistexecExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public IscmSuggestDistexecExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andRegisterDateIsNull() {
            addCriterion("register_date is null");
            return (Criteria) this;
        }

        public Criteria andRegisterDateIsNotNull() {
            addCriterion("register_date is not null");
            return (Criteria) this;
        }

        public Criteria andRegisterDateEqualTo(Date value) {
            addCriterion("register_date =", value, "registerDate");
            return (Criteria) this;
        }

        public Criteria andRegisterDateNotEqualTo(Date value) {
            addCriterion("register_date <>", value, "registerDate");
            return (Criteria) this;
        }

        public Criteria andRegisterDateGreaterThan(Date value) {
            addCriterion("register_date >", value, "registerDate");
            return (Criteria) this;
        }

        public Criteria andRegisterDateGreaterThanOrEqualTo(Date value) {
            addCriterion("register_date >=", value, "registerDate");
            return (Criteria) this;
        }

        public Criteria andRegisterDateLessThan(Date value) {
            addCriterion("register_date <", value, "registerDate");
            return (Criteria) this;
        }

        public Criteria andRegisterDateLessThanOrEqualTo(Date value) {
            addCriterion("register_date <=", value, "registerDate");
            return (Criteria) this;
        }

        public Criteria andRegisterDateIn(List<Date> values) {
            addCriterion("register_date in", values, "registerDate");
            return (Criteria) this;
        }

        public Criteria andRegisterDateNotIn(List<Date> values) {
            addCriterion("register_date not in", values, "registerDate");
            return (Criteria) this;
        }

        public Criteria andRegisterDateBetween(Date value1, Date value2) {
            addCriterion("register_date between", value1, value2, "registerDate");
            return (Criteria) this;
        }

        public Criteria andRegisterDateNotBetween(Date value1, Date value2) {
            addCriterion("register_date not between", value1, value2, "registerDate");
            return (Criteria) this;
        }

        public Criteria andAllotTypeIsNull() {
            addCriterion("allot_type is null");
            return (Criteria) this;
        }

        public Criteria andAllotTypeIsNotNull() {
            addCriterion("allot_type is not null");
            return (Criteria) this;
        }

        public Criteria andAllotTypeEqualTo(Byte value) {
            addCriterion("allot_type =", value, "allotType");
            return (Criteria) this;
        }

        public Criteria andAllotTypeNotEqualTo(Byte value) {
            addCriterion("allot_type <>", value, "allotType");
            return (Criteria) this;
        }

        public Criteria andAllotTypeGreaterThan(Byte value) {
            addCriterion("allot_type >", value, "allotType");
            return (Criteria) this;
        }

        public Criteria andAllotTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("allot_type >=", value, "allotType");
            return (Criteria) this;
        }

        public Criteria andAllotTypeLessThan(Byte value) {
            addCriterion("allot_type <", value, "allotType");
            return (Criteria) this;
        }

        public Criteria andAllotTypeLessThanOrEqualTo(Byte value) {
            addCriterion("allot_type <=", value, "allotType");
            return (Criteria) this;
        }

        public Criteria andAllotTypeIn(List<Byte> values) {
            addCriterion("allot_type in", values, "allotType");
            return (Criteria) this;
        }

        public Criteria andAllotTypeNotIn(List<Byte> values) {
            addCriterion("allot_type not in", values, "allotType");
            return (Criteria) this;
        }

        public Criteria andAllotTypeBetween(Byte value1, Byte value2) {
            addCriterion("allot_type between", value1, value2, "allotType");
            return (Criteria) this;
        }

        public Criteria andAllotTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("allot_type not between", value1, value2, "allotType");
            return (Criteria) this;
        }

        public Criteria andRegisterNoIsNull() {
            addCriterion("register_no is null");
            return (Criteria) this;
        }

        public Criteria andRegisterNoIsNotNull() {
            addCriterion("register_no is not null");
            return (Criteria) this;
        }

        public Criteria andRegisterNoEqualTo(String value) {
            addCriterion("register_no =", value, "registerNo");
            return (Criteria) this;
        }

        public Criteria andRegisterNoNotEqualTo(String value) {
            addCriterion("register_no <>", value, "registerNo");
            return (Criteria) this;
        }

        public Criteria andRegisterNoGreaterThan(String value) {
            addCriterion("register_no >", value, "registerNo");
            return (Criteria) this;
        }

        public Criteria andRegisterNoGreaterThanOrEqualTo(String value) {
            addCriterion("register_no >=", value, "registerNo");
            return (Criteria) this;
        }

        public Criteria andRegisterNoLessThan(String value) {
            addCriterion("register_no <", value, "registerNo");
            return (Criteria) this;
        }

        public Criteria andRegisterNoLessThanOrEqualTo(String value) {
            addCriterion("register_no <=", value, "registerNo");
            return (Criteria) this;
        }

        public Criteria andRegisterNoLike(String value) {
            addCriterion("register_no like", value, "registerNo");
            return (Criteria) this;
        }

        public Criteria andRegisterNoNotLike(String value) {
            addCriterion("register_no not like", value, "registerNo");
            return (Criteria) this;
        }

        public Criteria andRegisterNoIn(List<String> values) {
            addCriterion("register_no in", values, "registerNo");
            return (Criteria) this;
        }

        public Criteria andRegisterNoNotIn(List<String> values) {
            addCriterion("register_no not in", values, "registerNo");
            return (Criteria) this;
        }

        public Criteria andRegisterNoBetween(String value1, String value2) {
            addCriterion("register_no between", value1, value2, "registerNo");
            return (Criteria) this;
        }

        public Criteria andRegisterNoNotBetween(String value1, String value2) {
            addCriterion("register_no not between", value1, value2, "registerNo");
            return (Criteria) this;
        }

        public Criteria andPosAllotNoIsNull() {
            addCriterion("pos_allot_no is null");
            return (Criteria) this;
        }

        public Criteria andPosAllotNoIsNotNull() {
            addCriterion("pos_allot_no is not null");
            return (Criteria) this;
        }

        public Criteria andPosAllotNoEqualTo(String value) {
            addCriterion("pos_allot_no =", value, "posAllotNo");
            return (Criteria) this;
        }

        public Criteria andPosAllotNoNotEqualTo(String value) {
            addCriterion("pos_allot_no <>", value, "posAllotNo");
            return (Criteria) this;
        }

        public Criteria andPosAllotNoGreaterThan(String value) {
            addCriterion("pos_allot_no >", value, "posAllotNo");
            return (Criteria) this;
        }

        public Criteria andPosAllotNoGreaterThanOrEqualTo(String value) {
            addCriterion("pos_allot_no >=", value, "posAllotNo");
            return (Criteria) this;
        }

        public Criteria andPosAllotNoLessThan(String value) {
            addCriterion("pos_allot_no <", value, "posAllotNo");
            return (Criteria) this;
        }

        public Criteria andPosAllotNoLessThanOrEqualTo(String value) {
            addCriterion("pos_allot_no <=", value, "posAllotNo");
            return (Criteria) this;
        }

        public Criteria andPosAllotNoLike(String value) {
            addCriterion("pos_allot_no like", value, "posAllotNo");
            return (Criteria) this;
        }

        public Criteria andPosAllotNoNotLike(String value) {
            addCriterion("pos_allot_no not like", value, "posAllotNo");
            return (Criteria) this;
        }

        public Criteria andPosAllotNoIn(List<String> values) {
            addCriterion("pos_allot_no in", values, "posAllotNo");
            return (Criteria) this;
        }

        public Criteria andPosAllotNoNotIn(List<String> values) {
            addCriterion("pos_allot_no not in", values, "posAllotNo");
            return (Criteria) this;
        }

        public Criteria andPosAllotNoBetween(String value1, String value2) {
            addCriterion("pos_allot_no between", value1, value2, "posAllotNo");
            return (Criteria) this;
        }

        public Criteria andPosAllotNoNotBetween(String value1, String value2) {
            addCriterion("pos_allot_no not between", value1, value2, "posAllotNo");
            return (Criteria) this;
        }

        public Criteria andPosAllotDetailIdIsNull() {
            addCriterion("pos_allot_detail_id is null");
            return (Criteria) this;
        }

        public Criteria andPosAllotDetailIdIsNotNull() {
            addCriterion("pos_allot_detail_id is not null");
            return (Criteria) this;
        }

        public Criteria andPosAllotDetailIdEqualTo(Long value) {
            addCriterion("pos_allot_detail_id =", value, "posAllotDetailId");
            return (Criteria) this;
        }

        public Criteria andPosAllotDetailIdNotEqualTo(Long value) {
            addCriterion("pos_allot_detail_id <>", value, "posAllotDetailId");
            return (Criteria) this;
        }

        public Criteria andPosAllotDetailIdGreaterThan(Long value) {
            addCriterion("pos_allot_detail_id >", value, "posAllotDetailId");
            return (Criteria) this;
        }

        public Criteria andPosAllotDetailIdGreaterThanOrEqualTo(Long value) {
            addCriterion("pos_allot_detail_id >=", value, "posAllotDetailId");
            return (Criteria) this;
        }

        public Criteria andPosAllotDetailIdLessThan(Long value) {
            addCriterion("pos_allot_detail_id <", value, "posAllotDetailId");
            return (Criteria) this;
        }

        public Criteria andPosAllotDetailIdLessThanOrEqualTo(Long value) {
            addCriterion("pos_allot_detail_id <=", value, "posAllotDetailId");
            return (Criteria) this;
        }

        public Criteria andPosAllotDetailIdIn(List<Long> values) {
            addCriterion("pos_allot_detail_id in", values, "posAllotDetailId");
            return (Criteria) this;
        }

        public Criteria andPosAllotDetailIdNotIn(List<Long> values) {
            addCriterion("pos_allot_detail_id not in", values, "posAllotDetailId");
            return (Criteria) this;
        }

        public Criteria andPosAllotDetailIdBetween(Long value1, Long value2) {
            addCriterion("pos_allot_detail_id between", value1, value2, "posAllotDetailId");
            return (Criteria) this;
        }

        public Criteria andPosAllotDetailIdNotBetween(Long value1, Long value2) {
            addCriterion("pos_allot_detail_id not between", value1, value2, "posAllotDetailId");
            return (Criteria) this;
        }

        public Criteria andOutCompanyCodeIsNull() {
            addCriterion("out_company_code is null");
            return (Criteria) this;
        }

        public Criteria andOutCompanyCodeIsNotNull() {
            addCriterion("out_company_code is not null");
            return (Criteria) this;
        }

        public Criteria andOutCompanyCodeEqualTo(String value) {
            addCriterion("out_company_code =", value, "outCompanyCode");
            return (Criteria) this;
        }

        public Criteria andOutCompanyCodeNotEqualTo(String value) {
            addCriterion("out_company_code <>", value, "outCompanyCode");
            return (Criteria) this;
        }

        public Criteria andOutCompanyCodeGreaterThan(String value) {
            addCriterion("out_company_code >", value, "outCompanyCode");
            return (Criteria) this;
        }

        public Criteria andOutCompanyCodeGreaterThanOrEqualTo(String value) {
            addCriterion("out_company_code >=", value, "outCompanyCode");
            return (Criteria) this;
        }

        public Criteria andOutCompanyCodeLessThan(String value) {
            addCriterion("out_company_code <", value, "outCompanyCode");
            return (Criteria) this;
        }

        public Criteria andOutCompanyCodeLessThanOrEqualTo(String value) {
            addCriterion("out_company_code <=", value, "outCompanyCode");
            return (Criteria) this;
        }

        public Criteria andOutCompanyCodeLike(String value) {
            addCriterion("out_company_code like", value, "outCompanyCode");
            return (Criteria) this;
        }

        public Criteria andOutCompanyCodeNotLike(String value) {
            addCriterion("out_company_code not like", value, "outCompanyCode");
            return (Criteria) this;
        }

        public Criteria andOutCompanyCodeIn(List<String> values) {
            addCriterion("out_company_code in", values, "outCompanyCode");
            return (Criteria) this;
        }

        public Criteria andOutCompanyCodeNotIn(List<String> values) {
            addCriterion("out_company_code not in", values, "outCompanyCode");
            return (Criteria) this;
        }

        public Criteria andOutCompanyCodeBetween(String value1, String value2) {
            addCriterion("out_company_code between", value1, value2, "outCompanyCode");
            return (Criteria) this;
        }

        public Criteria andOutCompanyCodeNotBetween(String value1, String value2) {
            addCriterion("out_company_code not between", value1, value2, "outCompanyCode");
            return (Criteria) this;
        }

        public Criteria andInCompanyCodeIsNull() {
            addCriterion("in_company_code is null");
            return (Criteria) this;
        }

        public Criteria andInCompanyCodeIsNotNull() {
            addCriterion("in_company_code is not null");
            return (Criteria) this;
        }

        public Criteria andInCompanyCodeEqualTo(String value) {
            addCriterion("in_company_code =", value, "inCompanyCode");
            return (Criteria) this;
        }

        public Criteria andInCompanyCodeNotEqualTo(String value) {
            addCriterion("in_company_code <>", value, "inCompanyCode");
            return (Criteria) this;
        }

        public Criteria andInCompanyCodeGreaterThan(String value) {
            addCriterion("in_company_code >", value, "inCompanyCode");
            return (Criteria) this;
        }

        public Criteria andInCompanyCodeGreaterThanOrEqualTo(String value) {
            addCriterion("in_company_code >=", value, "inCompanyCode");
            return (Criteria) this;
        }

        public Criteria andInCompanyCodeLessThan(String value) {
            addCriterion("in_company_code <", value, "inCompanyCode");
            return (Criteria) this;
        }

        public Criteria andInCompanyCodeLessThanOrEqualTo(String value) {
            addCriterion("in_company_code <=", value, "inCompanyCode");
            return (Criteria) this;
        }

        public Criteria andInCompanyCodeLike(String value) {
            addCriterion("in_company_code like", value, "inCompanyCode");
            return (Criteria) this;
        }

        public Criteria andInCompanyCodeNotLike(String value) {
            addCriterion("in_company_code not like", value, "inCompanyCode");
            return (Criteria) this;
        }

        public Criteria andInCompanyCodeIn(List<String> values) {
            addCriterion("in_company_code in", values, "inCompanyCode");
            return (Criteria) this;
        }

        public Criteria andInCompanyCodeNotIn(List<String> values) {
            addCriterion("in_company_code not in", values, "inCompanyCode");
            return (Criteria) this;
        }

        public Criteria andInCompanyCodeBetween(String value1, String value2) {
            addCriterion("in_company_code between", value1, value2, "inCompanyCode");
            return (Criteria) this;
        }

        public Criteria andInCompanyCodeNotBetween(String value1, String value2) {
            addCriterion("in_company_code not between", value1, value2, "inCompanyCode");
            return (Criteria) this;
        }

        public Criteria andOutStoreCodeIsNull() {
            addCriterion("out_store_code is null");
            return (Criteria) this;
        }

        public Criteria andOutStoreCodeIsNotNull() {
            addCriterion("out_store_code is not null");
            return (Criteria) this;
        }

        public Criteria andOutStoreCodeEqualTo(String value) {
            addCriterion("out_store_code =", value, "outStoreCode");
            return (Criteria) this;
        }

        public Criteria andOutStoreCodeNotEqualTo(String value) {
            addCriterion("out_store_code <>", value, "outStoreCode");
            return (Criteria) this;
        }

        public Criteria andOutStoreCodeGreaterThan(String value) {
            addCriterion("out_store_code >", value, "outStoreCode");
            return (Criteria) this;
        }

        public Criteria andOutStoreCodeGreaterThanOrEqualTo(String value) {
            addCriterion("out_store_code >=", value, "outStoreCode");
            return (Criteria) this;
        }

        public Criteria andOutStoreCodeLessThan(String value) {
            addCriterion("out_store_code <", value, "outStoreCode");
            return (Criteria) this;
        }

        public Criteria andOutStoreCodeLessThanOrEqualTo(String value) {
            addCriterion("out_store_code <=", value, "outStoreCode");
            return (Criteria) this;
        }

        public Criteria andOutStoreCodeLike(String value) {
            addCriterion("out_store_code like", value, "outStoreCode");
            return (Criteria) this;
        }

        public Criteria andOutStoreCodeNotLike(String value) {
            addCriterion("out_store_code not like", value, "outStoreCode");
            return (Criteria) this;
        }

        public Criteria andOutStoreCodeIn(List<String> values) {
            addCriterion("out_store_code in", values, "outStoreCode");
            return (Criteria) this;
        }

        public Criteria andOutStoreCodeNotIn(List<String> values) {
            addCriterion("out_store_code not in", values, "outStoreCode");
            return (Criteria) this;
        }

        public Criteria andOutStoreCodeBetween(String value1, String value2) {
            addCriterion("out_store_code between", value1, value2, "outStoreCode");
            return (Criteria) this;
        }

        public Criteria andOutStoreCodeNotBetween(String value1, String value2) {
            addCriterion("out_store_code not between", value1, value2, "outStoreCode");
            return (Criteria) this;
        }

        public Criteria andInStoreCodeIsNull() {
            addCriterion("in_store_code is null");
            return (Criteria) this;
        }

        public Criteria andInStoreCodeIsNotNull() {
            addCriterion("in_store_code is not null");
            return (Criteria) this;
        }

        public Criteria andInStoreCodeEqualTo(String value) {
            addCriterion("in_store_code =", value, "inStoreCode");
            return (Criteria) this;
        }

        public Criteria andInStoreCodeNotEqualTo(String value) {
            addCriterion("in_store_code <>", value, "inStoreCode");
            return (Criteria) this;
        }

        public Criteria andInStoreCodeGreaterThan(String value) {
            addCriterion("in_store_code >", value, "inStoreCode");
            return (Criteria) this;
        }

        public Criteria andInStoreCodeGreaterThanOrEqualTo(String value) {
            addCriterion("in_store_code >=", value, "inStoreCode");
            return (Criteria) this;
        }

        public Criteria andInStoreCodeLessThan(String value) {
            addCriterion("in_store_code <", value, "inStoreCode");
            return (Criteria) this;
        }

        public Criteria andInStoreCodeLessThanOrEqualTo(String value) {
            addCriterion("in_store_code <=", value, "inStoreCode");
            return (Criteria) this;
        }

        public Criteria andInStoreCodeLike(String value) {
            addCriterion("in_store_code like", value, "inStoreCode");
            return (Criteria) this;
        }

        public Criteria andInStoreCodeNotLike(String value) {
            addCriterion("in_store_code not like", value, "inStoreCode");
            return (Criteria) this;
        }

        public Criteria andInStoreCodeIn(List<String> values) {
            addCriterion("in_store_code in", values, "inStoreCode");
            return (Criteria) this;
        }

        public Criteria andInStoreCodeNotIn(List<String> values) {
            addCriterion("in_store_code not in", values, "inStoreCode");
            return (Criteria) this;
        }

        public Criteria andInStoreCodeBetween(String value1, String value2) {
            addCriterion("in_store_code between", value1, value2, "inStoreCode");
            return (Criteria) this;
        }

        public Criteria andInStoreCodeNotBetween(String value1, String value2) {
            addCriterion("in_store_code not between", value1, value2, "inStoreCode");
            return (Criteria) this;
        }

        public Criteria andOutApproveStatusIsNull() {
            addCriterion("out_approve_status is null");
            return (Criteria) this;
        }

        public Criteria andOutApproveStatusIsNotNull() {
            addCriterion("out_approve_status is not null");
            return (Criteria) this;
        }

        public Criteria andOutApproveStatusEqualTo(Byte value) {
            addCriterion("out_approve_status =", value, "outApproveStatus");
            return (Criteria) this;
        }

        public Criteria andOutApproveStatusNotEqualTo(Byte value) {
            addCriterion("out_approve_status <>", value, "outApproveStatus");
            return (Criteria) this;
        }

        public Criteria andOutApproveStatusGreaterThan(Byte value) {
            addCriterion("out_approve_status >", value, "outApproveStatus");
            return (Criteria) this;
        }

        public Criteria andOutApproveStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("out_approve_status >=", value, "outApproveStatus");
            return (Criteria) this;
        }

        public Criteria andOutApproveStatusLessThan(Byte value) {
            addCriterion("out_approve_status <", value, "outApproveStatus");
            return (Criteria) this;
        }

        public Criteria andOutApproveStatusLessThanOrEqualTo(Byte value) {
            addCriterion("out_approve_status <=", value, "outApproveStatus");
            return (Criteria) this;
        }

        public Criteria andOutApproveStatusIn(List<Byte> values) {
            addCriterion("out_approve_status in", values, "outApproveStatus");
            return (Criteria) this;
        }

        public Criteria andOutApproveStatusNotIn(List<Byte> values) {
            addCriterion("out_approve_status not in", values, "outApproveStatus");
            return (Criteria) this;
        }

        public Criteria andOutApproveStatusBetween(Byte value1, Byte value2) {
            addCriterion("out_approve_status between", value1, value2, "outApproveStatus");
            return (Criteria) this;
        }

        public Criteria andOutApproveStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("out_approve_status not between", value1, value2, "outApproveStatus");
            return (Criteria) this;
        }

        public Criteria andOutApproveTimeIsNull() {
            addCriterion("out_approve_time is null");
            return (Criteria) this;
        }

        public Criteria andOutApproveTimeIsNotNull() {
            addCriterion("out_approve_time is not null");
            return (Criteria) this;
        }

        public Criteria andOutApproveTimeEqualTo(Date value) {
            addCriterion("out_approve_time =", value, "outApproveTime");
            return (Criteria) this;
        }

        public Criteria andOutApproveTimeNotEqualTo(Date value) {
            addCriterion("out_approve_time <>", value, "outApproveTime");
            return (Criteria) this;
        }

        public Criteria andOutApproveTimeGreaterThan(Date value) {
            addCriterion("out_approve_time >", value, "outApproveTime");
            return (Criteria) this;
        }

        public Criteria andOutApproveTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("out_approve_time >=", value, "outApproveTime");
            return (Criteria) this;
        }

        public Criteria andOutApproveTimeLessThan(Date value) {
            addCriterion("out_approve_time <", value, "outApproveTime");
            return (Criteria) this;
        }

        public Criteria andOutApproveTimeLessThanOrEqualTo(Date value) {
            addCriterion("out_approve_time <=", value, "outApproveTime");
            return (Criteria) this;
        }

        public Criteria andOutApproveTimeIn(List<Date> values) {
            addCriterion("out_approve_time in", values, "outApproveTime");
            return (Criteria) this;
        }

        public Criteria andOutApproveTimeNotIn(List<Date> values) {
            addCriterion("out_approve_time not in", values, "outApproveTime");
            return (Criteria) this;
        }

        public Criteria andOutApproveTimeBetween(Date value1, Date value2) {
            addCriterion("out_approve_time between", value1, value2, "outApproveTime");
            return (Criteria) this;
        }

        public Criteria andOutApproveTimeNotBetween(Date value1, Date value2) {
            addCriterion("out_approve_time not between", value1, value2, "outApproveTime");
            return (Criteria) this;
        }

        public Criteria andOutAllotQuantityIsNull() {
            addCriterion("out_allot_quantity is null");
            return (Criteria) this;
        }

        public Criteria andOutAllotQuantityIsNotNull() {
            addCriterion("out_allot_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andOutAllotQuantityEqualTo(BigDecimal value) {
            addCriterion("out_allot_quantity =", value, "outAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andOutAllotQuantityNotEqualTo(BigDecimal value) {
            addCriterion("out_allot_quantity <>", value, "outAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andOutAllotQuantityGreaterThan(BigDecimal value) {
            addCriterion("out_allot_quantity >", value, "outAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andOutAllotQuantityGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("out_allot_quantity >=", value, "outAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andOutAllotQuantityLessThan(BigDecimal value) {
            addCriterion("out_allot_quantity <", value, "outAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andOutAllotQuantityLessThanOrEqualTo(BigDecimal value) {
            addCriterion("out_allot_quantity <=", value, "outAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andOutAllotQuantityIn(List<BigDecimal> values) {
            addCriterion("out_allot_quantity in", values, "outAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andOutAllotQuantityNotIn(List<BigDecimal> values) {
            addCriterion("out_allot_quantity not in", values, "outAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andOutAllotQuantityBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("out_allot_quantity between", value1, value2, "outAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andOutAllotQuantityNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("out_allot_quantity not between", value1, value2, "outAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andInApproveStatusIsNull() {
            addCriterion("in_approve_status is null");
            return (Criteria) this;
        }

        public Criteria andInApproveStatusIsNotNull() {
            addCriterion("in_approve_status is not null");
            return (Criteria) this;
        }

        public Criteria andInApproveStatusEqualTo(Byte value) {
            addCriterion("in_approve_status =", value, "inApproveStatus");
            return (Criteria) this;
        }

        public Criteria andInApproveStatusNotEqualTo(Byte value) {
            addCriterion("in_approve_status <>", value, "inApproveStatus");
            return (Criteria) this;
        }

        public Criteria andInApproveStatusGreaterThan(Byte value) {
            addCriterion("in_approve_status >", value, "inApproveStatus");
            return (Criteria) this;
        }

        public Criteria andInApproveStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("in_approve_status >=", value, "inApproveStatus");
            return (Criteria) this;
        }

        public Criteria andInApproveStatusLessThan(Byte value) {
            addCriterion("in_approve_status <", value, "inApproveStatus");
            return (Criteria) this;
        }

        public Criteria andInApproveStatusLessThanOrEqualTo(Byte value) {
            addCriterion("in_approve_status <=", value, "inApproveStatus");
            return (Criteria) this;
        }

        public Criteria andInApproveStatusIn(List<Byte> values) {
            addCriterion("in_approve_status in", values, "inApproveStatus");
            return (Criteria) this;
        }

        public Criteria andInApproveStatusNotIn(List<Byte> values) {
            addCriterion("in_approve_status not in", values, "inApproveStatus");
            return (Criteria) this;
        }

        public Criteria andInApproveStatusBetween(Byte value1, Byte value2) {
            addCriterion("in_approve_status between", value1, value2, "inApproveStatus");
            return (Criteria) this;
        }

        public Criteria andInApproveStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("in_approve_status not between", value1, value2, "inApproveStatus");
            return (Criteria) this;
        }

        public Criteria andInApproveTimeIsNull() {
            addCriterion("in_approve_time is null");
            return (Criteria) this;
        }

        public Criteria andInApproveTimeIsNotNull() {
            addCriterion("in_approve_time is not null");
            return (Criteria) this;
        }

        public Criteria andInApproveTimeEqualTo(Date value) {
            addCriterion("in_approve_time =", value, "inApproveTime");
            return (Criteria) this;
        }

        public Criteria andInApproveTimeNotEqualTo(Date value) {
            addCriterion("in_approve_time <>", value, "inApproveTime");
            return (Criteria) this;
        }

        public Criteria andInApproveTimeGreaterThan(Date value) {
            addCriterion("in_approve_time >", value, "inApproveTime");
            return (Criteria) this;
        }

        public Criteria andInApproveTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("in_approve_time >=", value, "inApproveTime");
            return (Criteria) this;
        }

        public Criteria andInApproveTimeLessThan(Date value) {
            addCriterion("in_approve_time <", value, "inApproveTime");
            return (Criteria) this;
        }

        public Criteria andInApproveTimeLessThanOrEqualTo(Date value) {
            addCriterion("in_approve_time <=", value, "inApproveTime");
            return (Criteria) this;
        }

        public Criteria andInApproveTimeIn(List<Date> values) {
            addCriterion("in_approve_time in", values, "inApproveTime");
            return (Criteria) this;
        }

        public Criteria andInApproveTimeNotIn(List<Date> values) {
            addCriterion("in_approve_time not in", values, "inApproveTime");
            return (Criteria) this;
        }

        public Criteria andInApproveTimeBetween(Date value1, Date value2) {
            addCriterion("in_approve_time between", value1, value2, "inApproveTime");
            return (Criteria) this;
        }

        public Criteria andInApproveTimeNotBetween(Date value1, Date value2) {
            addCriterion("in_approve_time not between", value1, value2, "inApproveTime");
            return (Criteria) this;
        }

        public Criteria andInAllotQuantityIsNull() {
            addCriterion("in_allot_quantity is null");
            return (Criteria) this;
        }

        public Criteria andInAllotQuantityIsNotNull() {
            addCriterion("in_allot_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andInAllotQuantityEqualTo(BigDecimal value) {
            addCriterion("in_allot_quantity =", value, "inAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andInAllotQuantityNotEqualTo(BigDecimal value) {
            addCriterion("in_allot_quantity <>", value, "inAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andInAllotQuantityGreaterThan(BigDecimal value) {
            addCriterion("in_allot_quantity >", value, "inAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andInAllotQuantityGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("in_allot_quantity >=", value, "inAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andInAllotQuantityLessThan(BigDecimal value) {
            addCriterion("in_allot_quantity <", value, "inAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andInAllotQuantityLessThanOrEqualTo(BigDecimal value) {
            addCriterion("in_allot_quantity <=", value, "inAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andInAllotQuantityIn(List<BigDecimal> values) {
            addCriterion("in_allot_quantity in", values, "inAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andInAllotQuantityNotIn(List<BigDecimal> values) {
            addCriterion("in_allot_quantity not in", values, "inAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andInAllotQuantityBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("in_allot_quantity between", value1, value2, "inAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andInAllotQuantityNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("in_allot_quantity not between", value1, value2, "inAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andInStockTimeIsNull() {
            addCriterion("in_stock_time is null");
            return (Criteria) this;
        }

        public Criteria andInStockTimeIsNotNull() {
            addCriterion("in_stock_time is not null");
            return (Criteria) this;
        }

        public Criteria andInStockTimeEqualTo(Date value) {
            addCriterion("in_stock_time =", value, "inStockTime");
            return (Criteria) this;
        }

        public Criteria andInStockTimeNotEqualTo(Date value) {
            addCriterion("in_stock_time <>", value, "inStockTime");
            return (Criteria) this;
        }

        public Criteria andInStockTimeGreaterThan(Date value) {
            addCriterion("in_stock_time >", value, "inStockTime");
            return (Criteria) this;
        }

        public Criteria andInStockTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("in_stock_time >=", value, "inStockTime");
            return (Criteria) this;
        }

        public Criteria andInStockTimeLessThan(Date value) {
            addCriterion("in_stock_time <", value, "inStockTime");
            return (Criteria) this;
        }

        public Criteria andInStockTimeLessThanOrEqualTo(Date value) {
            addCriterion("in_stock_time <=", value, "inStockTime");
            return (Criteria) this;
        }

        public Criteria andInStockTimeIn(List<Date> values) {
            addCriterion("in_stock_time in", values, "inStockTime");
            return (Criteria) this;
        }

        public Criteria andInStockTimeNotIn(List<Date> values) {
            addCriterion("in_stock_time not in", values, "inStockTime");
            return (Criteria) this;
        }

        public Criteria andInStockTimeBetween(Date value1, Date value2) {
            addCriterion("in_stock_time between", value1, value2, "inStockTime");
            return (Criteria) this;
        }

        public Criteria andInStockTimeNotBetween(Date value1, Date value2) {
            addCriterion("in_stock_time not between", value1, value2, "inStockTime");
            return (Criteria) this;
        }

        public Criteria andAllotQuantityIsNull() {
            addCriterion("allot_quantity is null");
            return (Criteria) this;
        }

        public Criteria andAllotQuantityIsNotNull() {
            addCriterion("allot_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andAllotQuantityEqualTo(BigDecimal value) {
            addCriterion("allot_quantity =", value, "allotQuantity");
            return (Criteria) this;
        }

        public Criteria andAllotQuantityNotEqualTo(BigDecimal value) {
            addCriterion("allot_quantity <>", value, "allotQuantity");
            return (Criteria) this;
        }

        public Criteria andAllotQuantityGreaterThan(BigDecimal value) {
            addCriterion("allot_quantity >", value, "allotQuantity");
            return (Criteria) this;
        }

        public Criteria andAllotQuantityGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("allot_quantity >=", value, "allotQuantity");
            return (Criteria) this;
        }

        public Criteria andAllotQuantityLessThan(BigDecimal value) {
            addCriterion("allot_quantity <", value, "allotQuantity");
            return (Criteria) this;
        }

        public Criteria andAllotQuantityLessThanOrEqualTo(BigDecimal value) {
            addCriterion("allot_quantity <=", value, "allotQuantity");
            return (Criteria) this;
        }

        public Criteria andAllotQuantityIn(List<BigDecimal> values) {
            addCriterion("allot_quantity in", values, "allotQuantity");
            return (Criteria) this;
        }

        public Criteria andAllotQuantityNotIn(List<BigDecimal> values) {
            addCriterion("allot_quantity not in", values, "allotQuantity");
            return (Criteria) this;
        }

        public Criteria andAllotQuantityBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("allot_quantity between", value1, value2, "allotQuantity");
            return (Criteria) this;
        }

        public Criteria andAllotQuantityNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("allot_quantity not between", value1, value2, "allotQuantity");
            return (Criteria) this;
        }

        public Criteria andGoodsNoIsNull() {
            addCriterion("goods_no is null");
            return (Criteria) this;
        }

        public Criteria andGoodsNoIsNotNull() {
            addCriterion("goods_no is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsNoEqualTo(String value) {
            addCriterion("goods_no =", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotEqualTo(String value) {
            addCriterion("goods_no <>", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoGreaterThan(String value) {
            addCriterion("goods_no >", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoGreaterThanOrEqualTo(String value) {
            addCriterion("goods_no >=", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoLessThan(String value) {
            addCriterion("goods_no <", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoLessThanOrEqualTo(String value) {
            addCriterion("goods_no <=", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoLike(String value) {
            addCriterion("goods_no like", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotLike(String value) {
            addCriterion("goods_no not like", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoIn(List<String> values) {
            addCriterion("goods_no in", values, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotIn(List<String> values) {
            addCriterion("goods_no not in", values, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoBetween(String value1, String value2) {
            addCriterion("goods_no between", value1, value2, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotBetween(String value1, String value2) {
            addCriterion("goods_no not between", value1, value2, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoIsNull() {
            addCriterion("batch_no is null");
            return (Criteria) this;
        }

        public Criteria andBatchNoIsNotNull() {
            addCriterion("batch_no is not null");
            return (Criteria) this;
        }

        public Criteria andBatchNoEqualTo(String value) {
            addCriterion("batch_no =", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoNotEqualTo(String value) {
            addCriterion("batch_no <>", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoGreaterThan(String value) {
            addCriterion("batch_no >", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoGreaterThanOrEqualTo(String value) {
            addCriterion("batch_no >=", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoLessThan(String value) {
            addCriterion("batch_no <", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoLessThanOrEqualTo(String value) {
            addCriterion("batch_no <=", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoLike(String value) {
            addCriterion("batch_no like", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoNotLike(String value) {
            addCriterion("batch_no not like", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoIn(List<String> values) {
            addCriterion("batch_no in", values, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoNotIn(List<String> values) {
            addCriterion("batch_no not in", values, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoBetween(String value1, String value2) {
            addCriterion("batch_no between", value1, value2, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoNotBetween(String value1, String value2) {
            addCriterion("batch_no not between", value1, value2, "batchNo");
            return (Criteria) this;
        }

        public Criteria andProduceDateIsNull() {
            addCriterion("produce_date is null");
            return (Criteria) this;
        }

        public Criteria andProduceDateIsNotNull() {
            addCriterion("produce_date is not null");
            return (Criteria) this;
        }

        public Criteria andProduceDateEqualTo(String value) {
            addCriterion("produce_date =", value, "produceDate");
            return (Criteria) this;
        }

        public Criteria andProduceDateNotEqualTo(String value) {
            addCriterion("produce_date <>", value, "produceDate");
            return (Criteria) this;
        }

        public Criteria andProduceDateGreaterThan(String value) {
            addCriterion("produce_date >", value, "produceDate");
            return (Criteria) this;
        }

        public Criteria andProduceDateGreaterThanOrEqualTo(String value) {
            addCriterion("produce_date >=", value, "produceDate");
            return (Criteria) this;
        }

        public Criteria andProduceDateLessThan(String value) {
            addCriterion("produce_date <", value, "produceDate");
            return (Criteria) this;
        }

        public Criteria andProduceDateLessThanOrEqualTo(String value) {
            addCriterion("produce_date <=", value, "produceDate");
            return (Criteria) this;
        }

        public Criteria andProduceDateLike(String value) {
            addCriterion("produce_date like", value, "produceDate");
            return (Criteria) this;
        }

        public Criteria andProduceDateNotLike(String value) {
            addCriterion("produce_date not like", value, "produceDate");
            return (Criteria) this;
        }

        public Criteria andProduceDateIn(List<String> values) {
            addCriterion("produce_date in", values, "produceDate");
            return (Criteria) this;
        }

        public Criteria andProduceDateNotIn(List<String> values) {
            addCriterion("produce_date not in", values, "produceDate");
            return (Criteria) this;
        }

        public Criteria andProduceDateBetween(String value1, String value2) {
            addCriterion("produce_date between", value1, value2, "produceDate");
            return (Criteria) this;
        }

        public Criteria andProduceDateNotBetween(String value1, String value2) {
            addCriterion("produce_date not between", value1, value2, "produceDate");
            return (Criteria) this;
        }

        public Criteria andValidityDateIsNull() {
            addCriterion("validity_date is null");
            return (Criteria) this;
        }

        public Criteria andValidityDateIsNotNull() {
            addCriterion("validity_date is not null");
            return (Criteria) this;
        }

        public Criteria andValidityDateEqualTo(String value) {
            addCriterion("validity_date =", value, "validityDate");
            return (Criteria) this;
        }

        public Criteria andValidityDateNotEqualTo(String value) {
            addCriterion("validity_date <>", value, "validityDate");
            return (Criteria) this;
        }

        public Criteria andValidityDateGreaterThan(String value) {
            addCriterion("validity_date >", value, "validityDate");
            return (Criteria) this;
        }

        public Criteria andValidityDateGreaterThanOrEqualTo(String value) {
            addCriterion("validity_date >=", value, "validityDate");
            return (Criteria) this;
        }

        public Criteria andValidityDateLessThan(String value) {
            addCriterion("validity_date <", value, "validityDate");
            return (Criteria) this;
        }

        public Criteria andValidityDateLessThanOrEqualTo(String value) {
            addCriterion("validity_date <=", value, "validityDate");
            return (Criteria) this;
        }

        public Criteria andValidityDateLike(String value) {
            addCriterion("validity_date like", value, "validityDate");
            return (Criteria) this;
        }

        public Criteria andValidityDateNotLike(String value) {
            addCriterion("validity_date not like", value, "validityDate");
            return (Criteria) this;
        }

        public Criteria andValidityDateIn(List<String> values) {
            addCriterion("validity_date in", values, "validityDate");
            return (Criteria) this;
        }

        public Criteria andValidityDateNotIn(List<String> values) {
            addCriterion("validity_date not in", values, "validityDate");
            return (Criteria) this;
        }

        public Criteria andValidityDateBetween(String value1, String value2) {
            addCriterion("validity_date between", value1, value2, "validityDate");
            return (Criteria) this;
        }

        public Criteria andValidityDateNotBetween(String value1, String value2) {
            addCriterion("validity_date not between", value1, value2, "validityDate");
            return (Criteria) this;
        }

        public Criteria andHdBatchNoIsNull() {
            addCriterion("hd_batch_no is null");
            return (Criteria) this;
        }

        public Criteria andHdBatchNoIsNotNull() {
            addCriterion("hd_batch_no is not null");
            return (Criteria) this;
        }

        public Criteria andHdBatchNoEqualTo(String value) {
            addCriterion("hd_batch_no =", value, "hdBatchNo");
            return (Criteria) this;
        }

        public Criteria andHdBatchNoNotEqualTo(String value) {
            addCriterion("hd_batch_no <>", value, "hdBatchNo");
            return (Criteria) this;
        }

        public Criteria andHdBatchNoGreaterThan(String value) {
            addCriterion("hd_batch_no >", value, "hdBatchNo");
            return (Criteria) this;
        }

        public Criteria andHdBatchNoGreaterThanOrEqualTo(String value) {
            addCriterion("hd_batch_no >=", value, "hdBatchNo");
            return (Criteria) this;
        }

        public Criteria andHdBatchNoLessThan(String value) {
            addCriterion("hd_batch_no <", value, "hdBatchNo");
            return (Criteria) this;
        }

        public Criteria andHdBatchNoLessThanOrEqualTo(String value) {
            addCriterion("hd_batch_no <=", value, "hdBatchNo");
            return (Criteria) this;
        }

        public Criteria andHdBatchNoLike(String value) {
            addCriterion("hd_batch_no like", value, "hdBatchNo");
            return (Criteria) this;
        }

        public Criteria andHdBatchNoNotLike(String value) {
            addCriterion("hd_batch_no not like", value, "hdBatchNo");
            return (Criteria) this;
        }

        public Criteria andHdBatchNoIn(List<String> values) {
            addCriterion("hd_batch_no in", values, "hdBatchNo");
            return (Criteria) this;
        }

        public Criteria andHdBatchNoNotIn(List<String> values) {
            addCriterion("hd_batch_no not in", values, "hdBatchNo");
            return (Criteria) this;
        }

        public Criteria andHdBatchNoBetween(String value1, String value2) {
            addCriterion("hd_batch_no between", value1, value2, "hdBatchNo");
            return (Criteria) this;
        }

        public Criteria andHdBatchNoNotBetween(String value1, String value2) {
            addCriterion("hd_batch_no not between", value1, value2, "hdBatchNo");
            return (Criteria) this;
        }

        public Criteria andSapBatchNoIsNull() {
            addCriterion("sap_batch_no is null");
            return (Criteria) this;
        }

        public Criteria andSapBatchNoIsNotNull() {
            addCriterion("sap_batch_no is not null");
            return (Criteria) this;
        }

        public Criteria andSapBatchNoEqualTo(String value) {
            addCriterion("sap_batch_no =", value, "sapBatchNo");
            return (Criteria) this;
        }

        public Criteria andSapBatchNoNotEqualTo(String value) {
            addCriterion("sap_batch_no <>", value, "sapBatchNo");
            return (Criteria) this;
        }

        public Criteria andSapBatchNoGreaterThan(String value) {
            addCriterion("sap_batch_no >", value, "sapBatchNo");
            return (Criteria) this;
        }

        public Criteria andSapBatchNoGreaterThanOrEqualTo(String value) {
            addCriterion("sap_batch_no >=", value, "sapBatchNo");
            return (Criteria) this;
        }

        public Criteria andSapBatchNoLessThan(String value) {
            addCriterion("sap_batch_no <", value, "sapBatchNo");
            return (Criteria) this;
        }

        public Criteria andSapBatchNoLessThanOrEqualTo(String value) {
            addCriterion("sap_batch_no <=", value, "sapBatchNo");
            return (Criteria) this;
        }

        public Criteria andSapBatchNoLike(String value) {
            addCriterion("sap_batch_no like", value, "sapBatchNo");
            return (Criteria) this;
        }

        public Criteria andSapBatchNoNotLike(String value) {
            addCriterion("sap_batch_no not like", value, "sapBatchNo");
            return (Criteria) this;
        }

        public Criteria andSapBatchNoIn(List<String> values) {
            addCriterion("sap_batch_no in", values, "sapBatchNo");
            return (Criteria) this;
        }

        public Criteria andSapBatchNoNotIn(List<String> values) {
            addCriterion("sap_batch_no not in", values, "sapBatchNo");
            return (Criteria) this;
        }

        public Criteria andSapBatchNoBetween(String value1, String value2) {
            addCriterion("sap_batch_no between", value1, value2, "sapBatchNo");
            return (Criteria) this;
        }

        public Criteria andSapBatchNoNotBetween(String value1, String value2) {
            addCriterion("sap_batch_no not between", value1, value2, "sapBatchNo");
            return (Criteria) this;
        }

        public Criteria andNotesIsNull() {
            addCriterion("notes is null");
            return (Criteria) this;
        }

        public Criteria andNotesIsNotNull() {
            addCriterion("notes is not null");
            return (Criteria) this;
        }

        public Criteria andNotesEqualTo(String value) {
            addCriterion("notes =", value, "notes");
            return (Criteria) this;
        }

        public Criteria andNotesNotEqualTo(String value) {
            addCriterion("notes <>", value, "notes");
            return (Criteria) this;
        }

        public Criteria andNotesGreaterThan(String value) {
            addCriterion("notes >", value, "notes");
            return (Criteria) this;
        }

        public Criteria andNotesGreaterThanOrEqualTo(String value) {
            addCriterion("notes >=", value, "notes");
            return (Criteria) this;
        }

        public Criteria andNotesLessThan(String value) {
            addCriterion("notes <", value, "notes");
            return (Criteria) this;
        }

        public Criteria andNotesLessThanOrEqualTo(String value) {
            addCriterion("notes <=", value, "notes");
            return (Criteria) this;
        }

        public Criteria andNotesLike(String value) {
            addCriterion("notes like", value, "notes");
            return (Criteria) this;
        }

        public Criteria andNotesNotLike(String value) {
            addCriterion("notes not like", value, "notes");
            return (Criteria) this;
        }

        public Criteria andNotesIn(List<String> values) {
            addCriterion("notes in", values, "notes");
            return (Criteria) this;
        }

        public Criteria andNotesNotIn(List<String> values) {
            addCriterion("notes not in", values, "notes");
            return (Criteria) this;
        }

        public Criteria andNotesBetween(String value1, String value2) {
            addCriterion("notes between", value1, value2, "notes");
            return (Criteria) this;
        }

        public Criteria andNotesNotBetween(String value1, String value2) {
            addCriterion("notes not between", value1, value2, "notes");
            return (Criteria) this;
        }

        public Criteria andVoidReasonIsNull() {
            addCriterion("void_reason is null");
            return (Criteria) this;
        }

        public Criteria andVoidReasonIsNotNull() {
            addCriterion("void_reason is not null");
            return (Criteria) this;
        }

        public Criteria andVoidReasonEqualTo(String value) {
            addCriterion("void_reason =", value, "voidReason");
            return (Criteria) this;
        }

        public Criteria andVoidReasonNotEqualTo(String value) {
            addCriterion("void_reason <>", value, "voidReason");
            return (Criteria) this;
        }

        public Criteria andVoidReasonGreaterThan(String value) {
            addCriterion("void_reason >", value, "voidReason");
            return (Criteria) this;
        }

        public Criteria andVoidReasonGreaterThanOrEqualTo(String value) {
            addCriterion("void_reason >=", value, "voidReason");
            return (Criteria) this;
        }

        public Criteria andVoidReasonLessThan(String value) {
            addCriterion("void_reason <", value, "voidReason");
            return (Criteria) this;
        }

        public Criteria andVoidReasonLessThanOrEqualTo(String value) {
            addCriterion("void_reason <=", value, "voidReason");
            return (Criteria) this;
        }

        public Criteria andVoidReasonLike(String value) {
            addCriterion("void_reason like", value, "voidReason");
            return (Criteria) this;
        }

        public Criteria andVoidReasonNotLike(String value) {
            addCriterion("void_reason not like", value, "voidReason");
            return (Criteria) this;
        }

        public Criteria andVoidReasonIn(List<String> values) {
            addCriterion("void_reason in", values, "voidReason");
            return (Criteria) this;
        }

        public Criteria andVoidReasonNotIn(List<String> values) {
            addCriterion("void_reason not in", values, "voidReason");
            return (Criteria) this;
        }

        public Criteria andVoidReasonBetween(String value1, String value2) {
            addCriterion("void_reason between", value1, value2, "voidReason");
            return (Criteria) this;
        }

        public Criteria andVoidReasonNotBetween(String value1, String value2) {
            addCriterion("void_reason not between", value1, value2, "voidReason");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Byte value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Byte value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Byte value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Byte value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Byte value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Byte> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Byte> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Byte value1, Byte value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIsNull() {
            addCriterion("gmt_update is null");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIsNotNull() {
            addCriterion("gmt_update is not null");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateEqualTo(Date value) {
            addCriterion("gmt_update =", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotEqualTo(Date value) {
            addCriterion("gmt_update <>", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateGreaterThan(Date value) {
            addCriterion("gmt_update >", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_update >=", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateLessThan(Date value) {
            addCriterion("gmt_update <", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_update <=", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIn(List<Date> values) {
            addCriterion("gmt_update in", values, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotIn(List<Date> values) {
            addCriterion("gmt_update not in", values, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateBetween(Date value1, Date value2) {
            addCriterion("gmt_update between", value1, value2, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_update not between", value1, value2, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andExtendIsNull() {
            addCriterion("extend is null");
            return (Criteria) this;
        }

        public Criteria andExtendIsNotNull() {
            addCriterion("extend is not null");
            return (Criteria) this;
        }

        public Criteria andExtendEqualTo(String value) {
            addCriterion("extend =", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotEqualTo(String value) {
            addCriterion("extend <>", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendGreaterThan(String value) {
            addCriterion("extend >", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendGreaterThanOrEqualTo(String value) {
            addCriterion("extend >=", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLessThan(String value) {
            addCriterion("extend <", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLessThanOrEqualTo(String value) {
            addCriterion("extend <=", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLike(String value) {
            addCriterion("extend like", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotLike(String value) {
            addCriterion("extend not like", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendIn(List<String> values) {
            addCriterion("extend in", values, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotIn(List<String> values) {
            addCriterion("extend not in", values, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendBetween(String value1, String value2) {
            addCriterion("extend between", value1, value2, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotBetween(String value1, String value2) {
            addCriterion("extend not between", value1, value2, "extend");
            return (Criteria) this;
        }

        public Criteria andVersionIsNull() {
            addCriterion("version is null");
            return (Criteria) this;
        }

        public Criteria andVersionIsNotNull() {
            addCriterion("version is not null");
            return (Criteria) this;
        }

        public Criteria andVersionEqualTo(Integer value) {
            addCriterion("version =", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotEqualTo(Integer value) {
            addCriterion("version <>", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThan(Integer value) {
            addCriterion("version >", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThanOrEqualTo(Integer value) {
            addCriterion("version >=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThan(Integer value) {
            addCriterion("version <", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThanOrEqualTo(Integer value) {
            addCriterion("version <=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionIn(List<Integer> values) {
            addCriterion("version in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotIn(List<Integer> values) {
            addCriterion("version not in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionBetween(Integer value1, Integer value2) {
            addCriterion("version between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotBetween(Integer value1, Integer value2) {
            addCriterion("version not between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("created_by is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("created_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(Long value) {
            addCriterion("created_by =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(Long value) {
            addCriterion("created_by <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(Long value) {
            addCriterion("created_by >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(Long value) {
            addCriterion("created_by >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(Long value) {
            addCriterion("created_by <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(Long value) {
            addCriterion("created_by <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<Long> values) {
            addCriterion("created_by in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<Long> values) {
            addCriterion("created_by not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(Long value1, Long value2) {
            addCriterion("created_by between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(Long value1, Long value2) {
            addCriterion("created_by not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedNameIsNull() {
            addCriterion("created_name is null");
            return (Criteria) this;
        }

        public Criteria andCreatedNameIsNotNull() {
            addCriterion("created_name is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedNameEqualTo(String value) {
            addCriterion("created_name =", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotEqualTo(String value) {
            addCriterion("created_name <>", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameGreaterThan(String value) {
            addCriterion("created_name >", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameGreaterThanOrEqualTo(String value) {
            addCriterion("created_name >=", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameLessThan(String value) {
            addCriterion("created_name <", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameLessThanOrEqualTo(String value) {
            addCriterion("created_name <=", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameLike(String value) {
            addCriterion("created_name like", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotLike(String value) {
            addCriterion("created_name not like", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameIn(List<String> values) {
            addCriterion("created_name in", values, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotIn(List<String> values) {
            addCriterion("created_name not in", values, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameBetween(String value1, String value2) {
            addCriterion("created_name between", value1, value2, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotBetween(String value1, String value2) {
            addCriterion("created_name not between", value1, value2, "createdName");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNull() {
            addCriterion("updated_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNotNull() {
            addCriterion("updated_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByEqualTo(Long value) {
            addCriterion("updated_by =", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotEqualTo(Long value) {
            addCriterion("updated_by <>", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThan(Long value) {
            addCriterion("updated_by >", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThanOrEqualTo(Long value) {
            addCriterion("updated_by >=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThan(Long value) {
            addCriterion("updated_by <", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThanOrEqualTo(Long value) {
            addCriterion("updated_by <=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIn(List<Long> values) {
            addCriterion("updated_by in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotIn(List<Long> values) {
            addCriterion("updated_by not in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByBetween(Long value1, Long value2) {
            addCriterion("updated_by between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotBetween(Long value1, Long value2) {
            addCriterion("updated_by not between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameIsNull() {
            addCriterion("updated_name is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameIsNotNull() {
            addCriterion("updated_name is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameEqualTo(String value) {
            addCriterion("updated_name =", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotEqualTo(String value) {
            addCriterion("updated_name <>", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameGreaterThan(String value) {
            addCriterion("updated_name >", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameGreaterThanOrEqualTo(String value) {
            addCriterion("updated_name >=", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameLessThan(String value) {
            addCriterion("updated_name <", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameLessThanOrEqualTo(String value) {
            addCriterion("updated_name <=", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameLike(String value) {
            addCriterion("updated_name like", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotLike(String value) {
            addCriterion("updated_name not like", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameIn(List<String> values) {
            addCriterion("updated_name in", values, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotIn(List<String> values) {
            addCriterion("updated_name not in", values, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameBetween(String value1, String value2) {
            addCriterion("updated_name between", value1, value2, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotBetween(String value1, String value2) {
            addCriterion("updated_name not between", value1, value2, "updatedName");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}