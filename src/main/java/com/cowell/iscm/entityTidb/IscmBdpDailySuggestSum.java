package com.cowell.iscm.entityTidb;

import java.io.Serializable;

/**
 * iscm_bdp_daily_suggest_sum
 * <AUTHOR>
public class IscmBdpDailySuggestSum implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 平台名称
     */
    private String platformName;

    /**
     * 公司编码
     */
    private String companyCode;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * bdp接口表下发行数
     */
    private Integer bdpInterfaceCount;

    /**
     * pos接口表接收行数
     */
    private Integer posInterfaceCount;

    /**
     * pos业务表接收行数
     */
    private Integer posBusinessCount;

    /**
     * bdp-pos接口表差异行数
     */
    private Integer bdpPosInterfaceDiffCount;

    /**
     * pos接口表-pos业务表差异行数
     */
    private Integer posInterfaceBusinessDiffCount;

    /**
     * 总差异行数
     */
    private Integer diffCount;

    /**
     * 时间分区 yyyyMMdd
     */
    private String dt;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getPlatformName() {
        return platformName;
    }

    public void setPlatformName(String platformName) {
        this.platformName = platformName;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public Integer getBdpInterfaceCount() {
        return bdpInterfaceCount;
    }

    public void setBdpInterfaceCount(Integer bdpInterfaceCount) {
        this.bdpInterfaceCount = bdpInterfaceCount;
    }

    public Integer getPosInterfaceCount() {
        return posInterfaceCount;
    }

    public void setPosInterfaceCount(Integer posInterfaceCount) {
        this.posInterfaceCount = posInterfaceCount;
    }

    public Integer getPosBusinessCount() {
        return posBusinessCount;
    }

    public void setPosBusinessCount(Integer posBusinessCount) {
        this.posBusinessCount = posBusinessCount;
    }

    public Integer getBdpPosInterfaceDiffCount() {
        return bdpPosInterfaceDiffCount;
    }

    public void setBdpPosInterfaceDiffCount(Integer bdpPosInterfaceDiffCount) {
        this.bdpPosInterfaceDiffCount = bdpPosInterfaceDiffCount;
    }

    public Integer getPosInterfaceBusinessDiffCount() {
        return posInterfaceBusinessDiffCount;
    }

    public void setPosInterfaceBusinessDiffCount(Integer posInterfaceBusinessDiffCount) {
        this.posInterfaceBusinessDiffCount = posInterfaceBusinessDiffCount;
    }

    public Integer getDiffCount() {
        return diffCount;
    }

    public void setDiffCount(Integer diffCount) {
        this.diffCount = diffCount;
    }

    public String getDt() {
        return dt;
    }

    public void setDt(String dt) {
        this.dt = dt;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        IscmBdpDailySuggestSum other = (IscmBdpDailySuggestSum) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getPlatformName() == null ? other.getPlatformName() == null : this.getPlatformName().equals(other.getPlatformName()))
            && (this.getCompanyCode() == null ? other.getCompanyCode() == null : this.getCompanyCode().equals(other.getCompanyCode()))
            && (this.getCompanyName() == null ? other.getCompanyName() == null : this.getCompanyName().equals(other.getCompanyName()))
            && (this.getBdpInterfaceCount() == null ? other.getBdpInterfaceCount() == null : this.getBdpInterfaceCount().equals(other.getBdpInterfaceCount()))
            && (this.getPosInterfaceCount() == null ? other.getPosInterfaceCount() == null : this.getPosInterfaceCount().equals(other.getPosInterfaceCount()))
            && (this.getPosBusinessCount() == null ? other.getPosBusinessCount() == null : this.getPosBusinessCount().equals(other.getPosBusinessCount()))
            && (this.getBdpPosInterfaceDiffCount() == null ? other.getBdpPosInterfaceDiffCount() == null : this.getBdpPosInterfaceDiffCount().equals(other.getBdpPosInterfaceDiffCount()))
            && (this.getPosInterfaceBusinessDiffCount() == null ? other.getPosInterfaceBusinessDiffCount() == null : this.getPosInterfaceBusinessDiffCount().equals(other.getPosInterfaceBusinessDiffCount()))
            && (this.getDiffCount() == null ? other.getDiffCount() == null : this.getDiffCount().equals(other.getDiffCount()))
            && (this.getDt() == null ? other.getDt() == null : this.getDt().equals(other.getDt()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getPlatformName() == null) ? 0 : getPlatformName().hashCode());
        result = prime * result + ((getCompanyCode() == null) ? 0 : getCompanyCode().hashCode());
        result = prime * result + ((getCompanyName() == null) ? 0 : getCompanyName().hashCode());
        result = prime * result + ((getBdpInterfaceCount() == null) ? 0 : getBdpInterfaceCount().hashCode());
        result = prime * result + ((getPosInterfaceCount() == null) ? 0 : getPosInterfaceCount().hashCode());
        result = prime * result + ((getPosBusinessCount() == null) ? 0 : getPosBusinessCount().hashCode());
        result = prime * result + ((getBdpPosInterfaceDiffCount() == null) ? 0 : getBdpPosInterfaceDiffCount().hashCode());
        result = prime * result + ((getPosInterfaceBusinessDiffCount() == null) ? 0 : getPosInterfaceBusinessDiffCount().hashCode());
        result = prime * result + ((getDiffCount() == null) ? 0 : getDiffCount().hashCode());
        result = prime * result + ((getDt() == null) ? 0 : getDt().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", platformName=").append(platformName);
        sb.append(", companyCode=").append(companyCode);
        sb.append(", companyName=").append(companyName);
        sb.append(", bdpInterfaceCount=").append(bdpInterfaceCount);
        sb.append(", posInterfaceCount=").append(posInterfaceCount);
        sb.append(", posBusinessCount=").append(posBusinessCount);
        sb.append(", bdpPosInterfaceDiffCount=").append(bdpPosInterfaceDiffCount);
        sb.append(", posInterfaceBusinessDiffCount=").append(posInterfaceBusinessDiffCount);
        sb.append(", diffCount=").append(diffCount);
        sb.append(", dt=").append(dt);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}