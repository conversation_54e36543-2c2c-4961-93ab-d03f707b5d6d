package com.cowell.iscm.entityTidb;

import java.util.ArrayList;
import java.util.List;

public class IscmBdpDailySuggestSumExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public IscmBdpDailySuggestSumExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andPlatformNameIsNull() {
            addCriterion("platform_name is null");
            return (Criteria) this;
        }

        public Criteria andPlatformNameIsNotNull() {
            addCriterion("platform_name is not null");
            return (Criteria) this;
        }

        public Criteria andPlatformNameEqualTo(String value) {
            addCriterion("platform_name =", value, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameNotEqualTo(String value) {
            addCriterion("platform_name <>", value, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameGreaterThan(String value) {
            addCriterion("platform_name >", value, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameGreaterThanOrEqualTo(String value) {
            addCriterion("platform_name >=", value, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameLessThan(String value) {
            addCriterion("platform_name <", value, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameLessThanOrEqualTo(String value) {
            addCriterion("platform_name <=", value, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameLike(String value) {
            addCriterion("platform_name like", value, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameNotLike(String value) {
            addCriterion("platform_name not like", value, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameIn(List<String> values) {
            addCriterion("platform_name in", values, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameNotIn(List<String> values) {
            addCriterion("platform_name not in", values, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameBetween(String value1, String value2) {
            addCriterion("platform_name between", value1, value2, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameNotBetween(String value1, String value2) {
            addCriterion("platform_name not between", value1, value2, "platformName");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIsNull() {
            addCriterion("company_code is null");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIsNotNull() {
            addCriterion("company_code is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeEqualTo(String value) {
            addCriterion("company_code =", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotEqualTo(String value) {
            addCriterion("company_code <>", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeGreaterThan(String value) {
            addCriterion("company_code >", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeGreaterThanOrEqualTo(String value) {
            addCriterion("company_code >=", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLessThan(String value) {
            addCriterion("company_code <", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLessThanOrEqualTo(String value) {
            addCriterion("company_code <=", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLike(String value) {
            addCriterion("company_code like", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotLike(String value) {
            addCriterion("company_code not like", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIn(List<String> values) {
            addCriterion("company_code in", values, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotIn(List<String> values) {
            addCriterion("company_code not in", values, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeBetween(String value1, String value2) {
            addCriterion("company_code between", value1, value2, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotBetween(String value1, String value2) {
            addCriterion("company_code not between", value1, value2, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyNameIsNull() {
            addCriterion("company_name is null");
            return (Criteria) this;
        }

        public Criteria andCompanyNameIsNotNull() {
            addCriterion("company_name is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyNameEqualTo(String value) {
            addCriterion("company_name =", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotEqualTo(String value) {
            addCriterion("company_name <>", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameGreaterThan(String value) {
            addCriterion("company_name >", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameGreaterThanOrEqualTo(String value) {
            addCriterion("company_name >=", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameLessThan(String value) {
            addCriterion("company_name <", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameLessThanOrEqualTo(String value) {
            addCriterion("company_name <=", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameLike(String value) {
            addCriterion("company_name like", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotLike(String value) {
            addCriterion("company_name not like", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameIn(List<String> values) {
            addCriterion("company_name in", values, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotIn(List<String> values) {
            addCriterion("company_name not in", values, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameBetween(String value1, String value2) {
            addCriterion("company_name between", value1, value2, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotBetween(String value1, String value2) {
            addCriterion("company_name not between", value1, value2, "companyName");
            return (Criteria) this;
        }

        public Criteria andBdpInterfaceCountIsNull() {
            addCriterion("bdp_interface_count is null");
            return (Criteria) this;
        }

        public Criteria andBdpInterfaceCountIsNotNull() {
            addCriterion("bdp_interface_count is not null");
            return (Criteria) this;
        }

        public Criteria andBdpInterfaceCountEqualTo(Integer value) {
            addCriterion("bdp_interface_count =", value, "bdpInterfaceCount");
            return (Criteria) this;
        }

        public Criteria andBdpInterfaceCountNotEqualTo(Integer value) {
            addCriterion("bdp_interface_count <>", value, "bdpInterfaceCount");
            return (Criteria) this;
        }

        public Criteria andBdpInterfaceCountGreaterThan(Integer value) {
            addCriterion("bdp_interface_count >", value, "bdpInterfaceCount");
            return (Criteria) this;
        }

        public Criteria andBdpInterfaceCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("bdp_interface_count >=", value, "bdpInterfaceCount");
            return (Criteria) this;
        }

        public Criteria andBdpInterfaceCountLessThan(Integer value) {
            addCriterion("bdp_interface_count <", value, "bdpInterfaceCount");
            return (Criteria) this;
        }

        public Criteria andBdpInterfaceCountLessThanOrEqualTo(Integer value) {
            addCriterion("bdp_interface_count <=", value, "bdpInterfaceCount");
            return (Criteria) this;
        }

        public Criteria andBdpInterfaceCountIn(List<Integer> values) {
            addCriterion("bdp_interface_count in", values, "bdpInterfaceCount");
            return (Criteria) this;
        }

        public Criteria andBdpInterfaceCountNotIn(List<Integer> values) {
            addCriterion("bdp_interface_count not in", values, "bdpInterfaceCount");
            return (Criteria) this;
        }

        public Criteria andBdpInterfaceCountBetween(Integer value1, Integer value2) {
            addCriterion("bdp_interface_count between", value1, value2, "bdpInterfaceCount");
            return (Criteria) this;
        }

        public Criteria andBdpInterfaceCountNotBetween(Integer value1, Integer value2) {
            addCriterion("bdp_interface_count not between", value1, value2, "bdpInterfaceCount");
            return (Criteria) this;
        }

        public Criteria andPosInterfaceCountIsNull() {
            addCriterion("pos_interface_count is null");
            return (Criteria) this;
        }

        public Criteria andPosInterfaceCountIsNotNull() {
            addCriterion("pos_interface_count is not null");
            return (Criteria) this;
        }

        public Criteria andPosInterfaceCountEqualTo(Integer value) {
            addCriterion("pos_interface_count =", value, "posInterfaceCount");
            return (Criteria) this;
        }

        public Criteria andPosInterfaceCountNotEqualTo(Integer value) {
            addCriterion("pos_interface_count <>", value, "posInterfaceCount");
            return (Criteria) this;
        }

        public Criteria andPosInterfaceCountGreaterThan(Integer value) {
            addCriterion("pos_interface_count >", value, "posInterfaceCount");
            return (Criteria) this;
        }

        public Criteria andPosInterfaceCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("pos_interface_count >=", value, "posInterfaceCount");
            return (Criteria) this;
        }

        public Criteria andPosInterfaceCountLessThan(Integer value) {
            addCriterion("pos_interface_count <", value, "posInterfaceCount");
            return (Criteria) this;
        }

        public Criteria andPosInterfaceCountLessThanOrEqualTo(Integer value) {
            addCriterion("pos_interface_count <=", value, "posInterfaceCount");
            return (Criteria) this;
        }

        public Criteria andPosInterfaceCountIn(List<Integer> values) {
            addCriterion("pos_interface_count in", values, "posInterfaceCount");
            return (Criteria) this;
        }

        public Criteria andPosInterfaceCountNotIn(List<Integer> values) {
            addCriterion("pos_interface_count not in", values, "posInterfaceCount");
            return (Criteria) this;
        }

        public Criteria andPosInterfaceCountBetween(Integer value1, Integer value2) {
            addCriterion("pos_interface_count between", value1, value2, "posInterfaceCount");
            return (Criteria) this;
        }

        public Criteria andPosInterfaceCountNotBetween(Integer value1, Integer value2) {
            addCriterion("pos_interface_count not between", value1, value2, "posInterfaceCount");
            return (Criteria) this;
        }

        public Criteria andPosBusinessCountIsNull() {
            addCriterion("pos_business_count is null");
            return (Criteria) this;
        }

        public Criteria andPosBusinessCountIsNotNull() {
            addCriterion("pos_business_count is not null");
            return (Criteria) this;
        }

        public Criteria andPosBusinessCountEqualTo(Integer value) {
            addCriterion("pos_business_count =", value, "posBusinessCount");
            return (Criteria) this;
        }

        public Criteria andPosBusinessCountNotEqualTo(Integer value) {
            addCriterion("pos_business_count <>", value, "posBusinessCount");
            return (Criteria) this;
        }

        public Criteria andPosBusinessCountGreaterThan(Integer value) {
            addCriterion("pos_business_count >", value, "posBusinessCount");
            return (Criteria) this;
        }

        public Criteria andPosBusinessCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("pos_business_count >=", value, "posBusinessCount");
            return (Criteria) this;
        }

        public Criteria andPosBusinessCountLessThan(Integer value) {
            addCriterion("pos_business_count <", value, "posBusinessCount");
            return (Criteria) this;
        }

        public Criteria andPosBusinessCountLessThanOrEqualTo(Integer value) {
            addCriterion("pos_business_count <=", value, "posBusinessCount");
            return (Criteria) this;
        }

        public Criteria andPosBusinessCountIn(List<Integer> values) {
            addCriterion("pos_business_count in", values, "posBusinessCount");
            return (Criteria) this;
        }

        public Criteria andPosBusinessCountNotIn(List<Integer> values) {
            addCriterion("pos_business_count not in", values, "posBusinessCount");
            return (Criteria) this;
        }

        public Criteria andPosBusinessCountBetween(Integer value1, Integer value2) {
            addCriterion("pos_business_count between", value1, value2, "posBusinessCount");
            return (Criteria) this;
        }

        public Criteria andPosBusinessCountNotBetween(Integer value1, Integer value2) {
            addCriterion("pos_business_count not between", value1, value2, "posBusinessCount");
            return (Criteria) this;
        }

        public Criteria andBdpPosInterfaceDiffCountIsNull() {
            addCriterion("bdp_pos_interface_diff_count is null");
            return (Criteria) this;
        }

        public Criteria andBdpPosInterfaceDiffCountIsNotNull() {
            addCriterion("bdp_pos_interface_diff_count is not null");
            return (Criteria) this;
        }

        public Criteria andBdpPosInterfaceDiffCountEqualTo(Integer value) {
            addCriterion("bdp_pos_interface_diff_count =", value, "bdpPosInterfaceDiffCount");
            return (Criteria) this;
        }

        public Criteria andBdpPosInterfaceDiffCountNotEqualTo(Integer value) {
            addCriterion("bdp_pos_interface_diff_count <>", value, "bdpPosInterfaceDiffCount");
            return (Criteria) this;
        }

        public Criteria andBdpPosInterfaceDiffCountGreaterThan(Integer value) {
            addCriterion("bdp_pos_interface_diff_count >", value, "bdpPosInterfaceDiffCount");
            return (Criteria) this;
        }

        public Criteria andBdpPosInterfaceDiffCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("bdp_pos_interface_diff_count >=", value, "bdpPosInterfaceDiffCount");
            return (Criteria) this;
        }

        public Criteria andBdpPosInterfaceDiffCountLessThan(Integer value) {
            addCriterion("bdp_pos_interface_diff_count <", value, "bdpPosInterfaceDiffCount");
            return (Criteria) this;
        }

        public Criteria andBdpPosInterfaceDiffCountLessThanOrEqualTo(Integer value) {
            addCriterion("bdp_pos_interface_diff_count <=", value, "bdpPosInterfaceDiffCount");
            return (Criteria) this;
        }

        public Criteria andBdpPosInterfaceDiffCountIn(List<Integer> values) {
            addCriterion("bdp_pos_interface_diff_count in", values, "bdpPosInterfaceDiffCount");
            return (Criteria) this;
        }

        public Criteria andBdpPosInterfaceDiffCountNotIn(List<Integer> values) {
            addCriterion("bdp_pos_interface_diff_count not in", values, "bdpPosInterfaceDiffCount");
            return (Criteria) this;
        }

        public Criteria andBdpPosInterfaceDiffCountBetween(Integer value1, Integer value2) {
            addCriterion("bdp_pos_interface_diff_count between", value1, value2, "bdpPosInterfaceDiffCount");
            return (Criteria) this;
        }

        public Criteria andBdpPosInterfaceDiffCountNotBetween(Integer value1, Integer value2) {
            addCriterion("bdp_pos_interface_diff_count not between", value1, value2, "bdpPosInterfaceDiffCount");
            return (Criteria) this;
        }

        public Criteria andPosInterfaceBusinessDiffCountIsNull() {
            addCriterion("pos_interface_business_diff_count is null");
            return (Criteria) this;
        }

        public Criteria andPosInterfaceBusinessDiffCountIsNotNull() {
            addCriterion("pos_interface_business_diff_count is not null");
            return (Criteria) this;
        }

        public Criteria andPosInterfaceBusinessDiffCountEqualTo(Integer value) {
            addCriterion("pos_interface_business_diff_count =", value, "posInterfaceBusinessDiffCount");
            return (Criteria) this;
        }

        public Criteria andPosInterfaceBusinessDiffCountNotEqualTo(Integer value) {
            addCriterion("pos_interface_business_diff_count <>", value, "posInterfaceBusinessDiffCount");
            return (Criteria) this;
        }

        public Criteria andPosInterfaceBusinessDiffCountGreaterThan(Integer value) {
            addCriterion("pos_interface_business_diff_count >", value, "posInterfaceBusinessDiffCount");
            return (Criteria) this;
        }

        public Criteria andPosInterfaceBusinessDiffCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("pos_interface_business_diff_count >=", value, "posInterfaceBusinessDiffCount");
            return (Criteria) this;
        }

        public Criteria andPosInterfaceBusinessDiffCountLessThan(Integer value) {
            addCriterion("pos_interface_business_diff_count <", value, "posInterfaceBusinessDiffCount");
            return (Criteria) this;
        }

        public Criteria andPosInterfaceBusinessDiffCountLessThanOrEqualTo(Integer value) {
            addCriterion("pos_interface_business_diff_count <=", value, "posInterfaceBusinessDiffCount");
            return (Criteria) this;
        }

        public Criteria andPosInterfaceBusinessDiffCountIn(List<Integer> values) {
            addCriterion("pos_interface_business_diff_count in", values, "posInterfaceBusinessDiffCount");
            return (Criteria) this;
        }

        public Criteria andPosInterfaceBusinessDiffCountNotIn(List<Integer> values) {
            addCriterion("pos_interface_business_diff_count not in", values, "posInterfaceBusinessDiffCount");
            return (Criteria) this;
        }

        public Criteria andPosInterfaceBusinessDiffCountBetween(Integer value1, Integer value2) {
            addCriterion("pos_interface_business_diff_count between", value1, value2, "posInterfaceBusinessDiffCount");
            return (Criteria) this;
        }

        public Criteria andPosInterfaceBusinessDiffCountNotBetween(Integer value1, Integer value2) {
            addCriterion("pos_interface_business_diff_count not between", value1, value2, "posInterfaceBusinessDiffCount");
            return (Criteria) this;
        }

        public Criteria andDiffCountIsNull() {
            addCriterion("diff_count is null");
            return (Criteria) this;
        }

        public Criteria andDiffCountIsNotNull() {
            addCriterion("diff_count is not null");
            return (Criteria) this;
        }

        public Criteria andDiffCountEqualTo(Integer value) {
            addCriterion("diff_count =", value, "diffCount");
            return (Criteria) this;
        }

        public Criteria andDiffCountNotEqualTo(Integer value) {
            addCriterion("diff_count <>", value, "diffCount");
            return (Criteria) this;
        }

        public Criteria andDiffCountGreaterThan(Integer value) {
            addCriterion("diff_count >", value, "diffCount");
            return (Criteria) this;
        }

        public Criteria andDiffCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("diff_count >=", value, "diffCount");
            return (Criteria) this;
        }

        public Criteria andDiffCountLessThan(Integer value) {
            addCriterion("diff_count <", value, "diffCount");
            return (Criteria) this;
        }

        public Criteria andDiffCountLessThanOrEqualTo(Integer value) {
            addCriterion("diff_count <=", value, "diffCount");
            return (Criteria) this;
        }

        public Criteria andDiffCountIn(List<Integer> values) {
            addCriterion("diff_count in", values, "diffCount");
            return (Criteria) this;
        }

        public Criteria andDiffCountNotIn(List<Integer> values) {
            addCriterion("diff_count not in", values, "diffCount");
            return (Criteria) this;
        }

        public Criteria andDiffCountBetween(Integer value1, Integer value2) {
            addCriterion("diff_count between", value1, value2, "diffCount");
            return (Criteria) this;
        }

        public Criteria andDiffCountNotBetween(Integer value1, Integer value2) {
            addCriterion("diff_count not between", value1, value2, "diffCount");
            return (Criteria) this;
        }

        public Criteria andDtIsNull() {
            addCriterion("dt is null");
            return (Criteria) this;
        }

        public Criteria andDtIsNotNull() {
            addCriterion("dt is not null");
            return (Criteria) this;
        }

        public Criteria andDtEqualTo(String value) {
            addCriterion("dt =", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtNotEqualTo(String value) {
            addCriterion("dt <>", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtGreaterThan(String value) {
            addCriterion("dt >", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtGreaterThanOrEqualTo(String value) {
            addCriterion("dt >=", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtLessThan(String value) {
            addCriterion("dt <", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtLessThanOrEqualTo(String value) {
            addCriterion("dt <=", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtLike(String value) {
            addCriterion("dt like", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtNotLike(String value) {
            addCriterion("dt not like", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtIn(List<String> values) {
            addCriterion("dt in", values, "dt");
            return (Criteria) this;
        }

        public Criteria andDtNotIn(List<String> values) {
            addCriterion("dt not in", values, "dt");
            return (Criteria) this;
        }

        public Criteria andDtBetween(String value1, String value2) {
            addCriterion("dt between", value1, value2, "dt");
            return (Criteria) this;
        }

        public Criteria andDtNotBetween(String value1, String value2) {
            addCriterion("dt not between", value1, value2, "dt");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}