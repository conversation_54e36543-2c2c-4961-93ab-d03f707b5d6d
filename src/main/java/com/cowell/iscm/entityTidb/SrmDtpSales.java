package com.cowell.iscm.entityTidb;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * srm_dtp_sales
 * <AUTHOR>
public class SrmDtpSales implements Serializable {
    private Long id;

    /**
     * 销售日期
     */
    private Date businessDayDate;

    /**
     * 公司编码
     */
    private String companyCode;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 供应商编码
     */
    private String lifnr;

    /**
     * 供应商名称
     */
    private String lifnrName;

    /**
     * 门店编码
     */
    private String retailStoreId;

    /**
     * 门店名称
     */
    private String retailStoreName;

    /**
     * 商品编码
     */
    private String temid;

    /**
     * 商品名称
     */
    private String temidName;

    /**
     * 商品规格
     */
    private String temidSpec;

    /**
     * 厂家编码
     */
    private String manuId;

    /**
     * 生产厂家
     */
    private String manu;

    /**
     * 生产批号
     */
    private String batchCode;

    /**
     * 有效期至
     */
    private Date effDate;

    /**
     * 生产日期
     */
    private Date productDate;

    /**
     * 单位
     */
    private String unit;

    /**
     * 条形码
     */
    private String barCode;

    /**
     * 销售数量
     */
    private BigDecimal retailQuantity;

    /**
     * 零售金额（含税）
     */
    private BigDecimal retailAmount;

    /**
     * 医院编码
     */
    private String hospitalId;

    /**
     * 医院名称
     */
    private String hospitalName;

    /**
     * 销售流水单号
     */
    private String orderNo;

    /**
     * 是否上线sap企业
     */
    private Byte isSap;

    /**
     * 处方单号
     */
    private String prescriptionNumber;

    /**
     * 处方日期
     */
    private Date prescriptionDate;

    /**
     * 医师
     */
    private String doctorName;

    /**
     * 科室
     */
    private String department;

    /**
     * 疾病描述
     */
    private String diseaseDescription;

    /**
     * 批准文号
     */
    private String approvalNumber;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getBusinessDayDate() {
        return businessDayDate;
    }

    public void setBusinessDayDate(Date businessDayDate) {
        this.businessDayDate = businessDayDate;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getLifnr() {
        return lifnr;
    }

    public void setLifnr(String lifnr) {
        this.lifnr = lifnr;
    }

    public String getLifnrName() {
        return lifnrName;
    }

    public void setLifnrName(String lifnrName) {
        this.lifnrName = lifnrName;
    }

    public String getRetailStoreId() {
        return retailStoreId;
    }

    public void setRetailStoreId(String retailStoreId) {
        this.retailStoreId = retailStoreId;
    }

    public String getRetailStoreName() {
        return retailStoreName;
    }

    public void setRetailStoreName(String retailStoreName) {
        this.retailStoreName = retailStoreName;
    }

    public String getTemid() {
        return temid;
    }

    public void setTemid(String temid) {
        this.temid = temid;
    }

    public String getTemidName() {
        return temidName;
    }

    public void setTemidName(String temidName) {
        this.temidName = temidName;
    }

    public String getTemidSpec() {
        return temidSpec;
    }

    public void setTemidSpec(String temidSpec) {
        this.temidSpec = temidSpec;
    }

    public String getManuId() {
        return manuId;
    }

    public void setManuId(String manuId) {
        this.manuId = manuId;
    }

    public String getManu() {
        return manu;
    }

    public void setManu(String manu) {
        this.manu = manu;
    }

    public String getBatchCode() {
        return batchCode;
    }

    public void setBatchCode(String batchCode) {
        this.batchCode = batchCode;
    }

    public Date getEffDate() {
        return effDate;
    }

    public void setEffDate(Date effDate) {
        this.effDate = effDate;
    }

    public Date getProductDate() {
        return productDate;
    }

    public void setProductDate(Date productDate) {
        this.productDate = productDate;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getBarCode() {
        return barCode;
    }

    public void setBarCode(String barCode) {
        this.barCode = barCode;
    }

    public BigDecimal getRetailQuantity() {
        return retailQuantity;
    }

    public void setRetailQuantity(BigDecimal retailQuantity) {
        this.retailQuantity = retailQuantity;
    }

    public BigDecimal getRetailAmount() {
        return retailAmount;
    }

    public void setRetailAmount(BigDecimal retailAmount) {
        this.retailAmount = retailAmount;
    }

    public String getHospitalId() {
        return hospitalId;
    }

    public void setHospitalId(String hospitalId) {
        this.hospitalId = hospitalId;
    }

    public String getHospitalName() {
        return hospitalName;
    }

    public void setHospitalName(String hospitalName) {
        this.hospitalName = hospitalName;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Byte getIsSap() {
        return isSap;
    }

    public void setIsSap(Byte isSap) {
        this.isSap = isSap;
    }

    public String getPrescriptionNumber() {
        return prescriptionNumber;
    }

    public void setPrescriptionNumber(String prescriptionNumber) {
        this.prescriptionNumber = prescriptionNumber;
    }

    public Date getPrescriptionDate() {
        return prescriptionDate;
    }

    public void setPrescriptionDate(Date prescriptionDate) {
        this.prescriptionDate = prescriptionDate;
    }

    public String getDoctorName() {
        return doctorName;
    }

    public void setDoctorName(String doctorName) {
        this.doctorName = doctorName;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public String getDiseaseDescription() {
        return diseaseDescription;
    }

    public void setDiseaseDescription(String diseaseDescription) {
        this.diseaseDescription = diseaseDescription;
    }

    public String getApprovalNumber() {
        return approvalNumber;
    }

    public void setApprovalNumber(String approvalNumber) {
        this.approvalNumber = approvalNumber;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        SrmDtpSales other = (SrmDtpSales) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getBusinessDayDate() == null ? other.getBusinessDayDate() == null : this.getBusinessDayDate().equals(other.getBusinessDayDate()))
            && (this.getCompanyCode() == null ? other.getCompanyCode() == null : this.getCompanyCode().equals(other.getCompanyCode()))
            && (this.getCompanyName() == null ? other.getCompanyName() == null : this.getCompanyName().equals(other.getCompanyName()))
            && (this.getLifnr() == null ? other.getLifnr() == null : this.getLifnr().equals(other.getLifnr()))
            && (this.getLifnrName() == null ? other.getLifnrName() == null : this.getLifnrName().equals(other.getLifnrName()))
            && (this.getRetailStoreId() == null ? other.getRetailStoreId() == null : this.getRetailStoreId().equals(other.getRetailStoreId()))
            && (this.getRetailStoreName() == null ? other.getRetailStoreName() == null : this.getRetailStoreName().equals(other.getRetailStoreName()))
            && (this.getTemid() == null ? other.getTemid() == null : this.getTemid().equals(other.getTemid()))
            && (this.getTemidName() == null ? other.getTemidName() == null : this.getTemidName().equals(other.getTemidName()))
            && (this.getTemidSpec() == null ? other.getTemidSpec() == null : this.getTemidSpec().equals(other.getTemidSpec()))
            && (this.getManuId() == null ? other.getManuId() == null : this.getManuId().equals(other.getManuId()))
            && (this.getManu() == null ? other.getManu() == null : this.getManu().equals(other.getManu()))
            && (this.getBatchCode() == null ? other.getBatchCode() == null : this.getBatchCode().equals(other.getBatchCode()))
            && (this.getEffDate() == null ? other.getEffDate() == null : this.getEffDate().equals(other.getEffDate()))
            && (this.getProductDate() == null ? other.getProductDate() == null : this.getProductDate().equals(other.getProductDate()))
            && (this.getUnit() == null ? other.getUnit() == null : this.getUnit().equals(other.getUnit()))
            && (this.getBarCode() == null ? other.getBarCode() == null : this.getBarCode().equals(other.getBarCode()))
            && (this.getRetailQuantity() == null ? other.getRetailQuantity() == null : this.getRetailQuantity().equals(other.getRetailQuantity()))
            && (this.getRetailAmount() == null ? other.getRetailAmount() == null : this.getRetailAmount().equals(other.getRetailAmount()))
            && (this.getHospitalId() == null ? other.getHospitalId() == null : this.getHospitalId().equals(other.getHospitalId()))
            && (this.getHospitalName() == null ? other.getHospitalName() == null : this.getHospitalName().equals(other.getHospitalName()))
            && (this.getOrderNo() == null ? other.getOrderNo() == null : this.getOrderNo().equals(other.getOrderNo()))
            && (this.getIsSap() == null ? other.getIsSap() == null : this.getIsSap().equals(other.getIsSap()))
            && (this.getPrescriptionNumber() == null ? other.getPrescriptionNumber() == null : this.getPrescriptionNumber().equals(other.getPrescriptionNumber()))
            && (this.getPrescriptionDate() == null ? other.getPrescriptionDate() == null : this.getPrescriptionDate().equals(other.getPrescriptionDate()))
            && (this.getDoctorName() == null ? other.getDoctorName() == null : this.getDoctorName().equals(other.getDoctorName()))
            && (this.getDepartment() == null ? other.getDepartment() == null : this.getDepartment().equals(other.getDepartment()))
            && (this.getDiseaseDescription() == null ? other.getDiseaseDescription() == null : this.getDiseaseDescription().equals(other.getDiseaseDescription()))
            && (this.getApprovalNumber() == null ? other.getApprovalNumber() == null : this.getApprovalNumber().equals(other.getApprovalNumber()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getBusinessDayDate() == null) ? 0 : getBusinessDayDate().hashCode());
        result = prime * result + ((getCompanyCode() == null) ? 0 : getCompanyCode().hashCode());
        result = prime * result + ((getCompanyName() == null) ? 0 : getCompanyName().hashCode());
        result = prime * result + ((getLifnr() == null) ? 0 : getLifnr().hashCode());
        result = prime * result + ((getLifnrName() == null) ? 0 : getLifnrName().hashCode());
        result = prime * result + ((getRetailStoreId() == null) ? 0 : getRetailStoreId().hashCode());
        result = prime * result + ((getRetailStoreName() == null) ? 0 : getRetailStoreName().hashCode());
        result = prime * result + ((getTemid() == null) ? 0 : getTemid().hashCode());
        result = prime * result + ((getTemidName() == null) ? 0 : getTemidName().hashCode());
        result = prime * result + ((getTemidSpec() == null) ? 0 : getTemidSpec().hashCode());
        result = prime * result + ((getManuId() == null) ? 0 : getManuId().hashCode());
        result = prime * result + ((getManu() == null) ? 0 : getManu().hashCode());
        result = prime * result + ((getBatchCode() == null) ? 0 : getBatchCode().hashCode());
        result = prime * result + ((getEffDate() == null) ? 0 : getEffDate().hashCode());
        result = prime * result + ((getProductDate() == null) ? 0 : getProductDate().hashCode());
        result = prime * result + ((getUnit() == null) ? 0 : getUnit().hashCode());
        result = prime * result + ((getBarCode() == null) ? 0 : getBarCode().hashCode());
        result = prime * result + ((getRetailQuantity() == null) ? 0 : getRetailQuantity().hashCode());
        result = prime * result + ((getRetailAmount() == null) ? 0 : getRetailAmount().hashCode());
        result = prime * result + ((getHospitalId() == null) ? 0 : getHospitalId().hashCode());
        result = prime * result + ((getHospitalName() == null) ? 0 : getHospitalName().hashCode());
        result = prime * result + ((getOrderNo() == null) ? 0 : getOrderNo().hashCode());
        result = prime * result + ((getIsSap() == null) ? 0 : getIsSap().hashCode());
        result = prime * result + ((getPrescriptionNumber() == null) ? 0 : getPrescriptionNumber().hashCode());
        result = prime * result + ((getPrescriptionDate() == null) ? 0 : getPrescriptionDate().hashCode());
        result = prime * result + ((getDoctorName() == null) ? 0 : getDoctorName().hashCode());
        result = prime * result + ((getDepartment() == null) ? 0 : getDepartment().hashCode());
        result = prime * result + ((getDiseaseDescription() == null) ? 0 : getDiseaseDescription().hashCode());
        result = prime * result + ((getApprovalNumber() == null) ? 0 : getApprovalNumber().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", businessDayDate=").append(businessDayDate);
        sb.append(", companyCode=").append(companyCode);
        sb.append(", companyName=").append(companyName);
        sb.append(", lifnr=").append(lifnr);
        sb.append(", lifnrName=").append(lifnrName);
        sb.append(", retailStoreId=").append(retailStoreId);
        sb.append(", retailStoreName=").append(retailStoreName);
        sb.append(", temid=").append(temid);
        sb.append(", temidName=").append(temidName);
        sb.append(", temidSpec=").append(temidSpec);
        sb.append(", manuId=").append(manuId);
        sb.append(", manu=").append(manu);
        sb.append(", batchCode=").append(batchCode);
        sb.append(", effDate=").append(effDate);
        sb.append(", productDate=").append(productDate);
        sb.append(", unit=").append(unit);
        sb.append(", barCode=").append(barCode);
        sb.append(", retailQuantity=").append(retailQuantity);
        sb.append(", retailAmount=").append(retailAmount);
        sb.append(", hospitalId=").append(hospitalId);
        sb.append(", hospitalName=").append(hospitalName);
        sb.append(", orderNo=").append(orderNo);
        sb.append(", isSap=").append(isSap);
        sb.append(", prescriptionNumber=").append(prescriptionNumber);
        sb.append(", prescriptionDate=").append(prescriptionDate);
        sb.append(", doctorName=").append(doctorName);
        sb.append(", department=").append(department);
        sb.append(", diseaseDescription=").append(diseaseDescription);
        sb.append(", approvalNumber=").append(approvalNumber);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}