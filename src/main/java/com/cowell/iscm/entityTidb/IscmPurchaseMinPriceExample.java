package com.cowell.iscm.entityTidb;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class IscmPurchaseMinPriceExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Long offset;

    public IscmPurchaseMinPriceExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Long offset) {
        this.offset = offset;
    }

    public Long getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        protected void addCriterionForJDBCDate(String condition, Date value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value.getTime()), property);
        }

        protected void addCriterionForJDBCDate(String condition, List<Date> values, String property) {
            if (values == null || values.size() == 0) {
                throw new RuntimeException("Value list for " + property + " cannot be null or empty");
            }
            List<java.sql.Date> dateList = new ArrayList<java.sql.Date>();
            Iterator<Date> iter = values.iterator();
            while (iter.hasNext()) {
                dateList.add(new java.sql.Date(iter.next().getTime()));
            }
            addCriterion(condition, dateList, property);
        }

        protected void addCriterionForJDBCDate(String condition, Date value1, Date value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value1.getTime()), new java.sql.Date(value2.getTime()), property);
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andTransferDateIsNull() {
            addCriterion("transfer_date is null");
            return (Criteria) this;
        }

        public Criteria andTransferDateIsNotNull() {
            addCriterion("transfer_date is not null");
            return (Criteria) this;
        }

        public Criteria andTransferDateEqualTo(Date value) {
            addCriterionForJDBCDate("transfer_date =", value, "transferDate");
            return (Criteria) this;
        }

        public Criteria andTransferDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("transfer_date <>", value, "transferDate");
            return (Criteria) this;
        }

        public Criteria andTransferDateGreaterThan(Date value) {
            addCriterionForJDBCDate("transfer_date >", value, "transferDate");
            return (Criteria) this;
        }

        public Criteria andTransferDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("transfer_date >=", value, "transferDate");
            return (Criteria) this;
        }

        public Criteria andTransferDateLessThan(Date value) {
            addCriterionForJDBCDate("transfer_date <", value, "transferDate");
            return (Criteria) this;
        }

        public Criteria andTransferDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("transfer_date <=", value, "transferDate");
            return (Criteria) this;
        }

        public Criteria andTransferDateIn(List<Date> values) {
            addCriterionForJDBCDate("transfer_date in", values, "transferDate");
            return (Criteria) this;
        }

        public Criteria andTransferDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("transfer_date not in", values, "transferDate");
            return (Criteria) this;
        }

        public Criteria andTransferDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("transfer_date between", value1, value2, "transferDate");
            return (Criteria) this;
        }

        public Criteria andTransferDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("transfer_date not between", value1, value2, "transferDate");
            return (Criteria) this;
        }

        public Criteria andGoodsNoIsNull() {
            addCriterion("goods_no is null");
            return (Criteria) this;
        }

        public Criteria andGoodsNoIsNotNull() {
            addCriterion("goods_no is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsNoEqualTo(String value) {
            addCriterion("goods_no =", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotEqualTo(String value) {
            addCriterion("goods_no <>", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoGreaterThan(String value) {
            addCriterion("goods_no >", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoGreaterThanOrEqualTo(String value) {
            addCriterion("goods_no >=", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoLessThan(String value) {
            addCriterion("goods_no <", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoLessThanOrEqualTo(String value) {
            addCriterion("goods_no <=", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoLike(String value) {
            addCriterion("goods_no like", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotLike(String value) {
            addCriterion("goods_no not like", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoIn(List<String> values) {
            addCriterion("goods_no in", values, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotIn(List<String> values) {
            addCriterion("goods_no not in", values, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoBetween(String value1, String value2) {
            addCriterion("goods_no between", value1, value2, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotBetween(String value1, String value2) {
            addCriterion("goods_no not between", value1, value2, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNameIsNull() {
            addCriterion("goods_name is null");
            return (Criteria) this;
        }

        public Criteria andGoodsNameIsNotNull() {
            addCriterion("goods_name is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsNameEqualTo(String value) {
            addCriterion("goods_name =", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameNotEqualTo(String value) {
            addCriterion("goods_name <>", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameGreaterThan(String value) {
            addCriterion("goods_name >", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameGreaterThanOrEqualTo(String value) {
            addCriterion("goods_name >=", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameLessThan(String value) {
            addCriterion("goods_name <", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameLessThanOrEqualTo(String value) {
            addCriterion("goods_name <=", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameLike(String value) {
            addCriterion("goods_name like", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameNotLike(String value) {
            addCriterion("goods_name not like", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameIn(List<String> values) {
            addCriterion("goods_name in", values, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameNotIn(List<String> values) {
            addCriterion("goods_name not in", values, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameBetween(String value1, String value2) {
            addCriterion("goods_name between", value1, value2, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameNotBetween(String value1, String value2) {
            addCriterion("goods_name not between", value1, value2, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameLenIsNull() {
            addCriterion("goods_name_len is null");
            return (Criteria) this;
        }

        public Criteria andGoodsNameLenIsNotNull() {
            addCriterion("goods_name_len is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsNameLenEqualTo(Integer value) {
            addCriterion("goods_name_len =", value, "goodsNameLen");
            return (Criteria) this;
        }

        public Criteria andGoodsNameLenNotEqualTo(Integer value) {
            addCriterion("goods_name_len <>", value, "goodsNameLen");
            return (Criteria) this;
        }

        public Criteria andGoodsNameLenGreaterThan(Integer value) {
            addCriterion("goods_name_len >", value, "goodsNameLen");
            return (Criteria) this;
        }

        public Criteria andGoodsNameLenGreaterThanOrEqualTo(Integer value) {
            addCriterion("goods_name_len >=", value, "goodsNameLen");
            return (Criteria) this;
        }

        public Criteria andGoodsNameLenLessThan(Integer value) {
            addCriterion("goods_name_len <", value, "goodsNameLen");
            return (Criteria) this;
        }

        public Criteria andGoodsNameLenLessThanOrEqualTo(Integer value) {
            addCriterion("goods_name_len <=", value, "goodsNameLen");
            return (Criteria) this;
        }

        public Criteria andGoodsNameLenIn(List<Integer> values) {
            addCriterion("goods_name_len in", values, "goodsNameLen");
            return (Criteria) this;
        }

        public Criteria andGoodsNameLenNotIn(List<Integer> values) {
            addCriterion("goods_name_len not in", values, "goodsNameLen");
            return (Criteria) this;
        }

        public Criteria andGoodsNameLenBetween(Integer value1, Integer value2) {
            addCriterion("goods_name_len between", value1, value2, "goodsNameLen");
            return (Criteria) this;
        }

        public Criteria andGoodsNameLenNotBetween(Integer value1, Integer value2) {
            addCriterion("goods_name_len not between", value1, value2, "goodsNameLen");
            return (Criteria) this;
        }

        public Criteria andManufacturerCodeIsNull() {
            addCriterion("manufacturer_code is null");
            return (Criteria) this;
        }

        public Criteria andManufacturerCodeIsNotNull() {
            addCriterion("manufacturer_code is not null");
            return (Criteria) this;
        }

        public Criteria andManufacturerCodeEqualTo(String value) {
            addCriterion("manufacturer_code =", value, "manufacturerCode");
            return (Criteria) this;
        }

        public Criteria andManufacturerCodeNotEqualTo(String value) {
            addCriterion("manufacturer_code <>", value, "manufacturerCode");
            return (Criteria) this;
        }

        public Criteria andManufacturerCodeGreaterThan(String value) {
            addCriterion("manufacturer_code >", value, "manufacturerCode");
            return (Criteria) this;
        }

        public Criteria andManufacturerCodeGreaterThanOrEqualTo(String value) {
            addCriterion("manufacturer_code >=", value, "manufacturerCode");
            return (Criteria) this;
        }

        public Criteria andManufacturerCodeLessThan(String value) {
            addCriterion("manufacturer_code <", value, "manufacturerCode");
            return (Criteria) this;
        }

        public Criteria andManufacturerCodeLessThanOrEqualTo(String value) {
            addCriterion("manufacturer_code <=", value, "manufacturerCode");
            return (Criteria) this;
        }

        public Criteria andManufacturerCodeLike(String value) {
            addCriterion("manufacturer_code like", value, "manufacturerCode");
            return (Criteria) this;
        }

        public Criteria andManufacturerCodeNotLike(String value) {
            addCriterion("manufacturer_code not like", value, "manufacturerCode");
            return (Criteria) this;
        }

        public Criteria andManufacturerCodeIn(List<String> values) {
            addCriterion("manufacturer_code in", values, "manufacturerCode");
            return (Criteria) this;
        }

        public Criteria andManufacturerCodeNotIn(List<String> values) {
            addCriterion("manufacturer_code not in", values, "manufacturerCode");
            return (Criteria) this;
        }

        public Criteria andManufacturerCodeBetween(String value1, String value2) {
            addCriterion("manufacturer_code between", value1, value2, "manufacturerCode");
            return (Criteria) this;
        }

        public Criteria andManufacturerCodeNotBetween(String value1, String value2) {
            addCriterion("manufacturer_code not between", value1, value2, "manufacturerCode");
            return (Criteria) this;
        }

        public Criteria andManufacturerIsNull() {
            addCriterion("manufacturer is null");
            return (Criteria) this;
        }

        public Criteria andManufacturerIsNotNull() {
            addCriterion("manufacturer is not null");
            return (Criteria) this;
        }

        public Criteria andManufacturerEqualTo(String value) {
            addCriterion("manufacturer =", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerNotEqualTo(String value) {
            addCriterion("manufacturer <>", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerGreaterThan(String value) {
            addCriterion("manufacturer >", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerGreaterThanOrEqualTo(String value) {
            addCriterion("manufacturer >=", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerLessThan(String value) {
            addCriterion("manufacturer <", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerLessThanOrEqualTo(String value) {
            addCriterion("manufacturer <=", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerLike(String value) {
            addCriterion("manufacturer like", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerNotLike(String value) {
            addCriterion("manufacturer not like", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerIn(List<String> values) {
            addCriterion("manufacturer in", values, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerNotIn(List<String> values) {
            addCriterion("manufacturer not in", values, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerBetween(String value1, String value2) {
            addCriterion("manufacturer between", value1, value2, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerNotBetween(String value1, String value2) {
            addCriterion("manufacturer not between", value1, value2, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerLenIsNull() {
            addCriterion("manufacturer_len is null");
            return (Criteria) this;
        }

        public Criteria andManufacturerLenIsNotNull() {
            addCriterion("manufacturer_len is not null");
            return (Criteria) this;
        }

        public Criteria andManufacturerLenEqualTo(Integer value) {
            addCriterion("manufacturer_len =", value, "manufacturerLen");
            return (Criteria) this;
        }

        public Criteria andManufacturerLenNotEqualTo(Integer value) {
            addCriterion("manufacturer_len <>", value, "manufacturerLen");
            return (Criteria) this;
        }

        public Criteria andManufacturerLenGreaterThan(Integer value) {
            addCriterion("manufacturer_len >", value, "manufacturerLen");
            return (Criteria) this;
        }

        public Criteria andManufacturerLenGreaterThanOrEqualTo(Integer value) {
            addCriterion("manufacturer_len >=", value, "manufacturerLen");
            return (Criteria) this;
        }

        public Criteria andManufacturerLenLessThan(Integer value) {
            addCriterion("manufacturer_len <", value, "manufacturerLen");
            return (Criteria) this;
        }

        public Criteria andManufacturerLenLessThanOrEqualTo(Integer value) {
            addCriterion("manufacturer_len <=", value, "manufacturerLen");
            return (Criteria) this;
        }

        public Criteria andManufacturerLenIn(List<Integer> values) {
            addCriterion("manufacturer_len in", values, "manufacturerLen");
            return (Criteria) this;
        }

        public Criteria andManufacturerLenNotIn(List<Integer> values) {
            addCriterion("manufacturer_len not in", values, "manufacturerLen");
            return (Criteria) this;
        }

        public Criteria andManufacturerLenBetween(Integer value1, Integer value2) {
            addCriterion("manufacturer_len between", value1, value2, "manufacturerLen");
            return (Criteria) this;
        }

        public Criteria andManufacturerLenNotBetween(Integer value1, Integer value2) {
            addCriterion("manufacturer_len not between", value1, value2, "manufacturerLen");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrgTypeIsNull() {
            addCriterion("purchase_org_type is null");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrgTypeIsNotNull() {
            addCriterion("purchase_org_type is not null");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrgTypeEqualTo(Byte value) {
            addCriterion("purchase_org_type =", value, "purchaseOrgType");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrgTypeNotEqualTo(Byte value) {
            addCriterion("purchase_org_type <>", value, "purchaseOrgType");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrgTypeGreaterThan(Byte value) {
            addCriterion("purchase_org_type >", value, "purchaseOrgType");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrgTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("purchase_org_type >=", value, "purchaseOrgType");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrgTypeLessThan(Byte value) {
            addCriterion("purchase_org_type <", value, "purchaseOrgType");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrgTypeLessThanOrEqualTo(Byte value) {
            addCriterion("purchase_org_type <=", value, "purchaseOrgType");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrgTypeIn(List<Byte> values) {
            addCriterion("purchase_org_type in", values, "purchaseOrgType");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrgTypeNotIn(List<Byte> values) {
            addCriterion("purchase_org_type not in", values, "purchaseOrgType");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrgTypeBetween(Byte value1, Byte value2) {
            addCriterion("purchase_org_type between", value1, value2, "purchaseOrgType");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrgTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("purchase_org_type not between", value1, value2, "purchaseOrgType");
            return (Criteria) this;
        }

        public Criteria andThreePurchaseMinPriceIsNull() {
            addCriterion("three_purchase_min_price is null");
            return (Criteria) this;
        }

        public Criteria andThreePurchaseMinPriceIsNotNull() {
            addCriterion("three_purchase_min_price is not null");
            return (Criteria) this;
        }

        public Criteria andThreePurchaseMinPriceEqualTo(BigDecimal value) {
            addCriterion("three_purchase_min_price =", value, "threePurchaseMinPrice");
            return (Criteria) this;
        }

        public Criteria andThreePurchaseMinPriceNotEqualTo(BigDecimal value) {
            addCriterion("three_purchase_min_price <>", value, "threePurchaseMinPrice");
            return (Criteria) this;
        }

        public Criteria andThreePurchaseMinPriceGreaterThan(BigDecimal value) {
            addCriterion("three_purchase_min_price >", value, "threePurchaseMinPrice");
            return (Criteria) this;
        }

        public Criteria andThreePurchaseMinPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("three_purchase_min_price >=", value, "threePurchaseMinPrice");
            return (Criteria) this;
        }

        public Criteria andThreePurchaseMinPriceLessThan(BigDecimal value) {
            addCriterion("three_purchase_min_price <", value, "threePurchaseMinPrice");
            return (Criteria) this;
        }

        public Criteria andThreePurchaseMinPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("three_purchase_min_price <=", value, "threePurchaseMinPrice");
            return (Criteria) this;
        }

        public Criteria andThreePurchaseMinPriceIn(List<BigDecimal> values) {
            addCriterion("three_purchase_min_price in", values, "threePurchaseMinPrice");
            return (Criteria) this;
        }

        public Criteria andThreePurchaseMinPriceNotIn(List<BigDecimal> values) {
            addCriterion("three_purchase_min_price not in", values, "threePurchaseMinPrice");
            return (Criteria) this;
        }

        public Criteria andThreePurchaseMinPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("three_purchase_min_price between", value1, value2, "threePurchaseMinPrice");
            return (Criteria) this;
        }

        public Criteria andThreePurchaseMinPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("three_purchase_min_price not between", value1, value2, "threePurchaseMinPrice");
            return (Criteria) this;
        }

        public Criteria andSixPurchaseMinPriceIsNull() {
            addCriterion("six_purchase_min_price is null");
            return (Criteria) this;
        }

        public Criteria andSixPurchaseMinPriceIsNotNull() {
            addCriterion("six_purchase_min_price is not null");
            return (Criteria) this;
        }

        public Criteria andSixPurchaseMinPriceEqualTo(BigDecimal value) {
            addCriterion("six_purchase_min_price =", value, "sixPurchaseMinPrice");
            return (Criteria) this;
        }

        public Criteria andSixPurchaseMinPriceNotEqualTo(BigDecimal value) {
            addCriterion("six_purchase_min_price <>", value, "sixPurchaseMinPrice");
            return (Criteria) this;
        }

        public Criteria andSixPurchaseMinPriceGreaterThan(BigDecimal value) {
            addCriterion("six_purchase_min_price >", value, "sixPurchaseMinPrice");
            return (Criteria) this;
        }

        public Criteria andSixPurchaseMinPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("six_purchase_min_price >=", value, "sixPurchaseMinPrice");
            return (Criteria) this;
        }

        public Criteria andSixPurchaseMinPriceLessThan(BigDecimal value) {
            addCriterion("six_purchase_min_price <", value, "sixPurchaseMinPrice");
            return (Criteria) this;
        }

        public Criteria andSixPurchaseMinPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("six_purchase_min_price <=", value, "sixPurchaseMinPrice");
            return (Criteria) this;
        }

        public Criteria andSixPurchaseMinPriceIn(List<BigDecimal> values) {
            addCriterion("six_purchase_min_price in", values, "sixPurchaseMinPrice");
            return (Criteria) this;
        }

        public Criteria andSixPurchaseMinPriceNotIn(List<BigDecimal> values) {
            addCriterion("six_purchase_min_price not in", values, "sixPurchaseMinPrice");
            return (Criteria) this;
        }

        public Criteria andSixPurchaseMinPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("six_purchase_min_price between", value1, value2, "sixPurchaseMinPrice");
            return (Criteria) this;
        }

        public Criteria andSixPurchaseMinPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("six_purchase_min_price not between", value1, value2, "sixPurchaseMinPrice");
            return (Criteria) this;
        }

        public Criteria andTwelvePurchaseMinPriceIsNull() {
            addCriterion("twelve_purchase_min_price is null");
            return (Criteria) this;
        }

        public Criteria andTwelvePurchaseMinPriceIsNotNull() {
            addCriterion("twelve_purchase_min_price is not null");
            return (Criteria) this;
        }

        public Criteria andTwelvePurchaseMinPriceEqualTo(BigDecimal value) {
            addCriterion("twelve_purchase_min_price =", value, "twelvePurchaseMinPrice");
            return (Criteria) this;
        }

        public Criteria andTwelvePurchaseMinPriceNotEqualTo(BigDecimal value) {
            addCriterion("twelve_purchase_min_price <>", value, "twelvePurchaseMinPrice");
            return (Criteria) this;
        }

        public Criteria andTwelvePurchaseMinPriceGreaterThan(BigDecimal value) {
            addCriterion("twelve_purchase_min_price >", value, "twelvePurchaseMinPrice");
            return (Criteria) this;
        }

        public Criteria andTwelvePurchaseMinPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("twelve_purchase_min_price >=", value, "twelvePurchaseMinPrice");
            return (Criteria) this;
        }

        public Criteria andTwelvePurchaseMinPriceLessThan(BigDecimal value) {
            addCriterion("twelve_purchase_min_price <", value, "twelvePurchaseMinPrice");
            return (Criteria) this;
        }

        public Criteria andTwelvePurchaseMinPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("twelve_purchase_min_price <=", value, "twelvePurchaseMinPrice");
            return (Criteria) this;
        }

        public Criteria andTwelvePurchaseMinPriceIn(List<BigDecimal> values) {
            addCriterion("twelve_purchase_min_price in", values, "twelvePurchaseMinPrice");
            return (Criteria) this;
        }

        public Criteria andTwelvePurchaseMinPriceNotIn(List<BigDecimal> values) {
            addCriterion("twelve_purchase_min_price not in", values, "twelvePurchaseMinPrice");
            return (Criteria) this;
        }

        public Criteria andTwelvePurchaseMinPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("twelve_purchase_min_price between", value1, value2, "twelvePurchaseMinPrice");
            return (Criteria) this;
        }

        public Criteria andTwelvePurchaseMinPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("twelve_purchase_min_price not between", value1, value2, "twelvePurchaseMinPrice");
            return (Criteria) this;
        }

        public Criteria andThreePurchaseDateIsNull() {
            addCriterion("three_purchase_date is null");
            return (Criteria) this;
        }

        public Criteria andThreePurchaseDateIsNotNull() {
            addCriterion("three_purchase_date is not null");
            return (Criteria) this;
        }

        public Criteria andThreePurchaseDateEqualTo(Date value) {
            addCriterionForJDBCDate("three_purchase_date =", value, "threePurchaseDate");
            return (Criteria) this;
        }

        public Criteria andThreePurchaseDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("three_purchase_date <>", value, "threePurchaseDate");
            return (Criteria) this;
        }

        public Criteria andThreePurchaseDateGreaterThan(Date value) {
            addCriterionForJDBCDate("three_purchase_date >", value, "threePurchaseDate");
            return (Criteria) this;
        }

        public Criteria andThreePurchaseDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("three_purchase_date >=", value, "threePurchaseDate");
            return (Criteria) this;
        }

        public Criteria andThreePurchaseDateLessThan(Date value) {
            addCriterionForJDBCDate("three_purchase_date <", value, "threePurchaseDate");
            return (Criteria) this;
        }

        public Criteria andThreePurchaseDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("three_purchase_date <=", value, "threePurchaseDate");
            return (Criteria) this;
        }

        public Criteria andThreePurchaseDateIn(List<Date> values) {
            addCriterionForJDBCDate("three_purchase_date in", values, "threePurchaseDate");
            return (Criteria) this;
        }

        public Criteria andThreePurchaseDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("three_purchase_date not in", values, "threePurchaseDate");
            return (Criteria) this;
        }

        public Criteria andThreePurchaseDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("three_purchase_date between", value1, value2, "threePurchaseDate");
            return (Criteria) this;
        }

        public Criteria andThreePurchaseDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("three_purchase_date not between", value1, value2, "threePurchaseDate");
            return (Criteria) this;
        }

        public Criteria andSixPurchaseDateIsNull() {
            addCriterion("six_purchase_date is null");
            return (Criteria) this;
        }

        public Criteria andSixPurchaseDateIsNotNull() {
            addCriterion("six_purchase_date is not null");
            return (Criteria) this;
        }

        public Criteria andSixPurchaseDateEqualTo(Date value) {
            addCriterionForJDBCDate("six_purchase_date =", value, "sixPurchaseDate");
            return (Criteria) this;
        }

        public Criteria andSixPurchaseDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("six_purchase_date <>", value, "sixPurchaseDate");
            return (Criteria) this;
        }

        public Criteria andSixPurchaseDateGreaterThan(Date value) {
            addCriterionForJDBCDate("six_purchase_date >", value, "sixPurchaseDate");
            return (Criteria) this;
        }

        public Criteria andSixPurchaseDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("six_purchase_date >=", value, "sixPurchaseDate");
            return (Criteria) this;
        }

        public Criteria andSixPurchaseDateLessThan(Date value) {
            addCriterionForJDBCDate("six_purchase_date <", value, "sixPurchaseDate");
            return (Criteria) this;
        }

        public Criteria andSixPurchaseDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("six_purchase_date <=", value, "sixPurchaseDate");
            return (Criteria) this;
        }

        public Criteria andSixPurchaseDateIn(List<Date> values) {
            addCriterionForJDBCDate("six_purchase_date in", values, "sixPurchaseDate");
            return (Criteria) this;
        }

        public Criteria andSixPurchaseDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("six_purchase_date not in", values, "sixPurchaseDate");
            return (Criteria) this;
        }

        public Criteria andSixPurchaseDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("six_purchase_date between", value1, value2, "sixPurchaseDate");
            return (Criteria) this;
        }

        public Criteria andSixPurchaseDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("six_purchase_date not between", value1, value2, "sixPurchaseDate");
            return (Criteria) this;
        }

        public Criteria andTwelvePurchaseDateIsNull() {
            addCriterion("twelve_purchase_date is null");
            return (Criteria) this;
        }

        public Criteria andTwelvePurchaseDateIsNotNull() {
            addCriterion("twelve_purchase_date is not null");
            return (Criteria) this;
        }

        public Criteria andTwelvePurchaseDateEqualTo(Date value) {
            addCriterionForJDBCDate("twelve_purchase_date =", value, "twelvePurchaseDate");
            return (Criteria) this;
        }

        public Criteria andTwelvePurchaseDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("twelve_purchase_date <>", value, "twelvePurchaseDate");
            return (Criteria) this;
        }

        public Criteria andTwelvePurchaseDateGreaterThan(Date value) {
            addCriterionForJDBCDate("twelve_purchase_date >", value, "twelvePurchaseDate");
            return (Criteria) this;
        }

        public Criteria andTwelvePurchaseDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("twelve_purchase_date >=", value, "twelvePurchaseDate");
            return (Criteria) this;
        }

        public Criteria andTwelvePurchaseDateLessThan(Date value) {
            addCriterionForJDBCDate("twelve_purchase_date <", value, "twelvePurchaseDate");
            return (Criteria) this;
        }

        public Criteria andTwelvePurchaseDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("twelve_purchase_date <=", value, "twelvePurchaseDate");
            return (Criteria) this;
        }

        public Criteria andTwelvePurchaseDateIn(List<Date> values) {
            addCriterionForJDBCDate("twelve_purchase_date in", values, "twelvePurchaseDate");
            return (Criteria) this;
        }

        public Criteria andTwelvePurchaseDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("twelve_purchase_date not in", values, "twelvePurchaseDate");
            return (Criteria) this;
        }

        public Criteria andTwelvePurchaseDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("twelve_purchase_date between", value1, value2, "twelvePurchaseDate");
            return (Criteria) this;
        }

        public Criteria andTwelvePurchaseDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("twelve_purchase_date not between", value1, value2, "twelvePurchaseDate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIsNull() {
            addCriterion("gmt_update is null");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIsNotNull() {
            addCriterion("gmt_update is not null");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateEqualTo(Date value) {
            addCriterion("gmt_update =", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotEqualTo(Date value) {
            addCriterion("gmt_update <>", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateGreaterThan(Date value) {
            addCriterion("gmt_update >", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_update >=", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateLessThan(Date value) {
            addCriterion("gmt_update <", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_update <=", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIn(List<Date> values) {
            addCriterion("gmt_update in", values, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotIn(List<Date> values) {
            addCriterion("gmt_update not in", values, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateBetween(Date value1, Date value2) {
            addCriterion("gmt_update between", value1, value2, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_update not between", value1, value2, "gmtUpdate");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}