package com.cowell.iscm.entityTidb;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class IscmManualRegisterExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Long offset;

    public IscmManualRegisterExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Long offset) {
        this.offset = offset;
    }

    public Long getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andDataTypeIsNull() {
            addCriterion("data_type is null");
            return (Criteria) this;
        }

        public Criteria andDataTypeIsNotNull() {
            addCriterion("data_type is not null");
            return (Criteria) this;
        }

        public Criteria andDataTypeEqualTo(Byte value) {
            addCriterion("data_type =", value, "dataType");
            return (Criteria) this;
        }

        public Criteria andDataTypeNotEqualTo(Byte value) {
            addCriterion("data_type <>", value, "dataType");
            return (Criteria) this;
        }

        public Criteria andDataTypeGreaterThan(Byte value) {
            addCriterion("data_type >", value, "dataType");
            return (Criteria) this;
        }

        public Criteria andDataTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("data_type >=", value, "dataType");
            return (Criteria) this;
        }

        public Criteria andDataTypeLessThan(Byte value) {
            addCriterion("data_type <", value, "dataType");
            return (Criteria) this;
        }

        public Criteria andDataTypeLessThanOrEqualTo(Byte value) {
            addCriterion("data_type <=", value, "dataType");
            return (Criteria) this;
        }

        public Criteria andDataTypeIn(List<Byte> values) {
            addCriterion("data_type in", values, "dataType");
            return (Criteria) this;
        }

        public Criteria andDataTypeNotIn(List<Byte> values) {
            addCriterion("data_type not in", values, "dataType");
            return (Criteria) this;
        }

        public Criteria andDataTypeBetween(Byte value1, Byte value2) {
            addCriterion("data_type between", value1, value2, "dataType");
            return (Criteria) this;
        }

        public Criteria andDataTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("data_type not between", value1, value2, "dataType");
            return (Criteria) this;
        }

        public Criteria andRegisterTypeIsNull() {
            addCriterion("register_type is null");
            return (Criteria) this;
        }

        public Criteria andRegisterTypeIsNotNull() {
            addCriterion("register_type is not null");
            return (Criteria) this;
        }

        public Criteria andRegisterTypeEqualTo(Byte value) {
            addCriterion("register_type =", value, "registerType");
            return (Criteria) this;
        }

        public Criteria andRegisterTypeNotEqualTo(Byte value) {
            addCriterion("register_type <>", value, "registerType");
            return (Criteria) this;
        }

        public Criteria andRegisterTypeGreaterThan(Byte value) {
            addCriterion("register_type >", value, "registerType");
            return (Criteria) this;
        }

        public Criteria andRegisterTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("register_type >=", value, "registerType");
            return (Criteria) this;
        }

        public Criteria andRegisterTypeLessThan(Byte value) {
            addCriterion("register_type <", value, "registerType");
            return (Criteria) this;
        }

        public Criteria andRegisterTypeLessThanOrEqualTo(Byte value) {
            addCriterion("register_type <=", value, "registerType");
            return (Criteria) this;
        }

        public Criteria andRegisterTypeIn(List<Byte> values) {
            addCriterion("register_type in", values, "registerType");
            return (Criteria) this;
        }

        public Criteria andRegisterTypeNotIn(List<Byte> values) {
            addCriterion("register_type not in", values, "registerType");
            return (Criteria) this;
        }

        public Criteria andRegisterTypeBetween(Byte value1, Byte value2) {
            addCriterion("register_type between", value1, value2, "registerType");
            return (Criteria) this;
        }

        public Criteria andRegisterTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("register_type not between", value1, value2, "registerType");
            return (Criteria) this;
        }

        public Criteria andCompanyBdpCodeIsNull() {
            addCriterion("company_bdp_code is null");
            return (Criteria) this;
        }

        public Criteria andCompanyBdpCodeIsNotNull() {
            addCriterion("company_bdp_code is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyBdpCodeEqualTo(String value) {
            addCriterion("company_bdp_code =", value, "companyBdpCode");
            return (Criteria) this;
        }

        public Criteria andCompanyBdpCodeNotEqualTo(String value) {
            addCriterion("company_bdp_code <>", value, "companyBdpCode");
            return (Criteria) this;
        }

        public Criteria andCompanyBdpCodeGreaterThan(String value) {
            addCriterion("company_bdp_code >", value, "companyBdpCode");
            return (Criteria) this;
        }

        public Criteria andCompanyBdpCodeGreaterThanOrEqualTo(String value) {
            addCriterion("company_bdp_code >=", value, "companyBdpCode");
            return (Criteria) this;
        }

        public Criteria andCompanyBdpCodeLessThan(String value) {
            addCriterion("company_bdp_code <", value, "companyBdpCode");
            return (Criteria) this;
        }

        public Criteria andCompanyBdpCodeLessThanOrEqualTo(String value) {
            addCriterion("company_bdp_code <=", value, "companyBdpCode");
            return (Criteria) this;
        }

        public Criteria andCompanyBdpCodeLike(String value) {
            addCriterion("company_bdp_code like", value, "companyBdpCode");
            return (Criteria) this;
        }

        public Criteria andCompanyBdpCodeNotLike(String value) {
            addCriterion("company_bdp_code not like", value, "companyBdpCode");
            return (Criteria) this;
        }

        public Criteria andCompanyBdpCodeIn(List<String> values) {
            addCriterion("company_bdp_code in", values, "companyBdpCode");
            return (Criteria) this;
        }

        public Criteria andCompanyBdpCodeNotIn(List<String> values) {
            addCriterion("company_bdp_code not in", values, "companyBdpCode");
            return (Criteria) this;
        }

        public Criteria andCompanyBdpCodeBetween(String value1, String value2) {
            addCriterion("company_bdp_code between", value1, value2, "companyBdpCode");
            return (Criteria) this;
        }

        public Criteria andCompanyBdpCodeNotBetween(String value1, String value2) {
            addCriterion("company_bdp_code not between", value1, value2, "companyBdpCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIsNull() {
            addCriterion("company_code is null");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIsNotNull() {
            addCriterion("company_code is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeEqualTo(String value) {
            addCriterion("company_code =", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotEqualTo(String value) {
            addCriterion("company_code <>", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeGreaterThan(String value) {
            addCriterion("company_code >", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeGreaterThanOrEqualTo(String value) {
            addCriterion("company_code >=", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLessThan(String value) {
            addCriterion("company_code <", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLessThanOrEqualTo(String value) {
            addCriterion("company_code <=", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLike(String value) {
            addCriterion("company_code like", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotLike(String value) {
            addCriterion("company_code not like", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIn(List<String> values) {
            addCriterion("company_code in", values, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotIn(List<String> values) {
            addCriterion("company_code not in", values, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeBetween(String value1, String value2) {
            addCriterion("company_code between", value1, value2, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotBetween(String value1, String value2) {
            addCriterion("company_code not between", value1, value2, "companyCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeIsNull() {
            addCriterion("store_code is null");
            return (Criteria) this;
        }

        public Criteria andStoreCodeIsNotNull() {
            addCriterion("store_code is not null");
            return (Criteria) this;
        }

        public Criteria andStoreCodeEqualTo(String value) {
            addCriterion("store_code =", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotEqualTo(String value) {
            addCriterion("store_code <>", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeGreaterThan(String value) {
            addCriterion("store_code >", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeGreaterThanOrEqualTo(String value) {
            addCriterion("store_code >=", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeLessThan(String value) {
            addCriterion("store_code <", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeLessThanOrEqualTo(String value) {
            addCriterion("store_code <=", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeLike(String value) {
            addCriterion("store_code like", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotLike(String value) {
            addCriterion("store_code not like", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeIn(List<String> values) {
            addCriterion("store_code in", values, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotIn(List<String> values) {
            addCriterion("store_code not in", values, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeBetween(String value1, String value2) {
            addCriterion("store_code between", value1, value2, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotBetween(String value1, String value2) {
            addCriterion("store_code not between", value1, value2, "storeCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeIsNull() {
            addCriterion("warehouse_code is null");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeIsNotNull() {
            addCriterion("warehouse_code is not null");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeEqualTo(String value) {
            addCriterion("warehouse_code =", value, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeNotEqualTo(String value) {
            addCriterion("warehouse_code <>", value, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeGreaterThan(String value) {
            addCriterion("warehouse_code >", value, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeGreaterThanOrEqualTo(String value) {
            addCriterion("warehouse_code >=", value, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeLessThan(String value) {
            addCriterion("warehouse_code <", value, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeLessThanOrEqualTo(String value) {
            addCriterion("warehouse_code <=", value, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeLike(String value) {
            addCriterion("warehouse_code like", value, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeNotLike(String value) {
            addCriterion("warehouse_code not like", value, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeIn(List<String> values) {
            addCriterion("warehouse_code in", values, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeNotIn(List<String> values) {
            addCriterion("warehouse_code not in", values, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeBetween(String value1, String value2) {
            addCriterion("warehouse_code between", value1, value2, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeNotBetween(String value1, String value2) {
            addCriterion("warehouse_code not between", value1, value2, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseNameIsNull() {
            addCriterion("warehouse_name is null");
            return (Criteria) this;
        }

        public Criteria andWarehouseNameIsNotNull() {
            addCriterion("warehouse_name is not null");
            return (Criteria) this;
        }

        public Criteria andWarehouseNameEqualTo(String value) {
            addCriterion("warehouse_name =", value, "warehouseName");
            return (Criteria) this;
        }

        public Criteria andWarehouseNameNotEqualTo(String value) {
            addCriterion("warehouse_name <>", value, "warehouseName");
            return (Criteria) this;
        }

        public Criteria andWarehouseNameGreaterThan(String value) {
            addCriterion("warehouse_name >", value, "warehouseName");
            return (Criteria) this;
        }

        public Criteria andWarehouseNameGreaterThanOrEqualTo(String value) {
            addCriterion("warehouse_name >=", value, "warehouseName");
            return (Criteria) this;
        }

        public Criteria andWarehouseNameLessThan(String value) {
            addCriterion("warehouse_name <", value, "warehouseName");
            return (Criteria) this;
        }

        public Criteria andWarehouseNameLessThanOrEqualTo(String value) {
            addCriterion("warehouse_name <=", value, "warehouseName");
            return (Criteria) this;
        }

        public Criteria andWarehouseNameLike(String value) {
            addCriterion("warehouse_name like", value, "warehouseName");
            return (Criteria) this;
        }

        public Criteria andWarehouseNameNotLike(String value) {
            addCriterion("warehouse_name not like", value, "warehouseName");
            return (Criteria) this;
        }

        public Criteria andWarehouseNameIn(List<String> values) {
            addCriterion("warehouse_name in", values, "warehouseName");
            return (Criteria) this;
        }

        public Criteria andWarehouseNameNotIn(List<String> values) {
            addCriterion("warehouse_name not in", values, "warehouseName");
            return (Criteria) this;
        }

        public Criteria andWarehouseNameBetween(String value1, String value2) {
            addCriterion("warehouse_name between", value1, value2, "warehouseName");
            return (Criteria) this;
        }

        public Criteria andWarehouseNameNotBetween(String value1, String value2) {
            addCriterion("warehouse_name not between", value1, value2, "warehouseName");
            return (Criteria) this;
        }

        public Criteria andGoodsNoIsNull() {
            addCriterion("goods_no is null");
            return (Criteria) this;
        }

        public Criteria andGoodsNoIsNotNull() {
            addCriterion("goods_no is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsNoEqualTo(String value) {
            addCriterion("goods_no =", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotEqualTo(String value) {
            addCriterion("goods_no <>", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoGreaterThan(String value) {
            addCriterion("goods_no >", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoGreaterThanOrEqualTo(String value) {
            addCriterion("goods_no >=", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoLessThan(String value) {
            addCriterion("goods_no <", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoLessThanOrEqualTo(String value) {
            addCriterion("goods_no <=", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoLike(String value) {
            addCriterion("goods_no like", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotLike(String value) {
            addCriterion("goods_no not like", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoIn(List<String> values) {
            addCriterion("goods_no in", values, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotIn(List<String> values) {
            addCriterion("goods_no not in", values, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoBetween(String value1, String value2) {
            addCriterion("goods_no between", value1, value2, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotBetween(String value1, String value2) {
            addCriterion("goods_no not between", value1, value2, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoIsNull() {
            addCriterion("batch_no is null");
            return (Criteria) this;
        }

        public Criteria andBatchNoIsNotNull() {
            addCriterion("batch_no is not null");
            return (Criteria) this;
        }

        public Criteria andBatchNoEqualTo(String value) {
            addCriterion("batch_no =", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoNotEqualTo(String value) {
            addCriterion("batch_no <>", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoGreaterThan(String value) {
            addCriterion("batch_no >", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoGreaterThanOrEqualTo(String value) {
            addCriterion("batch_no >=", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoLessThan(String value) {
            addCriterion("batch_no <", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoLessThanOrEqualTo(String value) {
            addCriterion("batch_no <=", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoLike(String value) {
            addCriterion("batch_no like", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoNotLike(String value) {
            addCriterion("batch_no not like", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoIn(List<String> values) {
            addCriterion("batch_no in", values, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoNotIn(List<String> values) {
            addCriterion("batch_no not in", values, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoBetween(String value1, String value2) {
            addCriterion("batch_no between", value1, value2, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoNotBetween(String value1, String value2) {
            addCriterion("batch_no not between", value1, value2, "batchNo");
            return (Criteria) this;
        }

        public Criteria andManufacturerIsNull() {
            addCriterion("manufacturer is null");
            return (Criteria) this;
        }

        public Criteria andManufacturerIsNotNull() {
            addCriterion("manufacturer is not null");
            return (Criteria) this;
        }

        public Criteria andManufacturerEqualTo(String value) {
            addCriterion("manufacturer =", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerNotEqualTo(String value) {
            addCriterion("manufacturer <>", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerGreaterThan(String value) {
            addCriterion("manufacturer >", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerGreaterThanOrEqualTo(String value) {
            addCriterion("manufacturer >=", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerLessThan(String value) {
            addCriterion("manufacturer <", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerLessThanOrEqualTo(String value) {
            addCriterion("manufacturer <=", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerLike(String value) {
            addCriterion("manufacturer like", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerNotLike(String value) {
            addCriterion("manufacturer not like", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerIn(List<String> values) {
            addCriterion("manufacturer in", values, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerNotIn(List<String> values) {
            addCriterion("manufacturer not in", values, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerBetween(String value1, String value2) {
            addCriterion("manufacturer between", value1, value2, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerNotBetween(String value1, String value2) {
            addCriterion("manufacturer not between", value1, value2, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andRegisterQuantityIsNull() {
            addCriterion("register_quantity is null");
            return (Criteria) this;
        }

        public Criteria andRegisterQuantityIsNotNull() {
            addCriterion("register_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andRegisterQuantityEqualTo(BigDecimal value) {
            addCriterion("register_quantity =", value, "registerQuantity");
            return (Criteria) this;
        }

        public Criteria andRegisterQuantityNotEqualTo(BigDecimal value) {
            addCriterion("register_quantity <>", value, "registerQuantity");
            return (Criteria) this;
        }

        public Criteria andRegisterQuantityGreaterThan(BigDecimal value) {
            addCriterion("register_quantity >", value, "registerQuantity");
            return (Criteria) this;
        }

        public Criteria andRegisterQuantityGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("register_quantity >=", value, "registerQuantity");
            return (Criteria) this;
        }

        public Criteria andRegisterQuantityLessThan(BigDecimal value) {
            addCriterion("register_quantity <", value, "registerQuantity");
            return (Criteria) this;
        }

        public Criteria andRegisterQuantityLessThanOrEqualTo(BigDecimal value) {
            addCriterion("register_quantity <=", value, "registerQuantity");
            return (Criteria) this;
        }

        public Criteria andRegisterQuantityIn(List<BigDecimal> values) {
            addCriterion("register_quantity in", values, "registerQuantity");
            return (Criteria) this;
        }

        public Criteria andRegisterQuantityNotIn(List<BigDecimal> values) {
            addCriterion("register_quantity not in", values, "registerQuantity");
            return (Criteria) this;
        }

        public Criteria andRegisterQuantityBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("register_quantity between", value1, value2, "registerQuantity");
            return (Criteria) this;
        }

        public Criteria andRegisterQuantityNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("register_quantity not between", value1, value2, "registerQuantity");
            return (Criteria) this;
        }

        public Criteria andNonValidityStockQuantityIsNull() {
            addCriterion("non_validity_stock_quantity is null");
            return (Criteria) this;
        }

        public Criteria andNonValidityStockQuantityIsNotNull() {
            addCriterion("non_validity_stock_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andNonValidityStockQuantityEqualTo(BigDecimal value) {
            addCriterion("non_validity_stock_quantity =", value, "nonValidityStockQuantity");
            return (Criteria) this;
        }

        public Criteria andNonValidityStockQuantityNotEqualTo(BigDecimal value) {
            addCriterion("non_validity_stock_quantity <>", value, "nonValidityStockQuantity");
            return (Criteria) this;
        }

        public Criteria andNonValidityStockQuantityGreaterThan(BigDecimal value) {
            addCriterion("non_validity_stock_quantity >", value, "nonValidityStockQuantity");
            return (Criteria) this;
        }

        public Criteria andNonValidityStockQuantityGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("non_validity_stock_quantity >=", value, "nonValidityStockQuantity");
            return (Criteria) this;
        }

        public Criteria andNonValidityStockQuantityLessThan(BigDecimal value) {
            addCriterion("non_validity_stock_quantity <", value, "nonValidityStockQuantity");
            return (Criteria) this;
        }

        public Criteria andNonValidityStockQuantityLessThanOrEqualTo(BigDecimal value) {
            addCriterion("non_validity_stock_quantity <=", value, "nonValidityStockQuantity");
            return (Criteria) this;
        }

        public Criteria andNonValidityStockQuantityIn(List<BigDecimal> values) {
            addCriterion("non_validity_stock_quantity in", values, "nonValidityStockQuantity");
            return (Criteria) this;
        }

        public Criteria andNonValidityStockQuantityNotIn(List<BigDecimal> values) {
            addCriterion("non_validity_stock_quantity not in", values, "nonValidityStockQuantity");
            return (Criteria) this;
        }

        public Criteria andNonValidityStockQuantityBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("non_validity_stock_quantity between", value1, value2, "nonValidityStockQuantity");
            return (Criteria) this;
        }

        public Criteria andNonValidityStockQuantityNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("non_validity_stock_quantity not between", value1, value2, "nonValidityStockQuantity");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitIsNull() {
            addCriterion("stock_upper_limit is null");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitIsNotNull() {
            addCriterion("stock_upper_limit is not null");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitEqualTo(BigDecimal value) {
            addCriterion("stock_upper_limit =", value, "stockUpperLimit");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitNotEqualTo(BigDecimal value) {
            addCriterion("stock_upper_limit <>", value, "stockUpperLimit");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitGreaterThan(BigDecimal value) {
            addCriterion("stock_upper_limit >", value, "stockUpperLimit");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("stock_upper_limit >=", value, "stockUpperLimit");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitLessThan(BigDecimal value) {
            addCriterion("stock_upper_limit <", value, "stockUpperLimit");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitLessThanOrEqualTo(BigDecimal value) {
            addCriterion("stock_upper_limit <=", value, "stockUpperLimit");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitIn(List<BigDecimal> values) {
            addCriterion("stock_upper_limit in", values, "stockUpperLimit");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitNotIn(List<BigDecimal> values) {
            addCriterion("stock_upper_limit not in", values, "stockUpperLimit");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("stock_upper_limit between", value1, value2, "stockUpperLimit");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("stock_upper_limit not between", value1, value2, "stockUpperLimit");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitIsNull() {
            addCriterion("stock_lower_limit is null");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitIsNotNull() {
            addCriterion("stock_lower_limit is not null");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitEqualTo(BigDecimal value) {
            addCriterion("stock_lower_limit =", value, "stockLowerLimit");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitNotEqualTo(BigDecimal value) {
            addCriterion("stock_lower_limit <>", value, "stockLowerLimit");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitGreaterThan(BigDecimal value) {
            addCriterion("stock_lower_limit >", value, "stockLowerLimit");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("stock_lower_limit >=", value, "stockLowerLimit");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitLessThan(BigDecimal value) {
            addCriterion("stock_lower_limit <", value, "stockLowerLimit");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitLessThanOrEqualTo(BigDecimal value) {
            addCriterion("stock_lower_limit <=", value, "stockLowerLimit");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitIn(List<BigDecimal> values) {
            addCriterion("stock_lower_limit in", values, "stockLowerLimit");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitNotIn(List<BigDecimal> values) {
            addCriterion("stock_lower_limit not in", values, "stockLowerLimit");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("stock_lower_limit between", value1, value2, "stockLowerLimit");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("stock_lower_limit not between", value1, value2, "stockLowerLimit");
            return (Criteria) this;
        }

        public Criteria andNoTaxInventoryCostAmountIsNull() {
            addCriterion("no_tax_inventory_cost_amount is null");
            return (Criteria) this;
        }

        public Criteria andNoTaxInventoryCostAmountIsNotNull() {
            addCriterion("no_tax_inventory_cost_amount is not null");
            return (Criteria) this;
        }

        public Criteria andNoTaxInventoryCostAmountEqualTo(BigDecimal value) {
            addCriterion("no_tax_inventory_cost_amount =", value, "noTaxInventoryCostAmount");
            return (Criteria) this;
        }

        public Criteria andNoTaxInventoryCostAmountNotEqualTo(BigDecimal value) {
            addCriterion("no_tax_inventory_cost_amount <>", value, "noTaxInventoryCostAmount");
            return (Criteria) this;
        }

        public Criteria andNoTaxInventoryCostAmountGreaterThan(BigDecimal value) {
            addCriterion("no_tax_inventory_cost_amount >", value, "noTaxInventoryCostAmount");
            return (Criteria) this;
        }

        public Criteria andNoTaxInventoryCostAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("no_tax_inventory_cost_amount >=", value, "noTaxInventoryCostAmount");
            return (Criteria) this;
        }

        public Criteria andNoTaxInventoryCostAmountLessThan(BigDecimal value) {
            addCriterion("no_tax_inventory_cost_amount <", value, "noTaxInventoryCostAmount");
            return (Criteria) this;
        }

        public Criteria andNoTaxInventoryCostAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("no_tax_inventory_cost_amount <=", value, "noTaxInventoryCostAmount");
            return (Criteria) this;
        }

        public Criteria andNoTaxInventoryCostAmountIn(List<BigDecimal> values) {
            addCriterion("no_tax_inventory_cost_amount in", values, "noTaxInventoryCostAmount");
            return (Criteria) this;
        }

        public Criteria andNoTaxInventoryCostAmountNotIn(List<BigDecimal> values) {
            addCriterion("no_tax_inventory_cost_amount not in", values, "noTaxInventoryCostAmount");
            return (Criteria) this;
        }

        public Criteria andNoTaxInventoryCostAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("no_tax_inventory_cost_amount between", value1, value2, "noTaxInventoryCostAmount");
            return (Criteria) this;
        }

        public Criteria andNoTaxInventoryCostAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("no_tax_inventory_cost_amount not between", value1, value2, "noTaxInventoryCostAmount");
            return (Criteria) this;
        }

        public Criteria andNonSaleDaysIsNull() {
            addCriterion("non_sale_days is null");
            return (Criteria) this;
        }

        public Criteria andNonSaleDaysIsNotNull() {
            addCriterion("non_sale_days is not null");
            return (Criteria) this;
        }

        public Criteria andNonSaleDaysEqualTo(Integer value) {
            addCriterion("non_sale_days =", value, "nonSaleDays");
            return (Criteria) this;
        }

        public Criteria andNonSaleDaysNotEqualTo(Integer value) {
            addCriterion("non_sale_days <>", value, "nonSaleDays");
            return (Criteria) this;
        }

        public Criteria andNonSaleDaysGreaterThan(Integer value) {
            addCriterion("non_sale_days >", value, "nonSaleDays");
            return (Criteria) this;
        }

        public Criteria andNonSaleDaysGreaterThanOrEqualTo(Integer value) {
            addCriterion("non_sale_days >=", value, "nonSaleDays");
            return (Criteria) this;
        }

        public Criteria andNonSaleDaysLessThan(Integer value) {
            addCriterion("non_sale_days <", value, "nonSaleDays");
            return (Criteria) this;
        }

        public Criteria andNonSaleDaysLessThanOrEqualTo(Integer value) {
            addCriterion("non_sale_days <=", value, "nonSaleDays");
            return (Criteria) this;
        }

        public Criteria andNonSaleDaysIn(List<Integer> values) {
            addCriterion("non_sale_days in", values, "nonSaleDays");
            return (Criteria) this;
        }

        public Criteria andNonSaleDaysNotIn(List<Integer> values) {
            addCriterion("non_sale_days not in", values, "nonSaleDays");
            return (Criteria) this;
        }

        public Criteria andNonSaleDaysBetween(Integer value1, Integer value2) {
            addCriterion("non_sale_days between", value1, value2, "nonSaleDays");
            return (Criteria) this;
        }

        public Criteria andNonSaleDaysNotBetween(Integer value1, Integer value2) {
            addCriterion("non_sale_days not between", value1, value2, "nonSaleDays");
            return (Criteria) this;
        }

        public Criteria andSynthesizeAverageDailySalesIsNull() {
            addCriterion("synthesize_average_daily_sales is null");
            return (Criteria) this;
        }

        public Criteria andSynthesizeAverageDailySalesIsNotNull() {
            addCriterion("synthesize_average_daily_sales is not null");
            return (Criteria) this;
        }

        public Criteria andSynthesizeAverageDailySalesEqualTo(BigDecimal value) {
            addCriterion("synthesize_average_daily_sales =", value, "synthesizeAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andSynthesizeAverageDailySalesNotEqualTo(BigDecimal value) {
            addCriterion("synthesize_average_daily_sales <>", value, "synthesizeAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andSynthesizeAverageDailySalesGreaterThan(BigDecimal value) {
            addCriterion("synthesize_average_daily_sales >", value, "synthesizeAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andSynthesizeAverageDailySalesGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("synthesize_average_daily_sales >=", value, "synthesizeAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andSynthesizeAverageDailySalesLessThan(BigDecimal value) {
            addCriterion("synthesize_average_daily_sales <", value, "synthesizeAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andSynthesizeAverageDailySalesLessThanOrEqualTo(BigDecimal value) {
            addCriterion("synthesize_average_daily_sales <=", value, "synthesizeAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andSynthesizeAverageDailySalesIn(List<BigDecimal> values) {
            addCriterion("synthesize_average_daily_sales in", values, "synthesizeAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andSynthesizeAverageDailySalesNotIn(List<BigDecimal> values) {
            addCriterion("synthesize_average_daily_sales not in", values, "synthesizeAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andSynthesizeAverageDailySalesBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("synthesize_average_daily_sales between", value1, value2, "synthesizeAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andSynthesizeAverageDailySalesNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("synthesize_average_daily_sales not between", value1, value2, "synthesizeAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andThirtySalesQuantityIsNull() {
            addCriterion("thirty_sales_quantity is null");
            return (Criteria) this;
        }

        public Criteria andThirtySalesQuantityIsNotNull() {
            addCriterion("thirty_sales_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andThirtySalesQuantityEqualTo(BigDecimal value) {
            addCriterion("thirty_sales_quantity =", value, "thirtySalesQuantity");
            return (Criteria) this;
        }

        public Criteria andThirtySalesQuantityNotEqualTo(BigDecimal value) {
            addCriterion("thirty_sales_quantity <>", value, "thirtySalesQuantity");
            return (Criteria) this;
        }

        public Criteria andThirtySalesQuantityGreaterThan(BigDecimal value) {
            addCriterion("thirty_sales_quantity >", value, "thirtySalesQuantity");
            return (Criteria) this;
        }

        public Criteria andThirtySalesQuantityGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("thirty_sales_quantity >=", value, "thirtySalesQuantity");
            return (Criteria) this;
        }

        public Criteria andThirtySalesQuantityLessThan(BigDecimal value) {
            addCriterion("thirty_sales_quantity <", value, "thirtySalesQuantity");
            return (Criteria) this;
        }

        public Criteria andThirtySalesQuantityLessThanOrEqualTo(BigDecimal value) {
            addCriterion("thirty_sales_quantity <=", value, "thirtySalesQuantity");
            return (Criteria) this;
        }

        public Criteria andThirtySalesQuantityIn(List<BigDecimal> values) {
            addCriterion("thirty_sales_quantity in", values, "thirtySalesQuantity");
            return (Criteria) this;
        }

        public Criteria andThirtySalesQuantityNotIn(List<BigDecimal> values) {
            addCriterion("thirty_sales_quantity not in", values, "thirtySalesQuantity");
            return (Criteria) this;
        }

        public Criteria andThirtySalesQuantityBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("thirty_sales_quantity between", value1, value2, "thirtySalesQuantity");
            return (Criteria) this;
        }

        public Criteria andThirtySalesQuantityNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("thirty_sales_quantity not between", value1, value2, "thirtySalesQuantity");
            return (Criteria) this;
        }

        public Criteria andThirtySalesCountIsNull() {
            addCriterion("thirty_sales_count is null");
            return (Criteria) this;
        }

        public Criteria andThirtySalesCountIsNotNull() {
            addCriterion("thirty_sales_count is not null");
            return (Criteria) this;
        }

        public Criteria andThirtySalesCountEqualTo(Integer value) {
            addCriterion("thirty_sales_count =", value, "thirtySalesCount");
            return (Criteria) this;
        }

        public Criteria andThirtySalesCountNotEqualTo(Integer value) {
            addCriterion("thirty_sales_count <>", value, "thirtySalesCount");
            return (Criteria) this;
        }

        public Criteria andThirtySalesCountGreaterThan(Integer value) {
            addCriterion("thirty_sales_count >", value, "thirtySalesCount");
            return (Criteria) this;
        }

        public Criteria andThirtySalesCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("thirty_sales_count >=", value, "thirtySalesCount");
            return (Criteria) this;
        }

        public Criteria andThirtySalesCountLessThan(Integer value) {
            addCriterion("thirty_sales_count <", value, "thirtySalesCount");
            return (Criteria) this;
        }

        public Criteria andThirtySalesCountLessThanOrEqualTo(Integer value) {
            addCriterion("thirty_sales_count <=", value, "thirtySalesCount");
            return (Criteria) this;
        }

        public Criteria andThirtySalesCountIn(List<Integer> values) {
            addCriterion("thirty_sales_count in", values, "thirtySalesCount");
            return (Criteria) this;
        }

        public Criteria andThirtySalesCountNotIn(List<Integer> values) {
            addCriterion("thirty_sales_count not in", values, "thirtySalesCount");
            return (Criteria) this;
        }

        public Criteria andThirtySalesCountBetween(Integer value1, Integer value2) {
            addCriterion("thirty_sales_count between", value1, value2, "thirtySalesCount");
            return (Criteria) this;
        }

        public Criteria andThirtySalesCountNotBetween(Integer value1, Integer value2) {
            addCriterion("thirty_sales_count not between", value1, value2, "thirtySalesCount");
            return (Criteria) this;
        }

        public Criteria andStockQuantityIsNull() {
            addCriterion("stock_quantity is null");
            return (Criteria) this;
        }

        public Criteria andStockQuantityIsNotNull() {
            addCriterion("stock_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andStockQuantityEqualTo(BigDecimal value) {
            addCriterion("stock_quantity =", value, "stockQuantity");
            return (Criteria) this;
        }

        public Criteria andStockQuantityNotEqualTo(BigDecimal value) {
            addCriterion("stock_quantity <>", value, "stockQuantity");
            return (Criteria) this;
        }

        public Criteria andStockQuantityGreaterThan(BigDecimal value) {
            addCriterion("stock_quantity >", value, "stockQuantity");
            return (Criteria) this;
        }

        public Criteria andStockQuantityGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("stock_quantity >=", value, "stockQuantity");
            return (Criteria) this;
        }

        public Criteria andStockQuantityLessThan(BigDecimal value) {
            addCriterion("stock_quantity <", value, "stockQuantity");
            return (Criteria) this;
        }

        public Criteria andStockQuantityLessThanOrEqualTo(BigDecimal value) {
            addCriterion("stock_quantity <=", value, "stockQuantity");
            return (Criteria) this;
        }

        public Criteria andStockQuantityIn(List<BigDecimal> values) {
            addCriterion("stock_quantity in", values, "stockQuantity");
            return (Criteria) this;
        }

        public Criteria andStockQuantityNotIn(List<BigDecimal> values) {
            addCriterion("stock_quantity not in", values, "stockQuantity");
            return (Criteria) this;
        }

        public Criteria andStockQuantityBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("stock_quantity between", value1, value2, "stockQuantity");
            return (Criteria) this;
        }

        public Criteria andStockQuantityNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("stock_quantity not between", value1, value2, "stockQuantity");
            return (Criteria) this;
        }

        public Criteria andMinDisplayQuantityIsNull() {
            addCriterion("min_display_quantity is null");
            return (Criteria) this;
        }

        public Criteria andMinDisplayQuantityIsNotNull() {
            addCriterion("min_display_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andMinDisplayQuantityEqualTo(BigDecimal value) {
            addCriterion("min_display_quantity =", value, "minDisplayQuantity");
            return (Criteria) this;
        }

        public Criteria andMinDisplayQuantityNotEqualTo(BigDecimal value) {
            addCriterion("min_display_quantity <>", value, "minDisplayQuantity");
            return (Criteria) this;
        }

        public Criteria andMinDisplayQuantityGreaterThan(BigDecimal value) {
            addCriterion("min_display_quantity >", value, "minDisplayQuantity");
            return (Criteria) this;
        }

        public Criteria andMinDisplayQuantityGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("min_display_quantity >=", value, "minDisplayQuantity");
            return (Criteria) this;
        }

        public Criteria andMinDisplayQuantityLessThan(BigDecimal value) {
            addCriterion("min_display_quantity <", value, "minDisplayQuantity");
            return (Criteria) this;
        }

        public Criteria andMinDisplayQuantityLessThanOrEqualTo(BigDecimal value) {
            addCriterion("min_display_quantity <=", value, "minDisplayQuantity");
            return (Criteria) this;
        }

        public Criteria andMinDisplayQuantityIn(List<BigDecimal> values) {
            addCriterion("min_display_quantity in", values, "minDisplayQuantity");
            return (Criteria) this;
        }

        public Criteria andMinDisplayQuantityNotIn(List<BigDecimal> values) {
            addCriterion("min_display_quantity not in", values, "minDisplayQuantity");
            return (Criteria) this;
        }

        public Criteria andMinDisplayQuantityBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("min_display_quantity between", value1, value2, "minDisplayQuantity");
            return (Criteria) this;
        }

        public Criteria andMinDisplayQuantityNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("min_display_quantity not between", value1, value2, "minDisplayQuantity");
            return (Criteria) this;
        }

        public Criteria andExpectSaleDaysIsNull() {
            addCriterion("expect_sale_days is null");
            return (Criteria) this;
        }

        public Criteria andExpectSaleDaysIsNotNull() {
            addCriterion("expect_sale_days is not null");
            return (Criteria) this;
        }

        public Criteria andExpectSaleDaysEqualTo(BigDecimal value) {
            addCriterion("expect_sale_days =", value, "expectSaleDays");
            return (Criteria) this;
        }

        public Criteria andExpectSaleDaysNotEqualTo(BigDecimal value) {
            addCriterion("expect_sale_days <>", value, "expectSaleDays");
            return (Criteria) this;
        }

        public Criteria andExpectSaleDaysGreaterThan(BigDecimal value) {
            addCriterion("expect_sale_days >", value, "expectSaleDays");
            return (Criteria) this;
        }

        public Criteria andExpectSaleDaysGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("expect_sale_days >=", value, "expectSaleDays");
            return (Criteria) this;
        }

        public Criteria andExpectSaleDaysLessThan(BigDecimal value) {
            addCriterion("expect_sale_days <", value, "expectSaleDays");
            return (Criteria) this;
        }

        public Criteria andExpectSaleDaysLessThanOrEqualTo(BigDecimal value) {
            addCriterion("expect_sale_days <=", value, "expectSaleDays");
            return (Criteria) this;
        }

        public Criteria andExpectSaleDaysIn(List<BigDecimal> values) {
            addCriterion("expect_sale_days in", values, "expectSaleDays");
            return (Criteria) this;
        }

        public Criteria andExpectSaleDaysNotIn(List<BigDecimal> values) {
            addCriterion("expect_sale_days not in", values, "expectSaleDays");
            return (Criteria) this;
        }

        public Criteria andExpectSaleDaysBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("expect_sale_days between", value1, value2, "expectSaleDays");
            return (Criteria) this;
        }

        public Criteria andExpectSaleDaysNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("expect_sale_days not between", value1, value2, "expectSaleDays");
            return (Criteria) this;
        }

        public Criteria andDealStatusIsNull() {
            addCriterion("deal_status is null");
            return (Criteria) this;
        }

        public Criteria andDealStatusIsNotNull() {
            addCriterion("deal_status is not null");
            return (Criteria) this;
        }

        public Criteria andDealStatusEqualTo(Byte value) {
            addCriterion("deal_status =", value, "dealStatus");
            return (Criteria) this;
        }

        public Criteria andDealStatusNotEqualTo(Byte value) {
            addCriterion("deal_status <>", value, "dealStatus");
            return (Criteria) this;
        }

        public Criteria andDealStatusGreaterThan(Byte value) {
            addCriterion("deal_status >", value, "dealStatus");
            return (Criteria) this;
        }

        public Criteria andDealStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("deal_status >=", value, "dealStatus");
            return (Criteria) this;
        }

        public Criteria andDealStatusLessThan(Byte value) {
            addCriterion("deal_status <", value, "dealStatus");
            return (Criteria) this;
        }

        public Criteria andDealStatusLessThanOrEqualTo(Byte value) {
            addCriterion("deal_status <=", value, "dealStatus");
            return (Criteria) this;
        }

        public Criteria andDealStatusIn(List<Byte> values) {
            addCriterion("deal_status in", values, "dealStatus");
            return (Criteria) this;
        }

        public Criteria andDealStatusNotIn(List<Byte> values) {
            addCriterion("deal_status not in", values, "dealStatus");
            return (Criteria) this;
        }

        public Criteria andDealStatusBetween(Byte value1, Byte value2) {
            addCriterion("deal_status between", value1, value2, "dealStatus");
            return (Criteria) this;
        }

        public Criteria andDealStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("deal_status not between", value1, value2, "dealStatus");
            return (Criteria) this;
        }

        public Criteria andReturnStatusIsNull() {
            addCriterion("return_status is null");
            return (Criteria) this;
        }

        public Criteria andReturnStatusIsNotNull() {
            addCriterion("return_status is not null");
            return (Criteria) this;
        }

        public Criteria andReturnStatusEqualTo(Byte value) {
            addCriterion("return_status =", value, "returnStatus");
            return (Criteria) this;
        }

        public Criteria andReturnStatusNotEqualTo(Byte value) {
            addCriterion("return_status <>", value, "returnStatus");
            return (Criteria) this;
        }

        public Criteria andReturnStatusGreaterThan(Byte value) {
            addCriterion("return_status >", value, "returnStatus");
            return (Criteria) this;
        }

        public Criteria andReturnStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("return_status >=", value, "returnStatus");
            return (Criteria) this;
        }

        public Criteria andReturnStatusLessThan(Byte value) {
            addCriterion("return_status <", value, "returnStatus");
            return (Criteria) this;
        }

        public Criteria andReturnStatusLessThanOrEqualTo(Byte value) {
            addCriterion("return_status <=", value, "returnStatus");
            return (Criteria) this;
        }

        public Criteria andReturnStatusIn(List<Byte> values) {
            addCriterion("return_status in", values, "returnStatus");
            return (Criteria) this;
        }

        public Criteria andReturnStatusNotIn(List<Byte> values) {
            addCriterion("return_status not in", values, "returnStatus");
            return (Criteria) this;
        }

        public Criteria andReturnStatusBetween(Byte value1, Byte value2) {
            addCriterion("return_status between", value1, value2, "returnStatus");
            return (Criteria) this;
        }

        public Criteria andReturnStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("return_status not between", value1, value2, "returnStatus");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("`status` is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("`status` is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Byte value) {
            addCriterion("`status` =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Byte value) {
            addCriterion("`status` <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Byte value) {
            addCriterion("`status` >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("`status` >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Byte value) {
            addCriterion("`status` <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Byte value) {
            addCriterion("`status` <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Byte> values) {
            addCriterion("`status` in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Byte> values) {
            addCriterion("`status` not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Byte value1, Byte value2) {
            addCriterion("`status` between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("`status` not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIsNull() {
            addCriterion("gmt_update is null");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIsNotNull() {
            addCriterion("gmt_update is not null");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateEqualTo(Date value) {
            addCriterion("gmt_update =", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotEqualTo(Date value) {
            addCriterion("gmt_update <>", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateGreaterThan(Date value) {
            addCriterion("gmt_update >", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_update >=", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateLessThan(Date value) {
            addCriterion("gmt_update <", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_update <=", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIn(List<Date> values) {
            addCriterion("gmt_update in", values, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotIn(List<Date> values) {
            addCriterion("gmt_update not in", values, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateBetween(Date value1, Date value2) {
            addCriterion("gmt_update between", value1, value2, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_update not between", value1, value2, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andExtendIsNull() {
            addCriterion("extend is null");
            return (Criteria) this;
        }

        public Criteria andExtendIsNotNull() {
            addCriterion("extend is not null");
            return (Criteria) this;
        }

        public Criteria andExtendEqualTo(String value) {
            addCriterion("extend =", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotEqualTo(String value) {
            addCriterion("extend <>", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendGreaterThan(String value) {
            addCriterion("extend >", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendGreaterThanOrEqualTo(String value) {
            addCriterion("extend >=", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLessThan(String value) {
            addCriterion("extend <", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLessThanOrEqualTo(String value) {
            addCriterion("extend <=", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLike(String value) {
            addCriterion("extend like", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotLike(String value) {
            addCriterion("extend not like", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendIn(List<String> values) {
            addCriterion("extend in", values, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotIn(List<String> values) {
            addCriterion("extend not in", values, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendBetween(String value1, String value2) {
            addCriterion("extend between", value1, value2, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotBetween(String value1, String value2) {
            addCriterion("extend not between", value1, value2, "extend");
            return (Criteria) this;
        }

        public Criteria andVersionIsNull() {
            addCriterion("version is null");
            return (Criteria) this;
        }

        public Criteria andVersionIsNotNull() {
            addCriterion("version is not null");
            return (Criteria) this;
        }

        public Criteria andVersionEqualTo(Integer value) {
            addCriterion("version =", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotEqualTo(Integer value) {
            addCriterion("version <>", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThan(Integer value) {
            addCriterion("version >", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThanOrEqualTo(Integer value) {
            addCriterion("version >=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThan(Integer value) {
            addCriterion("version <", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThanOrEqualTo(Integer value) {
            addCriterion("version <=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionIn(List<Integer> values) {
            addCriterion("version in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotIn(List<Integer> values) {
            addCriterion("version not in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionBetween(Integer value1, Integer value2) {
            addCriterion("version between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotBetween(Integer value1, Integer value2) {
            addCriterion("version not between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("created_by is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("created_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(Long value) {
            addCriterion("created_by =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(Long value) {
            addCriterion("created_by <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(Long value) {
            addCriterion("created_by >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(Long value) {
            addCriterion("created_by >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(Long value) {
            addCriterion("created_by <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(Long value) {
            addCriterion("created_by <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<Long> values) {
            addCriterion("created_by in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<Long> values) {
            addCriterion("created_by not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(Long value1, Long value2) {
            addCriterion("created_by between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(Long value1, Long value2) {
            addCriterion("created_by not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedNameIsNull() {
            addCriterion("created_name is null");
            return (Criteria) this;
        }

        public Criteria andCreatedNameIsNotNull() {
            addCriterion("created_name is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedNameEqualTo(String value) {
            addCriterion("created_name =", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotEqualTo(String value) {
            addCriterion("created_name <>", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameGreaterThan(String value) {
            addCriterion("created_name >", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameGreaterThanOrEqualTo(String value) {
            addCriterion("created_name >=", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameLessThan(String value) {
            addCriterion("created_name <", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameLessThanOrEqualTo(String value) {
            addCriterion("created_name <=", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameLike(String value) {
            addCriterion("created_name like", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotLike(String value) {
            addCriterion("created_name not like", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameIn(List<String> values) {
            addCriterion("created_name in", values, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotIn(List<String> values) {
            addCriterion("created_name not in", values, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameBetween(String value1, String value2) {
            addCriterion("created_name between", value1, value2, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotBetween(String value1, String value2) {
            addCriterion("created_name not between", value1, value2, "createdName");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNull() {
            addCriterion("updated_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNotNull() {
            addCriterion("updated_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByEqualTo(Long value) {
            addCriterion("updated_by =", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotEqualTo(Long value) {
            addCriterion("updated_by <>", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThan(Long value) {
            addCriterion("updated_by >", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThanOrEqualTo(Long value) {
            addCriterion("updated_by >=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThan(Long value) {
            addCriterion("updated_by <", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThanOrEqualTo(Long value) {
            addCriterion("updated_by <=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIn(List<Long> values) {
            addCriterion("updated_by in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotIn(List<Long> values) {
            addCriterion("updated_by not in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByBetween(Long value1, Long value2) {
            addCriterion("updated_by between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotBetween(Long value1, Long value2) {
            addCriterion("updated_by not between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameIsNull() {
            addCriterion("updated_name is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameIsNotNull() {
            addCriterion("updated_name is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameEqualTo(String value) {
            addCriterion("updated_name =", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotEqualTo(String value) {
            addCriterion("updated_name <>", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameGreaterThan(String value) {
            addCriterion("updated_name >", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameGreaterThanOrEqualTo(String value) {
            addCriterion("updated_name >=", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameLessThan(String value) {
            addCriterion("updated_name <", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameLessThanOrEqualTo(String value) {
            addCriterion("updated_name <=", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameLike(String value) {
            addCriterion("updated_name like", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotLike(String value) {
            addCriterion("updated_name not like", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameIn(List<String> values) {
            addCriterion("updated_name in", values, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotIn(List<String> values) {
            addCriterion("updated_name not in", values, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameBetween(String value1, String value2) {
            addCriterion("updated_name between", value1, value2, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotBetween(String value1, String value2) {
            addCriterion("updated_name not between", value1, value2, "updatedName");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}