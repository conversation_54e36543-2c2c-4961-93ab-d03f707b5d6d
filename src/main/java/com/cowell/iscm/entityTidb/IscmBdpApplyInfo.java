package com.cowell.iscm.entityTidb;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> bdp请货表
 */
public class IscmBdpApplyInfo implements Serializable {
    /**
     * 无业务逻辑主键
     */
    private Long id;

    /**
     * 申请单号
     */
    private String applyNo;

    /**
     * 申请单行号
     */
    private String applyLine;

    /**
     * 公司编码
     */
    private String companyCode;

    /**
     * 门店编码
     */
    private String storeCode;

    /**
     * 合单日期
     */
    private Date applyDate;

    /**
     * 请货类型 1 DVC 2 bdp 3 其他
     */
    private Byte dataOriginType;

    /**
     * 商品编码
     */
    private String goodsNo;

    /**
     * 请货商品类型 1:常规品 2:低动销 3:促销品
     */
    private Byte applyGoodsType;

    /**
     * 请货数量
     */
    private BigDecimal applyTotal;

    /**
     * 变量1 非合格库存
     */
    private BigDecimal unqualifiedAwaitStock;

    /**
     * 变量2 在途库存
     */
    private BigDecimal transitStock;

    /**
     * 总库存
     */
    private BigDecimal stock;

    /**
     * 合格锁定库存
     */
    private BigDecimal lockStock;

    /**
     * 不合格库存
     */
    private BigDecimal unqualifiedStock;

    /**
     * 门店请货在途库存
     */
    private BigDecimal applyTransitStock;

    /**
     * 调入在途库存
     */
    private BigDecimal inTransitStock;

    /**
     * 铺货在途库存
     */
    private BigDecimal distrTransitStock;

    /**
     * 是否特管
     */
    private String specialCtrl;

    /**
     * 特管商品前30天请货数量
     */
    private BigDecimal specialThirtyDaysQty;

    /**
     * 商品等级
     */
    private String goodsLevel;

    /**
     * 库存上限天数
     */
    private Integer stockUpperLimitDays;

    /**
     * 库存下限天数
     */
    private Integer stockLowerLimitDays;

    /**
     * bdp日均销
     */
    private BigDecimal bdpAverageDailySales;

    /**
     * 最小陈列量
     */
    private BigDecimal minDisplayQty;

    /**
     * 库存上限
     */
    private BigDecimal stockUpperLimit;

    /**
     * 库存下限
     */
    private BigDecimal stockLowerLimit;

    /**
     * 可用库存
     */
    private BigDecimal buyStock;

    /**
     * 请货前可销天数
     */
    private BigDecimal saleDaysBefore;

    /**
     * 请货后可销天数
     */
    private BigDecimal saleDaysAfter;

    /**
     * 近30天销量
     */
    private BigDecimal thirtyDaysSales;

    /**
     * 近90天销量
     */
    private BigDecimal ninetyDaysSales;

    /**
     * 企业请货总量
     */
    private Long companyApplyTotal;

    /**
     * 门店请货总量
     */
    private Long storeApplyTotal;

    /**
     * 1:不处理 2:四舍五入 3:向上取整 4:参考海典GJ9084 5:参考ISCM
     */
    private String middlePackageSwitch;

    /**
     * 中包装数量
     */
    private BigDecimal middlePackageQty;

    /**
     * 中码组标识 0否 1是
     */
    private Byte middleCodeFlag;

    /**
     * 起请比例
     */
    private BigDecimal applyRatio;

    /**
     * 子类id
     */
    private Long categoryId;

    /**
     * 采购类型 1:全国统采,2:平台集采,3:连锁地采,4:门店采购,5:统采分购,6:集采分购,7:品牌自采,8:互医-统谈统采,9:新零售自采,10:中参-统谈统采,11:中参-统谈分采，12:加盟店采，13互医-统谈分采
     */
    private Integer purchaseType;

    /**
     * 采购渠道 A:门店店采 B:诊所请货
     */
    private String purchaseChannel;

    /**
     * 上级DC编码
     */
    private String warehouseCode;

    /**
     * 推荐原因
     */
    private String recommendReason;

    /**
     * 提成规则名称
     */
    private String promotionName;

    /**
     * 奖励方式
     */
    private String promotionWay;

    /**
     * 奖励类型
     */
    private String thresholdInfo;

    /**
     * 奖励
     */
    private String favInfo;

    /**
     * 是否新成分 0否 1是
     */
    private Byte compositeNew;

    /**
     * 榜样店历史30天销售数量
     */
    private BigDecimal thirtySalesQuantity;

    /**
     * 促销名称
     */
    private String promotionTitle;

    /**
     * 促销开始日期
     */
    private String promotionStartDate;

    /**
     * 促销结束日期
     */
    private String promotionEndDate;

    /**
     * 好品推荐处理状态 1:采纳 2:拒绝
     */
    private Byte dealSuggest;

    /**
     * 门店属性 0 直营店 1加盟店
     */
    private Byte storeAttr;

    /**
     * 近3天销售数量
     */
    private BigDecimal threeDaysSales;

    /**
     * 请配周期
     */
    private String deliverycycleCode;

    /**
     * 是否新品（0否 1是）
     */
    private Byte newProduct;

    /**
     * 削减商品类型 1：组货必备品 2：AA+品 3：AA品 4：品类旗舰品
     */
    private Integer goodsCutType;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新时间
     */
    private Date gmtUpdate;

    /**
     * 原因
     */
    private String reason;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getApplyNo() {
        return applyNo;
    }

    public void setApplyNo(String applyNo) {
        this.applyNo = applyNo;
    }

    public String getApplyLine() {
        return applyLine;
    }

    public void setApplyLine(String applyLine) {
        this.applyLine = applyLine;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getStoreCode() {
        return storeCode;
    }

    public void setStoreCode(String storeCode) {
        this.storeCode = storeCode;
    }

    public Date getApplyDate() {
        return applyDate;
    }

    public void setApplyDate(Date applyDate) {
        this.applyDate = applyDate;
    }

    public Byte getDataOriginType() {
        return dataOriginType;
    }

    public void setDataOriginType(Byte dataOriginType) {
        this.dataOriginType = dataOriginType;
    }

    public String getGoodsNo() {
        return goodsNo;
    }

    public void setGoodsNo(String goodsNo) {
        this.goodsNo = goodsNo;
    }

    public Byte getApplyGoodsType() {
        return applyGoodsType;
    }

    public void setApplyGoodsType(Byte applyGoodsType) {
        this.applyGoodsType = applyGoodsType;
    }

    public BigDecimal getApplyTotal() {
        return applyTotal;
    }

    public void setApplyTotal(BigDecimal applyTotal) {
        this.applyTotal = applyTotal;
    }

    public BigDecimal getUnqualifiedAwaitStock() {
        return unqualifiedAwaitStock;
    }

    public void setUnqualifiedAwaitStock(BigDecimal unqualifiedAwaitStock) {
        this.unqualifiedAwaitStock = unqualifiedAwaitStock;
    }

    public BigDecimal getTransitStock() {
        return transitStock;
    }

    public void setTransitStock(BigDecimal transitStock) {
        this.transitStock = transitStock;
    }

    public BigDecimal getStock() {
        return stock;
    }

    public void setStock(BigDecimal stock) {
        this.stock = stock;
    }

    public BigDecimal getLockStock() {
        return lockStock;
    }

    public void setLockStock(BigDecimal lockStock) {
        this.lockStock = lockStock;
    }

    public BigDecimal getUnqualifiedStock() {
        return unqualifiedStock;
    }

    public void setUnqualifiedStock(BigDecimal unqualifiedStock) {
        this.unqualifiedStock = unqualifiedStock;
    }

    public BigDecimal getApplyTransitStock() {
        return applyTransitStock;
    }

    public void setApplyTransitStock(BigDecimal applyTransitStock) {
        this.applyTransitStock = applyTransitStock;
    }

    public BigDecimal getInTransitStock() {
        return inTransitStock;
    }

    public void setInTransitStock(BigDecimal inTransitStock) {
        this.inTransitStock = inTransitStock;
    }

    public BigDecimal getDistrTransitStock() {
        return distrTransitStock;
    }

    public void setDistrTransitStock(BigDecimal distrTransitStock) {
        this.distrTransitStock = distrTransitStock;
    }

    public String getSpecialCtrl() {
        return specialCtrl;
    }

    public void setSpecialCtrl(String specialCtrl) {
        this.specialCtrl = specialCtrl;
    }

    public BigDecimal getSpecialThirtyDaysQty() {
        return specialThirtyDaysQty;
    }

    public void setSpecialThirtyDaysQty(BigDecimal specialThirtyDaysQty) {
        this.specialThirtyDaysQty = specialThirtyDaysQty;
    }

    public String getGoodsLevel() {
        return goodsLevel;
    }

    public void setGoodsLevel(String goodsLevel) {
        this.goodsLevel = goodsLevel;
    }

    public Integer getStockUpperLimitDays() {
        return stockUpperLimitDays;
    }

    public void setStockUpperLimitDays(Integer stockUpperLimitDays) {
        this.stockUpperLimitDays = stockUpperLimitDays;
    }

    public Integer getStockLowerLimitDays() {
        return stockLowerLimitDays;
    }

    public void setStockLowerLimitDays(Integer stockLowerLimitDays) {
        this.stockLowerLimitDays = stockLowerLimitDays;
    }

    public BigDecimal getBdpAverageDailySales() {
        return bdpAverageDailySales;
    }

    public void setBdpAverageDailySales(BigDecimal bdpAverageDailySales) {
        this.bdpAverageDailySales = bdpAverageDailySales;
    }

    public BigDecimal getMinDisplayQty() {
        return minDisplayQty;
    }

    public void setMinDisplayQty(BigDecimal minDisplayQty) {
        this.minDisplayQty = minDisplayQty;
    }

    public BigDecimal getStockUpperLimit() {
        return stockUpperLimit;
    }

    public void setStockUpperLimit(BigDecimal stockUpperLimit) {
        this.stockUpperLimit = stockUpperLimit;
    }

    public BigDecimal getStockLowerLimit() {
        return stockLowerLimit;
    }

    public void setStockLowerLimit(BigDecimal stockLowerLimit) {
        this.stockLowerLimit = stockLowerLimit;
    }

    public BigDecimal getBuyStock() {
        return buyStock;
    }

    public void setBuyStock(BigDecimal buyStock) {
        this.buyStock = buyStock;
    }

    public BigDecimal getSaleDaysBefore() {
        return saleDaysBefore;
    }

    public void setSaleDaysBefore(BigDecimal saleDaysBefore) {
        this.saleDaysBefore = saleDaysBefore;
    }

    public BigDecimal getSaleDaysAfter() {
        return saleDaysAfter;
    }

    public void setSaleDaysAfter(BigDecimal saleDaysAfter) {
        this.saleDaysAfter = saleDaysAfter;
    }

    public BigDecimal getThirtyDaysSales() {
        return thirtyDaysSales;
    }

    public void setThirtyDaysSales(BigDecimal thirtyDaysSales) {
        this.thirtyDaysSales = thirtyDaysSales;
    }

    public BigDecimal getNinetyDaysSales() {
        return ninetyDaysSales;
    }

    public void setNinetyDaysSales(BigDecimal ninetyDaysSales) {
        this.ninetyDaysSales = ninetyDaysSales;
    }

    public Long getCompanyApplyTotal() {
        return companyApplyTotal;
    }

    public void setCompanyApplyTotal(Long companyApplyTotal) {
        this.companyApplyTotal = companyApplyTotal;
    }

    public Long getStoreApplyTotal() {
        return storeApplyTotal;
    }

    public void setStoreApplyTotal(Long storeApplyTotal) {
        this.storeApplyTotal = storeApplyTotal;
    }

    public String getMiddlePackageSwitch() {
        return middlePackageSwitch;
    }

    public void setMiddlePackageSwitch(String middlePackageSwitch) {
        this.middlePackageSwitch = middlePackageSwitch;
    }

    public BigDecimal getMiddlePackageQty() {
        return middlePackageQty;
    }

    public void setMiddlePackageQty(BigDecimal middlePackageQty) {
        this.middlePackageQty = middlePackageQty;
    }

    public Byte getMiddleCodeFlag() {
        return middleCodeFlag;
    }

    public void setMiddleCodeFlag(Byte middleCodeFlag) {
        this.middleCodeFlag = middleCodeFlag;
    }

    public BigDecimal getApplyRatio() {
        return applyRatio;
    }

    public void setApplyRatio(BigDecimal applyRatio) {
        this.applyRatio = applyRatio;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public Integer getPurchaseType() {
        return purchaseType;
    }

    public void setPurchaseType(Integer purchaseType) {
        this.purchaseType = purchaseType;
    }

    public String getPurchaseChannel() {
        return purchaseChannel;
    }

    public void setPurchaseChannel(String purchaseChannel) {
        this.purchaseChannel = purchaseChannel;
    }

    public String getWarehouseCode() {
        return warehouseCode;
    }

    public void setWarehouseCode(String warehouseCode) {
        this.warehouseCode = warehouseCode;
    }

    public String getRecommendReason() {
        return recommendReason;
    }

    public void setRecommendReason(String recommendReason) {
        this.recommendReason = recommendReason;
    }

    public String getPromotionName() {
        return promotionName;
    }

    public void setPromotionName(String promotionName) {
        this.promotionName = promotionName;
    }

    public String getPromotionWay() {
        return promotionWay;
    }

    public void setPromotionWay(String promotionWay) {
        this.promotionWay = promotionWay;
    }

    public String getThresholdInfo() {
        return thresholdInfo;
    }

    public void setThresholdInfo(String thresholdInfo) {
        this.thresholdInfo = thresholdInfo;
    }

    public String getFavInfo() {
        return favInfo;
    }

    public void setFavInfo(String favInfo) {
        this.favInfo = favInfo;
    }

    public Byte getCompositeNew() {
        return compositeNew;
    }

    public void setCompositeNew(Byte compositeNew) {
        this.compositeNew = compositeNew;
    }

    public BigDecimal getThirtySalesQuantity() {
        return thirtySalesQuantity;
    }

    public void setThirtySalesQuantity(BigDecimal thirtySalesQuantity) {
        this.thirtySalesQuantity = thirtySalesQuantity;
    }

    public String getPromotionTitle() {
        return promotionTitle;
    }

    public void setPromotionTitle(String promotionTitle) {
        this.promotionTitle = promotionTitle;
    }

    public String getPromotionStartDate() {
        return promotionStartDate;
    }

    public void setPromotionStartDate(String promotionStartDate) {
        this.promotionStartDate = promotionStartDate;
    }

    public String getPromotionEndDate() {
        return promotionEndDate;
    }

    public void setPromotionEndDate(String promotionEndDate) {
        this.promotionEndDate = promotionEndDate;
    }

    public Byte getDealSuggest() {
        return dealSuggest;
    }

    public void setDealSuggest(Byte dealSuggest) {
        this.dealSuggest = dealSuggest;
    }

    public Byte getStoreAttr() {
        return storeAttr;
    }

    public void setStoreAttr(Byte storeAttr) {
        this.storeAttr = storeAttr;
    }

    public BigDecimal getThreeDaysSales() {
        return threeDaysSales;
    }

    public void setThreeDaysSales(BigDecimal threeDaysSales) {
        this.threeDaysSales = threeDaysSales;
    }

    public String getDeliverycycleCode() {
        return deliverycycleCode;
    }

    public void setDeliverycycleCode(String deliverycycleCode) {
        this.deliverycycleCode = deliverycycleCode;
    }

    public Byte getNewProduct() {
        return newProduct;
    }

    public void setNewProduct(Byte newProduct) {
        this.newProduct = newProduct;
    }

    public Integer getGoodsCutType() {
        return goodsCutType;
    }

    public void setGoodsCutType(Integer goodsCutType) {
        this.goodsCutType = goodsCutType;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtUpdate() {
        return gmtUpdate;
    }

    public void setGmtUpdate(Date gmtUpdate) {
        this.gmtUpdate = gmtUpdate;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        IscmBdpApplyInfo other = (IscmBdpApplyInfo) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getApplyNo() == null ? other.getApplyNo() == null : this.getApplyNo().equals(other.getApplyNo()))
            && (this.getApplyLine() == null ? other.getApplyLine() == null : this.getApplyLine().equals(other.getApplyLine()))
            && (this.getCompanyCode() == null ? other.getCompanyCode() == null : this.getCompanyCode().equals(other.getCompanyCode()))
            && (this.getStoreCode() == null ? other.getStoreCode() == null : this.getStoreCode().equals(other.getStoreCode()))
            && (this.getApplyDate() == null ? other.getApplyDate() == null : this.getApplyDate().equals(other.getApplyDate()))
            && (this.getDataOriginType() == null ? other.getDataOriginType() == null : this.getDataOriginType().equals(other.getDataOriginType()))
            && (this.getGoodsNo() == null ? other.getGoodsNo() == null : this.getGoodsNo().equals(other.getGoodsNo()))
            && (this.getApplyGoodsType() == null ? other.getApplyGoodsType() == null : this.getApplyGoodsType().equals(other.getApplyGoodsType()))
            && (this.getApplyTotal() == null ? other.getApplyTotal() == null : this.getApplyTotal().equals(other.getApplyTotal()))
            && (this.getUnqualifiedAwaitStock() == null ? other.getUnqualifiedAwaitStock() == null : this.getUnqualifiedAwaitStock().equals(other.getUnqualifiedAwaitStock()))
            && (this.getTransitStock() == null ? other.getTransitStock() == null : this.getTransitStock().equals(other.getTransitStock()))
            && (this.getStock() == null ? other.getStock() == null : this.getStock().equals(other.getStock()))
            && (this.getLockStock() == null ? other.getLockStock() == null : this.getLockStock().equals(other.getLockStock()))
            && (this.getUnqualifiedStock() == null ? other.getUnqualifiedStock() == null : this.getUnqualifiedStock().equals(other.getUnqualifiedStock()))
            && (this.getApplyTransitStock() == null ? other.getApplyTransitStock() == null : this.getApplyTransitStock().equals(other.getApplyTransitStock()))
            && (this.getInTransitStock() == null ? other.getInTransitStock() == null : this.getInTransitStock().equals(other.getInTransitStock()))
            && (this.getDistrTransitStock() == null ? other.getDistrTransitStock() == null : this.getDistrTransitStock().equals(other.getDistrTransitStock()))
            && (this.getSpecialCtrl() == null ? other.getSpecialCtrl() == null : this.getSpecialCtrl().equals(other.getSpecialCtrl()))
            && (this.getSpecialThirtyDaysQty() == null ? other.getSpecialThirtyDaysQty() == null : this.getSpecialThirtyDaysQty().equals(other.getSpecialThirtyDaysQty()))
            && (this.getGoodsLevel() == null ? other.getGoodsLevel() == null : this.getGoodsLevel().equals(other.getGoodsLevel()))
            && (this.getStockUpperLimitDays() == null ? other.getStockUpperLimitDays() == null : this.getStockUpperLimitDays().equals(other.getStockUpperLimitDays()))
            && (this.getStockLowerLimitDays() == null ? other.getStockLowerLimitDays() == null : this.getStockLowerLimitDays().equals(other.getStockLowerLimitDays()))
            && (this.getBdpAverageDailySales() == null ? other.getBdpAverageDailySales() == null : this.getBdpAverageDailySales().equals(other.getBdpAverageDailySales()))
            && (this.getMinDisplayQty() == null ? other.getMinDisplayQty() == null : this.getMinDisplayQty().equals(other.getMinDisplayQty()))
            && (this.getStockUpperLimit() == null ? other.getStockUpperLimit() == null : this.getStockUpperLimit().equals(other.getStockUpperLimit()))
            && (this.getStockLowerLimit() == null ? other.getStockLowerLimit() == null : this.getStockLowerLimit().equals(other.getStockLowerLimit()))
            && (this.getBuyStock() == null ? other.getBuyStock() == null : this.getBuyStock().equals(other.getBuyStock()))
            && (this.getSaleDaysBefore() == null ? other.getSaleDaysBefore() == null : this.getSaleDaysBefore().equals(other.getSaleDaysBefore()))
            && (this.getSaleDaysAfter() == null ? other.getSaleDaysAfter() == null : this.getSaleDaysAfter().equals(other.getSaleDaysAfter()))
            && (this.getThirtyDaysSales() == null ? other.getThirtyDaysSales() == null : this.getThirtyDaysSales().equals(other.getThirtyDaysSales()))
            && (this.getNinetyDaysSales() == null ? other.getNinetyDaysSales() == null : this.getNinetyDaysSales().equals(other.getNinetyDaysSales()))
            && (this.getCompanyApplyTotal() == null ? other.getCompanyApplyTotal() == null : this.getCompanyApplyTotal().equals(other.getCompanyApplyTotal()))
            && (this.getStoreApplyTotal() == null ? other.getStoreApplyTotal() == null : this.getStoreApplyTotal().equals(other.getStoreApplyTotal()))
            && (this.getMiddlePackageSwitch() == null ? other.getMiddlePackageSwitch() == null : this.getMiddlePackageSwitch().equals(other.getMiddlePackageSwitch()))
            && (this.getMiddlePackageQty() == null ? other.getMiddlePackageQty() == null : this.getMiddlePackageQty().equals(other.getMiddlePackageQty()))
            && (this.getMiddleCodeFlag() == null ? other.getMiddleCodeFlag() == null : this.getMiddleCodeFlag().equals(other.getMiddleCodeFlag()))
            && (this.getApplyRatio() == null ? other.getApplyRatio() == null : this.getApplyRatio().equals(other.getApplyRatio()))
            && (this.getCategoryId() == null ? other.getCategoryId() == null : this.getCategoryId().equals(other.getCategoryId()))
            && (this.getPurchaseType() == null ? other.getPurchaseType() == null : this.getPurchaseType().equals(other.getPurchaseType()))
            && (this.getPurchaseChannel() == null ? other.getPurchaseChannel() == null : this.getPurchaseChannel().equals(other.getPurchaseChannel()))
            && (this.getWarehouseCode() == null ? other.getWarehouseCode() == null : this.getWarehouseCode().equals(other.getWarehouseCode()))
            && (this.getRecommendReason() == null ? other.getRecommendReason() == null : this.getRecommendReason().equals(other.getRecommendReason()))
            && (this.getPromotionName() == null ? other.getPromotionName() == null : this.getPromotionName().equals(other.getPromotionName()))
            && (this.getPromotionWay() == null ? other.getPromotionWay() == null : this.getPromotionWay().equals(other.getPromotionWay()))
            && (this.getThresholdInfo() == null ? other.getThresholdInfo() == null : this.getThresholdInfo().equals(other.getThresholdInfo()))
            && (this.getFavInfo() == null ? other.getFavInfo() == null : this.getFavInfo().equals(other.getFavInfo()))
            && (this.getCompositeNew() == null ? other.getCompositeNew() == null : this.getCompositeNew().equals(other.getCompositeNew()))
            && (this.getThirtySalesQuantity() == null ? other.getThirtySalesQuantity() == null : this.getThirtySalesQuantity().equals(other.getThirtySalesQuantity()))
            && (this.getPromotionTitle() == null ? other.getPromotionTitle() == null : this.getPromotionTitle().equals(other.getPromotionTitle()))
            && (this.getPromotionStartDate() == null ? other.getPromotionStartDate() == null : this.getPromotionStartDate().equals(other.getPromotionStartDate()))
            && (this.getPromotionEndDate() == null ? other.getPromotionEndDate() == null : this.getPromotionEndDate().equals(other.getPromotionEndDate()))
            && (this.getDealSuggest() == null ? other.getDealSuggest() == null : this.getDealSuggest().equals(other.getDealSuggest()))
            && (this.getStoreAttr() == null ? other.getStoreAttr() == null : this.getStoreAttr().equals(other.getStoreAttr()))
            && (this.getThreeDaysSales() == null ? other.getThreeDaysSales() == null : this.getThreeDaysSales().equals(other.getThreeDaysSales()))
            && (this.getDeliverycycleCode() == null ? other.getDeliverycycleCode() == null : this.getDeliverycycleCode().equals(other.getDeliverycycleCode()))
            && (this.getNewProduct() == null ? other.getNewProduct() == null : this.getNewProduct().equals(other.getNewProduct()))
            && (this.getGoodsCutType() == null ? other.getGoodsCutType() == null : this.getGoodsCutType().equals(other.getGoodsCutType()))
            && (this.getGmtCreate() == null ? other.getGmtCreate() == null : this.getGmtCreate().equals(other.getGmtCreate()))
            && (this.getGmtUpdate() == null ? other.getGmtUpdate() == null : this.getGmtUpdate().equals(other.getGmtUpdate()))
            && (this.getReason() == null ? other.getReason() == null : this.getReason().equals(other.getReason()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getApplyNo() == null) ? 0 : getApplyNo().hashCode());
        result = prime * result + ((getApplyLine() == null) ? 0 : getApplyLine().hashCode());
        result = prime * result + ((getCompanyCode() == null) ? 0 : getCompanyCode().hashCode());
        result = prime * result + ((getStoreCode() == null) ? 0 : getStoreCode().hashCode());
        result = prime * result + ((getApplyDate() == null) ? 0 : getApplyDate().hashCode());
        result = prime * result + ((getDataOriginType() == null) ? 0 : getDataOriginType().hashCode());
        result = prime * result + ((getGoodsNo() == null) ? 0 : getGoodsNo().hashCode());
        result = prime * result + ((getApplyGoodsType() == null) ? 0 : getApplyGoodsType().hashCode());
        result = prime * result + ((getApplyTotal() == null) ? 0 : getApplyTotal().hashCode());
        result = prime * result + ((getUnqualifiedAwaitStock() == null) ? 0 : getUnqualifiedAwaitStock().hashCode());
        result = prime * result + ((getTransitStock() == null) ? 0 : getTransitStock().hashCode());
        result = prime * result + ((getStock() == null) ? 0 : getStock().hashCode());
        result = prime * result + ((getLockStock() == null) ? 0 : getLockStock().hashCode());
        result = prime * result + ((getUnqualifiedStock() == null) ? 0 : getUnqualifiedStock().hashCode());
        result = prime * result + ((getApplyTransitStock() == null) ? 0 : getApplyTransitStock().hashCode());
        result = prime * result + ((getInTransitStock() == null) ? 0 : getInTransitStock().hashCode());
        result = prime * result + ((getDistrTransitStock() == null) ? 0 : getDistrTransitStock().hashCode());
        result = prime * result + ((getSpecialCtrl() == null) ? 0 : getSpecialCtrl().hashCode());
        result = prime * result + ((getSpecialThirtyDaysQty() == null) ? 0 : getSpecialThirtyDaysQty().hashCode());
        result = prime * result + ((getGoodsLevel() == null) ? 0 : getGoodsLevel().hashCode());
        result = prime * result + ((getStockUpperLimitDays() == null) ? 0 : getStockUpperLimitDays().hashCode());
        result = prime * result + ((getStockLowerLimitDays() == null) ? 0 : getStockLowerLimitDays().hashCode());
        result = prime * result + ((getBdpAverageDailySales() == null) ? 0 : getBdpAverageDailySales().hashCode());
        result = prime * result + ((getMinDisplayQty() == null) ? 0 : getMinDisplayQty().hashCode());
        result = prime * result + ((getStockUpperLimit() == null) ? 0 : getStockUpperLimit().hashCode());
        result = prime * result + ((getStockLowerLimit() == null) ? 0 : getStockLowerLimit().hashCode());
        result = prime * result + ((getBuyStock() == null) ? 0 : getBuyStock().hashCode());
        result = prime * result + ((getSaleDaysBefore() == null) ? 0 : getSaleDaysBefore().hashCode());
        result = prime * result + ((getSaleDaysAfter() == null) ? 0 : getSaleDaysAfter().hashCode());
        result = prime * result + ((getThirtyDaysSales() == null) ? 0 : getThirtyDaysSales().hashCode());
        result = prime * result + ((getNinetyDaysSales() == null) ? 0 : getNinetyDaysSales().hashCode());
        result = prime * result + ((getCompanyApplyTotal() == null) ? 0 : getCompanyApplyTotal().hashCode());
        result = prime * result + ((getStoreApplyTotal() == null) ? 0 : getStoreApplyTotal().hashCode());
        result = prime * result + ((getMiddlePackageSwitch() == null) ? 0 : getMiddlePackageSwitch().hashCode());
        result = prime * result + ((getMiddlePackageQty() == null) ? 0 : getMiddlePackageQty().hashCode());
        result = prime * result + ((getMiddleCodeFlag() == null) ? 0 : getMiddleCodeFlag().hashCode());
        result = prime * result + ((getApplyRatio() == null) ? 0 : getApplyRatio().hashCode());
        result = prime * result + ((getCategoryId() == null) ? 0 : getCategoryId().hashCode());
        result = prime * result + ((getPurchaseType() == null) ? 0 : getPurchaseType().hashCode());
        result = prime * result + ((getPurchaseChannel() == null) ? 0 : getPurchaseChannel().hashCode());
        result = prime * result + ((getWarehouseCode() == null) ? 0 : getWarehouseCode().hashCode());
        result = prime * result + ((getRecommendReason() == null) ? 0 : getRecommendReason().hashCode());
        result = prime * result + ((getPromotionName() == null) ? 0 : getPromotionName().hashCode());
        result = prime * result + ((getPromotionWay() == null) ? 0 : getPromotionWay().hashCode());
        result = prime * result + ((getThresholdInfo() == null) ? 0 : getThresholdInfo().hashCode());
        result = prime * result + ((getFavInfo() == null) ? 0 : getFavInfo().hashCode());
        result = prime * result + ((getCompositeNew() == null) ? 0 : getCompositeNew().hashCode());
        result = prime * result + ((getThirtySalesQuantity() == null) ? 0 : getThirtySalesQuantity().hashCode());
        result = prime * result + ((getPromotionTitle() == null) ? 0 : getPromotionTitle().hashCode());
        result = prime * result + ((getPromotionStartDate() == null) ? 0 : getPromotionStartDate().hashCode());
        result = prime * result + ((getPromotionEndDate() == null) ? 0 : getPromotionEndDate().hashCode());
        result = prime * result + ((getDealSuggest() == null) ? 0 : getDealSuggest().hashCode());
        result = prime * result + ((getStoreAttr() == null) ? 0 : getStoreAttr().hashCode());
        result = prime * result + ((getThreeDaysSales() == null) ? 0 : getThreeDaysSales().hashCode());
        result = prime * result + ((getDeliverycycleCode() == null) ? 0 : getDeliverycycleCode().hashCode());
        result = prime * result + ((getNewProduct() == null) ? 0 : getNewProduct().hashCode());
        result = prime * result + ((getGoodsCutType() == null) ? 0 : getGoodsCutType().hashCode());
        result = prime * result + ((getGmtCreate() == null) ? 0 : getGmtCreate().hashCode());
        result = prime * result + ((getGmtUpdate() == null) ? 0 : getGmtUpdate().hashCode());
        result = prime * result + ((getReason() == null) ? 0 : getReason().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", applyNo=").append(applyNo);
        sb.append(", applyLine=").append(applyLine);
        sb.append(", companyCode=").append(companyCode);
        sb.append(", storeCode=").append(storeCode);
        sb.append(", applyDate=").append(applyDate);
        sb.append(", dataOriginType=").append(dataOriginType);
        sb.append(", goodsNo=").append(goodsNo);
        sb.append(", applyGoodsType=").append(applyGoodsType);
        sb.append(", applyTotal=").append(applyTotal);
        sb.append(", unqualifiedAwaitStock=").append(unqualifiedAwaitStock);
        sb.append(", transitStock=").append(transitStock);
        sb.append(", stock=").append(stock);
        sb.append(", lockStock=").append(lockStock);
        sb.append(", unqualifiedStock=").append(unqualifiedStock);
        sb.append(", applyTransitStock=").append(applyTransitStock);
        sb.append(", inTransitStock=").append(inTransitStock);
        sb.append(", distrTransitStock=").append(distrTransitStock);
        sb.append(", specialCtrl=").append(specialCtrl);
        sb.append(", specialThirtyDaysQty=").append(specialThirtyDaysQty);
        sb.append(", goodsLevel=").append(goodsLevel);
        sb.append(", stockUpperLimitDays=").append(stockUpperLimitDays);
        sb.append(", stockLowerLimitDays=").append(stockLowerLimitDays);
        sb.append(", bdpAverageDailySales=").append(bdpAverageDailySales);
        sb.append(", minDisplayQty=").append(minDisplayQty);
        sb.append(", stockUpperLimit=").append(stockUpperLimit);
        sb.append(", stockLowerLimit=").append(stockLowerLimit);
        sb.append(", buyStock=").append(buyStock);
        sb.append(", saleDaysBefore=").append(saleDaysBefore);
        sb.append(", saleDaysAfter=").append(saleDaysAfter);
        sb.append(", thirtyDaysSales=").append(thirtyDaysSales);
        sb.append(", ninetyDaysSales=").append(ninetyDaysSales);
        sb.append(", companyApplyTotal=").append(companyApplyTotal);
        sb.append(", storeApplyTotal=").append(storeApplyTotal);
        sb.append(", middlePackageSwitch=").append(middlePackageSwitch);
        sb.append(", middlePackageQty=").append(middlePackageQty);
        sb.append(", middleCodeFlag=").append(middleCodeFlag);
        sb.append(", applyRatio=").append(applyRatio);
        sb.append(", categoryId=").append(categoryId);
        sb.append(", purchaseType=").append(purchaseType);
        sb.append(", purchaseChannel=").append(purchaseChannel);
        sb.append(", warehouseCode=").append(warehouseCode);
        sb.append(", recommendReason=").append(recommendReason);
        sb.append(", promotionName=").append(promotionName);
        sb.append(", promotionWay=").append(promotionWay);
        sb.append(", thresholdInfo=").append(thresholdInfo);
        sb.append(", favInfo=").append(favInfo);
        sb.append(", compositeNew=").append(compositeNew);
        sb.append(", thirtySalesQuantity=").append(thirtySalesQuantity);
        sb.append(", promotionTitle=").append(promotionTitle);
        sb.append(", promotionStartDate=").append(promotionStartDate);
        sb.append(", promotionEndDate=").append(promotionEndDate);
        sb.append(", dealSuggest=").append(dealSuggest);
        sb.append(", storeAttr=").append(storeAttr);
        sb.append(", threeDaysSales=").append(threeDaysSales);
        sb.append(", deliverycycleCode=").append(deliverycycleCode);
        sb.append(", newProduct=").append(newProduct);
        sb.append(", goodsCutType=").append(goodsCutType);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", gmtUpdate=").append(gmtUpdate);
        sb.append(", reason=").append(reason);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}