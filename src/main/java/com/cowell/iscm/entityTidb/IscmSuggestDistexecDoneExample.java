package com.cowell.iscm.entityTidb;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class IscmSuggestDistexecDoneExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Long offset;

    public IscmSuggestDistexecDoneExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Long offset) {
        this.offset = offset;
    }

    public Long getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAllotDateIsNull() {
            addCriterion("allot_date is null");
            return (Criteria) this;
        }

        public Criteria andAllotDateIsNotNull() {
            addCriterion("allot_date is not null");
            return (Criteria) this;
        }

        public Criteria andAllotDateEqualTo(Date value) {
            addCriterion("allot_date =", value, "allotDate");
            return (Criteria) this;
        }

        public Criteria andAllotDateNotEqualTo(Date value) {
            addCriterion("allot_date <>", value, "allotDate");
            return (Criteria) this;
        }

        public Criteria andAllotDateGreaterThan(Date value) {
            addCriterion("allot_date >", value, "allotDate");
            return (Criteria) this;
        }

        public Criteria andAllotDateGreaterThanOrEqualTo(Date value) {
            addCriterion("allot_date >=", value, "allotDate");
            return (Criteria) this;
        }

        public Criteria andAllotDateLessThan(Date value) {
            addCriterion("allot_date <", value, "allotDate");
            return (Criteria) this;
        }

        public Criteria andAllotDateLessThanOrEqualTo(Date value) {
            addCriterion("allot_date <=", value, "allotDate");
            return (Criteria) this;
        }

        public Criteria andAllotDateIn(List<Date> values) {
            addCriterion("allot_date in", values, "allotDate");
            return (Criteria) this;
        }

        public Criteria andAllotDateNotIn(List<Date> values) {
            addCriterion("allot_date not in", values, "allotDate");
            return (Criteria) this;
        }

        public Criteria andAllotDateBetween(Date value1, Date value2) {
            addCriterion("allot_date between", value1, value2, "allotDate");
            return (Criteria) this;
        }

        public Criteria andAllotDateNotBetween(Date value1, Date value2) {
            addCriterion("allot_date not between", value1, value2, "allotDate");
            return (Criteria) this;
        }

        public Criteria andRegisterNoIsNull() {
            addCriterion("register_no is null");
            return (Criteria) this;
        }

        public Criteria andRegisterNoIsNotNull() {
            addCriterion("register_no is not null");
            return (Criteria) this;
        }

        public Criteria andRegisterNoEqualTo(String value) {
            addCriterion("register_no =", value, "registerNo");
            return (Criteria) this;
        }

        public Criteria andRegisterNoNotEqualTo(String value) {
            addCriterion("register_no <>", value, "registerNo");
            return (Criteria) this;
        }

        public Criteria andRegisterNoGreaterThan(String value) {
            addCriterion("register_no >", value, "registerNo");
            return (Criteria) this;
        }

        public Criteria andRegisterNoGreaterThanOrEqualTo(String value) {
            addCriterion("register_no >=", value, "registerNo");
            return (Criteria) this;
        }

        public Criteria andRegisterNoLessThan(String value) {
            addCriterion("register_no <", value, "registerNo");
            return (Criteria) this;
        }

        public Criteria andRegisterNoLessThanOrEqualTo(String value) {
            addCriterion("register_no <=", value, "registerNo");
            return (Criteria) this;
        }

        public Criteria andRegisterNoLike(String value) {
            addCriterion("register_no like", value, "registerNo");
            return (Criteria) this;
        }

        public Criteria andRegisterNoNotLike(String value) {
            addCriterion("register_no not like", value, "registerNo");
            return (Criteria) this;
        }

        public Criteria andRegisterNoIn(List<String> values) {
            addCriterion("register_no in", values, "registerNo");
            return (Criteria) this;
        }

        public Criteria andRegisterNoNotIn(List<String> values) {
            addCriterion("register_no not in", values, "registerNo");
            return (Criteria) this;
        }

        public Criteria andRegisterNoBetween(String value1, String value2) {
            addCriterion("register_no between", value1, value2, "registerNo");
            return (Criteria) this;
        }

        public Criteria andRegisterNoNotBetween(String value1, String value2) {
            addCriterion("register_no not between", value1, value2, "registerNo");
            return (Criteria) this;
        }

        public Criteria andPosAllotNoIsNull() {
            addCriterion("pos_allot_no is null");
            return (Criteria) this;
        }

        public Criteria andPosAllotNoIsNotNull() {
            addCriterion("pos_allot_no is not null");
            return (Criteria) this;
        }

        public Criteria andPosAllotNoEqualTo(String value) {
            addCriterion("pos_allot_no =", value, "posAllotNo");
            return (Criteria) this;
        }

        public Criteria andPosAllotNoNotEqualTo(String value) {
            addCriterion("pos_allot_no <>", value, "posAllotNo");
            return (Criteria) this;
        }

        public Criteria andPosAllotNoGreaterThan(String value) {
            addCriterion("pos_allot_no >", value, "posAllotNo");
            return (Criteria) this;
        }

        public Criteria andPosAllotNoGreaterThanOrEqualTo(String value) {
            addCriterion("pos_allot_no >=", value, "posAllotNo");
            return (Criteria) this;
        }

        public Criteria andPosAllotNoLessThan(String value) {
            addCriterion("pos_allot_no <", value, "posAllotNo");
            return (Criteria) this;
        }

        public Criteria andPosAllotNoLessThanOrEqualTo(String value) {
            addCriterion("pos_allot_no <=", value, "posAllotNo");
            return (Criteria) this;
        }

        public Criteria andPosAllotNoLike(String value) {
            addCriterion("pos_allot_no like", value, "posAllotNo");
            return (Criteria) this;
        }

        public Criteria andPosAllotNoNotLike(String value) {
            addCriterion("pos_allot_no not like", value, "posAllotNo");
            return (Criteria) this;
        }

        public Criteria andPosAllotNoIn(List<String> values) {
            addCriterion("pos_allot_no in", values, "posAllotNo");
            return (Criteria) this;
        }

        public Criteria andPosAllotNoNotIn(List<String> values) {
            addCriterion("pos_allot_no not in", values, "posAllotNo");
            return (Criteria) this;
        }

        public Criteria andPosAllotNoBetween(String value1, String value2) {
            addCriterion("pos_allot_no between", value1, value2, "posAllotNo");
            return (Criteria) this;
        }

        public Criteria andPosAllotNoNotBetween(String value1, String value2) {
            addCriterion("pos_allot_no not between", value1, value2, "posAllotNo");
            return (Criteria) this;
        }

        public Criteria andOutCompanyCodeIsNull() {
            addCriterion("out_company_code is null");
            return (Criteria) this;
        }

        public Criteria andOutCompanyCodeIsNotNull() {
            addCriterion("out_company_code is not null");
            return (Criteria) this;
        }

        public Criteria andOutCompanyCodeEqualTo(String value) {
            addCriterion("out_company_code =", value, "outCompanyCode");
            return (Criteria) this;
        }

        public Criteria andOutCompanyCodeNotEqualTo(String value) {
            addCriterion("out_company_code <>", value, "outCompanyCode");
            return (Criteria) this;
        }

        public Criteria andOutCompanyCodeGreaterThan(String value) {
            addCriterion("out_company_code >", value, "outCompanyCode");
            return (Criteria) this;
        }

        public Criteria andOutCompanyCodeGreaterThanOrEqualTo(String value) {
            addCriterion("out_company_code >=", value, "outCompanyCode");
            return (Criteria) this;
        }

        public Criteria andOutCompanyCodeLessThan(String value) {
            addCriterion("out_company_code <", value, "outCompanyCode");
            return (Criteria) this;
        }

        public Criteria andOutCompanyCodeLessThanOrEqualTo(String value) {
            addCriterion("out_company_code <=", value, "outCompanyCode");
            return (Criteria) this;
        }

        public Criteria andOutCompanyCodeLike(String value) {
            addCriterion("out_company_code like", value, "outCompanyCode");
            return (Criteria) this;
        }

        public Criteria andOutCompanyCodeNotLike(String value) {
            addCriterion("out_company_code not like", value, "outCompanyCode");
            return (Criteria) this;
        }

        public Criteria andOutCompanyCodeIn(List<String> values) {
            addCriterion("out_company_code in", values, "outCompanyCode");
            return (Criteria) this;
        }

        public Criteria andOutCompanyCodeNotIn(List<String> values) {
            addCriterion("out_company_code not in", values, "outCompanyCode");
            return (Criteria) this;
        }

        public Criteria andOutCompanyCodeBetween(String value1, String value2) {
            addCriterion("out_company_code between", value1, value2, "outCompanyCode");
            return (Criteria) this;
        }

        public Criteria andOutCompanyCodeNotBetween(String value1, String value2) {
            addCriterion("out_company_code not between", value1, value2, "outCompanyCode");
            return (Criteria) this;
        }

        public Criteria andOutStoreCodeIsNull() {
            addCriterion("out_store_code is null");
            return (Criteria) this;
        }

        public Criteria andOutStoreCodeIsNotNull() {
            addCriterion("out_store_code is not null");
            return (Criteria) this;
        }

        public Criteria andOutStoreCodeEqualTo(String value) {
            addCriterion("out_store_code =", value, "outStoreCode");
            return (Criteria) this;
        }

        public Criteria andOutStoreCodeNotEqualTo(String value) {
            addCriterion("out_store_code <>", value, "outStoreCode");
            return (Criteria) this;
        }

        public Criteria andOutStoreCodeGreaterThan(String value) {
            addCriterion("out_store_code >", value, "outStoreCode");
            return (Criteria) this;
        }

        public Criteria andOutStoreCodeGreaterThanOrEqualTo(String value) {
            addCriterion("out_store_code >=", value, "outStoreCode");
            return (Criteria) this;
        }

        public Criteria andOutStoreCodeLessThan(String value) {
            addCriterion("out_store_code <", value, "outStoreCode");
            return (Criteria) this;
        }

        public Criteria andOutStoreCodeLessThanOrEqualTo(String value) {
            addCriterion("out_store_code <=", value, "outStoreCode");
            return (Criteria) this;
        }

        public Criteria andOutStoreCodeLike(String value) {
            addCriterion("out_store_code like", value, "outStoreCode");
            return (Criteria) this;
        }

        public Criteria andOutStoreCodeNotLike(String value) {
            addCriterion("out_store_code not like", value, "outStoreCode");
            return (Criteria) this;
        }

        public Criteria andOutStoreCodeIn(List<String> values) {
            addCriterion("out_store_code in", values, "outStoreCode");
            return (Criteria) this;
        }

        public Criteria andOutStoreCodeNotIn(List<String> values) {
            addCriterion("out_store_code not in", values, "outStoreCode");
            return (Criteria) this;
        }

        public Criteria andOutStoreCodeBetween(String value1, String value2) {
            addCriterion("out_store_code between", value1, value2, "outStoreCode");
            return (Criteria) this;
        }

        public Criteria andOutStoreCodeNotBetween(String value1, String value2) {
            addCriterion("out_store_code not between", value1, value2, "outStoreCode");
            return (Criteria) this;
        }

        public Criteria andInCompanyCodeIsNull() {
            addCriterion("in_company_code is null");
            return (Criteria) this;
        }

        public Criteria andInCompanyCodeIsNotNull() {
            addCriterion("in_company_code is not null");
            return (Criteria) this;
        }

        public Criteria andInCompanyCodeEqualTo(String value) {
            addCriterion("in_company_code =", value, "inCompanyCode");
            return (Criteria) this;
        }

        public Criteria andInCompanyCodeNotEqualTo(String value) {
            addCriterion("in_company_code <>", value, "inCompanyCode");
            return (Criteria) this;
        }

        public Criteria andInCompanyCodeGreaterThan(String value) {
            addCriterion("in_company_code >", value, "inCompanyCode");
            return (Criteria) this;
        }

        public Criteria andInCompanyCodeGreaterThanOrEqualTo(String value) {
            addCriterion("in_company_code >=", value, "inCompanyCode");
            return (Criteria) this;
        }

        public Criteria andInCompanyCodeLessThan(String value) {
            addCriterion("in_company_code <", value, "inCompanyCode");
            return (Criteria) this;
        }

        public Criteria andInCompanyCodeLessThanOrEqualTo(String value) {
            addCriterion("in_company_code <=", value, "inCompanyCode");
            return (Criteria) this;
        }

        public Criteria andInCompanyCodeLike(String value) {
            addCriterion("in_company_code like", value, "inCompanyCode");
            return (Criteria) this;
        }

        public Criteria andInCompanyCodeNotLike(String value) {
            addCriterion("in_company_code not like", value, "inCompanyCode");
            return (Criteria) this;
        }

        public Criteria andInCompanyCodeIn(List<String> values) {
            addCriterion("in_company_code in", values, "inCompanyCode");
            return (Criteria) this;
        }

        public Criteria andInCompanyCodeNotIn(List<String> values) {
            addCriterion("in_company_code not in", values, "inCompanyCode");
            return (Criteria) this;
        }

        public Criteria andInCompanyCodeBetween(String value1, String value2) {
            addCriterion("in_company_code between", value1, value2, "inCompanyCode");
            return (Criteria) this;
        }

        public Criteria andInCompanyCodeNotBetween(String value1, String value2) {
            addCriterion("in_company_code not between", value1, value2, "inCompanyCode");
            return (Criteria) this;
        }

        public Criteria andInStoreCodeIsNull() {
            addCriterion("in_store_code is null");
            return (Criteria) this;
        }

        public Criteria andInStoreCodeIsNotNull() {
            addCriterion("in_store_code is not null");
            return (Criteria) this;
        }

        public Criteria andInStoreCodeEqualTo(String value) {
            addCriterion("in_store_code =", value, "inStoreCode");
            return (Criteria) this;
        }

        public Criteria andInStoreCodeNotEqualTo(String value) {
            addCriterion("in_store_code <>", value, "inStoreCode");
            return (Criteria) this;
        }

        public Criteria andInStoreCodeGreaterThan(String value) {
            addCriterion("in_store_code >", value, "inStoreCode");
            return (Criteria) this;
        }

        public Criteria andInStoreCodeGreaterThanOrEqualTo(String value) {
            addCriterion("in_store_code >=", value, "inStoreCode");
            return (Criteria) this;
        }

        public Criteria andInStoreCodeLessThan(String value) {
            addCriterion("in_store_code <", value, "inStoreCode");
            return (Criteria) this;
        }

        public Criteria andInStoreCodeLessThanOrEqualTo(String value) {
            addCriterion("in_store_code <=", value, "inStoreCode");
            return (Criteria) this;
        }

        public Criteria andInStoreCodeLike(String value) {
            addCriterion("in_store_code like", value, "inStoreCode");
            return (Criteria) this;
        }

        public Criteria andInStoreCodeNotLike(String value) {
            addCriterion("in_store_code not like", value, "inStoreCode");
            return (Criteria) this;
        }

        public Criteria andInStoreCodeIn(List<String> values) {
            addCriterion("in_store_code in", values, "inStoreCode");
            return (Criteria) this;
        }

        public Criteria andInStoreCodeNotIn(List<String> values) {
            addCriterion("in_store_code not in", values, "inStoreCode");
            return (Criteria) this;
        }

        public Criteria andInStoreCodeBetween(String value1, String value2) {
            addCriterion("in_store_code between", value1, value2, "inStoreCode");
            return (Criteria) this;
        }

        public Criteria andInStoreCodeNotBetween(String value1, String value2) {
            addCriterion("in_store_code not between", value1, value2, "inStoreCode");
            return (Criteria) this;
        }

        public Criteria andGoodsNoIsNull() {
            addCriterion("goods_no is null");
            return (Criteria) this;
        }

        public Criteria andGoodsNoIsNotNull() {
            addCriterion("goods_no is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsNoEqualTo(String value) {
            addCriterion("goods_no =", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotEqualTo(String value) {
            addCriterion("goods_no <>", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoGreaterThan(String value) {
            addCriterion("goods_no >", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoGreaterThanOrEqualTo(String value) {
            addCriterion("goods_no >=", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoLessThan(String value) {
            addCriterion("goods_no <", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoLessThanOrEqualTo(String value) {
            addCriterion("goods_no <=", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoLike(String value) {
            addCriterion("goods_no like", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotLike(String value) {
            addCriterion("goods_no not like", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoIn(List<String> values) {
            addCriterion("goods_no in", values, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotIn(List<String> values) {
            addCriterion("goods_no not in", values, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoBetween(String value1, String value2) {
            addCriterion("goods_no between", value1, value2, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotBetween(String value1, String value2) {
            addCriterion("goods_no not between", value1, value2, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andInAllotQuantityIsNull() {
            addCriterion("in_allot_quantity is null");
            return (Criteria) this;
        }

        public Criteria andInAllotQuantityIsNotNull() {
            addCriterion("in_allot_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andInAllotQuantityEqualTo(BigDecimal value) {
            addCriterion("in_allot_quantity =", value, "inAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andInAllotQuantityNotEqualTo(BigDecimal value) {
            addCriterion("in_allot_quantity <>", value, "inAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andInAllotQuantityGreaterThan(BigDecimal value) {
            addCriterion("in_allot_quantity >", value, "inAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andInAllotQuantityGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("in_allot_quantity >=", value, "inAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andInAllotQuantityLessThan(BigDecimal value) {
            addCriterion("in_allot_quantity <", value, "inAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andInAllotQuantityLessThanOrEqualTo(BigDecimal value) {
            addCriterion("in_allot_quantity <=", value, "inAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andInAllotQuantityIn(List<BigDecimal> values) {
            addCriterion("in_allot_quantity in", values, "inAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andInAllotQuantityNotIn(List<BigDecimal> values) {
            addCriterion("in_allot_quantity not in", values, "inAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andInAllotQuantityBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("in_allot_quantity between", value1, value2, "inAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andInAllotQuantityNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("in_allot_quantity not between", value1, value2, "inAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andInStoreSales7IsNull() {
            addCriterion("in_store_sales_7 is null");
            return (Criteria) this;
        }

        public Criteria andInStoreSales7IsNotNull() {
            addCriterion("in_store_sales_7 is not null");
            return (Criteria) this;
        }

        public Criteria andInStoreSales7EqualTo(BigDecimal value) {
            addCriterion("in_store_sales_7 =", value, "inStoreSales7");
            return (Criteria) this;
        }

        public Criteria andInStoreSales7NotEqualTo(BigDecimal value) {
            addCriterion("in_store_sales_7 <>", value, "inStoreSales7");
            return (Criteria) this;
        }

        public Criteria andInStoreSales7GreaterThan(BigDecimal value) {
            addCriterion("in_store_sales_7 >", value, "inStoreSales7");
            return (Criteria) this;
        }

        public Criteria andInStoreSales7GreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("in_store_sales_7 >=", value, "inStoreSales7");
            return (Criteria) this;
        }

        public Criteria andInStoreSales7LessThan(BigDecimal value) {
            addCriterion("in_store_sales_7 <", value, "inStoreSales7");
            return (Criteria) this;
        }

        public Criteria andInStoreSales7LessThanOrEqualTo(BigDecimal value) {
            addCriterion("in_store_sales_7 <=", value, "inStoreSales7");
            return (Criteria) this;
        }

        public Criteria andInStoreSales7In(List<BigDecimal> values) {
            addCriterion("in_store_sales_7 in", values, "inStoreSales7");
            return (Criteria) this;
        }

        public Criteria andInStoreSales7NotIn(List<BigDecimal> values) {
            addCriterion("in_store_sales_7 not in", values, "inStoreSales7");
            return (Criteria) this;
        }

        public Criteria andInStoreSales7Between(BigDecimal value1, BigDecimal value2) {
            addCriterion("in_store_sales_7 between", value1, value2, "inStoreSales7");
            return (Criteria) this;
        }

        public Criteria andInStoreSales7NotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("in_store_sales_7 not between", value1, value2, "inStoreSales7");
            return (Criteria) this;
        }

        public Criteria andInStorePuramount7IsNull() {
            addCriterion("in_store_puramount_7 is null");
            return (Criteria) this;
        }

        public Criteria andInStorePuramount7IsNotNull() {
            addCriterion("in_store_puramount_7 is not null");
            return (Criteria) this;
        }

        public Criteria andInStorePuramount7EqualTo(BigDecimal value) {
            addCriterion("in_store_puramount_7 =", value, "inStorePuramount7");
            return (Criteria) this;
        }

        public Criteria andInStorePuramount7NotEqualTo(BigDecimal value) {
            addCriterion("in_store_puramount_7 <>", value, "inStorePuramount7");
            return (Criteria) this;
        }

        public Criteria andInStorePuramount7GreaterThan(BigDecimal value) {
            addCriterion("in_store_puramount_7 >", value, "inStorePuramount7");
            return (Criteria) this;
        }

        public Criteria andInStorePuramount7GreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("in_store_puramount_7 >=", value, "inStorePuramount7");
            return (Criteria) this;
        }

        public Criteria andInStorePuramount7LessThan(BigDecimal value) {
            addCriterion("in_store_puramount_7 <", value, "inStorePuramount7");
            return (Criteria) this;
        }

        public Criteria andInStorePuramount7LessThanOrEqualTo(BigDecimal value) {
            addCriterion("in_store_puramount_7 <=", value, "inStorePuramount7");
            return (Criteria) this;
        }

        public Criteria andInStorePuramount7In(List<BigDecimal> values) {
            addCriterion("in_store_puramount_7 in", values, "inStorePuramount7");
            return (Criteria) this;
        }

        public Criteria andInStorePuramount7NotIn(List<BigDecimal> values) {
            addCriterion("in_store_puramount_7 not in", values, "inStorePuramount7");
            return (Criteria) this;
        }

        public Criteria andInStorePuramount7Between(BigDecimal value1, BigDecimal value2) {
            addCriterion("in_store_puramount_7 between", value1, value2, "inStorePuramount7");
            return (Criteria) this;
        }

        public Criteria andInStorePuramount7NotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("in_store_puramount_7 not between", value1, value2, "inStorePuramount7");
            return (Criteria) this;
        }

        public Criteria andInStoreSales14IsNull() {
            addCriterion("in_store_sales_14 is null");
            return (Criteria) this;
        }

        public Criteria andInStoreSales14IsNotNull() {
            addCriterion("in_store_sales_14 is not null");
            return (Criteria) this;
        }

        public Criteria andInStoreSales14EqualTo(BigDecimal value) {
            addCriterion("in_store_sales_14 =", value, "inStoreSales14");
            return (Criteria) this;
        }

        public Criteria andInStoreSales14NotEqualTo(BigDecimal value) {
            addCriterion("in_store_sales_14 <>", value, "inStoreSales14");
            return (Criteria) this;
        }

        public Criteria andInStoreSales14GreaterThan(BigDecimal value) {
            addCriterion("in_store_sales_14 >", value, "inStoreSales14");
            return (Criteria) this;
        }

        public Criteria andInStoreSales14GreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("in_store_sales_14 >=", value, "inStoreSales14");
            return (Criteria) this;
        }

        public Criteria andInStoreSales14LessThan(BigDecimal value) {
            addCriterion("in_store_sales_14 <", value, "inStoreSales14");
            return (Criteria) this;
        }

        public Criteria andInStoreSales14LessThanOrEqualTo(BigDecimal value) {
            addCriterion("in_store_sales_14 <=", value, "inStoreSales14");
            return (Criteria) this;
        }

        public Criteria andInStoreSales14In(List<BigDecimal> values) {
            addCriterion("in_store_sales_14 in", values, "inStoreSales14");
            return (Criteria) this;
        }

        public Criteria andInStoreSales14NotIn(List<BigDecimal> values) {
            addCriterion("in_store_sales_14 not in", values, "inStoreSales14");
            return (Criteria) this;
        }

        public Criteria andInStoreSales14Between(BigDecimal value1, BigDecimal value2) {
            addCriterion("in_store_sales_14 between", value1, value2, "inStoreSales14");
            return (Criteria) this;
        }

        public Criteria andInStoreSales14NotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("in_store_sales_14 not between", value1, value2, "inStoreSales14");
            return (Criteria) this;
        }

        public Criteria andInStorePuramount14IsNull() {
            addCriterion("in_store_puramount_14 is null");
            return (Criteria) this;
        }

        public Criteria andInStorePuramount14IsNotNull() {
            addCriterion("in_store_puramount_14 is not null");
            return (Criteria) this;
        }

        public Criteria andInStorePuramount14EqualTo(BigDecimal value) {
            addCriterion("in_store_puramount_14 =", value, "inStorePuramount14");
            return (Criteria) this;
        }

        public Criteria andInStorePuramount14NotEqualTo(BigDecimal value) {
            addCriterion("in_store_puramount_14 <>", value, "inStorePuramount14");
            return (Criteria) this;
        }

        public Criteria andInStorePuramount14GreaterThan(BigDecimal value) {
            addCriterion("in_store_puramount_14 >", value, "inStorePuramount14");
            return (Criteria) this;
        }

        public Criteria andInStorePuramount14GreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("in_store_puramount_14 >=", value, "inStorePuramount14");
            return (Criteria) this;
        }

        public Criteria andInStorePuramount14LessThan(BigDecimal value) {
            addCriterion("in_store_puramount_14 <", value, "inStorePuramount14");
            return (Criteria) this;
        }

        public Criteria andInStorePuramount14LessThanOrEqualTo(BigDecimal value) {
            addCriterion("in_store_puramount_14 <=", value, "inStorePuramount14");
            return (Criteria) this;
        }

        public Criteria andInStorePuramount14In(List<BigDecimal> values) {
            addCriterion("in_store_puramount_14 in", values, "inStorePuramount14");
            return (Criteria) this;
        }

        public Criteria andInStorePuramount14NotIn(List<BigDecimal> values) {
            addCriterion("in_store_puramount_14 not in", values, "inStorePuramount14");
            return (Criteria) this;
        }

        public Criteria andInStorePuramount14Between(BigDecimal value1, BigDecimal value2) {
            addCriterion("in_store_puramount_14 between", value1, value2, "inStorePuramount14");
            return (Criteria) this;
        }

        public Criteria andInStorePuramount14NotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("in_store_puramount_14 not between", value1, value2, "inStorePuramount14");
            return (Criteria) this;
        }

        public Criteria andInStoreSales30IsNull() {
            addCriterion("in_store_sales_30 is null");
            return (Criteria) this;
        }

        public Criteria andInStoreSales30IsNotNull() {
            addCriterion("in_store_sales_30 is not null");
            return (Criteria) this;
        }

        public Criteria andInStoreSales30EqualTo(BigDecimal value) {
            addCriterion("in_store_sales_30 =", value, "inStoreSales30");
            return (Criteria) this;
        }

        public Criteria andInStoreSales30NotEqualTo(BigDecimal value) {
            addCriterion("in_store_sales_30 <>", value, "inStoreSales30");
            return (Criteria) this;
        }

        public Criteria andInStoreSales30GreaterThan(BigDecimal value) {
            addCriterion("in_store_sales_30 >", value, "inStoreSales30");
            return (Criteria) this;
        }

        public Criteria andInStoreSales30GreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("in_store_sales_30 >=", value, "inStoreSales30");
            return (Criteria) this;
        }

        public Criteria andInStoreSales30LessThan(BigDecimal value) {
            addCriterion("in_store_sales_30 <", value, "inStoreSales30");
            return (Criteria) this;
        }

        public Criteria andInStoreSales30LessThanOrEqualTo(BigDecimal value) {
            addCriterion("in_store_sales_30 <=", value, "inStoreSales30");
            return (Criteria) this;
        }

        public Criteria andInStoreSales30In(List<BigDecimal> values) {
            addCriterion("in_store_sales_30 in", values, "inStoreSales30");
            return (Criteria) this;
        }

        public Criteria andInStoreSales30NotIn(List<BigDecimal> values) {
            addCriterion("in_store_sales_30 not in", values, "inStoreSales30");
            return (Criteria) this;
        }

        public Criteria andInStoreSales30Between(BigDecimal value1, BigDecimal value2) {
            addCriterion("in_store_sales_30 between", value1, value2, "inStoreSales30");
            return (Criteria) this;
        }

        public Criteria andInStoreSales30NotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("in_store_sales_30 not between", value1, value2, "inStoreSales30");
            return (Criteria) this;
        }

        public Criteria andInStorePuramount30IsNull() {
            addCriterion("in_store_puramount_30 is null");
            return (Criteria) this;
        }

        public Criteria andInStorePuramount30IsNotNull() {
            addCriterion("in_store_puramount_30 is not null");
            return (Criteria) this;
        }

        public Criteria andInStorePuramount30EqualTo(BigDecimal value) {
            addCriterion("in_store_puramount_30 =", value, "inStorePuramount30");
            return (Criteria) this;
        }

        public Criteria andInStorePuramount30NotEqualTo(BigDecimal value) {
            addCriterion("in_store_puramount_30 <>", value, "inStorePuramount30");
            return (Criteria) this;
        }

        public Criteria andInStorePuramount30GreaterThan(BigDecimal value) {
            addCriterion("in_store_puramount_30 >", value, "inStorePuramount30");
            return (Criteria) this;
        }

        public Criteria andInStorePuramount30GreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("in_store_puramount_30 >=", value, "inStorePuramount30");
            return (Criteria) this;
        }

        public Criteria andInStorePuramount30LessThan(BigDecimal value) {
            addCriterion("in_store_puramount_30 <", value, "inStorePuramount30");
            return (Criteria) this;
        }

        public Criteria andInStorePuramount30LessThanOrEqualTo(BigDecimal value) {
            addCriterion("in_store_puramount_30 <=", value, "inStorePuramount30");
            return (Criteria) this;
        }

        public Criteria andInStorePuramount30In(List<BigDecimal> values) {
            addCriterion("in_store_puramount_30 in", values, "inStorePuramount30");
            return (Criteria) this;
        }

        public Criteria andInStorePuramount30NotIn(List<BigDecimal> values) {
            addCriterion("in_store_puramount_30 not in", values, "inStorePuramount30");
            return (Criteria) this;
        }

        public Criteria andInStorePuramount30Between(BigDecimal value1, BigDecimal value2) {
            addCriterion("in_store_puramount_30 between", value1, value2, "inStorePuramount30");
            return (Criteria) this;
        }

        public Criteria andInStorePuramount30NotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("in_store_puramount_30 not between", value1, value2, "inStorePuramount30");
            return (Criteria) this;
        }

        public Criteria andInStoreSales60IsNull() {
            addCriterion("in_store_sales_60 is null");
            return (Criteria) this;
        }

        public Criteria andInStoreSales60IsNotNull() {
            addCriterion("in_store_sales_60 is not null");
            return (Criteria) this;
        }

        public Criteria andInStoreSales60EqualTo(BigDecimal value) {
            addCriterion("in_store_sales_60 =", value, "inStoreSales60");
            return (Criteria) this;
        }

        public Criteria andInStoreSales60NotEqualTo(BigDecimal value) {
            addCriterion("in_store_sales_60 <>", value, "inStoreSales60");
            return (Criteria) this;
        }

        public Criteria andInStoreSales60GreaterThan(BigDecimal value) {
            addCriterion("in_store_sales_60 >", value, "inStoreSales60");
            return (Criteria) this;
        }

        public Criteria andInStoreSales60GreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("in_store_sales_60 >=", value, "inStoreSales60");
            return (Criteria) this;
        }

        public Criteria andInStoreSales60LessThan(BigDecimal value) {
            addCriterion("in_store_sales_60 <", value, "inStoreSales60");
            return (Criteria) this;
        }

        public Criteria andInStoreSales60LessThanOrEqualTo(BigDecimal value) {
            addCriterion("in_store_sales_60 <=", value, "inStoreSales60");
            return (Criteria) this;
        }

        public Criteria andInStoreSales60In(List<BigDecimal> values) {
            addCriterion("in_store_sales_60 in", values, "inStoreSales60");
            return (Criteria) this;
        }

        public Criteria andInStoreSales60NotIn(List<BigDecimal> values) {
            addCriterion("in_store_sales_60 not in", values, "inStoreSales60");
            return (Criteria) this;
        }

        public Criteria andInStoreSales60Between(BigDecimal value1, BigDecimal value2) {
            addCriterion("in_store_sales_60 between", value1, value2, "inStoreSales60");
            return (Criteria) this;
        }

        public Criteria andInStoreSales60NotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("in_store_sales_60 not between", value1, value2, "inStoreSales60");
            return (Criteria) this;
        }

        public Criteria andInStorePuramount60IsNull() {
            addCriterion("in_store_puramount_60 is null");
            return (Criteria) this;
        }

        public Criteria andInStorePuramount60IsNotNull() {
            addCriterion("in_store_puramount_60 is not null");
            return (Criteria) this;
        }

        public Criteria andInStorePuramount60EqualTo(BigDecimal value) {
            addCriterion("in_store_puramount_60 =", value, "inStorePuramount60");
            return (Criteria) this;
        }

        public Criteria andInStorePuramount60NotEqualTo(BigDecimal value) {
            addCriterion("in_store_puramount_60 <>", value, "inStorePuramount60");
            return (Criteria) this;
        }

        public Criteria andInStorePuramount60GreaterThan(BigDecimal value) {
            addCriterion("in_store_puramount_60 >", value, "inStorePuramount60");
            return (Criteria) this;
        }

        public Criteria andInStorePuramount60GreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("in_store_puramount_60 >=", value, "inStorePuramount60");
            return (Criteria) this;
        }

        public Criteria andInStorePuramount60LessThan(BigDecimal value) {
            addCriterion("in_store_puramount_60 <", value, "inStorePuramount60");
            return (Criteria) this;
        }

        public Criteria andInStorePuramount60LessThanOrEqualTo(BigDecimal value) {
            addCriterion("in_store_puramount_60 <=", value, "inStorePuramount60");
            return (Criteria) this;
        }

        public Criteria andInStorePuramount60In(List<BigDecimal> values) {
            addCriterion("in_store_puramount_60 in", values, "inStorePuramount60");
            return (Criteria) this;
        }

        public Criteria andInStorePuramount60NotIn(List<BigDecimal> values) {
            addCriterion("in_store_puramount_60 not in", values, "inStorePuramount60");
            return (Criteria) this;
        }

        public Criteria andInStorePuramount60Between(BigDecimal value1, BigDecimal value2) {
            addCriterion("in_store_puramount_60 between", value1, value2, "inStorePuramount60");
            return (Criteria) this;
        }

        public Criteria andInStorePuramount60NotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("in_store_puramount_60 not between", value1, value2, "inStorePuramount60");
            return (Criteria) this;
        }

        public Criteria andInStoreSales90IsNull() {
            addCriterion("in_store_sales_90 is null");
            return (Criteria) this;
        }

        public Criteria andInStoreSales90IsNotNull() {
            addCriterion("in_store_sales_90 is not null");
            return (Criteria) this;
        }

        public Criteria andInStoreSales90EqualTo(BigDecimal value) {
            addCriterion("in_store_sales_90 =", value, "inStoreSales90");
            return (Criteria) this;
        }

        public Criteria andInStoreSales90NotEqualTo(BigDecimal value) {
            addCriterion("in_store_sales_90 <>", value, "inStoreSales90");
            return (Criteria) this;
        }

        public Criteria andInStoreSales90GreaterThan(BigDecimal value) {
            addCriterion("in_store_sales_90 >", value, "inStoreSales90");
            return (Criteria) this;
        }

        public Criteria andInStoreSales90GreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("in_store_sales_90 >=", value, "inStoreSales90");
            return (Criteria) this;
        }

        public Criteria andInStoreSales90LessThan(BigDecimal value) {
            addCriterion("in_store_sales_90 <", value, "inStoreSales90");
            return (Criteria) this;
        }

        public Criteria andInStoreSales90LessThanOrEqualTo(BigDecimal value) {
            addCriterion("in_store_sales_90 <=", value, "inStoreSales90");
            return (Criteria) this;
        }

        public Criteria andInStoreSales90In(List<BigDecimal> values) {
            addCriterion("in_store_sales_90 in", values, "inStoreSales90");
            return (Criteria) this;
        }

        public Criteria andInStoreSales90NotIn(List<BigDecimal> values) {
            addCriterion("in_store_sales_90 not in", values, "inStoreSales90");
            return (Criteria) this;
        }

        public Criteria andInStoreSales90Between(BigDecimal value1, BigDecimal value2) {
            addCriterion("in_store_sales_90 between", value1, value2, "inStoreSales90");
            return (Criteria) this;
        }

        public Criteria andInStoreSales90NotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("in_store_sales_90 not between", value1, value2, "inStoreSales90");
            return (Criteria) this;
        }

        public Criteria andInStorePuramount90IsNull() {
            addCriterion("in_store_puramount_90 is null");
            return (Criteria) this;
        }

        public Criteria andInStorePuramount90IsNotNull() {
            addCriterion("in_store_puramount_90 is not null");
            return (Criteria) this;
        }

        public Criteria andInStorePuramount90EqualTo(BigDecimal value) {
            addCriterion("in_store_puramount_90 =", value, "inStorePuramount90");
            return (Criteria) this;
        }

        public Criteria andInStorePuramount90NotEqualTo(BigDecimal value) {
            addCriterion("in_store_puramount_90 <>", value, "inStorePuramount90");
            return (Criteria) this;
        }

        public Criteria andInStorePuramount90GreaterThan(BigDecimal value) {
            addCriterion("in_store_puramount_90 >", value, "inStorePuramount90");
            return (Criteria) this;
        }

        public Criteria andInStorePuramount90GreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("in_store_puramount_90 >=", value, "inStorePuramount90");
            return (Criteria) this;
        }

        public Criteria andInStorePuramount90LessThan(BigDecimal value) {
            addCriterion("in_store_puramount_90 <", value, "inStorePuramount90");
            return (Criteria) this;
        }

        public Criteria andInStorePuramount90LessThanOrEqualTo(BigDecimal value) {
            addCriterion("in_store_puramount_90 <=", value, "inStorePuramount90");
            return (Criteria) this;
        }

        public Criteria andInStorePuramount90In(List<BigDecimal> values) {
            addCriterion("in_store_puramount_90 in", values, "inStorePuramount90");
            return (Criteria) this;
        }

        public Criteria andInStorePuramount90NotIn(List<BigDecimal> values) {
            addCriterion("in_store_puramount_90 not in", values, "inStorePuramount90");
            return (Criteria) this;
        }

        public Criteria andInStorePuramount90Between(BigDecimal value1, BigDecimal value2) {
            addCriterion("in_store_puramount_90 between", value1, value2, "inStorePuramount90");
            return (Criteria) this;
        }

        public Criteria andInStorePuramount90NotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("in_store_puramount_90 not between", value1, value2, "inStorePuramount90");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("`status` is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("`status` is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Byte value) {
            addCriterion("`status` =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Byte value) {
            addCriterion("`status` <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Byte value) {
            addCriterion("`status` >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("`status` >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Byte value) {
            addCriterion("`status` <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Byte value) {
            addCriterion("`status` <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Byte> values) {
            addCriterion("`status` in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Byte> values) {
            addCriterion("`status` not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Byte value1, Byte value2) {
            addCriterion("`status` between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("`status` not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIsNull() {
            addCriterion("gmt_update is null");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIsNotNull() {
            addCriterion("gmt_update is not null");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateEqualTo(Date value) {
            addCriterion("gmt_update =", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotEqualTo(Date value) {
            addCriterion("gmt_update <>", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateGreaterThan(Date value) {
            addCriterion("gmt_update >", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_update >=", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateLessThan(Date value) {
            addCriterion("gmt_update <", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_update <=", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIn(List<Date> values) {
            addCriterion("gmt_update in", values, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotIn(List<Date> values) {
            addCriterion("gmt_update not in", values, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateBetween(Date value1, Date value2) {
            addCriterion("gmt_update between", value1, value2, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_update not between", value1, value2, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andExtendIsNull() {
            addCriterion("extend is null");
            return (Criteria) this;
        }

        public Criteria andExtendIsNotNull() {
            addCriterion("extend is not null");
            return (Criteria) this;
        }

        public Criteria andExtendEqualTo(String value) {
            addCriterion("extend =", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotEqualTo(String value) {
            addCriterion("extend <>", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendGreaterThan(String value) {
            addCriterion("extend >", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendGreaterThanOrEqualTo(String value) {
            addCriterion("extend >=", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLessThan(String value) {
            addCriterion("extend <", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLessThanOrEqualTo(String value) {
            addCriterion("extend <=", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLike(String value) {
            addCriterion("extend like", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotLike(String value) {
            addCriterion("extend not like", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendIn(List<String> values) {
            addCriterion("extend in", values, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotIn(List<String> values) {
            addCriterion("extend not in", values, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendBetween(String value1, String value2) {
            addCriterion("extend between", value1, value2, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotBetween(String value1, String value2) {
            addCriterion("extend not between", value1, value2, "extend");
            return (Criteria) this;
        }

        public Criteria andVersionIsNull() {
            addCriterion("version is null");
            return (Criteria) this;
        }

        public Criteria andVersionIsNotNull() {
            addCriterion("version is not null");
            return (Criteria) this;
        }

        public Criteria andVersionEqualTo(Integer value) {
            addCriterion("version =", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotEqualTo(Integer value) {
            addCriterion("version <>", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThan(Integer value) {
            addCriterion("version >", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThanOrEqualTo(Integer value) {
            addCriterion("version >=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThan(Integer value) {
            addCriterion("version <", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThanOrEqualTo(Integer value) {
            addCriterion("version <=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionIn(List<Integer> values) {
            addCriterion("version in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotIn(List<Integer> values) {
            addCriterion("version not in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionBetween(Integer value1, Integer value2) {
            addCriterion("version between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotBetween(Integer value1, Integer value2) {
            addCriterion("version not between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("created_by is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("created_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(Long value) {
            addCriterion("created_by =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(Long value) {
            addCriterion("created_by <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(Long value) {
            addCriterion("created_by >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(Long value) {
            addCriterion("created_by >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(Long value) {
            addCriterion("created_by <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(Long value) {
            addCriterion("created_by <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<Long> values) {
            addCriterion("created_by in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<Long> values) {
            addCriterion("created_by not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(Long value1, Long value2) {
            addCriterion("created_by between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(Long value1, Long value2) {
            addCriterion("created_by not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedNameIsNull() {
            addCriterion("created_name is null");
            return (Criteria) this;
        }

        public Criteria andCreatedNameIsNotNull() {
            addCriterion("created_name is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedNameEqualTo(String value) {
            addCriterion("created_name =", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotEqualTo(String value) {
            addCriterion("created_name <>", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameGreaterThan(String value) {
            addCriterion("created_name >", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameGreaterThanOrEqualTo(String value) {
            addCriterion("created_name >=", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameLessThan(String value) {
            addCriterion("created_name <", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameLessThanOrEqualTo(String value) {
            addCriterion("created_name <=", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameLike(String value) {
            addCriterion("created_name like", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotLike(String value) {
            addCriterion("created_name not like", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameIn(List<String> values) {
            addCriterion("created_name in", values, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotIn(List<String> values) {
            addCriterion("created_name not in", values, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameBetween(String value1, String value2) {
            addCriterion("created_name between", value1, value2, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotBetween(String value1, String value2) {
            addCriterion("created_name not between", value1, value2, "createdName");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNull() {
            addCriterion("updated_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNotNull() {
            addCriterion("updated_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByEqualTo(Long value) {
            addCriterion("updated_by =", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotEqualTo(Long value) {
            addCriterion("updated_by <>", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThan(Long value) {
            addCriterion("updated_by >", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThanOrEqualTo(Long value) {
            addCriterion("updated_by >=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThan(Long value) {
            addCriterion("updated_by <", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThanOrEqualTo(Long value) {
            addCriterion("updated_by <=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIn(List<Long> values) {
            addCriterion("updated_by in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotIn(List<Long> values) {
            addCriterion("updated_by not in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByBetween(Long value1, Long value2) {
            addCriterion("updated_by between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotBetween(Long value1, Long value2) {
            addCriterion("updated_by not between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameIsNull() {
            addCriterion("updated_name is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameIsNotNull() {
            addCriterion("updated_name is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameEqualTo(String value) {
            addCriterion("updated_name =", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotEqualTo(String value) {
            addCriterion("updated_name <>", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameGreaterThan(String value) {
            addCriterion("updated_name >", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameGreaterThanOrEqualTo(String value) {
            addCriterion("updated_name >=", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameLessThan(String value) {
            addCriterion("updated_name <", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameLessThanOrEqualTo(String value) {
            addCriterion("updated_name <=", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameLike(String value) {
            addCriterion("updated_name like", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotLike(String value) {
            addCriterion("updated_name not like", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameIn(List<String> values) {
            addCriterion("updated_name in", values, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotIn(List<String> values) {
            addCriterion("updated_name not in", values, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameBetween(String value1, String value2) {
            addCriterion("updated_name between", value1, value2, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotBetween(String value1, String value2) {
            addCriterion("updated_name not between", value1, value2, "updatedName");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}