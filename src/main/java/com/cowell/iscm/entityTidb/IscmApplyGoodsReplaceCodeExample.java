package com.cowell.iscm.entityTidb;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class IscmApplyGoodsReplaceCodeExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Long offset;

    public IscmApplyGoodsReplaceCodeExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Long offset) {
        this.offset = offset;
    }

    public Long getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIsNull() {
            addCriterion("company_code is null");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIsNotNull() {
            addCriterion("company_code is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeEqualTo(String value) {
            addCriterion("company_code =", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotEqualTo(String value) {
            addCriterion("company_code <>", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeGreaterThan(String value) {
            addCriterion("company_code >", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeGreaterThanOrEqualTo(String value) {
            addCriterion("company_code >=", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLessThan(String value) {
            addCriterion("company_code <", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLessThanOrEqualTo(String value) {
            addCriterion("company_code <=", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLike(String value) {
            addCriterion("company_code like", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotLike(String value) {
            addCriterion("company_code not like", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIn(List<String> values) {
            addCriterion("company_code in", values, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotIn(List<String> values) {
            addCriterion("company_code not in", values, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeBetween(String value1, String value2) {
            addCriterion("company_code between", value1, value2, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotBetween(String value1, String value2) {
            addCriterion("company_code not between", value1, value2, "companyCode");
            return (Criteria) this;
        }

        public Criteria andMasterGoodsNoIsNull() {
            addCriterion("master_goods_no is null");
            return (Criteria) this;
        }

        public Criteria andMasterGoodsNoIsNotNull() {
            addCriterion("master_goods_no is not null");
            return (Criteria) this;
        }

        public Criteria andMasterGoodsNoEqualTo(String value) {
            addCriterion("master_goods_no =", value, "masterGoodsNo");
            return (Criteria) this;
        }

        public Criteria andMasterGoodsNoNotEqualTo(String value) {
            addCriterion("master_goods_no <>", value, "masterGoodsNo");
            return (Criteria) this;
        }

        public Criteria andMasterGoodsNoGreaterThan(String value) {
            addCriterion("master_goods_no >", value, "masterGoodsNo");
            return (Criteria) this;
        }

        public Criteria andMasterGoodsNoGreaterThanOrEqualTo(String value) {
            addCriterion("master_goods_no >=", value, "masterGoodsNo");
            return (Criteria) this;
        }

        public Criteria andMasterGoodsNoLessThan(String value) {
            addCriterion("master_goods_no <", value, "masterGoodsNo");
            return (Criteria) this;
        }

        public Criteria andMasterGoodsNoLessThanOrEqualTo(String value) {
            addCriterion("master_goods_no <=", value, "masterGoodsNo");
            return (Criteria) this;
        }

        public Criteria andMasterGoodsNoLike(String value) {
            addCriterion("master_goods_no like", value, "masterGoodsNo");
            return (Criteria) this;
        }

        public Criteria andMasterGoodsNoNotLike(String value) {
            addCriterion("master_goods_no not like", value, "masterGoodsNo");
            return (Criteria) this;
        }

        public Criteria andMasterGoodsNoIn(List<String> values) {
            addCriterion("master_goods_no in", values, "masterGoodsNo");
            return (Criteria) this;
        }

        public Criteria andMasterGoodsNoNotIn(List<String> values) {
            addCriterion("master_goods_no not in", values, "masterGoodsNo");
            return (Criteria) this;
        }

        public Criteria andMasterGoodsNoBetween(String value1, String value2) {
            addCriterion("master_goods_no between", value1, value2, "masterGoodsNo");
            return (Criteria) this;
        }

        public Criteria andMasterGoodsNoNotBetween(String value1, String value2) {
            addCriterion("master_goods_no not between", value1, value2, "masterGoodsNo");
            return (Criteria) this;
        }

        public Criteria andReplaceGoodsNoIsNull() {
            addCriterion("replace_goods_no is null");
            return (Criteria) this;
        }

        public Criteria andReplaceGoodsNoIsNotNull() {
            addCriterion("replace_goods_no is not null");
            return (Criteria) this;
        }

        public Criteria andReplaceGoodsNoEqualTo(String value) {
            addCriterion("replace_goods_no =", value, "replaceGoodsNo");
            return (Criteria) this;
        }

        public Criteria andReplaceGoodsNoNotEqualTo(String value) {
            addCriterion("replace_goods_no <>", value, "replaceGoodsNo");
            return (Criteria) this;
        }

        public Criteria andReplaceGoodsNoGreaterThan(String value) {
            addCriterion("replace_goods_no >", value, "replaceGoodsNo");
            return (Criteria) this;
        }

        public Criteria andReplaceGoodsNoGreaterThanOrEqualTo(String value) {
            addCriterion("replace_goods_no >=", value, "replaceGoodsNo");
            return (Criteria) this;
        }

        public Criteria andReplaceGoodsNoLessThan(String value) {
            addCriterion("replace_goods_no <", value, "replaceGoodsNo");
            return (Criteria) this;
        }

        public Criteria andReplaceGoodsNoLessThanOrEqualTo(String value) {
            addCriterion("replace_goods_no <=", value, "replaceGoodsNo");
            return (Criteria) this;
        }

        public Criteria andReplaceGoodsNoLike(String value) {
            addCriterion("replace_goods_no like", value, "replaceGoodsNo");
            return (Criteria) this;
        }

        public Criteria andReplaceGoodsNoNotLike(String value) {
            addCriterion("replace_goods_no not like", value, "replaceGoodsNo");
            return (Criteria) this;
        }

        public Criteria andReplaceGoodsNoIn(List<String> values) {
            addCriterion("replace_goods_no in", values, "replaceGoodsNo");
            return (Criteria) this;
        }

        public Criteria andReplaceGoodsNoNotIn(List<String> values) {
            addCriterion("replace_goods_no not in", values, "replaceGoodsNo");
            return (Criteria) this;
        }

        public Criteria andReplaceGoodsNoBetween(String value1, String value2) {
            addCriterion("replace_goods_no between", value1, value2, "replaceGoodsNo");
            return (Criteria) this;
        }

        public Criteria andReplaceGoodsNoNotBetween(String value1, String value2) {
            addCriterion("replace_goods_no not between", value1, value2, "replaceGoodsNo");
            return (Criteria) this;
        }

        public Criteria andChangeRatioIsNull() {
            addCriterion("change_ratio is null");
            return (Criteria) this;
        }

        public Criteria andChangeRatioIsNotNull() {
            addCriterion("change_ratio is not null");
            return (Criteria) this;
        }

        public Criteria andChangeRatioEqualTo(BigDecimal value) {
            addCriterion("change_ratio =", value, "changeRatio");
            return (Criteria) this;
        }

        public Criteria andChangeRatioNotEqualTo(BigDecimal value) {
            addCriterion("change_ratio <>", value, "changeRatio");
            return (Criteria) this;
        }

        public Criteria andChangeRatioGreaterThan(BigDecimal value) {
            addCriterion("change_ratio >", value, "changeRatio");
            return (Criteria) this;
        }

        public Criteria andChangeRatioGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("change_ratio >=", value, "changeRatio");
            return (Criteria) this;
        }

        public Criteria andChangeRatioLessThan(BigDecimal value) {
            addCriterion("change_ratio <", value, "changeRatio");
            return (Criteria) this;
        }

        public Criteria andChangeRatioLessThanOrEqualTo(BigDecimal value) {
            addCriterion("change_ratio <=", value, "changeRatio");
            return (Criteria) this;
        }

        public Criteria andChangeRatioIn(List<BigDecimal> values) {
            addCriterion("change_ratio in", values, "changeRatio");
            return (Criteria) this;
        }

        public Criteria andChangeRatioNotIn(List<BigDecimal> values) {
            addCriterion("change_ratio not in", values, "changeRatio");
            return (Criteria) this;
        }

        public Criteria andChangeRatioBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("change_ratio between", value1, value2, "changeRatio");
            return (Criteria) this;
        }

        public Criteria andChangeRatioNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("change_ratio not between", value1, value2, "changeRatio");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIsNull() {
            addCriterion("gmt_update is null");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIsNotNull() {
            addCriterion("gmt_update is not null");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateEqualTo(Date value) {
            addCriterion("gmt_update =", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotEqualTo(Date value) {
            addCriterion("gmt_update <>", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateGreaterThan(Date value) {
            addCriterion("gmt_update >", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_update >=", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateLessThan(Date value) {
            addCriterion("gmt_update <", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_update <=", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIn(List<Date> values) {
            addCriterion("gmt_update in", values, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotIn(List<Date> values) {
            addCriterion("gmt_update not in", values, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateBetween(Date value1, Date value2) {
            addCriterion("gmt_update between", value1, value2, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_update not between", value1, value2, "gmtUpdate");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}