package com.cowell.iscm.entityTidb;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * iscm_suggest_goods_allot
 * <AUTHOR>
public class IscmSuggestGoodsAllot implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 业务日期
     */
    private Date businessDate;

    /**
     * 调拨单号
     */
    private String allotNo;

    /**
     * 调拨明细行号
     */
    private String allotDetailNo;

    /**
     * 登记单号
     */
    private String registerNo;

    /**
     * 登记类型,1:近效期调拨,2:大库存、滞销商品调拨,3:断货商品调拨
     */
    private Byte allotType;

    /**
     * 登记来源 0:海典POS 1:ISCM智慧供应链平台 2:BDP自动登记
     */
    private Byte registerSource;

    /**
     * 调出公司MDM编码
     */
    private String outCompanyCode;

    /**
     * 调入公司MDM编码
     */
    private String inCompanyCode;

    /**
     * 调出门店MDM编码
     */
    private String outStoreCode;

    /**
     * 调入门店MDM编码
     */
    private String inStoreCode;

    /**
     * 商品编码
     */
    private String goodsNo;

    /**
     * 批号
     */
    private String batchNo;

    /**
     * 建议调出数量
     */
    private BigDecimal suggestAllotQuantity;

    /**
     * 实际调出数量
     */
    private BigDecimal realAllotQuantity;

    /**
     * 预计销售天数
     */
    private BigDecimal expectSaleDays;

    /**
     * 审批状态 0:未审批 1:审批通过 2:作废
     */
    private Byte approveStatus;

    /**
     * 审批人ID
     */
    private Long approveBy;

    /**
     * 审批人
     */
    private String approveName;

    /**
     * 审批时间
     */
    private Date approveTime;

    /**
     * 状态(-1删除，0正常)
     */
    private Byte status;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新时间
     */
    private Date gmtUpdate;

    /**
     * 扩展字段
     */
    private String extend;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 创建人ID
     */
    private Long createdBy;

    /**
     * 创建人
     */
    private String createdName;

    /**
     * 更新人ID
     */
    private Long updatedBy;

    /**
     * 更新人
     */
    private String updatedName;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getBusinessDate() {
        return businessDate;
    }

    public void setBusinessDate(Date businessDate) {
        this.businessDate = businessDate;
    }

    public String getAllotNo() {
        return allotNo;
    }

    public void setAllotNo(String allotNo) {
        this.allotNo = allotNo;
    }

    public String getAllotDetailNo() {
        return allotDetailNo;
    }

    public void setAllotDetailNo(String allotDetailNo) {
        this.allotDetailNo = allotDetailNo;
    }

    public String getRegisterNo() {
        return registerNo;
    }

    public void setRegisterNo(String registerNo) {
        this.registerNo = registerNo;
    }

    public Byte getAllotType() {
        return allotType;
    }

    public void setAllotType(Byte allotType) {
        this.allotType = allotType;
    }

    public Byte getRegisterSource() {
        return registerSource;
    }

    public void setRegisterSource(Byte registerSource) {
        this.registerSource = registerSource;
    }

    public String getOutCompanyCode() {
        return outCompanyCode;
    }

    public void setOutCompanyCode(String outCompanyCode) {
        this.outCompanyCode = outCompanyCode;
    }

    public String getInCompanyCode() {
        return inCompanyCode;
    }

    public void setInCompanyCode(String inCompanyCode) {
        this.inCompanyCode = inCompanyCode;
    }

    public String getOutStoreCode() {
        return outStoreCode;
    }

    public void setOutStoreCode(String outStoreCode) {
        this.outStoreCode = outStoreCode;
    }

    public String getInStoreCode() {
        return inStoreCode;
    }

    public void setInStoreCode(String inStoreCode) {
        this.inStoreCode = inStoreCode;
    }

    public String getGoodsNo() {
        return goodsNo;
    }

    public void setGoodsNo(String goodsNo) {
        this.goodsNo = goodsNo;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public BigDecimal getSuggestAllotQuantity() {
        return suggestAllotQuantity;
    }

    public void setSuggestAllotQuantity(BigDecimal suggestAllotQuantity) {
        this.suggestAllotQuantity = suggestAllotQuantity;
    }

    public BigDecimal getRealAllotQuantity() {
        return realAllotQuantity;
    }

    public void setRealAllotQuantity(BigDecimal realAllotQuantity) {
        this.realAllotQuantity = realAllotQuantity;
    }

    public BigDecimal getExpectSaleDays() {
        return expectSaleDays;
    }

    public void setExpectSaleDays(BigDecimal expectSaleDays) {
        this.expectSaleDays = expectSaleDays;
    }

    public Byte getApproveStatus() {
        return approveStatus;
    }

    public void setApproveStatus(Byte approveStatus) {
        this.approveStatus = approveStatus;
    }

    public Long getApproveBy() {
        return approveBy;
    }

    public void setApproveBy(Long approveBy) {
        this.approveBy = approveBy;
    }

    public String getApproveName() {
        return approveName;
    }

    public void setApproveName(String approveName) {
        this.approveName = approveName;
    }

    public Date getApproveTime() {
        return approveTime;
    }

    public void setApproveTime(Date approveTime) {
        this.approveTime = approveTime;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtUpdate() {
        return gmtUpdate;
    }

    public void setGmtUpdate(Date gmtUpdate) {
        this.gmtUpdate = gmtUpdate;
    }

    public String getExtend() {
        return extend;
    }

    public void setExtend(String extend) {
        this.extend = extend;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Long getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    public String getCreatedName() {
        return createdName;
    }

    public void setCreatedName(String createdName) {
        this.createdName = createdName;
    }

    public Long getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(Long updatedBy) {
        this.updatedBy = updatedBy;
    }

    public String getUpdatedName() {
        return updatedName;
    }

    public void setUpdatedName(String updatedName) {
        this.updatedName = updatedName;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        IscmSuggestGoodsAllot other = (IscmSuggestGoodsAllot) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getBusinessDate() == null ? other.getBusinessDate() == null : this.getBusinessDate().equals(other.getBusinessDate()))
            && (this.getAllotNo() == null ? other.getAllotNo() == null : this.getAllotNo().equals(other.getAllotNo()))
            && (this.getAllotDetailNo() == null ? other.getAllotDetailNo() == null : this.getAllotDetailNo().equals(other.getAllotDetailNo()))
            && (this.getRegisterNo() == null ? other.getRegisterNo() == null : this.getRegisterNo().equals(other.getRegisterNo()))
            && (this.getAllotType() == null ? other.getAllotType() == null : this.getAllotType().equals(other.getAllotType()))
            && (this.getRegisterSource() == null ? other.getRegisterSource() == null : this.getRegisterSource().equals(other.getRegisterSource()))
            && (this.getOutCompanyCode() == null ? other.getOutCompanyCode() == null : this.getOutCompanyCode().equals(other.getOutCompanyCode()))
            && (this.getInCompanyCode() == null ? other.getInCompanyCode() == null : this.getInCompanyCode().equals(other.getInCompanyCode()))
            && (this.getOutStoreCode() == null ? other.getOutStoreCode() == null : this.getOutStoreCode().equals(other.getOutStoreCode()))
            && (this.getInStoreCode() == null ? other.getInStoreCode() == null : this.getInStoreCode().equals(other.getInStoreCode()))
            && (this.getGoodsNo() == null ? other.getGoodsNo() == null : this.getGoodsNo().equals(other.getGoodsNo()))
            && (this.getBatchNo() == null ? other.getBatchNo() == null : this.getBatchNo().equals(other.getBatchNo()))
            && (this.getSuggestAllotQuantity() == null ? other.getSuggestAllotQuantity() == null : this.getSuggestAllotQuantity().equals(other.getSuggestAllotQuantity()))
            && (this.getRealAllotQuantity() == null ? other.getRealAllotQuantity() == null : this.getRealAllotQuantity().equals(other.getRealAllotQuantity()))
            && (this.getExpectSaleDays() == null ? other.getExpectSaleDays() == null : this.getExpectSaleDays().equals(other.getExpectSaleDays()))
            && (this.getApproveStatus() == null ? other.getApproveStatus() == null : this.getApproveStatus().equals(other.getApproveStatus()))
            && (this.getApproveBy() == null ? other.getApproveBy() == null : this.getApproveBy().equals(other.getApproveBy()))
            && (this.getApproveName() == null ? other.getApproveName() == null : this.getApproveName().equals(other.getApproveName()))
            && (this.getApproveTime() == null ? other.getApproveTime() == null : this.getApproveTime().equals(other.getApproveTime()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getGmtCreate() == null ? other.getGmtCreate() == null : this.getGmtCreate().equals(other.getGmtCreate()))
            && (this.getGmtUpdate() == null ? other.getGmtUpdate() == null : this.getGmtUpdate().equals(other.getGmtUpdate()))
            && (this.getExtend() == null ? other.getExtend() == null : this.getExtend().equals(other.getExtend()))
            && (this.getVersion() == null ? other.getVersion() == null : this.getVersion().equals(other.getVersion()))
            && (this.getCreatedBy() == null ? other.getCreatedBy() == null : this.getCreatedBy().equals(other.getCreatedBy()))
            && (this.getCreatedName() == null ? other.getCreatedName() == null : this.getCreatedName().equals(other.getCreatedName()))
            && (this.getUpdatedBy() == null ? other.getUpdatedBy() == null : this.getUpdatedBy().equals(other.getUpdatedBy()))
            && (this.getUpdatedName() == null ? other.getUpdatedName() == null : this.getUpdatedName().equals(other.getUpdatedName()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getBusinessDate() == null) ? 0 : getBusinessDate().hashCode());
        result = prime * result + ((getAllotNo() == null) ? 0 : getAllotNo().hashCode());
        result = prime * result + ((getAllotDetailNo() == null) ? 0 : getAllotDetailNo().hashCode());
        result = prime * result + ((getRegisterNo() == null) ? 0 : getRegisterNo().hashCode());
        result = prime * result + ((getAllotType() == null) ? 0 : getAllotType().hashCode());
        result = prime * result + ((getRegisterSource() == null) ? 0 : getRegisterSource().hashCode());
        result = prime * result + ((getOutCompanyCode() == null) ? 0 : getOutCompanyCode().hashCode());
        result = prime * result + ((getInCompanyCode() == null) ? 0 : getInCompanyCode().hashCode());
        result = prime * result + ((getOutStoreCode() == null) ? 0 : getOutStoreCode().hashCode());
        result = prime * result + ((getInStoreCode() == null) ? 0 : getInStoreCode().hashCode());
        result = prime * result + ((getGoodsNo() == null) ? 0 : getGoodsNo().hashCode());
        result = prime * result + ((getBatchNo() == null) ? 0 : getBatchNo().hashCode());
        result = prime * result + ((getSuggestAllotQuantity() == null) ? 0 : getSuggestAllotQuantity().hashCode());
        result = prime * result + ((getRealAllotQuantity() == null) ? 0 : getRealAllotQuantity().hashCode());
        result = prime * result + ((getExpectSaleDays() == null) ? 0 : getExpectSaleDays().hashCode());
        result = prime * result + ((getApproveStatus() == null) ? 0 : getApproveStatus().hashCode());
        result = prime * result + ((getApproveBy() == null) ? 0 : getApproveBy().hashCode());
        result = prime * result + ((getApproveName() == null) ? 0 : getApproveName().hashCode());
        result = prime * result + ((getApproveTime() == null) ? 0 : getApproveTime().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getGmtCreate() == null) ? 0 : getGmtCreate().hashCode());
        result = prime * result + ((getGmtUpdate() == null) ? 0 : getGmtUpdate().hashCode());
        result = prime * result + ((getExtend() == null) ? 0 : getExtend().hashCode());
        result = prime * result + ((getVersion() == null) ? 0 : getVersion().hashCode());
        result = prime * result + ((getCreatedBy() == null) ? 0 : getCreatedBy().hashCode());
        result = prime * result + ((getCreatedName() == null) ? 0 : getCreatedName().hashCode());
        result = prime * result + ((getUpdatedBy() == null) ? 0 : getUpdatedBy().hashCode());
        result = prime * result + ((getUpdatedName() == null) ? 0 : getUpdatedName().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", businessDate=").append(businessDate);
        sb.append(", allotNo=").append(allotNo);
        sb.append(", allotDetailNo=").append(allotDetailNo);
        sb.append(", registerNo=").append(registerNo);
        sb.append(", allotType=").append(allotType);
        sb.append(", registerSource=").append(registerSource);
        sb.append(", outCompanyCode=").append(outCompanyCode);
        sb.append(", inCompanyCode=").append(inCompanyCode);
        sb.append(", outStoreCode=").append(outStoreCode);
        sb.append(", inStoreCode=").append(inStoreCode);
        sb.append(", goodsNo=").append(goodsNo);
        sb.append(", batchNo=").append(batchNo);
        sb.append(", suggestAllotQuantity=").append(suggestAllotQuantity);
        sb.append(", realAllotQuantity=").append(realAllotQuantity);
        sb.append(", expectSaleDays=").append(expectSaleDays);
        sb.append(", approveStatus=").append(approveStatus);
        sb.append(", approveBy=").append(approveBy);
        sb.append(", approveName=").append(approveName);
        sb.append(", approveTime=").append(approveTime);
        sb.append(", status=").append(status);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", gmtUpdate=").append(gmtUpdate);
        sb.append(", extend=").append(extend);
        sb.append(", version=").append(version);
        sb.append(", createdBy=").append(createdBy);
        sb.append(", createdName=").append(createdName);
        sb.append(", updatedBy=").append(updatedBy);
        sb.append(", updatedName=").append(updatedName);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}