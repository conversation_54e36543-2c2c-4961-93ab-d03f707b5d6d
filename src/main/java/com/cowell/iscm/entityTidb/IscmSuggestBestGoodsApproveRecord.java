package com.cowell.iscm.entityTidb;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> 好品推荐审批记录
 */
public class IscmSuggestBestGoodsApproveRecord implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 推荐日期
     */
    private Date suggestDate;

    /**
     * 公司MDM编码
     */
    private String companyCode;

    /**
     * 门店MDM编码
     */
    private String storeCode;

    /**
     * 推送海典状态 0否 1是
     */
    private Byte pushHdStatus;

    /**
     * 商品编码
     */
    private String goodsNo;

    /**
     * 商品名
     */
    private String goodsName;

    /**
     * 规格
     */
    private String specifications;

    /**
     * 剂型
     */
    private String dosageForm;

    /**
     * 生产厂家
     */
    private String manufacturer;

    /**
     * 成分
     */
    private String component;

    /**
     * 榜样店历史30天销售数量
     */
    private BigDecimal thirtySalesQuantity;

    /**
     * 参考零售价
     */
    private BigDecimal referRetailPrice;

    /**
     * 参考毛利率
     */
    private BigDecimal referGrossProfit;

    /**
     * 销售排名
     */
    private Integer salesRank;

    /**
     * 销售属性(推类等级)
     */
    private String pushLevel;

    /**
     * 用药人群
     */
    private String medicinePeople;

    /**
     * 是否OTC 0否1是
     */
    private Byte otcAble;

    /**
     * 是否rx 0否1是
     */
    private Byte rxAble;

    /**
     * 是否医保 0否1是
     */
    private Byte ybAble;

    /**
     * 是否敏感品 0否1是
     */
    private Byte sensitiveAble;

    /**
     * 商品子类id
     */
    private Long categoryId;

    /**
     * 商品子类名称
     */
    private String category;

    /**
     * 促销标签
     */
    private String promotionTag;

    /**
     * 提成规则名称
     */
    private String promotionName;

    /**
     * 奖励方式
     */
    private String promotionWay;

    /**
     * 奖励类型
     */
    private String thresholdInfo;

    /**
     * 奖励
     */
    private String favInfo;

    /**
     * 推荐原因
     */
    private String recommendReason;

    /**
     * 是否新成分 0否 1是
     */
    private Byte compositeNew;

    /**
     * 功能主治
     */
    private String indications;

    /**
     * 图片链接 多个,分割
     */
    private String pictureUrl;

    /**
     * 推荐类型(0推荐 1淘汰)
     */
    private Byte suggestType;

    /**
     * 处理建议(1 采纳 2 拒绝)
     */
    private Byte dealSuggest;

    /**
     * 补充数量
     */
    private Integer complementQty;

    /**
     * 状态(-1删除，0正常)
     */
    private Byte status;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新时间
     */
    private Date gmtUpdate;

    /**
     * 扩展字段
     */
    private String extend;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 创建人ID
     */
    private Long createdBy;

    /**
     * 创建人
     */
    private String createdName;

    /**
     * 更新人ID
     */
    private Long updatedBy;

    /**
     * 更新人
     */
    private String updatedName;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getSuggestDate() {
        return suggestDate;
    }

    public void setSuggestDate(Date suggestDate) {
        this.suggestDate = suggestDate;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getStoreCode() {
        return storeCode;
    }

    public void setStoreCode(String storeCode) {
        this.storeCode = storeCode;
    }

    public Byte getPushHdStatus() {
        return pushHdStatus;
    }

    public void setPushHdStatus(Byte pushHdStatus) {
        this.pushHdStatus = pushHdStatus;
    }

    public String getGoodsNo() {
        return goodsNo;
    }

    public void setGoodsNo(String goodsNo) {
        this.goodsNo = goodsNo;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getSpecifications() {
        return specifications;
    }

    public void setSpecifications(String specifications) {
        this.specifications = specifications;
    }

    public String getDosageForm() {
        return dosageForm;
    }

    public void setDosageForm(String dosageForm) {
        this.dosageForm = dosageForm;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    public String getComponent() {
        return component;
    }

    public void setComponent(String component) {
        this.component = component;
    }

    public BigDecimal getThirtySalesQuantity() {
        return thirtySalesQuantity;
    }

    public void setThirtySalesQuantity(BigDecimal thirtySalesQuantity) {
        this.thirtySalesQuantity = thirtySalesQuantity;
    }

    public BigDecimal getReferRetailPrice() {
        return referRetailPrice;
    }

    public void setReferRetailPrice(BigDecimal referRetailPrice) {
        this.referRetailPrice = referRetailPrice;
    }

    public BigDecimal getReferGrossProfit() {
        return referGrossProfit;
    }

    public void setReferGrossProfit(BigDecimal referGrossProfit) {
        this.referGrossProfit = referGrossProfit;
    }

    public Integer getSalesRank() {
        return salesRank;
    }

    public void setSalesRank(Integer salesRank) {
        this.salesRank = salesRank;
    }

    public String getPushLevel() {
        return pushLevel;
    }

    public void setPushLevel(String pushLevel) {
        this.pushLevel = pushLevel;
    }

    public String getMedicinePeople() {
        return medicinePeople;
    }

    public void setMedicinePeople(String medicinePeople) {
        this.medicinePeople = medicinePeople;
    }

    public Byte getOtcAble() {
        return otcAble;
    }

    public void setOtcAble(Byte otcAble) {
        this.otcAble = otcAble;
    }

    public Byte getRxAble() {
        return rxAble;
    }

    public void setRxAble(Byte rxAble) {
        this.rxAble = rxAble;
    }

    public Byte getYbAble() {
        return ybAble;
    }

    public void setYbAble(Byte ybAble) {
        this.ybAble = ybAble;
    }

    public Byte getSensitiveAble() {
        return sensitiveAble;
    }

    public void setSensitiveAble(Byte sensitiveAble) {
        this.sensitiveAble = sensitiveAble;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getPromotionTag() {
        return promotionTag;
    }

    public void setPromotionTag(String promotionTag) {
        this.promotionTag = promotionTag;
    }

    public String getPromotionName() {
        return promotionName;
    }

    public void setPromotionName(String promotionName) {
        this.promotionName = promotionName;
    }

    public String getPromotionWay() {
        return promotionWay;
    }

    public void setPromotionWay(String promotionWay) {
        this.promotionWay = promotionWay;
    }

    public String getThresholdInfo() {
        return thresholdInfo;
    }

    public void setThresholdInfo(String thresholdInfo) {
        this.thresholdInfo = thresholdInfo;
    }

    public String getFavInfo() {
        return favInfo;
    }

    public void setFavInfo(String favInfo) {
        this.favInfo = favInfo;
    }

    public String getRecommendReason() {
        return recommendReason;
    }

    public void setRecommendReason(String recommendReason) {
        this.recommendReason = recommendReason;
    }

    public Byte getCompositeNew() {
        return compositeNew;
    }

    public void setCompositeNew(Byte compositeNew) {
        this.compositeNew = compositeNew;
    }

    public String getIndications() {
        return indications;
    }

    public void setIndications(String indications) {
        this.indications = indications;
    }

    public String getPictureUrl() {
        return pictureUrl;
    }

    public void setPictureUrl(String pictureUrl) {
        this.pictureUrl = pictureUrl;
    }

    public Byte getSuggestType() {
        return suggestType;
    }

    public void setSuggestType(Byte suggestType) {
        this.suggestType = suggestType;
    }

    public Byte getDealSuggest() {
        return dealSuggest;
    }

    public void setDealSuggest(Byte dealSuggest) {
        this.dealSuggest = dealSuggest;
    }

    public Integer getComplementQty() {
        return complementQty;
    }

    public void setComplementQty(Integer complementQty) {
        this.complementQty = complementQty;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtUpdate() {
        return gmtUpdate;
    }

    public void setGmtUpdate(Date gmtUpdate) {
        this.gmtUpdate = gmtUpdate;
    }

    public String getExtend() {
        return extend;
    }

    public void setExtend(String extend) {
        this.extend = extend;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Long getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    public String getCreatedName() {
        return createdName;
    }

    public void setCreatedName(String createdName) {
        this.createdName = createdName;
    }

    public Long getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(Long updatedBy) {
        this.updatedBy = updatedBy;
    }

    public String getUpdatedName() {
        return updatedName;
    }

    public void setUpdatedName(String updatedName) {
        this.updatedName = updatedName;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        IscmSuggestBestGoodsApproveRecord other = (IscmSuggestBestGoodsApproveRecord) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getSuggestDate() == null ? other.getSuggestDate() == null : this.getSuggestDate().equals(other.getSuggestDate()))
            && (this.getCompanyCode() == null ? other.getCompanyCode() == null : this.getCompanyCode().equals(other.getCompanyCode()))
            && (this.getStoreCode() == null ? other.getStoreCode() == null : this.getStoreCode().equals(other.getStoreCode()))
            && (this.getPushHdStatus() == null ? other.getPushHdStatus() == null : this.getPushHdStatus().equals(other.getPushHdStatus()))
            && (this.getGoodsNo() == null ? other.getGoodsNo() == null : this.getGoodsNo().equals(other.getGoodsNo()))
            && (this.getGoodsName() == null ? other.getGoodsName() == null : this.getGoodsName().equals(other.getGoodsName()))
            && (this.getSpecifications() == null ? other.getSpecifications() == null : this.getSpecifications().equals(other.getSpecifications()))
            && (this.getDosageForm() == null ? other.getDosageForm() == null : this.getDosageForm().equals(other.getDosageForm()))
            && (this.getManufacturer() == null ? other.getManufacturer() == null : this.getManufacturer().equals(other.getManufacturer()))
            && (this.getComponent() == null ? other.getComponent() == null : this.getComponent().equals(other.getComponent()))
            && (this.getThirtySalesQuantity() == null ? other.getThirtySalesQuantity() == null : this.getThirtySalesQuantity().equals(other.getThirtySalesQuantity()))
            && (this.getReferRetailPrice() == null ? other.getReferRetailPrice() == null : this.getReferRetailPrice().equals(other.getReferRetailPrice()))
            && (this.getReferGrossProfit() == null ? other.getReferGrossProfit() == null : this.getReferGrossProfit().equals(other.getReferGrossProfit()))
            && (this.getSalesRank() == null ? other.getSalesRank() == null : this.getSalesRank().equals(other.getSalesRank()))
            && (this.getPushLevel() == null ? other.getPushLevel() == null : this.getPushLevel().equals(other.getPushLevel()))
            && (this.getMedicinePeople() == null ? other.getMedicinePeople() == null : this.getMedicinePeople().equals(other.getMedicinePeople()))
            && (this.getOtcAble() == null ? other.getOtcAble() == null : this.getOtcAble().equals(other.getOtcAble()))
            && (this.getRxAble() == null ? other.getRxAble() == null : this.getRxAble().equals(other.getRxAble()))
            && (this.getYbAble() == null ? other.getYbAble() == null : this.getYbAble().equals(other.getYbAble()))
            && (this.getSensitiveAble() == null ? other.getSensitiveAble() == null : this.getSensitiveAble().equals(other.getSensitiveAble()))
            && (this.getCategoryId() == null ? other.getCategoryId() == null : this.getCategoryId().equals(other.getCategoryId()))
            && (this.getCategory() == null ? other.getCategory() == null : this.getCategory().equals(other.getCategory()))
            && (this.getPromotionTag() == null ? other.getPromotionTag() == null : this.getPromotionTag().equals(other.getPromotionTag()))
            && (this.getPromotionName() == null ? other.getPromotionName() == null : this.getPromotionName().equals(other.getPromotionName()))
            && (this.getPromotionWay() == null ? other.getPromotionWay() == null : this.getPromotionWay().equals(other.getPromotionWay()))
            && (this.getThresholdInfo() == null ? other.getThresholdInfo() == null : this.getThresholdInfo().equals(other.getThresholdInfo()))
            && (this.getFavInfo() == null ? other.getFavInfo() == null : this.getFavInfo().equals(other.getFavInfo()))
            && (this.getRecommendReason() == null ? other.getRecommendReason() == null : this.getRecommendReason().equals(other.getRecommendReason()))
            && (this.getCompositeNew() == null ? other.getCompositeNew() == null : this.getCompositeNew().equals(other.getCompositeNew()))
            && (this.getIndications() == null ? other.getIndications() == null : this.getIndications().equals(other.getIndications()))
            && (this.getPictureUrl() == null ? other.getPictureUrl() == null : this.getPictureUrl().equals(other.getPictureUrl()))
            && (this.getSuggestType() == null ? other.getSuggestType() == null : this.getSuggestType().equals(other.getSuggestType()))
            && (this.getDealSuggest() == null ? other.getDealSuggest() == null : this.getDealSuggest().equals(other.getDealSuggest()))
            && (this.getComplementQty() == null ? other.getComplementQty() == null : this.getComplementQty().equals(other.getComplementQty()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getGmtCreate() == null ? other.getGmtCreate() == null : this.getGmtCreate().equals(other.getGmtCreate()))
            && (this.getGmtUpdate() == null ? other.getGmtUpdate() == null : this.getGmtUpdate().equals(other.getGmtUpdate()))
            && (this.getExtend() == null ? other.getExtend() == null : this.getExtend().equals(other.getExtend()))
            && (this.getVersion() == null ? other.getVersion() == null : this.getVersion().equals(other.getVersion()))
            && (this.getCreatedBy() == null ? other.getCreatedBy() == null : this.getCreatedBy().equals(other.getCreatedBy()))
            && (this.getCreatedName() == null ? other.getCreatedName() == null : this.getCreatedName().equals(other.getCreatedName()))
            && (this.getUpdatedBy() == null ? other.getUpdatedBy() == null : this.getUpdatedBy().equals(other.getUpdatedBy()))
            && (this.getUpdatedName() == null ? other.getUpdatedName() == null : this.getUpdatedName().equals(other.getUpdatedName()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getSuggestDate() == null) ? 0 : getSuggestDate().hashCode());
        result = prime * result + ((getCompanyCode() == null) ? 0 : getCompanyCode().hashCode());
        result = prime * result + ((getStoreCode() == null) ? 0 : getStoreCode().hashCode());
        result = prime * result + ((getPushHdStatus() == null) ? 0 : getPushHdStatus().hashCode());
        result = prime * result + ((getGoodsNo() == null) ? 0 : getGoodsNo().hashCode());
        result = prime * result + ((getGoodsName() == null) ? 0 : getGoodsName().hashCode());
        result = prime * result + ((getSpecifications() == null) ? 0 : getSpecifications().hashCode());
        result = prime * result + ((getDosageForm() == null) ? 0 : getDosageForm().hashCode());
        result = prime * result + ((getManufacturer() == null) ? 0 : getManufacturer().hashCode());
        result = prime * result + ((getComponent() == null) ? 0 : getComponent().hashCode());
        result = prime * result + ((getThirtySalesQuantity() == null) ? 0 : getThirtySalesQuantity().hashCode());
        result = prime * result + ((getReferRetailPrice() == null) ? 0 : getReferRetailPrice().hashCode());
        result = prime * result + ((getReferGrossProfit() == null) ? 0 : getReferGrossProfit().hashCode());
        result = prime * result + ((getSalesRank() == null) ? 0 : getSalesRank().hashCode());
        result = prime * result + ((getPushLevel() == null) ? 0 : getPushLevel().hashCode());
        result = prime * result + ((getMedicinePeople() == null) ? 0 : getMedicinePeople().hashCode());
        result = prime * result + ((getOtcAble() == null) ? 0 : getOtcAble().hashCode());
        result = prime * result + ((getRxAble() == null) ? 0 : getRxAble().hashCode());
        result = prime * result + ((getYbAble() == null) ? 0 : getYbAble().hashCode());
        result = prime * result + ((getSensitiveAble() == null) ? 0 : getSensitiveAble().hashCode());
        result = prime * result + ((getCategoryId() == null) ? 0 : getCategoryId().hashCode());
        result = prime * result + ((getCategory() == null) ? 0 : getCategory().hashCode());
        result = prime * result + ((getPromotionTag() == null) ? 0 : getPromotionTag().hashCode());
        result = prime * result + ((getPromotionName() == null) ? 0 : getPromotionName().hashCode());
        result = prime * result + ((getPromotionWay() == null) ? 0 : getPromotionWay().hashCode());
        result = prime * result + ((getThresholdInfo() == null) ? 0 : getThresholdInfo().hashCode());
        result = prime * result + ((getFavInfo() == null) ? 0 : getFavInfo().hashCode());
        result = prime * result + ((getRecommendReason() == null) ? 0 : getRecommendReason().hashCode());
        result = prime * result + ((getCompositeNew() == null) ? 0 : getCompositeNew().hashCode());
        result = prime * result + ((getIndications() == null) ? 0 : getIndications().hashCode());
        result = prime * result + ((getPictureUrl() == null) ? 0 : getPictureUrl().hashCode());
        result = prime * result + ((getSuggestType() == null) ? 0 : getSuggestType().hashCode());
        result = prime * result + ((getDealSuggest() == null) ? 0 : getDealSuggest().hashCode());
        result = prime * result + ((getComplementQty() == null) ? 0 : getComplementQty().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getGmtCreate() == null) ? 0 : getGmtCreate().hashCode());
        result = prime * result + ((getGmtUpdate() == null) ? 0 : getGmtUpdate().hashCode());
        result = prime * result + ((getExtend() == null) ? 0 : getExtend().hashCode());
        result = prime * result + ((getVersion() == null) ? 0 : getVersion().hashCode());
        result = prime * result + ((getCreatedBy() == null) ? 0 : getCreatedBy().hashCode());
        result = prime * result + ((getCreatedName() == null) ? 0 : getCreatedName().hashCode());
        result = prime * result + ((getUpdatedBy() == null) ? 0 : getUpdatedBy().hashCode());
        result = prime * result + ((getUpdatedName() == null) ? 0 : getUpdatedName().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", suggestDate=").append(suggestDate);
        sb.append(", companyCode=").append(companyCode);
        sb.append(", storeCode=").append(storeCode);
        sb.append(", pushHdStatus=").append(pushHdStatus);
        sb.append(", goodsNo=").append(goodsNo);
        sb.append(", goodsName=").append(goodsName);
        sb.append(", specifications=").append(specifications);
        sb.append(", dosageForm=").append(dosageForm);
        sb.append(", manufacturer=").append(manufacturer);
        sb.append(", component=").append(component);
        sb.append(", thirtySalesQuantity=").append(thirtySalesQuantity);
        sb.append(", referRetailPrice=").append(referRetailPrice);
        sb.append(", referGrossProfit=").append(referGrossProfit);
        sb.append(", salesRank=").append(salesRank);
        sb.append(", pushLevel=").append(pushLevel);
        sb.append(", medicinePeople=").append(medicinePeople);
        sb.append(", otcAble=").append(otcAble);
        sb.append(", rxAble=").append(rxAble);
        sb.append(", ybAble=").append(ybAble);
        sb.append(", sensitiveAble=").append(sensitiveAble);
        sb.append(", categoryId=").append(categoryId);
        sb.append(", category=").append(category);
        sb.append(", promotionTag=").append(promotionTag);
        sb.append(", promotionName=").append(promotionName);
        sb.append(", promotionWay=").append(promotionWay);
        sb.append(", thresholdInfo=").append(thresholdInfo);
        sb.append(", favInfo=").append(favInfo);
        sb.append(", recommendReason=").append(recommendReason);
        sb.append(", compositeNew=").append(compositeNew);
        sb.append(", indications=").append(indications);
        sb.append(", pictureUrl=").append(pictureUrl);
        sb.append(", suggestType=").append(suggestType);
        sb.append(", dealSuggest=").append(dealSuggest);
        sb.append(", complementQty=").append(complementQty);
        sb.append(", status=").append(status);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", gmtUpdate=").append(gmtUpdate);
        sb.append(", extend=").append(extend);
        sb.append(", version=").append(version);
        sb.append(", createdBy=").append(createdBy);
        sb.append(", createdName=").append(createdName);
        sb.append(", updatedBy=").append(updatedBy);
        sb.append(", updatedName=").append(updatedName);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}