package com.cowell.iscm.entityTidb;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> 采购最低价表
 */
public class IscmPurchaseMinPrice implements Serializable {
    /**
     * 无业务逻辑主键
     */
    private Long id;

    /**
     * 传输日期
     */
    private Date transferDate;

    /**
     * 商品编码
     */
    private String goodsNo;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品名称长度
     */
    private Integer goodsNameLen;

    /**
     * 生产厂家编码
     */
    private String manufacturerCode;

    /**
     * 生产厂家
     */
    private String manufacturer;

    /**
     * 生产厂家长度
     */
    private Integer manufacturerLen;

    /**
     * 采购组织类型 1：平台 2：企业
     */
    private Byte purchaseOrgType;

    /**
     * 3个月采购最低价格
     */
    private BigDecimal threePurchaseMinPrice;

    /**
     * 6个月采购最低价格
     */
    private BigDecimal sixPurchaseMinPrice;

    /**
     * 12个月采购最低价格
     */
    private BigDecimal twelvePurchaseMinPrice;

    /**
     * 近3个月采购时间
     */
    private Date threePurchaseDate;

    /**
     * 近6个月采购时间
     */
    private Date sixPurchaseDate;

    /**
     * 近12个月采购时间
     */
    private Date twelvePurchaseDate;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新时间
     */
    private Date gmtUpdate;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getTransferDate() {
        return transferDate;
    }

    public void setTransferDate(Date transferDate) {
        this.transferDate = transferDate;
    }

    public String getGoodsNo() {
        return goodsNo;
    }

    public void setGoodsNo(String goodsNo) {
        this.goodsNo = goodsNo;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public Integer getGoodsNameLen() {
        return goodsNameLen;
    }

    public void setGoodsNameLen(Integer goodsNameLen) {
        this.goodsNameLen = goodsNameLen;
    }

    public String getManufacturerCode() {
        return manufacturerCode;
    }

    public void setManufacturerCode(String manufacturerCode) {
        this.manufacturerCode = manufacturerCode;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    public Integer getManufacturerLen() {
        return manufacturerLen;
    }

    public void setManufacturerLen(Integer manufacturerLen) {
        this.manufacturerLen = manufacturerLen;
    }

    public Byte getPurchaseOrgType() {
        return purchaseOrgType;
    }

    public void setPurchaseOrgType(Byte purchaseOrgType) {
        this.purchaseOrgType = purchaseOrgType;
    }

    public BigDecimal getThreePurchaseMinPrice() {
        return threePurchaseMinPrice;
    }

    public void setThreePurchaseMinPrice(BigDecimal threePurchaseMinPrice) {
        this.threePurchaseMinPrice = threePurchaseMinPrice;
    }

    public BigDecimal getSixPurchaseMinPrice() {
        return sixPurchaseMinPrice;
    }

    public void setSixPurchaseMinPrice(BigDecimal sixPurchaseMinPrice) {
        this.sixPurchaseMinPrice = sixPurchaseMinPrice;
    }

    public BigDecimal getTwelvePurchaseMinPrice() {
        return twelvePurchaseMinPrice;
    }

    public void setTwelvePurchaseMinPrice(BigDecimal twelvePurchaseMinPrice) {
        this.twelvePurchaseMinPrice = twelvePurchaseMinPrice;
    }

    public Date getThreePurchaseDate() {
        return threePurchaseDate;
    }

    public void setThreePurchaseDate(Date threePurchaseDate) {
        this.threePurchaseDate = threePurchaseDate;
    }

    public Date getSixPurchaseDate() {
        return sixPurchaseDate;
    }

    public void setSixPurchaseDate(Date sixPurchaseDate) {
        this.sixPurchaseDate = sixPurchaseDate;
    }

    public Date getTwelvePurchaseDate() {
        return twelvePurchaseDate;
    }

    public void setTwelvePurchaseDate(Date twelvePurchaseDate) {
        this.twelvePurchaseDate = twelvePurchaseDate;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtUpdate() {
        return gmtUpdate;
    }

    public void setGmtUpdate(Date gmtUpdate) {
        this.gmtUpdate = gmtUpdate;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        IscmPurchaseMinPrice other = (IscmPurchaseMinPrice) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getTransferDate() == null ? other.getTransferDate() == null : this.getTransferDate().equals(other.getTransferDate()))
            && (this.getGoodsNo() == null ? other.getGoodsNo() == null : this.getGoodsNo().equals(other.getGoodsNo()))
            && (this.getGoodsName() == null ? other.getGoodsName() == null : this.getGoodsName().equals(other.getGoodsName()))
            && (this.getGoodsNameLen() == null ? other.getGoodsNameLen() == null : this.getGoodsNameLen().equals(other.getGoodsNameLen()))
            && (this.getManufacturerCode() == null ? other.getManufacturerCode() == null : this.getManufacturerCode().equals(other.getManufacturerCode()))
            && (this.getManufacturer() == null ? other.getManufacturer() == null : this.getManufacturer().equals(other.getManufacturer()))
            && (this.getManufacturerLen() == null ? other.getManufacturerLen() == null : this.getManufacturerLen().equals(other.getManufacturerLen()))
            && (this.getPurchaseOrgType() == null ? other.getPurchaseOrgType() == null : this.getPurchaseOrgType().equals(other.getPurchaseOrgType()))
            && (this.getThreePurchaseMinPrice() == null ? other.getThreePurchaseMinPrice() == null : this.getThreePurchaseMinPrice().equals(other.getThreePurchaseMinPrice()))
            && (this.getSixPurchaseMinPrice() == null ? other.getSixPurchaseMinPrice() == null : this.getSixPurchaseMinPrice().equals(other.getSixPurchaseMinPrice()))
            && (this.getTwelvePurchaseMinPrice() == null ? other.getTwelvePurchaseMinPrice() == null : this.getTwelvePurchaseMinPrice().equals(other.getTwelvePurchaseMinPrice()))
            && (this.getThreePurchaseDate() == null ? other.getThreePurchaseDate() == null : this.getThreePurchaseDate().equals(other.getThreePurchaseDate()))
            && (this.getSixPurchaseDate() == null ? other.getSixPurchaseDate() == null : this.getSixPurchaseDate().equals(other.getSixPurchaseDate()))
            && (this.getTwelvePurchaseDate() == null ? other.getTwelvePurchaseDate() == null : this.getTwelvePurchaseDate().equals(other.getTwelvePurchaseDate()))
            && (this.getGmtCreate() == null ? other.getGmtCreate() == null : this.getGmtCreate().equals(other.getGmtCreate()))
            && (this.getGmtUpdate() == null ? other.getGmtUpdate() == null : this.getGmtUpdate().equals(other.getGmtUpdate()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getTransferDate() == null) ? 0 : getTransferDate().hashCode());
        result = prime * result + ((getGoodsNo() == null) ? 0 : getGoodsNo().hashCode());
        result = prime * result + ((getGoodsName() == null) ? 0 : getGoodsName().hashCode());
        result = prime * result + ((getGoodsNameLen() == null) ? 0 : getGoodsNameLen().hashCode());
        result = prime * result + ((getManufacturerCode() == null) ? 0 : getManufacturerCode().hashCode());
        result = prime * result + ((getManufacturer() == null) ? 0 : getManufacturer().hashCode());
        result = prime * result + ((getManufacturerLen() == null) ? 0 : getManufacturerLen().hashCode());
        result = prime * result + ((getPurchaseOrgType() == null) ? 0 : getPurchaseOrgType().hashCode());
        result = prime * result + ((getThreePurchaseMinPrice() == null) ? 0 : getThreePurchaseMinPrice().hashCode());
        result = prime * result + ((getSixPurchaseMinPrice() == null) ? 0 : getSixPurchaseMinPrice().hashCode());
        result = prime * result + ((getTwelvePurchaseMinPrice() == null) ? 0 : getTwelvePurchaseMinPrice().hashCode());
        result = prime * result + ((getThreePurchaseDate() == null) ? 0 : getThreePurchaseDate().hashCode());
        result = prime * result + ((getSixPurchaseDate() == null) ? 0 : getSixPurchaseDate().hashCode());
        result = prime * result + ((getTwelvePurchaseDate() == null) ? 0 : getTwelvePurchaseDate().hashCode());
        result = prime * result + ((getGmtCreate() == null) ? 0 : getGmtCreate().hashCode());
        result = prime * result + ((getGmtUpdate() == null) ? 0 : getGmtUpdate().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", transferDate=").append(transferDate);
        sb.append(", goodsNo=").append(goodsNo);
        sb.append(", goodsName=").append(goodsName);
        sb.append(", goodsNameLen=").append(goodsNameLen);
        sb.append(", manufacturerCode=").append(manufacturerCode);
        sb.append(", manufacturer=").append(manufacturer);
        sb.append(", manufacturerLen=").append(manufacturerLen);
        sb.append(", purchaseOrgType=").append(purchaseOrgType);
        sb.append(", threePurchaseMinPrice=").append(threePurchaseMinPrice);
        sb.append(", sixPurchaseMinPrice=").append(sixPurchaseMinPrice);
        sb.append(", twelvePurchaseMinPrice=").append(twelvePurchaseMinPrice);
        sb.append(", threePurchaseDate=").append(threePurchaseDate);
        sb.append(", sixPurchaseDate=").append(sixPurchaseDate);
        sb.append(", twelvePurchaseDate=").append(twelvePurchaseDate);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", gmtUpdate=").append(gmtUpdate);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}