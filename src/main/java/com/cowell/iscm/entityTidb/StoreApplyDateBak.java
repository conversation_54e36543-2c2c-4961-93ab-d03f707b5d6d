package com.cowell.iscm.entityTidb;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> 门店请货日备份表
 */
public class StoreApplyDateBak implements Serializable {
    /**
     * 无业务逻辑主键
     */
    private Long id;

    /**
     * 公司编码
     */
    private String companyCode;

    /**
     * 门店编码
     */
    private String storeCode;

    /**
     * 门店属性
     */
    private String storeAttr;

    /**
     * 营业日期
     */
    private Date openDate;

    /**
     * 请货日期
     */
    private Date applyDate;

    /**
     * 门店状态
     */
    private String storeStatus;

    /**
     * 是否电商店
     */
    private String storeElectricAble;

    /**
     * 配送周期
     */
    private String distrCircle;

    /**
     * 配送在途天数
     */
    private Integer distrTransitDay;

    /**
     * 月销售额等级
     */
    private String monthSalesLevel;

    /**
     * 门店数量
     */
    private Integer storeCount;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新时间
     */
    private Date gmtUpdate;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getStoreCode() {
        return storeCode;
    }

    public void setStoreCode(String storeCode) {
        this.storeCode = storeCode;
    }

    public String getStoreAttr() {
        return storeAttr;
    }

    public void setStoreAttr(String storeAttr) {
        this.storeAttr = storeAttr;
    }

    public Date getOpenDate() {
        return openDate;
    }

    public void setOpenDate(Date openDate) {
        this.openDate = openDate;
    }

    public Date getApplyDate() {
        return applyDate;
    }

    public void setApplyDate(Date applyDate) {
        this.applyDate = applyDate;
    }

    public String getStoreStatus() {
        return storeStatus;
    }

    public void setStoreStatus(String storeStatus) {
        this.storeStatus = storeStatus;
    }

    public String getStoreElectricAble() {
        return storeElectricAble;
    }

    public void setStoreElectricAble(String storeElectricAble) {
        this.storeElectricAble = storeElectricAble;
    }

    public String getDistrCircle() {
        return distrCircle;
    }

    public void setDistrCircle(String distrCircle) {
        this.distrCircle = distrCircle;
    }

    public Integer getDistrTransitDay() {
        return distrTransitDay;
    }

    public void setDistrTransitDay(Integer distrTransitDay) {
        this.distrTransitDay = distrTransitDay;
    }

    public String getMonthSalesLevel() {
        return monthSalesLevel;
    }

    public void setMonthSalesLevel(String monthSalesLevel) {
        this.monthSalesLevel = monthSalesLevel;
    }

    public Integer getStoreCount() {
        return storeCount;
    }

    public void setStoreCount(Integer storeCount) {
        this.storeCount = storeCount;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtUpdate() {
        return gmtUpdate;
    }

    public void setGmtUpdate(Date gmtUpdate) {
        this.gmtUpdate = gmtUpdate;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        StoreApplyDateBak other = (StoreApplyDateBak) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getCompanyCode() == null ? other.getCompanyCode() == null : this.getCompanyCode().equals(other.getCompanyCode()))
            && (this.getStoreCode() == null ? other.getStoreCode() == null : this.getStoreCode().equals(other.getStoreCode()))
            && (this.getStoreAttr() == null ? other.getStoreAttr() == null : this.getStoreAttr().equals(other.getStoreAttr()))
            && (this.getOpenDate() == null ? other.getOpenDate() == null : this.getOpenDate().equals(other.getOpenDate()))
            && (this.getApplyDate() == null ? other.getApplyDate() == null : this.getApplyDate().equals(other.getApplyDate()))
            && (this.getStoreStatus() == null ? other.getStoreStatus() == null : this.getStoreStatus().equals(other.getStoreStatus()))
            && (this.getStoreElectricAble() == null ? other.getStoreElectricAble() == null : this.getStoreElectricAble().equals(other.getStoreElectricAble()))
            && (this.getDistrCircle() == null ? other.getDistrCircle() == null : this.getDistrCircle().equals(other.getDistrCircle()))
            && (this.getDistrTransitDay() == null ? other.getDistrTransitDay() == null : this.getDistrTransitDay().equals(other.getDistrTransitDay()))
            && (this.getMonthSalesLevel() == null ? other.getMonthSalesLevel() == null : this.getMonthSalesLevel().equals(other.getMonthSalesLevel()))
            && (this.getStoreCount() == null ? other.getStoreCount() == null : this.getStoreCount().equals(other.getStoreCount()))
            && (this.getGmtCreate() == null ? other.getGmtCreate() == null : this.getGmtCreate().equals(other.getGmtCreate()))
            && (this.getGmtUpdate() == null ? other.getGmtUpdate() == null : this.getGmtUpdate().equals(other.getGmtUpdate()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getCompanyCode() == null) ? 0 : getCompanyCode().hashCode());
        result = prime * result + ((getStoreCode() == null) ? 0 : getStoreCode().hashCode());
        result = prime * result + ((getStoreAttr() == null) ? 0 : getStoreAttr().hashCode());
        result = prime * result + ((getOpenDate() == null) ? 0 : getOpenDate().hashCode());
        result = prime * result + ((getApplyDate() == null) ? 0 : getApplyDate().hashCode());
        result = prime * result + ((getStoreStatus() == null) ? 0 : getStoreStatus().hashCode());
        result = prime * result + ((getStoreElectricAble() == null) ? 0 : getStoreElectricAble().hashCode());
        result = prime * result + ((getDistrCircle() == null) ? 0 : getDistrCircle().hashCode());
        result = prime * result + ((getDistrTransitDay() == null) ? 0 : getDistrTransitDay().hashCode());
        result = prime * result + ((getMonthSalesLevel() == null) ? 0 : getMonthSalesLevel().hashCode());
        result = prime * result + ((getStoreCount() == null) ? 0 : getStoreCount().hashCode());
        result = prime * result + ((getGmtCreate() == null) ? 0 : getGmtCreate().hashCode());
        result = prime * result + ((getGmtUpdate() == null) ? 0 : getGmtUpdate().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", companyCode=").append(companyCode);
        sb.append(", storeCode=").append(storeCode);
        sb.append(", storeAttr=").append(storeAttr);
        sb.append(", openDate=").append(openDate);
        sb.append(", applyDate=").append(applyDate);
        sb.append(", storeStatus=").append(storeStatus);
        sb.append(", storeElectricAble=").append(storeElectricAble);
        sb.append(", distrCircle=").append(distrCircle);
        sb.append(", distrTransitDay=").append(distrTransitDay);
        sb.append(", monthSalesLevel=").append(monthSalesLevel);
        sb.append(", storeCount=").append(storeCount);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", gmtUpdate=").append(gmtUpdate);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}