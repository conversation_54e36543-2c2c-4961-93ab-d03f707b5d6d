package com.cowell.iscm.entityTidb;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class IscmApplyGoodsMultipleCodeExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Long offset;

    public IscmApplyGoodsMultipleCodeExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Long offset) {
        this.offset = offset;
    }

    public Long getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andOldGoodsNoIsNull() {
            addCriterion("old_goods_no is null");
            return (Criteria) this;
        }

        public Criteria andOldGoodsNoIsNotNull() {
            addCriterion("old_goods_no is not null");
            return (Criteria) this;
        }

        public Criteria andOldGoodsNoEqualTo(String value) {
            addCriterion("old_goods_no =", value, "oldGoodsNo");
            return (Criteria) this;
        }

        public Criteria andOldGoodsNoNotEqualTo(String value) {
            addCriterion("old_goods_no <>", value, "oldGoodsNo");
            return (Criteria) this;
        }

        public Criteria andOldGoodsNoGreaterThan(String value) {
            addCriterion("old_goods_no >", value, "oldGoodsNo");
            return (Criteria) this;
        }

        public Criteria andOldGoodsNoGreaterThanOrEqualTo(String value) {
            addCriterion("old_goods_no >=", value, "oldGoodsNo");
            return (Criteria) this;
        }

        public Criteria andOldGoodsNoLessThan(String value) {
            addCriterion("old_goods_no <", value, "oldGoodsNo");
            return (Criteria) this;
        }

        public Criteria andOldGoodsNoLessThanOrEqualTo(String value) {
            addCriterion("old_goods_no <=", value, "oldGoodsNo");
            return (Criteria) this;
        }

        public Criteria andOldGoodsNoLike(String value) {
            addCriterion("old_goods_no like", value, "oldGoodsNo");
            return (Criteria) this;
        }

        public Criteria andOldGoodsNoNotLike(String value) {
            addCriterion("old_goods_no not like", value, "oldGoodsNo");
            return (Criteria) this;
        }

        public Criteria andOldGoodsNoIn(List<String> values) {
            addCriterion("old_goods_no in", values, "oldGoodsNo");
            return (Criteria) this;
        }

        public Criteria andOldGoodsNoNotIn(List<String> values) {
            addCriterion("old_goods_no not in", values, "oldGoodsNo");
            return (Criteria) this;
        }

        public Criteria andOldGoodsNoBetween(String value1, String value2) {
            addCriterion("old_goods_no between", value1, value2, "oldGoodsNo");
            return (Criteria) this;
        }

        public Criteria andOldGoodsNoNotBetween(String value1, String value2) {
            addCriterion("old_goods_no not between", value1, value2, "oldGoodsNo");
            return (Criteria) this;
        }

        public Criteria andNewGoodsNoIsNull() {
            addCriterion("new_goods_no is null");
            return (Criteria) this;
        }

        public Criteria andNewGoodsNoIsNotNull() {
            addCriterion("new_goods_no is not null");
            return (Criteria) this;
        }

        public Criteria andNewGoodsNoEqualTo(String value) {
            addCriterion("new_goods_no =", value, "newGoodsNo");
            return (Criteria) this;
        }

        public Criteria andNewGoodsNoNotEqualTo(String value) {
            addCriterion("new_goods_no <>", value, "newGoodsNo");
            return (Criteria) this;
        }

        public Criteria andNewGoodsNoGreaterThan(String value) {
            addCriterion("new_goods_no >", value, "newGoodsNo");
            return (Criteria) this;
        }

        public Criteria andNewGoodsNoGreaterThanOrEqualTo(String value) {
            addCriterion("new_goods_no >=", value, "newGoodsNo");
            return (Criteria) this;
        }

        public Criteria andNewGoodsNoLessThan(String value) {
            addCriterion("new_goods_no <", value, "newGoodsNo");
            return (Criteria) this;
        }

        public Criteria andNewGoodsNoLessThanOrEqualTo(String value) {
            addCriterion("new_goods_no <=", value, "newGoodsNo");
            return (Criteria) this;
        }

        public Criteria andNewGoodsNoLike(String value) {
            addCriterion("new_goods_no like", value, "newGoodsNo");
            return (Criteria) this;
        }

        public Criteria andNewGoodsNoNotLike(String value) {
            addCriterion("new_goods_no not like", value, "newGoodsNo");
            return (Criteria) this;
        }

        public Criteria andNewGoodsNoIn(List<String> values) {
            addCriterion("new_goods_no in", values, "newGoodsNo");
            return (Criteria) this;
        }

        public Criteria andNewGoodsNoNotIn(List<String> values) {
            addCriterion("new_goods_no not in", values, "newGoodsNo");
            return (Criteria) this;
        }

        public Criteria andNewGoodsNoBetween(String value1, String value2) {
            addCriterion("new_goods_no between", value1, value2, "newGoodsNo");
            return (Criteria) this;
        }

        public Criteria andNewGoodsNoNotBetween(String value1, String value2) {
            addCriterion("new_goods_no not between", value1, value2, "newGoodsNo");
            return (Criteria) this;
        }

        public Criteria andChangeRatioIsNull() {
            addCriterion("change_ratio is null");
            return (Criteria) this;
        }

        public Criteria andChangeRatioIsNotNull() {
            addCriterion("change_ratio is not null");
            return (Criteria) this;
        }

        public Criteria andChangeRatioEqualTo(BigDecimal value) {
            addCriterion("change_ratio =", value, "changeRatio");
            return (Criteria) this;
        }

        public Criteria andChangeRatioNotEqualTo(BigDecimal value) {
            addCriterion("change_ratio <>", value, "changeRatio");
            return (Criteria) this;
        }

        public Criteria andChangeRatioGreaterThan(BigDecimal value) {
            addCriterion("change_ratio >", value, "changeRatio");
            return (Criteria) this;
        }

        public Criteria andChangeRatioGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("change_ratio >=", value, "changeRatio");
            return (Criteria) this;
        }

        public Criteria andChangeRatioLessThan(BigDecimal value) {
            addCriterion("change_ratio <", value, "changeRatio");
            return (Criteria) this;
        }

        public Criteria andChangeRatioLessThanOrEqualTo(BigDecimal value) {
            addCriterion("change_ratio <=", value, "changeRatio");
            return (Criteria) this;
        }

        public Criteria andChangeRatioIn(List<BigDecimal> values) {
            addCriterion("change_ratio in", values, "changeRatio");
            return (Criteria) this;
        }

        public Criteria andChangeRatioNotIn(List<BigDecimal> values) {
            addCriterion("change_ratio not in", values, "changeRatio");
            return (Criteria) this;
        }

        public Criteria andChangeRatioBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("change_ratio between", value1, value2, "changeRatio");
            return (Criteria) this;
        }

        public Criteria andChangeRatioNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("change_ratio not between", value1, value2, "changeRatio");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIsNull() {
            addCriterion("gmt_update is null");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIsNotNull() {
            addCriterion("gmt_update is not null");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateEqualTo(Date value) {
            addCriterion("gmt_update =", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotEqualTo(Date value) {
            addCriterion("gmt_update <>", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateGreaterThan(Date value) {
            addCriterion("gmt_update >", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_update >=", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateLessThan(Date value) {
            addCriterion("gmt_update <", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_update <=", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIn(List<Date> values) {
            addCriterion("gmt_update in", values, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotIn(List<Date> values) {
            addCriterion("gmt_update not in", values, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateBetween(Date value1, Date value2) {
            addCriterion("gmt_update between", value1, value2, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_update not between", value1, value2, "gmtUpdate");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}