package com.cowell.iscm.entityTidb;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class IscmBdpApplyInfoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Long offset;

    public IscmBdpApplyInfoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Long offset) {
        this.offset = offset;
    }

    public Long getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        protected void addCriterionForJDBCDate(String condition, Date value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value.getTime()), property);
        }

        protected void addCriterionForJDBCDate(String condition, List<Date> values, String property) {
            if (values == null || values.size() == 0) {
                throw new RuntimeException("Value list for " + property + " cannot be null or empty");
            }
            List<java.sql.Date> dateList = new ArrayList<java.sql.Date>();
            Iterator<Date> iter = values.iterator();
            while (iter.hasNext()) {
                dateList.add(new java.sql.Date(iter.next().getTime()));
            }
            addCriterion(condition, dateList, property);
        }

        protected void addCriterionForJDBCDate(String condition, Date value1, Date value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value1.getTime()), new java.sql.Date(value2.getTime()), property);
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andApplyNoIsNull() {
            addCriterion("apply_no is null");
            return (Criteria) this;
        }

        public Criteria andApplyNoIsNotNull() {
            addCriterion("apply_no is not null");
            return (Criteria) this;
        }

        public Criteria andApplyNoEqualTo(String value) {
            addCriterion("apply_no =", value, "applyNo");
            return (Criteria) this;
        }

        public Criteria andApplyNoNotEqualTo(String value) {
            addCriterion("apply_no <>", value, "applyNo");
            return (Criteria) this;
        }

        public Criteria andApplyNoGreaterThan(String value) {
            addCriterion("apply_no >", value, "applyNo");
            return (Criteria) this;
        }

        public Criteria andApplyNoGreaterThanOrEqualTo(String value) {
            addCriterion("apply_no >=", value, "applyNo");
            return (Criteria) this;
        }

        public Criteria andApplyNoLessThan(String value) {
            addCriterion("apply_no <", value, "applyNo");
            return (Criteria) this;
        }

        public Criteria andApplyNoLessThanOrEqualTo(String value) {
            addCriterion("apply_no <=", value, "applyNo");
            return (Criteria) this;
        }

        public Criteria andApplyNoLike(String value) {
            addCriterion("apply_no like", value, "applyNo");
            return (Criteria) this;
        }

        public Criteria andApplyNoNotLike(String value) {
            addCriterion("apply_no not like", value, "applyNo");
            return (Criteria) this;
        }

        public Criteria andApplyNoIn(List<String> values) {
            addCriterion("apply_no in", values, "applyNo");
            return (Criteria) this;
        }

        public Criteria andApplyNoNotIn(List<String> values) {
            addCriterion("apply_no not in", values, "applyNo");
            return (Criteria) this;
        }

        public Criteria andApplyNoBetween(String value1, String value2) {
            addCriterion("apply_no between", value1, value2, "applyNo");
            return (Criteria) this;
        }

        public Criteria andApplyNoNotBetween(String value1, String value2) {
            addCriterion("apply_no not between", value1, value2, "applyNo");
            return (Criteria) this;
        }

        public Criteria andApplyLineIsNull() {
            addCriterion("apply_line is null");
            return (Criteria) this;
        }

        public Criteria andApplyLineIsNotNull() {
            addCriterion("apply_line is not null");
            return (Criteria) this;
        }

        public Criteria andApplyLineEqualTo(String value) {
            addCriterion("apply_line =", value, "applyLine");
            return (Criteria) this;
        }

        public Criteria andApplyLineNotEqualTo(String value) {
            addCriterion("apply_line <>", value, "applyLine");
            return (Criteria) this;
        }

        public Criteria andApplyLineGreaterThan(String value) {
            addCriterion("apply_line >", value, "applyLine");
            return (Criteria) this;
        }

        public Criteria andApplyLineGreaterThanOrEqualTo(String value) {
            addCriterion("apply_line >=", value, "applyLine");
            return (Criteria) this;
        }

        public Criteria andApplyLineLessThan(String value) {
            addCriterion("apply_line <", value, "applyLine");
            return (Criteria) this;
        }

        public Criteria andApplyLineLessThanOrEqualTo(String value) {
            addCriterion("apply_line <=", value, "applyLine");
            return (Criteria) this;
        }

        public Criteria andApplyLineLike(String value) {
            addCriterion("apply_line like", value, "applyLine");
            return (Criteria) this;
        }

        public Criteria andApplyLineNotLike(String value) {
            addCriterion("apply_line not like", value, "applyLine");
            return (Criteria) this;
        }

        public Criteria andApplyLineIn(List<String> values) {
            addCriterion("apply_line in", values, "applyLine");
            return (Criteria) this;
        }

        public Criteria andApplyLineNotIn(List<String> values) {
            addCriterion("apply_line not in", values, "applyLine");
            return (Criteria) this;
        }

        public Criteria andApplyLineBetween(String value1, String value2) {
            addCriterion("apply_line between", value1, value2, "applyLine");
            return (Criteria) this;
        }

        public Criteria andApplyLineNotBetween(String value1, String value2) {
            addCriterion("apply_line not between", value1, value2, "applyLine");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIsNull() {
            addCriterion("company_code is null");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIsNotNull() {
            addCriterion("company_code is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeEqualTo(String value) {
            addCriterion("company_code =", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotEqualTo(String value) {
            addCriterion("company_code <>", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeGreaterThan(String value) {
            addCriterion("company_code >", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeGreaterThanOrEqualTo(String value) {
            addCriterion("company_code >=", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLessThan(String value) {
            addCriterion("company_code <", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLessThanOrEqualTo(String value) {
            addCriterion("company_code <=", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLike(String value) {
            addCriterion("company_code like", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotLike(String value) {
            addCriterion("company_code not like", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIn(List<String> values) {
            addCriterion("company_code in", values, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotIn(List<String> values) {
            addCriterion("company_code not in", values, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeBetween(String value1, String value2) {
            addCriterion("company_code between", value1, value2, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotBetween(String value1, String value2) {
            addCriterion("company_code not between", value1, value2, "companyCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeIsNull() {
            addCriterion("store_code is null");
            return (Criteria) this;
        }

        public Criteria andStoreCodeIsNotNull() {
            addCriterion("store_code is not null");
            return (Criteria) this;
        }

        public Criteria andStoreCodeEqualTo(String value) {
            addCriterion("store_code =", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotEqualTo(String value) {
            addCriterion("store_code <>", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeGreaterThan(String value) {
            addCriterion("store_code >", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeGreaterThanOrEqualTo(String value) {
            addCriterion("store_code >=", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeLessThan(String value) {
            addCriterion("store_code <", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeLessThanOrEqualTo(String value) {
            addCriterion("store_code <=", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeLike(String value) {
            addCriterion("store_code like", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotLike(String value) {
            addCriterion("store_code not like", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeIn(List<String> values) {
            addCriterion("store_code in", values, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotIn(List<String> values) {
            addCriterion("store_code not in", values, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeBetween(String value1, String value2) {
            addCriterion("store_code between", value1, value2, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotBetween(String value1, String value2) {
            addCriterion("store_code not between", value1, value2, "storeCode");
            return (Criteria) this;
        }

        public Criteria andApplyDateIsNull() {
            addCriterion("apply_date is null");
            return (Criteria) this;
        }

        public Criteria andApplyDateIsNotNull() {
            addCriterion("apply_date is not null");
            return (Criteria) this;
        }

        public Criteria andApplyDateEqualTo(Date value) {
            addCriterionForJDBCDate("apply_date =", value, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("apply_date <>", value, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateGreaterThan(Date value) {
            addCriterionForJDBCDate("apply_date >", value, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("apply_date >=", value, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateLessThan(Date value) {
            addCriterionForJDBCDate("apply_date <", value, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("apply_date <=", value, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateIn(List<Date> values) {
            addCriterionForJDBCDate("apply_date in", values, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("apply_date not in", values, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("apply_date between", value1, value2, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("apply_date not between", value1, value2, "applyDate");
            return (Criteria) this;
        }

        public Criteria andDataOriginTypeIsNull() {
            addCriterion("data_origin_type is null");
            return (Criteria) this;
        }

        public Criteria andDataOriginTypeIsNotNull() {
            addCriterion("data_origin_type is not null");
            return (Criteria) this;
        }

        public Criteria andDataOriginTypeEqualTo(Byte value) {
            addCriterion("data_origin_type =", value, "dataOriginType");
            return (Criteria) this;
        }

        public Criteria andDataOriginTypeNotEqualTo(Byte value) {
            addCriterion("data_origin_type <>", value, "dataOriginType");
            return (Criteria) this;
        }

        public Criteria andDataOriginTypeGreaterThan(Byte value) {
            addCriterion("data_origin_type >", value, "dataOriginType");
            return (Criteria) this;
        }

        public Criteria andDataOriginTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("data_origin_type >=", value, "dataOriginType");
            return (Criteria) this;
        }

        public Criteria andDataOriginTypeLessThan(Byte value) {
            addCriterion("data_origin_type <", value, "dataOriginType");
            return (Criteria) this;
        }

        public Criteria andDataOriginTypeLessThanOrEqualTo(Byte value) {
            addCriterion("data_origin_type <=", value, "dataOriginType");
            return (Criteria) this;
        }

        public Criteria andDataOriginTypeIn(List<Byte> values) {
            addCriterion("data_origin_type in", values, "dataOriginType");
            return (Criteria) this;
        }

        public Criteria andDataOriginTypeNotIn(List<Byte> values) {
            addCriterion("data_origin_type not in", values, "dataOriginType");
            return (Criteria) this;
        }

        public Criteria andDataOriginTypeBetween(Byte value1, Byte value2) {
            addCriterion("data_origin_type between", value1, value2, "dataOriginType");
            return (Criteria) this;
        }

        public Criteria andDataOriginTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("data_origin_type not between", value1, value2, "dataOriginType");
            return (Criteria) this;
        }

        public Criteria andGoodsNoIsNull() {
            addCriterion("goods_no is null");
            return (Criteria) this;
        }

        public Criteria andGoodsNoIsNotNull() {
            addCriterion("goods_no is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsNoEqualTo(String value) {
            addCriterion("goods_no =", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotEqualTo(String value) {
            addCriterion("goods_no <>", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoGreaterThan(String value) {
            addCriterion("goods_no >", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoGreaterThanOrEqualTo(String value) {
            addCriterion("goods_no >=", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoLessThan(String value) {
            addCriterion("goods_no <", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoLessThanOrEqualTo(String value) {
            addCriterion("goods_no <=", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoLike(String value) {
            addCriterion("goods_no like", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotLike(String value) {
            addCriterion("goods_no not like", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoIn(List<String> values) {
            addCriterion("goods_no in", values, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotIn(List<String> values) {
            addCriterion("goods_no not in", values, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoBetween(String value1, String value2) {
            addCriterion("goods_no between", value1, value2, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotBetween(String value1, String value2) {
            addCriterion("goods_no not between", value1, value2, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andApplyGoodsTypeIsNull() {
            addCriterion("apply_goods_type is null");
            return (Criteria) this;
        }

        public Criteria andApplyGoodsTypeIsNotNull() {
            addCriterion("apply_goods_type is not null");
            return (Criteria) this;
        }

        public Criteria andApplyGoodsTypeEqualTo(Byte value) {
            addCriterion("apply_goods_type =", value, "applyGoodsType");
            return (Criteria) this;
        }

        public Criteria andApplyGoodsTypeNotEqualTo(Byte value) {
            addCriterion("apply_goods_type <>", value, "applyGoodsType");
            return (Criteria) this;
        }

        public Criteria andApplyGoodsTypeGreaterThan(Byte value) {
            addCriterion("apply_goods_type >", value, "applyGoodsType");
            return (Criteria) this;
        }

        public Criteria andApplyGoodsTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("apply_goods_type >=", value, "applyGoodsType");
            return (Criteria) this;
        }

        public Criteria andApplyGoodsTypeLessThan(Byte value) {
            addCriterion("apply_goods_type <", value, "applyGoodsType");
            return (Criteria) this;
        }

        public Criteria andApplyGoodsTypeLessThanOrEqualTo(Byte value) {
            addCriterion("apply_goods_type <=", value, "applyGoodsType");
            return (Criteria) this;
        }

        public Criteria andApplyGoodsTypeIn(List<Byte> values) {
            addCriterion("apply_goods_type in", values, "applyGoodsType");
            return (Criteria) this;
        }

        public Criteria andApplyGoodsTypeNotIn(List<Byte> values) {
            addCriterion("apply_goods_type not in", values, "applyGoodsType");
            return (Criteria) this;
        }

        public Criteria andApplyGoodsTypeBetween(Byte value1, Byte value2) {
            addCriterion("apply_goods_type between", value1, value2, "applyGoodsType");
            return (Criteria) this;
        }

        public Criteria andApplyGoodsTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("apply_goods_type not between", value1, value2, "applyGoodsType");
            return (Criteria) this;
        }

        public Criteria andApplyTotalIsNull() {
            addCriterion("apply_total is null");
            return (Criteria) this;
        }

        public Criteria andApplyTotalIsNotNull() {
            addCriterion("apply_total is not null");
            return (Criteria) this;
        }

        public Criteria andApplyTotalEqualTo(BigDecimal value) {
            addCriterion("apply_total =", value, "applyTotal");
            return (Criteria) this;
        }

        public Criteria andApplyTotalNotEqualTo(BigDecimal value) {
            addCriterion("apply_total <>", value, "applyTotal");
            return (Criteria) this;
        }

        public Criteria andApplyTotalGreaterThan(BigDecimal value) {
            addCriterion("apply_total >", value, "applyTotal");
            return (Criteria) this;
        }

        public Criteria andApplyTotalGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("apply_total >=", value, "applyTotal");
            return (Criteria) this;
        }

        public Criteria andApplyTotalLessThan(BigDecimal value) {
            addCriterion("apply_total <", value, "applyTotal");
            return (Criteria) this;
        }

        public Criteria andApplyTotalLessThanOrEqualTo(BigDecimal value) {
            addCriterion("apply_total <=", value, "applyTotal");
            return (Criteria) this;
        }

        public Criteria andApplyTotalIn(List<BigDecimal> values) {
            addCriterion("apply_total in", values, "applyTotal");
            return (Criteria) this;
        }

        public Criteria andApplyTotalNotIn(List<BigDecimal> values) {
            addCriterion("apply_total not in", values, "applyTotal");
            return (Criteria) this;
        }

        public Criteria andApplyTotalBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("apply_total between", value1, value2, "applyTotal");
            return (Criteria) this;
        }

        public Criteria andApplyTotalNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("apply_total not between", value1, value2, "applyTotal");
            return (Criteria) this;
        }

        public Criteria andUnqualifiedAwaitStockIsNull() {
            addCriterion("unqualified_await_stock is null");
            return (Criteria) this;
        }

        public Criteria andUnqualifiedAwaitStockIsNotNull() {
            addCriterion("unqualified_await_stock is not null");
            return (Criteria) this;
        }

        public Criteria andUnqualifiedAwaitStockEqualTo(BigDecimal value) {
            addCriterion("unqualified_await_stock =", value, "unqualifiedAwaitStock");
            return (Criteria) this;
        }

        public Criteria andUnqualifiedAwaitStockNotEqualTo(BigDecimal value) {
            addCriterion("unqualified_await_stock <>", value, "unqualifiedAwaitStock");
            return (Criteria) this;
        }

        public Criteria andUnqualifiedAwaitStockGreaterThan(BigDecimal value) {
            addCriterion("unqualified_await_stock >", value, "unqualifiedAwaitStock");
            return (Criteria) this;
        }

        public Criteria andUnqualifiedAwaitStockGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("unqualified_await_stock >=", value, "unqualifiedAwaitStock");
            return (Criteria) this;
        }

        public Criteria andUnqualifiedAwaitStockLessThan(BigDecimal value) {
            addCriterion("unqualified_await_stock <", value, "unqualifiedAwaitStock");
            return (Criteria) this;
        }

        public Criteria andUnqualifiedAwaitStockLessThanOrEqualTo(BigDecimal value) {
            addCriterion("unqualified_await_stock <=", value, "unqualifiedAwaitStock");
            return (Criteria) this;
        }

        public Criteria andUnqualifiedAwaitStockIn(List<BigDecimal> values) {
            addCriterion("unqualified_await_stock in", values, "unqualifiedAwaitStock");
            return (Criteria) this;
        }

        public Criteria andUnqualifiedAwaitStockNotIn(List<BigDecimal> values) {
            addCriterion("unqualified_await_stock not in", values, "unqualifiedAwaitStock");
            return (Criteria) this;
        }

        public Criteria andUnqualifiedAwaitStockBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("unqualified_await_stock between", value1, value2, "unqualifiedAwaitStock");
            return (Criteria) this;
        }

        public Criteria andUnqualifiedAwaitStockNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("unqualified_await_stock not between", value1, value2, "unqualifiedAwaitStock");
            return (Criteria) this;
        }

        public Criteria andTransitStockIsNull() {
            addCriterion("transit_stock is null");
            return (Criteria) this;
        }

        public Criteria andTransitStockIsNotNull() {
            addCriterion("transit_stock is not null");
            return (Criteria) this;
        }

        public Criteria andTransitStockEqualTo(BigDecimal value) {
            addCriterion("transit_stock =", value, "transitStock");
            return (Criteria) this;
        }

        public Criteria andTransitStockNotEqualTo(BigDecimal value) {
            addCriterion("transit_stock <>", value, "transitStock");
            return (Criteria) this;
        }

        public Criteria andTransitStockGreaterThan(BigDecimal value) {
            addCriterion("transit_stock >", value, "transitStock");
            return (Criteria) this;
        }

        public Criteria andTransitStockGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("transit_stock >=", value, "transitStock");
            return (Criteria) this;
        }

        public Criteria andTransitStockLessThan(BigDecimal value) {
            addCriterion("transit_stock <", value, "transitStock");
            return (Criteria) this;
        }

        public Criteria andTransitStockLessThanOrEqualTo(BigDecimal value) {
            addCriterion("transit_stock <=", value, "transitStock");
            return (Criteria) this;
        }

        public Criteria andTransitStockIn(List<BigDecimal> values) {
            addCriterion("transit_stock in", values, "transitStock");
            return (Criteria) this;
        }

        public Criteria andTransitStockNotIn(List<BigDecimal> values) {
            addCriterion("transit_stock not in", values, "transitStock");
            return (Criteria) this;
        }

        public Criteria andTransitStockBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("transit_stock between", value1, value2, "transitStock");
            return (Criteria) this;
        }

        public Criteria andTransitStockNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("transit_stock not between", value1, value2, "transitStock");
            return (Criteria) this;
        }

        public Criteria andStockIsNull() {
            addCriterion("stock is null");
            return (Criteria) this;
        }

        public Criteria andStockIsNotNull() {
            addCriterion("stock is not null");
            return (Criteria) this;
        }

        public Criteria andStockEqualTo(BigDecimal value) {
            addCriterion("stock =", value, "stock");
            return (Criteria) this;
        }

        public Criteria andStockNotEqualTo(BigDecimal value) {
            addCriterion("stock <>", value, "stock");
            return (Criteria) this;
        }

        public Criteria andStockGreaterThan(BigDecimal value) {
            addCriterion("stock >", value, "stock");
            return (Criteria) this;
        }

        public Criteria andStockGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("stock >=", value, "stock");
            return (Criteria) this;
        }

        public Criteria andStockLessThan(BigDecimal value) {
            addCriterion("stock <", value, "stock");
            return (Criteria) this;
        }

        public Criteria andStockLessThanOrEqualTo(BigDecimal value) {
            addCriterion("stock <=", value, "stock");
            return (Criteria) this;
        }

        public Criteria andStockIn(List<BigDecimal> values) {
            addCriterion("stock in", values, "stock");
            return (Criteria) this;
        }

        public Criteria andStockNotIn(List<BigDecimal> values) {
            addCriterion("stock not in", values, "stock");
            return (Criteria) this;
        }

        public Criteria andStockBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("stock between", value1, value2, "stock");
            return (Criteria) this;
        }

        public Criteria andStockNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("stock not between", value1, value2, "stock");
            return (Criteria) this;
        }

        public Criteria andLockStockIsNull() {
            addCriterion("lock_stock is null");
            return (Criteria) this;
        }

        public Criteria andLockStockIsNotNull() {
            addCriterion("lock_stock is not null");
            return (Criteria) this;
        }

        public Criteria andLockStockEqualTo(BigDecimal value) {
            addCriterion("lock_stock =", value, "lockStock");
            return (Criteria) this;
        }

        public Criteria andLockStockNotEqualTo(BigDecimal value) {
            addCriterion("lock_stock <>", value, "lockStock");
            return (Criteria) this;
        }

        public Criteria andLockStockGreaterThan(BigDecimal value) {
            addCriterion("lock_stock >", value, "lockStock");
            return (Criteria) this;
        }

        public Criteria andLockStockGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("lock_stock >=", value, "lockStock");
            return (Criteria) this;
        }

        public Criteria andLockStockLessThan(BigDecimal value) {
            addCriterion("lock_stock <", value, "lockStock");
            return (Criteria) this;
        }

        public Criteria andLockStockLessThanOrEqualTo(BigDecimal value) {
            addCriterion("lock_stock <=", value, "lockStock");
            return (Criteria) this;
        }

        public Criteria andLockStockIn(List<BigDecimal> values) {
            addCriterion("lock_stock in", values, "lockStock");
            return (Criteria) this;
        }

        public Criteria andLockStockNotIn(List<BigDecimal> values) {
            addCriterion("lock_stock not in", values, "lockStock");
            return (Criteria) this;
        }

        public Criteria andLockStockBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("lock_stock between", value1, value2, "lockStock");
            return (Criteria) this;
        }

        public Criteria andLockStockNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("lock_stock not between", value1, value2, "lockStock");
            return (Criteria) this;
        }

        public Criteria andUnqualifiedStockIsNull() {
            addCriterion("unqualified_stock is null");
            return (Criteria) this;
        }

        public Criteria andUnqualifiedStockIsNotNull() {
            addCriterion("unqualified_stock is not null");
            return (Criteria) this;
        }

        public Criteria andUnqualifiedStockEqualTo(BigDecimal value) {
            addCriterion("unqualified_stock =", value, "unqualifiedStock");
            return (Criteria) this;
        }

        public Criteria andUnqualifiedStockNotEqualTo(BigDecimal value) {
            addCriterion("unqualified_stock <>", value, "unqualifiedStock");
            return (Criteria) this;
        }

        public Criteria andUnqualifiedStockGreaterThan(BigDecimal value) {
            addCriterion("unqualified_stock >", value, "unqualifiedStock");
            return (Criteria) this;
        }

        public Criteria andUnqualifiedStockGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("unqualified_stock >=", value, "unqualifiedStock");
            return (Criteria) this;
        }

        public Criteria andUnqualifiedStockLessThan(BigDecimal value) {
            addCriterion("unqualified_stock <", value, "unqualifiedStock");
            return (Criteria) this;
        }

        public Criteria andUnqualifiedStockLessThanOrEqualTo(BigDecimal value) {
            addCriterion("unqualified_stock <=", value, "unqualifiedStock");
            return (Criteria) this;
        }

        public Criteria andUnqualifiedStockIn(List<BigDecimal> values) {
            addCriterion("unqualified_stock in", values, "unqualifiedStock");
            return (Criteria) this;
        }

        public Criteria andUnqualifiedStockNotIn(List<BigDecimal> values) {
            addCriterion("unqualified_stock not in", values, "unqualifiedStock");
            return (Criteria) this;
        }

        public Criteria andUnqualifiedStockBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("unqualified_stock between", value1, value2, "unqualifiedStock");
            return (Criteria) this;
        }

        public Criteria andUnqualifiedStockNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("unqualified_stock not between", value1, value2, "unqualifiedStock");
            return (Criteria) this;
        }

        public Criteria andApplyTransitStockIsNull() {
            addCriterion("apply_transit_stock is null");
            return (Criteria) this;
        }

        public Criteria andApplyTransitStockIsNotNull() {
            addCriterion("apply_transit_stock is not null");
            return (Criteria) this;
        }

        public Criteria andApplyTransitStockEqualTo(BigDecimal value) {
            addCriterion("apply_transit_stock =", value, "applyTransitStock");
            return (Criteria) this;
        }

        public Criteria andApplyTransitStockNotEqualTo(BigDecimal value) {
            addCriterion("apply_transit_stock <>", value, "applyTransitStock");
            return (Criteria) this;
        }

        public Criteria andApplyTransitStockGreaterThan(BigDecimal value) {
            addCriterion("apply_transit_stock >", value, "applyTransitStock");
            return (Criteria) this;
        }

        public Criteria andApplyTransitStockGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("apply_transit_stock >=", value, "applyTransitStock");
            return (Criteria) this;
        }

        public Criteria andApplyTransitStockLessThan(BigDecimal value) {
            addCriterion("apply_transit_stock <", value, "applyTransitStock");
            return (Criteria) this;
        }

        public Criteria andApplyTransitStockLessThanOrEqualTo(BigDecimal value) {
            addCriterion("apply_transit_stock <=", value, "applyTransitStock");
            return (Criteria) this;
        }

        public Criteria andApplyTransitStockIn(List<BigDecimal> values) {
            addCriterion("apply_transit_stock in", values, "applyTransitStock");
            return (Criteria) this;
        }

        public Criteria andApplyTransitStockNotIn(List<BigDecimal> values) {
            addCriterion("apply_transit_stock not in", values, "applyTransitStock");
            return (Criteria) this;
        }

        public Criteria andApplyTransitStockBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("apply_transit_stock between", value1, value2, "applyTransitStock");
            return (Criteria) this;
        }

        public Criteria andApplyTransitStockNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("apply_transit_stock not between", value1, value2, "applyTransitStock");
            return (Criteria) this;
        }

        public Criteria andInTransitStockIsNull() {
            addCriterion("in_transit_stock is null");
            return (Criteria) this;
        }

        public Criteria andInTransitStockIsNotNull() {
            addCriterion("in_transit_stock is not null");
            return (Criteria) this;
        }

        public Criteria andInTransitStockEqualTo(BigDecimal value) {
            addCriterion("in_transit_stock =", value, "inTransitStock");
            return (Criteria) this;
        }

        public Criteria andInTransitStockNotEqualTo(BigDecimal value) {
            addCriterion("in_transit_stock <>", value, "inTransitStock");
            return (Criteria) this;
        }

        public Criteria andInTransitStockGreaterThan(BigDecimal value) {
            addCriterion("in_transit_stock >", value, "inTransitStock");
            return (Criteria) this;
        }

        public Criteria andInTransitStockGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("in_transit_stock >=", value, "inTransitStock");
            return (Criteria) this;
        }

        public Criteria andInTransitStockLessThan(BigDecimal value) {
            addCriterion("in_transit_stock <", value, "inTransitStock");
            return (Criteria) this;
        }

        public Criteria andInTransitStockLessThanOrEqualTo(BigDecimal value) {
            addCriterion("in_transit_stock <=", value, "inTransitStock");
            return (Criteria) this;
        }

        public Criteria andInTransitStockIn(List<BigDecimal> values) {
            addCriterion("in_transit_stock in", values, "inTransitStock");
            return (Criteria) this;
        }

        public Criteria andInTransitStockNotIn(List<BigDecimal> values) {
            addCriterion("in_transit_stock not in", values, "inTransitStock");
            return (Criteria) this;
        }

        public Criteria andInTransitStockBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("in_transit_stock between", value1, value2, "inTransitStock");
            return (Criteria) this;
        }

        public Criteria andInTransitStockNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("in_transit_stock not between", value1, value2, "inTransitStock");
            return (Criteria) this;
        }

        public Criteria andDistrTransitStockIsNull() {
            addCriterion("distr_transit_stock is null");
            return (Criteria) this;
        }

        public Criteria andDistrTransitStockIsNotNull() {
            addCriterion("distr_transit_stock is not null");
            return (Criteria) this;
        }

        public Criteria andDistrTransitStockEqualTo(BigDecimal value) {
            addCriterion("distr_transit_stock =", value, "distrTransitStock");
            return (Criteria) this;
        }

        public Criteria andDistrTransitStockNotEqualTo(BigDecimal value) {
            addCriterion("distr_transit_stock <>", value, "distrTransitStock");
            return (Criteria) this;
        }

        public Criteria andDistrTransitStockGreaterThan(BigDecimal value) {
            addCriterion("distr_transit_stock >", value, "distrTransitStock");
            return (Criteria) this;
        }

        public Criteria andDistrTransitStockGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("distr_transit_stock >=", value, "distrTransitStock");
            return (Criteria) this;
        }

        public Criteria andDistrTransitStockLessThan(BigDecimal value) {
            addCriterion("distr_transit_stock <", value, "distrTransitStock");
            return (Criteria) this;
        }

        public Criteria andDistrTransitStockLessThanOrEqualTo(BigDecimal value) {
            addCriterion("distr_transit_stock <=", value, "distrTransitStock");
            return (Criteria) this;
        }

        public Criteria andDistrTransitStockIn(List<BigDecimal> values) {
            addCriterion("distr_transit_stock in", values, "distrTransitStock");
            return (Criteria) this;
        }

        public Criteria andDistrTransitStockNotIn(List<BigDecimal> values) {
            addCriterion("distr_transit_stock not in", values, "distrTransitStock");
            return (Criteria) this;
        }

        public Criteria andDistrTransitStockBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("distr_transit_stock between", value1, value2, "distrTransitStock");
            return (Criteria) this;
        }

        public Criteria andDistrTransitStockNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("distr_transit_stock not between", value1, value2, "distrTransitStock");
            return (Criteria) this;
        }

        public Criteria andSpecialCtrlIsNull() {
            addCriterion("special_ctrl is null");
            return (Criteria) this;
        }

        public Criteria andSpecialCtrlIsNotNull() {
            addCriterion("special_ctrl is not null");
            return (Criteria) this;
        }

        public Criteria andSpecialCtrlEqualTo(String value) {
            addCriterion("special_ctrl =", value, "specialCtrl");
            return (Criteria) this;
        }

        public Criteria andSpecialCtrlNotEqualTo(String value) {
            addCriterion("special_ctrl <>", value, "specialCtrl");
            return (Criteria) this;
        }

        public Criteria andSpecialCtrlGreaterThan(String value) {
            addCriterion("special_ctrl >", value, "specialCtrl");
            return (Criteria) this;
        }

        public Criteria andSpecialCtrlGreaterThanOrEqualTo(String value) {
            addCriterion("special_ctrl >=", value, "specialCtrl");
            return (Criteria) this;
        }

        public Criteria andSpecialCtrlLessThan(String value) {
            addCriterion("special_ctrl <", value, "specialCtrl");
            return (Criteria) this;
        }

        public Criteria andSpecialCtrlLessThanOrEqualTo(String value) {
            addCriterion("special_ctrl <=", value, "specialCtrl");
            return (Criteria) this;
        }

        public Criteria andSpecialCtrlLike(String value) {
            addCriterion("special_ctrl like", value, "specialCtrl");
            return (Criteria) this;
        }

        public Criteria andSpecialCtrlNotLike(String value) {
            addCriterion("special_ctrl not like", value, "specialCtrl");
            return (Criteria) this;
        }

        public Criteria andSpecialCtrlIn(List<String> values) {
            addCriterion("special_ctrl in", values, "specialCtrl");
            return (Criteria) this;
        }

        public Criteria andSpecialCtrlNotIn(List<String> values) {
            addCriterion("special_ctrl not in", values, "specialCtrl");
            return (Criteria) this;
        }

        public Criteria andSpecialCtrlBetween(String value1, String value2) {
            addCriterion("special_ctrl between", value1, value2, "specialCtrl");
            return (Criteria) this;
        }

        public Criteria andSpecialCtrlNotBetween(String value1, String value2) {
            addCriterion("special_ctrl not between", value1, value2, "specialCtrl");
            return (Criteria) this;
        }

        public Criteria andSpecialThirtyDaysQtyIsNull() {
            addCriterion("special_thirty_days_qty is null");
            return (Criteria) this;
        }

        public Criteria andSpecialThirtyDaysQtyIsNotNull() {
            addCriterion("special_thirty_days_qty is not null");
            return (Criteria) this;
        }

        public Criteria andSpecialThirtyDaysQtyEqualTo(BigDecimal value) {
            addCriterion("special_thirty_days_qty =", value, "specialThirtyDaysQty");
            return (Criteria) this;
        }

        public Criteria andSpecialThirtyDaysQtyNotEqualTo(BigDecimal value) {
            addCriterion("special_thirty_days_qty <>", value, "specialThirtyDaysQty");
            return (Criteria) this;
        }

        public Criteria andSpecialThirtyDaysQtyGreaterThan(BigDecimal value) {
            addCriterion("special_thirty_days_qty >", value, "specialThirtyDaysQty");
            return (Criteria) this;
        }

        public Criteria andSpecialThirtyDaysQtyGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("special_thirty_days_qty >=", value, "specialThirtyDaysQty");
            return (Criteria) this;
        }

        public Criteria andSpecialThirtyDaysQtyLessThan(BigDecimal value) {
            addCriterion("special_thirty_days_qty <", value, "specialThirtyDaysQty");
            return (Criteria) this;
        }

        public Criteria andSpecialThirtyDaysQtyLessThanOrEqualTo(BigDecimal value) {
            addCriterion("special_thirty_days_qty <=", value, "specialThirtyDaysQty");
            return (Criteria) this;
        }

        public Criteria andSpecialThirtyDaysQtyIn(List<BigDecimal> values) {
            addCriterion("special_thirty_days_qty in", values, "specialThirtyDaysQty");
            return (Criteria) this;
        }

        public Criteria andSpecialThirtyDaysQtyNotIn(List<BigDecimal> values) {
            addCriterion("special_thirty_days_qty not in", values, "specialThirtyDaysQty");
            return (Criteria) this;
        }

        public Criteria andSpecialThirtyDaysQtyBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("special_thirty_days_qty between", value1, value2, "specialThirtyDaysQty");
            return (Criteria) this;
        }

        public Criteria andSpecialThirtyDaysQtyNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("special_thirty_days_qty not between", value1, value2, "specialThirtyDaysQty");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelIsNull() {
            addCriterion("goods_level is null");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelIsNotNull() {
            addCriterion("goods_level is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelEqualTo(String value) {
            addCriterion("goods_level =", value, "goodsLevel");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelNotEqualTo(String value) {
            addCriterion("goods_level <>", value, "goodsLevel");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelGreaterThan(String value) {
            addCriterion("goods_level >", value, "goodsLevel");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelGreaterThanOrEqualTo(String value) {
            addCriterion("goods_level >=", value, "goodsLevel");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelLessThan(String value) {
            addCriterion("goods_level <", value, "goodsLevel");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelLessThanOrEqualTo(String value) {
            addCriterion("goods_level <=", value, "goodsLevel");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelLike(String value) {
            addCriterion("goods_level like", value, "goodsLevel");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelNotLike(String value) {
            addCriterion("goods_level not like", value, "goodsLevel");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelIn(List<String> values) {
            addCriterion("goods_level in", values, "goodsLevel");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelNotIn(List<String> values) {
            addCriterion("goods_level not in", values, "goodsLevel");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelBetween(String value1, String value2) {
            addCriterion("goods_level between", value1, value2, "goodsLevel");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelNotBetween(String value1, String value2) {
            addCriterion("goods_level not between", value1, value2, "goodsLevel");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitDaysIsNull() {
            addCriterion("stock_upper_limit_days is null");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitDaysIsNotNull() {
            addCriterion("stock_upper_limit_days is not null");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitDaysEqualTo(Integer value) {
            addCriterion("stock_upper_limit_days =", value, "stockUpperLimitDays");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitDaysNotEqualTo(Integer value) {
            addCriterion("stock_upper_limit_days <>", value, "stockUpperLimitDays");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitDaysGreaterThan(Integer value) {
            addCriterion("stock_upper_limit_days >", value, "stockUpperLimitDays");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitDaysGreaterThanOrEqualTo(Integer value) {
            addCriterion("stock_upper_limit_days >=", value, "stockUpperLimitDays");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitDaysLessThan(Integer value) {
            addCriterion("stock_upper_limit_days <", value, "stockUpperLimitDays");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitDaysLessThanOrEqualTo(Integer value) {
            addCriterion("stock_upper_limit_days <=", value, "stockUpperLimitDays");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitDaysIn(List<Integer> values) {
            addCriterion("stock_upper_limit_days in", values, "stockUpperLimitDays");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitDaysNotIn(List<Integer> values) {
            addCriterion("stock_upper_limit_days not in", values, "stockUpperLimitDays");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitDaysBetween(Integer value1, Integer value2) {
            addCriterion("stock_upper_limit_days between", value1, value2, "stockUpperLimitDays");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitDaysNotBetween(Integer value1, Integer value2) {
            addCriterion("stock_upper_limit_days not between", value1, value2, "stockUpperLimitDays");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitDaysIsNull() {
            addCriterion("stock_lower_limit_days is null");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitDaysIsNotNull() {
            addCriterion("stock_lower_limit_days is not null");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitDaysEqualTo(Integer value) {
            addCriterion("stock_lower_limit_days =", value, "stockLowerLimitDays");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitDaysNotEqualTo(Integer value) {
            addCriterion("stock_lower_limit_days <>", value, "stockLowerLimitDays");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitDaysGreaterThan(Integer value) {
            addCriterion("stock_lower_limit_days >", value, "stockLowerLimitDays");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitDaysGreaterThanOrEqualTo(Integer value) {
            addCriterion("stock_lower_limit_days >=", value, "stockLowerLimitDays");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitDaysLessThan(Integer value) {
            addCriterion("stock_lower_limit_days <", value, "stockLowerLimitDays");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitDaysLessThanOrEqualTo(Integer value) {
            addCriterion("stock_lower_limit_days <=", value, "stockLowerLimitDays");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitDaysIn(List<Integer> values) {
            addCriterion("stock_lower_limit_days in", values, "stockLowerLimitDays");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitDaysNotIn(List<Integer> values) {
            addCriterion("stock_lower_limit_days not in", values, "stockLowerLimitDays");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitDaysBetween(Integer value1, Integer value2) {
            addCriterion("stock_lower_limit_days between", value1, value2, "stockLowerLimitDays");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitDaysNotBetween(Integer value1, Integer value2) {
            addCriterion("stock_lower_limit_days not between", value1, value2, "stockLowerLimitDays");
            return (Criteria) this;
        }

        public Criteria andBdpAverageDailySalesIsNull() {
            addCriterion("bdp_average_daily_sales is null");
            return (Criteria) this;
        }

        public Criteria andBdpAverageDailySalesIsNotNull() {
            addCriterion("bdp_average_daily_sales is not null");
            return (Criteria) this;
        }

        public Criteria andBdpAverageDailySalesEqualTo(BigDecimal value) {
            addCriterion("bdp_average_daily_sales =", value, "bdpAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andBdpAverageDailySalesNotEqualTo(BigDecimal value) {
            addCriterion("bdp_average_daily_sales <>", value, "bdpAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andBdpAverageDailySalesGreaterThan(BigDecimal value) {
            addCriterion("bdp_average_daily_sales >", value, "bdpAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andBdpAverageDailySalesGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("bdp_average_daily_sales >=", value, "bdpAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andBdpAverageDailySalesLessThan(BigDecimal value) {
            addCriterion("bdp_average_daily_sales <", value, "bdpAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andBdpAverageDailySalesLessThanOrEqualTo(BigDecimal value) {
            addCriterion("bdp_average_daily_sales <=", value, "bdpAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andBdpAverageDailySalesIn(List<BigDecimal> values) {
            addCriterion("bdp_average_daily_sales in", values, "bdpAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andBdpAverageDailySalesNotIn(List<BigDecimal> values) {
            addCriterion("bdp_average_daily_sales not in", values, "bdpAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andBdpAverageDailySalesBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("bdp_average_daily_sales between", value1, value2, "bdpAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andBdpAverageDailySalesNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("bdp_average_daily_sales not between", value1, value2, "bdpAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andMinDisplayQtyIsNull() {
            addCriterion("min_display_qty is null");
            return (Criteria) this;
        }

        public Criteria andMinDisplayQtyIsNotNull() {
            addCriterion("min_display_qty is not null");
            return (Criteria) this;
        }

        public Criteria andMinDisplayQtyEqualTo(BigDecimal value) {
            addCriterion("min_display_qty =", value, "minDisplayQty");
            return (Criteria) this;
        }

        public Criteria andMinDisplayQtyNotEqualTo(BigDecimal value) {
            addCriterion("min_display_qty <>", value, "minDisplayQty");
            return (Criteria) this;
        }

        public Criteria andMinDisplayQtyGreaterThan(BigDecimal value) {
            addCriterion("min_display_qty >", value, "minDisplayQty");
            return (Criteria) this;
        }

        public Criteria andMinDisplayQtyGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("min_display_qty >=", value, "minDisplayQty");
            return (Criteria) this;
        }

        public Criteria andMinDisplayQtyLessThan(BigDecimal value) {
            addCriterion("min_display_qty <", value, "minDisplayQty");
            return (Criteria) this;
        }

        public Criteria andMinDisplayQtyLessThanOrEqualTo(BigDecimal value) {
            addCriterion("min_display_qty <=", value, "minDisplayQty");
            return (Criteria) this;
        }

        public Criteria andMinDisplayQtyIn(List<BigDecimal> values) {
            addCriterion("min_display_qty in", values, "minDisplayQty");
            return (Criteria) this;
        }

        public Criteria andMinDisplayQtyNotIn(List<BigDecimal> values) {
            addCriterion("min_display_qty not in", values, "minDisplayQty");
            return (Criteria) this;
        }

        public Criteria andMinDisplayQtyBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("min_display_qty between", value1, value2, "minDisplayQty");
            return (Criteria) this;
        }

        public Criteria andMinDisplayQtyNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("min_display_qty not between", value1, value2, "minDisplayQty");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitIsNull() {
            addCriterion("stock_upper_limit is null");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitIsNotNull() {
            addCriterion("stock_upper_limit is not null");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitEqualTo(BigDecimal value) {
            addCriterion("stock_upper_limit =", value, "stockUpperLimit");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitNotEqualTo(BigDecimal value) {
            addCriterion("stock_upper_limit <>", value, "stockUpperLimit");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitGreaterThan(BigDecimal value) {
            addCriterion("stock_upper_limit >", value, "stockUpperLimit");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("stock_upper_limit >=", value, "stockUpperLimit");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitLessThan(BigDecimal value) {
            addCriterion("stock_upper_limit <", value, "stockUpperLimit");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitLessThanOrEqualTo(BigDecimal value) {
            addCriterion("stock_upper_limit <=", value, "stockUpperLimit");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitIn(List<BigDecimal> values) {
            addCriterion("stock_upper_limit in", values, "stockUpperLimit");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitNotIn(List<BigDecimal> values) {
            addCriterion("stock_upper_limit not in", values, "stockUpperLimit");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("stock_upper_limit between", value1, value2, "stockUpperLimit");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("stock_upper_limit not between", value1, value2, "stockUpperLimit");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitIsNull() {
            addCriterion("stock_lower_limit is null");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitIsNotNull() {
            addCriterion("stock_lower_limit is not null");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitEqualTo(BigDecimal value) {
            addCriterion("stock_lower_limit =", value, "stockLowerLimit");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitNotEqualTo(BigDecimal value) {
            addCriterion("stock_lower_limit <>", value, "stockLowerLimit");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitGreaterThan(BigDecimal value) {
            addCriterion("stock_lower_limit >", value, "stockLowerLimit");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("stock_lower_limit >=", value, "stockLowerLimit");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitLessThan(BigDecimal value) {
            addCriterion("stock_lower_limit <", value, "stockLowerLimit");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitLessThanOrEqualTo(BigDecimal value) {
            addCriterion("stock_lower_limit <=", value, "stockLowerLimit");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitIn(List<BigDecimal> values) {
            addCriterion("stock_lower_limit in", values, "stockLowerLimit");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitNotIn(List<BigDecimal> values) {
            addCriterion("stock_lower_limit not in", values, "stockLowerLimit");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("stock_lower_limit between", value1, value2, "stockLowerLimit");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("stock_lower_limit not between", value1, value2, "stockLowerLimit");
            return (Criteria) this;
        }

        public Criteria andBuyStockIsNull() {
            addCriterion("buy_stock is null");
            return (Criteria) this;
        }

        public Criteria andBuyStockIsNotNull() {
            addCriterion("buy_stock is not null");
            return (Criteria) this;
        }

        public Criteria andBuyStockEqualTo(BigDecimal value) {
            addCriterion("buy_stock =", value, "buyStock");
            return (Criteria) this;
        }

        public Criteria andBuyStockNotEqualTo(BigDecimal value) {
            addCriterion("buy_stock <>", value, "buyStock");
            return (Criteria) this;
        }

        public Criteria andBuyStockGreaterThan(BigDecimal value) {
            addCriterion("buy_stock >", value, "buyStock");
            return (Criteria) this;
        }

        public Criteria andBuyStockGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("buy_stock >=", value, "buyStock");
            return (Criteria) this;
        }

        public Criteria andBuyStockLessThan(BigDecimal value) {
            addCriterion("buy_stock <", value, "buyStock");
            return (Criteria) this;
        }

        public Criteria andBuyStockLessThanOrEqualTo(BigDecimal value) {
            addCriterion("buy_stock <=", value, "buyStock");
            return (Criteria) this;
        }

        public Criteria andBuyStockIn(List<BigDecimal> values) {
            addCriterion("buy_stock in", values, "buyStock");
            return (Criteria) this;
        }

        public Criteria andBuyStockNotIn(List<BigDecimal> values) {
            addCriterion("buy_stock not in", values, "buyStock");
            return (Criteria) this;
        }

        public Criteria andBuyStockBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("buy_stock between", value1, value2, "buyStock");
            return (Criteria) this;
        }

        public Criteria andBuyStockNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("buy_stock not between", value1, value2, "buyStock");
            return (Criteria) this;
        }

        public Criteria andSaleDaysBeforeIsNull() {
            addCriterion("sale_days_before is null");
            return (Criteria) this;
        }

        public Criteria andSaleDaysBeforeIsNotNull() {
            addCriterion("sale_days_before is not null");
            return (Criteria) this;
        }

        public Criteria andSaleDaysBeforeEqualTo(BigDecimal value) {
            addCriterion("sale_days_before =", value, "saleDaysBefore");
            return (Criteria) this;
        }

        public Criteria andSaleDaysBeforeNotEqualTo(BigDecimal value) {
            addCriterion("sale_days_before <>", value, "saleDaysBefore");
            return (Criteria) this;
        }

        public Criteria andSaleDaysBeforeGreaterThan(BigDecimal value) {
            addCriterion("sale_days_before >", value, "saleDaysBefore");
            return (Criteria) this;
        }

        public Criteria andSaleDaysBeforeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("sale_days_before >=", value, "saleDaysBefore");
            return (Criteria) this;
        }

        public Criteria andSaleDaysBeforeLessThan(BigDecimal value) {
            addCriterion("sale_days_before <", value, "saleDaysBefore");
            return (Criteria) this;
        }

        public Criteria andSaleDaysBeforeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("sale_days_before <=", value, "saleDaysBefore");
            return (Criteria) this;
        }

        public Criteria andSaleDaysBeforeIn(List<BigDecimal> values) {
            addCriterion("sale_days_before in", values, "saleDaysBefore");
            return (Criteria) this;
        }

        public Criteria andSaleDaysBeforeNotIn(List<BigDecimal> values) {
            addCriterion("sale_days_before not in", values, "saleDaysBefore");
            return (Criteria) this;
        }

        public Criteria andSaleDaysBeforeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("sale_days_before between", value1, value2, "saleDaysBefore");
            return (Criteria) this;
        }

        public Criteria andSaleDaysBeforeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("sale_days_before not between", value1, value2, "saleDaysBefore");
            return (Criteria) this;
        }

        public Criteria andSaleDaysAfterIsNull() {
            addCriterion("sale_days_after is null");
            return (Criteria) this;
        }

        public Criteria andSaleDaysAfterIsNotNull() {
            addCriterion("sale_days_after is not null");
            return (Criteria) this;
        }

        public Criteria andSaleDaysAfterEqualTo(BigDecimal value) {
            addCriterion("sale_days_after =", value, "saleDaysAfter");
            return (Criteria) this;
        }

        public Criteria andSaleDaysAfterNotEqualTo(BigDecimal value) {
            addCriterion("sale_days_after <>", value, "saleDaysAfter");
            return (Criteria) this;
        }

        public Criteria andSaleDaysAfterGreaterThan(BigDecimal value) {
            addCriterion("sale_days_after >", value, "saleDaysAfter");
            return (Criteria) this;
        }

        public Criteria andSaleDaysAfterGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("sale_days_after >=", value, "saleDaysAfter");
            return (Criteria) this;
        }

        public Criteria andSaleDaysAfterLessThan(BigDecimal value) {
            addCriterion("sale_days_after <", value, "saleDaysAfter");
            return (Criteria) this;
        }

        public Criteria andSaleDaysAfterLessThanOrEqualTo(BigDecimal value) {
            addCriterion("sale_days_after <=", value, "saleDaysAfter");
            return (Criteria) this;
        }

        public Criteria andSaleDaysAfterIn(List<BigDecimal> values) {
            addCriterion("sale_days_after in", values, "saleDaysAfter");
            return (Criteria) this;
        }

        public Criteria andSaleDaysAfterNotIn(List<BigDecimal> values) {
            addCriterion("sale_days_after not in", values, "saleDaysAfter");
            return (Criteria) this;
        }

        public Criteria andSaleDaysAfterBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("sale_days_after between", value1, value2, "saleDaysAfter");
            return (Criteria) this;
        }

        public Criteria andSaleDaysAfterNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("sale_days_after not between", value1, value2, "saleDaysAfter");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysSalesIsNull() {
            addCriterion("thirty_days_sales is null");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysSalesIsNotNull() {
            addCriterion("thirty_days_sales is not null");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysSalesEqualTo(BigDecimal value) {
            addCriterion("thirty_days_sales =", value, "thirtyDaysSales");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysSalesNotEqualTo(BigDecimal value) {
            addCriterion("thirty_days_sales <>", value, "thirtyDaysSales");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysSalesGreaterThan(BigDecimal value) {
            addCriterion("thirty_days_sales >", value, "thirtyDaysSales");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysSalesGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("thirty_days_sales >=", value, "thirtyDaysSales");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysSalesLessThan(BigDecimal value) {
            addCriterion("thirty_days_sales <", value, "thirtyDaysSales");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysSalesLessThanOrEqualTo(BigDecimal value) {
            addCriterion("thirty_days_sales <=", value, "thirtyDaysSales");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysSalesIn(List<BigDecimal> values) {
            addCriterion("thirty_days_sales in", values, "thirtyDaysSales");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysSalesNotIn(List<BigDecimal> values) {
            addCriterion("thirty_days_sales not in", values, "thirtyDaysSales");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysSalesBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("thirty_days_sales between", value1, value2, "thirtyDaysSales");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysSalesNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("thirty_days_sales not between", value1, value2, "thirtyDaysSales");
            return (Criteria) this;
        }

        public Criteria andNinetyDaysSalesIsNull() {
            addCriterion("ninety_days_sales is null");
            return (Criteria) this;
        }

        public Criteria andNinetyDaysSalesIsNotNull() {
            addCriterion("ninety_days_sales is not null");
            return (Criteria) this;
        }

        public Criteria andNinetyDaysSalesEqualTo(BigDecimal value) {
            addCriterion("ninety_days_sales =", value, "ninetyDaysSales");
            return (Criteria) this;
        }

        public Criteria andNinetyDaysSalesNotEqualTo(BigDecimal value) {
            addCriterion("ninety_days_sales <>", value, "ninetyDaysSales");
            return (Criteria) this;
        }

        public Criteria andNinetyDaysSalesGreaterThan(BigDecimal value) {
            addCriterion("ninety_days_sales >", value, "ninetyDaysSales");
            return (Criteria) this;
        }

        public Criteria andNinetyDaysSalesGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("ninety_days_sales >=", value, "ninetyDaysSales");
            return (Criteria) this;
        }

        public Criteria andNinetyDaysSalesLessThan(BigDecimal value) {
            addCriterion("ninety_days_sales <", value, "ninetyDaysSales");
            return (Criteria) this;
        }

        public Criteria andNinetyDaysSalesLessThanOrEqualTo(BigDecimal value) {
            addCriterion("ninety_days_sales <=", value, "ninetyDaysSales");
            return (Criteria) this;
        }

        public Criteria andNinetyDaysSalesIn(List<BigDecimal> values) {
            addCriterion("ninety_days_sales in", values, "ninetyDaysSales");
            return (Criteria) this;
        }

        public Criteria andNinetyDaysSalesNotIn(List<BigDecimal> values) {
            addCriterion("ninety_days_sales not in", values, "ninetyDaysSales");
            return (Criteria) this;
        }

        public Criteria andNinetyDaysSalesBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("ninety_days_sales between", value1, value2, "ninetyDaysSales");
            return (Criteria) this;
        }

        public Criteria andNinetyDaysSalesNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("ninety_days_sales not between", value1, value2, "ninetyDaysSales");
            return (Criteria) this;
        }

        public Criteria andCompanyApplyTotalIsNull() {
            addCriterion("company_apply_total is null");
            return (Criteria) this;
        }

        public Criteria andCompanyApplyTotalIsNotNull() {
            addCriterion("company_apply_total is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyApplyTotalEqualTo(Long value) {
            addCriterion("company_apply_total =", value, "companyApplyTotal");
            return (Criteria) this;
        }

        public Criteria andCompanyApplyTotalNotEqualTo(Long value) {
            addCriterion("company_apply_total <>", value, "companyApplyTotal");
            return (Criteria) this;
        }

        public Criteria andCompanyApplyTotalGreaterThan(Long value) {
            addCriterion("company_apply_total >", value, "companyApplyTotal");
            return (Criteria) this;
        }

        public Criteria andCompanyApplyTotalGreaterThanOrEqualTo(Long value) {
            addCriterion("company_apply_total >=", value, "companyApplyTotal");
            return (Criteria) this;
        }

        public Criteria andCompanyApplyTotalLessThan(Long value) {
            addCriterion("company_apply_total <", value, "companyApplyTotal");
            return (Criteria) this;
        }

        public Criteria andCompanyApplyTotalLessThanOrEqualTo(Long value) {
            addCriterion("company_apply_total <=", value, "companyApplyTotal");
            return (Criteria) this;
        }

        public Criteria andCompanyApplyTotalIn(List<Long> values) {
            addCriterion("company_apply_total in", values, "companyApplyTotal");
            return (Criteria) this;
        }

        public Criteria andCompanyApplyTotalNotIn(List<Long> values) {
            addCriterion("company_apply_total not in", values, "companyApplyTotal");
            return (Criteria) this;
        }

        public Criteria andCompanyApplyTotalBetween(Long value1, Long value2) {
            addCriterion("company_apply_total between", value1, value2, "companyApplyTotal");
            return (Criteria) this;
        }

        public Criteria andCompanyApplyTotalNotBetween(Long value1, Long value2) {
            addCriterion("company_apply_total not between", value1, value2, "companyApplyTotal");
            return (Criteria) this;
        }

        public Criteria andStoreApplyTotalIsNull() {
            addCriterion("store_apply_total is null");
            return (Criteria) this;
        }

        public Criteria andStoreApplyTotalIsNotNull() {
            addCriterion("store_apply_total is not null");
            return (Criteria) this;
        }

        public Criteria andStoreApplyTotalEqualTo(Long value) {
            addCriterion("store_apply_total =", value, "storeApplyTotal");
            return (Criteria) this;
        }

        public Criteria andStoreApplyTotalNotEqualTo(Long value) {
            addCriterion("store_apply_total <>", value, "storeApplyTotal");
            return (Criteria) this;
        }

        public Criteria andStoreApplyTotalGreaterThan(Long value) {
            addCriterion("store_apply_total >", value, "storeApplyTotal");
            return (Criteria) this;
        }

        public Criteria andStoreApplyTotalGreaterThanOrEqualTo(Long value) {
            addCriterion("store_apply_total >=", value, "storeApplyTotal");
            return (Criteria) this;
        }

        public Criteria andStoreApplyTotalLessThan(Long value) {
            addCriterion("store_apply_total <", value, "storeApplyTotal");
            return (Criteria) this;
        }

        public Criteria andStoreApplyTotalLessThanOrEqualTo(Long value) {
            addCriterion("store_apply_total <=", value, "storeApplyTotal");
            return (Criteria) this;
        }

        public Criteria andStoreApplyTotalIn(List<Long> values) {
            addCriterion("store_apply_total in", values, "storeApplyTotal");
            return (Criteria) this;
        }

        public Criteria andStoreApplyTotalNotIn(List<Long> values) {
            addCriterion("store_apply_total not in", values, "storeApplyTotal");
            return (Criteria) this;
        }

        public Criteria andStoreApplyTotalBetween(Long value1, Long value2) {
            addCriterion("store_apply_total between", value1, value2, "storeApplyTotal");
            return (Criteria) this;
        }

        public Criteria andStoreApplyTotalNotBetween(Long value1, Long value2) {
            addCriterion("store_apply_total not between", value1, value2, "storeApplyTotal");
            return (Criteria) this;
        }

        public Criteria andMiddlePackageSwitchIsNull() {
            addCriterion("middle_package_switch is null");
            return (Criteria) this;
        }

        public Criteria andMiddlePackageSwitchIsNotNull() {
            addCriterion("middle_package_switch is not null");
            return (Criteria) this;
        }

        public Criteria andMiddlePackageSwitchEqualTo(String value) {
            addCriterion("middle_package_switch =", value, "middlePackageSwitch");
            return (Criteria) this;
        }

        public Criteria andMiddlePackageSwitchNotEqualTo(String value) {
            addCriterion("middle_package_switch <>", value, "middlePackageSwitch");
            return (Criteria) this;
        }

        public Criteria andMiddlePackageSwitchGreaterThan(String value) {
            addCriterion("middle_package_switch >", value, "middlePackageSwitch");
            return (Criteria) this;
        }

        public Criteria andMiddlePackageSwitchGreaterThanOrEqualTo(String value) {
            addCriterion("middle_package_switch >=", value, "middlePackageSwitch");
            return (Criteria) this;
        }

        public Criteria andMiddlePackageSwitchLessThan(String value) {
            addCriterion("middle_package_switch <", value, "middlePackageSwitch");
            return (Criteria) this;
        }

        public Criteria andMiddlePackageSwitchLessThanOrEqualTo(String value) {
            addCriterion("middle_package_switch <=", value, "middlePackageSwitch");
            return (Criteria) this;
        }

        public Criteria andMiddlePackageSwitchLike(String value) {
            addCriterion("middle_package_switch like", value, "middlePackageSwitch");
            return (Criteria) this;
        }

        public Criteria andMiddlePackageSwitchNotLike(String value) {
            addCriterion("middle_package_switch not like", value, "middlePackageSwitch");
            return (Criteria) this;
        }

        public Criteria andMiddlePackageSwitchIn(List<String> values) {
            addCriterion("middle_package_switch in", values, "middlePackageSwitch");
            return (Criteria) this;
        }

        public Criteria andMiddlePackageSwitchNotIn(List<String> values) {
            addCriterion("middle_package_switch not in", values, "middlePackageSwitch");
            return (Criteria) this;
        }

        public Criteria andMiddlePackageSwitchBetween(String value1, String value2) {
            addCriterion("middle_package_switch between", value1, value2, "middlePackageSwitch");
            return (Criteria) this;
        }

        public Criteria andMiddlePackageSwitchNotBetween(String value1, String value2) {
            addCriterion("middle_package_switch not between", value1, value2, "middlePackageSwitch");
            return (Criteria) this;
        }

        public Criteria andMiddlePackageQtyIsNull() {
            addCriterion("middle_package_qty is null");
            return (Criteria) this;
        }

        public Criteria andMiddlePackageQtyIsNotNull() {
            addCriterion("middle_package_qty is not null");
            return (Criteria) this;
        }

        public Criteria andMiddlePackageQtyEqualTo(BigDecimal value) {
            addCriterion("middle_package_qty =", value, "middlePackageQty");
            return (Criteria) this;
        }

        public Criteria andMiddlePackageQtyNotEqualTo(BigDecimal value) {
            addCriterion("middle_package_qty <>", value, "middlePackageQty");
            return (Criteria) this;
        }

        public Criteria andMiddlePackageQtyGreaterThan(BigDecimal value) {
            addCriterion("middle_package_qty >", value, "middlePackageQty");
            return (Criteria) this;
        }

        public Criteria andMiddlePackageQtyGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("middle_package_qty >=", value, "middlePackageQty");
            return (Criteria) this;
        }

        public Criteria andMiddlePackageQtyLessThan(BigDecimal value) {
            addCriterion("middle_package_qty <", value, "middlePackageQty");
            return (Criteria) this;
        }

        public Criteria andMiddlePackageQtyLessThanOrEqualTo(BigDecimal value) {
            addCriterion("middle_package_qty <=", value, "middlePackageQty");
            return (Criteria) this;
        }

        public Criteria andMiddlePackageQtyIn(List<BigDecimal> values) {
            addCriterion("middle_package_qty in", values, "middlePackageQty");
            return (Criteria) this;
        }

        public Criteria andMiddlePackageQtyNotIn(List<BigDecimal> values) {
            addCriterion("middle_package_qty not in", values, "middlePackageQty");
            return (Criteria) this;
        }

        public Criteria andMiddlePackageQtyBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("middle_package_qty between", value1, value2, "middlePackageQty");
            return (Criteria) this;
        }

        public Criteria andMiddlePackageQtyNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("middle_package_qty not between", value1, value2, "middlePackageQty");
            return (Criteria) this;
        }

        public Criteria andMiddleCodeFlagIsNull() {
            addCriterion("middle_code_flag is null");
            return (Criteria) this;
        }

        public Criteria andMiddleCodeFlagIsNotNull() {
            addCriterion("middle_code_flag is not null");
            return (Criteria) this;
        }

        public Criteria andMiddleCodeFlagEqualTo(Byte value) {
            addCriterion("middle_code_flag =", value, "middleCodeFlag");
            return (Criteria) this;
        }

        public Criteria andMiddleCodeFlagNotEqualTo(Byte value) {
            addCriterion("middle_code_flag <>", value, "middleCodeFlag");
            return (Criteria) this;
        }

        public Criteria andMiddleCodeFlagGreaterThan(Byte value) {
            addCriterion("middle_code_flag >", value, "middleCodeFlag");
            return (Criteria) this;
        }

        public Criteria andMiddleCodeFlagGreaterThanOrEqualTo(Byte value) {
            addCriterion("middle_code_flag >=", value, "middleCodeFlag");
            return (Criteria) this;
        }

        public Criteria andMiddleCodeFlagLessThan(Byte value) {
            addCriterion("middle_code_flag <", value, "middleCodeFlag");
            return (Criteria) this;
        }

        public Criteria andMiddleCodeFlagLessThanOrEqualTo(Byte value) {
            addCriterion("middle_code_flag <=", value, "middleCodeFlag");
            return (Criteria) this;
        }

        public Criteria andMiddleCodeFlagIn(List<Byte> values) {
            addCriterion("middle_code_flag in", values, "middleCodeFlag");
            return (Criteria) this;
        }

        public Criteria andMiddleCodeFlagNotIn(List<Byte> values) {
            addCriterion("middle_code_flag not in", values, "middleCodeFlag");
            return (Criteria) this;
        }

        public Criteria andMiddleCodeFlagBetween(Byte value1, Byte value2) {
            addCriterion("middle_code_flag between", value1, value2, "middleCodeFlag");
            return (Criteria) this;
        }

        public Criteria andMiddleCodeFlagNotBetween(Byte value1, Byte value2) {
            addCriterion("middle_code_flag not between", value1, value2, "middleCodeFlag");
            return (Criteria) this;
        }

        public Criteria andApplyRatioIsNull() {
            addCriterion("apply_ratio is null");
            return (Criteria) this;
        }

        public Criteria andApplyRatioIsNotNull() {
            addCriterion("apply_ratio is not null");
            return (Criteria) this;
        }

        public Criteria andApplyRatioEqualTo(BigDecimal value) {
            addCriterion("apply_ratio =", value, "applyRatio");
            return (Criteria) this;
        }

        public Criteria andApplyRatioNotEqualTo(BigDecimal value) {
            addCriterion("apply_ratio <>", value, "applyRatio");
            return (Criteria) this;
        }

        public Criteria andApplyRatioGreaterThan(BigDecimal value) {
            addCriterion("apply_ratio >", value, "applyRatio");
            return (Criteria) this;
        }

        public Criteria andApplyRatioGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("apply_ratio >=", value, "applyRatio");
            return (Criteria) this;
        }

        public Criteria andApplyRatioLessThan(BigDecimal value) {
            addCriterion("apply_ratio <", value, "applyRatio");
            return (Criteria) this;
        }

        public Criteria andApplyRatioLessThanOrEqualTo(BigDecimal value) {
            addCriterion("apply_ratio <=", value, "applyRatio");
            return (Criteria) this;
        }

        public Criteria andApplyRatioIn(List<BigDecimal> values) {
            addCriterion("apply_ratio in", values, "applyRatio");
            return (Criteria) this;
        }

        public Criteria andApplyRatioNotIn(List<BigDecimal> values) {
            addCriterion("apply_ratio not in", values, "applyRatio");
            return (Criteria) this;
        }

        public Criteria andApplyRatioBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("apply_ratio between", value1, value2, "applyRatio");
            return (Criteria) this;
        }

        public Criteria andApplyRatioNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("apply_ratio not between", value1, value2, "applyRatio");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIsNull() {
            addCriterion("category_id is null");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIsNotNull() {
            addCriterion("category_id is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryIdEqualTo(Long value) {
            addCriterion("category_id =", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotEqualTo(Long value) {
            addCriterion("category_id <>", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdGreaterThan(Long value) {
            addCriterion("category_id >", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdGreaterThanOrEqualTo(Long value) {
            addCriterion("category_id >=", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdLessThan(Long value) {
            addCriterion("category_id <", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdLessThanOrEqualTo(Long value) {
            addCriterion("category_id <=", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIn(List<Long> values) {
            addCriterion("category_id in", values, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotIn(List<Long> values) {
            addCriterion("category_id not in", values, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdBetween(Long value1, Long value2) {
            addCriterion("category_id between", value1, value2, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotBetween(Long value1, Long value2) {
            addCriterion("category_id not between", value1, value2, "categoryId");
            return (Criteria) this;
        }

        public Criteria andPurchaseTypeIsNull() {
            addCriterion("purchase_type is null");
            return (Criteria) this;
        }

        public Criteria andPurchaseTypeIsNotNull() {
            addCriterion("purchase_type is not null");
            return (Criteria) this;
        }

        public Criteria andPurchaseTypeEqualTo(Integer value) {
            addCriterion("purchase_type =", value, "purchaseType");
            return (Criteria) this;
        }

        public Criteria andPurchaseTypeNotEqualTo(Integer value) {
            addCriterion("purchase_type <>", value, "purchaseType");
            return (Criteria) this;
        }

        public Criteria andPurchaseTypeGreaterThan(Integer value) {
            addCriterion("purchase_type >", value, "purchaseType");
            return (Criteria) this;
        }

        public Criteria andPurchaseTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("purchase_type >=", value, "purchaseType");
            return (Criteria) this;
        }

        public Criteria andPurchaseTypeLessThan(Integer value) {
            addCriterion("purchase_type <", value, "purchaseType");
            return (Criteria) this;
        }

        public Criteria andPurchaseTypeLessThanOrEqualTo(Integer value) {
            addCriterion("purchase_type <=", value, "purchaseType");
            return (Criteria) this;
        }

        public Criteria andPurchaseTypeIn(List<Integer> values) {
            addCriterion("purchase_type in", values, "purchaseType");
            return (Criteria) this;
        }

        public Criteria andPurchaseTypeNotIn(List<Integer> values) {
            addCriterion("purchase_type not in", values, "purchaseType");
            return (Criteria) this;
        }

        public Criteria andPurchaseTypeBetween(Integer value1, Integer value2) {
            addCriterion("purchase_type between", value1, value2, "purchaseType");
            return (Criteria) this;
        }

        public Criteria andPurchaseTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("purchase_type not between", value1, value2, "purchaseType");
            return (Criteria) this;
        }

        public Criteria andPurchaseChannelIsNull() {
            addCriterion("purchase_channel is null");
            return (Criteria) this;
        }

        public Criteria andPurchaseChannelIsNotNull() {
            addCriterion("purchase_channel is not null");
            return (Criteria) this;
        }

        public Criteria andPurchaseChannelEqualTo(String value) {
            addCriterion("purchase_channel =", value, "purchaseChannel");
            return (Criteria) this;
        }

        public Criteria andPurchaseChannelNotEqualTo(String value) {
            addCriterion("purchase_channel <>", value, "purchaseChannel");
            return (Criteria) this;
        }

        public Criteria andPurchaseChannelGreaterThan(String value) {
            addCriterion("purchase_channel >", value, "purchaseChannel");
            return (Criteria) this;
        }

        public Criteria andPurchaseChannelGreaterThanOrEqualTo(String value) {
            addCriterion("purchase_channel >=", value, "purchaseChannel");
            return (Criteria) this;
        }

        public Criteria andPurchaseChannelLessThan(String value) {
            addCriterion("purchase_channel <", value, "purchaseChannel");
            return (Criteria) this;
        }

        public Criteria andPurchaseChannelLessThanOrEqualTo(String value) {
            addCriterion("purchase_channel <=", value, "purchaseChannel");
            return (Criteria) this;
        }

        public Criteria andPurchaseChannelLike(String value) {
            addCriterion("purchase_channel like", value, "purchaseChannel");
            return (Criteria) this;
        }

        public Criteria andPurchaseChannelNotLike(String value) {
            addCriterion("purchase_channel not like", value, "purchaseChannel");
            return (Criteria) this;
        }

        public Criteria andPurchaseChannelIn(List<String> values) {
            addCriterion("purchase_channel in", values, "purchaseChannel");
            return (Criteria) this;
        }

        public Criteria andPurchaseChannelNotIn(List<String> values) {
            addCriterion("purchase_channel not in", values, "purchaseChannel");
            return (Criteria) this;
        }

        public Criteria andPurchaseChannelBetween(String value1, String value2) {
            addCriterion("purchase_channel between", value1, value2, "purchaseChannel");
            return (Criteria) this;
        }

        public Criteria andPurchaseChannelNotBetween(String value1, String value2) {
            addCriterion("purchase_channel not between", value1, value2, "purchaseChannel");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeIsNull() {
            addCriterion("warehouse_code is null");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeIsNotNull() {
            addCriterion("warehouse_code is not null");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeEqualTo(String value) {
            addCriterion("warehouse_code =", value, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeNotEqualTo(String value) {
            addCriterion("warehouse_code <>", value, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeGreaterThan(String value) {
            addCriterion("warehouse_code >", value, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeGreaterThanOrEqualTo(String value) {
            addCriterion("warehouse_code >=", value, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeLessThan(String value) {
            addCriterion("warehouse_code <", value, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeLessThanOrEqualTo(String value) {
            addCriterion("warehouse_code <=", value, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeLike(String value) {
            addCriterion("warehouse_code like", value, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeNotLike(String value) {
            addCriterion("warehouse_code not like", value, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeIn(List<String> values) {
            addCriterion("warehouse_code in", values, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeNotIn(List<String> values) {
            addCriterion("warehouse_code not in", values, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeBetween(String value1, String value2) {
            addCriterion("warehouse_code between", value1, value2, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeNotBetween(String value1, String value2) {
            addCriterion("warehouse_code not between", value1, value2, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andRecommendReasonIsNull() {
            addCriterion("recommend_reason is null");
            return (Criteria) this;
        }

        public Criteria andRecommendReasonIsNotNull() {
            addCriterion("recommend_reason is not null");
            return (Criteria) this;
        }

        public Criteria andRecommendReasonEqualTo(String value) {
            addCriterion("recommend_reason =", value, "recommendReason");
            return (Criteria) this;
        }

        public Criteria andRecommendReasonNotEqualTo(String value) {
            addCriterion("recommend_reason <>", value, "recommendReason");
            return (Criteria) this;
        }

        public Criteria andRecommendReasonGreaterThan(String value) {
            addCriterion("recommend_reason >", value, "recommendReason");
            return (Criteria) this;
        }

        public Criteria andRecommendReasonGreaterThanOrEqualTo(String value) {
            addCriterion("recommend_reason >=", value, "recommendReason");
            return (Criteria) this;
        }

        public Criteria andRecommendReasonLessThan(String value) {
            addCriterion("recommend_reason <", value, "recommendReason");
            return (Criteria) this;
        }

        public Criteria andRecommendReasonLessThanOrEqualTo(String value) {
            addCriterion("recommend_reason <=", value, "recommendReason");
            return (Criteria) this;
        }

        public Criteria andRecommendReasonLike(String value) {
            addCriterion("recommend_reason like", value, "recommendReason");
            return (Criteria) this;
        }

        public Criteria andRecommendReasonNotLike(String value) {
            addCriterion("recommend_reason not like", value, "recommendReason");
            return (Criteria) this;
        }

        public Criteria andRecommendReasonIn(List<String> values) {
            addCriterion("recommend_reason in", values, "recommendReason");
            return (Criteria) this;
        }

        public Criteria andRecommendReasonNotIn(List<String> values) {
            addCriterion("recommend_reason not in", values, "recommendReason");
            return (Criteria) this;
        }

        public Criteria andRecommendReasonBetween(String value1, String value2) {
            addCriterion("recommend_reason between", value1, value2, "recommendReason");
            return (Criteria) this;
        }

        public Criteria andRecommendReasonNotBetween(String value1, String value2) {
            addCriterion("recommend_reason not between", value1, value2, "recommendReason");
            return (Criteria) this;
        }

        public Criteria andPromotionNameIsNull() {
            addCriterion("promotion_name is null");
            return (Criteria) this;
        }

        public Criteria andPromotionNameIsNotNull() {
            addCriterion("promotion_name is not null");
            return (Criteria) this;
        }

        public Criteria andPromotionNameEqualTo(String value) {
            addCriterion("promotion_name =", value, "promotionName");
            return (Criteria) this;
        }

        public Criteria andPromotionNameNotEqualTo(String value) {
            addCriterion("promotion_name <>", value, "promotionName");
            return (Criteria) this;
        }

        public Criteria andPromotionNameGreaterThan(String value) {
            addCriterion("promotion_name >", value, "promotionName");
            return (Criteria) this;
        }

        public Criteria andPromotionNameGreaterThanOrEqualTo(String value) {
            addCriterion("promotion_name >=", value, "promotionName");
            return (Criteria) this;
        }

        public Criteria andPromotionNameLessThan(String value) {
            addCriterion("promotion_name <", value, "promotionName");
            return (Criteria) this;
        }

        public Criteria andPromotionNameLessThanOrEqualTo(String value) {
            addCriterion("promotion_name <=", value, "promotionName");
            return (Criteria) this;
        }

        public Criteria andPromotionNameLike(String value) {
            addCriterion("promotion_name like", value, "promotionName");
            return (Criteria) this;
        }

        public Criteria andPromotionNameNotLike(String value) {
            addCriterion("promotion_name not like", value, "promotionName");
            return (Criteria) this;
        }

        public Criteria andPromotionNameIn(List<String> values) {
            addCriterion("promotion_name in", values, "promotionName");
            return (Criteria) this;
        }

        public Criteria andPromotionNameNotIn(List<String> values) {
            addCriterion("promotion_name not in", values, "promotionName");
            return (Criteria) this;
        }

        public Criteria andPromotionNameBetween(String value1, String value2) {
            addCriterion("promotion_name between", value1, value2, "promotionName");
            return (Criteria) this;
        }

        public Criteria andPromotionNameNotBetween(String value1, String value2) {
            addCriterion("promotion_name not between", value1, value2, "promotionName");
            return (Criteria) this;
        }

        public Criteria andPromotionWayIsNull() {
            addCriterion("promotion_way is null");
            return (Criteria) this;
        }

        public Criteria andPromotionWayIsNotNull() {
            addCriterion("promotion_way is not null");
            return (Criteria) this;
        }

        public Criteria andPromotionWayEqualTo(String value) {
            addCriterion("promotion_way =", value, "promotionWay");
            return (Criteria) this;
        }

        public Criteria andPromotionWayNotEqualTo(String value) {
            addCriterion("promotion_way <>", value, "promotionWay");
            return (Criteria) this;
        }

        public Criteria andPromotionWayGreaterThan(String value) {
            addCriterion("promotion_way >", value, "promotionWay");
            return (Criteria) this;
        }

        public Criteria andPromotionWayGreaterThanOrEqualTo(String value) {
            addCriterion("promotion_way >=", value, "promotionWay");
            return (Criteria) this;
        }

        public Criteria andPromotionWayLessThan(String value) {
            addCriterion("promotion_way <", value, "promotionWay");
            return (Criteria) this;
        }

        public Criteria andPromotionWayLessThanOrEqualTo(String value) {
            addCriterion("promotion_way <=", value, "promotionWay");
            return (Criteria) this;
        }

        public Criteria andPromotionWayLike(String value) {
            addCriterion("promotion_way like", value, "promotionWay");
            return (Criteria) this;
        }

        public Criteria andPromotionWayNotLike(String value) {
            addCriterion("promotion_way not like", value, "promotionWay");
            return (Criteria) this;
        }

        public Criteria andPromotionWayIn(List<String> values) {
            addCriterion("promotion_way in", values, "promotionWay");
            return (Criteria) this;
        }

        public Criteria andPromotionWayNotIn(List<String> values) {
            addCriterion("promotion_way not in", values, "promotionWay");
            return (Criteria) this;
        }

        public Criteria andPromotionWayBetween(String value1, String value2) {
            addCriterion("promotion_way between", value1, value2, "promotionWay");
            return (Criteria) this;
        }

        public Criteria andPromotionWayNotBetween(String value1, String value2) {
            addCriterion("promotion_way not between", value1, value2, "promotionWay");
            return (Criteria) this;
        }

        public Criteria andThresholdInfoIsNull() {
            addCriterion("threshold_info is null");
            return (Criteria) this;
        }

        public Criteria andThresholdInfoIsNotNull() {
            addCriterion("threshold_info is not null");
            return (Criteria) this;
        }

        public Criteria andThresholdInfoEqualTo(String value) {
            addCriterion("threshold_info =", value, "thresholdInfo");
            return (Criteria) this;
        }

        public Criteria andThresholdInfoNotEqualTo(String value) {
            addCriterion("threshold_info <>", value, "thresholdInfo");
            return (Criteria) this;
        }

        public Criteria andThresholdInfoGreaterThan(String value) {
            addCriterion("threshold_info >", value, "thresholdInfo");
            return (Criteria) this;
        }

        public Criteria andThresholdInfoGreaterThanOrEqualTo(String value) {
            addCriterion("threshold_info >=", value, "thresholdInfo");
            return (Criteria) this;
        }

        public Criteria andThresholdInfoLessThan(String value) {
            addCriterion("threshold_info <", value, "thresholdInfo");
            return (Criteria) this;
        }

        public Criteria andThresholdInfoLessThanOrEqualTo(String value) {
            addCriterion("threshold_info <=", value, "thresholdInfo");
            return (Criteria) this;
        }

        public Criteria andThresholdInfoLike(String value) {
            addCriterion("threshold_info like", value, "thresholdInfo");
            return (Criteria) this;
        }

        public Criteria andThresholdInfoNotLike(String value) {
            addCriterion("threshold_info not like", value, "thresholdInfo");
            return (Criteria) this;
        }

        public Criteria andThresholdInfoIn(List<String> values) {
            addCriterion("threshold_info in", values, "thresholdInfo");
            return (Criteria) this;
        }

        public Criteria andThresholdInfoNotIn(List<String> values) {
            addCriterion("threshold_info not in", values, "thresholdInfo");
            return (Criteria) this;
        }

        public Criteria andThresholdInfoBetween(String value1, String value2) {
            addCriterion("threshold_info between", value1, value2, "thresholdInfo");
            return (Criteria) this;
        }

        public Criteria andThresholdInfoNotBetween(String value1, String value2) {
            addCriterion("threshold_info not between", value1, value2, "thresholdInfo");
            return (Criteria) this;
        }

        public Criteria andFavInfoIsNull() {
            addCriterion("fav_info is null");
            return (Criteria) this;
        }

        public Criteria andFavInfoIsNotNull() {
            addCriterion("fav_info is not null");
            return (Criteria) this;
        }

        public Criteria andFavInfoEqualTo(String value) {
            addCriterion("fav_info =", value, "favInfo");
            return (Criteria) this;
        }

        public Criteria andFavInfoNotEqualTo(String value) {
            addCriterion("fav_info <>", value, "favInfo");
            return (Criteria) this;
        }

        public Criteria andFavInfoGreaterThan(String value) {
            addCriterion("fav_info >", value, "favInfo");
            return (Criteria) this;
        }

        public Criteria andFavInfoGreaterThanOrEqualTo(String value) {
            addCriterion("fav_info >=", value, "favInfo");
            return (Criteria) this;
        }

        public Criteria andFavInfoLessThan(String value) {
            addCriterion("fav_info <", value, "favInfo");
            return (Criteria) this;
        }

        public Criteria andFavInfoLessThanOrEqualTo(String value) {
            addCriterion("fav_info <=", value, "favInfo");
            return (Criteria) this;
        }

        public Criteria andFavInfoLike(String value) {
            addCriterion("fav_info like", value, "favInfo");
            return (Criteria) this;
        }

        public Criteria andFavInfoNotLike(String value) {
            addCriterion("fav_info not like", value, "favInfo");
            return (Criteria) this;
        }

        public Criteria andFavInfoIn(List<String> values) {
            addCriterion("fav_info in", values, "favInfo");
            return (Criteria) this;
        }

        public Criteria andFavInfoNotIn(List<String> values) {
            addCriterion("fav_info not in", values, "favInfo");
            return (Criteria) this;
        }

        public Criteria andFavInfoBetween(String value1, String value2) {
            addCriterion("fav_info between", value1, value2, "favInfo");
            return (Criteria) this;
        }

        public Criteria andFavInfoNotBetween(String value1, String value2) {
            addCriterion("fav_info not between", value1, value2, "favInfo");
            return (Criteria) this;
        }

        public Criteria andCompositeNewIsNull() {
            addCriterion("composite_new is null");
            return (Criteria) this;
        }

        public Criteria andCompositeNewIsNotNull() {
            addCriterion("composite_new is not null");
            return (Criteria) this;
        }

        public Criteria andCompositeNewEqualTo(Byte value) {
            addCriterion("composite_new =", value, "compositeNew");
            return (Criteria) this;
        }

        public Criteria andCompositeNewNotEqualTo(Byte value) {
            addCriterion("composite_new <>", value, "compositeNew");
            return (Criteria) this;
        }

        public Criteria andCompositeNewGreaterThan(Byte value) {
            addCriterion("composite_new >", value, "compositeNew");
            return (Criteria) this;
        }

        public Criteria andCompositeNewGreaterThanOrEqualTo(Byte value) {
            addCriterion("composite_new >=", value, "compositeNew");
            return (Criteria) this;
        }

        public Criteria andCompositeNewLessThan(Byte value) {
            addCriterion("composite_new <", value, "compositeNew");
            return (Criteria) this;
        }

        public Criteria andCompositeNewLessThanOrEqualTo(Byte value) {
            addCriterion("composite_new <=", value, "compositeNew");
            return (Criteria) this;
        }

        public Criteria andCompositeNewIn(List<Byte> values) {
            addCriterion("composite_new in", values, "compositeNew");
            return (Criteria) this;
        }

        public Criteria andCompositeNewNotIn(List<Byte> values) {
            addCriterion("composite_new not in", values, "compositeNew");
            return (Criteria) this;
        }

        public Criteria andCompositeNewBetween(Byte value1, Byte value2) {
            addCriterion("composite_new between", value1, value2, "compositeNew");
            return (Criteria) this;
        }

        public Criteria andCompositeNewNotBetween(Byte value1, Byte value2) {
            addCriterion("composite_new not between", value1, value2, "compositeNew");
            return (Criteria) this;
        }

        public Criteria andThirtySalesQuantityIsNull() {
            addCriterion("thirty_sales_quantity is null");
            return (Criteria) this;
        }

        public Criteria andThirtySalesQuantityIsNotNull() {
            addCriterion("thirty_sales_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andThirtySalesQuantityEqualTo(BigDecimal value) {
            addCriterion("thirty_sales_quantity =", value, "thirtySalesQuantity");
            return (Criteria) this;
        }

        public Criteria andThirtySalesQuantityNotEqualTo(BigDecimal value) {
            addCriterion("thirty_sales_quantity <>", value, "thirtySalesQuantity");
            return (Criteria) this;
        }

        public Criteria andThirtySalesQuantityGreaterThan(BigDecimal value) {
            addCriterion("thirty_sales_quantity >", value, "thirtySalesQuantity");
            return (Criteria) this;
        }

        public Criteria andThirtySalesQuantityGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("thirty_sales_quantity >=", value, "thirtySalesQuantity");
            return (Criteria) this;
        }

        public Criteria andThirtySalesQuantityLessThan(BigDecimal value) {
            addCriterion("thirty_sales_quantity <", value, "thirtySalesQuantity");
            return (Criteria) this;
        }

        public Criteria andThirtySalesQuantityLessThanOrEqualTo(BigDecimal value) {
            addCriterion("thirty_sales_quantity <=", value, "thirtySalesQuantity");
            return (Criteria) this;
        }

        public Criteria andThirtySalesQuantityIn(List<BigDecimal> values) {
            addCriterion("thirty_sales_quantity in", values, "thirtySalesQuantity");
            return (Criteria) this;
        }

        public Criteria andThirtySalesQuantityNotIn(List<BigDecimal> values) {
            addCriterion("thirty_sales_quantity not in", values, "thirtySalesQuantity");
            return (Criteria) this;
        }

        public Criteria andThirtySalesQuantityBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("thirty_sales_quantity between", value1, value2, "thirtySalesQuantity");
            return (Criteria) this;
        }

        public Criteria andThirtySalesQuantityNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("thirty_sales_quantity not between", value1, value2, "thirtySalesQuantity");
            return (Criteria) this;
        }

        public Criteria andPromotionTitleIsNull() {
            addCriterion("promotion_title is null");
            return (Criteria) this;
        }

        public Criteria andPromotionTitleIsNotNull() {
            addCriterion("promotion_title is not null");
            return (Criteria) this;
        }

        public Criteria andPromotionTitleEqualTo(String value) {
            addCriterion("promotion_title =", value, "promotionTitle");
            return (Criteria) this;
        }

        public Criteria andPromotionTitleNotEqualTo(String value) {
            addCriterion("promotion_title <>", value, "promotionTitle");
            return (Criteria) this;
        }

        public Criteria andPromotionTitleGreaterThan(String value) {
            addCriterion("promotion_title >", value, "promotionTitle");
            return (Criteria) this;
        }

        public Criteria andPromotionTitleGreaterThanOrEqualTo(String value) {
            addCriterion("promotion_title >=", value, "promotionTitle");
            return (Criteria) this;
        }

        public Criteria andPromotionTitleLessThan(String value) {
            addCriterion("promotion_title <", value, "promotionTitle");
            return (Criteria) this;
        }

        public Criteria andPromotionTitleLessThanOrEqualTo(String value) {
            addCriterion("promotion_title <=", value, "promotionTitle");
            return (Criteria) this;
        }

        public Criteria andPromotionTitleLike(String value) {
            addCriterion("promotion_title like", value, "promotionTitle");
            return (Criteria) this;
        }

        public Criteria andPromotionTitleNotLike(String value) {
            addCriterion("promotion_title not like", value, "promotionTitle");
            return (Criteria) this;
        }

        public Criteria andPromotionTitleIn(List<String> values) {
            addCriterion("promotion_title in", values, "promotionTitle");
            return (Criteria) this;
        }

        public Criteria andPromotionTitleNotIn(List<String> values) {
            addCriterion("promotion_title not in", values, "promotionTitle");
            return (Criteria) this;
        }

        public Criteria andPromotionTitleBetween(String value1, String value2) {
            addCriterion("promotion_title between", value1, value2, "promotionTitle");
            return (Criteria) this;
        }

        public Criteria andPromotionTitleNotBetween(String value1, String value2) {
            addCriterion("promotion_title not between", value1, value2, "promotionTitle");
            return (Criteria) this;
        }

        public Criteria andPromotionStartDateIsNull() {
            addCriterion("promotion_start_date is null");
            return (Criteria) this;
        }

        public Criteria andPromotionStartDateIsNotNull() {
            addCriterion("promotion_start_date is not null");
            return (Criteria) this;
        }

        public Criteria andPromotionStartDateEqualTo(String value) {
            addCriterion("promotion_start_date =", value, "promotionStartDate");
            return (Criteria) this;
        }

        public Criteria andPromotionStartDateNotEqualTo(String value) {
            addCriterion("promotion_start_date <>", value, "promotionStartDate");
            return (Criteria) this;
        }

        public Criteria andPromotionStartDateGreaterThan(String value) {
            addCriterion("promotion_start_date >", value, "promotionStartDate");
            return (Criteria) this;
        }

        public Criteria andPromotionStartDateGreaterThanOrEqualTo(String value) {
            addCriterion("promotion_start_date >=", value, "promotionStartDate");
            return (Criteria) this;
        }

        public Criteria andPromotionStartDateLessThan(String value) {
            addCriterion("promotion_start_date <", value, "promotionStartDate");
            return (Criteria) this;
        }

        public Criteria andPromotionStartDateLessThanOrEqualTo(String value) {
            addCriterion("promotion_start_date <=", value, "promotionStartDate");
            return (Criteria) this;
        }

        public Criteria andPromotionStartDateLike(String value) {
            addCriterion("promotion_start_date like", value, "promotionStartDate");
            return (Criteria) this;
        }

        public Criteria andPromotionStartDateNotLike(String value) {
            addCriterion("promotion_start_date not like", value, "promotionStartDate");
            return (Criteria) this;
        }

        public Criteria andPromotionStartDateIn(List<String> values) {
            addCriterion("promotion_start_date in", values, "promotionStartDate");
            return (Criteria) this;
        }

        public Criteria andPromotionStartDateNotIn(List<String> values) {
            addCriterion("promotion_start_date not in", values, "promotionStartDate");
            return (Criteria) this;
        }

        public Criteria andPromotionStartDateBetween(String value1, String value2) {
            addCriterion("promotion_start_date between", value1, value2, "promotionStartDate");
            return (Criteria) this;
        }

        public Criteria andPromotionStartDateNotBetween(String value1, String value2) {
            addCriterion("promotion_start_date not between", value1, value2, "promotionStartDate");
            return (Criteria) this;
        }

        public Criteria andPromotionEndDateIsNull() {
            addCriterion("promotion_end_date is null");
            return (Criteria) this;
        }

        public Criteria andPromotionEndDateIsNotNull() {
            addCriterion("promotion_end_date is not null");
            return (Criteria) this;
        }

        public Criteria andPromotionEndDateEqualTo(String value) {
            addCriterion("promotion_end_date =", value, "promotionEndDate");
            return (Criteria) this;
        }

        public Criteria andPromotionEndDateNotEqualTo(String value) {
            addCriterion("promotion_end_date <>", value, "promotionEndDate");
            return (Criteria) this;
        }

        public Criteria andPromotionEndDateGreaterThan(String value) {
            addCriterion("promotion_end_date >", value, "promotionEndDate");
            return (Criteria) this;
        }

        public Criteria andPromotionEndDateGreaterThanOrEqualTo(String value) {
            addCriterion("promotion_end_date >=", value, "promotionEndDate");
            return (Criteria) this;
        }

        public Criteria andPromotionEndDateLessThan(String value) {
            addCriterion("promotion_end_date <", value, "promotionEndDate");
            return (Criteria) this;
        }

        public Criteria andPromotionEndDateLessThanOrEqualTo(String value) {
            addCriterion("promotion_end_date <=", value, "promotionEndDate");
            return (Criteria) this;
        }

        public Criteria andPromotionEndDateLike(String value) {
            addCriterion("promotion_end_date like", value, "promotionEndDate");
            return (Criteria) this;
        }

        public Criteria andPromotionEndDateNotLike(String value) {
            addCriterion("promotion_end_date not like", value, "promotionEndDate");
            return (Criteria) this;
        }

        public Criteria andPromotionEndDateIn(List<String> values) {
            addCriterion("promotion_end_date in", values, "promotionEndDate");
            return (Criteria) this;
        }

        public Criteria andPromotionEndDateNotIn(List<String> values) {
            addCriterion("promotion_end_date not in", values, "promotionEndDate");
            return (Criteria) this;
        }

        public Criteria andPromotionEndDateBetween(String value1, String value2) {
            addCriterion("promotion_end_date between", value1, value2, "promotionEndDate");
            return (Criteria) this;
        }

        public Criteria andPromotionEndDateNotBetween(String value1, String value2) {
            addCriterion("promotion_end_date not between", value1, value2, "promotionEndDate");
            return (Criteria) this;
        }

        public Criteria andDealSuggestIsNull() {
            addCriterion("deal_suggest is null");
            return (Criteria) this;
        }

        public Criteria andDealSuggestIsNotNull() {
            addCriterion("deal_suggest is not null");
            return (Criteria) this;
        }

        public Criteria andDealSuggestEqualTo(Byte value) {
            addCriterion("deal_suggest =", value, "dealSuggest");
            return (Criteria) this;
        }

        public Criteria andDealSuggestNotEqualTo(Byte value) {
            addCriterion("deal_suggest <>", value, "dealSuggest");
            return (Criteria) this;
        }

        public Criteria andDealSuggestGreaterThan(Byte value) {
            addCriterion("deal_suggest >", value, "dealSuggest");
            return (Criteria) this;
        }

        public Criteria andDealSuggestGreaterThanOrEqualTo(Byte value) {
            addCriterion("deal_suggest >=", value, "dealSuggest");
            return (Criteria) this;
        }

        public Criteria andDealSuggestLessThan(Byte value) {
            addCriterion("deal_suggest <", value, "dealSuggest");
            return (Criteria) this;
        }

        public Criteria andDealSuggestLessThanOrEqualTo(Byte value) {
            addCriterion("deal_suggest <=", value, "dealSuggest");
            return (Criteria) this;
        }

        public Criteria andDealSuggestIn(List<Byte> values) {
            addCriterion("deal_suggest in", values, "dealSuggest");
            return (Criteria) this;
        }

        public Criteria andDealSuggestNotIn(List<Byte> values) {
            addCriterion("deal_suggest not in", values, "dealSuggest");
            return (Criteria) this;
        }

        public Criteria andDealSuggestBetween(Byte value1, Byte value2) {
            addCriterion("deal_suggest between", value1, value2, "dealSuggest");
            return (Criteria) this;
        }

        public Criteria andDealSuggestNotBetween(Byte value1, Byte value2) {
            addCriterion("deal_suggest not between", value1, value2, "dealSuggest");
            return (Criteria) this;
        }

        public Criteria andStoreAttrIsNull() {
            addCriterion("store_attr is null");
            return (Criteria) this;
        }

        public Criteria andStoreAttrIsNotNull() {
            addCriterion("store_attr is not null");
            return (Criteria) this;
        }

        public Criteria andStoreAttrEqualTo(Byte value) {
            addCriterion("store_attr =", value, "storeAttr");
            return (Criteria) this;
        }

        public Criteria andStoreAttrNotEqualTo(Byte value) {
            addCriterion("store_attr <>", value, "storeAttr");
            return (Criteria) this;
        }

        public Criteria andStoreAttrGreaterThan(Byte value) {
            addCriterion("store_attr >", value, "storeAttr");
            return (Criteria) this;
        }

        public Criteria andStoreAttrGreaterThanOrEqualTo(Byte value) {
            addCriterion("store_attr >=", value, "storeAttr");
            return (Criteria) this;
        }

        public Criteria andStoreAttrLessThan(Byte value) {
            addCriterion("store_attr <", value, "storeAttr");
            return (Criteria) this;
        }

        public Criteria andStoreAttrLessThanOrEqualTo(Byte value) {
            addCriterion("store_attr <=", value, "storeAttr");
            return (Criteria) this;
        }

        public Criteria andStoreAttrIn(List<Byte> values) {
            addCriterion("store_attr in", values, "storeAttr");
            return (Criteria) this;
        }

        public Criteria andStoreAttrNotIn(List<Byte> values) {
            addCriterion("store_attr not in", values, "storeAttr");
            return (Criteria) this;
        }

        public Criteria andStoreAttrBetween(Byte value1, Byte value2) {
            addCriterion("store_attr between", value1, value2, "storeAttr");
            return (Criteria) this;
        }

        public Criteria andStoreAttrNotBetween(Byte value1, Byte value2) {
            addCriterion("store_attr not between", value1, value2, "storeAttr");
            return (Criteria) this;
        }

        public Criteria andThreeDaysSalesIsNull() {
            addCriterion("three_days_sales is null");
            return (Criteria) this;
        }

        public Criteria andThreeDaysSalesIsNotNull() {
            addCriterion("three_days_sales is not null");
            return (Criteria) this;
        }

        public Criteria andThreeDaysSalesEqualTo(BigDecimal value) {
            addCriterion("three_days_sales =", value, "threeDaysSales");
            return (Criteria) this;
        }

        public Criteria andThreeDaysSalesNotEqualTo(BigDecimal value) {
            addCriterion("three_days_sales <>", value, "threeDaysSales");
            return (Criteria) this;
        }

        public Criteria andThreeDaysSalesGreaterThan(BigDecimal value) {
            addCriterion("three_days_sales >", value, "threeDaysSales");
            return (Criteria) this;
        }

        public Criteria andThreeDaysSalesGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("three_days_sales >=", value, "threeDaysSales");
            return (Criteria) this;
        }

        public Criteria andThreeDaysSalesLessThan(BigDecimal value) {
            addCriterion("three_days_sales <", value, "threeDaysSales");
            return (Criteria) this;
        }

        public Criteria andThreeDaysSalesLessThanOrEqualTo(BigDecimal value) {
            addCriterion("three_days_sales <=", value, "threeDaysSales");
            return (Criteria) this;
        }

        public Criteria andThreeDaysSalesIn(List<BigDecimal> values) {
            addCriterion("three_days_sales in", values, "threeDaysSales");
            return (Criteria) this;
        }

        public Criteria andThreeDaysSalesNotIn(List<BigDecimal> values) {
            addCriterion("three_days_sales not in", values, "threeDaysSales");
            return (Criteria) this;
        }

        public Criteria andThreeDaysSalesBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("three_days_sales between", value1, value2, "threeDaysSales");
            return (Criteria) this;
        }

        public Criteria andThreeDaysSalesNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("three_days_sales not between", value1, value2, "threeDaysSales");
            return (Criteria) this;
        }

        public Criteria andDeliverycycleCodeIsNull() {
            addCriterion("deliverycycle_code is null");
            return (Criteria) this;
        }

        public Criteria andDeliverycycleCodeIsNotNull() {
            addCriterion("deliverycycle_code is not null");
            return (Criteria) this;
        }

        public Criteria andDeliverycycleCodeEqualTo(String value) {
            addCriterion("deliverycycle_code =", value, "deliverycycleCode");
            return (Criteria) this;
        }

        public Criteria andDeliverycycleCodeNotEqualTo(String value) {
            addCriterion("deliverycycle_code <>", value, "deliverycycleCode");
            return (Criteria) this;
        }

        public Criteria andDeliverycycleCodeGreaterThan(String value) {
            addCriterion("deliverycycle_code >", value, "deliverycycleCode");
            return (Criteria) this;
        }

        public Criteria andDeliverycycleCodeGreaterThanOrEqualTo(String value) {
            addCriterion("deliverycycle_code >=", value, "deliverycycleCode");
            return (Criteria) this;
        }

        public Criteria andDeliverycycleCodeLessThan(String value) {
            addCriterion("deliverycycle_code <", value, "deliverycycleCode");
            return (Criteria) this;
        }

        public Criteria andDeliverycycleCodeLessThanOrEqualTo(String value) {
            addCriterion("deliverycycle_code <=", value, "deliverycycleCode");
            return (Criteria) this;
        }

        public Criteria andDeliverycycleCodeLike(String value) {
            addCriterion("deliverycycle_code like", value, "deliverycycleCode");
            return (Criteria) this;
        }

        public Criteria andDeliverycycleCodeNotLike(String value) {
            addCriterion("deliverycycle_code not like", value, "deliverycycleCode");
            return (Criteria) this;
        }

        public Criteria andDeliverycycleCodeIn(List<String> values) {
            addCriterion("deliverycycle_code in", values, "deliverycycleCode");
            return (Criteria) this;
        }

        public Criteria andDeliverycycleCodeNotIn(List<String> values) {
            addCriterion("deliverycycle_code not in", values, "deliverycycleCode");
            return (Criteria) this;
        }

        public Criteria andDeliverycycleCodeBetween(String value1, String value2) {
            addCriterion("deliverycycle_code between", value1, value2, "deliverycycleCode");
            return (Criteria) this;
        }

        public Criteria andDeliverycycleCodeNotBetween(String value1, String value2) {
            addCriterion("deliverycycle_code not between", value1, value2, "deliverycycleCode");
            return (Criteria) this;
        }

        public Criteria andNewProductIsNull() {
            addCriterion("new_product is null");
            return (Criteria) this;
        }

        public Criteria andNewProductIsNotNull() {
            addCriterion("new_product is not null");
            return (Criteria) this;
        }

        public Criteria andNewProductEqualTo(Byte value) {
            addCriterion("new_product =", value, "newProduct");
            return (Criteria) this;
        }

        public Criteria andNewProductNotEqualTo(Byte value) {
            addCriterion("new_product <>", value, "newProduct");
            return (Criteria) this;
        }

        public Criteria andNewProductGreaterThan(Byte value) {
            addCriterion("new_product >", value, "newProduct");
            return (Criteria) this;
        }

        public Criteria andNewProductGreaterThanOrEqualTo(Byte value) {
            addCriterion("new_product >=", value, "newProduct");
            return (Criteria) this;
        }

        public Criteria andNewProductLessThan(Byte value) {
            addCriterion("new_product <", value, "newProduct");
            return (Criteria) this;
        }

        public Criteria andNewProductLessThanOrEqualTo(Byte value) {
            addCriterion("new_product <=", value, "newProduct");
            return (Criteria) this;
        }

        public Criteria andNewProductIn(List<Byte> values) {
            addCriterion("new_product in", values, "newProduct");
            return (Criteria) this;
        }

        public Criteria andNewProductNotIn(List<Byte> values) {
            addCriterion("new_product not in", values, "newProduct");
            return (Criteria) this;
        }

        public Criteria andNewProductBetween(Byte value1, Byte value2) {
            addCriterion("new_product between", value1, value2, "newProduct");
            return (Criteria) this;
        }

        public Criteria andNewProductNotBetween(Byte value1, Byte value2) {
            addCriterion("new_product not between", value1, value2, "newProduct");
            return (Criteria) this;
        }

        public Criteria andGoodsCutTypeIsNull() {
            addCriterion("goods_cut_type is null");
            return (Criteria) this;
        }

        public Criteria andGoodsCutTypeIsNotNull() {
            addCriterion("goods_cut_type is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsCutTypeEqualTo(Integer value) {
            addCriterion("goods_cut_type =", value, "goodsCutType");
            return (Criteria) this;
        }

        public Criteria andGoodsCutTypeNotEqualTo(Integer value) {
            addCriterion("goods_cut_type <>", value, "goodsCutType");
            return (Criteria) this;
        }

        public Criteria andGoodsCutTypeGreaterThan(Integer value) {
            addCriterion("goods_cut_type >", value, "goodsCutType");
            return (Criteria) this;
        }

        public Criteria andGoodsCutTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("goods_cut_type >=", value, "goodsCutType");
            return (Criteria) this;
        }

        public Criteria andGoodsCutTypeLessThan(Integer value) {
            addCriterion("goods_cut_type <", value, "goodsCutType");
            return (Criteria) this;
        }

        public Criteria andGoodsCutTypeLessThanOrEqualTo(Integer value) {
            addCriterion("goods_cut_type <=", value, "goodsCutType");
            return (Criteria) this;
        }

        public Criteria andGoodsCutTypeIn(List<Integer> values) {
            addCriterion("goods_cut_type in", values, "goodsCutType");
            return (Criteria) this;
        }

        public Criteria andGoodsCutTypeNotIn(List<Integer> values) {
            addCriterion("goods_cut_type not in", values, "goodsCutType");
            return (Criteria) this;
        }

        public Criteria andGoodsCutTypeBetween(Integer value1, Integer value2) {
            addCriterion("goods_cut_type between", value1, value2, "goodsCutType");
            return (Criteria) this;
        }

        public Criteria andGoodsCutTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("goods_cut_type not between", value1, value2, "goodsCutType");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIsNull() {
            addCriterion("gmt_update is null");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIsNotNull() {
            addCriterion("gmt_update is not null");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateEqualTo(Date value) {
            addCriterion("gmt_update =", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotEqualTo(Date value) {
            addCriterion("gmt_update <>", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateGreaterThan(Date value) {
            addCriterion("gmt_update >", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_update >=", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateLessThan(Date value) {
            addCriterion("gmt_update <", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_update <=", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIn(List<Date> values) {
            addCriterion("gmt_update in", values, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotIn(List<Date> values) {
            addCriterion("gmt_update not in", values, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateBetween(Date value1, Date value2) {
            addCriterion("gmt_update between", value1, value2, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_update not between", value1, value2, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andReasonIsNull() {
            addCriterion("reason is null");
            return (Criteria) this;
        }

        public Criteria andReasonIsNotNull() {
            addCriterion("reason is not null");
            return (Criteria) this;
        }

        public Criteria andReasonEqualTo(String value) {
            addCriterion("reason =", value, "reason");
            return (Criteria) this;
        }

        public Criteria andReasonNotEqualTo(String value) {
            addCriterion("reason <>", value, "reason");
            return (Criteria) this;
        }

        public Criteria andReasonGreaterThan(String value) {
            addCriterion("reason >", value, "reason");
            return (Criteria) this;
        }

        public Criteria andReasonGreaterThanOrEqualTo(String value) {
            addCriterion("reason >=", value, "reason");
            return (Criteria) this;
        }

        public Criteria andReasonLessThan(String value) {
            addCriterion("reason <", value, "reason");
            return (Criteria) this;
        }

        public Criteria andReasonLessThanOrEqualTo(String value) {
            addCriterion("reason <=", value, "reason");
            return (Criteria) this;
        }

        public Criteria andReasonLike(String value) {
            addCriterion("reason like", value, "reason");
            return (Criteria) this;
        }

        public Criteria andReasonNotLike(String value) {
            addCriterion("reason not like", value, "reason");
            return (Criteria) this;
        }

        public Criteria andReasonIn(List<String> values) {
            addCriterion("reason in", values, "reason");
            return (Criteria) this;
        }

        public Criteria andReasonNotIn(List<String> values) {
            addCriterion("reason not in", values, "reason");
            return (Criteria) this;
        }

        public Criteria andReasonBetween(String value1, String value2) {
            addCriterion("reason between", value1, value2, "reason");
            return (Criteria) this;
        }

        public Criteria andReasonNotBetween(String value1, String value2) {
            addCriterion("reason not between", value1, value2, "reason");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}