package com.cowell.iscm.entityTidb;

import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class IscmNodePlanTimeExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public IscmNodePlanTimeExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        protected void addCriterionForJDBCTime(String condition, Date value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Time(value.getTime()), property);
        }

        protected void addCriterionForJDBCTime(String condition, List<Date> values, String property) {
            if (values == null || values.size() == 0) {
                throw new RuntimeException("Value list for " + property + " cannot be null or empty");
            }
            List<java.sql.Time> timeList = new ArrayList<java.sql.Time>();
            Iterator<Date> iter = values.iterator();
            while (iter.hasNext()) {
                timeList.add(new java.sql.Time(iter.next().getTime()));
            }
            addCriterion(condition, timeList, property);
        }

        protected void addCriterionForJDBCTime(String condition, Date value1, Date value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Time(value1.getTime()), new java.sql.Time(value2.getTime()), property);
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIsNull() {
            addCriterion("company_code is null");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIsNotNull() {
            addCriterion("company_code is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeEqualTo(String value) {
            addCriterion("company_code =", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotEqualTo(String value) {
            addCriterion("company_code <>", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeGreaterThan(String value) {
            addCriterion("company_code >", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeGreaterThanOrEqualTo(String value) {
            addCriterion("company_code >=", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLessThan(String value) {
            addCriterion("company_code <", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLessThanOrEqualTo(String value) {
            addCriterion("company_code <=", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLike(String value) {
            addCriterion("company_code like", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotLike(String value) {
            addCriterion("company_code not like", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIn(List<String> values) {
            addCriterion("company_code in", values, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotIn(List<String> values) {
            addCriterion("company_code not in", values, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeBetween(String value1, String value2) {
            addCriterion("company_code between", value1, value2, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotBetween(String value1, String value2) {
            addCriterion("company_code not between", value1, value2, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyNameIsNull() {
            addCriterion("company_name is null");
            return (Criteria) this;
        }

        public Criteria andCompanyNameIsNotNull() {
            addCriterion("company_name is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyNameEqualTo(String value) {
            addCriterion("company_name =", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotEqualTo(String value) {
            addCriterion("company_name <>", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameGreaterThan(String value) {
            addCriterion("company_name >", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameGreaterThanOrEqualTo(String value) {
            addCriterion("company_name >=", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameLessThan(String value) {
            addCriterion("company_name <", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameLessThanOrEqualTo(String value) {
            addCriterion("company_name <=", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameLike(String value) {
            addCriterion("company_name like", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotLike(String value) {
            addCriterion("company_name not like", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameIn(List<String> values) {
            addCriterion("company_name in", values, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotIn(List<String> values) {
            addCriterion("company_name not in", values, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameBetween(String value1, String value2) {
            addCriterion("company_name between", value1, value2, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotBetween(String value1, String value2) {
            addCriterion("company_name not between", value1, value2, "companyName");
            return (Criteria) this;
        }

        public Criteria andPosGenorderTimeIsNull() {
            addCriterion("pos_genorder_time is null");
            return (Criteria) this;
        }

        public Criteria andPosGenorderTimeIsNotNull() {
            addCriterion("pos_genorder_time is not null");
            return (Criteria) this;
        }

        public Criteria andPosGenorderTimeEqualTo(Date value) {
            addCriterionForJDBCTime("pos_genorder_time =", value, "posGenorderTime");
            return (Criteria) this;
        }

        public Criteria andPosGenorderTimeNotEqualTo(Date value) {
            addCriterionForJDBCTime("pos_genorder_time <>", value, "posGenorderTime");
            return (Criteria) this;
        }

        public Criteria andPosGenorderTimeGreaterThan(Date value) {
            addCriterionForJDBCTime("pos_genorder_time >", value, "posGenorderTime");
            return (Criteria) this;
        }

        public Criteria andPosGenorderTimeGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCTime("pos_genorder_time >=", value, "posGenorderTime");
            return (Criteria) this;
        }

        public Criteria andPosGenorderTimeLessThan(Date value) {
            addCriterionForJDBCTime("pos_genorder_time <", value, "posGenorderTime");
            return (Criteria) this;
        }

        public Criteria andPosGenorderTimeLessThanOrEqualTo(Date value) {
            addCriterionForJDBCTime("pos_genorder_time <=", value, "posGenorderTime");
            return (Criteria) this;
        }

        public Criteria andPosGenorderTimeIn(List<Date> values) {
            addCriterionForJDBCTime("pos_genorder_time in", values, "posGenorderTime");
            return (Criteria) this;
        }

        public Criteria andPosGenorderTimeNotIn(List<Date> values) {
            addCriterionForJDBCTime("pos_genorder_time not in", values, "posGenorderTime");
            return (Criteria) this;
        }

        public Criteria andPosGenorderTimeBetween(Date value1, Date value2) {
            addCriterionForJDBCTime("pos_genorder_time between", value1, value2, "posGenorderTime");
            return (Criteria) this;
        }

        public Criteria andPosGenorderTimeNotBetween(Date value1, Date value2) {
            addCriterionForJDBCTime("pos_genorder_time not between", value1, value2, "posGenorderTime");
            return (Criteria) this;
        }

        public Criteria andPosMergeorderTimeIsNull() {
            addCriterion("pos_mergeorder_time is null");
            return (Criteria) this;
        }

        public Criteria andPosMergeorderTimeIsNotNull() {
            addCriterion("pos_mergeorder_time is not null");
            return (Criteria) this;
        }

        public Criteria andPosMergeorderTimeEqualTo(Date value) {
            addCriterionForJDBCTime("pos_mergeorder_time =", value, "posMergeorderTime");
            return (Criteria) this;
        }

        public Criteria andPosMergeorderTimeNotEqualTo(Date value) {
            addCriterionForJDBCTime("pos_mergeorder_time <>", value, "posMergeorderTime");
            return (Criteria) this;
        }

        public Criteria andPosMergeorderTimeGreaterThan(Date value) {
            addCriterionForJDBCTime("pos_mergeorder_time >", value, "posMergeorderTime");
            return (Criteria) this;
        }

        public Criteria andPosMergeorderTimeGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCTime("pos_mergeorder_time >=", value, "posMergeorderTime");
            return (Criteria) this;
        }

        public Criteria andPosMergeorderTimeLessThan(Date value) {
            addCriterionForJDBCTime("pos_mergeorder_time <", value, "posMergeorderTime");
            return (Criteria) this;
        }

        public Criteria andPosMergeorderTimeLessThanOrEqualTo(Date value) {
            addCriterionForJDBCTime("pos_mergeorder_time <=", value, "posMergeorderTime");
            return (Criteria) this;
        }

        public Criteria andPosMergeorderTimeIn(List<Date> values) {
            addCriterionForJDBCTime("pos_mergeorder_time in", values, "posMergeorderTime");
            return (Criteria) this;
        }

        public Criteria andPosMergeorderTimeNotIn(List<Date> values) {
            addCriterionForJDBCTime("pos_mergeorder_time not in", values, "posMergeorderTime");
            return (Criteria) this;
        }

        public Criteria andPosMergeorderTimeBetween(Date value1, Date value2) {
            addCriterionForJDBCTime("pos_mergeorder_time between", value1, value2, "posMergeorderTime");
            return (Criteria) this;
        }

        public Criteria andPosMergeorderTimeNotBetween(Date value1, Date value2) {
            addCriterionForJDBCTime("pos_mergeorder_time not between", value1, value2, "posMergeorderTime");
            return (Criteria) this;
        }

        public Criteria andSapSapPurchaseApproveTimeIsNull() {
            addCriterion("sap_sap_purchase_approve_time is null");
            return (Criteria) this;
        }

        public Criteria andSapSapPurchaseApproveTimeIsNotNull() {
            addCriterion("sap_sap_purchase_approve_time is not null");
            return (Criteria) this;
        }

        public Criteria andSapSapPurchaseApproveTimeEqualTo(Date value) {
            addCriterionForJDBCTime("sap_sap_purchase_approve_time =", value, "sapSapPurchaseApproveTime");
            return (Criteria) this;
        }

        public Criteria andSapSapPurchaseApproveTimeNotEqualTo(Date value) {
            addCriterionForJDBCTime("sap_sap_purchase_approve_time <>", value, "sapSapPurchaseApproveTime");
            return (Criteria) this;
        }

        public Criteria andSapSapPurchaseApproveTimeGreaterThan(Date value) {
            addCriterionForJDBCTime("sap_sap_purchase_approve_time >", value, "sapSapPurchaseApproveTime");
            return (Criteria) this;
        }

        public Criteria andSapSapPurchaseApproveTimeGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCTime("sap_sap_purchase_approve_time >=", value, "sapSapPurchaseApproveTime");
            return (Criteria) this;
        }

        public Criteria andSapSapPurchaseApproveTimeLessThan(Date value) {
            addCriterionForJDBCTime("sap_sap_purchase_approve_time <", value, "sapSapPurchaseApproveTime");
            return (Criteria) this;
        }

        public Criteria andSapSapPurchaseApproveTimeLessThanOrEqualTo(Date value) {
            addCriterionForJDBCTime("sap_sap_purchase_approve_time <=", value, "sapSapPurchaseApproveTime");
            return (Criteria) this;
        }

        public Criteria andSapSapPurchaseApproveTimeIn(List<Date> values) {
            addCriterionForJDBCTime("sap_sap_purchase_approve_time in", values, "sapSapPurchaseApproveTime");
            return (Criteria) this;
        }

        public Criteria andSapSapPurchaseApproveTimeNotIn(List<Date> values) {
            addCriterionForJDBCTime("sap_sap_purchase_approve_time not in", values, "sapSapPurchaseApproveTime");
            return (Criteria) this;
        }

        public Criteria andSapSapPurchaseApproveTimeBetween(Date value1, Date value2) {
            addCriterionForJDBCTime("sap_sap_purchase_approve_time between", value1, value2, "sapSapPurchaseApproveTime");
            return (Criteria) this;
        }

        public Criteria andSapSapPurchaseApproveTimeNotBetween(Date value1, Date value2) {
            addCriterionForJDBCTime("sap_sap_purchase_approve_time not between", value1, value2, "sapSapPurchaseApproveTime");
            return (Criteria) this;
        }

        public Criteria andBdpDistributeTimeIsNull() {
            addCriterion("bdp_distribute_time is null");
            return (Criteria) this;
        }

        public Criteria andBdpDistributeTimeIsNotNull() {
            addCriterion("bdp_distribute_time is not null");
            return (Criteria) this;
        }

        public Criteria andBdpDistributeTimeEqualTo(Date value) {
            addCriterionForJDBCTime("bdp_distribute_time =", value, "bdpDistributeTime");
            return (Criteria) this;
        }

        public Criteria andBdpDistributeTimeNotEqualTo(Date value) {
            addCriterionForJDBCTime("bdp_distribute_time <>", value, "bdpDistributeTime");
            return (Criteria) this;
        }

        public Criteria andBdpDistributeTimeGreaterThan(Date value) {
            addCriterionForJDBCTime("bdp_distribute_time >", value, "bdpDistributeTime");
            return (Criteria) this;
        }

        public Criteria andBdpDistributeTimeGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCTime("bdp_distribute_time >=", value, "bdpDistributeTime");
            return (Criteria) this;
        }

        public Criteria andBdpDistributeTimeLessThan(Date value) {
            addCriterionForJDBCTime("bdp_distribute_time <", value, "bdpDistributeTime");
            return (Criteria) this;
        }

        public Criteria andBdpDistributeTimeLessThanOrEqualTo(Date value) {
            addCriterionForJDBCTime("bdp_distribute_time <=", value, "bdpDistributeTime");
            return (Criteria) this;
        }

        public Criteria andBdpDistributeTimeIn(List<Date> values) {
            addCriterionForJDBCTime("bdp_distribute_time in", values, "bdpDistributeTime");
            return (Criteria) this;
        }

        public Criteria andBdpDistributeTimeNotIn(List<Date> values) {
            addCriterionForJDBCTime("bdp_distribute_time not in", values, "bdpDistributeTime");
            return (Criteria) this;
        }

        public Criteria andBdpDistributeTimeBetween(Date value1, Date value2) {
            addCriterionForJDBCTime("bdp_distribute_time between", value1, value2, "bdpDistributeTime");
            return (Criteria) this;
        }

        public Criteria andBdpDistributeTimeNotBetween(Date value1, Date value2) {
            addCriterionForJDBCTime("bdp_distribute_time not between", value1, value2, "bdpDistributeTime");
            return (Criteria) this;
        }

        public Criteria andSapSapTransferTimeIsNull() {
            addCriterion("sap_sap_transfer_time is null");
            return (Criteria) this;
        }

        public Criteria andSapSapTransferTimeIsNotNull() {
            addCriterion("sap_sap_transfer_time is not null");
            return (Criteria) this;
        }

        public Criteria andSapSapTransferTimeEqualTo(Date value) {
            addCriterionForJDBCTime("sap_sap_transfer_time =", value, "sapSapTransferTime");
            return (Criteria) this;
        }

        public Criteria andSapSapTransferTimeNotEqualTo(Date value) {
            addCriterionForJDBCTime("sap_sap_transfer_time <>", value, "sapSapTransferTime");
            return (Criteria) this;
        }

        public Criteria andSapSapTransferTimeGreaterThan(Date value) {
            addCriterionForJDBCTime("sap_sap_transfer_time >", value, "sapSapTransferTime");
            return (Criteria) this;
        }

        public Criteria andSapSapTransferTimeGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCTime("sap_sap_transfer_time >=", value, "sapSapTransferTime");
            return (Criteria) this;
        }

        public Criteria andSapSapTransferTimeLessThan(Date value) {
            addCriterionForJDBCTime("sap_sap_transfer_time <", value, "sapSapTransferTime");
            return (Criteria) this;
        }

        public Criteria andSapSapTransferTimeLessThanOrEqualTo(Date value) {
            addCriterionForJDBCTime("sap_sap_transfer_time <=", value, "sapSapTransferTime");
            return (Criteria) this;
        }

        public Criteria andSapSapTransferTimeIn(List<Date> values) {
            addCriterionForJDBCTime("sap_sap_transfer_time in", values, "sapSapTransferTime");
            return (Criteria) this;
        }

        public Criteria andSapSapTransferTimeNotIn(List<Date> values) {
            addCriterionForJDBCTime("sap_sap_transfer_time not in", values, "sapSapTransferTime");
            return (Criteria) this;
        }

        public Criteria andSapSapTransferTimeBetween(Date value1, Date value2) {
            addCriterionForJDBCTime("sap_sap_transfer_time between", value1, value2, "sapSapTransferTime");
            return (Criteria) this;
        }

        public Criteria andSapSapTransferTimeNotBetween(Date value1, Date value2) {
            addCriterionForJDBCTime("sap_sap_transfer_time not between", value1, value2, "sapSapTransferTime");
            return (Criteria) this;
        }

        public Criteria andDtIsNull() {
            addCriterion("dt is null");
            return (Criteria) this;
        }

        public Criteria andDtIsNotNull() {
            addCriterion("dt is not null");
            return (Criteria) this;
        }

        public Criteria andDtEqualTo(String value) {
            addCriterion("dt =", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtNotEqualTo(String value) {
            addCriterion("dt <>", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtGreaterThan(String value) {
            addCriterion("dt >", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtGreaterThanOrEqualTo(String value) {
            addCriterion("dt >=", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtLessThan(String value) {
            addCriterion("dt <", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtLessThanOrEqualTo(String value) {
            addCriterion("dt <=", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtLike(String value) {
            addCriterion("dt like", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtNotLike(String value) {
            addCriterion("dt not like", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtIn(List<String> values) {
            addCriterion("dt in", values, "dt");
            return (Criteria) this;
        }

        public Criteria andDtNotIn(List<String> values) {
            addCriterion("dt not in", values, "dt");
            return (Criteria) this;
        }

        public Criteria andDtBetween(String value1, String value2) {
            addCriterion("dt between", value1, value2, "dt");
            return (Criteria) this;
        }

        public Criteria andDtNotBetween(String value1, String value2) {
            addCriterion("dt not between", value1, value2, "dt");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}