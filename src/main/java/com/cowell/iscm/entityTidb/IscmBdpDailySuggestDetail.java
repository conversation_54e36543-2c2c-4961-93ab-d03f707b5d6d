package com.cowell.iscm.entityTidb;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * iscm_bdp_daily_suggest_detail
 * <AUTHOR>
public class IscmBdpDailySuggestDetail implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 平台名称
     */
    private String platformName;

    /**
     * 公司编码
     */
    private String companyCode;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 门店编码
     */
    private String storeCode;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 商品编码
     */
    private String goodsNo;

    /**
     * bdp日销建议量
     */
    private BigDecimal bdpInterfaceQuantity;

    /**
     * pos接口表日销建议量
     */
    private BigDecimal posInterfaceQuantity;

    /**
     * pos正式表日销建议量
     */
    private BigDecimal posBusinessQuantity;

    /**
     * 执行标识(0未处理,1成功,2失败,3接口未获取)
     */
    private Byte execStatus;

    /**
     * 差异原因
     */
    private String diffMsg;

    /**
     * 时间分区 yyyyMMdd
     */
    private String dt;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getPlatformName() {
        return platformName;
    }

    public void setPlatformName(String platformName) {
        this.platformName = platformName;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getStoreCode() {
        return storeCode;
    }

    public void setStoreCode(String storeCode) {
        this.storeCode = storeCode;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getGoodsNo() {
        return goodsNo;
    }

    public void setGoodsNo(String goodsNo) {
        this.goodsNo = goodsNo;
    }

    public BigDecimal getBdpInterfaceQuantity() {
        return bdpInterfaceQuantity;
    }

    public void setBdpInterfaceQuantity(BigDecimal bdpInterfaceQuantity) {
        this.bdpInterfaceQuantity = bdpInterfaceQuantity;
    }

    public BigDecimal getPosInterfaceQuantity() {
        return posInterfaceQuantity;
    }

    public void setPosInterfaceQuantity(BigDecimal posInterfaceQuantity) {
        this.posInterfaceQuantity = posInterfaceQuantity;
    }

    public BigDecimal getPosBusinessQuantity() {
        return posBusinessQuantity;
    }

    public void setPosBusinessQuantity(BigDecimal posBusinessQuantity) {
        this.posBusinessQuantity = posBusinessQuantity;
    }

    public Byte getExecStatus() {
        return execStatus;
    }

    public void setExecStatus(Byte execStatus) {
        this.execStatus = execStatus;
    }

    public String getDiffMsg() {
        return diffMsg;
    }

    public void setDiffMsg(String diffMsg) {
        this.diffMsg = diffMsg;
    }

    public String getDt() {
        return dt;
    }

    public void setDt(String dt) {
        this.dt = dt;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        IscmBdpDailySuggestDetail other = (IscmBdpDailySuggestDetail) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getPlatformName() == null ? other.getPlatformName() == null : this.getPlatformName().equals(other.getPlatformName()))
            && (this.getCompanyCode() == null ? other.getCompanyCode() == null : this.getCompanyCode().equals(other.getCompanyCode()))
            && (this.getCompanyName() == null ? other.getCompanyName() == null : this.getCompanyName().equals(other.getCompanyName()))
            && (this.getStoreCode() == null ? other.getStoreCode() == null : this.getStoreCode().equals(other.getStoreCode()))
            && (this.getStoreName() == null ? other.getStoreName() == null : this.getStoreName().equals(other.getStoreName()))
            && (this.getGoodsNo() == null ? other.getGoodsNo() == null : this.getGoodsNo().equals(other.getGoodsNo()))
            && (this.getBdpInterfaceQuantity() == null ? other.getBdpInterfaceQuantity() == null : this.getBdpInterfaceQuantity().equals(other.getBdpInterfaceQuantity()))
            && (this.getPosInterfaceQuantity() == null ? other.getPosInterfaceQuantity() == null : this.getPosInterfaceQuantity().equals(other.getPosInterfaceQuantity()))
            && (this.getPosBusinessQuantity() == null ? other.getPosBusinessQuantity() == null : this.getPosBusinessQuantity().equals(other.getPosBusinessQuantity()))
            && (this.getExecStatus() == null ? other.getExecStatus() == null : this.getExecStatus().equals(other.getExecStatus()))
            && (this.getDiffMsg() == null ? other.getDiffMsg() == null : this.getDiffMsg().equals(other.getDiffMsg()))
            && (this.getDt() == null ? other.getDt() == null : this.getDt().equals(other.getDt()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getPlatformName() == null) ? 0 : getPlatformName().hashCode());
        result = prime * result + ((getCompanyCode() == null) ? 0 : getCompanyCode().hashCode());
        result = prime * result + ((getCompanyName() == null) ? 0 : getCompanyName().hashCode());
        result = prime * result + ((getStoreCode() == null) ? 0 : getStoreCode().hashCode());
        result = prime * result + ((getStoreName() == null) ? 0 : getStoreName().hashCode());
        result = prime * result + ((getGoodsNo() == null) ? 0 : getGoodsNo().hashCode());
        result = prime * result + ((getBdpInterfaceQuantity() == null) ? 0 : getBdpInterfaceQuantity().hashCode());
        result = prime * result + ((getPosInterfaceQuantity() == null) ? 0 : getPosInterfaceQuantity().hashCode());
        result = prime * result + ((getPosBusinessQuantity() == null) ? 0 : getPosBusinessQuantity().hashCode());
        result = prime * result + ((getExecStatus() == null) ? 0 : getExecStatus().hashCode());
        result = prime * result + ((getDiffMsg() == null) ? 0 : getDiffMsg().hashCode());
        result = prime * result + ((getDt() == null) ? 0 : getDt().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", platformName=").append(platformName);
        sb.append(", companyCode=").append(companyCode);
        sb.append(", companyName=").append(companyName);
        sb.append(", storeCode=").append(storeCode);
        sb.append(", storeName=").append(storeName);
        sb.append(", goodsNo=").append(goodsNo);
        sb.append(", bdpInterfaceQuantity=").append(bdpInterfaceQuantity);
        sb.append(", posInterfaceQuantity=").append(posInterfaceQuantity);
        sb.append(", posBusinessQuantity=").append(posBusinessQuantity);
        sb.append(", execStatus=").append(execStatus);
        sb.append(", diffMsg=").append(diffMsg);
        sb.append(", dt=").append(dt);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}