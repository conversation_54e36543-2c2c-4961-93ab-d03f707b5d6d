package com.cowell.iscm.entityTidb;

import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class StoreApplyDateExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Long offset;

    public StoreApplyDateExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Long offset) {
        this.offset = offset;
    }

    public Long getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        protected void addCriterionForJDBCDate(String condition, Date value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value.getTime()), property);
        }

        protected void addCriterionForJDBCDate(String condition, List<Date> values, String property) {
            if (values == null || values.size() == 0) {
                throw new RuntimeException("Value list for " + property + " cannot be null or empty");
            }
            List<java.sql.Date> dateList = new ArrayList<java.sql.Date>();
            Iterator<Date> iter = values.iterator();
            while (iter.hasNext()) {
                dateList.add(new java.sql.Date(iter.next().getTime()));
            }
            addCriterion(condition, dateList, property);
        }

        protected void addCriterionForJDBCDate(String condition, Date value1, Date value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value1.getTime()), new java.sql.Date(value2.getTime()), property);
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIsNull() {
            addCriterion("company_code is null");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIsNotNull() {
            addCriterion("company_code is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeEqualTo(String value) {
            addCriterion("company_code =", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotEqualTo(String value) {
            addCriterion("company_code <>", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeGreaterThan(String value) {
            addCriterion("company_code >", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeGreaterThanOrEqualTo(String value) {
            addCriterion("company_code >=", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLessThan(String value) {
            addCriterion("company_code <", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLessThanOrEqualTo(String value) {
            addCriterion("company_code <=", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLike(String value) {
            addCriterion("company_code like", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotLike(String value) {
            addCriterion("company_code not like", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIn(List<String> values) {
            addCriterion("company_code in", values, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotIn(List<String> values) {
            addCriterion("company_code not in", values, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeBetween(String value1, String value2) {
            addCriterion("company_code between", value1, value2, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotBetween(String value1, String value2) {
            addCriterion("company_code not between", value1, value2, "companyCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeIsNull() {
            addCriterion("store_code is null");
            return (Criteria) this;
        }

        public Criteria andStoreCodeIsNotNull() {
            addCriterion("store_code is not null");
            return (Criteria) this;
        }

        public Criteria andStoreCodeEqualTo(String value) {
            addCriterion("store_code =", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotEqualTo(String value) {
            addCriterion("store_code <>", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeGreaterThan(String value) {
            addCriterion("store_code >", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeGreaterThanOrEqualTo(String value) {
            addCriterion("store_code >=", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeLessThan(String value) {
            addCriterion("store_code <", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeLessThanOrEqualTo(String value) {
            addCriterion("store_code <=", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeLike(String value) {
            addCriterion("store_code like", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotLike(String value) {
            addCriterion("store_code not like", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeIn(List<String> values) {
            addCriterion("store_code in", values, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotIn(List<String> values) {
            addCriterion("store_code not in", values, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeBetween(String value1, String value2) {
            addCriterion("store_code between", value1, value2, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotBetween(String value1, String value2) {
            addCriterion("store_code not between", value1, value2, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreAttrIsNull() {
            addCriterion("store_attr is null");
            return (Criteria) this;
        }

        public Criteria andStoreAttrIsNotNull() {
            addCriterion("store_attr is not null");
            return (Criteria) this;
        }

        public Criteria andStoreAttrEqualTo(String value) {
            addCriterion("store_attr =", value, "storeAttr");
            return (Criteria) this;
        }

        public Criteria andStoreAttrNotEqualTo(String value) {
            addCriterion("store_attr <>", value, "storeAttr");
            return (Criteria) this;
        }

        public Criteria andStoreAttrGreaterThan(String value) {
            addCriterion("store_attr >", value, "storeAttr");
            return (Criteria) this;
        }

        public Criteria andStoreAttrGreaterThanOrEqualTo(String value) {
            addCriterion("store_attr >=", value, "storeAttr");
            return (Criteria) this;
        }

        public Criteria andStoreAttrLessThan(String value) {
            addCriterion("store_attr <", value, "storeAttr");
            return (Criteria) this;
        }

        public Criteria andStoreAttrLessThanOrEqualTo(String value) {
            addCriterion("store_attr <=", value, "storeAttr");
            return (Criteria) this;
        }

        public Criteria andStoreAttrLike(String value) {
            addCriterion("store_attr like", value, "storeAttr");
            return (Criteria) this;
        }

        public Criteria andStoreAttrNotLike(String value) {
            addCriterion("store_attr not like", value, "storeAttr");
            return (Criteria) this;
        }

        public Criteria andStoreAttrIn(List<String> values) {
            addCriterion("store_attr in", values, "storeAttr");
            return (Criteria) this;
        }

        public Criteria andStoreAttrNotIn(List<String> values) {
            addCriterion("store_attr not in", values, "storeAttr");
            return (Criteria) this;
        }

        public Criteria andStoreAttrBetween(String value1, String value2) {
            addCriterion("store_attr between", value1, value2, "storeAttr");
            return (Criteria) this;
        }

        public Criteria andStoreAttrNotBetween(String value1, String value2) {
            addCriterion("store_attr not between", value1, value2, "storeAttr");
            return (Criteria) this;
        }

        public Criteria andOpenDateIsNull() {
            addCriterion("open_date is null");
            return (Criteria) this;
        }

        public Criteria andOpenDateIsNotNull() {
            addCriterion("open_date is not null");
            return (Criteria) this;
        }

        public Criteria andOpenDateEqualTo(Date value) {
            addCriterionForJDBCDate("open_date =", value, "openDate");
            return (Criteria) this;
        }

        public Criteria andOpenDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("open_date <>", value, "openDate");
            return (Criteria) this;
        }

        public Criteria andOpenDateGreaterThan(Date value) {
            addCriterionForJDBCDate("open_date >", value, "openDate");
            return (Criteria) this;
        }

        public Criteria andOpenDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("open_date >=", value, "openDate");
            return (Criteria) this;
        }

        public Criteria andOpenDateLessThan(Date value) {
            addCriterionForJDBCDate("open_date <", value, "openDate");
            return (Criteria) this;
        }

        public Criteria andOpenDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("open_date <=", value, "openDate");
            return (Criteria) this;
        }

        public Criteria andOpenDateIn(List<Date> values) {
            addCriterionForJDBCDate("open_date in", values, "openDate");
            return (Criteria) this;
        }

        public Criteria andOpenDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("open_date not in", values, "openDate");
            return (Criteria) this;
        }

        public Criteria andOpenDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("open_date between", value1, value2, "openDate");
            return (Criteria) this;
        }

        public Criteria andOpenDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("open_date not between", value1, value2, "openDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateIsNull() {
            addCriterion("apply_date is null");
            return (Criteria) this;
        }

        public Criteria andApplyDateIsNotNull() {
            addCriterion("apply_date is not null");
            return (Criteria) this;
        }

        public Criteria andApplyDateEqualTo(Date value) {
            addCriterionForJDBCDate("apply_date =", value, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("apply_date <>", value, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateGreaterThan(Date value) {
            addCriterionForJDBCDate("apply_date >", value, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("apply_date >=", value, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateLessThan(Date value) {
            addCriterionForJDBCDate("apply_date <", value, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("apply_date <=", value, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateIn(List<Date> values) {
            addCriterionForJDBCDate("apply_date in", values, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("apply_date not in", values, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("apply_date between", value1, value2, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("apply_date not between", value1, value2, "applyDate");
            return (Criteria) this;
        }

        public Criteria andStoreStatusIsNull() {
            addCriterion("store_status is null");
            return (Criteria) this;
        }

        public Criteria andStoreStatusIsNotNull() {
            addCriterion("store_status is not null");
            return (Criteria) this;
        }

        public Criteria andStoreStatusEqualTo(String value) {
            addCriterion("store_status =", value, "storeStatus");
            return (Criteria) this;
        }

        public Criteria andStoreStatusNotEqualTo(String value) {
            addCriterion("store_status <>", value, "storeStatus");
            return (Criteria) this;
        }

        public Criteria andStoreStatusGreaterThan(String value) {
            addCriterion("store_status >", value, "storeStatus");
            return (Criteria) this;
        }

        public Criteria andStoreStatusGreaterThanOrEqualTo(String value) {
            addCriterion("store_status >=", value, "storeStatus");
            return (Criteria) this;
        }

        public Criteria andStoreStatusLessThan(String value) {
            addCriterion("store_status <", value, "storeStatus");
            return (Criteria) this;
        }

        public Criteria andStoreStatusLessThanOrEqualTo(String value) {
            addCriterion("store_status <=", value, "storeStatus");
            return (Criteria) this;
        }

        public Criteria andStoreStatusLike(String value) {
            addCriterion("store_status like", value, "storeStatus");
            return (Criteria) this;
        }

        public Criteria andStoreStatusNotLike(String value) {
            addCriterion("store_status not like", value, "storeStatus");
            return (Criteria) this;
        }

        public Criteria andStoreStatusIn(List<String> values) {
            addCriterion("store_status in", values, "storeStatus");
            return (Criteria) this;
        }

        public Criteria andStoreStatusNotIn(List<String> values) {
            addCriterion("store_status not in", values, "storeStatus");
            return (Criteria) this;
        }

        public Criteria andStoreStatusBetween(String value1, String value2) {
            addCriterion("store_status between", value1, value2, "storeStatus");
            return (Criteria) this;
        }

        public Criteria andStoreStatusNotBetween(String value1, String value2) {
            addCriterion("store_status not between", value1, value2, "storeStatus");
            return (Criteria) this;
        }

        public Criteria andStoreElectricAbleIsNull() {
            addCriterion("store_electric_able is null");
            return (Criteria) this;
        }

        public Criteria andStoreElectricAbleIsNotNull() {
            addCriterion("store_electric_able is not null");
            return (Criteria) this;
        }

        public Criteria andStoreElectricAbleEqualTo(String value) {
            addCriterion("store_electric_able =", value, "storeElectricAble");
            return (Criteria) this;
        }

        public Criteria andStoreElectricAbleNotEqualTo(String value) {
            addCriterion("store_electric_able <>", value, "storeElectricAble");
            return (Criteria) this;
        }

        public Criteria andStoreElectricAbleGreaterThan(String value) {
            addCriterion("store_electric_able >", value, "storeElectricAble");
            return (Criteria) this;
        }

        public Criteria andStoreElectricAbleGreaterThanOrEqualTo(String value) {
            addCriterion("store_electric_able >=", value, "storeElectricAble");
            return (Criteria) this;
        }

        public Criteria andStoreElectricAbleLessThan(String value) {
            addCriterion("store_electric_able <", value, "storeElectricAble");
            return (Criteria) this;
        }

        public Criteria andStoreElectricAbleLessThanOrEqualTo(String value) {
            addCriterion("store_electric_able <=", value, "storeElectricAble");
            return (Criteria) this;
        }

        public Criteria andStoreElectricAbleLike(String value) {
            addCriterion("store_electric_able like", value, "storeElectricAble");
            return (Criteria) this;
        }

        public Criteria andStoreElectricAbleNotLike(String value) {
            addCriterion("store_electric_able not like", value, "storeElectricAble");
            return (Criteria) this;
        }

        public Criteria andStoreElectricAbleIn(List<String> values) {
            addCriterion("store_electric_able in", values, "storeElectricAble");
            return (Criteria) this;
        }

        public Criteria andStoreElectricAbleNotIn(List<String> values) {
            addCriterion("store_electric_able not in", values, "storeElectricAble");
            return (Criteria) this;
        }

        public Criteria andStoreElectricAbleBetween(String value1, String value2) {
            addCriterion("store_electric_able between", value1, value2, "storeElectricAble");
            return (Criteria) this;
        }

        public Criteria andStoreElectricAbleNotBetween(String value1, String value2) {
            addCriterion("store_electric_able not between", value1, value2, "storeElectricAble");
            return (Criteria) this;
        }

        public Criteria andDistrCircleIsNull() {
            addCriterion("distr_circle is null");
            return (Criteria) this;
        }

        public Criteria andDistrCircleIsNotNull() {
            addCriterion("distr_circle is not null");
            return (Criteria) this;
        }

        public Criteria andDistrCircleEqualTo(String value) {
            addCriterion("distr_circle =", value, "distrCircle");
            return (Criteria) this;
        }

        public Criteria andDistrCircleNotEqualTo(String value) {
            addCriterion("distr_circle <>", value, "distrCircle");
            return (Criteria) this;
        }

        public Criteria andDistrCircleGreaterThan(String value) {
            addCriterion("distr_circle >", value, "distrCircle");
            return (Criteria) this;
        }

        public Criteria andDistrCircleGreaterThanOrEqualTo(String value) {
            addCriterion("distr_circle >=", value, "distrCircle");
            return (Criteria) this;
        }

        public Criteria andDistrCircleLessThan(String value) {
            addCriterion("distr_circle <", value, "distrCircle");
            return (Criteria) this;
        }

        public Criteria andDistrCircleLessThanOrEqualTo(String value) {
            addCriterion("distr_circle <=", value, "distrCircle");
            return (Criteria) this;
        }

        public Criteria andDistrCircleLike(String value) {
            addCriterion("distr_circle like", value, "distrCircle");
            return (Criteria) this;
        }

        public Criteria andDistrCircleNotLike(String value) {
            addCriterion("distr_circle not like", value, "distrCircle");
            return (Criteria) this;
        }

        public Criteria andDistrCircleIn(List<String> values) {
            addCriterion("distr_circle in", values, "distrCircle");
            return (Criteria) this;
        }

        public Criteria andDistrCircleNotIn(List<String> values) {
            addCriterion("distr_circle not in", values, "distrCircle");
            return (Criteria) this;
        }

        public Criteria andDistrCircleBetween(String value1, String value2) {
            addCriterion("distr_circle between", value1, value2, "distrCircle");
            return (Criteria) this;
        }

        public Criteria andDistrCircleNotBetween(String value1, String value2) {
            addCriterion("distr_circle not between", value1, value2, "distrCircle");
            return (Criteria) this;
        }

        public Criteria andDistrTransitDayIsNull() {
            addCriterion("distr_transit_day is null");
            return (Criteria) this;
        }

        public Criteria andDistrTransitDayIsNotNull() {
            addCriterion("distr_transit_day is not null");
            return (Criteria) this;
        }

        public Criteria andDistrTransitDayEqualTo(Integer value) {
            addCriterion("distr_transit_day =", value, "distrTransitDay");
            return (Criteria) this;
        }

        public Criteria andDistrTransitDayNotEqualTo(Integer value) {
            addCriterion("distr_transit_day <>", value, "distrTransitDay");
            return (Criteria) this;
        }

        public Criteria andDistrTransitDayGreaterThan(Integer value) {
            addCriterion("distr_transit_day >", value, "distrTransitDay");
            return (Criteria) this;
        }

        public Criteria andDistrTransitDayGreaterThanOrEqualTo(Integer value) {
            addCriterion("distr_transit_day >=", value, "distrTransitDay");
            return (Criteria) this;
        }

        public Criteria andDistrTransitDayLessThan(Integer value) {
            addCriterion("distr_transit_day <", value, "distrTransitDay");
            return (Criteria) this;
        }

        public Criteria andDistrTransitDayLessThanOrEqualTo(Integer value) {
            addCriterion("distr_transit_day <=", value, "distrTransitDay");
            return (Criteria) this;
        }

        public Criteria andDistrTransitDayIn(List<Integer> values) {
            addCriterion("distr_transit_day in", values, "distrTransitDay");
            return (Criteria) this;
        }

        public Criteria andDistrTransitDayNotIn(List<Integer> values) {
            addCriterion("distr_transit_day not in", values, "distrTransitDay");
            return (Criteria) this;
        }

        public Criteria andDistrTransitDayBetween(Integer value1, Integer value2) {
            addCriterion("distr_transit_day between", value1, value2, "distrTransitDay");
            return (Criteria) this;
        }

        public Criteria andDistrTransitDayNotBetween(Integer value1, Integer value2) {
            addCriterion("distr_transit_day not between", value1, value2, "distrTransitDay");
            return (Criteria) this;
        }

        public Criteria andMonthSalesLevelIsNull() {
            addCriterion("month_sales_level is null");
            return (Criteria) this;
        }

        public Criteria andMonthSalesLevelIsNotNull() {
            addCriterion("month_sales_level is not null");
            return (Criteria) this;
        }

        public Criteria andMonthSalesLevelEqualTo(String value) {
            addCriterion("month_sales_level =", value, "monthSalesLevel");
            return (Criteria) this;
        }

        public Criteria andMonthSalesLevelNotEqualTo(String value) {
            addCriterion("month_sales_level <>", value, "monthSalesLevel");
            return (Criteria) this;
        }

        public Criteria andMonthSalesLevelGreaterThan(String value) {
            addCriterion("month_sales_level >", value, "monthSalesLevel");
            return (Criteria) this;
        }

        public Criteria andMonthSalesLevelGreaterThanOrEqualTo(String value) {
            addCriterion("month_sales_level >=", value, "monthSalesLevel");
            return (Criteria) this;
        }

        public Criteria andMonthSalesLevelLessThan(String value) {
            addCriterion("month_sales_level <", value, "monthSalesLevel");
            return (Criteria) this;
        }

        public Criteria andMonthSalesLevelLessThanOrEqualTo(String value) {
            addCriterion("month_sales_level <=", value, "monthSalesLevel");
            return (Criteria) this;
        }

        public Criteria andMonthSalesLevelLike(String value) {
            addCriterion("month_sales_level like", value, "monthSalesLevel");
            return (Criteria) this;
        }

        public Criteria andMonthSalesLevelNotLike(String value) {
            addCriterion("month_sales_level not like", value, "monthSalesLevel");
            return (Criteria) this;
        }

        public Criteria andMonthSalesLevelIn(List<String> values) {
            addCriterion("month_sales_level in", values, "monthSalesLevel");
            return (Criteria) this;
        }

        public Criteria andMonthSalesLevelNotIn(List<String> values) {
            addCriterion("month_sales_level not in", values, "monthSalesLevel");
            return (Criteria) this;
        }

        public Criteria andMonthSalesLevelBetween(String value1, String value2) {
            addCriterion("month_sales_level between", value1, value2, "monthSalesLevel");
            return (Criteria) this;
        }

        public Criteria andMonthSalesLevelNotBetween(String value1, String value2) {
            addCriterion("month_sales_level not between", value1, value2, "monthSalesLevel");
            return (Criteria) this;
        }

        public Criteria andStoreCountIsNull() {
            addCriterion("store_count is null");
            return (Criteria) this;
        }

        public Criteria andStoreCountIsNotNull() {
            addCriterion("store_count is not null");
            return (Criteria) this;
        }

        public Criteria andStoreCountEqualTo(Integer value) {
            addCriterion("store_count =", value, "storeCount");
            return (Criteria) this;
        }

        public Criteria andStoreCountNotEqualTo(Integer value) {
            addCriterion("store_count <>", value, "storeCount");
            return (Criteria) this;
        }

        public Criteria andStoreCountGreaterThan(Integer value) {
            addCriterion("store_count >", value, "storeCount");
            return (Criteria) this;
        }

        public Criteria andStoreCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("store_count >=", value, "storeCount");
            return (Criteria) this;
        }

        public Criteria andStoreCountLessThan(Integer value) {
            addCriterion("store_count <", value, "storeCount");
            return (Criteria) this;
        }

        public Criteria andStoreCountLessThanOrEqualTo(Integer value) {
            addCriterion("store_count <=", value, "storeCount");
            return (Criteria) this;
        }

        public Criteria andStoreCountIn(List<Integer> values) {
            addCriterion("store_count in", values, "storeCount");
            return (Criteria) this;
        }

        public Criteria andStoreCountNotIn(List<Integer> values) {
            addCriterion("store_count not in", values, "storeCount");
            return (Criteria) this;
        }

        public Criteria andStoreCountBetween(Integer value1, Integer value2) {
            addCriterion("store_count between", value1, value2, "storeCount");
            return (Criteria) this;
        }

        public Criteria andStoreCountNotBetween(Integer value1, Integer value2) {
            addCriterion("store_count not between", value1, value2, "storeCount");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIsNull() {
            addCriterion("gmt_update is null");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIsNotNull() {
            addCriterion("gmt_update is not null");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateEqualTo(Date value) {
            addCriterion("gmt_update =", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotEqualTo(Date value) {
            addCriterion("gmt_update <>", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateGreaterThan(Date value) {
            addCriterion("gmt_update >", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_update >=", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateLessThan(Date value) {
            addCriterion("gmt_update <", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_update <=", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIn(List<Date> values) {
            addCriterion("gmt_update in", values, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotIn(List<Date> values) {
            addCriterion("gmt_update not in", values, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateBetween(Date value1, Date value2) {
            addCriterion("gmt_update between", value1, value2, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_update not between", value1, value2, "gmtUpdate");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}