package com.cowell.iscm.entityTidb;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> 一品多码转换表
 */
public class IscmApplyGoodsMultipleCode implements Serializable {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 旧商品编码
     */
    private String oldGoodsNo;

    /**
     * 新商品编码
     */
    private String newGoodsNo;

    /**
     * 转换比率
     */
    private BigDecimal changeRatio;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新时间
     */
    private Date gmtUpdate;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOldGoodsNo() {
        return oldGoodsNo;
    }

    public void setOldGoodsNo(String oldGoodsNo) {
        this.oldGoodsNo = oldGoodsNo;
    }

    public String getNewGoodsNo() {
        return newGoodsNo;
    }

    public void setNewGoodsNo(String newGoodsNo) {
        this.newGoodsNo = newGoodsNo;
    }

    public BigDecimal getChangeRatio() {
        return changeRatio;
    }

    public void setChangeRatio(BigDecimal changeRatio) {
        this.changeRatio = changeRatio;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtUpdate() {
        return gmtUpdate;
    }

    public void setGmtUpdate(Date gmtUpdate) {
        this.gmtUpdate = gmtUpdate;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        IscmApplyGoodsMultipleCode other = (IscmApplyGoodsMultipleCode) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getOldGoodsNo() == null ? other.getOldGoodsNo() == null : this.getOldGoodsNo().equals(other.getOldGoodsNo()))
            && (this.getNewGoodsNo() == null ? other.getNewGoodsNo() == null : this.getNewGoodsNo().equals(other.getNewGoodsNo()))
            && (this.getChangeRatio() == null ? other.getChangeRatio() == null : this.getChangeRatio().equals(other.getChangeRatio()))
            && (this.getGmtCreate() == null ? other.getGmtCreate() == null : this.getGmtCreate().equals(other.getGmtCreate()))
            && (this.getGmtUpdate() == null ? other.getGmtUpdate() == null : this.getGmtUpdate().equals(other.getGmtUpdate()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getOldGoodsNo() == null) ? 0 : getOldGoodsNo().hashCode());
        result = prime * result + ((getNewGoodsNo() == null) ? 0 : getNewGoodsNo().hashCode());
        result = prime * result + ((getChangeRatio() == null) ? 0 : getChangeRatio().hashCode());
        result = prime * result + ((getGmtCreate() == null) ? 0 : getGmtCreate().hashCode());
        result = prime * result + ((getGmtUpdate() == null) ? 0 : getGmtUpdate().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", oldGoodsNo=").append(oldGoodsNo);
        sb.append(", newGoodsNo=").append(newGoodsNo);
        sb.append(", changeRatio=").append(changeRatio);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", gmtUpdate=").append(gmtUpdate);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}