package com.cowell.iscm.entityTidb;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> pos调拨完成跟踪详情
 */
public class IscmSuggestDistexecDoneDetail implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 调拨日期
     */
    private Date allotDate;

    /**
     * 调拨建议生成月份 格式 yyyyMM 例如 202103
     */
    private Integer allotMonth;

    /**
     * 登记单号
     */
    private String registerNo;

    /**
     * 登记月份 例:202103
     */
    private Integer registerMonth;

    /**
     * 调拨类型,1:近效期调拨,2:大库存、滞销商品调拨,3:断货商品调拨
     */
    private Byte allotType;

    /**
     * pos调拨单号
     */
    private String posAllotNo;

    /**
     * 平台orgId
     */
    private Long platformOrgId;

    /**
     * 平台名称
     */
    private String platformOrgName;

    /**
     * 调出公司Id
     */
    private Long outCompanyId;

    /**
     * 调出公司MDM编码
     */
    private String outCompanyCode;

    /**
     * 调出公司名称
     */
    private String outCompanyName;

    /**
     * 调入公司id
     */
    private Long inCompanyId;

    /**
     * 调入公司MDM编码
     */
    private String inCompanyCode;

    /**
     * 调入公司名称
     */
    private String inCompanyName;

    /**
     * 调出门店 id
     */
    private Long outStoreId;

    /**
     * 调出门店MDM编码
     */
    private String outStoreCode;

    /**
     * 调出门店名称
     */
    private String outStoreName;

    /**
     * 调出门店属性
     */
    private String outStoreAttr;

    /**
     * 调出门店月销等级
     */
    private String outStoreSalesLevel;

    /**
     * 调入门店 id
     */
    private Long inStoreId;

    /**
     * 调入门店MDM编码
     */
    private String inStoreCode;

    /**
     * 调入门店名称
     */
    private String inStoreName;

    /**
     * 调入门店属性
     */
    private String inStoreAttr;

    /**
     * 调入门店月销等级
     */
    private String inStoreSalesLevel;

    /**
     * 调拨组编码
     */
    private String allotGroupCode;

    /**
     * 调拨组名称
     */
    private String allotGroupName;

    /**
     * 商品编码
     */
    private String goodsNo;

    /**
     * 商品描述
     */
    private String goodsDesc;

    /**
     * 通用名
     */
    private String goodsCommonName;

    /**
     * 生产厂家
     */
    private String manufacturer;

    /**
     * 单位
     */
    private String unit;

    /**
     * 实际调出数量
     */
    private BigDecimal realAllotQuantity;

    /**
     * 实际调拨成本金额
     */
    private BigDecimal realCostAmount;

    /**
     * 审批时间
     */
    private Date allotApproveTime;

    /**
     * 调入门店审批状态 0:未审批 1:审批通过 2:作废
     */
    private Byte inApproveStatus;

    /**
     * 调入门店审批时间
     */
    private Date inApproveTime;

    /**
     * 调入数量
     */
    private BigDecimal inAllotQuantity;

    /**
     * 调入门店调拨成本金额
     */
    private BigDecimal inAllotCostAmount;

    /**
     * 调入门店入库时间
     */
    private Date inStockTime;

    /**
     * 调入后7天销售数量
     */
    private BigDecimal inStoreSales7;

    /**
     * 调入后7天销售成本金额
     */
    private BigDecimal inStorePuramount7;

    /**
     * 调入后14天销售数量
     */
    private BigDecimal inStoreSales14;

    /**
     * 调入后14天销售成本金额
     */
    private BigDecimal inStorePuramount14;

    /**
     * 调入后30天销售数量
     */
    private BigDecimal inStoreSales30;

    /**
     * 调入后30天销售成本金额
     */
    private BigDecimal inStorePuramount30;

    /**
     * 调入后60天销售数量
     */
    private BigDecimal inStoreSales60;

    /**
     * 调入后60天销售成本金额
     */
    private BigDecimal inStorePuramount60;

    /**
     * 调入后90天销售数量
     */
    private BigDecimal inStoreSales90;

    /**
     * 调入后90天销售成本金额
     */
    private BigDecimal inStorePuramount90;

    /**
     * 状态(-1删除，0正常)
     */
    private Byte status;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新时间
     */
    private Date gmtUpdate;

    /**
     * 扩展字段
     */
    private String extend;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 创建人ID
     */
    private Long createdBy;

    /**
     * 创建人
     */
    private String createdName;

    /**
     * 更新人ID
     */
    private Long updatedBy;

    /**
     * 更新人
     */
    private String updatedName;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getAllotDate() {
        return allotDate;
    }

    public void setAllotDate(Date allotDate) {
        this.allotDate = allotDate;
    }

    public Integer getAllotMonth() {
        return allotMonth;
    }

    public void setAllotMonth(Integer allotMonth) {
        this.allotMonth = allotMonth;
    }

    public String getRegisterNo() {
        return registerNo;
    }

    public void setRegisterNo(String registerNo) {
        this.registerNo = registerNo;
    }

    public Integer getRegisterMonth() {
        return registerMonth;
    }

    public void setRegisterMonth(Integer registerMonth) {
        this.registerMonth = registerMonth;
    }

    public Byte getAllotType() {
        return allotType;
    }

    public void setAllotType(Byte allotType) {
        this.allotType = allotType;
    }

    public String getPosAllotNo() {
        return posAllotNo;
    }

    public void setPosAllotNo(String posAllotNo) {
        this.posAllotNo = posAllotNo;
    }

    public Long getPlatformOrgId() {
        return platformOrgId;
    }

    public void setPlatformOrgId(Long platformOrgId) {
        this.platformOrgId = platformOrgId;
    }

    public String getPlatformOrgName() {
        return platformOrgName;
    }

    public void setPlatformOrgName(String platformOrgName) {
        this.platformOrgName = platformOrgName;
    }

    public Long getOutCompanyId() {
        return outCompanyId;
    }

    public void setOutCompanyId(Long outCompanyId) {
        this.outCompanyId = outCompanyId;
    }

    public String getOutCompanyCode() {
        return outCompanyCode;
    }

    public void setOutCompanyCode(String outCompanyCode) {
        this.outCompanyCode = outCompanyCode;
    }

    public String getOutCompanyName() {
        return outCompanyName;
    }

    public void setOutCompanyName(String outCompanyName) {
        this.outCompanyName = outCompanyName;
    }

    public Long getInCompanyId() {
        return inCompanyId;
    }

    public void setInCompanyId(Long inCompanyId) {
        this.inCompanyId = inCompanyId;
    }

    public String getInCompanyCode() {
        return inCompanyCode;
    }

    public void setInCompanyCode(String inCompanyCode) {
        this.inCompanyCode = inCompanyCode;
    }

    public String getInCompanyName() {
        return inCompanyName;
    }

    public void setInCompanyName(String inCompanyName) {
        this.inCompanyName = inCompanyName;
    }

    public Long getOutStoreId() {
        return outStoreId;
    }

    public void setOutStoreId(Long outStoreId) {
        this.outStoreId = outStoreId;
    }

    public String getOutStoreCode() {
        return outStoreCode;
    }

    public void setOutStoreCode(String outStoreCode) {
        this.outStoreCode = outStoreCode;
    }

    public String getOutStoreName() {
        return outStoreName;
    }

    public void setOutStoreName(String outStoreName) {
        this.outStoreName = outStoreName;
    }

    public String getOutStoreAttr() {
        return outStoreAttr;
    }

    public void setOutStoreAttr(String outStoreAttr) {
        this.outStoreAttr = outStoreAttr;
    }

    public String getOutStoreSalesLevel() {
        return outStoreSalesLevel;
    }

    public void setOutStoreSalesLevel(String outStoreSalesLevel) {
        this.outStoreSalesLevel = outStoreSalesLevel;
    }

    public Long getInStoreId() {
        return inStoreId;
    }

    public void setInStoreId(Long inStoreId) {
        this.inStoreId = inStoreId;
    }

    public String getInStoreCode() {
        return inStoreCode;
    }

    public void setInStoreCode(String inStoreCode) {
        this.inStoreCode = inStoreCode;
    }

    public String getInStoreName() {
        return inStoreName;
    }

    public void setInStoreName(String inStoreName) {
        this.inStoreName = inStoreName;
    }

    public String getInStoreAttr() {
        return inStoreAttr;
    }

    public void setInStoreAttr(String inStoreAttr) {
        this.inStoreAttr = inStoreAttr;
    }

    public String getInStoreSalesLevel() {
        return inStoreSalesLevel;
    }

    public void setInStoreSalesLevel(String inStoreSalesLevel) {
        this.inStoreSalesLevel = inStoreSalesLevel;
    }

    public String getAllotGroupCode() {
        return allotGroupCode;
    }

    public void setAllotGroupCode(String allotGroupCode) {
        this.allotGroupCode = allotGroupCode;
    }

    public String getAllotGroupName() {
        return allotGroupName;
    }

    public void setAllotGroupName(String allotGroupName) {
        this.allotGroupName = allotGroupName;
    }

    public String getGoodsNo() {
        return goodsNo;
    }

    public void setGoodsNo(String goodsNo) {
        this.goodsNo = goodsNo;
    }

    public String getGoodsDesc() {
        return goodsDesc;
    }

    public void setGoodsDesc(String goodsDesc) {
        this.goodsDesc = goodsDesc;
    }

    public String getGoodsCommonName() {
        return goodsCommonName;
    }

    public void setGoodsCommonName(String goodsCommonName) {
        this.goodsCommonName = goodsCommonName;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public BigDecimal getRealAllotQuantity() {
        return realAllotQuantity;
    }

    public void setRealAllotQuantity(BigDecimal realAllotQuantity) {
        this.realAllotQuantity = realAllotQuantity;
    }

    public BigDecimal getRealCostAmount() {
        return realCostAmount;
    }

    public void setRealCostAmount(BigDecimal realCostAmount) {
        this.realCostAmount = realCostAmount;
    }

    public Date getAllotApproveTime() {
        return allotApproveTime;
    }

    public void setAllotApproveTime(Date allotApproveTime) {
        this.allotApproveTime = allotApproveTime;
    }

    public Byte getInApproveStatus() {
        return inApproveStatus;
    }

    public void setInApproveStatus(Byte inApproveStatus) {
        this.inApproveStatus = inApproveStatus;
    }

    public Date getInApproveTime() {
        return inApproveTime;
    }

    public void setInApproveTime(Date inApproveTime) {
        this.inApproveTime = inApproveTime;
    }

    public BigDecimal getInAllotQuantity() {
        return inAllotQuantity;
    }

    public void setInAllotQuantity(BigDecimal inAllotQuantity) {
        this.inAllotQuantity = inAllotQuantity;
    }

    public BigDecimal getInAllotCostAmount() {
        return inAllotCostAmount;
    }

    public void setInAllotCostAmount(BigDecimal inAllotCostAmount) {
        this.inAllotCostAmount = inAllotCostAmount;
    }

    public Date getInStockTime() {
        return inStockTime;
    }

    public void setInStockTime(Date inStockTime) {
        this.inStockTime = inStockTime;
    }

    public BigDecimal getInStoreSales7() {
        return inStoreSales7;
    }

    public void setInStoreSales7(BigDecimal inStoreSales7) {
        this.inStoreSales7 = inStoreSales7;
    }

    public BigDecimal getInStorePuramount7() {
        return inStorePuramount7;
    }

    public void setInStorePuramount7(BigDecimal inStorePuramount7) {
        this.inStorePuramount7 = inStorePuramount7;
    }

    public BigDecimal getInStoreSales14() {
        return inStoreSales14;
    }

    public void setInStoreSales14(BigDecimal inStoreSales14) {
        this.inStoreSales14 = inStoreSales14;
    }

    public BigDecimal getInStorePuramount14() {
        return inStorePuramount14;
    }

    public void setInStorePuramount14(BigDecimal inStorePuramount14) {
        this.inStorePuramount14 = inStorePuramount14;
    }

    public BigDecimal getInStoreSales30() {
        return inStoreSales30;
    }

    public void setInStoreSales30(BigDecimal inStoreSales30) {
        this.inStoreSales30 = inStoreSales30;
    }

    public BigDecimal getInStorePuramount30() {
        return inStorePuramount30;
    }

    public void setInStorePuramount30(BigDecimal inStorePuramount30) {
        this.inStorePuramount30 = inStorePuramount30;
    }

    public BigDecimal getInStoreSales60() {
        return inStoreSales60;
    }

    public void setInStoreSales60(BigDecimal inStoreSales60) {
        this.inStoreSales60 = inStoreSales60;
    }

    public BigDecimal getInStorePuramount60() {
        return inStorePuramount60;
    }

    public void setInStorePuramount60(BigDecimal inStorePuramount60) {
        this.inStorePuramount60 = inStorePuramount60;
    }

    public BigDecimal getInStoreSales90() {
        return inStoreSales90;
    }

    public void setInStoreSales90(BigDecimal inStoreSales90) {
        this.inStoreSales90 = inStoreSales90;
    }

    public BigDecimal getInStorePuramount90() {
        return inStorePuramount90;
    }

    public void setInStorePuramount90(BigDecimal inStorePuramount90) {
        this.inStorePuramount90 = inStorePuramount90;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtUpdate() {
        return gmtUpdate;
    }

    public void setGmtUpdate(Date gmtUpdate) {
        this.gmtUpdate = gmtUpdate;
    }

    public String getExtend() {
        return extend;
    }

    public void setExtend(String extend) {
        this.extend = extend;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Long getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    public String getCreatedName() {
        return createdName;
    }

    public void setCreatedName(String createdName) {
        this.createdName = createdName;
    }

    public Long getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(Long updatedBy) {
        this.updatedBy = updatedBy;
    }

    public String getUpdatedName() {
        return updatedName;
    }

    public void setUpdatedName(String updatedName) {
        this.updatedName = updatedName;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        IscmSuggestDistexecDoneDetail other = (IscmSuggestDistexecDoneDetail) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getAllotDate() == null ? other.getAllotDate() == null : this.getAllotDate().equals(other.getAllotDate()))
            && (this.getAllotMonth() == null ? other.getAllotMonth() == null : this.getAllotMonth().equals(other.getAllotMonth()))
            && (this.getRegisterNo() == null ? other.getRegisterNo() == null : this.getRegisterNo().equals(other.getRegisterNo()))
            && (this.getRegisterMonth() == null ? other.getRegisterMonth() == null : this.getRegisterMonth().equals(other.getRegisterMonth()))
            && (this.getAllotType() == null ? other.getAllotType() == null : this.getAllotType().equals(other.getAllotType()))
            && (this.getPosAllotNo() == null ? other.getPosAllotNo() == null : this.getPosAllotNo().equals(other.getPosAllotNo()))
            && (this.getPlatformOrgId() == null ? other.getPlatformOrgId() == null : this.getPlatformOrgId().equals(other.getPlatformOrgId()))
            && (this.getPlatformOrgName() == null ? other.getPlatformOrgName() == null : this.getPlatformOrgName().equals(other.getPlatformOrgName()))
            && (this.getOutCompanyId() == null ? other.getOutCompanyId() == null : this.getOutCompanyId().equals(other.getOutCompanyId()))
            && (this.getOutCompanyCode() == null ? other.getOutCompanyCode() == null : this.getOutCompanyCode().equals(other.getOutCompanyCode()))
            && (this.getOutCompanyName() == null ? other.getOutCompanyName() == null : this.getOutCompanyName().equals(other.getOutCompanyName()))
            && (this.getInCompanyId() == null ? other.getInCompanyId() == null : this.getInCompanyId().equals(other.getInCompanyId()))
            && (this.getInCompanyCode() == null ? other.getInCompanyCode() == null : this.getInCompanyCode().equals(other.getInCompanyCode()))
            && (this.getInCompanyName() == null ? other.getInCompanyName() == null : this.getInCompanyName().equals(other.getInCompanyName()))
            && (this.getOutStoreId() == null ? other.getOutStoreId() == null : this.getOutStoreId().equals(other.getOutStoreId()))
            && (this.getOutStoreCode() == null ? other.getOutStoreCode() == null : this.getOutStoreCode().equals(other.getOutStoreCode()))
            && (this.getOutStoreName() == null ? other.getOutStoreName() == null : this.getOutStoreName().equals(other.getOutStoreName()))
            && (this.getOutStoreAttr() == null ? other.getOutStoreAttr() == null : this.getOutStoreAttr().equals(other.getOutStoreAttr()))
            && (this.getOutStoreSalesLevel() == null ? other.getOutStoreSalesLevel() == null : this.getOutStoreSalesLevel().equals(other.getOutStoreSalesLevel()))
            && (this.getInStoreId() == null ? other.getInStoreId() == null : this.getInStoreId().equals(other.getInStoreId()))
            && (this.getInStoreCode() == null ? other.getInStoreCode() == null : this.getInStoreCode().equals(other.getInStoreCode()))
            && (this.getInStoreName() == null ? other.getInStoreName() == null : this.getInStoreName().equals(other.getInStoreName()))
            && (this.getInStoreAttr() == null ? other.getInStoreAttr() == null : this.getInStoreAttr().equals(other.getInStoreAttr()))
            && (this.getInStoreSalesLevel() == null ? other.getInStoreSalesLevel() == null : this.getInStoreSalesLevel().equals(other.getInStoreSalesLevel()))
            && (this.getAllotGroupCode() == null ? other.getAllotGroupCode() == null : this.getAllotGroupCode().equals(other.getAllotGroupCode()))
            && (this.getAllotGroupName() == null ? other.getAllotGroupName() == null : this.getAllotGroupName().equals(other.getAllotGroupName()))
            && (this.getGoodsNo() == null ? other.getGoodsNo() == null : this.getGoodsNo().equals(other.getGoodsNo()))
            && (this.getGoodsDesc() == null ? other.getGoodsDesc() == null : this.getGoodsDesc().equals(other.getGoodsDesc()))
            && (this.getGoodsCommonName() == null ? other.getGoodsCommonName() == null : this.getGoodsCommonName().equals(other.getGoodsCommonName()))
            && (this.getManufacturer() == null ? other.getManufacturer() == null : this.getManufacturer().equals(other.getManufacturer()))
            && (this.getUnit() == null ? other.getUnit() == null : this.getUnit().equals(other.getUnit()))
            && (this.getRealAllotQuantity() == null ? other.getRealAllotQuantity() == null : this.getRealAllotQuantity().equals(other.getRealAllotQuantity()))
            && (this.getRealCostAmount() == null ? other.getRealCostAmount() == null : this.getRealCostAmount().equals(other.getRealCostAmount()))
            && (this.getAllotApproveTime() == null ? other.getAllotApproveTime() == null : this.getAllotApproveTime().equals(other.getAllotApproveTime()))
            && (this.getInApproveStatus() == null ? other.getInApproveStatus() == null : this.getInApproveStatus().equals(other.getInApproveStatus()))
            && (this.getInApproveTime() == null ? other.getInApproveTime() == null : this.getInApproveTime().equals(other.getInApproveTime()))
            && (this.getInAllotQuantity() == null ? other.getInAllotQuantity() == null : this.getInAllotQuantity().equals(other.getInAllotQuantity()))
            && (this.getInAllotCostAmount() == null ? other.getInAllotCostAmount() == null : this.getInAllotCostAmount().equals(other.getInAllotCostAmount()))
            && (this.getInStockTime() == null ? other.getInStockTime() == null : this.getInStockTime().equals(other.getInStockTime()))
            && (this.getInStoreSales7() == null ? other.getInStoreSales7() == null : this.getInStoreSales7().equals(other.getInStoreSales7()))
            && (this.getInStorePuramount7() == null ? other.getInStorePuramount7() == null : this.getInStorePuramount7().equals(other.getInStorePuramount7()))
            && (this.getInStoreSales14() == null ? other.getInStoreSales14() == null : this.getInStoreSales14().equals(other.getInStoreSales14()))
            && (this.getInStorePuramount14() == null ? other.getInStorePuramount14() == null : this.getInStorePuramount14().equals(other.getInStorePuramount14()))
            && (this.getInStoreSales30() == null ? other.getInStoreSales30() == null : this.getInStoreSales30().equals(other.getInStoreSales30()))
            && (this.getInStorePuramount30() == null ? other.getInStorePuramount30() == null : this.getInStorePuramount30().equals(other.getInStorePuramount30()))
            && (this.getInStoreSales60() == null ? other.getInStoreSales60() == null : this.getInStoreSales60().equals(other.getInStoreSales60()))
            && (this.getInStorePuramount60() == null ? other.getInStorePuramount60() == null : this.getInStorePuramount60().equals(other.getInStorePuramount60()))
            && (this.getInStoreSales90() == null ? other.getInStoreSales90() == null : this.getInStoreSales90().equals(other.getInStoreSales90()))
            && (this.getInStorePuramount90() == null ? other.getInStorePuramount90() == null : this.getInStorePuramount90().equals(other.getInStorePuramount90()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getGmtCreate() == null ? other.getGmtCreate() == null : this.getGmtCreate().equals(other.getGmtCreate()))
            && (this.getGmtUpdate() == null ? other.getGmtUpdate() == null : this.getGmtUpdate().equals(other.getGmtUpdate()))
            && (this.getExtend() == null ? other.getExtend() == null : this.getExtend().equals(other.getExtend()))
            && (this.getVersion() == null ? other.getVersion() == null : this.getVersion().equals(other.getVersion()))
            && (this.getCreatedBy() == null ? other.getCreatedBy() == null : this.getCreatedBy().equals(other.getCreatedBy()))
            && (this.getCreatedName() == null ? other.getCreatedName() == null : this.getCreatedName().equals(other.getCreatedName()))
            && (this.getUpdatedBy() == null ? other.getUpdatedBy() == null : this.getUpdatedBy().equals(other.getUpdatedBy()))
            && (this.getUpdatedName() == null ? other.getUpdatedName() == null : this.getUpdatedName().equals(other.getUpdatedName()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getAllotDate() == null) ? 0 : getAllotDate().hashCode());
        result = prime * result + ((getAllotMonth() == null) ? 0 : getAllotMonth().hashCode());
        result = prime * result + ((getRegisterNo() == null) ? 0 : getRegisterNo().hashCode());
        result = prime * result + ((getRegisterMonth() == null) ? 0 : getRegisterMonth().hashCode());
        result = prime * result + ((getAllotType() == null) ? 0 : getAllotType().hashCode());
        result = prime * result + ((getPosAllotNo() == null) ? 0 : getPosAllotNo().hashCode());
        result = prime * result + ((getPlatformOrgId() == null) ? 0 : getPlatformOrgId().hashCode());
        result = prime * result + ((getPlatformOrgName() == null) ? 0 : getPlatformOrgName().hashCode());
        result = prime * result + ((getOutCompanyId() == null) ? 0 : getOutCompanyId().hashCode());
        result = prime * result + ((getOutCompanyCode() == null) ? 0 : getOutCompanyCode().hashCode());
        result = prime * result + ((getOutCompanyName() == null) ? 0 : getOutCompanyName().hashCode());
        result = prime * result + ((getInCompanyId() == null) ? 0 : getInCompanyId().hashCode());
        result = prime * result + ((getInCompanyCode() == null) ? 0 : getInCompanyCode().hashCode());
        result = prime * result + ((getInCompanyName() == null) ? 0 : getInCompanyName().hashCode());
        result = prime * result + ((getOutStoreId() == null) ? 0 : getOutStoreId().hashCode());
        result = prime * result + ((getOutStoreCode() == null) ? 0 : getOutStoreCode().hashCode());
        result = prime * result + ((getOutStoreName() == null) ? 0 : getOutStoreName().hashCode());
        result = prime * result + ((getOutStoreAttr() == null) ? 0 : getOutStoreAttr().hashCode());
        result = prime * result + ((getOutStoreSalesLevel() == null) ? 0 : getOutStoreSalesLevel().hashCode());
        result = prime * result + ((getInStoreId() == null) ? 0 : getInStoreId().hashCode());
        result = prime * result + ((getInStoreCode() == null) ? 0 : getInStoreCode().hashCode());
        result = prime * result + ((getInStoreName() == null) ? 0 : getInStoreName().hashCode());
        result = prime * result + ((getInStoreAttr() == null) ? 0 : getInStoreAttr().hashCode());
        result = prime * result + ((getInStoreSalesLevel() == null) ? 0 : getInStoreSalesLevel().hashCode());
        result = prime * result + ((getAllotGroupCode() == null) ? 0 : getAllotGroupCode().hashCode());
        result = prime * result + ((getAllotGroupName() == null) ? 0 : getAllotGroupName().hashCode());
        result = prime * result + ((getGoodsNo() == null) ? 0 : getGoodsNo().hashCode());
        result = prime * result + ((getGoodsDesc() == null) ? 0 : getGoodsDesc().hashCode());
        result = prime * result + ((getGoodsCommonName() == null) ? 0 : getGoodsCommonName().hashCode());
        result = prime * result + ((getManufacturer() == null) ? 0 : getManufacturer().hashCode());
        result = prime * result + ((getUnit() == null) ? 0 : getUnit().hashCode());
        result = prime * result + ((getRealAllotQuantity() == null) ? 0 : getRealAllotQuantity().hashCode());
        result = prime * result + ((getRealCostAmount() == null) ? 0 : getRealCostAmount().hashCode());
        result = prime * result + ((getAllotApproveTime() == null) ? 0 : getAllotApproveTime().hashCode());
        result = prime * result + ((getInApproveStatus() == null) ? 0 : getInApproveStatus().hashCode());
        result = prime * result + ((getInApproveTime() == null) ? 0 : getInApproveTime().hashCode());
        result = prime * result + ((getInAllotQuantity() == null) ? 0 : getInAllotQuantity().hashCode());
        result = prime * result + ((getInAllotCostAmount() == null) ? 0 : getInAllotCostAmount().hashCode());
        result = prime * result + ((getInStockTime() == null) ? 0 : getInStockTime().hashCode());
        result = prime * result + ((getInStoreSales7() == null) ? 0 : getInStoreSales7().hashCode());
        result = prime * result + ((getInStorePuramount7() == null) ? 0 : getInStorePuramount7().hashCode());
        result = prime * result + ((getInStoreSales14() == null) ? 0 : getInStoreSales14().hashCode());
        result = prime * result + ((getInStorePuramount14() == null) ? 0 : getInStorePuramount14().hashCode());
        result = prime * result + ((getInStoreSales30() == null) ? 0 : getInStoreSales30().hashCode());
        result = prime * result + ((getInStorePuramount30() == null) ? 0 : getInStorePuramount30().hashCode());
        result = prime * result + ((getInStoreSales60() == null) ? 0 : getInStoreSales60().hashCode());
        result = prime * result + ((getInStorePuramount60() == null) ? 0 : getInStorePuramount60().hashCode());
        result = prime * result + ((getInStoreSales90() == null) ? 0 : getInStoreSales90().hashCode());
        result = prime * result + ((getInStorePuramount90() == null) ? 0 : getInStorePuramount90().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getGmtCreate() == null) ? 0 : getGmtCreate().hashCode());
        result = prime * result + ((getGmtUpdate() == null) ? 0 : getGmtUpdate().hashCode());
        result = prime * result + ((getExtend() == null) ? 0 : getExtend().hashCode());
        result = prime * result + ((getVersion() == null) ? 0 : getVersion().hashCode());
        result = prime * result + ((getCreatedBy() == null) ? 0 : getCreatedBy().hashCode());
        result = prime * result + ((getCreatedName() == null) ? 0 : getCreatedName().hashCode());
        result = prime * result + ((getUpdatedBy() == null) ? 0 : getUpdatedBy().hashCode());
        result = prime * result + ((getUpdatedName() == null) ? 0 : getUpdatedName().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", allotDate=").append(allotDate);
        sb.append(", allotMonth=").append(allotMonth);
        sb.append(", registerNo=").append(registerNo);
        sb.append(", registerMonth=").append(registerMonth);
        sb.append(", allotType=").append(allotType);
        sb.append(", posAllotNo=").append(posAllotNo);
        sb.append(", platformOrgId=").append(platformOrgId);
        sb.append(", platformOrgName=").append(platformOrgName);
        sb.append(", outCompanyId=").append(outCompanyId);
        sb.append(", outCompanyCode=").append(outCompanyCode);
        sb.append(", outCompanyName=").append(outCompanyName);
        sb.append(", inCompanyId=").append(inCompanyId);
        sb.append(", inCompanyCode=").append(inCompanyCode);
        sb.append(", inCompanyName=").append(inCompanyName);
        sb.append(", outStoreId=").append(outStoreId);
        sb.append(", outStoreCode=").append(outStoreCode);
        sb.append(", outStoreName=").append(outStoreName);
        sb.append(", outStoreAttr=").append(outStoreAttr);
        sb.append(", outStoreSalesLevel=").append(outStoreSalesLevel);
        sb.append(", inStoreId=").append(inStoreId);
        sb.append(", inStoreCode=").append(inStoreCode);
        sb.append(", inStoreName=").append(inStoreName);
        sb.append(", inStoreAttr=").append(inStoreAttr);
        sb.append(", inStoreSalesLevel=").append(inStoreSalesLevel);
        sb.append(", allotGroupCode=").append(allotGroupCode);
        sb.append(", allotGroupName=").append(allotGroupName);
        sb.append(", goodsNo=").append(goodsNo);
        sb.append(", goodsDesc=").append(goodsDesc);
        sb.append(", goodsCommonName=").append(goodsCommonName);
        sb.append(", manufacturer=").append(manufacturer);
        sb.append(", unit=").append(unit);
        sb.append(", realAllotQuantity=").append(realAllotQuantity);
        sb.append(", realCostAmount=").append(realCostAmount);
        sb.append(", allotApproveTime=").append(allotApproveTime);
        sb.append(", inApproveStatus=").append(inApproveStatus);
        sb.append(", inApproveTime=").append(inApproveTime);
        sb.append(", inAllotQuantity=").append(inAllotQuantity);
        sb.append(", inAllotCostAmount=").append(inAllotCostAmount);
        sb.append(", inStockTime=").append(inStockTime);
        sb.append(", inStoreSales7=").append(inStoreSales7);
        sb.append(", inStorePuramount7=").append(inStorePuramount7);
        sb.append(", inStoreSales14=").append(inStoreSales14);
        sb.append(", inStorePuramount14=").append(inStorePuramount14);
        sb.append(", inStoreSales30=").append(inStoreSales30);
        sb.append(", inStorePuramount30=").append(inStorePuramount30);
        sb.append(", inStoreSales60=").append(inStoreSales60);
        sb.append(", inStorePuramount60=").append(inStorePuramount60);
        sb.append(", inStoreSales90=").append(inStoreSales90);
        sb.append(", inStorePuramount90=").append(inStorePuramount90);
        sb.append(", status=").append(status);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", gmtUpdate=").append(gmtUpdate);
        sb.append(", extend=").append(extend);
        sb.append(", version=").append(version);
        sb.append(", createdBy=").append(createdBy);
        sb.append(", createdName=").append(createdName);
        sb.append(", updatedBy=").append(updatedBy);
        sb.append(", updatedName=").append(updatedName);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}