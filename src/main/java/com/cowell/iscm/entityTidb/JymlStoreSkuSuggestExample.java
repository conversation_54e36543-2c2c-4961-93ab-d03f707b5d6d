package com.cowell.iscm.entityTidb;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class JymlStoreSkuSuggestExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Long offset;

    public JymlStoreSkuSuggestExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Long offset) {
        this.offset = offset;
    }

    public Long getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andBusinessOrgIdIsNull() {
            addCriterion("business_org_id is null");
            return (Criteria) this;
        }

        public Criteria andBusinessOrgIdIsNotNull() {
            addCriterion("business_org_id is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessOrgIdEqualTo(Long value) {
            addCriterion("business_org_id =", value, "businessOrgId");
            return (Criteria) this;
        }

        public Criteria andBusinessOrgIdNotEqualTo(Long value) {
            addCriterion("business_org_id <>", value, "businessOrgId");
            return (Criteria) this;
        }

        public Criteria andBusinessOrgIdGreaterThan(Long value) {
            addCriterion("business_org_id >", value, "businessOrgId");
            return (Criteria) this;
        }

        public Criteria andBusinessOrgIdGreaterThanOrEqualTo(Long value) {
            addCriterion("business_org_id >=", value, "businessOrgId");
            return (Criteria) this;
        }

        public Criteria andBusinessOrgIdLessThan(Long value) {
            addCriterion("business_org_id <", value, "businessOrgId");
            return (Criteria) this;
        }

        public Criteria andBusinessOrgIdLessThanOrEqualTo(Long value) {
            addCriterion("business_org_id <=", value, "businessOrgId");
            return (Criteria) this;
        }

        public Criteria andBusinessOrgIdIn(List<Long> values) {
            addCriterion("business_org_id in", values, "businessOrgId");
            return (Criteria) this;
        }

        public Criteria andBusinessOrgIdNotIn(List<Long> values) {
            addCriterion("business_org_id not in", values, "businessOrgId");
            return (Criteria) this;
        }

        public Criteria andBusinessOrgIdBetween(Long value1, Long value2) {
            addCriterion("business_org_id between", value1, value2, "businessOrgId");
            return (Criteria) this;
        }

        public Criteria andBusinessOrgIdNotBetween(Long value1, Long value2) {
            addCriterion("business_org_id not between", value1, value2, "businessOrgId");
            return (Criteria) this;
        }

        public Criteria andStoreIdIsNull() {
            addCriterion("store_id is null");
            return (Criteria) this;
        }

        public Criteria andStoreIdIsNotNull() {
            addCriterion("store_id is not null");
            return (Criteria) this;
        }

        public Criteria andStoreIdEqualTo(Long value) {
            addCriterion("store_id =", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdNotEqualTo(Long value) {
            addCriterion("store_id <>", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdGreaterThan(Long value) {
            addCriterion("store_id >", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdGreaterThanOrEqualTo(Long value) {
            addCriterion("store_id >=", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdLessThan(Long value) {
            addCriterion("store_id <", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdLessThanOrEqualTo(Long value) {
            addCriterion("store_id <=", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdIn(List<Long> values) {
            addCriterion("store_id in", values, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdNotIn(List<Long> values) {
            addCriterion("store_id not in", values, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdBetween(Long value1, Long value2) {
            addCriterion("store_id between", value1, value2, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdNotBetween(Long value1, Long value2) {
            addCriterion("store_id not between", value1, value2, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreCodeIsNull() {
            addCriterion("store_code is null");
            return (Criteria) this;
        }

        public Criteria andStoreCodeIsNotNull() {
            addCriterion("store_code is not null");
            return (Criteria) this;
        }

        public Criteria andStoreCodeEqualTo(String value) {
            addCriterion("store_code =", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotEqualTo(String value) {
            addCriterion("store_code <>", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeGreaterThan(String value) {
            addCriterion("store_code >", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeGreaterThanOrEqualTo(String value) {
            addCriterion("store_code >=", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeLessThan(String value) {
            addCriterion("store_code <", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeLessThanOrEqualTo(String value) {
            addCriterion("store_code <=", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeLike(String value) {
            addCriterion("store_code like", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotLike(String value) {
            addCriterion("store_code not like", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeIn(List<String> values) {
            addCriterion("store_code in", values, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotIn(List<String> values) {
            addCriterion("store_code not in", values, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeBetween(String value1, String value2) {
            addCriterion("store_code between", value1, value2, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotBetween(String value1, String value2) {
            addCriterion("store_code not between", value1, value2, "storeCode");
            return (Criteria) this;
        }

        public Criteria andGoodsNoIsNull() {
            addCriterion("goods_no is null");
            return (Criteria) this;
        }

        public Criteria andGoodsNoIsNotNull() {
            addCriterion("goods_no is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsNoEqualTo(String value) {
            addCriterion("goods_no =", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotEqualTo(String value) {
            addCriterion("goods_no <>", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoGreaterThan(String value) {
            addCriterion("goods_no >", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoGreaterThanOrEqualTo(String value) {
            addCriterion("goods_no >=", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoLessThan(String value) {
            addCriterion("goods_no <", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoLessThanOrEqualTo(String value) {
            addCriterion("goods_no <=", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoLike(String value) {
            addCriterion("goods_no like", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotLike(String value) {
            addCriterion("goods_no not like", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoIn(List<String> values) {
            addCriterion("goods_no in", values, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotIn(List<String> values) {
            addCriterion("goods_no not in", values, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoBetween(String value1, String value2) {
            addCriterion("goods_no between", value1, value2, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotBetween(String value1, String value2) {
            addCriterion("goods_no not between", value1, value2, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNameIsNull() {
            addCriterion("goods_name is null");
            return (Criteria) this;
        }

        public Criteria andGoodsNameIsNotNull() {
            addCriterion("goods_name is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsNameEqualTo(String value) {
            addCriterion("goods_name =", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameNotEqualTo(String value) {
            addCriterion("goods_name <>", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameGreaterThan(String value) {
            addCriterion("goods_name >", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameGreaterThanOrEqualTo(String value) {
            addCriterion("goods_name >=", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameLessThan(String value) {
            addCriterion("goods_name <", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameLessThanOrEqualTo(String value) {
            addCriterion("goods_name <=", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameLike(String value) {
            addCriterion("goods_name like", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameNotLike(String value) {
            addCriterion("goods_name not like", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameIn(List<String> values) {
            addCriterion("goods_name in", values, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameNotIn(List<String> values) {
            addCriterion("goods_name not in", values, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameBetween(String value1, String value2) {
            addCriterion("goods_name between", value1, value2, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameNotBetween(String value1, String value2) {
            addCriterion("goods_name not between", value1, value2, "goodsName");
            return (Criteria) this;
        }

        public Criteria andSuggestManageStatusIsNull() {
            addCriterion("suggest_manage_status is null");
            return (Criteria) this;
        }

        public Criteria andSuggestManageStatusIsNotNull() {
            addCriterion("suggest_manage_status is not null");
            return (Criteria) this;
        }

        public Criteria andSuggestManageStatusEqualTo(String value) {
            addCriterion("suggest_manage_status =", value, "suggestManageStatus");
            return (Criteria) this;
        }

        public Criteria andSuggestManageStatusNotEqualTo(String value) {
            addCriterion("suggest_manage_status <>", value, "suggestManageStatus");
            return (Criteria) this;
        }

        public Criteria andSuggestManageStatusGreaterThan(String value) {
            addCriterion("suggest_manage_status >", value, "suggestManageStatus");
            return (Criteria) this;
        }

        public Criteria andSuggestManageStatusGreaterThanOrEqualTo(String value) {
            addCriterion("suggest_manage_status >=", value, "suggestManageStatus");
            return (Criteria) this;
        }

        public Criteria andSuggestManageStatusLessThan(String value) {
            addCriterion("suggest_manage_status <", value, "suggestManageStatus");
            return (Criteria) this;
        }

        public Criteria andSuggestManageStatusLessThanOrEqualTo(String value) {
            addCriterion("suggest_manage_status <=", value, "suggestManageStatus");
            return (Criteria) this;
        }

        public Criteria andSuggestManageStatusLike(String value) {
            addCriterion("suggest_manage_status like", value, "suggestManageStatus");
            return (Criteria) this;
        }

        public Criteria andSuggestManageStatusNotLike(String value) {
            addCriterion("suggest_manage_status not like", value, "suggestManageStatus");
            return (Criteria) this;
        }

        public Criteria andSuggestManageStatusIn(List<String> values) {
            addCriterion("suggest_manage_status in", values, "suggestManageStatus");
            return (Criteria) this;
        }

        public Criteria andSuggestManageStatusNotIn(List<String> values) {
            addCriterion("suggest_manage_status not in", values, "suggestManageStatus");
            return (Criteria) this;
        }

        public Criteria andSuggestManageStatusBetween(String value1, String value2) {
            addCriterion("suggest_manage_status between", value1, value2, "suggestManageStatus");
            return (Criteria) this;
        }

        public Criteria andSuggestManageStatusNotBetween(String value1, String value2) {
            addCriterion("suggest_manage_status not between", value1, value2, "suggestManageStatus");
            return (Criteria) this;
        }

        public Criteria andSuggestManageStatusNameIsNull() {
            addCriterion("suggest_manage_status_name is null");
            return (Criteria) this;
        }

        public Criteria andSuggestManageStatusNameIsNotNull() {
            addCriterion("suggest_manage_status_name is not null");
            return (Criteria) this;
        }

        public Criteria andSuggestManageStatusNameEqualTo(String value) {
            addCriterion("suggest_manage_status_name =", value, "suggestManageStatusName");
            return (Criteria) this;
        }

        public Criteria andSuggestManageStatusNameNotEqualTo(String value) {
            addCriterion("suggest_manage_status_name <>", value, "suggestManageStatusName");
            return (Criteria) this;
        }

        public Criteria andSuggestManageStatusNameGreaterThan(String value) {
            addCriterion("suggest_manage_status_name >", value, "suggestManageStatusName");
            return (Criteria) this;
        }

        public Criteria andSuggestManageStatusNameGreaterThanOrEqualTo(String value) {
            addCriterion("suggest_manage_status_name >=", value, "suggestManageStatusName");
            return (Criteria) this;
        }

        public Criteria andSuggestManageStatusNameLessThan(String value) {
            addCriterion("suggest_manage_status_name <", value, "suggestManageStatusName");
            return (Criteria) this;
        }

        public Criteria andSuggestManageStatusNameLessThanOrEqualTo(String value) {
            addCriterion("suggest_manage_status_name <=", value, "suggestManageStatusName");
            return (Criteria) this;
        }

        public Criteria andSuggestManageStatusNameLike(String value) {
            addCriterion("suggest_manage_status_name like", value, "suggestManageStatusName");
            return (Criteria) this;
        }

        public Criteria andSuggestManageStatusNameNotLike(String value) {
            addCriterion("suggest_manage_status_name not like", value, "suggestManageStatusName");
            return (Criteria) this;
        }

        public Criteria andSuggestManageStatusNameIn(List<String> values) {
            addCriterion("suggest_manage_status_name in", values, "suggestManageStatusName");
            return (Criteria) this;
        }

        public Criteria andSuggestManageStatusNameNotIn(List<String> values) {
            addCriterion("suggest_manage_status_name not in", values, "suggestManageStatusName");
            return (Criteria) this;
        }

        public Criteria andSuggestManageStatusNameBetween(String value1, String value2) {
            addCriterion("suggest_manage_status_name between", value1, value2, "suggestManageStatusName");
            return (Criteria) this;
        }

        public Criteria andSuggestManageStatusNameNotBetween(String value1, String value2) {
            addCriterion("suggest_manage_status_name not between", value1, value2, "suggestManageStatusName");
            return (Criteria) this;
        }

        public Criteria andCategoryIsNull() {
            addCriterion("category is null");
            return (Criteria) this;
        }

        public Criteria andCategoryIsNotNull() {
            addCriterion("category is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryEqualTo(String value) {
            addCriterion("category =", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryNotEqualTo(String value) {
            addCriterion("category <>", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryGreaterThan(String value) {
            addCriterion("category >", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryGreaterThanOrEqualTo(String value) {
            addCriterion("category >=", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryLessThan(String value) {
            addCriterion("category <", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryLessThanOrEqualTo(String value) {
            addCriterion("category <=", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryLike(String value) {
            addCriterion("category like", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryNotLike(String value) {
            addCriterion("category not like", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryIn(List<String> values) {
            addCriterion("category in", values, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryNotIn(List<String> values) {
            addCriterion("category not in", values, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryBetween(String value1, String value2) {
            addCriterion("category between", value1, value2, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryNotBetween(String value1, String value2) {
            addCriterion("category not between", value1, value2, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryNameIsNull() {
            addCriterion("category_name is null");
            return (Criteria) this;
        }

        public Criteria andCategoryNameIsNotNull() {
            addCriterion("category_name is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryNameEqualTo(String value) {
            addCriterion("category_name =", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameNotEqualTo(String value) {
            addCriterion("category_name <>", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameGreaterThan(String value) {
            addCriterion("category_name >", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameGreaterThanOrEqualTo(String value) {
            addCriterion("category_name >=", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameLessThan(String value) {
            addCriterion("category_name <", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameLessThanOrEqualTo(String value) {
            addCriterion("category_name <=", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameLike(String value) {
            addCriterion("category_name like", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameNotLike(String value) {
            addCriterion("category_name not like", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameIn(List<String> values) {
            addCriterion("category_name in", values, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameNotIn(List<String> values) {
            addCriterion("category_name not in", values, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameBetween(String value1, String value2) {
            addCriterion("category_name between", value1, value2, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameNotBetween(String value1, String value2) {
            addCriterion("category_name not between", value1, value2, "categoryName");
            return (Criteria) this;
        }

        public Criteria andRxOtcIsNull() {
            addCriterion("rx_otc is null");
            return (Criteria) this;
        }

        public Criteria andRxOtcIsNotNull() {
            addCriterion("rx_otc is not null");
            return (Criteria) this;
        }

        public Criteria andRxOtcEqualTo(String value) {
            addCriterion("rx_otc =", value, "rxOtc");
            return (Criteria) this;
        }

        public Criteria andRxOtcNotEqualTo(String value) {
            addCriterion("rx_otc <>", value, "rxOtc");
            return (Criteria) this;
        }

        public Criteria andRxOtcGreaterThan(String value) {
            addCriterion("rx_otc >", value, "rxOtc");
            return (Criteria) this;
        }

        public Criteria andRxOtcGreaterThanOrEqualTo(String value) {
            addCriterion("rx_otc >=", value, "rxOtc");
            return (Criteria) this;
        }

        public Criteria andRxOtcLessThan(String value) {
            addCriterion("rx_otc <", value, "rxOtc");
            return (Criteria) this;
        }

        public Criteria andRxOtcLessThanOrEqualTo(String value) {
            addCriterion("rx_otc <=", value, "rxOtc");
            return (Criteria) this;
        }

        public Criteria andRxOtcLike(String value) {
            addCriterion("rx_otc like", value, "rxOtc");
            return (Criteria) this;
        }

        public Criteria andRxOtcNotLike(String value) {
            addCriterion("rx_otc not like", value, "rxOtc");
            return (Criteria) this;
        }

        public Criteria andRxOtcIn(List<String> values) {
            addCriterion("rx_otc in", values, "rxOtc");
            return (Criteria) this;
        }

        public Criteria andRxOtcNotIn(List<String> values) {
            addCriterion("rx_otc not in", values, "rxOtc");
            return (Criteria) this;
        }

        public Criteria andRxOtcBetween(String value1, String value2) {
            addCriterion("rx_otc between", value1, value2, "rxOtc");
            return (Criteria) this;
        }

        public Criteria andRxOtcNotBetween(String value1, String value2) {
            addCriterion("rx_otc not between", value1, value2, "rxOtc");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryIsNull() {
            addCriterion("middle_category is null");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryIsNotNull() {
            addCriterion("middle_category is not null");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryEqualTo(String value) {
            addCriterion("middle_category =", value, "middleCategory");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNotEqualTo(String value) {
            addCriterion("middle_category <>", value, "middleCategory");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryGreaterThan(String value) {
            addCriterion("middle_category >", value, "middleCategory");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryGreaterThanOrEqualTo(String value) {
            addCriterion("middle_category >=", value, "middleCategory");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryLessThan(String value) {
            addCriterion("middle_category <", value, "middleCategory");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryLessThanOrEqualTo(String value) {
            addCriterion("middle_category <=", value, "middleCategory");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryLike(String value) {
            addCriterion("middle_category like", value, "middleCategory");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNotLike(String value) {
            addCriterion("middle_category not like", value, "middleCategory");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryIn(List<String> values) {
            addCriterion("middle_category in", values, "middleCategory");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNotIn(List<String> values) {
            addCriterion("middle_category not in", values, "middleCategory");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryBetween(String value1, String value2) {
            addCriterion("middle_category between", value1, value2, "middleCategory");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNotBetween(String value1, String value2) {
            addCriterion("middle_category not between", value1, value2, "middleCategory");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameIsNull() {
            addCriterion("middle_category_name is null");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameIsNotNull() {
            addCriterion("middle_category_name is not null");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameEqualTo(String value) {
            addCriterion("middle_category_name =", value, "middleCategoryName");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameNotEqualTo(String value) {
            addCriterion("middle_category_name <>", value, "middleCategoryName");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameGreaterThan(String value) {
            addCriterion("middle_category_name >", value, "middleCategoryName");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameGreaterThanOrEqualTo(String value) {
            addCriterion("middle_category_name >=", value, "middleCategoryName");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameLessThan(String value) {
            addCriterion("middle_category_name <", value, "middleCategoryName");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameLessThanOrEqualTo(String value) {
            addCriterion("middle_category_name <=", value, "middleCategoryName");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameLike(String value) {
            addCriterion("middle_category_name like", value, "middleCategoryName");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameNotLike(String value) {
            addCriterion("middle_category_name not like", value, "middleCategoryName");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameIn(List<String> values) {
            addCriterion("middle_category_name in", values, "middleCategoryName");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameNotIn(List<String> values) {
            addCriterion("middle_category_name not in", values, "middleCategoryName");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameBetween(String value1, String value2) {
            addCriterion("middle_category_name between", value1, value2, "middleCategoryName");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryNameNotBetween(String value1, String value2) {
            addCriterion("middle_category_name not between", value1, value2, "middleCategoryName");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryIsNull() {
            addCriterion("small_category is null");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryIsNotNull() {
            addCriterion("small_category is not null");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryEqualTo(String value) {
            addCriterion("small_category =", value, "smallCategory");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNotEqualTo(String value) {
            addCriterion("small_category <>", value, "smallCategory");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryGreaterThan(String value) {
            addCriterion("small_category >", value, "smallCategory");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryGreaterThanOrEqualTo(String value) {
            addCriterion("small_category >=", value, "smallCategory");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryLessThan(String value) {
            addCriterion("small_category <", value, "smallCategory");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryLessThanOrEqualTo(String value) {
            addCriterion("small_category <=", value, "smallCategory");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryLike(String value) {
            addCriterion("small_category like", value, "smallCategory");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNotLike(String value) {
            addCriterion("small_category not like", value, "smallCategory");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryIn(List<String> values) {
            addCriterion("small_category in", values, "smallCategory");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNotIn(List<String> values) {
            addCriterion("small_category not in", values, "smallCategory");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryBetween(String value1, String value2) {
            addCriterion("small_category between", value1, value2, "smallCategory");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNotBetween(String value1, String value2) {
            addCriterion("small_category not between", value1, value2, "smallCategory");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameIsNull() {
            addCriterion("small_category_name is null");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameIsNotNull() {
            addCriterion("small_category_name is not null");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameEqualTo(String value) {
            addCriterion("small_category_name =", value, "smallCategoryName");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameNotEqualTo(String value) {
            addCriterion("small_category_name <>", value, "smallCategoryName");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameGreaterThan(String value) {
            addCriterion("small_category_name >", value, "smallCategoryName");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameGreaterThanOrEqualTo(String value) {
            addCriterion("small_category_name >=", value, "smallCategoryName");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameLessThan(String value) {
            addCriterion("small_category_name <", value, "smallCategoryName");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameLessThanOrEqualTo(String value) {
            addCriterion("small_category_name <=", value, "smallCategoryName");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameLike(String value) {
            addCriterion("small_category_name like", value, "smallCategoryName");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameNotLike(String value) {
            addCriterion("small_category_name not like", value, "smallCategoryName");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameIn(List<String> values) {
            addCriterion("small_category_name in", values, "smallCategoryName");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameNotIn(List<String> values) {
            addCriterion("small_category_name not in", values, "smallCategoryName");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameBetween(String value1, String value2) {
            addCriterion("small_category_name between", value1, value2, "smallCategoryName");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryNameNotBetween(String value1, String value2) {
            addCriterion("small_category_name not between", value1, value2, "smallCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIsNull() {
            addCriterion("sub_category is null");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIsNotNull() {
            addCriterion("sub_category is not null");
            return (Criteria) this;
        }

        public Criteria andSubCategoryEqualTo(String value) {
            addCriterion("sub_category =", value, "subCategory");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNotEqualTo(String value) {
            addCriterion("sub_category <>", value, "subCategory");
            return (Criteria) this;
        }

        public Criteria andSubCategoryGreaterThan(String value) {
            addCriterion("sub_category >", value, "subCategory");
            return (Criteria) this;
        }

        public Criteria andSubCategoryGreaterThanOrEqualTo(String value) {
            addCriterion("sub_category >=", value, "subCategory");
            return (Criteria) this;
        }

        public Criteria andSubCategoryLessThan(String value) {
            addCriterion("sub_category <", value, "subCategory");
            return (Criteria) this;
        }

        public Criteria andSubCategoryLessThanOrEqualTo(String value) {
            addCriterion("sub_category <=", value, "subCategory");
            return (Criteria) this;
        }

        public Criteria andSubCategoryLike(String value) {
            addCriterion("sub_category like", value, "subCategory");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNotLike(String value) {
            addCriterion("sub_category not like", value, "subCategory");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIn(List<String> values) {
            addCriterion("sub_category in", values, "subCategory");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNotIn(List<String> values) {
            addCriterion("sub_category not in", values, "subCategory");
            return (Criteria) this;
        }

        public Criteria andSubCategoryBetween(String value1, String value2) {
            addCriterion("sub_category between", value1, value2, "subCategory");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNotBetween(String value1, String value2) {
            addCriterion("sub_category not between", value1, value2, "subCategory");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameIsNull() {
            addCriterion("sub_category_name is null");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameIsNotNull() {
            addCriterion("sub_category_name is not null");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameEqualTo(String value) {
            addCriterion("sub_category_name =", value, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameNotEqualTo(String value) {
            addCriterion("sub_category_name <>", value, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameGreaterThan(String value) {
            addCriterion("sub_category_name >", value, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameGreaterThanOrEqualTo(String value) {
            addCriterion("sub_category_name >=", value, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameLessThan(String value) {
            addCriterion("sub_category_name <", value, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameLessThanOrEqualTo(String value) {
            addCriterion("sub_category_name <=", value, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameLike(String value) {
            addCriterion("sub_category_name like", value, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameNotLike(String value) {
            addCriterion("sub_category_name not like", value, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameIn(List<String> values) {
            addCriterion("sub_category_name in", values, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameNotIn(List<String> values) {
            addCriterion("sub_category_name not in", values, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameBetween(String value1, String value2) {
            addCriterion("sub_category_name between", value1, value2, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andSubCategoryNameNotBetween(String value1, String value2) {
            addCriterion("sub_category_name not between", value1, value2, "subCategoryName");
            return (Criteria) this;
        }

        public Criteria andComponentIsNull() {
            addCriterion("component is null");
            return (Criteria) this;
        }

        public Criteria andComponentIsNotNull() {
            addCriterion("component is not null");
            return (Criteria) this;
        }

        public Criteria andComponentEqualTo(String value) {
            addCriterion("component =", value, "component");
            return (Criteria) this;
        }

        public Criteria andComponentNotEqualTo(String value) {
            addCriterion("component <>", value, "component");
            return (Criteria) this;
        }

        public Criteria andComponentGreaterThan(String value) {
            addCriterion("component >", value, "component");
            return (Criteria) this;
        }

        public Criteria andComponentGreaterThanOrEqualTo(String value) {
            addCriterion("component >=", value, "component");
            return (Criteria) this;
        }

        public Criteria andComponentLessThan(String value) {
            addCriterion("component <", value, "component");
            return (Criteria) this;
        }

        public Criteria andComponentLessThanOrEqualTo(String value) {
            addCriterion("component <=", value, "component");
            return (Criteria) this;
        }

        public Criteria andComponentLike(String value) {
            addCriterion("component like", value, "component");
            return (Criteria) this;
        }

        public Criteria andComponentNotLike(String value) {
            addCriterion("component not like", value, "component");
            return (Criteria) this;
        }

        public Criteria andComponentIn(List<String> values) {
            addCriterion("component in", values, "component");
            return (Criteria) this;
        }

        public Criteria andComponentNotIn(List<String> values) {
            addCriterion("component not in", values, "component");
            return (Criteria) this;
        }

        public Criteria andComponentBetween(String value1, String value2) {
            addCriterion("component between", value1, value2, "component");
            return (Criteria) this;
        }

        public Criteria andComponentNotBetween(String value1, String value2) {
            addCriterion("component not between", value1, value2, "component");
            return (Criteria) this;
        }

        public Criteria andStockQuantityIsNull() {
            addCriterion("stock_quantity is null");
            return (Criteria) this;
        }

        public Criteria andStockQuantityIsNotNull() {
            addCriterion("stock_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andStockQuantityEqualTo(String value) {
            addCriterion("stock_quantity =", value, "stockQuantity");
            return (Criteria) this;
        }

        public Criteria andStockQuantityNotEqualTo(String value) {
            addCriterion("stock_quantity <>", value, "stockQuantity");
            return (Criteria) this;
        }

        public Criteria andStockQuantityGreaterThan(String value) {
            addCriterion("stock_quantity >", value, "stockQuantity");
            return (Criteria) this;
        }

        public Criteria andStockQuantityGreaterThanOrEqualTo(String value) {
            addCriterion("stock_quantity >=", value, "stockQuantity");
            return (Criteria) this;
        }

        public Criteria andStockQuantityLessThan(String value) {
            addCriterion("stock_quantity <", value, "stockQuantity");
            return (Criteria) this;
        }

        public Criteria andStockQuantityLessThanOrEqualTo(String value) {
            addCriterion("stock_quantity <=", value, "stockQuantity");
            return (Criteria) this;
        }

        public Criteria andStockQuantityLike(String value) {
            addCriterion("stock_quantity like", value, "stockQuantity");
            return (Criteria) this;
        }

        public Criteria andStockQuantityNotLike(String value) {
            addCriterion("stock_quantity not like", value, "stockQuantity");
            return (Criteria) this;
        }

        public Criteria andStockQuantityIn(List<String> values) {
            addCriterion("stock_quantity in", values, "stockQuantity");
            return (Criteria) this;
        }

        public Criteria andStockQuantityNotIn(List<String> values) {
            addCriterion("stock_quantity not in", values, "stockQuantity");
            return (Criteria) this;
        }

        public Criteria andStockQuantityBetween(String value1, String value2) {
            addCriterion("stock_quantity between", value1, value2, "stockQuantity");
            return (Criteria) this;
        }

        public Criteria andStockQuantityNotBetween(String value1, String value2) {
            addCriterion("stock_quantity not between", value1, value2, "stockQuantity");
            return (Criteria) this;
        }

        public Criteria andSalesLast30dIsNull() {
            addCriterion("sales_last_30d is null");
            return (Criteria) this;
        }

        public Criteria andSalesLast30dIsNotNull() {
            addCriterion("sales_last_30d is not null");
            return (Criteria) this;
        }

        public Criteria andSalesLast30dEqualTo(String value) {
            addCriterion("sales_last_30d =", value, "salesLast30d");
            return (Criteria) this;
        }

        public Criteria andSalesLast30dNotEqualTo(String value) {
            addCriterion("sales_last_30d <>", value, "salesLast30d");
            return (Criteria) this;
        }

        public Criteria andSalesLast30dGreaterThan(String value) {
            addCriterion("sales_last_30d >", value, "salesLast30d");
            return (Criteria) this;
        }

        public Criteria andSalesLast30dGreaterThanOrEqualTo(String value) {
            addCriterion("sales_last_30d >=", value, "salesLast30d");
            return (Criteria) this;
        }

        public Criteria andSalesLast30dLessThan(String value) {
            addCriterion("sales_last_30d <", value, "salesLast30d");
            return (Criteria) this;
        }

        public Criteria andSalesLast30dLessThanOrEqualTo(String value) {
            addCriterion("sales_last_30d <=", value, "salesLast30d");
            return (Criteria) this;
        }

        public Criteria andSalesLast30dLike(String value) {
            addCriterion("sales_last_30d like", value, "salesLast30d");
            return (Criteria) this;
        }

        public Criteria andSalesLast30dNotLike(String value) {
            addCriterion("sales_last_30d not like", value, "salesLast30d");
            return (Criteria) this;
        }

        public Criteria andSalesLast30dIn(List<String> values) {
            addCriterion("sales_last_30d in", values, "salesLast30d");
            return (Criteria) this;
        }

        public Criteria andSalesLast30dNotIn(List<String> values) {
            addCriterion("sales_last_30d not in", values, "salesLast30d");
            return (Criteria) this;
        }

        public Criteria andSalesLast30dBetween(String value1, String value2) {
            addCriterion("sales_last_30d between", value1, value2, "salesLast30d");
            return (Criteria) this;
        }

        public Criteria andSalesLast30dNotBetween(String value1, String value2) {
            addCriterion("sales_last_30d not between", value1, value2, "salesLast30d");
            return (Criteria) this;
        }

        public Criteria andSalesLast60dIsNull() {
            addCriterion("sales_last_60d is null");
            return (Criteria) this;
        }

        public Criteria andSalesLast60dIsNotNull() {
            addCriterion("sales_last_60d is not null");
            return (Criteria) this;
        }

        public Criteria andSalesLast60dEqualTo(String value) {
            addCriterion("sales_last_60d =", value, "salesLast60d");
            return (Criteria) this;
        }

        public Criteria andSalesLast60dNotEqualTo(String value) {
            addCriterion("sales_last_60d <>", value, "salesLast60d");
            return (Criteria) this;
        }

        public Criteria andSalesLast60dGreaterThan(String value) {
            addCriterion("sales_last_60d >", value, "salesLast60d");
            return (Criteria) this;
        }

        public Criteria andSalesLast60dGreaterThanOrEqualTo(String value) {
            addCriterion("sales_last_60d >=", value, "salesLast60d");
            return (Criteria) this;
        }

        public Criteria andSalesLast60dLessThan(String value) {
            addCriterion("sales_last_60d <", value, "salesLast60d");
            return (Criteria) this;
        }

        public Criteria andSalesLast60dLessThanOrEqualTo(String value) {
            addCriterion("sales_last_60d <=", value, "salesLast60d");
            return (Criteria) this;
        }

        public Criteria andSalesLast60dLike(String value) {
            addCriterion("sales_last_60d like", value, "salesLast60d");
            return (Criteria) this;
        }

        public Criteria andSalesLast60dNotLike(String value) {
            addCriterion("sales_last_60d not like", value, "salesLast60d");
            return (Criteria) this;
        }

        public Criteria andSalesLast60dIn(List<String> values) {
            addCriterion("sales_last_60d in", values, "salesLast60d");
            return (Criteria) this;
        }

        public Criteria andSalesLast60dNotIn(List<String> values) {
            addCriterion("sales_last_60d not in", values, "salesLast60d");
            return (Criteria) this;
        }

        public Criteria andSalesLast60dBetween(String value1, String value2) {
            addCriterion("sales_last_60d between", value1, value2, "salesLast60d");
            return (Criteria) this;
        }

        public Criteria andSalesLast60dNotBetween(String value1, String value2) {
            addCriterion("sales_last_60d not between", value1, value2, "salesLast60d");
            return (Criteria) this;
        }

        public Criteria andSalesLast90dIsNull() {
            addCriterion("sales_last_90d is null");
            return (Criteria) this;
        }

        public Criteria andSalesLast90dIsNotNull() {
            addCriterion("sales_last_90d is not null");
            return (Criteria) this;
        }

        public Criteria andSalesLast90dEqualTo(String value) {
            addCriterion("sales_last_90d =", value, "salesLast90d");
            return (Criteria) this;
        }

        public Criteria andSalesLast90dNotEqualTo(String value) {
            addCriterion("sales_last_90d <>", value, "salesLast90d");
            return (Criteria) this;
        }

        public Criteria andSalesLast90dGreaterThan(String value) {
            addCriterion("sales_last_90d >", value, "salesLast90d");
            return (Criteria) this;
        }

        public Criteria andSalesLast90dGreaterThanOrEqualTo(String value) {
            addCriterion("sales_last_90d >=", value, "salesLast90d");
            return (Criteria) this;
        }

        public Criteria andSalesLast90dLessThan(String value) {
            addCriterion("sales_last_90d <", value, "salesLast90d");
            return (Criteria) this;
        }

        public Criteria andSalesLast90dLessThanOrEqualTo(String value) {
            addCriterion("sales_last_90d <=", value, "salesLast90d");
            return (Criteria) this;
        }

        public Criteria andSalesLast90dLike(String value) {
            addCriterion("sales_last_90d like", value, "salesLast90d");
            return (Criteria) this;
        }

        public Criteria andSalesLast90dNotLike(String value) {
            addCriterion("sales_last_90d not like", value, "salesLast90d");
            return (Criteria) this;
        }

        public Criteria andSalesLast90dIn(List<String> values) {
            addCriterion("sales_last_90d in", values, "salesLast90d");
            return (Criteria) this;
        }

        public Criteria andSalesLast90dNotIn(List<String> values) {
            addCriterion("sales_last_90d not in", values, "salesLast90d");
            return (Criteria) this;
        }

        public Criteria andSalesLast90dBetween(String value1, String value2) {
            addCriterion("sales_last_90d between", value1, value2, "salesLast90d");
            return (Criteria) this;
        }

        public Criteria andSalesLast90dNotBetween(String value1, String value2) {
            addCriterion("sales_last_90d not between", value1, value2, "salesLast90d");
            return (Criteria) this;
        }

        public Criteria andSalesLast180dIsNull() {
            addCriterion("sales_last_180d is null");
            return (Criteria) this;
        }

        public Criteria andSalesLast180dIsNotNull() {
            addCriterion("sales_last_180d is not null");
            return (Criteria) this;
        }

        public Criteria andSalesLast180dEqualTo(String value) {
            addCriterion("sales_last_180d =", value, "salesLast180d");
            return (Criteria) this;
        }

        public Criteria andSalesLast180dNotEqualTo(String value) {
            addCriterion("sales_last_180d <>", value, "salesLast180d");
            return (Criteria) this;
        }

        public Criteria andSalesLast180dGreaterThan(String value) {
            addCriterion("sales_last_180d >", value, "salesLast180d");
            return (Criteria) this;
        }

        public Criteria andSalesLast180dGreaterThanOrEqualTo(String value) {
            addCriterion("sales_last_180d >=", value, "salesLast180d");
            return (Criteria) this;
        }

        public Criteria andSalesLast180dLessThan(String value) {
            addCriterion("sales_last_180d <", value, "salesLast180d");
            return (Criteria) this;
        }

        public Criteria andSalesLast180dLessThanOrEqualTo(String value) {
            addCriterion("sales_last_180d <=", value, "salesLast180d");
            return (Criteria) this;
        }

        public Criteria andSalesLast180dLike(String value) {
            addCriterion("sales_last_180d like", value, "salesLast180d");
            return (Criteria) this;
        }

        public Criteria andSalesLast180dNotLike(String value) {
            addCriterion("sales_last_180d not like", value, "salesLast180d");
            return (Criteria) this;
        }

        public Criteria andSalesLast180dIn(List<String> values) {
            addCriterion("sales_last_180d in", values, "salesLast180d");
            return (Criteria) this;
        }

        public Criteria andSalesLast180dNotIn(List<String> values) {
            addCriterion("sales_last_180d not in", values, "salesLast180d");
            return (Criteria) this;
        }

        public Criteria andSalesLast180dBetween(String value1, String value2) {
            addCriterion("sales_last_180d between", value1, value2, "salesLast180d");
            return (Criteria) this;
        }

        public Criteria andSalesLast180dNotBetween(String value1, String value2) {
            addCriterion("sales_last_180d not between", value1, value2, "salesLast180d");
            return (Criteria) this;
        }

        public Criteria andSeasonalComponentIsNull() {
            addCriterion("seasonal_component is null");
            return (Criteria) this;
        }

        public Criteria andSeasonalComponentIsNotNull() {
            addCriterion("seasonal_component is not null");
            return (Criteria) this;
        }

        public Criteria andSeasonalComponentEqualTo(String value) {
            addCriterion("seasonal_component =", value, "seasonalComponent");
            return (Criteria) this;
        }

        public Criteria andSeasonalComponentNotEqualTo(String value) {
            addCriterion("seasonal_component <>", value, "seasonalComponent");
            return (Criteria) this;
        }

        public Criteria andSeasonalComponentGreaterThan(String value) {
            addCriterion("seasonal_component >", value, "seasonalComponent");
            return (Criteria) this;
        }

        public Criteria andSeasonalComponentGreaterThanOrEqualTo(String value) {
            addCriterion("seasonal_component >=", value, "seasonalComponent");
            return (Criteria) this;
        }

        public Criteria andSeasonalComponentLessThan(String value) {
            addCriterion("seasonal_component <", value, "seasonalComponent");
            return (Criteria) this;
        }

        public Criteria andSeasonalComponentLessThanOrEqualTo(String value) {
            addCriterion("seasonal_component <=", value, "seasonalComponent");
            return (Criteria) this;
        }

        public Criteria andSeasonalComponentLike(String value) {
            addCriterion("seasonal_component like", value, "seasonalComponent");
            return (Criteria) this;
        }

        public Criteria andSeasonalComponentNotLike(String value) {
            addCriterion("seasonal_component not like", value, "seasonalComponent");
            return (Criteria) this;
        }

        public Criteria andSeasonalComponentIn(List<String> values) {
            addCriterion("seasonal_component in", values, "seasonalComponent");
            return (Criteria) this;
        }

        public Criteria andSeasonalComponentNotIn(List<String> values) {
            addCriterion("seasonal_component not in", values, "seasonalComponent");
            return (Criteria) this;
        }

        public Criteria andSeasonalComponentBetween(String value1, String value2) {
            addCriterion("seasonal_component between", value1, value2, "seasonalComponent");
            return (Criteria) this;
        }

        public Criteria andSeasonalComponentNotBetween(String value1, String value2) {
            addCriterion("seasonal_component not between", value1, value2, "seasonalComponent");
            return (Criteria) this;
        }

        public Criteria andSalesLastYearSamePeriodLast90dIsNull() {
            addCriterion("sales_last_year_same_period_last_90d is null");
            return (Criteria) this;
        }

        public Criteria andSalesLastYearSamePeriodLast90dIsNotNull() {
            addCriterion("sales_last_year_same_period_last_90d is not null");
            return (Criteria) this;
        }

        public Criteria andSalesLastYearSamePeriodLast90dEqualTo(String value) {
            addCriterion("sales_last_year_same_period_last_90d =", value, "salesLastYearSamePeriodLast90d");
            return (Criteria) this;
        }

        public Criteria andSalesLastYearSamePeriodLast90dNotEqualTo(String value) {
            addCriterion("sales_last_year_same_period_last_90d <>", value, "salesLastYearSamePeriodLast90d");
            return (Criteria) this;
        }

        public Criteria andSalesLastYearSamePeriodLast90dGreaterThan(String value) {
            addCriterion("sales_last_year_same_period_last_90d >", value, "salesLastYearSamePeriodLast90d");
            return (Criteria) this;
        }

        public Criteria andSalesLastYearSamePeriodLast90dGreaterThanOrEqualTo(String value) {
            addCriterion("sales_last_year_same_period_last_90d >=", value, "salesLastYearSamePeriodLast90d");
            return (Criteria) this;
        }

        public Criteria andSalesLastYearSamePeriodLast90dLessThan(String value) {
            addCriterion("sales_last_year_same_period_last_90d <", value, "salesLastYearSamePeriodLast90d");
            return (Criteria) this;
        }

        public Criteria andSalesLastYearSamePeriodLast90dLessThanOrEqualTo(String value) {
            addCriterion("sales_last_year_same_period_last_90d <=", value, "salesLastYearSamePeriodLast90d");
            return (Criteria) this;
        }

        public Criteria andSalesLastYearSamePeriodLast90dLike(String value) {
            addCriterion("sales_last_year_same_period_last_90d like", value, "salesLastYearSamePeriodLast90d");
            return (Criteria) this;
        }

        public Criteria andSalesLastYearSamePeriodLast90dNotLike(String value) {
            addCriterion("sales_last_year_same_period_last_90d not like", value, "salesLastYearSamePeriodLast90d");
            return (Criteria) this;
        }

        public Criteria andSalesLastYearSamePeriodLast90dIn(List<String> values) {
            addCriterion("sales_last_year_same_period_last_90d in", values, "salesLastYearSamePeriodLast90d");
            return (Criteria) this;
        }

        public Criteria andSalesLastYearSamePeriodLast90dNotIn(List<String> values) {
            addCriterion("sales_last_year_same_period_last_90d not in", values, "salesLastYearSamePeriodLast90d");
            return (Criteria) this;
        }

        public Criteria andSalesLastYearSamePeriodLast90dBetween(String value1, String value2) {
            addCriterion("sales_last_year_same_period_last_90d between", value1, value2, "salesLastYearSamePeriodLast90d");
            return (Criteria) this;
        }

        public Criteria andSalesLastYearSamePeriodLast90dNotBetween(String value1, String value2) {
            addCriterion("sales_last_year_same_period_last_90d not between", value1, value2, "salesLastYearSamePeriodLast90d");
            return (Criteria) this;
        }

        public Criteria andCityStoreSalesRate90dIsNull() {
            addCriterion("city_store_sales_rate_90d is null");
            return (Criteria) this;
        }

        public Criteria andCityStoreSalesRate90dIsNotNull() {
            addCriterion("city_store_sales_rate_90d is not null");
            return (Criteria) this;
        }

        public Criteria andCityStoreSalesRate90dEqualTo(String value) {
            addCriterion("city_store_sales_rate_90d =", value, "cityStoreSalesRate90d");
            return (Criteria) this;
        }

        public Criteria andCityStoreSalesRate90dNotEqualTo(String value) {
            addCriterion("city_store_sales_rate_90d <>", value, "cityStoreSalesRate90d");
            return (Criteria) this;
        }

        public Criteria andCityStoreSalesRate90dGreaterThan(String value) {
            addCriterion("city_store_sales_rate_90d >", value, "cityStoreSalesRate90d");
            return (Criteria) this;
        }

        public Criteria andCityStoreSalesRate90dGreaterThanOrEqualTo(String value) {
            addCriterion("city_store_sales_rate_90d >=", value, "cityStoreSalesRate90d");
            return (Criteria) this;
        }

        public Criteria andCityStoreSalesRate90dLessThan(String value) {
            addCriterion("city_store_sales_rate_90d <", value, "cityStoreSalesRate90d");
            return (Criteria) this;
        }

        public Criteria andCityStoreSalesRate90dLessThanOrEqualTo(String value) {
            addCriterion("city_store_sales_rate_90d <=", value, "cityStoreSalesRate90d");
            return (Criteria) this;
        }

        public Criteria andCityStoreSalesRate90dLike(String value) {
            addCriterion("city_store_sales_rate_90d like", value, "cityStoreSalesRate90d");
            return (Criteria) this;
        }

        public Criteria andCityStoreSalesRate90dNotLike(String value) {
            addCriterion("city_store_sales_rate_90d not like", value, "cityStoreSalesRate90d");
            return (Criteria) this;
        }

        public Criteria andCityStoreSalesRate90dIn(List<String> values) {
            addCriterion("city_store_sales_rate_90d in", values, "cityStoreSalesRate90d");
            return (Criteria) this;
        }

        public Criteria andCityStoreSalesRate90dNotIn(List<String> values) {
            addCriterion("city_store_sales_rate_90d not in", values, "cityStoreSalesRate90d");
            return (Criteria) this;
        }

        public Criteria andCityStoreSalesRate90dBetween(String value1, String value2) {
            addCriterion("city_store_sales_rate_90d between", value1, value2, "cityStoreSalesRate90d");
            return (Criteria) this;
        }

        public Criteria andCityStoreSalesRate90dNotBetween(String value1, String value2) {
            addCriterion("city_store_sales_rate_90d not between", value1, value2, "cityStoreSalesRate90d");
            return (Criteria) this;
        }

        public Criteria andStoreTypeSalesRate90dIsNull() {
            addCriterion("store_type_sales_rate_90d is null");
            return (Criteria) this;
        }

        public Criteria andStoreTypeSalesRate90dIsNotNull() {
            addCriterion("store_type_sales_rate_90d is not null");
            return (Criteria) this;
        }

        public Criteria andStoreTypeSalesRate90dEqualTo(String value) {
            addCriterion("store_type_sales_rate_90d =", value, "storeTypeSalesRate90d");
            return (Criteria) this;
        }

        public Criteria andStoreTypeSalesRate90dNotEqualTo(String value) {
            addCriterion("store_type_sales_rate_90d <>", value, "storeTypeSalesRate90d");
            return (Criteria) this;
        }

        public Criteria andStoreTypeSalesRate90dGreaterThan(String value) {
            addCriterion("store_type_sales_rate_90d >", value, "storeTypeSalesRate90d");
            return (Criteria) this;
        }

        public Criteria andStoreTypeSalesRate90dGreaterThanOrEqualTo(String value) {
            addCriterion("store_type_sales_rate_90d >=", value, "storeTypeSalesRate90d");
            return (Criteria) this;
        }

        public Criteria andStoreTypeSalesRate90dLessThan(String value) {
            addCriterion("store_type_sales_rate_90d <", value, "storeTypeSalesRate90d");
            return (Criteria) this;
        }

        public Criteria andStoreTypeSalesRate90dLessThanOrEqualTo(String value) {
            addCriterion("store_type_sales_rate_90d <=", value, "storeTypeSalesRate90d");
            return (Criteria) this;
        }

        public Criteria andStoreTypeSalesRate90dLike(String value) {
            addCriterion("store_type_sales_rate_90d like", value, "storeTypeSalesRate90d");
            return (Criteria) this;
        }

        public Criteria andStoreTypeSalesRate90dNotLike(String value) {
            addCriterion("store_type_sales_rate_90d not like", value, "storeTypeSalesRate90d");
            return (Criteria) this;
        }

        public Criteria andStoreTypeSalesRate90dIn(List<String> values) {
            addCriterion("store_type_sales_rate_90d in", values, "storeTypeSalesRate90d");
            return (Criteria) this;
        }

        public Criteria andStoreTypeSalesRate90dNotIn(List<String> values) {
            addCriterion("store_type_sales_rate_90d not in", values, "storeTypeSalesRate90d");
            return (Criteria) this;
        }

        public Criteria andStoreTypeSalesRate90dBetween(String value1, String value2) {
            addCriterion("store_type_sales_rate_90d between", value1, value2, "storeTypeSalesRate90d");
            return (Criteria) this;
        }

        public Criteria andStoreTypeSalesRate90dNotBetween(String value1, String value2) {
            addCriterion("store_type_sales_rate_90d not between", value1, value2, "storeTypeSalesRate90d");
            return (Criteria) this;
        }

        public Criteria andDaysUnsoldIsNull() {
            addCriterion("days_unsold is null");
            return (Criteria) this;
        }

        public Criteria andDaysUnsoldIsNotNull() {
            addCriterion("days_unsold is not null");
            return (Criteria) this;
        }

        public Criteria andDaysUnsoldEqualTo(String value) {
            addCriterion("days_unsold =", value, "daysUnsold");
            return (Criteria) this;
        }

        public Criteria andDaysUnsoldNotEqualTo(String value) {
            addCriterion("days_unsold <>", value, "daysUnsold");
            return (Criteria) this;
        }

        public Criteria andDaysUnsoldGreaterThan(String value) {
            addCriterion("days_unsold >", value, "daysUnsold");
            return (Criteria) this;
        }

        public Criteria andDaysUnsoldGreaterThanOrEqualTo(String value) {
            addCriterion("days_unsold >=", value, "daysUnsold");
            return (Criteria) this;
        }

        public Criteria andDaysUnsoldLessThan(String value) {
            addCriterion("days_unsold <", value, "daysUnsold");
            return (Criteria) this;
        }

        public Criteria andDaysUnsoldLessThanOrEqualTo(String value) {
            addCriterion("days_unsold <=", value, "daysUnsold");
            return (Criteria) this;
        }

        public Criteria andDaysUnsoldLike(String value) {
            addCriterion("days_unsold like", value, "daysUnsold");
            return (Criteria) this;
        }

        public Criteria andDaysUnsoldNotLike(String value) {
            addCriterion("days_unsold not like", value, "daysUnsold");
            return (Criteria) this;
        }

        public Criteria andDaysUnsoldIn(List<String> values) {
            addCriterion("days_unsold in", values, "daysUnsold");
            return (Criteria) this;
        }

        public Criteria andDaysUnsoldNotIn(List<String> values) {
            addCriterion("days_unsold not in", values, "daysUnsold");
            return (Criteria) this;
        }

        public Criteria andDaysUnsoldBetween(String value1, String value2) {
            addCriterion("days_unsold between", value1, value2, "daysUnsold");
            return (Criteria) this;
        }

        public Criteria andDaysUnsoldNotBetween(String value1, String value2) {
            addCriterion("days_unsold not between", value1, value2, "daysUnsold");
            return (Criteria) this;
        }

        public Criteria andCityProductContributionRankIsNull() {
            addCriterion("city_product_contribution_rank is null");
            return (Criteria) this;
        }

        public Criteria andCityProductContributionRankIsNotNull() {
            addCriterion("city_product_contribution_rank is not null");
            return (Criteria) this;
        }

        public Criteria andCityProductContributionRankEqualTo(String value) {
            addCriterion("city_product_contribution_rank =", value, "cityProductContributionRank");
            return (Criteria) this;
        }

        public Criteria andCityProductContributionRankNotEqualTo(String value) {
            addCriterion("city_product_contribution_rank <>", value, "cityProductContributionRank");
            return (Criteria) this;
        }

        public Criteria andCityProductContributionRankGreaterThan(String value) {
            addCriterion("city_product_contribution_rank >", value, "cityProductContributionRank");
            return (Criteria) this;
        }

        public Criteria andCityProductContributionRankGreaterThanOrEqualTo(String value) {
            addCriterion("city_product_contribution_rank >=", value, "cityProductContributionRank");
            return (Criteria) this;
        }

        public Criteria andCityProductContributionRankLessThan(String value) {
            addCriterion("city_product_contribution_rank <", value, "cityProductContributionRank");
            return (Criteria) this;
        }

        public Criteria andCityProductContributionRankLessThanOrEqualTo(String value) {
            addCriterion("city_product_contribution_rank <=", value, "cityProductContributionRank");
            return (Criteria) this;
        }

        public Criteria andCityProductContributionRankLike(String value) {
            addCriterion("city_product_contribution_rank like", value, "cityProductContributionRank");
            return (Criteria) this;
        }

        public Criteria andCityProductContributionRankNotLike(String value) {
            addCriterion("city_product_contribution_rank not like", value, "cityProductContributionRank");
            return (Criteria) this;
        }

        public Criteria andCityProductContributionRankIn(List<String> values) {
            addCriterion("city_product_contribution_rank in", values, "cityProductContributionRank");
            return (Criteria) this;
        }

        public Criteria andCityProductContributionRankNotIn(List<String> values) {
            addCriterion("city_product_contribution_rank not in", values, "cityProductContributionRank");
            return (Criteria) this;
        }

        public Criteria andCityProductContributionRankBetween(String value1, String value2) {
            addCriterion("city_product_contribution_rank between", value1, value2, "cityProductContributionRank");
            return (Criteria) this;
        }

        public Criteria andCityProductContributionRankNotBetween(String value1, String value2) {
            addCriterion("city_product_contribution_rank not between", value1, value2, "cityProductContributionRank");
            return (Criteria) this;
        }

        public Criteria andCitySubcategoryContributionRankIsNull() {
            addCriterion("city_subcategory_contribution_rank is null");
            return (Criteria) this;
        }

        public Criteria andCitySubcategoryContributionRankIsNotNull() {
            addCriterion("city_subcategory_contribution_rank is not null");
            return (Criteria) this;
        }

        public Criteria andCitySubcategoryContributionRankEqualTo(String value) {
            addCriterion("city_subcategory_contribution_rank =", value, "citySubcategoryContributionRank");
            return (Criteria) this;
        }

        public Criteria andCitySubcategoryContributionRankNotEqualTo(String value) {
            addCriterion("city_subcategory_contribution_rank <>", value, "citySubcategoryContributionRank");
            return (Criteria) this;
        }

        public Criteria andCitySubcategoryContributionRankGreaterThan(String value) {
            addCriterion("city_subcategory_contribution_rank >", value, "citySubcategoryContributionRank");
            return (Criteria) this;
        }

        public Criteria andCitySubcategoryContributionRankGreaterThanOrEqualTo(String value) {
            addCriterion("city_subcategory_contribution_rank >=", value, "citySubcategoryContributionRank");
            return (Criteria) this;
        }

        public Criteria andCitySubcategoryContributionRankLessThan(String value) {
            addCriterion("city_subcategory_contribution_rank <", value, "citySubcategoryContributionRank");
            return (Criteria) this;
        }

        public Criteria andCitySubcategoryContributionRankLessThanOrEqualTo(String value) {
            addCriterion("city_subcategory_contribution_rank <=", value, "citySubcategoryContributionRank");
            return (Criteria) this;
        }

        public Criteria andCitySubcategoryContributionRankLike(String value) {
            addCriterion("city_subcategory_contribution_rank like", value, "citySubcategoryContributionRank");
            return (Criteria) this;
        }

        public Criteria andCitySubcategoryContributionRankNotLike(String value) {
            addCriterion("city_subcategory_contribution_rank not like", value, "citySubcategoryContributionRank");
            return (Criteria) this;
        }

        public Criteria andCitySubcategoryContributionRankIn(List<String> values) {
            addCriterion("city_subcategory_contribution_rank in", values, "citySubcategoryContributionRank");
            return (Criteria) this;
        }

        public Criteria andCitySubcategoryContributionRankNotIn(List<String> values) {
            addCriterion("city_subcategory_contribution_rank not in", values, "citySubcategoryContributionRank");
            return (Criteria) this;
        }

        public Criteria andCitySubcategoryContributionRankBetween(String value1, String value2) {
            addCriterion("city_subcategory_contribution_rank between", value1, value2, "citySubcategoryContributionRank");
            return (Criteria) this;
        }

        public Criteria andCitySubcategoryContributionRankNotBetween(String value1, String value2) {
            addCriterion("city_subcategory_contribution_rank not between", value1, value2, "citySubcategoryContributionRank");
            return (Criteria) this;
        }

        public Criteria andPriceLsjIsNull() {
            addCriterion("price_lsj is null");
            return (Criteria) this;
        }

        public Criteria andPriceLsjIsNotNull() {
            addCriterion("price_lsj is not null");
            return (Criteria) this;
        }

        public Criteria andPriceLsjEqualTo(String value) {
            addCriterion("price_lsj =", value, "priceLsj");
            return (Criteria) this;
        }

        public Criteria andPriceLsjNotEqualTo(String value) {
            addCriterion("price_lsj <>", value, "priceLsj");
            return (Criteria) this;
        }

        public Criteria andPriceLsjGreaterThan(String value) {
            addCriterion("price_lsj >", value, "priceLsj");
            return (Criteria) this;
        }

        public Criteria andPriceLsjGreaterThanOrEqualTo(String value) {
            addCriterion("price_lsj >=", value, "priceLsj");
            return (Criteria) this;
        }

        public Criteria andPriceLsjLessThan(String value) {
            addCriterion("price_lsj <", value, "priceLsj");
            return (Criteria) this;
        }

        public Criteria andPriceLsjLessThanOrEqualTo(String value) {
            addCriterion("price_lsj <=", value, "priceLsj");
            return (Criteria) this;
        }

        public Criteria andPriceLsjLike(String value) {
            addCriterion("price_lsj like", value, "priceLsj");
            return (Criteria) this;
        }

        public Criteria andPriceLsjNotLike(String value) {
            addCriterion("price_lsj not like", value, "priceLsj");
            return (Criteria) this;
        }

        public Criteria andPriceLsjIn(List<String> values) {
            addCriterion("price_lsj in", values, "priceLsj");
            return (Criteria) this;
        }

        public Criteria andPriceLsjNotIn(List<String> values) {
            addCriterion("price_lsj not in", values, "priceLsj");
            return (Criteria) this;
        }

        public Criteria andPriceLsjBetween(String value1, String value2) {
            addCriterion("price_lsj between", value1, value2, "priceLsj");
            return (Criteria) this;
        }

        public Criteria andPriceLsjNotBetween(String value1, String value2) {
            addCriterion("price_lsj not between", value1, value2, "priceLsj");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("`status` is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("`status` is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Byte value) {
            addCriterion("`status` =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Byte value) {
            addCriterion("`status` <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Byte value) {
            addCriterion("`status` >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("`status` >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Byte value) {
            addCriterion("`status` <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Byte value) {
            addCriterion("`status` <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Byte> values) {
            addCriterion("`status` in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Byte> values) {
            addCriterion("`status` not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Byte value1, Byte value2) {
            addCriterion("`status` between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("`status` not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIsNull() {
            addCriterion("gmt_update is null");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIsNotNull() {
            addCriterion("gmt_update is not null");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateEqualTo(Date value) {
            addCriterion("gmt_update =", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotEqualTo(Date value) {
            addCriterion("gmt_update <>", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateGreaterThan(Date value) {
            addCriterion("gmt_update >", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_update >=", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateLessThan(Date value) {
            addCriterion("gmt_update <", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_update <=", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIn(List<Date> values) {
            addCriterion("gmt_update in", values, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotIn(List<Date> values) {
            addCriterion("gmt_update not in", values, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateBetween(Date value1, Date value2) {
            addCriterion("gmt_update between", value1, value2, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_update not between", value1, value2, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andExtendIsNull() {
            addCriterion("extend is null");
            return (Criteria) this;
        }

        public Criteria andExtendIsNotNull() {
            addCriterion("extend is not null");
            return (Criteria) this;
        }

        public Criteria andExtendEqualTo(String value) {
            addCriterion("extend =", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotEqualTo(String value) {
            addCriterion("extend <>", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendGreaterThan(String value) {
            addCriterion("extend >", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendGreaterThanOrEqualTo(String value) {
            addCriterion("extend >=", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLessThan(String value) {
            addCriterion("extend <", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLessThanOrEqualTo(String value) {
            addCriterion("extend <=", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLike(String value) {
            addCriterion("extend like", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotLike(String value) {
            addCriterion("extend not like", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendIn(List<String> values) {
            addCriterion("extend in", values, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotIn(List<String> values) {
            addCriterion("extend not in", values, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendBetween(String value1, String value2) {
            addCriterion("extend between", value1, value2, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotBetween(String value1, String value2) {
            addCriterion("extend not between", value1, value2, "extend");
            return (Criteria) this;
        }

        public Criteria andVersionIsNull() {
            addCriterion("version is null");
            return (Criteria) this;
        }

        public Criteria andVersionIsNotNull() {
            addCriterion("version is not null");
            return (Criteria) this;
        }

        public Criteria andVersionEqualTo(Long value) {
            addCriterion("version =", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotEqualTo(Long value) {
            addCriterion("version <>", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThan(Long value) {
            addCriterion("version >", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThanOrEqualTo(Long value) {
            addCriterion("version >=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThan(Long value) {
            addCriterion("version <", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThanOrEqualTo(Long value) {
            addCriterion("version <=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionIn(List<Long> values) {
            addCriterion("version in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotIn(List<Long> values) {
            addCriterion("version not in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionBetween(Long value1, Long value2) {
            addCriterion("version between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotBetween(Long value1, Long value2) {
            addCriterion("version not between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("created_by is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("created_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(Long value) {
            addCriterion("created_by =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(Long value) {
            addCriterion("created_by <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(Long value) {
            addCriterion("created_by >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(Long value) {
            addCriterion("created_by >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(Long value) {
            addCriterion("created_by <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(Long value) {
            addCriterion("created_by <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<Long> values) {
            addCriterion("created_by in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<Long> values) {
            addCriterion("created_by not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(Long value1, Long value2) {
            addCriterion("created_by between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(Long value1, Long value2) {
            addCriterion("created_by not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedNameIsNull() {
            addCriterion("created_name is null");
            return (Criteria) this;
        }

        public Criteria andCreatedNameIsNotNull() {
            addCriterion("created_name is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedNameEqualTo(String value) {
            addCriterion("created_name =", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotEqualTo(String value) {
            addCriterion("created_name <>", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameGreaterThan(String value) {
            addCriterion("created_name >", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameGreaterThanOrEqualTo(String value) {
            addCriterion("created_name >=", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameLessThan(String value) {
            addCriterion("created_name <", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameLessThanOrEqualTo(String value) {
            addCriterion("created_name <=", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameLike(String value) {
            addCriterion("created_name like", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotLike(String value) {
            addCriterion("created_name not like", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameIn(List<String> values) {
            addCriterion("created_name in", values, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotIn(List<String> values) {
            addCriterion("created_name not in", values, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameBetween(String value1, String value2) {
            addCriterion("created_name between", value1, value2, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotBetween(String value1, String value2) {
            addCriterion("created_name not between", value1, value2, "createdName");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNull() {
            addCriterion("updated_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNotNull() {
            addCriterion("updated_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByEqualTo(Long value) {
            addCriterion("updated_by =", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotEqualTo(Long value) {
            addCriterion("updated_by <>", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThan(Long value) {
            addCriterion("updated_by >", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThanOrEqualTo(Long value) {
            addCriterion("updated_by >=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThan(Long value) {
            addCriterion("updated_by <", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThanOrEqualTo(Long value) {
            addCriterion("updated_by <=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIn(List<Long> values) {
            addCriterion("updated_by in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotIn(List<Long> values) {
            addCriterion("updated_by not in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByBetween(Long value1, Long value2) {
            addCriterion("updated_by between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotBetween(Long value1, Long value2) {
            addCriterion("updated_by not between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameIsNull() {
            addCriterion("updated_name is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameIsNotNull() {
            addCriterion("updated_name is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameEqualTo(String value) {
            addCriterion("updated_name =", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotEqualTo(String value) {
            addCriterion("updated_name <>", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameGreaterThan(String value) {
            addCriterion("updated_name >", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameGreaterThanOrEqualTo(String value) {
            addCriterion("updated_name >=", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameLessThan(String value) {
            addCriterion("updated_name <", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameLessThanOrEqualTo(String value) {
            addCriterion("updated_name <=", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameLike(String value) {
            addCriterion("updated_name like", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotLike(String value) {
            addCriterion("updated_name not like", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameIn(List<String> values) {
            addCriterion("updated_name in", values, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotIn(List<String> values) {
            addCriterion("updated_name not in", values, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameBetween(String value1, String value2) {
            addCriterion("updated_name between", value1, value2, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotBetween(String value1, String value2) {
            addCriterion("updated_name not between", value1, value2, "updatedName");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}