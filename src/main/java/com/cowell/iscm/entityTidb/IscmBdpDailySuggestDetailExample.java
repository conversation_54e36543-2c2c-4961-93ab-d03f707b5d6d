package com.cowell.iscm.entityTidb;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class IscmBdpDailySuggestDetailExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public IscmBdpDailySuggestDetailExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andPlatformNameIsNull() {
            addCriterion("platform_name is null");
            return (Criteria) this;
        }

        public Criteria andPlatformNameIsNotNull() {
            addCriterion("platform_name is not null");
            return (Criteria) this;
        }

        public Criteria andPlatformNameEqualTo(String value) {
            addCriterion("platform_name =", value, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameNotEqualTo(String value) {
            addCriterion("platform_name <>", value, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameGreaterThan(String value) {
            addCriterion("platform_name >", value, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameGreaterThanOrEqualTo(String value) {
            addCriterion("platform_name >=", value, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameLessThan(String value) {
            addCriterion("platform_name <", value, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameLessThanOrEqualTo(String value) {
            addCriterion("platform_name <=", value, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameLike(String value) {
            addCriterion("platform_name like", value, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameNotLike(String value) {
            addCriterion("platform_name not like", value, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameIn(List<String> values) {
            addCriterion("platform_name in", values, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameNotIn(List<String> values) {
            addCriterion("platform_name not in", values, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameBetween(String value1, String value2) {
            addCriterion("platform_name between", value1, value2, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameNotBetween(String value1, String value2) {
            addCriterion("platform_name not between", value1, value2, "platformName");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIsNull() {
            addCriterion("company_code is null");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIsNotNull() {
            addCriterion("company_code is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeEqualTo(String value) {
            addCriterion("company_code =", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotEqualTo(String value) {
            addCriterion("company_code <>", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeGreaterThan(String value) {
            addCriterion("company_code >", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeGreaterThanOrEqualTo(String value) {
            addCriterion("company_code >=", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLessThan(String value) {
            addCriterion("company_code <", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLessThanOrEqualTo(String value) {
            addCriterion("company_code <=", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLike(String value) {
            addCriterion("company_code like", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotLike(String value) {
            addCriterion("company_code not like", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIn(List<String> values) {
            addCriterion("company_code in", values, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotIn(List<String> values) {
            addCriterion("company_code not in", values, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeBetween(String value1, String value2) {
            addCriterion("company_code between", value1, value2, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotBetween(String value1, String value2) {
            addCriterion("company_code not between", value1, value2, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyNameIsNull() {
            addCriterion("company_name is null");
            return (Criteria) this;
        }

        public Criteria andCompanyNameIsNotNull() {
            addCriterion("company_name is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyNameEqualTo(String value) {
            addCriterion("company_name =", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotEqualTo(String value) {
            addCriterion("company_name <>", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameGreaterThan(String value) {
            addCriterion("company_name >", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameGreaterThanOrEqualTo(String value) {
            addCriterion("company_name >=", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameLessThan(String value) {
            addCriterion("company_name <", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameLessThanOrEqualTo(String value) {
            addCriterion("company_name <=", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameLike(String value) {
            addCriterion("company_name like", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotLike(String value) {
            addCriterion("company_name not like", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameIn(List<String> values) {
            addCriterion("company_name in", values, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotIn(List<String> values) {
            addCriterion("company_name not in", values, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameBetween(String value1, String value2) {
            addCriterion("company_name between", value1, value2, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotBetween(String value1, String value2) {
            addCriterion("company_name not between", value1, value2, "companyName");
            return (Criteria) this;
        }

        public Criteria andStoreCodeIsNull() {
            addCriterion("store_code is null");
            return (Criteria) this;
        }

        public Criteria andStoreCodeIsNotNull() {
            addCriterion("store_code is not null");
            return (Criteria) this;
        }

        public Criteria andStoreCodeEqualTo(String value) {
            addCriterion("store_code =", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotEqualTo(String value) {
            addCriterion("store_code <>", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeGreaterThan(String value) {
            addCriterion("store_code >", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeGreaterThanOrEqualTo(String value) {
            addCriterion("store_code >=", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeLessThan(String value) {
            addCriterion("store_code <", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeLessThanOrEqualTo(String value) {
            addCriterion("store_code <=", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeLike(String value) {
            addCriterion("store_code like", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotLike(String value) {
            addCriterion("store_code not like", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeIn(List<String> values) {
            addCriterion("store_code in", values, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotIn(List<String> values) {
            addCriterion("store_code not in", values, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeBetween(String value1, String value2) {
            addCriterion("store_code between", value1, value2, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotBetween(String value1, String value2) {
            addCriterion("store_code not between", value1, value2, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreNameIsNull() {
            addCriterion("store_name is null");
            return (Criteria) this;
        }

        public Criteria andStoreNameIsNotNull() {
            addCriterion("store_name is not null");
            return (Criteria) this;
        }

        public Criteria andStoreNameEqualTo(String value) {
            addCriterion("store_name =", value, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameNotEqualTo(String value) {
            addCriterion("store_name <>", value, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameGreaterThan(String value) {
            addCriterion("store_name >", value, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameGreaterThanOrEqualTo(String value) {
            addCriterion("store_name >=", value, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameLessThan(String value) {
            addCriterion("store_name <", value, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameLessThanOrEqualTo(String value) {
            addCriterion("store_name <=", value, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameLike(String value) {
            addCriterion("store_name like", value, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameNotLike(String value) {
            addCriterion("store_name not like", value, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameIn(List<String> values) {
            addCriterion("store_name in", values, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameNotIn(List<String> values) {
            addCriterion("store_name not in", values, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameBetween(String value1, String value2) {
            addCriterion("store_name between", value1, value2, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameNotBetween(String value1, String value2) {
            addCriterion("store_name not between", value1, value2, "storeName");
            return (Criteria) this;
        }

        public Criteria andGoodsNoIsNull() {
            addCriterion("goods_no is null");
            return (Criteria) this;
        }

        public Criteria andGoodsNoIsNotNull() {
            addCriterion("goods_no is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsNoEqualTo(String value) {
            addCriterion("goods_no =", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotEqualTo(String value) {
            addCriterion("goods_no <>", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoGreaterThan(String value) {
            addCriterion("goods_no >", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoGreaterThanOrEqualTo(String value) {
            addCriterion("goods_no >=", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoLessThan(String value) {
            addCriterion("goods_no <", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoLessThanOrEqualTo(String value) {
            addCriterion("goods_no <=", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoLike(String value) {
            addCriterion("goods_no like", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotLike(String value) {
            addCriterion("goods_no not like", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoIn(List<String> values) {
            addCriterion("goods_no in", values, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotIn(List<String> values) {
            addCriterion("goods_no not in", values, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoBetween(String value1, String value2) {
            addCriterion("goods_no between", value1, value2, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotBetween(String value1, String value2) {
            addCriterion("goods_no not between", value1, value2, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andBdpInterfaceQuantityIsNull() {
            addCriterion("bdp_interface_quantity is null");
            return (Criteria) this;
        }

        public Criteria andBdpInterfaceQuantityIsNotNull() {
            addCriterion("bdp_interface_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andBdpInterfaceQuantityEqualTo(BigDecimal value) {
            addCriterion("bdp_interface_quantity =", value, "bdpInterfaceQuantity");
            return (Criteria) this;
        }

        public Criteria andBdpInterfaceQuantityNotEqualTo(BigDecimal value) {
            addCriterion("bdp_interface_quantity <>", value, "bdpInterfaceQuantity");
            return (Criteria) this;
        }

        public Criteria andBdpInterfaceQuantityGreaterThan(BigDecimal value) {
            addCriterion("bdp_interface_quantity >", value, "bdpInterfaceQuantity");
            return (Criteria) this;
        }

        public Criteria andBdpInterfaceQuantityGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("bdp_interface_quantity >=", value, "bdpInterfaceQuantity");
            return (Criteria) this;
        }

        public Criteria andBdpInterfaceQuantityLessThan(BigDecimal value) {
            addCriterion("bdp_interface_quantity <", value, "bdpInterfaceQuantity");
            return (Criteria) this;
        }

        public Criteria andBdpInterfaceQuantityLessThanOrEqualTo(BigDecimal value) {
            addCriterion("bdp_interface_quantity <=", value, "bdpInterfaceQuantity");
            return (Criteria) this;
        }

        public Criteria andBdpInterfaceQuantityIn(List<BigDecimal> values) {
            addCriterion("bdp_interface_quantity in", values, "bdpInterfaceQuantity");
            return (Criteria) this;
        }

        public Criteria andBdpInterfaceQuantityNotIn(List<BigDecimal> values) {
            addCriterion("bdp_interface_quantity not in", values, "bdpInterfaceQuantity");
            return (Criteria) this;
        }

        public Criteria andBdpInterfaceQuantityBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("bdp_interface_quantity between", value1, value2, "bdpInterfaceQuantity");
            return (Criteria) this;
        }

        public Criteria andBdpInterfaceQuantityNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("bdp_interface_quantity not between", value1, value2, "bdpInterfaceQuantity");
            return (Criteria) this;
        }

        public Criteria andPosInterfaceQuantityIsNull() {
            addCriterion("pos_interface_quantity is null");
            return (Criteria) this;
        }

        public Criteria andPosInterfaceQuantityIsNotNull() {
            addCriterion("pos_interface_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andPosInterfaceQuantityEqualTo(BigDecimal value) {
            addCriterion("pos_interface_quantity =", value, "posInterfaceQuantity");
            return (Criteria) this;
        }

        public Criteria andPosInterfaceQuantityNotEqualTo(BigDecimal value) {
            addCriterion("pos_interface_quantity <>", value, "posInterfaceQuantity");
            return (Criteria) this;
        }

        public Criteria andPosInterfaceQuantityGreaterThan(BigDecimal value) {
            addCriterion("pos_interface_quantity >", value, "posInterfaceQuantity");
            return (Criteria) this;
        }

        public Criteria andPosInterfaceQuantityGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("pos_interface_quantity >=", value, "posInterfaceQuantity");
            return (Criteria) this;
        }

        public Criteria andPosInterfaceQuantityLessThan(BigDecimal value) {
            addCriterion("pos_interface_quantity <", value, "posInterfaceQuantity");
            return (Criteria) this;
        }

        public Criteria andPosInterfaceQuantityLessThanOrEqualTo(BigDecimal value) {
            addCriterion("pos_interface_quantity <=", value, "posInterfaceQuantity");
            return (Criteria) this;
        }

        public Criteria andPosInterfaceQuantityIn(List<BigDecimal> values) {
            addCriterion("pos_interface_quantity in", values, "posInterfaceQuantity");
            return (Criteria) this;
        }

        public Criteria andPosInterfaceQuantityNotIn(List<BigDecimal> values) {
            addCriterion("pos_interface_quantity not in", values, "posInterfaceQuantity");
            return (Criteria) this;
        }

        public Criteria andPosInterfaceQuantityBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("pos_interface_quantity between", value1, value2, "posInterfaceQuantity");
            return (Criteria) this;
        }

        public Criteria andPosInterfaceQuantityNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("pos_interface_quantity not between", value1, value2, "posInterfaceQuantity");
            return (Criteria) this;
        }

        public Criteria andPosBusinessQuantityIsNull() {
            addCriterion("pos_business_quantity is null");
            return (Criteria) this;
        }

        public Criteria andPosBusinessQuantityIsNotNull() {
            addCriterion("pos_business_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andPosBusinessQuantityEqualTo(BigDecimal value) {
            addCriterion("pos_business_quantity =", value, "posBusinessQuantity");
            return (Criteria) this;
        }

        public Criteria andPosBusinessQuantityNotEqualTo(BigDecimal value) {
            addCriterion("pos_business_quantity <>", value, "posBusinessQuantity");
            return (Criteria) this;
        }

        public Criteria andPosBusinessQuantityGreaterThan(BigDecimal value) {
            addCriterion("pos_business_quantity >", value, "posBusinessQuantity");
            return (Criteria) this;
        }

        public Criteria andPosBusinessQuantityGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("pos_business_quantity >=", value, "posBusinessQuantity");
            return (Criteria) this;
        }

        public Criteria andPosBusinessQuantityLessThan(BigDecimal value) {
            addCriterion("pos_business_quantity <", value, "posBusinessQuantity");
            return (Criteria) this;
        }

        public Criteria andPosBusinessQuantityLessThanOrEqualTo(BigDecimal value) {
            addCriterion("pos_business_quantity <=", value, "posBusinessQuantity");
            return (Criteria) this;
        }

        public Criteria andPosBusinessQuantityIn(List<BigDecimal> values) {
            addCriterion("pos_business_quantity in", values, "posBusinessQuantity");
            return (Criteria) this;
        }

        public Criteria andPosBusinessQuantityNotIn(List<BigDecimal> values) {
            addCriterion("pos_business_quantity not in", values, "posBusinessQuantity");
            return (Criteria) this;
        }

        public Criteria andPosBusinessQuantityBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("pos_business_quantity between", value1, value2, "posBusinessQuantity");
            return (Criteria) this;
        }

        public Criteria andPosBusinessQuantityNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("pos_business_quantity not between", value1, value2, "posBusinessQuantity");
            return (Criteria) this;
        }

        public Criteria andExecStatusIsNull() {
            addCriterion("exec_status is null");
            return (Criteria) this;
        }

        public Criteria andExecStatusIsNotNull() {
            addCriterion("exec_status is not null");
            return (Criteria) this;
        }

        public Criteria andExecStatusEqualTo(Byte value) {
            addCriterion("exec_status =", value, "execStatus");
            return (Criteria) this;
        }

        public Criteria andExecStatusNotEqualTo(Byte value) {
            addCriterion("exec_status <>", value, "execStatus");
            return (Criteria) this;
        }

        public Criteria andExecStatusGreaterThan(Byte value) {
            addCriterion("exec_status >", value, "execStatus");
            return (Criteria) this;
        }

        public Criteria andExecStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("exec_status >=", value, "execStatus");
            return (Criteria) this;
        }

        public Criteria andExecStatusLessThan(Byte value) {
            addCriterion("exec_status <", value, "execStatus");
            return (Criteria) this;
        }

        public Criteria andExecStatusLessThanOrEqualTo(Byte value) {
            addCriterion("exec_status <=", value, "execStatus");
            return (Criteria) this;
        }

        public Criteria andExecStatusIn(List<Byte> values) {
            addCriterion("exec_status in", values, "execStatus");
            return (Criteria) this;
        }

        public Criteria andExecStatusNotIn(List<Byte> values) {
            addCriterion("exec_status not in", values, "execStatus");
            return (Criteria) this;
        }

        public Criteria andExecStatusBetween(Byte value1, Byte value2) {
            addCriterion("exec_status between", value1, value2, "execStatus");
            return (Criteria) this;
        }

        public Criteria andExecStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("exec_status not between", value1, value2, "execStatus");
            return (Criteria) this;
        }

        public Criteria andDiffMsgIsNull() {
            addCriterion("diff_msg is null");
            return (Criteria) this;
        }

        public Criteria andDiffMsgIsNotNull() {
            addCriterion("diff_msg is not null");
            return (Criteria) this;
        }

        public Criteria andDiffMsgEqualTo(String value) {
            addCriterion("diff_msg =", value, "diffMsg");
            return (Criteria) this;
        }

        public Criteria andDiffMsgNotEqualTo(String value) {
            addCriterion("diff_msg <>", value, "diffMsg");
            return (Criteria) this;
        }

        public Criteria andDiffMsgGreaterThan(String value) {
            addCriterion("diff_msg >", value, "diffMsg");
            return (Criteria) this;
        }

        public Criteria andDiffMsgGreaterThanOrEqualTo(String value) {
            addCriterion("diff_msg >=", value, "diffMsg");
            return (Criteria) this;
        }

        public Criteria andDiffMsgLessThan(String value) {
            addCriterion("diff_msg <", value, "diffMsg");
            return (Criteria) this;
        }

        public Criteria andDiffMsgLessThanOrEqualTo(String value) {
            addCriterion("diff_msg <=", value, "diffMsg");
            return (Criteria) this;
        }

        public Criteria andDiffMsgLike(String value) {
            addCriterion("diff_msg like", value, "diffMsg");
            return (Criteria) this;
        }

        public Criteria andDiffMsgNotLike(String value) {
            addCriterion("diff_msg not like", value, "diffMsg");
            return (Criteria) this;
        }

        public Criteria andDiffMsgIn(List<String> values) {
            addCriterion("diff_msg in", values, "diffMsg");
            return (Criteria) this;
        }

        public Criteria andDiffMsgNotIn(List<String> values) {
            addCriterion("diff_msg not in", values, "diffMsg");
            return (Criteria) this;
        }

        public Criteria andDiffMsgBetween(String value1, String value2) {
            addCriterion("diff_msg between", value1, value2, "diffMsg");
            return (Criteria) this;
        }

        public Criteria andDiffMsgNotBetween(String value1, String value2) {
            addCriterion("diff_msg not between", value1, value2, "diffMsg");
            return (Criteria) this;
        }

        public Criteria andDtIsNull() {
            addCriterion("dt is null");
            return (Criteria) this;
        }

        public Criteria andDtIsNotNull() {
            addCriterion("dt is not null");
            return (Criteria) this;
        }

        public Criteria andDtEqualTo(String value) {
            addCriterion("dt =", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtNotEqualTo(String value) {
            addCriterion("dt <>", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtGreaterThan(String value) {
            addCriterion("dt >", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtGreaterThanOrEqualTo(String value) {
            addCriterion("dt >=", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtLessThan(String value) {
            addCriterion("dt <", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtLessThanOrEqualTo(String value) {
            addCriterion("dt <=", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtLike(String value) {
            addCriterion("dt like", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtNotLike(String value) {
            addCriterion("dt not like", value, "dt");
            return (Criteria) this;
        }

        public Criteria andDtIn(List<String> values) {
            addCriterion("dt in", values, "dt");
            return (Criteria) this;
        }

        public Criteria andDtNotIn(List<String> values) {
            addCriterion("dt not in", values, "dt");
            return (Criteria) this;
        }

        public Criteria andDtBetween(String value1, String value2) {
            addCriterion("dt between", value1, value2, "dt");
            return (Criteria) this;
        }

        public Criteria andDtNotBetween(String value1, String value2) {
            addCriterion("dt not between", value1, value2, "dt");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}