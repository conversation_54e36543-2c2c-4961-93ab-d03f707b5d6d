package com.cowell.iscm.entityTidb;

import java.io.Serializable;
import java.util.Date;

/**
 * iscm_node_plan_time
 * <AUTHOR>
public class IscmNodePlanTime implements Serializable {
    /**
     * 无逻辑自增主键
     */
    private Long id;

    /**
     * 公司MDM编码
     */
    private String companyCode;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * POS生成请货单时间（计划开始时间）
     */
    private Date posGenorderTime;

    /**
     * POS合单时间（计划开始时间）
     */
    private Date posMergeorderTime;

    /**
     * SAP自动审核时间（计划开始时间）
     */
    private Date sapSapPurchaseApproveTime;

    /**
     * BDP分货时间（计划开始时间）
     */
    private Date bdpDistributeTime;

    /**
     * SAP自动转单时间（计划审核开始时间）
     */
    private Date sapSapTransferTime;

    /**
     * 时间分区 yyyy-MM-dd
     */
    private String dt;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public Date getPosGenorderTime() {
        return posGenorderTime;
    }

    public void setPosGenorderTime(Date posGenorderTime) {
        this.posGenorderTime = posGenorderTime;
    }

    public Date getPosMergeorderTime() {
        return posMergeorderTime;
    }

    public void setPosMergeorderTime(Date posMergeorderTime) {
        this.posMergeorderTime = posMergeorderTime;
    }

    public Date getSapSapPurchaseApproveTime() {
        return sapSapPurchaseApproveTime;
    }

    public void setSapSapPurchaseApproveTime(Date sapSapPurchaseApproveTime) {
        this.sapSapPurchaseApproveTime = sapSapPurchaseApproveTime;
    }

    public Date getBdpDistributeTime() {
        return bdpDistributeTime;
    }

    public void setBdpDistributeTime(Date bdpDistributeTime) {
        this.bdpDistributeTime = bdpDistributeTime;
    }

    public Date getSapSapTransferTime() {
        return sapSapTransferTime;
    }

    public void setSapSapTransferTime(Date sapSapTransferTime) {
        this.sapSapTransferTime = sapSapTransferTime;
    }

    public String getDt() {
        return dt;
    }

    public void setDt(String dt) {
        this.dt = dt;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        IscmNodePlanTime other = (IscmNodePlanTime) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getCompanyCode() == null ? other.getCompanyCode() == null : this.getCompanyCode().equals(other.getCompanyCode()))
            && (this.getCompanyName() == null ? other.getCompanyName() == null : this.getCompanyName().equals(other.getCompanyName()))
            && (this.getPosGenorderTime() == null ? other.getPosGenorderTime() == null : this.getPosGenorderTime().equals(other.getPosGenorderTime()))
            && (this.getPosMergeorderTime() == null ? other.getPosMergeorderTime() == null : this.getPosMergeorderTime().equals(other.getPosMergeorderTime()))
            && (this.getSapSapPurchaseApproveTime() == null ? other.getSapSapPurchaseApproveTime() == null : this.getSapSapPurchaseApproveTime().equals(other.getSapSapPurchaseApproveTime()))
            && (this.getBdpDistributeTime() == null ? other.getBdpDistributeTime() == null : this.getBdpDistributeTime().equals(other.getBdpDistributeTime()))
            && (this.getSapSapTransferTime() == null ? other.getSapSapTransferTime() == null : this.getSapSapTransferTime().equals(other.getSapSapTransferTime()))
            && (this.getDt() == null ? other.getDt() == null : this.getDt().equals(other.getDt()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getCompanyCode() == null) ? 0 : getCompanyCode().hashCode());
        result = prime * result + ((getCompanyName() == null) ? 0 : getCompanyName().hashCode());
        result = prime * result + ((getPosGenorderTime() == null) ? 0 : getPosGenorderTime().hashCode());
        result = prime * result + ((getPosMergeorderTime() == null) ? 0 : getPosMergeorderTime().hashCode());
        result = prime * result + ((getSapSapPurchaseApproveTime() == null) ? 0 : getSapSapPurchaseApproveTime().hashCode());
        result = prime * result + ((getBdpDistributeTime() == null) ? 0 : getBdpDistributeTime().hashCode());
        result = prime * result + ((getSapSapTransferTime() == null) ? 0 : getSapSapTransferTime().hashCode());
        result = prime * result + ((getDt() == null) ? 0 : getDt().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", companyCode=").append(companyCode);
        sb.append(", companyName=").append(companyName);
        sb.append(", posGenorderTime=").append(posGenorderTime);
        sb.append(", posMergeorderTime=").append(posMergeorderTime);
        sb.append(", sapSapPurchaseApproveTime=").append(sapSapPurchaseApproveTime);
        sb.append(", bdpDistributeTime=").append(bdpDistributeTime);
        sb.append(", sapSapTransferTime=").append(sapSapTransferTime);
        sb.append(", dt=").append(dt);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}