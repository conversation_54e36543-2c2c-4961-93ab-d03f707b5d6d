package com.cowell.iscm.entityTidb;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class IscmBdpStoreGoodsInfoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Long offset;

    public IscmBdpStoreGoodsInfoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Long offset) {
        this.offset = offset;
    }

    public Long getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIsNull() {
            addCriterion("company_code is null");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIsNotNull() {
            addCriterion("company_code is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeEqualTo(String value) {
            addCriterion("company_code =", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotEqualTo(String value) {
            addCriterion("company_code <>", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeGreaterThan(String value) {
            addCriterion("company_code >", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeGreaterThanOrEqualTo(String value) {
            addCriterion("company_code >=", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLessThan(String value) {
            addCriterion("company_code <", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLessThanOrEqualTo(String value) {
            addCriterion("company_code <=", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLike(String value) {
            addCriterion("company_code like", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotLike(String value) {
            addCriterion("company_code not like", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIn(List<String> values) {
            addCriterion("company_code in", values, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotIn(List<String> values) {
            addCriterion("company_code not in", values, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeBetween(String value1, String value2) {
            addCriterion("company_code between", value1, value2, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotBetween(String value1, String value2) {
            addCriterion("company_code not between", value1, value2, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyBdpCodeIsNull() {
            addCriterion("company_bdp_code is null");
            return (Criteria) this;
        }

        public Criteria andCompanyBdpCodeIsNotNull() {
            addCriterion("company_bdp_code is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyBdpCodeEqualTo(String value) {
            addCriterion("company_bdp_code =", value, "companyBdpCode");
            return (Criteria) this;
        }

        public Criteria andCompanyBdpCodeNotEqualTo(String value) {
            addCriterion("company_bdp_code <>", value, "companyBdpCode");
            return (Criteria) this;
        }

        public Criteria andCompanyBdpCodeGreaterThan(String value) {
            addCriterion("company_bdp_code >", value, "companyBdpCode");
            return (Criteria) this;
        }

        public Criteria andCompanyBdpCodeGreaterThanOrEqualTo(String value) {
            addCriterion("company_bdp_code >=", value, "companyBdpCode");
            return (Criteria) this;
        }

        public Criteria andCompanyBdpCodeLessThan(String value) {
            addCriterion("company_bdp_code <", value, "companyBdpCode");
            return (Criteria) this;
        }

        public Criteria andCompanyBdpCodeLessThanOrEqualTo(String value) {
            addCriterion("company_bdp_code <=", value, "companyBdpCode");
            return (Criteria) this;
        }

        public Criteria andCompanyBdpCodeLike(String value) {
            addCriterion("company_bdp_code like", value, "companyBdpCode");
            return (Criteria) this;
        }

        public Criteria andCompanyBdpCodeNotLike(String value) {
            addCriterion("company_bdp_code not like", value, "companyBdpCode");
            return (Criteria) this;
        }

        public Criteria andCompanyBdpCodeIn(List<String> values) {
            addCriterion("company_bdp_code in", values, "companyBdpCode");
            return (Criteria) this;
        }

        public Criteria andCompanyBdpCodeNotIn(List<String> values) {
            addCriterion("company_bdp_code not in", values, "companyBdpCode");
            return (Criteria) this;
        }

        public Criteria andCompanyBdpCodeBetween(String value1, String value2) {
            addCriterion("company_bdp_code between", value1, value2, "companyBdpCode");
            return (Criteria) this;
        }

        public Criteria andCompanyBdpCodeNotBetween(String value1, String value2) {
            addCriterion("company_bdp_code not between", value1, value2, "companyBdpCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeIsNull() {
            addCriterion("store_code is null");
            return (Criteria) this;
        }

        public Criteria andStoreCodeIsNotNull() {
            addCriterion("store_code is not null");
            return (Criteria) this;
        }

        public Criteria andStoreCodeEqualTo(String value) {
            addCriterion("store_code =", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotEqualTo(String value) {
            addCriterion("store_code <>", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeGreaterThan(String value) {
            addCriterion("store_code >", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeGreaterThanOrEqualTo(String value) {
            addCriterion("store_code >=", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeLessThan(String value) {
            addCriterion("store_code <", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeLessThanOrEqualTo(String value) {
            addCriterion("store_code <=", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeLike(String value) {
            addCriterion("store_code like", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotLike(String value) {
            addCriterion("store_code not like", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeIn(List<String> values) {
            addCriterion("store_code in", values, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotIn(List<String> values) {
            addCriterion("store_code not in", values, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeBetween(String value1, String value2) {
            addCriterion("store_code between", value1, value2, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotBetween(String value1, String value2) {
            addCriterion("store_code not between", value1, value2, "storeCode");
            return (Criteria) this;
        }

        public Criteria andGoodsNoIsNull() {
            addCriterion("goods_no is null");
            return (Criteria) this;
        }

        public Criteria andGoodsNoIsNotNull() {
            addCriterion("goods_no is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsNoEqualTo(String value) {
            addCriterion("goods_no =", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotEqualTo(String value) {
            addCriterion("goods_no <>", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoGreaterThan(String value) {
            addCriterion("goods_no >", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoGreaterThanOrEqualTo(String value) {
            addCriterion("goods_no >=", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoLessThan(String value) {
            addCriterion("goods_no <", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoLessThanOrEqualTo(String value) {
            addCriterion("goods_no <=", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoLike(String value) {
            addCriterion("goods_no like", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotLike(String value) {
            addCriterion("goods_no not like", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoIn(List<String> values) {
            addCriterion("goods_no in", values, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotIn(List<String> values) {
            addCriterion("goods_no not in", values, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoBetween(String value1, String value2) {
            addCriterion("goods_no between", value1, value2, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotBetween(String value1, String value2) {
            addCriterion("goods_no not between", value1, value2, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andManufacturerIsNull() {
            addCriterion("manufacturer is null");
            return (Criteria) this;
        }

        public Criteria andManufacturerIsNotNull() {
            addCriterion("manufacturer is not null");
            return (Criteria) this;
        }

        public Criteria andManufacturerEqualTo(String value) {
            addCriterion("manufacturer =", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerNotEqualTo(String value) {
            addCriterion("manufacturer <>", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerGreaterThan(String value) {
            addCriterion("manufacturer >", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerGreaterThanOrEqualTo(String value) {
            addCriterion("manufacturer >=", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerLessThan(String value) {
            addCriterion("manufacturer <", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerLessThanOrEqualTo(String value) {
            addCriterion("manufacturer <=", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerLike(String value) {
            addCriterion("manufacturer like", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerNotLike(String value) {
            addCriterion("manufacturer not like", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerIn(List<String> values) {
            addCriterion("manufacturer in", values, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerNotIn(List<String> values) {
            addCriterion("manufacturer not in", values, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerBetween(String value1, String value2) {
            addCriterion("manufacturer between", value1, value2, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerNotBetween(String value1, String value2) {
            addCriterion("manufacturer not between", value1, value2, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andStorageDaysIsNull() {
            addCriterion("storage_days is null");
            return (Criteria) this;
        }

        public Criteria andStorageDaysIsNotNull() {
            addCriterion("storage_days is not null");
            return (Criteria) this;
        }

        public Criteria andStorageDaysEqualTo(Integer value) {
            addCriterion("storage_days =", value, "storageDays");
            return (Criteria) this;
        }

        public Criteria andStorageDaysNotEqualTo(Integer value) {
            addCriterion("storage_days <>", value, "storageDays");
            return (Criteria) this;
        }

        public Criteria andStorageDaysGreaterThan(Integer value) {
            addCriterion("storage_days >", value, "storageDays");
            return (Criteria) this;
        }

        public Criteria andStorageDaysGreaterThanOrEqualTo(Integer value) {
            addCriterion("storage_days >=", value, "storageDays");
            return (Criteria) this;
        }

        public Criteria andStorageDaysLessThan(Integer value) {
            addCriterion("storage_days <", value, "storageDays");
            return (Criteria) this;
        }

        public Criteria andStorageDaysLessThanOrEqualTo(Integer value) {
            addCriterion("storage_days <=", value, "storageDays");
            return (Criteria) this;
        }

        public Criteria andStorageDaysIn(List<Integer> values) {
            addCriterion("storage_days in", values, "storageDays");
            return (Criteria) this;
        }

        public Criteria andStorageDaysNotIn(List<Integer> values) {
            addCriterion("storage_days not in", values, "storageDays");
            return (Criteria) this;
        }

        public Criteria andStorageDaysBetween(Integer value1, Integer value2) {
            addCriterion("storage_days between", value1, value2, "storageDays");
            return (Criteria) this;
        }

        public Criteria andStorageDaysNotBetween(Integer value1, Integer value2) {
            addCriterion("storage_days not between", value1, value2, "storageDays");
            return (Criteria) this;
        }

        public Criteria andNonSalesDaysIsNull() {
            addCriterion("non_sales_days is null");
            return (Criteria) this;
        }

        public Criteria andNonSalesDaysIsNotNull() {
            addCriterion("non_sales_days is not null");
            return (Criteria) this;
        }

        public Criteria andNonSalesDaysEqualTo(Integer value) {
            addCriterion("non_sales_days =", value, "nonSalesDays");
            return (Criteria) this;
        }

        public Criteria andNonSalesDaysNotEqualTo(Integer value) {
            addCriterion("non_sales_days <>", value, "nonSalesDays");
            return (Criteria) this;
        }

        public Criteria andNonSalesDaysGreaterThan(Integer value) {
            addCriterion("non_sales_days >", value, "nonSalesDays");
            return (Criteria) this;
        }

        public Criteria andNonSalesDaysGreaterThanOrEqualTo(Integer value) {
            addCriterion("non_sales_days >=", value, "nonSalesDays");
            return (Criteria) this;
        }

        public Criteria andNonSalesDaysLessThan(Integer value) {
            addCriterion("non_sales_days <", value, "nonSalesDays");
            return (Criteria) this;
        }

        public Criteria andNonSalesDaysLessThanOrEqualTo(Integer value) {
            addCriterion("non_sales_days <=", value, "nonSalesDays");
            return (Criteria) this;
        }

        public Criteria andNonSalesDaysIn(List<Integer> values) {
            addCriterion("non_sales_days in", values, "nonSalesDays");
            return (Criteria) this;
        }

        public Criteria andNonSalesDaysNotIn(List<Integer> values) {
            addCriterion("non_sales_days not in", values, "nonSalesDays");
            return (Criteria) this;
        }

        public Criteria andNonSalesDaysBetween(Integer value1, Integer value2) {
            addCriterion("non_sales_days between", value1, value2, "nonSalesDays");
            return (Criteria) this;
        }

        public Criteria andNonSalesDaysNotBetween(Integer value1, Integer value2) {
            addCriterion("non_sales_days not between", value1, value2, "nonSalesDays");
            return (Criteria) this;
        }

        public Criteria andThirtySalesQuantityIsNull() {
            addCriterion("thirty_sales_quantity is null");
            return (Criteria) this;
        }

        public Criteria andThirtySalesQuantityIsNotNull() {
            addCriterion("thirty_sales_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andThirtySalesQuantityEqualTo(BigDecimal value) {
            addCriterion("thirty_sales_quantity =", value, "thirtySalesQuantity");
            return (Criteria) this;
        }

        public Criteria andThirtySalesQuantityNotEqualTo(BigDecimal value) {
            addCriterion("thirty_sales_quantity <>", value, "thirtySalesQuantity");
            return (Criteria) this;
        }

        public Criteria andThirtySalesQuantityGreaterThan(BigDecimal value) {
            addCriterion("thirty_sales_quantity >", value, "thirtySalesQuantity");
            return (Criteria) this;
        }

        public Criteria andThirtySalesQuantityGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("thirty_sales_quantity >=", value, "thirtySalesQuantity");
            return (Criteria) this;
        }

        public Criteria andThirtySalesQuantityLessThan(BigDecimal value) {
            addCriterion("thirty_sales_quantity <", value, "thirtySalesQuantity");
            return (Criteria) this;
        }

        public Criteria andThirtySalesQuantityLessThanOrEqualTo(BigDecimal value) {
            addCriterion("thirty_sales_quantity <=", value, "thirtySalesQuantity");
            return (Criteria) this;
        }

        public Criteria andThirtySalesQuantityIn(List<BigDecimal> values) {
            addCriterion("thirty_sales_quantity in", values, "thirtySalesQuantity");
            return (Criteria) this;
        }

        public Criteria andThirtySalesQuantityNotIn(List<BigDecimal> values) {
            addCriterion("thirty_sales_quantity not in", values, "thirtySalesQuantity");
            return (Criteria) this;
        }

        public Criteria andThirtySalesQuantityBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("thirty_sales_quantity between", value1, value2, "thirtySalesQuantity");
            return (Criteria) this;
        }

        public Criteria andThirtySalesQuantityNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("thirty_sales_quantity not between", value1, value2, "thirtySalesQuantity");
            return (Criteria) this;
        }

        public Criteria andThirtySalesCountIsNull() {
            addCriterion("thirty_sales_count is null");
            return (Criteria) this;
        }

        public Criteria andThirtySalesCountIsNotNull() {
            addCriterion("thirty_sales_count is not null");
            return (Criteria) this;
        }

        public Criteria andThirtySalesCountEqualTo(Integer value) {
            addCriterion("thirty_sales_count =", value, "thirtySalesCount");
            return (Criteria) this;
        }

        public Criteria andThirtySalesCountNotEqualTo(Integer value) {
            addCriterion("thirty_sales_count <>", value, "thirtySalesCount");
            return (Criteria) this;
        }

        public Criteria andThirtySalesCountGreaterThan(Integer value) {
            addCriterion("thirty_sales_count >", value, "thirtySalesCount");
            return (Criteria) this;
        }

        public Criteria andThirtySalesCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("thirty_sales_count >=", value, "thirtySalesCount");
            return (Criteria) this;
        }

        public Criteria andThirtySalesCountLessThan(Integer value) {
            addCriterion("thirty_sales_count <", value, "thirtySalesCount");
            return (Criteria) this;
        }

        public Criteria andThirtySalesCountLessThanOrEqualTo(Integer value) {
            addCriterion("thirty_sales_count <=", value, "thirtySalesCount");
            return (Criteria) this;
        }

        public Criteria andThirtySalesCountIn(List<Integer> values) {
            addCriterion("thirty_sales_count in", values, "thirtySalesCount");
            return (Criteria) this;
        }

        public Criteria andThirtySalesCountNotIn(List<Integer> values) {
            addCriterion("thirty_sales_count not in", values, "thirtySalesCount");
            return (Criteria) this;
        }

        public Criteria andThirtySalesCountBetween(Integer value1, Integer value2) {
            addCriterion("thirty_sales_count between", value1, value2, "thirtySalesCount");
            return (Criteria) this;
        }

        public Criteria andThirtySalesCountNotBetween(Integer value1, Integer value2) {
            addCriterion("thirty_sales_count not between", value1, value2, "thirtySalesCount");
            return (Criteria) this;
        }

        public Criteria andMinDisplayQuantityIsNull() {
            addCriterion("min_display_quantity is null");
            return (Criteria) this;
        }

        public Criteria andMinDisplayQuantityIsNotNull() {
            addCriterion("min_display_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andMinDisplayQuantityEqualTo(BigDecimal value) {
            addCriterion("min_display_quantity =", value, "minDisplayQuantity");
            return (Criteria) this;
        }

        public Criteria andMinDisplayQuantityNotEqualTo(BigDecimal value) {
            addCriterion("min_display_quantity <>", value, "minDisplayQuantity");
            return (Criteria) this;
        }

        public Criteria andMinDisplayQuantityGreaterThan(BigDecimal value) {
            addCriterion("min_display_quantity >", value, "minDisplayQuantity");
            return (Criteria) this;
        }

        public Criteria andMinDisplayQuantityGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("min_display_quantity >=", value, "minDisplayQuantity");
            return (Criteria) this;
        }

        public Criteria andMinDisplayQuantityLessThan(BigDecimal value) {
            addCriterion("min_display_quantity <", value, "minDisplayQuantity");
            return (Criteria) this;
        }

        public Criteria andMinDisplayQuantityLessThanOrEqualTo(BigDecimal value) {
            addCriterion("min_display_quantity <=", value, "minDisplayQuantity");
            return (Criteria) this;
        }

        public Criteria andMinDisplayQuantityIn(List<BigDecimal> values) {
            addCriterion("min_display_quantity in", values, "minDisplayQuantity");
            return (Criteria) this;
        }

        public Criteria andMinDisplayQuantityNotIn(List<BigDecimal> values) {
            addCriterion("min_display_quantity not in", values, "minDisplayQuantity");
            return (Criteria) this;
        }

        public Criteria andMinDisplayQuantityBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("min_display_quantity between", value1, value2, "minDisplayQuantity");
            return (Criteria) this;
        }

        public Criteria andMinDisplayQuantityNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("min_display_quantity not between", value1, value2, "minDisplayQuantity");
            return (Criteria) this;
        }

        public Criteria andExpectSaleDaysIsNull() {
            addCriterion("expect_sale_days is null");
            return (Criteria) this;
        }

        public Criteria andExpectSaleDaysIsNotNull() {
            addCriterion("expect_sale_days is not null");
            return (Criteria) this;
        }

        public Criteria andExpectSaleDaysEqualTo(BigDecimal value) {
            addCriterion("expect_sale_days =", value, "expectSaleDays");
            return (Criteria) this;
        }

        public Criteria andExpectSaleDaysNotEqualTo(BigDecimal value) {
            addCriterion("expect_sale_days <>", value, "expectSaleDays");
            return (Criteria) this;
        }

        public Criteria andExpectSaleDaysGreaterThan(BigDecimal value) {
            addCriterion("expect_sale_days >", value, "expectSaleDays");
            return (Criteria) this;
        }

        public Criteria andExpectSaleDaysGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("expect_sale_days >=", value, "expectSaleDays");
            return (Criteria) this;
        }

        public Criteria andExpectSaleDaysLessThan(BigDecimal value) {
            addCriterion("expect_sale_days <", value, "expectSaleDays");
            return (Criteria) this;
        }

        public Criteria andExpectSaleDaysLessThanOrEqualTo(BigDecimal value) {
            addCriterion("expect_sale_days <=", value, "expectSaleDays");
            return (Criteria) this;
        }

        public Criteria andExpectSaleDaysIn(List<BigDecimal> values) {
            addCriterion("expect_sale_days in", values, "expectSaleDays");
            return (Criteria) this;
        }

        public Criteria andExpectSaleDaysNotIn(List<BigDecimal> values) {
            addCriterion("expect_sale_days not in", values, "expectSaleDays");
            return (Criteria) this;
        }

        public Criteria andExpectSaleDaysBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("expect_sale_days between", value1, value2, "expectSaleDays");
            return (Criteria) this;
        }

        public Criteria andExpectSaleDaysNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("expect_sale_days not between", value1, value2, "expectSaleDays");
            return (Criteria) this;
        }

        public Criteria andRegisterQuantityIsNull() {
            addCriterion("register_quantity is null");
            return (Criteria) this;
        }

        public Criteria andRegisterQuantityIsNotNull() {
            addCriterion("register_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andRegisterQuantityEqualTo(BigDecimal value) {
            addCriterion("register_quantity =", value, "registerQuantity");
            return (Criteria) this;
        }

        public Criteria andRegisterQuantityNotEqualTo(BigDecimal value) {
            addCriterion("register_quantity <>", value, "registerQuantity");
            return (Criteria) this;
        }

        public Criteria andRegisterQuantityGreaterThan(BigDecimal value) {
            addCriterion("register_quantity >", value, "registerQuantity");
            return (Criteria) this;
        }

        public Criteria andRegisterQuantityGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("register_quantity >=", value, "registerQuantity");
            return (Criteria) this;
        }

        public Criteria andRegisterQuantityLessThan(BigDecimal value) {
            addCriterion("register_quantity <", value, "registerQuantity");
            return (Criteria) this;
        }

        public Criteria andRegisterQuantityLessThanOrEqualTo(BigDecimal value) {
            addCriterion("register_quantity <=", value, "registerQuantity");
            return (Criteria) this;
        }

        public Criteria andRegisterQuantityIn(List<BigDecimal> values) {
            addCriterion("register_quantity in", values, "registerQuantity");
            return (Criteria) this;
        }

        public Criteria andRegisterQuantityNotIn(List<BigDecimal> values) {
            addCriterion("register_quantity not in", values, "registerQuantity");
            return (Criteria) this;
        }

        public Criteria andRegisterQuantityBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("register_quantity between", value1, value2, "registerQuantity");
            return (Criteria) this;
        }

        public Criteria andRegisterQuantityNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("register_quantity not between", value1, value2, "registerQuantity");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeIsNull() {
            addCriterion("warehouse_code is null");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeIsNotNull() {
            addCriterion("warehouse_code is not null");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeEqualTo(String value) {
            addCriterion("warehouse_code =", value, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeNotEqualTo(String value) {
            addCriterion("warehouse_code <>", value, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeGreaterThan(String value) {
            addCriterion("warehouse_code >", value, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeGreaterThanOrEqualTo(String value) {
            addCriterion("warehouse_code >=", value, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeLessThan(String value) {
            addCriterion("warehouse_code <", value, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeLessThanOrEqualTo(String value) {
            addCriterion("warehouse_code <=", value, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeLike(String value) {
            addCriterion("warehouse_code like", value, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeNotLike(String value) {
            addCriterion("warehouse_code not like", value, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeIn(List<String> values) {
            addCriterion("warehouse_code in", values, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeNotIn(List<String> values) {
            addCriterion("warehouse_code not in", values, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeBetween(String value1, String value2) {
            addCriterion("warehouse_code between", value1, value2, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeNotBetween(String value1, String value2) {
            addCriterion("warehouse_code not between", value1, value2, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseNameIsNull() {
            addCriterion("warehouse_name is null");
            return (Criteria) this;
        }

        public Criteria andWarehouseNameIsNotNull() {
            addCriterion("warehouse_name is not null");
            return (Criteria) this;
        }

        public Criteria andWarehouseNameEqualTo(String value) {
            addCriterion("warehouse_name =", value, "warehouseName");
            return (Criteria) this;
        }

        public Criteria andWarehouseNameNotEqualTo(String value) {
            addCriterion("warehouse_name <>", value, "warehouseName");
            return (Criteria) this;
        }

        public Criteria andWarehouseNameGreaterThan(String value) {
            addCriterion("warehouse_name >", value, "warehouseName");
            return (Criteria) this;
        }

        public Criteria andWarehouseNameGreaterThanOrEqualTo(String value) {
            addCriterion("warehouse_name >=", value, "warehouseName");
            return (Criteria) this;
        }

        public Criteria andWarehouseNameLessThan(String value) {
            addCriterion("warehouse_name <", value, "warehouseName");
            return (Criteria) this;
        }

        public Criteria andWarehouseNameLessThanOrEqualTo(String value) {
            addCriterion("warehouse_name <=", value, "warehouseName");
            return (Criteria) this;
        }

        public Criteria andWarehouseNameLike(String value) {
            addCriterion("warehouse_name like", value, "warehouseName");
            return (Criteria) this;
        }

        public Criteria andWarehouseNameNotLike(String value) {
            addCriterion("warehouse_name not like", value, "warehouseName");
            return (Criteria) this;
        }

        public Criteria andWarehouseNameIn(List<String> values) {
            addCriterion("warehouse_name in", values, "warehouseName");
            return (Criteria) this;
        }

        public Criteria andWarehouseNameNotIn(List<String> values) {
            addCriterion("warehouse_name not in", values, "warehouseName");
            return (Criteria) this;
        }

        public Criteria andWarehouseNameBetween(String value1, String value2) {
            addCriterion("warehouse_name between", value1, value2, "warehouseName");
            return (Criteria) this;
        }

        public Criteria andWarehouseNameNotBetween(String value1, String value2) {
            addCriterion("warehouse_name not between", value1, value2, "warehouseName");
            return (Criteria) this;
        }

        public Criteria andGoodslineIsNull() {
            addCriterion("goodsline is null");
            return (Criteria) this;
        }

        public Criteria andGoodslineIsNotNull() {
            addCriterion("goodsline is not null");
            return (Criteria) this;
        }

        public Criteria andGoodslineEqualTo(String value) {
            addCriterion("goodsline =", value, "goodsline");
            return (Criteria) this;
        }

        public Criteria andGoodslineNotEqualTo(String value) {
            addCriterion("goodsline <>", value, "goodsline");
            return (Criteria) this;
        }

        public Criteria andGoodslineGreaterThan(String value) {
            addCriterion("goodsline >", value, "goodsline");
            return (Criteria) this;
        }

        public Criteria andGoodslineGreaterThanOrEqualTo(String value) {
            addCriterion("goodsline >=", value, "goodsline");
            return (Criteria) this;
        }

        public Criteria andGoodslineLessThan(String value) {
            addCriterion("goodsline <", value, "goodsline");
            return (Criteria) this;
        }

        public Criteria andGoodslineLessThanOrEqualTo(String value) {
            addCriterion("goodsline <=", value, "goodsline");
            return (Criteria) this;
        }

        public Criteria andGoodslineLike(String value) {
            addCriterion("goodsline like", value, "goodsline");
            return (Criteria) this;
        }

        public Criteria andGoodslineNotLike(String value) {
            addCriterion("goodsline not like", value, "goodsline");
            return (Criteria) this;
        }

        public Criteria andGoodslineIn(List<String> values) {
            addCriterion("goodsline in", values, "goodsline");
            return (Criteria) this;
        }

        public Criteria andGoodslineNotIn(List<String> values) {
            addCriterion("goodsline not in", values, "goodsline");
            return (Criteria) this;
        }

        public Criteria andGoodslineBetween(String value1, String value2) {
            addCriterion("goodsline between", value1, value2, "goodsline");
            return (Criteria) this;
        }

        public Criteria andGoodslineNotBetween(String value1, String value2) {
            addCriterion("goodsline not between", value1, value2, "goodsline");
            return (Criteria) this;
        }

        public Criteria andPushlevelIsNull() {
            addCriterion("pushlevel is null");
            return (Criteria) this;
        }

        public Criteria andPushlevelIsNotNull() {
            addCriterion("pushlevel is not null");
            return (Criteria) this;
        }

        public Criteria andPushlevelEqualTo(String value) {
            addCriterion("pushlevel =", value, "pushlevel");
            return (Criteria) this;
        }

        public Criteria andPushlevelNotEqualTo(String value) {
            addCriterion("pushlevel <>", value, "pushlevel");
            return (Criteria) this;
        }

        public Criteria andPushlevelGreaterThan(String value) {
            addCriterion("pushlevel >", value, "pushlevel");
            return (Criteria) this;
        }

        public Criteria andPushlevelGreaterThanOrEqualTo(String value) {
            addCriterion("pushlevel >=", value, "pushlevel");
            return (Criteria) this;
        }

        public Criteria andPushlevelLessThan(String value) {
            addCriterion("pushlevel <", value, "pushlevel");
            return (Criteria) this;
        }

        public Criteria andPushlevelLessThanOrEqualTo(String value) {
            addCriterion("pushlevel <=", value, "pushlevel");
            return (Criteria) this;
        }

        public Criteria andPushlevelLike(String value) {
            addCriterion("pushlevel like", value, "pushlevel");
            return (Criteria) this;
        }

        public Criteria andPushlevelNotLike(String value) {
            addCriterion("pushlevel not like", value, "pushlevel");
            return (Criteria) this;
        }

        public Criteria andPushlevelIn(List<String> values) {
            addCriterion("pushlevel in", values, "pushlevel");
            return (Criteria) this;
        }

        public Criteria andPushlevelNotIn(List<String> values) {
            addCriterion("pushlevel not in", values, "pushlevel");
            return (Criteria) this;
        }

        public Criteria andPushlevelBetween(String value1, String value2) {
            addCriterion("pushlevel between", value1, value2, "pushlevel");
            return (Criteria) this;
        }

        public Criteria andPushlevelNotBetween(String value1, String value2) {
            addCriterion("pushlevel not between", value1, value2, "pushlevel");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelIsNull() {
            addCriterion("goods_level is null");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelIsNotNull() {
            addCriterion("goods_level is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelEqualTo(Integer value) {
            addCriterion("goods_level =", value, "goodsLevel");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelNotEqualTo(Integer value) {
            addCriterion("goods_level <>", value, "goodsLevel");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelGreaterThan(Integer value) {
            addCriterion("goods_level >", value, "goodsLevel");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelGreaterThanOrEqualTo(Integer value) {
            addCriterion("goods_level >=", value, "goodsLevel");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelLessThan(Integer value) {
            addCriterion("goods_level <", value, "goodsLevel");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelLessThanOrEqualTo(Integer value) {
            addCriterion("goods_level <=", value, "goodsLevel");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelIn(List<Integer> values) {
            addCriterion("goods_level in", values, "goodsLevel");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelNotIn(List<Integer> values) {
            addCriterion("goods_level not in", values, "goodsLevel");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelBetween(Integer value1, Integer value2) {
            addCriterion("goods_level between", value1, value2, "goodsLevel");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelNotBetween(Integer value1, Integer value2) {
            addCriterion("goods_level not between", value1, value2, "goodsLevel");
            return (Criteria) this;
        }

        public Criteria andForbidDistributeIsNull() {
            addCriterion("forbid_distribute is null");
            return (Criteria) this;
        }

        public Criteria andForbidDistributeIsNotNull() {
            addCriterion("forbid_distribute is not null");
            return (Criteria) this;
        }

        public Criteria andForbidDistributeEqualTo(String value) {
            addCriterion("forbid_distribute =", value, "forbidDistribute");
            return (Criteria) this;
        }

        public Criteria andForbidDistributeNotEqualTo(String value) {
            addCriterion("forbid_distribute <>", value, "forbidDistribute");
            return (Criteria) this;
        }

        public Criteria andForbidDistributeGreaterThan(String value) {
            addCriterion("forbid_distribute >", value, "forbidDistribute");
            return (Criteria) this;
        }

        public Criteria andForbidDistributeGreaterThanOrEqualTo(String value) {
            addCriterion("forbid_distribute >=", value, "forbidDistribute");
            return (Criteria) this;
        }

        public Criteria andForbidDistributeLessThan(String value) {
            addCriterion("forbid_distribute <", value, "forbidDistribute");
            return (Criteria) this;
        }

        public Criteria andForbidDistributeLessThanOrEqualTo(String value) {
            addCriterion("forbid_distribute <=", value, "forbidDistribute");
            return (Criteria) this;
        }

        public Criteria andForbidDistributeLike(String value) {
            addCriterion("forbid_distribute like", value, "forbidDistribute");
            return (Criteria) this;
        }

        public Criteria andForbidDistributeNotLike(String value) {
            addCriterion("forbid_distribute not like", value, "forbidDistribute");
            return (Criteria) this;
        }

        public Criteria andForbidDistributeIn(List<String> values) {
            addCriterion("forbid_distribute in", values, "forbidDistribute");
            return (Criteria) this;
        }

        public Criteria andForbidDistributeNotIn(List<String> values) {
            addCriterion("forbid_distribute not in", values, "forbidDistribute");
            return (Criteria) this;
        }

        public Criteria andForbidDistributeBetween(String value1, String value2) {
            addCriterion("forbid_distribute between", value1, value2, "forbidDistribute");
            return (Criteria) this;
        }

        public Criteria andForbidDistributeNotBetween(String value1, String value2) {
            addCriterion("forbid_distribute not between", value1, value2, "forbidDistribute");
            return (Criteria) this;
        }

        public Criteria andForbidReturnWarehouseIsNull() {
            addCriterion("forbid_return_warehouse is null");
            return (Criteria) this;
        }

        public Criteria andForbidReturnWarehouseIsNotNull() {
            addCriterion("forbid_return_warehouse is not null");
            return (Criteria) this;
        }

        public Criteria andForbidReturnWarehouseEqualTo(String value) {
            addCriterion("forbid_return_warehouse =", value, "forbidReturnWarehouse");
            return (Criteria) this;
        }

        public Criteria andForbidReturnWarehouseNotEqualTo(String value) {
            addCriterion("forbid_return_warehouse <>", value, "forbidReturnWarehouse");
            return (Criteria) this;
        }

        public Criteria andForbidReturnWarehouseGreaterThan(String value) {
            addCriterion("forbid_return_warehouse >", value, "forbidReturnWarehouse");
            return (Criteria) this;
        }

        public Criteria andForbidReturnWarehouseGreaterThanOrEqualTo(String value) {
            addCriterion("forbid_return_warehouse >=", value, "forbidReturnWarehouse");
            return (Criteria) this;
        }

        public Criteria andForbidReturnWarehouseLessThan(String value) {
            addCriterion("forbid_return_warehouse <", value, "forbidReturnWarehouse");
            return (Criteria) this;
        }

        public Criteria andForbidReturnWarehouseLessThanOrEqualTo(String value) {
            addCriterion("forbid_return_warehouse <=", value, "forbidReturnWarehouse");
            return (Criteria) this;
        }

        public Criteria andForbidReturnWarehouseLike(String value) {
            addCriterion("forbid_return_warehouse like", value, "forbidReturnWarehouse");
            return (Criteria) this;
        }

        public Criteria andForbidReturnWarehouseNotLike(String value) {
            addCriterion("forbid_return_warehouse not like", value, "forbidReturnWarehouse");
            return (Criteria) this;
        }

        public Criteria andForbidReturnWarehouseIn(List<String> values) {
            addCriterion("forbid_return_warehouse in", values, "forbidReturnWarehouse");
            return (Criteria) this;
        }

        public Criteria andForbidReturnWarehouseNotIn(List<String> values) {
            addCriterion("forbid_return_warehouse not in", values, "forbidReturnWarehouse");
            return (Criteria) this;
        }

        public Criteria andForbidReturnWarehouseBetween(String value1, String value2) {
            addCriterion("forbid_return_warehouse between", value1, value2, "forbidReturnWarehouse");
            return (Criteria) this;
        }

        public Criteria andForbidReturnWarehouseNotBetween(String value1, String value2) {
            addCriterion("forbid_return_warehouse not between", value1, value2, "forbidReturnWarehouse");
            return (Criteria) this;
        }

        public Criteria andForbidApplyIsNull() {
            addCriterion("forbid_apply is null");
            return (Criteria) this;
        }

        public Criteria andForbidApplyIsNotNull() {
            addCriterion("forbid_apply is not null");
            return (Criteria) this;
        }

        public Criteria andForbidApplyEqualTo(String value) {
            addCriterion("forbid_apply =", value, "forbidApply");
            return (Criteria) this;
        }

        public Criteria andForbidApplyNotEqualTo(String value) {
            addCriterion("forbid_apply <>", value, "forbidApply");
            return (Criteria) this;
        }

        public Criteria andForbidApplyGreaterThan(String value) {
            addCriterion("forbid_apply >", value, "forbidApply");
            return (Criteria) this;
        }

        public Criteria andForbidApplyGreaterThanOrEqualTo(String value) {
            addCriterion("forbid_apply >=", value, "forbidApply");
            return (Criteria) this;
        }

        public Criteria andForbidApplyLessThan(String value) {
            addCriterion("forbid_apply <", value, "forbidApply");
            return (Criteria) this;
        }

        public Criteria andForbidApplyLessThanOrEqualTo(String value) {
            addCriterion("forbid_apply <=", value, "forbidApply");
            return (Criteria) this;
        }

        public Criteria andForbidApplyLike(String value) {
            addCriterion("forbid_apply like", value, "forbidApply");
            return (Criteria) this;
        }

        public Criteria andForbidApplyNotLike(String value) {
            addCriterion("forbid_apply not like", value, "forbidApply");
            return (Criteria) this;
        }

        public Criteria andForbidApplyIn(List<String> values) {
            addCriterion("forbid_apply in", values, "forbidApply");
            return (Criteria) this;
        }

        public Criteria andForbidApplyNotIn(List<String> values) {
            addCriterion("forbid_apply not in", values, "forbidApply");
            return (Criteria) this;
        }

        public Criteria andForbidApplyBetween(String value1, String value2) {
            addCriterion("forbid_apply between", value1, value2, "forbidApply");
            return (Criteria) this;
        }

        public Criteria andForbidApplyNotBetween(String value1, String value2) {
            addCriterion("forbid_apply not between", value1, value2, "forbidApply");
            return (Criteria) this;
        }

        public Criteria andForbidAllotIsNull() {
            addCriterion("forbid_allot is null");
            return (Criteria) this;
        }

        public Criteria andForbidAllotIsNotNull() {
            addCriterion("forbid_allot is not null");
            return (Criteria) this;
        }

        public Criteria andForbidAllotEqualTo(String value) {
            addCriterion("forbid_allot =", value, "forbidAllot");
            return (Criteria) this;
        }

        public Criteria andForbidAllotNotEqualTo(String value) {
            addCriterion("forbid_allot <>", value, "forbidAllot");
            return (Criteria) this;
        }

        public Criteria andForbidAllotGreaterThan(String value) {
            addCriterion("forbid_allot >", value, "forbidAllot");
            return (Criteria) this;
        }

        public Criteria andForbidAllotGreaterThanOrEqualTo(String value) {
            addCriterion("forbid_allot >=", value, "forbidAllot");
            return (Criteria) this;
        }

        public Criteria andForbidAllotLessThan(String value) {
            addCriterion("forbid_allot <", value, "forbidAllot");
            return (Criteria) this;
        }

        public Criteria andForbidAllotLessThanOrEqualTo(String value) {
            addCriterion("forbid_allot <=", value, "forbidAllot");
            return (Criteria) this;
        }

        public Criteria andForbidAllotLike(String value) {
            addCriterion("forbid_allot like", value, "forbidAllot");
            return (Criteria) this;
        }

        public Criteria andForbidAllotNotLike(String value) {
            addCriterion("forbid_allot not like", value, "forbidAllot");
            return (Criteria) this;
        }

        public Criteria andForbidAllotIn(List<String> values) {
            addCriterion("forbid_allot in", values, "forbidAllot");
            return (Criteria) this;
        }

        public Criteria andForbidAllotNotIn(List<String> values) {
            addCriterion("forbid_allot not in", values, "forbidAllot");
            return (Criteria) this;
        }

        public Criteria andForbidAllotBetween(String value1, String value2) {
            addCriterion("forbid_allot between", value1, value2, "forbidAllot");
            return (Criteria) this;
        }

        public Criteria andForbidAllotNotBetween(String value1, String value2) {
            addCriterion("forbid_allot not between", value1, value2, "forbidAllot");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitDaysIsNull() {
            addCriterion("stock_upper_limit_days is null");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitDaysIsNotNull() {
            addCriterion("stock_upper_limit_days is not null");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitDaysEqualTo(Integer value) {
            addCriterion("stock_upper_limit_days =", value, "stockUpperLimitDays");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitDaysNotEqualTo(Integer value) {
            addCriterion("stock_upper_limit_days <>", value, "stockUpperLimitDays");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitDaysGreaterThan(Integer value) {
            addCriterion("stock_upper_limit_days >", value, "stockUpperLimitDays");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitDaysGreaterThanOrEqualTo(Integer value) {
            addCriterion("stock_upper_limit_days >=", value, "stockUpperLimitDays");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitDaysLessThan(Integer value) {
            addCriterion("stock_upper_limit_days <", value, "stockUpperLimitDays");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitDaysLessThanOrEqualTo(Integer value) {
            addCriterion("stock_upper_limit_days <=", value, "stockUpperLimitDays");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitDaysIn(List<Integer> values) {
            addCriterion("stock_upper_limit_days in", values, "stockUpperLimitDays");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitDaysNotIn(List<Integer> values) {
            addCriterion("stock_upper_limit_days not in", values, "stockUpperLimitDays");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitDaysBetween(Integer value1, Integer value2) {
            addCriterion("stock_upper_limit_days between", value1, value2, "stockUpperLimitDays");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitDaysNotBetween(Integer value1, Integer value2) {
            addCriterion("stock_upper_limit_days not between", value1, value2, "stockUpperLimitDays");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitDaysIsNull() {
            addCriterion("stock_lower_limit_days is null");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitDaysIsNotNull() {
            addCriterion("stock_lower_limit_days is not null");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitDaysEqualTo(Integer value) {
            addCriterion("stock_lower_limit_days =", value, "stockLowerLimitDays");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitDaysNotEqualTo(Integer value) {
            addCriterion("stock_lower_limit_days <>", value, "stockLowerLimitDays");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitDaysGreaterThan(Integer value) {
            addCriterion("stock_lower_limit_days >", value, "stockLowerLimitDays");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitDaysGreaterThanOrEqualTo(Integer value) {
            addCriterion("stock_lower_limit_days >=", value, "stockLowerLimitDays");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitDaysLessThan(Integer value) {
            addCriterion("stock_lower_limit_days <", value, "stockLowerLimitDays");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitDaysLessThanOrEqualTo(Integer value) {
            addCriterion("stock_lower_limit_days <=", value, "stockLowerLimitDays");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitDaysIn(List<Integer> values) {
            addCriterion("stock_lower_limit_days in", values, "stockLowerLimitDays");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitDaysNotIn(List<Integer> values) {
            addCriterion("stock_lower_limit_days not in", values, "stockLowerLimitDays");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitDaysBetween(Integer value1, Integer value2) {
            addCriterion("stock_lower_limit_days between", value1, value2, "stockLowerLimitDays");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitDaysNotBetween(Integer value1, Integer value2) {
            addCriterion("stock_lower_limit_days not between", value1, value2, "stockLowerLimitDays");
            return (Criteria) this;
        }

        public Criteria andHdSynthesizeAverageDailySalesIsNull() {
            addCriterion("hd_synthesize_average_daily_sales is null");
            return (Criteria) this;
        }

        public Criteria andHdSynthesizeAverageDailySalesIsNotNull() {
            addCriterion("hd_synthesize_average_daily_sales is not null");
            return (Criteria) this;
        }

        public Criteria andHdSynthesizeAverageDailySalesEqualTo(BigDecimal value) {
            addCriterion("hd_synthesize_average_daily_sales =", value, "hdSynthesizeAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andHdSynthesizeAverageDailySalesNotEqualTo(BigDecimal value) {
            addCriterion("hd_synthesize_average_daily_sales <>", value, "hdSynthesizeAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andHdSynthesizeAverageDailySalesGreaterThan(BigDecimal value) {
            addCriterion("hd_synthesize_average_daily_sales >", value, "hdSynthesizeAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andHdSynthesizeAverageDailySalesGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("hd_synthesize_average_daily_sales >=", value, "hdSynthesizeAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andHdSynthesizeAverageDailySalesLessThan(BigDecimal value) {
            addCriterion("hd_synthesize_average_daily_sales <", value, "hdSynthesizeAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andHdSynthesizeAverageDailySalesLessThanOrEqualTo(BigDecimal value) {
            addCriterion("hd_synthesize_average_daily_sales <=", value, "hdSynthesizeAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andHdSynthesizeAverageDailySalesIn(List<BigDecimal> values) {
            addCriterion("hd_synthesize_average_daily_sales in", values, "hdSynthesizeAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andHdSynthesizeAverageDailySalesNotIn(List<BigDecimal> values) {
            addCriterion("hd_synthesize_average_daily_sales not in", values, "hdSynthesizeAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andHdSynthesizeAverageDailySalesBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("hd_synthesize_average_daily_sales between", value1, value2, "hdSynthesizeAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andHdSynthesizeAverageDailySalesNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("hd_synthesize_average_daily_sales not between", value1, value2, "hdSynthesizeAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitIsNull() {
            addCriterion("stock_upper_limit is null");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitIsNotNull() {
            addCriterion("stock_upper_limit is not null");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitEqualTo(BigDecimal value) {
            addCriterion("stock_upper_limit =", value, "stockUpperLimit");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitNotEqualTo(BigDecimal value) {
            addCriterion("stock_upper_limit <>", value, "stockUpperLimit");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitGreaterThan(BigDecimal value) {
            addCriterion("stock_upper_limit >", value, "stockUpperLimit");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("stock_upper_limit >=", value, "stockUpperLimit");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitLessThan(BigDecimal value) {
            addCriterion("stock_upper_limit <", value, "stockUpperLimit");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitLessThanOrEqualTo(BigDecimal value) {
            addCriterion("stock_upper_limit <=", value, "stockUpperLimit");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitIn(List<BigDecimal> values) {
            addCriterion("stock_upper_limit in", values, "stockUpperLimit");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitNotIn(List<BigDecimal> values) {
            addCriterion("stock_upper_limit not in", values, "stockUpperLimit");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("stock_upper_limit between", value1, value2, "stockUpperLimit");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("stock_upper_limit not between", value1, value2, "stockUpperLimit");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitIsNull() {
            addCriterion("stock_lower_limit is null");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitIsNotNull() {
            addCriterion("stock_lower_limit is not null");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitEqualTo(BigDecimal value) {
            addCriterion("stock_lower_limit =", value, "stockLowerLimit");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitNotEqualTo(BigDecimal value) {
            addCriterion("stock_lower_limit <>", value, "stockLowerLimit");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitGreaterThan(BigDecimal value) {
            addCriterion("stock_lower_limit >", value, "stockLowerLimit");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("stock_lower_limit >=", value, "stockLowerLimit");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitLessThan(BigDecimal value) {
            addCriterion("stock_lower_limit <", value, "stockLowerLimit");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitLessThanOrEqualTo(BigDecimal value) {
            addCriterion("stock_lower_limit <=", value, "stockLowerLimit");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitIn(List<BigDecimal> values) {
            addCriterion("stock_lower_limit in", values, "stockLowerLimit");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitNotIn(List<BigDecimal> values) {
            addCriterion("stock_lower_limit not in", values, "stockLowerLimit");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("stock_lower_limit between", value1, value2, "stockLowerLimit");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("stock_lower_limit not between", value1, value2, "stockLowerLimit");
            return (Criteria) this;
        }

        public Criteria andBdpSynthesizeAverageDailySalesIsNull() {
            addCriterion("bdp_synthesize_average_daily_sales is null");
            return (Criteria) this;
        }

        public Criteria andBdpSynthesizeAverageDailySalesIsNotNull() {
            addCriterion("bdp_synthesize_average_daily_sales is not null");
            return (Criteria) this;
        }

        public Criteria andBdpSynthesizeAverageDailySalesEqualTo(BigDecimal value) {
            addCriterion("bdp_synthesize_average_daily_sales =", value, "bdpSynthesizeAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andBdpSynthesizeAverageDailySalesNotEqualTo(BigDecimal value) {
            addCriterion("bdp_synthesize_average_daily_sales <>", value, "bdpSynthesizeAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andBdpSynthesizeAverageDailySalesGreaterThan(BigDecimal value) {
            addCriterion("bdp_synthesize_average_daily_sales >", value, "bdpSynthesizeAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andBdpSynthesizeAverageDailySalesGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("bdp_synthesize_average_daily_sales >=", value, "bdpSynthesizeAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andBdpSynthesizeAverageDailySalesLessThan(BigDecimal value) {
            addCriterion("bdp_synthesize_average_daily_sales <", value, "bdpSynthesizeAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andBdpSynthesizeAverageDailySalesLessThanOrEqualTo(BigDecimal value) {
            addCriterion("bdp_synthesize_average_daily_sales <=", value, "bdpSynthesizeAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andBdpSynthesizeAverageDailySalesIn(List<BigDecimal> values) {
            addCriterion("bdp_synthesize_average_daily_sales in", values, "bdpSynthesizeAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andBdpSynthesizeAverageDailySalesNotIn(List<BigDecimal> values) {
            addCriterion("bdp_synthesize_average_daily_sales not in", values, "bdpSynthesizeAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andBdpSynthesizeAverageDailySalesBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("bdp_synthesize_average_daily_sales between", value1, value2, "bdpSynthesizeAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andBdpSynthesizeAverageDailySalesNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("bdp_synthesize_average_daily_sales not between", value1, value2, "bdpSynthesizeAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andStockQuantityIsNull() {
            addCriterion("stock_quantity is null");
            return (Criteria) this;
        }

        public Criteria andStockQuantityIsNotNull() {
            addCriterion("stock_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andStockQuantityEqualTo(BigDecimal value) {
            addCriterion("stock_quantity =", value, "stockQuantity");
            return (Criteria) this;
        }

        public Criteria andStockQuantityNotEqualTo(BigDecimal value) {
            addCriterion("stock_quantity <>", value, "stockQuantity");
            return (Criteria) this;
        }

        public Criteria andStockQuantityGreaterThan(BigDecimal value) {
            addCriterion("stock_quantity >", value, "stockQuantity");
            return (Criteria) this;
        }

        public Criteria andStockQuantityGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("stock_quantity >=", value, "stockQuantity");
            return (Criteria) this;
        }

        public Criteria andStockQuantityLessThan(BigDecimal value) {
            addCriterion("stock_quantity <", value, "stockQuantity");
            return (Criteria) this;
        }

        public Criteria andStockQuantityLessThanOrEqualTo(BigDecimal value) {
            addCriterion("stock_quantity <=", value, "stockQuantity");
            return (Criteria) this;
        }

        public Criteria andStockQuantityIn(List<BigDecimal> values) {
            addCriterion("stock_quantity in", values, "stockQuantity");
            return (Criteria) this;
        }

        public Criteria andStockQuantityNotIn(List<BigDecimal> values) {
            addCriterion("stock_quantity not in", values, "stockQuantity");
            return (Criteria) this;
        }

        public Criteria andStockQuantityBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("stock_quantity between", value1, value2, "stockQuantity");
            return (Criteria) this;
        }

        public Criteria andStockQuantityNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("stock_quantity not between", value1, value2, "stockQuantity");
            return (Criteria) this;
        }

        public Criteria andCostAmountIsNull() {
            addCriterion("cost_amount is null");
            return (Criteria) this;
        }

        public Criteria andCostAmountIsNotNull() {
            addCriterion("cost_amount is not null");
            return (Criteria) this;
        }

        public Criteria andCostAmountEqualTo(BigDecimal value) {
            addCriterion("cost_amount =", value, "costAmount");
            return (Criteria) this;
        }

        public Criteria andCostAmountNotEqualTo(BigDecimal value) {
            addCriterion("cost_amount <>", value, "costAmount");
            return (Criteria) this;
        }

        public Criteria andCostAmountGreaterThan(BigDecimal value) {
            addCriterion("cost_amount >", value, "costAmount");
            return (Criteria) this;
        }

        public Criteria andCostAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("cost_amount >=", value, "costAmount");
            return (Criteria) this;
        }

        public Criteria andCostAmountLessThan(BigDecimal value) {
            addCriterion("cost_amount <", value, "costAmount");
            return (Criteria) this;
        }

        public Criteria andCostAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("cost_amount <=", value, "costAmount");
            return (Criteria) this;
        }

        public Criteria andCostAmountIn(List<BigDecimal> values) {
            addCriterion("cost_amount in", values, "costAmount");
            return (Criteria) this;
        }

        public Criteria andCostAmountNotIn(List<BigDecimal> values) {
            addCriterion("cost_amount not in", values, "costAmount");
            return (Criteria) this;
        }

        public Criteria andCostAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("cost_amount between", value1, value2, "costAmount");
            return (Criteria) this;
        }

        public Criteria andCostAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("cost_amount not between", value1, value2, "costAmount");
            return (Criteria) this;
        }

        public Criteria andReturnStatusIsNull() {
            addCriterion("return_status is null");
            return (Criteria) this;
        }

        public Criteria andReturnStatusIsNotNull() {
            addCriterion("return_status is not null");
            return (Criteria) this;
        }

        public Criteria andReturnStatusEqualTo(Byte value) {
            addCriterion("return_status =", value, "returnStatus");
            return (Criteria) this;
        }

        public Criteria andReturnStatusNotEqualTo(Byte value) {
            addCriterion("return_status <>", value, "returnStatus");
            return (Criteria) this;
        }

        public Criteria andReturnStatusGreaterThan(Byte value) {
            addCriterion("return_status >", value, "returnStatus");
            return (Criteria) this;
        }

        public Criteria andReturnStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("return_status >=", value, "returnStatus");
            return (Criteria) this;
        }

        public Criteria andReturnStatusLessThan(Byte value) {
            addCriterion("return_status <", value, "returnStatus");
            return (Criteria) this;
        }

        public Criteria andReturnStatusLessThanOrEqualTo(Byte value) {
            addCriterion("return_status <=", value, "returnStatus");
            return (Criteria) this;
        }

        public Criteria andReturnStatusIn(List<Byte> values) {
            addCriterion("return_status in", values, "returnStatus");
            return (Criteria) this;
        }

        public Criteria andReturnStatusNotIn(List<Byte> values) {
            addCriterion("return_status not in", values, "returnStatus");
            return (Criteria) this;
        }

        public Criteria andReturnStatusBetween(Byte value1, Byte value2) {
            addCriterion("return_status between", value1, value2, "returnStatus");
            return (Criteria) this;
        }

        public Criteria andReturnStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("return_status not between", value1, value2, "returnStatus");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}