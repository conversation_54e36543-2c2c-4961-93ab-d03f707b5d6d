package com.cowell.iscm.entityTidb;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class IscmSuggestGoodsAllotExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public IscmSuggestGoodsAllotExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andBusinessDateIsNull() {
            addCriterion("business_date is null");
            return (Criteria) this;
        }

        public Criteria andBusinessDateIsNotNull() {
            addCriterion("business_date is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessDateEqualTo(Date value) {
            addCriterion("business_date =", value, "businessDate");
            return (Criteria) this;
        }

        public Criteria andBusinessDateNotEqualTo(Date value) {
            addCriterion("business_date <>", value, "businessDate");
            return (Criteria) this;
        }

        public Criteria andBusinessDateGreaterThan(Date value) {
            addCriterion("business_date >", value, "businessDate");
            return (Criteria) this;
        }

        public Criteria andBusinessDateGreaterThanOrEqualTo(Date value) {
            addCriterion("business_date >=", value, "businessDate");
            return (Criteria) this;
        }

        public Criteria andBusinessDateLessThan(Date value) {
            addCriterion("business_date <", value, "businessDate");
            return (Criteria) this;
        }

        public Criteria andBusinessDateLessThanOrEqualTo(Date value) {
            addCriterion("business_date <=", value, "businessDate");
            return (Criteria) this;
        }

        public Criteria andBusinessDateIn(List<Date> values) {
            addCriterion("business_date in", values, "businessDate");
            return (Criteria) this;
        }

        public Criteria andBusinessDateNotIn(List<Date> values) {
            addCriterion("business_date not in", values, "businessDate");
            return (Criteria) this;
        }

        public Criteria andBusinessDateBetween(Date value1, Date value2) {
            addCriterion("business_date between", value1, value2, "businessDate");
            return (Criteria) this;
        }

        public Criteria andBusinessDateNotBetween(Date value1, Date value2) {
            addCriterion("business_date not between", value1, value2, "businessDate");
            return (Criteria) this;
        }

        public Criteria andAllotNoIsNull() {
            addCriterion("allot_no is null");
            return (Criteria) this;
        }

        public Criteria andAllotNoIsNotNull() {
            addCriterion("allot_no is not null");
            return (Criteria) this;
        }

        public Criteria andAllotNoEqualTo(String value) {
            addCriterion("allot_no =", value, "allotNo");
            return (Criteria) this;
        }

        public Criteria andAllotNoNotEqualTo(String value) {
            addCriterion("allot_no <>", value, "allotNo");
            return (Criteria) this;
        }

        public Criteria andAllotNoGreaterThan(String value) {
            addCriterion("allot_no >", value, "allotNo");
            return (Criteria) this;
        }

        public Criteria andAllotNoGreaterThanOrEqualTo(String value) {
            addCriterion("allot_no >=", value, "allotNo");
            return (Criteria) this;
        }

        public Criteria andAllotNoLessThan(String value) {
            addCriterion("allot_no <", value, "allotNo");
            return (Criteria) this;
        }

        public Criteria andAllotNoLessThanOrEqualTo(String value) {
            addCriterion("allot_no <=", value, "allotNo");
            return (Criteria) this;
        }

        public Criteria andAllotNoLike(String value) {
            addCriterion("allot_no like", value, "allotNo");
            return (Criteria) this;
        }

        public Criteria andAllotNoNotLike(String value) {
            addCriterion("allot_no not like", value, "allotNo");
            return (Criteria) this;
        }

        public Criteria andAllotNoIn(List<String> values) {
            addCriterion("allot_no in", values, "allotNo");
            return (Criteria) this;
        }

        public Criteria andAllotNoNotIn(List<String> values) {
            addCriterion("allot_no not in", values, "allotNo");
            return (Criteria) this;
        }

        public Criteria andAllotNoBetween(String value1, String value2) {
            addCriterion("allot_no between", value1, value2, "allotNo");
            return (Criteria) this;
        }

        public Criteria andAllotNoNotBetween(String value1, String value2) {
            addCriterion("allot_no not between", value1, value2, "allotNo");
            return (Criteria) this;
        }

        public Criteria andAllotDetailNoIsNull() {
            addCriterion("allot_detail_no is null");
            return (Criteria) this;
        }

        public Criteria andAllotDetailNoIsNotNull() {
            addCriterion("allot_detail_no is not null");
            return (Criteria) this;
        }

        public Criteria andAllotDetailNoEqualTo(String value) {
            addCriterion("allot_detail_no =", value, "allotDetailNo");
            return (Criteria) this;
        }

        public Criteria andAllotDetailNoNotEqualTo(String value) {
            addCriterion("allot_detail_no <>", value, "allotDetailNo");
            return (Criteria) this;
        }

        public Criteria andAllotDetailNoGreaterThan(String value) {
            addCriterion("allot_detail_no >", value, "allotDetailNo");
            return (Criteria) this;
        }

        public Criteria andAllotDetailNoGreaterThanOrEqualTo(String value) {
            addCriterion("allot_detail_no >=", value, "allotDetailNo");
            return (Criteria) this;
        }

        public Criteria andAllotDetailNoLessThan(String value) {
            addCriterion("allot_detail_no <", value, "allotDetailNo");
            return (Criteria) this;
        }

        public Criteria andAllotDetailNoLessThanOrEqualTo(String value) {
            addCriterion("allot_detail_no <=", value, "allotDetailNo");
            return (Criteria) this;
        }

        public Criteria andAllotDetailNoLike(String value) {
            addCriterion("allot_detail_no like", value, "allotDetailNo");
            return (Criteria) this;
        }

        public Criteria andAllotDetailNoNotLike(String value) {
            addCriterion("allot_detail_no not like", value, "allotDetailNo");
            return (Criteria) this;
        }

        public Criteria andAllotDetailNoIn(List<String> values) {
            addCriterion("allot_detail_no in", values, "allotDetailNo");
            return (Criteria) this;
        }

        public Criteria andAllotDetailNoNotIn(List<String> values) {
            addCriterion("allot_detail_no not in", values, "allotDetailNo");
            return (Criteria) this;
        }

        public Criteria andAllotDetailNoBetween(String value1, String value2) {
            addCriterion("allot_detail_no between", value1, value2, "allotDetailNo");
            return (Criteria) this;
        }

        public Criteria andAllotDetailNoNotBetween(String value1, String value2) {
            addCriterion("allot_detail_no not between", value1, value2, "allotDetailNo");
            return (Criteria) this;
        }

        public Criteria andRegisterNoIsNull() {
            addCriterion("register_no is null");
            return (Criteria) this;
        }

        public Criteria andRegisterNoIsNotNull() {
            addCriterion("register_no is not null");
            return (Criteria) this;
        }

        public Criteria andRegisterNoEqualTo(String value) {
            addCriterion("register_no =", value, "registerNo");
            return (Criteria) this;
        }

        public Criteria andRegisterNoNotEqualTo(String value) {
            addCriterion("register_no <>", value, "registerNo");
            return (Criteria) this;
        }

        public Criteria andRegisterNoGreaterThan(String value) {
            addCriterion("register_no >", value, "registerNo");
            return (Criteria) this;
        }

        public Criteria andRegisterNoGreaterThanOrEqualTo(String value) {
            addCriterion("register_no >=", value, "registerNo");
            return (Criteria) this;
        }

        public Criteria andRegisterNoLessThan(String value) {
            addCriterion("register_no <", value, "registerNo");
            return (Criteria) this;
        }

        public Criteria andRegisterNoLessThanOrEqualTo(String value) {
            addCriterion("register_no <=", value, "registerNo");
            return (Criteria) this;
        }

        public Criteria andRegisterNoLike(String value) {
            addCriterion("register_no like", value, "registerNo");
            return (Criteria) this;
        }

        public Criteria andRegisterNoNotLike(String value) {
            addCriterion("register_no not like", value, "registerNo");
            return (Criteria) this;
        }

        public Criteria andRegisterNoIn(List<String> values) {
            addCriterion("register_no in", values, "registerNo");
            return (Criteria) this;
        }

        public Criteria andRegisterNoNotIn(List<String> values) {
            addCriterion("register_no not in", values, "registerNo");
            return (Criteria) this;
        }

        public Criteria andRegisterNoBetween(String value1, String value2) {
            addCriterion("register_no between", value1, value2, "registerNo");
            return (Criteria) this;
        }

        public Criteria andRegisterNoNotBetween(String value1, String value2) {
            addCriterion("register_no not between", value1, value2, "registerNo");
            return (Criteria) this;
        }

        public Criteria andAllotTypeIsNull() {
            addCriterion("allot_type is null");
            return (Criteria) this;
        }

        public Criteria andAllotTypeIsNotNull() {
            addCriterion("allot_type is not null");
            return (Criteria) this;
        }

        public Criteria andAllotTypeEqualTo(Byte value) {
            addCriterion("allot_type =", value, "allotType");
            return (Criteria) this;
        }

        public Criteria andAllotTypeNotEqualTo(Byte value) {
            addCriterion("allot_type <>", value, "allotType");
            return (Criteria) this;
        }

        public Criteria andAllotTypeGreaterThan(Byte value) {
            addCriterion("allot_type >", value, "allotType");
            return (Criteria) this;
        }

        public Criteria andAllotTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("allot_type >=", value, "allotType");
            return (Criteria) this;
        }

        public Criteria andAllotTypeLessThan(Byte value) {
            addCriterion("allot_type <", value, "allotType");
            return (Criteria) this;
        }

        public Criteria andAllotTypeLessThanOrEqualTo(Byte value) {
            addCriterion("allot_type <=", value, "allotType");
            return (Criteria) this;
        }

        public Criteria andAllotTypeIn(List<Byte> values) {
            addCriterion("allot_type in", values, "allotType");
            return (Criteria) this;
        }

        public Criteria andAllotTypeNotIn(List<Byte> values) {
            addCriterion("allot_type not in", values, "allotType");
            return (Criteria) this;
        }

        public Criteria andAllotTypeBetween(Byte value1, Byte value2) {
            addCriterion("allot_type between", value1, value2, "allotType");
            return (Criteria) this;
        }

        public Criteria andAllotTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("allot_type not between", value1, value2, "allotType");
            return (Criteria) this;
        }

        public Criteria andRegisterSourceIsNull() {
            addCriterion("register_source is null");
            return (Criteria) this;
        }

        public Criteria andRegisterSourceIsNotNull() {
            addCriterion("register_source is not null");
            return (Criteria) this;
        }

        public Criteria andRegisterSourceEqualTo(Byte value) {
            addCriterion("register_source =", value, "registerSource");
            return (Criteria) this;
        }

        public Criteria andRegisterSourceNotEqualTo(Byte value) {
            addCriterion("register_source <>", value, "registerSource");
            return (Criteria) this;
        }

        public Criteria andRegisterSourceGreaterThan(Byte value) {
            addCriterion("register_source >", value, "registerSource");
            return (Criteria) this;
        }

        public Criteria andRegisterSourceGreaterThanOrEqualTo(Byte value) {
            addCriterion("register_source >=", value, "registerSource");
            return (Criteria) this;
        }

        public Criteria andRegisterSourceLessThan(Byte value) {
            addCriterion("register_source <", value, "registerSource");
            return (Criteria) this;
        }

        public Criteria andRegisterSourceLessThanOrEqualTo(Byte value) {
            addCriterion("register_source <=", value, "registerSource");
            return (Criteria) this;
        }

        public Criteria andRegisterSourceIn(List<Byte> values) {
            addCriterion("register_source in", values, "registerSource");
            return (Criteria) this;
        }

        public Criteria andRegisterSourceNotIn(List<Byte> values) {
            addCriterion("register_source not in", values, "registerSource");
            return (Criteria) this;
        }

        public Criteria andRegisterSourceBetween(Byte value1, Byte value2) {
            addCriterion("register_source between", value1, value2, "registerSource");
            return (Criteria) this;
        }

        public Criteria andRegisterSourceNotBetween(Byte value1, Byte value2) {
            addCriterion("register_source not between", value1, value2, "registerSource");
            return (Criteria) this;
        }

        public Criteria andOutCompanyCodeIsNull() {
            addCriterion("out_company_code is null");
            return (Criteria) this;
        }

        public Criteria andOutCompanyCodeIsNotNull() {
            addCriterion("out_company_code is not null");
            return (Criteria) this;
        }

        public Criteria andOutCompanyCodeEqualTo(String value) {
            addCriterion("out_company_code =", value, "outCompanyCode");
            return (Criteria) this;
        }

        public Criteria andOutCompanyCodeNotEqualTo(String value) {
            addCriterion("out_company_code <>", value, "outCompanyCode");
            return (Criteria) this;
        }

        public Criteria andOutCompanyCodeGreaterThan(String value) {
            addCriterion("out_company_code >", value, "outCompanyCode");
            return (Criteria) this;
        }

        public Criteria andOutCompanyCodeGreaterThanOrEqualTo(String value) {
            addCriterion("out_company_code >=", value, "outCompanyCode");
            return (Criteria) this;
        }

        public Criteria andOutCompanyCodeLessThan(String value) {
            addCriterion("out_company_code <", value, "outCompanyCode");
            return (Criteria) this;
        }

        public Criteria andOutCompanyCodeLessThanOrEqualTo(String value) {
            addCriterion("out_company_code <=", value, "outCompanyCode");
            return (Criteria) this;
        }

        public Criteria andOutCompanyCodeLike(String value) {
            addCriterion("out_company_code like", value, "outCompanyCode");
            return (Criteria) this;
        }

        public Criteria andOutCompanyCodeNotLike(String value) {
            addCriterion("out_company_code not like", value, "outCompanyCode");
            return (Criteria) this;
        }

        public Criteria andOutCompanyCodeIn(List<String> values) {
            addCriterion("out_company_code in", values, "outCompanyCode");
            return (Criteria) this;
        }

        public Criteria andOutCompanyCodeNotIn(List<String> values) {
            addCriterion("out_company_code not in", values, "outCompanyCode");
            return (Criteria) this;
        }

        public Criteria andOutCompanyCodeBetween(String value1, String value2) {
            addCriterion("out_company_code between", value1, value2, "outCompanyCode");
            return (Criteria) this;
        }

        public Criteria andOutCompanyCodeNotBetween(String value1, String value2) {
            addCriterion("out_company_code not between", value1, value2, "outCompanyCode");
            return (Criteria) this;
        }

        public Criteria andInCompanyCodeIsNull() {
            addCriterion("in_company_code is null");
            return (Criteria) this;
        }

        public Criteria andInCompanyCodeIsNotNull() {
            addCriterion("in_company_code is not null");
            return (Criteria) this;
        }

        public Criteria andInCompanyCodeEqualTo(String value) {
            addCriterion("in_company_code =", value, "inCompanyCode");
            return (Criteria) this;
        }

        public Criteria andInCompanyCodeNotEqualTo(String value) {
            addCriterion("in_company_code <>", value, "inCompanyCode");
            return (Criteria) this;
        }

        public Criteria andInCompanyCodeGreaterThan(String value) {
            addCriterion("in_company_code >", value, "inCompanyCode");
            return (Criteria) this;
        }

        public Criteria andInCompanyCodeGreaterThanOrEqualTo(String value) {
            addCriterion("in_company_code >=", value, "inCompanyCode");
            return (Criteria) this;
        }

        public Criteria andInCompanyCodeLessThan(String value) {
            addCriterion("in_company_code <", value, "inCompanyCode");
            return (Criteria) this;
        }

        public Criteria andInCompanyCodeLessThanOrEqualTo(String value) {
            addCriterion("in_company_code <=", value, "inCompanyCode");
            return (Criteria) this;
        }

        public Criteria andInCompanyCodeLike(String value) {
            addCriterion("in_company_code like", value, "inCompanyCode");
            return (Criteria) this;
        }

        public Criteria andInCompanyCodeNotLike(String value) {
            addCriterion("in_company_code not like", value, "inCompanyCode");
            return (Criteria) this;
        }

        public Criteria andInCompanyCodeIn(List<String> values) {
            addCriterion("in_company_code in", values, "inCompanyCode");
            return (Criteria) this;
        }

        public Criteria andInCompanyCodeNotIn(List<String> values) {
            addCriterion("in_company_code not in", values, "inCompanyCode");
            return (Criteria) this;
        }

        public Criteria andInCompanyCodeBetween(String value1, String value2) {
            addCriterion("in_company_code between", value1, value2, "inCompanyCode");
            return (Criteria) this;
        }

        public Criteria andInCompanyCodeNotBetween(String value1, String value2) {
            addCriterion("in_company_code not between", value1, value2, "inCompanyCode");
            return (Criteria) this;
        }

        public Criteria andOutStoreCodeIsNull() {
            addCriterion("out_store_code is null");
            return (Criteria) this;
        }

        public Criteria andOutStoreCodeIsNotNull() {
            addCriterion("out_store_code is not null");
            return (Criteria) this;
        }

        public Criteria andOutStoreCodeEqualTo(String value) {
            addCriterion("out_store_code =", value, "outStoreCode");
            return (Criteria) this;
        }

        public Criteria andOutStoreCodeNotEqualTo(String value) {
            addCriterion("out_store_code <>", value, "outStoreCode");
            return (Criteria) this;
        }

        public Criteria andOutStoreCodeGreaterThan(String value) {
            addCriterion("out_store_code >", value, "outStoreCode");
            return (Criteria) this;
        }

        public Criteria andOutStoreCodeGreaterThanOrEqualTo(String value) {
            addCriterion("out_store_code >=", value, "outStoreCode");
            return (Criteria) this;
        }

        public Criteria andOutStoreCodeLessThan(String value) {
            addCriterion("out_store_code <", value, "outStoreCode");
            return (Criteria) this;
        }

        public Criteria andOutStoreCodeLessThanOrEqualTo(String value) {
            addCriterion("out_store_code <=", value, "outStoreCode");
            return (Criteria) this;
        }

        public Criteria andOutStoreCodeLike(String value) {
            addCriterion("out_store_code like", value, "outStoreCode");
            return (Criteria) this;
        }

        public Criteria andOutStoreCodeNotLike(String value) {
            addCriterion("out_store_code not like", value, "outStoreCode");
            return (Criteria) this;
        }

        public Criteria andOutStoreCodeIn(List<String> values) {
            addCriterion("out_store_code in", values, "outStoreCode");
            return (Criteria) this;
        }

        public Criteria andOutStoreCodeNotIn(List<String> values) {
            addCriterion("out_store_code not in", values, "outStoreCode");
            return (Criteria) this;
        }

        public Criteria andOutStoreCodeBetween(String value1, String value2) {
            addCriterion("out_store_code between", value1, value2, "outStoreCode");
            return (Criteria) this;
        }

        public Criteria andOutStoreCodeNotBetween(String value1, String value2) {
            addCriterion("out_store_code not between", value1, value2, "outStoreCode");
            return (Criteria) this;
        }

        public Criteria andInStoreCodeIsNull() {
            addCriterion("in_store_code is null");
            return (Criteria) this;
        }

        public Criteria andInStoreCodeIsNotNull() {
            addCriterion("in_store_code is not null");
            return (Criteria) this;
        }

        public Criteria andInStoreCodeEqualTo(String value) {
            addCriterion("in_store_code =", value, "inStoreCode");
            return (Criteria) this;
        }

        public Criteria andInStoreCodeNotEqualTo(String value) {
            addCriterion("in_store_code <>", value, "inStoreCode");
            return (Criteria) this;
        }

        public Criteria andInStoreCodeGreaterThan(String value) {
            addCriterion("in_store_code >", value, "inStoreCode");
            return (Criteria) this;
        }

        public Criteria andInStoreCodeGreaterThanOrEqualTo(String value) {
            addCriterion("in_store_code >=", value, "inStoreCode");
            return (Criteria) this;
        }

        public Criteria andInStoreCodeLessThan(String value) {
            addCriterion("in_store_code <", value, "inStoreCode");
            return (Criteria) this;
        }

        public Criteria andInStoreCodeLessThanOrEqualTo(String value) {
            addCriterion("in_store_code <=", value, "inStoreCode");
            return (Criteria) this;
        }

        public Criteria andInStoreCodeLike(String value) {
            addCriterion("in_store_code like", value, "inStoreCode");
            return (Criteria) this;
        }

        public Criteria andInStoreCodeNotLike(String value) {
            addCriterion("in_store_code not like", value, "inStoreCode");
            return (Criteria) this;
        }

        public Criteria andInStoreCodeIn(List<String> values) {
            addCriterion("in_store_code in", values, "inStoreCode");
            return (Criteria) this;
        }

        public Criteria andInStoreCodeNotIn(List<String> values) {
            addCriterion("in_store_code not in", values, "inStoreCode");
            return (Criteria) this;
        }

        public Criteria andInStoreCodeBetween(String value1, String value2) {
            addCriterion("in_store_code between", value1, value2, "inStoreCode");
            return (Criteria) this;
        }

        public Criteria andInStoreCodeNotBetween(String value1, String value2) {
            addCriterion("in_store_code not between", value1, value2, "inStoreCode");
            return (Criteria) this;
        }

        public Criteria andGoodsNoIsNull() {
            addCriterion("goods_no is null");
            return (Criteria) this;
        }

        public Criteria andGoodsNoIsNotNull() {
            addCriterion("goods_no is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsNoEqualTo(String value) {
            addCriterion("goods_no =", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotEqualTo(String value) {
            addCriterion("goods_no <>", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoGreaterThan(String value) {
            addCriterion("goods_no >", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoGreaterThanOrEqualTo(String value) {
            addCriterion("goods_no >=", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoLessThan(String value) {
            addCriterion("goods_no <", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoLessThanOrEqualTo(String value) {
            addCriterion("goods_no <=", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoLike(String value) {
            addCriterion("goods_no like", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotLike(String value) {
            addCriterion("goods_no not like", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoIn(List<String> values) {
            addCriterion("goods_no in", values, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotIn(List<String> values) {
            addCriterion("goods_no not in", values, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoBetween(String value1, String value2) {
            addCriterion("goods_no between", value1, value2, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotBetween(String value1, String value2) {
            addCriterion("goods_no not between", value1, value2, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoIsNull() {
            addCriterion("batch_no is null");
            return (Criteria) this;
        }

        public Criteria andBatchNoIsNotNull() {
            addCriterion("batch_no is not null");
            return (Criteria) this;
        }

        public Criteria andBatchNoEqualTo(String value) {
            addCriterion("batch_no =", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoNotEqualTo(String value) {
            addCriterion("batch_no <>", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoGreaterThan(String value) {
            addCriterion("batch_no >", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoGreaterThanOrEqualTo(String value) {
            addCriterion("batch_no >=", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoLessThan(String value) {
            addCriterion("batch_no <", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoLessThanOrEqualTo(String value) {
            addCriterion("batch_no <=", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoLike(String value) {
            addCriterion("batch_no like", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoNotLike(String value) {
            addCriterion("batch_no not like", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoIn(List<String> values) {
            addCriterion("batch_no in", values, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoNotIn(List<String> values) {
            addCriterion("batch_no not in", values, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoBetween(String value1, String value2) {
            addCriterion("batch_no between", value1, value2, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoNotBetween(String value1, String value2) {
            addCriterion("batch_no not between", value1, value2, "batchNo");
            return (Criteria) this;
        }

        public Criteria andSuggestAllotQuantityIsNull() {
            addCriterion("suggest_allot_quantity is null");
            return (Criteria) this;
        }

        public Criteria andSuggestAllotQuantityIsNotNull() {
            addCriterion("suggest_allot_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andSuggestAllotQuantityEqualTo(BigDecimal value) {
            addCriterion("suggest_allot_quantity =", value, "suggestAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andSuggestAllotQuantityNotEqualTo(BigDecimal value) {
            addCriterion("suggest_allot_quantity <>", value, "suggestAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andSuggestAllotQuantityGreaterThan(BigDecimal value) {
            addCriterion("suggest_allot_quantity >", value, "suggestAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andSuggestAllotQuantityGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("suggest_allot_quantity >=", value, "suggestAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andSuggestAllotQuantityLessThan(BigDecimal value) {
            addCriterion("suggest_allot_quantity <", value, "suggestAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andSuggestAllotQuantityLessThanOrEqualTo(BigDecimal value) {
            addCriterion("suggest_allot_quantity <=", value, "suggestAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andSuggestAllotQuantityIn(List<BigDecimal> values) {
            addCriterion("suggest_allot_quantity in", values, "suggestAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andSuggestAllotQuantityNotIn(List<BigDecimal> values) {
            addCriterion("suggest_allot_quantity not in", values, "suggestAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andSuggestAllotQuantityBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("suggest_allot_quantity between", value1, value2, "suggestAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andSuggestAllotQuantityNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("suggest_allot_quantity not between", value1, value2, "suggestAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andRealAllotQuantityIsNull() {
            addCriterion("real_allot_quantity is null");
            return (Criteria) this;
        }

        public Criteria andRealAllotQuantityIsNotNull() {
            addCriterion("real_allot_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andRealAllotQuantityEqualTo(BigDecimal value) {
            addCriterion("real_allot_quantity =", value, "realAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andRealAllotQuantityNotEqualTo(BigDecimal value) {
            addCriterion("real_allot_quantity <>", value, "realAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andRealAllotQuantityGreaterThan(BigDecimal value) {
            addCriterion("real_allot_quantity >", value, "realAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andRealAllotQuantityGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("real_allot_quantity >=", value, "realAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andRealAllotQuantityLessThan(BigDecimal value) {
            addCriterion("real_allot_quantity <", value, "realAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andRealAllotQuantityLessThanOrEqualTo(BigDecimal value) {
            addCriterion("real_allot_quantity <=", value, "realAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andRealAllotQuantityIn(List<BigDecimal> values) {
            addCriterion("real_allot_quantity in", values, "realAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andRealAllotQuantityNotIn(List<BigDecimal> values) {
            addCriterion("real_allot_quantity not in", values, "realAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andRealAllotQuantityBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("real_allot_quantity between", value1, value2, "realAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andRealAllotQuantityNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("real_allot_quantity not between", value1, value2, "realAllotQuantity");
            return (Criteria) this;
        }

        public Criteria andExpectSaleDaysIsNull() {
            addCriterion("expect_sale_days is null");
            return (Criteria) this;
        }

        public Criteria andExpectSaleDaysIsNotNull() {
            addCriterion("expect_sale_days is not null");
            return (Criteria) this;
        }

        public Criteria andExpectSaleDaysEqualTo(BigDecimal value) {
            addCriterion("expect_sale_days =", value, "expectSaleDays");
            return (Criteria) this;
        }

        public Criteria andExpectSaleDaysNotEqualTo(BigDecimal value) {
            addCriterion("expect_sale_days <>", value, "expectSaleDays");
            return (Criteria) this;
        }

        public Criteria andExpectSaleDaysGreaterThan(BigDecimal value) {
            addCriterion("expect_sale_days >", value, "expectSaleDays");
            return (Criteria) this;
        }

        public Criteria andExpectSaleDaysGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("expect_sale_days >=", value, "expectSaleDays");
            return (Criteria) this;
        }

        public Criteria andExpectSaleDaysLessThan(BigDecimal value) {
            addCriterion("expect_sale_days <", value, "expectSaleDays");
            return (Criteria) this;
        }

        public Criteria andExpectSaleDaysLessThanOrEqualTo(BigDecimal value) {
            addCriterion("expect_sale_days <=", value, "expectSaleDays");
            return (Criteria) this;
        }

        public Criteria andExpectSaleDaysIn(List<BigDecimal> values) {
            addCriterion("expect_sale_days in", values, "expectSaleDays");
            return (Criteria) this;
        }

        public Criteria andExpectSaleDaysNotIn(List<BigDecimal> values) {
            addCriterion("expect_sale_days not in", values, "expectSaleDays");
            return (Criteria) this;
        }

        public Criteria andExpectSaleDaysBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("expect_sale_days between", value1, value2, "expectSaleDays");
            return (Criteria) this;
        }

        public Criteria andExpectSaleDaysNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("expect_sale_days not between", value1, value2, "expectSaleDays");
            return (Criteria) this;
        }

        public Criteria andApproveStatusIsNull() {
            addCriterion("approve_status is null");
            return (Criteria) this;
        }

        public Criteria andApproveStatusIsNotNull() {
            addCriterion("approve_status is not null");
            return (Criteria) this;
        }

        public Criteria andApproveStatusEqualTo(Byte value) {
            addCriterion("approve_status =", value, "approveStatus");
            return (Criteria) this;
        }

        public Criteria andApproveStatusNotEqualTo(Byte value) {
            addCriterion("approve_status <>", value, "approveStatus");
            return (Criteria) this;
        }

        public Criteria andApproveStatusGreaterThan(Byte value) {
            addCriterion("approve_status >", value, "approveStatus");
            return (Criteria) this;
        }

        public Criteria andApproveStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("approve_status >=", value, "approveStatus");
            return (Criteria) this;
        }

        public Criteria andApproveStatusLessThan(Byte value) {
            addCriterion("approve_status <", value, "approveStatus");
            return (Criteria) this;
        }

        public Criteria andApproveStatusLessThanOrEqualTo(Byte value) {
            addCriterion("approve_status <=", value, "approveStatus");
            return (Criteria) this;
        }

        public Criteria andApproveStatusIn(List<Byte> values) {
            addCriterion("approve_status in", values, "approveStatus");
            return (Criteria) this;
        }

        public Criteria andApproveStatusNotIn(List<Byte> values) {
            addCriterion("approve_status not in", values, "approveStatus");
            return (Criteria) this;
        }

        public Criteria andApproveStatusBetween(Byte value1, Byte value2) {
            addCriterion("approve_status between", value1, value2, "approveStatus");
            return (Criteria) this;
        }

        public Criteria andApproveStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("approve_status not between", value1, value2, "approveStatus");
            return (Criteria) this;
        }

        public Criteria andApproveByIsNull() {
            addCriterion("approve_by is null");
            return (Criteria) this;
        }

        public Criteria andApproveByIsNotNull() {
            addCriterion("approve_by is not null");
            return (Criteria) this;
        }

        public Criteria andApproveByEqualTo(Long value) {
            addCriterion("approve_by =", value, "approveBy");
            return (Criteria) this;
        }

        public Criteria andApproveByNotEqualTo(Long value) {
            addCriterion("approve_by <>", value, "approveBy");
            return (Criteria) this;
        }

        public Criteria andApproveByGreaterThan(Long value) {
            addCriterion("approve_by >", value, "approveBy");
            return (Criteria) this;
        }

        public Criteria andApproveByGreaterThanOrEqualTo(Long value) {
            addCriterion("approve_by >=", value, "approveBy");
            return (Criteria) this;
        }

        public Criteria andApproveByLessThan(Long value) {
            addCriterion("approve_by <", value, "approveBy");
            return (Criteria) this;
        }

        public Criteria andApproveByLessThanOrEqualTo(Long value) {
            addCriterion("approve_by <=", value, "approveBy");
            return (Criteria) this;
        }

        public Criteria andApproveByIn(List<Long> values) {
            addCriterion("approve_by in", values, "approveBy");
            return (Criteria) this;
        }

        public Criteria andApproveByNotIn(List<Long> values) {
            addCriterion("approve_by not in", values, "approveBy");
            return (Criteria) this;
        }

        public Criteria andApproveByBetween(Long value1, Long value2) {
            addCriterion("approve_by between", value1, value2, "approveBy");
            return (Criteria) this;
        }

        public Criteria andApproveByNotBetween(Long value1, Long value2) {
            addCriterion("approve_by not between", value1, value2, "approveBy");
            return (Criteria) this;
        }

        public Criteria andApproveNameIsNull() {
            addCriterion("approve_name is null");
            return (Criteria) this;
        }

        public Criteria andApproveNameIsNotNull() {
            addCriterion("approve_name is not null");
            return (Criteria) this;
        }

        public Criteria andApproveNameEqualTo(String value) {
            addCriterion("approve_name =", value, "approveName");
            return (Criteria) this;
        }

        public Criteria andApproveNameNotEqualTo(String value) {
            addCriterion("approve_name <>", value, "approveName");
            return (Criteria) this;
        }

        public Criteria andApproveNameGreaterThan(String value) {
            addCriterion("approve_name >", value, "approveName");
            return (Criteria) this;
        }

        public Criteria andApproveNameGreaterThanOrEqualTo(String value) {
            addCriterion("approve_name >=", value, "approveName");
            return (Criteria) this;
        }

        public Criteria andApproveNameLessThan(String value) {
            addCriterion("approve_name <", value, "approveName");
            return (Criteria) this;
        }

        public Criteria andApproveNameLessThanOrEqualTo(String value) {
            addCriterion("approve_name <=", value, "approveName");
            return (Criteria) this;
        }

        public Criteria andApproveNameLike(String value) {
            addCriterion("approve_name like", value, "approveName");
            return (Criteria) this;
        }

        public Criteria andApproveNameNotLike(String value) {
            addCriterion("approve_name not like", value, "approveName");
            return (Criteria) this;
        }

        public Criteria andApproveNameIn(List<String> values) {
            addCriterion("approve_name in", values, "approveName");
            return (Criteria) this;
        }

        public Criteria andApproveNameNotIn(List<String> values) {
            addCriterion("approve_name not in", values, "approveName");
            return (Criteria) this;
        }

        public Criteria andApproveNameBetween(String value1, String value2) {
            addCriterion("approve_name between", value1, value2, "approveName");
            return (Criteria) this;
        }

        public Criteria andApproveNameNotBetween(String value1, String value2) {
            addCriterion("approve_name not between", value1, value2, "approveName");
            return (Criteria) this;
        }

        public Criteria andApproveTimeIsNull() {
            addCriterion("approve_time is null");
            return (Criteria) this;
        }

        public Criteria andApproveTimeIsNotNull() {
            addCriterion("approve_time is not null");
            return (Criteria) this;
        }

        public Criteria andApproveTimeEqualTo(Date value) {
            addCriterion("approve_time =", value, "approveTime");
            return (Criteria) this;
        }

        public Criteria andApproveTimeNotEqualTo(Date value) {
            addCriterion("approve_time <>", value, "approveTime");
            return (Criteria) this;
        }

        public Criteria andApproveTimeGreaterThan(Date value) {
            addCriterion("approve_time >", value, "approveTime");
            return (Criteria) this;
        }

        public Criteria andApproveTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("approve_time >=", value, "approveTime");
            return (Criteria) this;
        }

        public Criteria andApproveTimeLessThan(Date value) {
            addCriterion("approve_time <", value, "approveTime");
            return (Criteria) this;
        }

        public Criteria andApproveTimeLessThanOrEqualTo(Date value) {
            addCriterion("approve_time <=", value, "approveTime");
            return (Criteria) this;
        }

        public Criteria andApproveTimeIn(List<Date> values) {
            addCriterion("approve_time in", values, "approveTime");
            return (Criteria) this;
        }

        public Criteria andApproveTimeNotIn(List<Date> values) {
            addCriterion("approve_time not in", values, "approveTime");
            return (Criteria) this;
        }

        public Criteria andApproveTimeBetween(Date value1, Date value2) {
            addCriterion("approve_time between", value1, value2, "approveTime");
            return (Criteria) this;
        }

        public Criteria andApproveTimeNotBetween(Date value1, Date value2) {
            addCriterion("approve_time not between", value1, value2, "approveTime");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Byte value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Byte value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Byte value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Byte value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Byte value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Byte> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Byte> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Byte value1, Byte value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIsNull() {
            addCriterion("gmt_update is null");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIsNotNull() {
            addCriterion("gmt_update is not null");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateEqualTo(Date value) {
            addCriterion("gmt_update =", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotEqualTo(Date value) {
            addCriterion("gmt_update <>", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateGreaterThan(Date value) {
            addCriterion("gmt_update >", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_update >=", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateLessThan(Date value) {
            addCriterion("gmt_update <", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_update <=", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIn(List<Date> values) {
            addCriterion("gmt_update in", values, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotIn(List<Date> values) {
            addCriterion("gmt_update not in", values, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateBetween(Date value1, Date value2) {
            addCriterion("gmt_update between", value1, value2, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_update not between", value1, value2, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andExtendIsNull() {
            addCriterion("extend is null");
            return (Criteria) this;
        }

        public Criteria andExtendIsNotNull() {
            addCriterion("extend is not null");
            return (Criteria) this;
        }

        public Criteria andExtendEqualTo(String value) {
            addCriterion("extend =", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotEqualTo(String value) {
            addCriterion("extend <>", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendGreaterThan(String value) {
            addCriterion("extend >", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendGreaterThanOrEqualTo(String value) {
            addCriterion("extend >=", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLessThan(String value) {
            addCriterion("extend <", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLessThanOrEqualTo(String value) {
            addCriterion("extend <=", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLike(String value) {
            addCriterion("extend like", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotLike(String value) {
            addCriterion("extend not like", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendIn(List<String> values) {
            addCriterion("extend in", values, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotIn(List<String> values) {
            addCriterion("extend not in", values, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendBetween(String value1, String value2) {
            addCriterion("extend between", value1, value2, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotBetween(String value1, String value2) {
            addCriterion("extend not between", value1, value2, "extend");
            return (Criteria) this;
        }

        public Criteria andVersionIsNull() {
            addCriterion("version is null");
            return (Criteria) this;
        }

        public Criteria andVersionIsNotNull() {
            addCriterion("version is not null");
            return (Criteria) this;
        }

        public Criteria andVersionEqualTo(Integer value) {
            addCriterion("version =", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotEqualTo(Integer value) {
            addCriterion("version <>", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThan(Integer value) {
            addCriterion("version >", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThanOrEqualTo(Integer value) {
            addCriterion("version >=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThan(Integer value) {
            addCriterion("version <", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThanOrEqualTo(Integer value) {
            addCriterion("version <=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionIn(List<Integer> values) {
            addCriterion("version in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotIn(List<Integer> values) {
            addCriterion("version not in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionBetween(Integer value1, Integer value2) {
            addCriterion("version between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotBetween(Integer value1, Integer value2) {
            addCriterion("version not between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("created_by is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("created_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(Long value) {
            addCriterion("created_by =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(Long value) {
            addCriterion("created_by <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(Long value) {
            addCriterion("created_by >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(Long value) {
            addCriterion("created_by >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(Long value) {
            addCriterion("created_by <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(Long value) {
            addCriterion("created_by <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<Long> values) {
            addCriterion("created_by in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<Long> values) {
            addCriterion("created_by not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(Long value1, Long value2) {
            addCriterion("created_by between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(Long value1, Long value2) {
            addCriterion("created_by not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedNameIsNull() {
            addCriterion("created_name is null");
            return (Criteria) this;
        }

        public Criteria andCreatedNameIsNotNull() {
            addCriterion("created_name is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedNameEqualTo(String value) {
            addCriterion("created_name =", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotEqualTo(String value) {
            addCriterion("created_name <>", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameGreaterThan(String value) {
            addCriterion("created_name >", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameGreaterThanOrEqualTo(String value) {
            addCriterion("created_name >=", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameLessThan(String value) {
            addCriterion("created_name <", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameLessThanOrEqualTo(String value) {
            addCriterion("created_name <=", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameLike(String value) {
            addCriterion("created_name like", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotLike(String value) {
            addCriterion("created_name not like", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameIn(List<String> values) {
            addCriterion("created_name in", values, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotIn(List<String> values) {
            addCriterion("created_name not in", values, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameBetween(String value1, String value2) {
            addCriterion("created_name between", value1, value2, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotBetween(String value1, String value2) {
            addCriterion("created_name not between", value1, value2, "createdName");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNull() {
            addCriterion("updated_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNotNull() {
            addCriterion("updated_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByEqualTo(Long value) {
            addCriterion("updated_by =", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotEqualTo(Long value) {
            addCriterion("updated_by <>", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThan(Long value) {
            addCriterion("updated_by >", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThanOrEqualTo(Long value) {
            addCriterion("updated_by >=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThan(Long value) {
            addCriterion("updated_by <", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThanOrEqualTo(Long value) {
            addCriterion("updated_by <=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIn(List<Long> values) {
            addCriterion("updated_by in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotIn(List<Long> values) {
            addCriterion("updated_by not in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByBetween(Long value1, Long value2) {
            addCriterion("updated_by between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotBetween(Long value1, Long value2) {
            addCriterion("updated_by not between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameIsNull() {
            addCriterion("updated_name is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameIsNotNull() {
            addCriterion("updated_name is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameEqualTo(String value) {
            addCriterion("updated_name =", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotEqualTo(String value) {
            addCriterion("updated_name <>", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameGreaterThan(String value) {
            addCriterion("updated_name >", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameGreaterThanOrEqualTo(String value) {
            addCriterion("updated_name >=", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameLessThan(String value) {
            addCriterion("updated_name <", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameLessThanOrEqualTo(String value) {
            addCriterion("updated_name <=", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameLike(String value) {
            addCriterion("updated_name like", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotLike(String value) {
            addCriterion("updated_name not like", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameIn(List<String> values) {
            addCriterion("updated_name in", values, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotIn(List<String> values) {
            addCriterion("updated_name not in", values, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameBetween(String value1, String value2) {
            addCriterion("updated_name between", value1, value2, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotBetween(String value1, String value2) {
            addCriterion("updated_name not between", value1, value2, "updatedName");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}