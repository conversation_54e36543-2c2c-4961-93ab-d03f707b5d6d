package com.cowell.iscm.entityTidb;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class IscmStorePurchaseDetailExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public IscmStorePurchaseDetailExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIsNull() {
            addCriterion("company_code is null");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIsNotNull() {
            addCriterion("company_code is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeEqualTo(String value) {
            addCriterion("company_code =", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotEqualTo(String value) {
            addCriterion("company_code <>", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeGreaterThan(String value) {
            addCriterion("company_code >", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeGreaterThanOrEqualTo(String value) {
            addCriterion("company_code >=", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLessThan(String value) {
            addCriterion("company_code <", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLessThanOrEqualTo(String value) {
            addCriterion("company_code <=", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLike(String value) {
            addCriterion("company_code like", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotLike(String value) {
            addCriterion("company_code not like", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIn(List<String> values) {
            addCriterion("company_code in", values, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotIn(List<String> values) {
            addCriterion("company_code not in", values, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeBetween(String value1, String value2) {
            addCriterion("company_code between", value1, value2, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotBetween(String value1, String value2) {
            addCriterion("company_code not between", value1, value2, "companyCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeIsNull() {
            addCriterion("store_code is null");
            return (Criteria) this;
        }

        public Criteria andStoreCodeIsNotNull() {
            addCriterion("store_code is not null");
            return (Criteria) this;
        }

        public Criteria andStoreCodeEqualTo(String value) {
            addCriterion("store_code =", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotEqualTo(String value) {
            addCriterion("store_code <>", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeGreaterThan(String value) {
            addCriterion("store_code >", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeGreaterThanOrEqualTo(String value) {
            addCriterion("store_code >=", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeLessThan(String value) {
            addCriterion("store_code <", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeLessThanOrEqualTo(String value) {
            addCriterion("store_code <=", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeLike(String value) {
            addCriterion("store_code like", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotLike(String value) {
            addCriterion("store_code not like", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeIn(List<String> values) {
            addCriterion("store_code in", values, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotIn(List<String> values) {
            addCriterion("store_code not in", values, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeBetween(String value1, String value2) {
            addCriterion("store_code between", value1, value2, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotBetween(String value1, String value2) {
            addCriterion("store_code not between", value1, value2, "storeCode");
            return (Criteria) this;
        }

        public Criteria andPurchaseNoIsNull() {
            addCriterion("purchase_no is null");
            return (Criteria) this;
        }

        public Criteria andPurchaseNoIsNotNull() {
            addCriterion("purchase_no is not null");
            return (Criteria) this;
        }

        public Criteria andPurchaseNoEqualTo(String value) {
            addCriterion("purchase_no =", value, "purchaseNo");
            return (Criteria) this;
        }

        public Criteria andPurchaseNoNotEqualTo(String value) {
            addCriterion("purchase_no <>", value, "purchaseNo");
            return (Criteria) this;
        }

        public Criteria andPurchaseNoGreaterThan(String value) {
            addCriterion("purchase_no >", value, "purchaseNo");
            return (Criteria) this;
        }

        public Criteria andPurchaseNoGreaterThanOrEqualTo(String value) {
            addCriterion("purchase_no >=", value, "purchaseNo");
            return (Criteria) this;
        }

        public Criteria andPurchaseNoLessThan(String value) {
            addCriterion("purchase_no <", value, "purchaseNo");
            return (Criteria) this;
        }

        public Criteria andPurchaseNoLessThanOrEqualTo(String value) {
            addCriterion("purchase_no <=", value, "purchaseNo");
            return (Criteria) this;
        }

        public Criteria andPurchaseNoLike(String value) {
            addCriterion("purchase_no like", value, "purchaseNo");
            return (Criteria) this;
        }

        public Criteria andPurchaseNoNotLike(String value) {
            addCriterion("purchase_no not like", value, "purchaseNo");
            return (Criteria) this;
        }

        public Criteria andPurchaseNoIn(List<String> values) {
            addCriterion("purchase_no in", values, "purchaseNo");
            return (Criteria) this;
        }

        public Criteria andPurchaseNoNotIn(List<String> values) {
            addCriterion("purchase_no not in", values, "purchaseNo");
            return (Criteria) this;
        }

        public Criteria andPurchaseNoBetween(String value1, String value2) {
            addCriterion("purchase_no between", value1, value2, "purchaseNo");
            return (Criteria) this;
        }

        public Criteria andPurchaseNoNotBetween(String value1, String value2) {
            addCriterion("purchase_no not between", value1, value2, "purchaseNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoIsNull() {
            addCriterion("order_no is null");
            return (Criteria) this;
        }

        public Criteria andOrderNoIsNotNull() {
            addCriterion("order_no is not null");
            return (Criteria) this;
        }

        public Criteria andOrderNoEqualTo(String value) {
            addCriterion("order_no =", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotEqualTo(String value) {
            addCriterion("order_no <>", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoGreaterThan(String value) {
            addCriterion("order_no >", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoGreaterThanOrEqualTo(String value) {
            addCriterion("order_no >=", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLessThan(String value) {
            addCriterion("order_no <", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLessThanOrEqualTo(String value) {
            addCriterion("order_no <=", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLike(String value) {
            addCriterion("order_no like", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotLike(String value) {
            addCriterion("order_no not like", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoIn(List<String> values) {
            addCriterion("order_no in", values, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotIn(List<String> values) {
            addCriterion("order_no not in", values, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoBetween(String value1, String value2) {
            addCriterion("order_no between", value1, value2, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotBetween(String value1, String value2) {
            addCriterion("order_no not between", value1, value2, "orderNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoIsNull() {
            addCriterion("goods_no is null");
            return (Criteria) this;
        }

        public Criteria andGoodsNoIsNotNull() {
            addCriterion("goods_no is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsNoEqualTo(String value) {
            addCriterion("goods_no =", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotEqualTo(String value) {
            addCriterion("goods_no <>", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoGreaterThan(String value) {
            addCriterion("goods_no >", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoGreaterThanOrEqualTo(String value) {
            addCriterion("goods_no >=", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoLessThan(String value) {
            addCriterion("goods_no <", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoLessThanOrEqualTo(String value) {
            addCriterion("goods_no <=", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoLike(String value) {
            addCriterion("goods_no like", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotLike(String value) {
            addCriterion("goods_no not like", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoIn(List<String> values) {
            addCriterion("goods_no in", values, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotIn(List<String> values) {
            addCriterion("goods_no not in", values, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoBetween(String value1, String value2) {
            addCriterion("goods_no between", value1, value2, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotBetween(String value1, String value2) {
            addCriterion("goods_no not between", value1, value2, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNameIsNull() {
            addCriterion("goods_name is null");
            return (Criteria) this;
        }

        public Criteria andGoodsNameIsNotNull() {
            addCriterion("goods_name is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsNameEqualTo(String value) {
            addCriterion("goods_name =", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameNotEqualTo(String value) {
            addCriterion("goods_name <>", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameGreaterThan(String value) {
            addCriterion("goods_name >", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameGreaterThanOrEqualTo(String value) {
            addCriterion("goods_name >=", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameLessThan(String value) {
            addCriterion("goods_name <", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameLessThanOrEqualTo(String value) {
            addCriterion("goods_name <=", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameLike(String value) {
            addCriterion("goods_name like", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameNotLike(String value) {
            addCriterion("goods_name not like", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameIn(List<String> values) {
            addCriterion("goods_name in", values, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameNotIn(List<String> values) {
            addCriterion("goods_name not in", values, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameBetween(String value1, String value2) {
            addCriterion("goods_name between", value1, value2, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameNotBetween(String value1, String value2) {
            addCriterion("goods_name not between", value1, value2, "goodsName");
            return (Criteria) this;
        }

        public Criteria andStockQuantityIsNull() {
            addCriterion("stock_quantity is null");
            return (Criteria) this;
        }

        public Criteria andStockQuantityIsNotNull() {
            addCriterion("stock_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andStockQuantityEqualTo(BigDecimal value) {
            addCriterion("stock_quantity =", value, "stockQuantity");
            return (Criteria) this;
        }

        public Criteria andStockQuantityNotEqualTo(BigDecimal value) {
            addCriterion("stock_quantity <>", value, "stockQuantity");
            return (Criteria) this;
        }

        public Criteria andStockQuantityGreaterThan(BigDecimal value) {
            addCriterion("stock_quantity >", value, "stockQuantity");
            return (Criteria) this;
        }

        public Criteria andStockQuantityGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("stock_quantity >=", value, "stockQuantity");
            return (Criteria) this;
        }

        public Criteria andStockQuantityLessThan(BigDecimal value) {
            addCriterion("stock_quantity <", value, "stockQuantity");
            return (Criteria) this;
        }

        public Criteria andStockQuantityLessThanOrEqualTo(BigDecimal value) {
            addCriterion("stock_quantity <=", value, "stockQuantity");
            return (Criteria) this;
        }

        public Criteria andStockQuantityIn(List<BigDecimal> values) {
            addCriterion("stock_quantity in", values, "stockQuantity");
            return (Criteria) this;
        }

        public Criteria andStockQuantityNotIn(List<BigDecimal> values) {
            addCriterion("stock_quantity not in", values, "stockQuantity");
            return (Criteria) this;
        }

        public Criteria andStockQuantityBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("stock_quantity between", value1, value2, "stockQuantity");
            return (Criteria) this;
        }

        public Criteria andStockQuantityNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("stock_quantity not between", value1, value2, "stockQuantity");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitIsNull() {
            addCriterion("stock_upper_limit is null");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitIsNotNull() {
            addCriterion("stock_upper_limit is not null");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitEqualTo(BigDecimal value) {
            addCriterion("stock_upper_limit =", value, "stockUpperLimit");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitNotEqualTo(BigDecimal value) {
            addCriterion("stock_upper_limit <>", value, "stockUpperLimit");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitGreaterThan(BigDecimal value) {
            addCriterion("stock_upper_limit >", value, "stockUpperLimit");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("stock_upper_limit >=", value, "stockUpperLimit");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitLessThan(BigDecimal value) {
            addCriterion("stock_upper_limit <", value, "stockUpperLimit");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitLessThanOrEqualTo(BigDecimal value) {
            addCriterion("stock_upper_limit <=", value, "stockUpperLimit");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitIn(List<BigDecimal> values) {
            addCriterion("stock_upper_limit in", values, "stockUpperLimit");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitNotIn(List<BigDecimal> values) {
            addCriterion("stock_upper_limit not in", values, "stockUpperLimit");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("stock_upper_limit between", value1, value2, "stockUpperLimit");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("stock_upper_limit not between", value1, value2, "stockUpperLimit");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitIsNull() {
            addCriterion("stock_lower_limit is null");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitIsNotNull() {
            addCriterion("stock_lower_limit is not null");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitEqualTo(BigDecimal value) {
            addCriterion("stock_lower_limit =", value, "stockLowerLimit");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitNotEqualTo(BigDecimal value) {
            addCriterion("stock_lower_limit <>", value, "stockLowerLimit");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitGreaterThan(BigDecimal value) {
            addCriterion("stock_lower_limit >", value, "stockLowerLimit");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("stock_lower_limit >=", value, "stockLowerLimit");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitLessThan(BigDecimal value) {
            addCriterion("stock_lower_limit <", value, "stockLowerLimit");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitLessThanOrEqualTo(BigDecimal value) {
            addCriterion("stock_lower_limit <=", value, "stockLowerLimit");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitIn(List<BigDecimal> values) {
            addCriterion("stock_lower_limit in", values, "stockLowerLimit");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitNotIn(List<BigDecimal> values) {
            addCriterion("stock_lower_limit not in", values, "stockLowerLimit");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("stock_lower_limit between", value1, value2, "stockLowerLimit");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("stock_lower_limit not between", value1, value2, "stockLowerLimit");
            return (Criteria) this;
        }

        public Criteria andSaleQuantity30IsNull() {
            addCriterion("sale_quantity_30 is null");
            return (Criteria) this;
        }

        public Criteria andSaleQuantity30IsNotNull() {
            addCriterion("sale_quantity_30 is not null");
            return (Criteria) this;
        }

        public Criteria andSaleQuantity30EqualTo(BigDecimal value) {
            addCriterion("sale_quantity_30 =", value, "saleQuantity30");
            return (Criteria) this;
        }

        public Criteria andSaleQuantity30NotEqualTo(BigDecimal value) {
            addCriterion("sale_quantity_30 <>", value, "saleQuantity30");
            return (Criteria) this;
        }

        public Criteria andSaleQuantity30GreaterThan(BigDecimal value) {
            addCriterion("sale_quantity_30 >", value, "saleQuantity30");
            return (Criteria) this;
        }

        public Criteria andSaleQuantity30GreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("sale_quantity_30 >=", value, "saleQuantity30");
            return (Criteria) this;
        }

        public Criteria andSaleQuantity30LessThan(BigDecimal value) {
            addCriterion("sale_quantity_30 <", value, "saleQuantity30");
            return (Criteria) this;
        }

        public Criteria andSaleQuantity30LessThanOrEqualTo(BigDecimal value) {
            addCriterion("sale_quantity_30 <=", value, "saleQuantity30");
            return (Criteria) this;
        }

        public Criteria andSaleQuantity30In(List<BigDecimal> values) {
            addCriterion("sale_quantity_30 in", values, "saleQuantity30");
            return (Criteria) this;
        }

        public Criteria andSaleQuantity30NotIn(List<BigDecimal> values) {
            addCriterion("sale_quantity_30 not in", values, "saleQuantity30");
            return (Criteria) this;
        }

        public Criteria andSaleQuantity30Between(BigDecimal value1, BigDecimal value2) {
            addCriterion("sale_quantity_30 between", value1, value2, "saleQuantity30");
            return (Criteria) this;
        }

        public Criteria andSaleQuantity30NotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("sale_quantity_30 not between", value1, value2, "saleQuantity30");
            return (Criteria) this;
        }

        public Criteria andSaleTimes30IsNull() {
            addCriterion("sale_times_30 is null");
            return (Criteria) this;
        }

        public Criteria andSaleTimes30IsNotNull() {
            addCriterion("sale_times_30 is not null");
            return (Criteria) this;
        }

        public Criteria andSaleTimes30EqualTo(BigDecimal value) {
            addCriterion("sale_times_30 =", value, "saleTimes30");
            return (Criteria) this;
        }

        public Criteria andSaleTimes30NotEqualTo(BigDecimal value) {
            addCriterion("sale_times_30 <>", value, "saleTimes30");
            return (Criteria) this;
        }

        public Criteria andSaleTimes30GreaterThan(BigDecimal value) {
            addCriterion("sale_times_30 >", value, "saleTimes30");
            return (Criteria) this;
        }

        public Criteria andSaleTimes30GreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("sale_times_30 >=", value, "saleTimes30");
            return (Criteria) this;
        }

        public Criteria andSaleTimes30LessThan(BigDecimal value) {
            addCriterion("sale_times_30 <", value, "saleTimes30");
            return (Criteria) this;
        }

        public Criteria andSaleTimes30LessThanOrEqualTo(BigDecimal value) {
            addCriterion("sale_times_30 <=", value, "saleTimes30");
            return (Criteria) this;
        }

        public Criteria andSaleTimes30In(List<BigDecimal> values) {
            addCriterion("sale_times_30 in", values, "saleTimes30");
            return (Criteria) this;
        }

        public Criteria andSaleTimes30NotIn(List<BigDecimal> values) {
            addCriterion("sale_times_30 not in", values, "saleTimes30");
            return (Criteria) this;
        }

        public Criteria andSaleTimes30Between(BigDecimal value1, BigDecimal value2) {
            addCriterion("sale_times_30 between", value1, value2, "saleTimes30");
            return (Criteria) this;
        }

        public Criteria andSaleTimes30NotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("sale_times_30 not between", value1, value2, "saleTimes30");
            return (Criteria) this;
        }

        public Criteria andAutoPurchaseQuantityIsNull() {
            addCriterion("auto_purchase_quantity is null");
            return (Criteria) this;
        }

        public Criteria andAutoPurchaseQuantityIsNotNull() {
            addCriterion("auto_purchase_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andAutoPurchaseQuantityEqualTo(BigDecimal value) {
            addCriterion("auto_purchase_quantity =", value, "autoPurchaseQuantity");
            return (Criteria) this;
        }

        public Criteria andAutoPurchaseQuantityNotEqualTo(BigDecimal value) {
            addCriterion("auto_purchase_quantity <>", value, "autoPurchaseQuantity");
            return (Criteria) this;
        }

        public Criteria andAutoPurchaseQuantityGreaterThan(BigDecimal value) {
            addCriterion("auto_purchase_quantity >", value, "autoPurchaseQuantity");
            return (Criteria) this;
        }

        public Criteria andAutoPurchaseQuantityGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("auto_purchase_quantity >=", value, "autoPurchaseQuantity");
            return (Criteria) this;
        }

        public Criteria andAutoPurchaseQuantityLessThan(BigDecimal value) {
            addCriterion("auto_purchase_quantity <", value, "autoPurchaseQuantity");
            return (Criteria) this;
        }

        public Criteria andAutoPurchaseQuantityLessThanOrEqualTo(BigDecimal value) {
            addCriterion("auto_purchase_quantity <=", value, "autoPurchaseQuantity");
            return (Criteria) this;
        }

        public Criteria andAutoPurchaseQuantityIn(List<BigDecimal> values) {
            addCriterion("auto_purchase_quantity in", values, "autoPurchaseQuantity");
            return (Criteria) this;
        }

        public Criteria andAutoPurchaseQuantityNotIn(List<BigDecimal> values) {
            addCriterion("auto_purchase_quantity not in", values, "autoPurchaseQuantity");
            return (Criteria) this;
        }

        public Criteria andAutoPurchaseQuantityBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("auto_purchase_quantity between", value1, value2, "autoPurchaseQuantity");
            return (Criteria) this;
        }

        public Criteria andAutoPurchaseQuantityNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("auto_purchase_quantity not between", value1, value2, "autoPurchaseQuantity");
            return (Criteria) this;
        }

        public Criteria andPosAverageDailySalesIsNull() {
            addCriterion("pos_average_daily_sales is null");
            return (Criteria) this;
        }

        public Criteria andPosAverageDailySalesIsNotNull() {
            addCriterion("pos_average_daily_sales is not null");
            return (Criteria) this;
        }

        public Criteria andPosAverageDailySalesEqualTo(BigDecimal value) {
            addCriterion("pos_average_daily_sales =", value, "posAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andPosAverageDailySalesNotEqualTo(BigDecimal value) {
            addCriterion("pos_average_daily_sales <>", value, "posAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andPosAverageDailySalesGreaterThan(BigDecimal value) {
            addCriterion("pos_average_daily_sales >", value, "posAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andPosAverageDailySalesGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("pos_average_daily_sales >=", value, "posAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andPosAverageDailySalesLessThan(BigDecimal value) {
            addCriterion("pos_average_daily_sales <", value, "posAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andPosAverageDailySalesLessThanOrEqualTo(BigDecimal value) {
            addCriterion("pos_average_daily_sales <=", value, "posAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andPosAverageDailySalesIn(List<BigDecimal> values) {
            addCriterion("pos_average_daily_sales in", values, "posAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andPosAverageDailySalesNotIn(List<BigDecimal> values) {
            addCriterion("pos_average_daily_sales not in", values, "posAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andPosAverageDailySalesBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("pos_average_daily_sales between", value1, value2, "posAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andPosAverageDailySalesNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("pos_average_daily_sales not between", value1, value2, "posAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andBdpAverageDailySalesIsNull() {
            addCriterion("bdp_average_daily_sales is null");
            return (Criteria) this;
        }

        public Criteria andBdpAverageDailySalesIsNotNull() {
            addCriterion("bdp_average_daily_sales is not null");
            return (Criteria) this;
        }

        public Criteria andBdpAverageDailySalesEqualTo(BigDecimal value) {
            addCriterion("bdp_average_daily_sales =", value, "bdpAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andBdpAverageDailySalesNotEqualTo(BigDecimal value) {
            addCriterion("bdp_average_daily_sales <>", value, "bdpAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andBdpAverageDailySalesGreaterThan(BigDecimal value) {
            addCriterion("bdp_average_daily_sales >", value, "bdpAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andBdpAverageDailySalesGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("bdp_average_daily_sales >=", value, "bdpAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andBdpAverageDailySalesLessThan(BigDecimal value) {
            addCriterion("bdp_average_daily_sales <", value, "bdpAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andBdpAverageDailySalesLessThanOrEqualTo(BigDecimal value) {
            addCriterion("bdp_average_daily_sales <=", value, "bdpAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andBdpAverageDailySalesIn(List<BigDecimal> values) {
            addCriterion("bdp_average_daily_sales in", values, "bdpAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andBdpAverageDailySalesNotIn(List<BigDecimal> values) {
            addCriterion("bdp_average_daily_sales not in", values, "bdpAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andBdpAverageDailySalesBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("bdp_average_daily_sales between", value1, value2, "bdpAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andBdpAverageDailySalesNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("bdp_average_daily_sales not between", value1, value2, "bdpAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Byte value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Byte value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Byte value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Byte value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Byte value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Byte> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Byte> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Byte value1, Byte value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIsNull() {
            addCriterion("gmt_update is null");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIsNotNull() {
            addCriterion("gmt_update is not null");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateEqualTo(Date value) {
            addCriterion("gmt_update =", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotEqualTo(Date value) {
            addCriterion("gmt_update <>", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateGreaterThan(Date value) {
            addCriterion("gmt_update >", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_update >=", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateLessThan(Date value) {
            addCriterion("gmt_update <", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_update <=", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIn(List<Date> values) {
            addCriterion("gmt_update in", values, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotIn(List<Date> values) {
            addCriterion("gmt_update not in", values, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateBetween(Date value1, Date value2) {
            addCriterion("gmt_update between", value1, value2, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_update not between", value1, value2, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andExtendIsNull() {
            addCriterion("extend is null");
            return (Criteria) this;
        }

        public Criteria andExtendIsNotNull() {
            addCriterion("extend is not null");
            return (Criteria) this;
        }

        public Criteria andExtendEqualTo(String value) {
            addCriterion("extend =", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotEqualTo(String value) {
            addCriterion("extend <>", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendGreaterThan(String value) {
            addCriterion("extend >", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendGreaterThanOrEqualTo(String value) {
            addCriterion("extend >=", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLessThan(String value) {
            addCriterion("extend <", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLessThanOrEqualTo(String value) {
            addCriterion("extend <=", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLike(String value) {
            addCriterion("extend like", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotLike(String value) {
            addCriterion("extend not like", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendIn(List<String> values) {
            addCriterion("extend in", values, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotIn(List<String> values) {
            addCriterion("extend not in", values, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendBetween(String value1, String value2) {
            addCriterion("extend between", value1, value2, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotBetween(String value1, String value2) {
            addCriterion("extend not between", value1, value2, "extend");
            return (Criteria) this;
        }

        public Criteria andVersionIsNull() {
            addCriterion("version is null");
            return (Criteria) this;
        }

        public Criteria andVersionIsNotNull() {
            addCriterion("version is not null");
            return (Criteria) this;
        }

        public Criteria andVersionEqualTo(Integer value) {
            addCriterion("version =", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotEqualTo(Integer value) {
            addCriterion("version <>", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThan(Integer value) {
            addCriterion("version >", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThanOrEqualTo(Integer value) {
            addCriterion("version >=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThan(Integer value) {
            addCriterion("version <", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThanOrEqualTo(Integer value) {
            addCriterion("version <=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionIn(List<Integer> values) {
            addCriterion("version in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotIn(List<Integer> values) {
            addCriterion("version not in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionBetween(Integer value1, Integer value2) {
            addCriterion("version between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotBetween(Integer value1, Integer value2) {
            addCriterion("version not between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("created_by is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("created_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(Long value) {
            addCriterion("created_by =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(Long value) {
            addCriterion("created_by <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(Long value) {
            addCriterion("created_by >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(Long value) {
            addCriterion("created_by >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(Long value) {
            addCriterion("created_by <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(Long value) {
            addCriterion("created_by <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<Long> values) {
            addCriterion("created_by in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<Long> values) {
            addCriterion("created_by not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(Long value1, Long value2) {
            addCriterion("created_by between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(Long value1, Long value2) {
            addCriterion("created_by not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedNameIsNull() {
            addCriterion("created_name is null");
            return (Criteria) this;
        }

        public Criteria andCreatedNameIsNotNull() {
            addCriterion("created_name is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedNameEqualTo(String value) {
            addCriterion("created_name =", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotEqualTo(String value) {
            addCriterion("created_name <>", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameGreaterThan(String value) {
            addCriterion("created_name >", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameGreaterThanOrEqualTo(String value) {
            addCriterion("created_name >=", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameLessThan(String value) {
            addCriterion("created_name <", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameLessThanOrEqualTo(String value) {
            addCriterion("created_name <=", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameLike(String value) {
            addCriterion("created_name like", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotLike(String value) {
            addCriterion("created_name not like", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameIn(List<String> values) {
            addCriterion("created_name in", values, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotIn(List<String> values) {
            addCriterion("created_name not in", values, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameBetween(String value1, String value2) {
            addCriterion("created_name between", value1, value2, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotBetween(String value1, String value2) {
            addCriterion("created_name not between", value1, value2, "createdName");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNull() {
            addCriterion("updated_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNotNull() {
            addCriterion("updated_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByEqualTo(Long value) {
            addCriterion("updated_by =", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotEqualTo(Long value) {
            addCriterion("updated_by <>", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThan(Long value) {
            addCriterion("updated_by >", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThanOrEqualTo(Long value) {
            addCriterion("updated_by >=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThan(Long value) {
            addCriterion("updated_by <", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThanOrEqualTo(Long value) {
            addCriterion("updated_by <=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIn(List<Long> values) {
            addCriterion("updated_by in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotIn(List<Long> values) {
            addCriterion("updated_by not in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByBetween(Long value1, Long value2) {
            addCriterion("updated_by between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotBetween(Long value1, Long value2) {
            addCriterion("updated_by not between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameIsNull() {
            addCriterion("updated_name is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameIsNotNull() {
            addCriterion("updated_name is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameEqualTo(String value) {
            addCriterion("updated_name =", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotEqualTo(String value) {
            addCriterion("updated_name <>", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameGreaterThan(String value) {
            addCriterion("updated_name >", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameGreaterThanOrEqualTo(String value) {
            addCriterion("updated_name >=", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameLessThan(String value) {
            addCriterion("updated_name <", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameLessThanOrEqualTo(String value) {
            addCriterion("updated_name <=", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameLike(String value) {
            addCriterion("updated_name like", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotLike(String value) {
            addCriterion("updated_name not like", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameIn(List<String> values) {
            addCriterion("updated_name in", values, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotIn(List<String> values) {
            addCriterion("updated_name not in", values, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameBetween(String value1, String value2) {
            addCriterion("updated_name between", value1, value2, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotBetween(String value1, String value2) {
            addCriterion("updated_name not between", value1, value2, "updatedName");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}