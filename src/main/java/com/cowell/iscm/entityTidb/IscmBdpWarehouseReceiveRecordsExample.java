package com.cowell.iscm.entityTidb;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class IscmBdpWarehouseReceiveRecordsExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Long offset;

    public IscmBdpWarehouseReceiveRecordsExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Long offset) {
        this.offset = offset;
    }

    public Long getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIsNull() {
            addCriterion("company_code is null");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIsNotNull() {
            addCriterion("company_code is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeEqualTo(String value) {
            addCriterion("company_code =", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotEqualTo(String value) {
            addCriterion("company_code <>", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeGreaterThan(String value) {
            addCriterion("company_code >", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeGreaterThanOrEqualTo(String value) {
            addCriterion("company_code >=", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLessThan(String value) {
            addCriterion("company_code <", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLessThanOrEqualTo(String value) {
            addCriterion("company_code <=", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLike(String value) {
            addCriterion("company_code like", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotLike(String value) {
            addCriterion("company_code not like", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIn(List<String> values) {
            addCriterion("company_code in", values, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotIn(List<String> values) {
            addCriterion("company_code not in", values, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeBetween(String value1, String value2) {
            addCriterion("company_code between", value1, value2, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotBetween(String value1, String value2) {
            addCriterion("company_code not between", value1, value2, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyBdpCodeIsNull() {
            addCriterion("company_bdp_code is null");
            return (Criteria) this;
        }

        public Criteria andCompanyBdpCodeIsNotNull() {
            addCriterion("company_bdp_code is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyBdpCodeEqualTo(String value) {
            addCriterion("company_bdp_code =", value, "companyBdpCode");
            return (Criteria) this;
        }

        public Criteria andCompanyBdpCodeNotEqualTo(String value) {
            addCriterion("company_bdp_code <>", value, "companyBdpCode");
            return (Criteria) this;
        }

        public Criteria andCompanyBdpCodeGreaterThan(String value) {
            addCriterion("company_bdp_code >", value, "companyBdpCode");
            return (Criteria) this;
        }

        public Criteria andCompanyBdpCodeGreaterThanOrEqualTo(String value) {
            addCriterion("company_bdp_code >=", value, "companyBdpCode");
            return (Criteria) this;
        }

        public Criteria andCompanyBdpCodeLessThan(String value) {
            addCriterion("company_bdp_code <", value, "companyBdpCode");
            return (Criteria) this;
        }

        public Criteria andCompanyBdpCodeLessThanOrEqualTo(String value) {
            addCriterion("company_bdp_code <=", value, "companyBdpCode");
            return (Criteria) this;
        }

        public Criteria andCompanyBdpCodeLike(String value) {
            addCriterion("company_bdp_code like", value, "companyBdpCode");
            return (Criteria) this;
        }

        public Criteria andCompanyBdpCodeNotLike(String value) {
            addCriterion("company_bdp_code not like", value, "companyBdpCode");
            return (Criteria) this;
        }

        public Criteria andCompanyBdpCodeIn(List<String> values) {
            addCriterion("company_bdp_code in", values, "companyBdpCode");
            return (Criteria) this;
        }

        public Criteria andCompanyBdpCodeNotIn(List<String> values) {
            addCriterion("company_bdp_code not in", values, "companyBdpCode");
            return (Criteria) this;
        }

        public Criteria andCompanyBdpCodeBetween(String value1, String value2) {
            addCriterion("company_bdp_code between", value1, value2, "companyBdpCode");
            return (Criteria) this;
        }

        public Criteria andCompanyBdpCodeNotBetween(String value1, String value2) {
            addCriterion("company_bdp_code not between", value1, value2, "companyBdpCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeIsNull() {
            addCriterion("store_code is null");
            return (Criteria) this;
        }

        public Criteria andStoreCodeIsNotNull() {
            addCriterion("store_code is not null");
            return (Criteria) this;
        }

        public Criteria andStoreCodeEqualTo(String value) {
            addCriterion("store_code =", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotEqualTo(String value) {
            addCriterion("store_code <>", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeGreaterThan(String value) {
            addCriterion("store_code >", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeGreaterThanOrEqualTo(String value) {
            addCriterion("store_code >=", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeLessThan(String value) {
            addCriterion("store_code <", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeLessThanOrEqualTo(String value) {
            addCriterion("store_code <=", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeLike(String value) {
            addCriterion("store_code like", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotLike(String value) {
            addCriterion("store_code not like", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeIn(List<String> values) {
            addCriterion("store_code in", values, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotIn(List<String> values) {
            addCriterion("store_code not in", values, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeBetween(String value1, String value2) {
            addCriterion("store_code between", value1, value2, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotBetween(String value1, String value2) {
            addCriterion("store_code not between", value1, value2, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreBdpCodeIsNull() {
            addCriterion("store_bdp_code is null");
            return (Criteria) this;
        }

        public Criteria andStoreBdpCodeIsNotNull() {
            addCriterion("store_bdp_code is not null");
            return (Criteria) this;
        }

        public Criteria andStoreBdpCodeEqualTo(String value) {
            addCriterion("store_bdp_code =", value, "storeBdpCode");
            return (Criteria) this;
        }

        public Criteria andStoreBdpCodeNotEqualTo(String value) {
            addCriterion("store_bdp_code <>", value, "storeBdpCode");
            return (Criteria) this;
        }

        public Criteria andStoreBdpCodeGreaterThan(String value) {
            addCriterion("store_bdp_code >", value, "storeBdpCode");
            return (Criteria) this;
        }

        public Criteria andStoreBdpCodeGreaterThanOrEqualTo(String value) {
            addCriterion("store_bdp_code >=", value, "storeBdpCode");
            return (Criteria) this;
        }

        public Criteria andStoreBdpCodeLessThan(String value) {
            addCriterion("store_bdp_code <", value, "storeBdpCode");
            return (Criteria) this;
        }

        public Criteria andStoreBdpCodeLessThanOrEqualTo(String value) {
            addCriterion("store_bdp_code <=", value, "storeBdpCode");
            return (Criteria) this;
        }

        public Criteria andStoreBdpCodeLike(String value) {
            addCriterion("store_bdp_code like", value, "storeBdpCode");
            return (Criteria) this;
        }

        public Criteria andStoreBdpCodeNotLike(String value) {
            addCriterion("store_bdp_code not like", value, "storeBdpCode");
            return (Criteria) this;
        }

        public Criteria andStoreBdpCodeIn(List<String> values) {
            addCriterion("store_bdp_code in", values, "storeBdpCode");
            return (Criteria) this;
        }

        public Criteria andStoreBdpCodeNotIn(List<String> values) {
            addCriterion("store_bdp_code not in", values, "storeBdpCode");
            return (Criteria) this;
        }

        public Criteria andStoreBdpCodeBetween(String value1, String value2) {
            addCriterion("store_bdp_code between", value1, value2, "storeBdpCode");
            return (Criteria) this;
        }

        public Criteria andStoreBdpCodeNotBetween(String value1, String value2) {
            addCriterion("store_bdp_code not between", value1, value2, "storeBdpCode");
            return (Criteria) this;
        }

        public Criteria andGoodsNoIsNull() {
            addCriterion("goods_no is null");
            return (Criteria) this;
        }

        public Criteria andGoodsNoIsNotNull() {
            addCriterion("goods_no is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsNoEqualTo(String value) {
            addCriterion("goods_no =", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotEqualTo(String value) {
            addCriterion("goods_no <>", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoGreaterThan(String value) {
            addCriterion("goods_no >", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoGreaterThanOrEqualTo(String value) {
            addCriterion("goods_no >=", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoLessThan(String value) {
            addCriterion("goods_no <", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoLessThanOrEqualTo(String value) {
            addCriterion("goods_no <=", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoLike(String value) {
            addCriterion("goods_no like", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotLike(String value) {
            addCriterion("goods_no not like", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoIn(List<String> values) {
            addCriterion("goods_no in", values, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotIn(List<String> values) {
            addCriterion("goods_no not in", values, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoBetween(String value1, String value2) {
            addCriterion("goods_no between", value1, value2, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotBetween(String value1, String value2) {
            addCriterion("goods_no not between", value1, value2, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsHdNoIsNull() {
            addCriterion("goods_hd_no is null");
            return (Criteria) this;
        }

        public Criteria andGoodsHdNoIsNotNull() {
            addCriterion("goods_hd_no is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsHdNoEqualTo(String value) {
            addCriterion("goods_hd_no =", value, "goodsHdNo");
            return (Criteria) this;
        }

        public Criteria andGoodsHdNoNotEqualTo(String value) {
            addCriterion("goods_hd_no <>", value, "goodsHdNo");
            return (Criteria) this;
        }

        public Criteria andGoodsHdNoGreaterThan(String value) {
            addCriterion("goods_hd_no >", value, "goodsHdNo");
            return (Criteria) this;
        }

        public Criteria andGoodsHdNoGreaterThanOrEqualTo(String value) {
            addCriterion("goods_hd_no >=", value, "goodsHdNo");
            return (Criteria) this;
        }

        public Criteria andGoodsHdNoLessThan(String value) {
            addCriterion("goods_hd_no <", value, "goodsHdNo");
            return (Criteria) this;
        }

        public Criteria andGoodsHdNoLessThanOrEqualTo(String value) {
            addCriterion("goods_hd_no <=", value, "goodsHdNo");
            return (Criteria) this;
        }

        public Criteria andGoodsHdNoLike(String value) {
            addCriterion("goods_hd_no like", value, "goodsHdNo");
            return (Criteria) this;
        }

        public Criteria andGoodsHdNoNotLike(String value) {
            addCriterion("goods_hd_no not like", value, "goodsHdNo");
            return (Criteria) this;
        }

        public Criteria andGoodsHdNoIn(List<String> values) {
            addCriterion("goods_hd_no in", values, "goodsHdNo");
            return (Criteria) this;
        }

        public Criteria andGoodsHdNoNotIn(List<String> values) {
            addCriterion("goods_hd_no not in", values, "goodsHdNo");
            return (Criteria) this;
        }

        public Criteria andGoodsHdNoBetween(String value1, String value2) {
            addCriterion("goods_hd_no between", value1, value2, "goodsHdNo");
            return (Criteria) this;
        }

        public Criteria andGoodsHdNoNotBetween(String value1, String value2) {
            addCriterion("goods_hd_no not between", value1, value2, "goodsHdNo");
            return (Criteria) this;
        }

        public Criteria andDistrNoIsNull() {
            addCriterion("distr_no is null");
            return (Criteria) this;
        }

        public Criteria andDistrNoIsNotNull() {
            addCriterion("distr_no is not null");
            return (Criteria) this;
        }

        public Criteria andDistrNoEqualTo(String value) {
            addCriterion("distr_no =", value, "distrNo");
            return (Criteria) this;
        }

        public Criteria andDistrNoNotEqualTo(String value) {
            addCriterion("distr_no <>", value, "distrNo");
            return (Criteria) this;
        }

        public Criteria andDistrNoGreaterThan(String value) {
            addCriterion("distr_no >", value, "distrNo");
            return (Criteria) this;
        }

        public Criteria andDistrNoGreaterThanOrEqualTo(String value) {
            addCriterion("distr_no >=", value, "distrNo");
            return (Criteria) this;
        }

        public Criteria andDistrNoLessThan(String value) {
            addCriterion("distr_no <", value, "distrNo");
            return (Criteria) this;
        }

        public Criteria andDistrNoLessThanOrEqualTo(String value) {
            addCriterion("distr_no <=", value, "distrNo");
            return (Criteria) this;
        }

        public Criteria andDistrNoLike(String value) {
            addCriterion("distr_no like", value, "distrNo");
            return (Criteria) this;
        }

        public Criteria andDistrNoNotLike(String value) {
            addCriterion("distr_no not like", value, "distrNo");
            return (Criteria) this;
        }

        public Criteria andDistrNoIn(List<String> values) {
            addCriterion("distr_no in", values, "distrNo");
            return (Criteria) this;
        }

        public Criteria andDistrNoNotIn(List<String> values) {
            addCriterion("distr_no not in", values, "distrNo");
            return (Criteria) this;
        }

        public Criteria andDistrNoBetween(String value1, String value2) {
            addCriterion("distr_no between", value1, value2, "distrNo");
            return (Criteria) this;
        }

        public Criteria andDistrNoNotBetween(String value1, String value2) {
            addCriterion("distr_no not between", value1, value2, "distrNo");
            return (Criteria) this;
        }

        public Criteria andDistrLineNoIsNull() {
            addCriterion("distr_line_no is null");
            return (Criteria) this;
        }

        public Criteria andDistrLineNoIsNotNull() {
            addCriterion("distr_line_no is not null");
            return (Criteria) this;
        }

        public Criteria andDistrLineNoEqualTo(String value) {
            addCriterion("distr_line_no =", value, "distrLineNo");
            return (Criteria) this;
        }

        public Criteria andDistrLineNoNotEqualTo(String value) {
            addCriterion("distr_line_no <>", value, "distrLineNo");
            return (Criteria) this;
        }

        public Criteria andDistrLineNoGreaterThan(String value) {
            addCriterion("distr_line_no >", value, "distrLineNo");
            return (Criteria) this;
        }

        public Criteria andDistrLineNoGreaterThanOrEqualTo(String value) {
            addCriterion("distr_line_no >=", value, "distrLineNo");
            return (Criteria) this;
        }

        public Criteria andDistrLineNoLessThan(String value) {
            addCriterion("distr_line_no <", value, "distrLineNo");
            return (Criteria) this;
        }

        public Criteria andDistrLineNoLessThanOrEqualTo(String value) {
            addCriterion("distr_line_no <=", value, "distrLineNo");
            return (Criteria) this;
        }

        public Criteria andDistrLineNoLike(String value) {
            addCriterion("distr_line_no like", value, "distrLineNo");
            return (Criteria) this;
        }

        public Criteria andDistrLineNoNotLike(String value) {
            addCriterion("distr_line_no not like", value, "distrLineNo");
            return (Criteria) this;
        }

        public Criteria andDistrLineNoIn(List<String> values) {
            addCriterion("distr_line_no in", values, "distrLineNo");
            return (Criteria) this;
        }

        public Criteria andDistrLineNoNotIn(List<String> values) {
            addCriterion("distr_line_no not in", values, "distrLineNo");
            return (Criteria) this;
        }

        public Criteria andDistrLineNoBetween(String value1, String value2) {
            addCriterion("distr_line_no between", value1, value2, "distrLineNo");
            return (Criteria) this;
        }

        public Criteria andDistrLineNoNotBetween(String value1, String value2) {
            addCriterion("distr_line_no not between", value1, value2, "distrLineNo");
            return (Criteria) this;
        }

        public Criteria andDistrDateIsNull() {
            addCriterion("distr_date is null");
            return (Criteria) this;
        }

        public Criteria andDistrDateIsNotNull() {
            addCriterion("distr_date is not null");
            return (Criteria) this;
        }

        public Criteria andDistrDateEqualTo(Date value) {
            addCriterion("distr_date =", value, "distrDate");
            return (Criteria) this;
        }

        public Criteria andDistrDateNotEqualTo(Date value) {
            addCriterion("distr_date <>", value, "distrDate");
            return (Criteria) this;
        }

        public Criteria andDistrDateGreaterThan(Date value) {
            addCriterion("distr_date >", value, "distrDate");
            return (Criteria) this;
        }

        public Criteria andDistrDateGreaterThanOrEqualTo(Date value) {
            addCriterion("distr_date >=", value, "distrDate");
            return (Criteria) this;
        }

        public Criteria andDistrDateLessThan(Date value) {
            addCriterion("distr_date <", value, "distrDate");
            return (Criteria) this;
        }

        public Criteria andDistrDateLessThanOrEqualTo(Date value) {
            addCriterion("distr_date <=", value, "distrDate");
            return (Criteria) this;
        }

        public Criteria andDistrDateIn(List<Date> values) {
            addCriterion("distr_date in", values, "distrDate");
            return (Criteria) this;
        }

        public Criteria andDistrDateNotIn(List<Date> values) {
            addCriterion("distr_date not in", values, "distrDate");
            return (Criteria) this;
        }

        public Criteria andDistrDateBetween(Date value1, Date value2) {
            addCriterion("distr_date between", value1, value2, "distrDate");
            return (Criteria) this;
        }

        public Criteria andDistrDateNotBetween(Date value1, Date value2) {
            addCriterion("distr_date not between", value1, value2, "distrDate");
            return (Criteria) this;
        }

        public Criteria andBatchNoIsNull() {
            addCriterion("batch_no is null");
            return (Criteria) this;
        }

        public Criteria andBatchNoIsNotNull() {
            addCriterion("batch_no is not null");
            return (Criteria) this;
        }

        public Criteria andBatchNoEqualTo(String value) {
            addCriterion("batch_no =", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoNotEqualTo(String value) {
            addCriterion("batch_no <>", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoGreaterThan(String value) {
            addCriterion("batch_no >", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoGreaterThanOrEqualTo(String value) {
            addCriterion("batch_no >=", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoLessThan(String value) {
            addCriterion("batch_no <", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoLessThanOrEqualTo(String value) {
            addCriterion("batch_no <=", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoLike(String value) {
            addCriterion("batch_no like", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoNotLike(String value) {
            addCriterion("batch_no not like", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoIn(List<String> values) {
            addCriterion("batch_no in", values, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoNotIn(List<String> values) {
            addCriterion("batch_no not in", values, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoBetween(String value1, String value2) {
            addCriterion("batch_no between", value1, value2, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoNotBetween(String value1, String value2) {
            addCriterion("batch_no not between", value1, value2, "batchNo");
            return (Criteria) this;
        }

        public Criteria andValidityDateIsNull() {
            addCriterion("validity_date is null");
            return (Criteria) this;
        }

        public Criteria andValidityDateIsNotNull() {
            addCriterion("validity_date is not null");
            return (Criteria) this;
        }

        public Criteria andValidityDateEqualTo(Date value) {
            addCriterion("validity_date =", value, "validityDate");
            return (Criteria) this;
        }

        public Criteria andValidityDateNotEqualTo(Date value) {
            addCriterion("validity_date <>", value, "validityDate");
            return (Criteria) this;
        }

        public Criteria andValidityDateGreaterThan(Date value) {
            addCriterion("validity_date >", value, "validityDate");
            return (Criteria) this;
        }

        public Criteria andValidityDateGreaterThanOrEqualTo(Date value) {
            addCriterion("validity_date >=", value, "validityDate");
            return (Criteria) this;
        }

        public Criteria andValidityDateLessThan(Date value) {
            addCriterion("validity_date <", value, "validityDate");
            return (Criteria) this;
        }

        public Criteria andValidityDateLessThanOrEqualTo(Date value) {
            addCriterion("validity_date <=", value, "validityDate");
            return (Criteria) this;
        }

        public Criteria andValidityDateIn(List<Date> values) {
            addCriterion("validity_date in", values, "validityDate");
            return (Criteria) this;
        }

        public Criteria andValidityDateNotIn(List<Date> values) {
            addCriterion("validity_date not in", values, "validityDate");
            return (Criteria) this;
        }

        public Criteria andValidityDateBetween(Date value1, Date value2) {
            addCriterion("validity_date between", value1, value2, "validityDate");
            return (Criteria) this;
        }

        public Criteria andValidityDateNotBetween(Date value1, Date value2) {
            addCriterion("validity_date not between", value1, value2, "validityDate");
            return (Criteria) this;
        }

        public Criteria andSapBatchCodeIsNull() {
            addCriterion("sap_batch_code is null");
            return (Criteria) this;
        }

        public Criteria andSapBatchCodeIsNotNull() {
            addCriterion("sap_batch_code is not null");
            return (Criteria) this;
        }

        public Criteria andSapBatchCodeEqualTo(String value) {
            addCriterion("sap_batch_code =", value, "sapBatchCode");
            return (Criteria) this;
        }

        public Criteria andSapBatchCodeNotEqualTo(String value) {
            addCriterion("sap_batch_code <>", value, "sapBatchCode");
            return (Criteria) this;
        }

        public Criteria andSapBatchCodeGreaterThan(String value) {
            addCriterion("sap_batch_code >", value, "sapBatchCode");
            return (Criteria) this;
        }

        public Criteria andSapBatchCodeGreaterThanOrEqualTo(String value) {
            addCriterion("sap_batch_code >=", value, "sapBatchCode");
            return (Criteria) this;
        }

        public Criteria andSapBatchCodeLessThan(String value) {
            addCriterion("sap_batch_code <", value, "sapBatchCode");
            return (Criteria) this;
        }

        public Criteria andSapBatchCodeLessThanOrEqualTo(String value) {
            addCriterion("sap_batch_code <=", value, "sapBatchCode");
            return (Criteria) this;
        }

        public Criteria andSapBatchCodeLike(String value) {
            addCriterion("sap_batch_code like", value, "sapBatchCode");
            return (Criteria) this;
        }

        public Criteria andSapBatchCodeNotLike(String value) {
            addCriterion("sap_batch_code not like", value, "sapBatchCode");
            return (Criteria) this;
        }

        public Criteria andSapBatchCodeIn(List<String> values) {
            addCriterion("sap_batch_code in", values, "sapBatchCode");
            return (Criteria) this;
        }

        public Criteria andSapBatchCodeNotIn(List<String> values) {
            addCriterion("sap_batch_code not in", values, "sapBatchCode");
            return (Criteria) this;
        }

        public Criteria andSapBatchCodeBetween(String value1, String value2) {
            addCriterion("sap_batch_code between", value1, value2, "sapBatchCode");
            return (Criteria) this;
        }

        public Criteria andSapBatchCodeNotBetween(String value1, String value2) {
            addCriterion("sap_batch_code not between", value1, value2, "sapBatchCode");
            return (Criteria) this;
        }

        public Criteria andHdBatchCodeIsNull() {
            addCriterion("hd_batch_code is null");
            return (Criteria) this;
        }

        public Criteria andHdBatchCodeIsNotNull() {
            addCriterion("hd_batch_code is not null");
            return (Criteria) this;
        }

        public Criteria andHdBatchCodeEqualTo(String value) {
            addCriterion("hd_batch_code =", value, "hdBatchCode");
            return (Criteria) this;
        }

        public Criteria andHdBatchCodeNotEqualTo(String value) {
            addCriterion("hd_batch_code <>", value, "hdBatchCode");
            return (Criteria) this;
        }

        public Criteria andHdBatchCodeGreaterThan(String value) {
            addCriterion("hd_batch_code >", value, "hdBatchCode");
            return (Criteria) this;
        }

        public Criteria andHdBatchCodeGreaterThanOrEqualTo(String value) {
            addCriterion("hd_batch_code >=", value, "hdBatchCode");
            return (Criteria) this;
        }

        public Criteria andHdBatchCodeLessThan(String value) {
            addCriterion("hd_batch_code <", value, "hdBatchCode");
            return (Criteria) this;
        }

        public Criteria andHdBatchCodeLessThanOrEqualTo(String value) {
            addCriterion("hd_batch_code <=", value, "hdBatchCode");
            return (Criteria) this;
        }

        public Criteria andHdBatchCodeLike(String value) {
            addCriterion("hd_batch_code like", value, "hdBatchCode");
            return (Criteria) this;
        }

        public Criteria andHdBatchCodeNotLike(String value) {
            addCriterion("hd_batch_code not like", value, "hdBatchCode");
            return (Criteria) this;
        }

        public Criteria andHdBatchCodeIn(List<String> values) {
            addCriterion("hd_batch_code in", values, "hdBatchCode");
            return (Criteria) this;
        }

        public Criteria andHdBatchCodeNotIn(List<String> values) {
            addCriterion("hd_batch_code not in", values, "hdBatchCode");
            return (Criteria) this;
        }

        public Criteria andHdBatchCodeBetween(String value1, String value2) {
            addCriterion("hd_batch_code between", value1, value2, "hdBatchCode");
            return (Criteria) this;
        }

        public Criteria andHdBatchCodeNotBetween(String value1, String value2) {
            addCriterion("hd_batch_code not between", value1, value2, "hdBatchCode");
            return (Criteria) this;
        }

        public Criteria andDistrQuantityIsNull() {
            addCriterion("distr_quantity is null");
            return (Criteria) this;
        }

        public Criteria andDistrQuantityIsNotNull() {
            addCriterion("distr_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andDistrQuantityEqualTo(BigDecimal value) {
            addCriterion("distr_quantity =", value, "distrQuantity");
            return (Criteria) this;
        }

        public Criteria andDistrQuantityNotEqualTo(BigDecimal value) {
            addCriterion("distr_quantity <>", value, "distrQuantity");
            return (Criteria) this;
        }

        public Criteria andDistrQuantityGreaterThan(BigDecimal value) {
            addCriterion("distr_quantity >", value, "distrQuantity");
            return (Criteria) this;
        }

        public Criteria andDistrQuantityGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("distr_quantity >=", value, "distrQuantity");
            return (Criteria) this;
        }

        public Criteria andDistrQuantityLessThan(BigDecimal value) {
            addCriterion("distr_quantity <", value, "distrQuantity");
            return (Criteria) this;
        }

        public Criteria andDistrQuantityLessThanOrEqualTo(BigDecimal value) {
            addCriterion("distr_quantity <=", value, "distrQuantity");
            return (Criteria) this;
        }

        public Criteria andDistrQuantityIn(List<BigDecimal> values) {
            addCriterion("distr_quantity in", values, "distrQuantity");
            return (Criteria) this;
        }

        public Criteria andDistrQuantityNotIn(List<BigDecimal> values) {
            addCriterion("distr_quantity not in", values, "distrQuantity");
            return (Criteria) this;
        }

        public Criteria andDistrQuantityBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("distr_quantity between", value1, value2, "distrQuantity");
            return (Criteria) this;
        }

        public Criteria andDistrQuantityNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("distr_quantity not between", value1, value2, "distrQuantity");
            return (Criteria) this;
        }

        public Criteria andShouldReturnQuantityIsNull() {
            addCriterion("should_return_quantity is null");
            return (Criteria) this;
        }

        public Criteria andShouldReturnQuantityIsNotNull() {
            addCriterion("should_return_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andShouldReturnQuantityEqualTo(BigDecimal value) {
            addCriterion("should_return_quantity =", value, "shouldReturnQuantity");
            return (Criteria) this;
        }

        public Criteria andShouldReturnQuantityNotEqualTo(BigDecimal value) {
            addCriterion("should_return_quantity <>", value, "shouldReturnQuantity");
            return (Criteria) this;
        }

        public Criteria andShouldReturnQuantityGreaterThan(BigDecimal value) {
            addCriterion("should_return_quantity >", value, "shouldReturnQuantity");
            return (Criteria) this;
        }

        public Criteria andShouldReturnQuantityGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("should_return_quantity >=", value, "shouldReturnQuantity");
            return (Criteria) this;
        }

        public Criteria andShouldReturnQuantityLessThan(BigDecimal value) {
            addCriterion("should_return_quantity <", value, "shouldReturnQuantity");
            return (Criteria) this;
        }

        public Criteria andShouldReturnQuantityLessThanOrEqualTo(BigDecimal value) {
            addCriterion("should_return_quantity <=", value, "shouldReturnQuantity");
            return (Criteria) this;
        }

        public Criteria andShouldReturnQuantityIn(List<BigDecimal> values) {
            addCriterion("should_return_quantity in", values, "shouldReturnQuantity");
            return (Criteria) this;
        }

        public Criteria andShouldReturnQuantityNotIn(List<BigDecimal> values) {
            addCriterion("should_return_quantity not in", values, "shouldReturnQuantity");
            return (Criteria) this;
        }

        public Criteria andShouldReturnQuantityBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("should_return_quantity between", value1, value2, "shouldReturnQuantity");
            return (Criteria) this;
        }

        public Criteria andShouldReturnQuantityNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("should_return_quantity not between", value1, value2, "shouldReturnQuantity");
            return (Criteria) this;
        }

        public Criteria andReturnQuantityIsNull() {
            addCriterion("return_quantity is null");
            return (Criteria) this;
        }

        public Criteria andReturnQuantityIsNotNull() {
            addCriterion("return_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andReturnQuantityEqualTo(BigDecimal value) {
            addCriterion("return_quantity =", value, "returnQuantity");
            return (Criteria) this;
        }

        public Criteria andReturnQuantityNotEqualTo(BigDecimal value) {
            addCriterion("return_quantity <>", value, "returnQuantity");
            return (Criteria) this;
        }

        public Criteria andReturnQuantityGreaterThan(BigDecimal value) {
            addCriterion("return_quantity >", value, "returnQuantity");
            return (Criteria) this;
        }

        public Criteria andReturnQuantityGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("return_quantity >=", value, "returnQuantity");
            return (Criteria) this;
        }

        public Criteria andReturnQuantityLessThan(BigDecimal value) {
            addCriterion("return_quantity <", value, "returnQuantity");
            return (Criteria) this;
        }

        public Criteria andReturnQuantityLessThanOrEqualTo(BigDecimal value) {
            addCriterion("return_quantity <=", value, "returnQuantity");
            return (Criteria) this;
        }

        public Criteria andReturnQuantityIn(List<BigDecimal> values) {
            addCriterion("return_quantity in", values, "returnQuantity");
            return (Criteria) this;
        }

        public Criteria andReturnQuantityNotIn(List<BigDecimal> values) {
            addCriterion("return_quantity not in", values, "returnQuantity");
            return (Criteria) this;
        }

        public Criteria andReturnQuantityBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("return_quantity between", value1, value2, "returnQuantity");
            return (Criteria) this;
        }

        public Criteria andReturnQuantityNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("return_quantity not between", value1, value2, "returnQuantity");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeIsNull() {
            addCriterion("warehouse_code is null");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeIsNotNull() {
            addCriterion("warehouse_code is not null");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeEqualTo(String value) {
            addCriterion("warehouse_code =", value, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeNotEqualTo(String value) {
            addCriterion("warehouse_code <>", value, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeGreaterThan(String value) {
            addCriterion("warehouse_code >", value, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeGreaterThanOrEqualTo(String value) {
            addCriterion("warehouse_code >=", value, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeLessThan(String value) {
            addCriterion("warehouse_code <", value, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeLessThanOrEqualTo(String value) {
            addCriterion("warehouse_code <=", value, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeLike(String value) {
            addCriterion("warehouse_code like", value, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeNotLike(String value) {
            addCriterion("warehouse_code not like", value, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeIn(List<String> values) {
            addCriterion("warehouse_code in", values, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeNotIn(List<String> values) {
            addCriterion("warehouse_code not in", values, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeBetween(String value1, String value2) {
            addCriterion("warehouse_code between", value1, value2, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeNotBetween(String value1, String value2) {
            addCriterion("warehouse_code not between", value1, value2, "warehouseCode");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}