package com.cowell.iscm.entityTidb;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * iscm_sotre_purchase_task_detail
 * <AUTHOR>
public class IscmStorePurchaseTaskDetail implements Serializable {
    private Long id;

    /**
     * 公司Id
     */
    private Long companyId;

    /**
     * 公司MDM编码
     */
    private String companyCode;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 门店 id
     */
    private Long storeId;

    /**
     * 门店MDM编码
     */
    private String storeCode;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 申请单号
     */
    private String purchaseNo;

    /**
     * 业务唯一单号
     */
    private String orderNo;

    /**
     * 商品编码
     */
    private String goodsNo;

    /**
     * 商品名
     */
    private String goodsName;

    /**
     * 商品的特殊销售属性
     */
    private String goodsline;

    /**
     * 商品的推类等级(销售属性)
     */
    private String pushlevel;

    /**
     * 必备、黄金单品(暂时不用)
     */
    private String goodsMarks;

    /**
     * 生产厂家(简称,无简称取全称)
     */
    private String manufacturer;

    /**
     * 规格
     */
    private String specifications;

    /**
     * 请货时库存
     */
    private BigDecimal stockQuantity;

    /**
     * 库存上限
     */
    private BigDecimal stockUpperLimit;

    /**
     * 库存下限
     */
    private BigDecimal stockLowerLimit;

    /**
     * 近30天销量
     */
    private BigDecimal saleQuantity30;

    /**
     * 近30天销售次数
     */
    private BigDecimal saleTimes30;

    /**
     * 系统自动请货量
     */
    private BigDecimal autoPurchaseQuantity;

    /**
     * 海典POS综合日均销量
     */
    private BigDecimal posAverageDailySales;

    /**
     * BDP日均销量
     */
    private BigDecimal bdpAverageDailySales;

    /**
     * 状态(-1删除，0正常)
     */
    private Byte status;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新时间
     */
    private Date gmtUpdate;

    /**
     * 扩展字段
     */
    private String extend;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 创建人ID
     */
    private Long createdBy;

    /**
     * 创建人
     */
    private String createdName;

    /**
     * 更新人ID
     */
    private Long updatedBy;

    /**
     * 更新人
     */
    private String updatedName;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public Long getStoreId() {
        return storeId;
    }

    public void setStoreId(Long storeId) {
        this.storeId = storeId;
    }

    public String getStoreCode() {
        return storeCode;
    }

    public void setStoreCode(String storeCode) {
        this.storeCode = storeCode;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getPurchaseNo() {
        return purchaseNo;
    }

    public void setPurchaseNo(String purchaseNo) {
        this.purchaseNo = purchaseNo;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getGoodsNo() {
        return goodsNo;
    }

    public void setGoodsNo(String goodsNo) {
        this.goodsNo = goodsNo;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getGoodsline() {
        return goodsline;
    }

    public void setGoodsline(String goodsline) {
        this.goodsline = goodsline;
    }

    public String getPushlevel() {
        return pushlevel;
    }

    public void setPushlevel(String pushlevel) {
        this.pushlevel = pushlevel;
    }

    public String getGoodsMarks() {
        return goodsMarks;
    }

    public void setGoodsMarks(String goodsMarks) {
        this.goodsMarks = goodsMarks;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    public String getSpecifications() {
        return specifications;
    }

    public void setSpecifications(String specifications) {
        this.specifications = specifications;
    }

    public BigDecimal getStockQuantity() {
        return stockQuantity;
    }

    public void setStockQuantity(BigDecimal stockQuantity) {
        this.stockQuantity = stockQuantity;
    }

    public BigDecimal getStockUpperLimit() {
        return stockUpperLimit;
    }

    public void setStockUpperLimit(BigDecimal stockUpperLimit) {
        this.stockUpperLimit = stockUpperLimit;
    }

    public BigDecimal getStockLowerLimit() {
        return stockLowerLimit;
    }

    public void setStockLowerLimit(BigDecimal stockLowerLimit) {
        this.stockLowerLimit = stockLowerLimit;
    }

    public BigDecimal getSaleQuantity30() {
        return saleQuantity30;
    }

    public void setSaleQuantity30(BigDecimal saleQuantity30) {
        this.saleQuantity30 = saleQuantity30;
    }

    public BigDecimal getSaleTimes30() {
        return saleTimes30;
    }

    public void setSaleTimes30(BigDecimal saleTimes30) {
        this.saleTimes30 = saleTimes30;
    }

    public BigDecimal getAutoPurchaseQuantity() {
        return autoPurchaseQuantity;
    }

    public void setAutoPurchaseQuantity(BigDecimal autoPurchaseQuantity) {
        this.autoPurchaseQuantity = autoPurchaseQuantity;
    }

    public BigDecimal getPosAverageDailySales() {
        return posAverageDailySales;
    }

    public void setPosAverageDailySales(BigDecimal posAverageDailySales) {
        this.posAverageDailySales = posAverageDailySales;
    }

    public BigDecimal getBdpAverageDailySales() {
        return bdpAverageDailySales;
    }

    public void setBdpAverageDailySales(BigDecimal bdpAverageDailySales) {
        this.bdpAverageDailySales = bdpAverageDailySales;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtUpdate() {
        return gmtUpdate;
    }

    public void setGmtUpdate(Date gmtUpdate) {
        this.gmtUpdate = gmtUpdate;
    }

    public String getExtend() {
        return extend;
    }

    public void setExtend(String extend) {
        this.extend = extend;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Long getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    public String getCreatedName() {
        return createdName;
    }

    public void setCreatedName(String createdName) {
        this.createdName = createdName;
    }

    public Long getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(Long updatedBy) {
        this.updatedBy = updatedBy;
    }

    public String getUpdatedName() {
        return updatedName;
    }

    public void setUpdatedName(String updatedName) {
        this.updatedName = updatedName;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        IscmStorePurchaseTaskDetail other = (IscmStorePurchaseTaskDetail) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getCompanyId() == null ? other.getCompanyId() == null : this.getCompanyId().equals(other.getCompanyId()))
            && (this.getCompanyCode() == null ? other.getCompanyCode() == null : this.getCompanyCode().equals(other.getCompanyCode()))
            && (this.getCompanyName() == null ? other.getCompanyName() == null : this.getCompanyName().equals(other.getCompanyName()))
            && (this.getStoreId() == null ? other.getStoreId() == null : this.getStoreId().equals(other.getStoreId()))
            && (this.getStoreCode() == null ? other.getStoreCode() == null : this.getStoreCode().equals(other.getStoreCode()))
            && (this.getStoreName() == null ? other.getStoreName() == null : this.getStoreName().equals(other.getStoreName()))
            && (this.getPurchaseNo() == null ? other.getPurchaseNo() == null : this.getPurchaseNo().equals(other.getPurchaseNo()))
            && (this.getOrderNo() == null ? other.getOrderNo() == null : this.getOrderNo().equals(other.getOrderNo()))
            && (this.getGoodsNo() == null ? other.getGoodsNo() == null : this.getGoodsNo().equals(other.getGoodsNo()))
            && (this.getGoodsName() == null ? other.getGoodsName() == null : this.getGoodsName().equals(other.getGoodsName()))
            && (this.getGoodsline() == null ? other.getGoodsline() == null : this.getGoodsline().equals(other.getGoodsline()))
            && (this.getPushlevel() == null ? other.getPushlevel() == null : this.getPushlevel().equals(other.getPushlevel()))
            && (this.getGoodsMarks() == null ? other.getGoodsMarks() == null : this.getGoodsMarks().equals(other.getGoodsMarks()))
            && (this.getManufacturer() == null ? other.getManufacturer() == null : this.getManufacturer().equals(other.getManufacturer()))
            && (this.getSpecifications() == null ? other.getSpecifications() == null : this.getSpecifications().equals(other.getSpecifications()))
            && (this.getStockQuantity() == null ? other.getStockQuantity() == null : this.getStockQuantity().equals(other.getStockQuantity()))
            && (this.getStockUpperLimit() == null ? other.getStockUpperLimit() == null : this.getStockUpperLimit().equals(other.getStockUpperLimit()))
            && (this.getStockLowerLimit() == null ? other.getStockLowerLimit() == null : this.getStockLowerLimit().equals(other.getStockLowerLimit()))
            && (this.getSaleQuantity30() == null ? other.getSaleQuantity30() == null : this.getSaleQuantity30().equals(other.getSaleQuantity30()))
            && (this.getSaleTimes30() == null ? other.getSaleTimes30() == null : this.getSaleTimes30().equals(other.getSaleTimes30()))
            && (this.getAutoPurchaseQuantity() == null ? other.getAutoPurchaseQuantity() == null : this.getAutoPurchaseQuantity().equals(other.getAutoPurchaseQuantity()))
            && (this.getPosAverageDailySales() == null ? other.getPosAverageDailySales() == null : this.getPosAverageDailySales().equals(other.getPosAverageDailySales()))
            && (this.getBdpAverageDailySales() == null ? other.getBdpAverageDailySales() == null : this.getBdpAverageDailySales().equals(other.getBdpAverageDailySales()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getGmtCreate() == null ? other.getGmtCreate() == null : this.getGmtCreate().equals(other.getGmtCreate()))
            && (this.getGmtUpdate() == null ? other.getGmtUpdate() == null : this.getGmtUpdate().equals(other.getGmtUpdate()))
            && (this.getExtend() == null ? other.getExtend() == null : this.getExtend().equals(other.getExtend()))
            && (this.getVersion() == null ? other.getVersion() == null : this.getVersion().equals(other.getVersion()))
            && (this.getCreatedBy() == null ? other.getCreatedBy() == null : this.getCreatedBy().equals(other.getCreatedBy()))
            && (this.getCreatedName() == null ? other.getCreatedName() == null : this.getCreatedName().equals(other.getCreatedName()))
            && (this.getUpdatedBy() == null ? other.getUpdatedBy() == null : this.getUpdatedBy().equals(other.getUpdatedBy()))
            && (this.getUpdatedName() == null ? other.getUpdatedName() == null : this.getUpdatedName().equals(other.getUpdatedName()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getCompanyId() == null) ? 0 : getCompanyId().hashCode());
        result = prime * result + ((getCompanyCode() == null) ? 0 : getCompanyCode().hashCode());
        result = prime * result + ((getCompanyName() == null) ? 0 : getCompanyName().hashCode());
        result = prime * result + ((getStoreId() == null) ? 0 : getStoreId().hashCode());
        result = prime * result + ((getStoreCode() == null) ? 0 : getStoreCode().hashCode());
        result = prime * result + ((getStoreName() == null) ? 0 : getStoreName().hashCode());
        result = prime * result + ((getPurchaseNo() == null) ? 0 : getPurchaseNo().hashCode());
        result = prime * result + ((getOrderNo() == null) ? 0 : getOrderNo().hashCode());
        result = prime * result + ((getGoodsNo() == null) ? 0 : getGoodsNo().hashCode());
        result = prime * result + ((getGoodsName() == null) ? 0 : getGoodsName().hashCode());
        result = prime * result + ((getGoodsline() == null) ? 0 : getGoodsline().hashCode());
        result = prime * result + ((getPushlevel() == null) ? 0 : getPushlevel().hashCode());
        result = prime * result + ((getGoodsMarks() == null) ? 0 : getGoodsMarks().hashCode());
        result = prime * result + ((getManufacturer() == null) ? 0 : getManufacturer().hashCode());
        result = prime * result + ((getSpecifications() == null) ? 0 : getSpecifications().hashCode());
        result = prime * result + ((getStockQuantity() == null) ? 0 : getStockQuantity().hashCode());
        result = prime * result + ((getStockUpperLimit() == null) ? 0 : getStockUpperLimit().hashCode());
        result = prime * result + ((getStockLowerLimit() == null) ? 0 : getStockLowerLimit().hashCode());
        result = prime * result + ((getSaleQuantity30() == null) ? 0 : getSaleQuantity30().hashCode());
        result = prime * result + ((getSaleTimes30() == null) ? 0 : getSaleTimes30().hashCode());
        result = prime * result + ((getAutoPurchaseQuantity() == null) ? 0 : getAutoPurchaseQuantity().hashCode());
        result = prime * result + ((getPosAverageDailySales() == null) ? 0 : getPosAverageDailySales().hashCode());
        result = prime * result + ((getBdpAverageDailySales() == null) ? 0 : getBdpAverageDailySales().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getGmtCreate() == null) ? 0 : getGmtCreate().hashCode());
        result = prime * result + ((getGmtUpdate() == null) ? 0 : getGmtUpdate().hashCode());
        result = prime * result + ((getExtend() == null) ? 0 : getExtend().hashCode());
        result = prime * result + ((getVersion() == null) ? 0 : getVersion().hashCode());
        result = prime * result + ((getCreatedBy() == null) ? 0 : getCreatedBy().hashCode());
        result = prime * result + ((getCreatedName() == null) ? 0 : getCreatedName().hashCode());
        result = prime * result + ((getUpdatedBy() == null) ? 0 : getUpdatedBy().hashCode());
        result = prime * result + ((getUpdatedName() == null) ? 0 : getUpdatedName().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", companyId=").append(companyId);
        sb.append(", companyCode=").append(companyCode);
        sb.append(", companyName=").append(companyName);
        sb.append(", storeId=").append(storeId);
        sb.append(", storeCode=").append(storeCode);
        sb.append(", storeName=").append(storeName);
        sb.append(", purchaseNo=").append(purchaseNo);
        sb.append(", orderNo=").append(orderNo);
        sb.append(", goodsNo=").append(goodsNo);
        sb.append(", goodsName=").append(goodsName);
        sb.append(", goodsline=").append(goodsline);
        sb.append(", pushlevel=").append(pushlevel);
        sb.append(", goodsMarks=").append(goodsMarks);
        sb.append(", manufacturer=").append(manufacturer);
        sb.append(", specifications=").append(specifications);
        sb.append(", stockQuantity=").append(stockQuantity);
        sb.append(", stockUpperLimit=").append(stockUpperLimit);
        sb.append(", stockLowerLimit=").append(stockLowerLimit);
        sb.append(", saleQuantity30=").append(saleQuantity30);
        sb.append(", saleTimes30=").append(saleTimes30);
        sb.append(", autoPurchaseQuantity=").append(autoPurchaseQuantity);
        sb.append(", posAverageDailySales=").append(posAverageDailySales);
        sb.append(", bdpAverageDailySales=").append(bdpAverageDailySales);
        sb.append(", status=").append(status);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", gmtUpdate=").append(gmtUpdate);
        sb.append(", extend=").append(extend);
        sb.append(", version=").append(version);
        sb.append(", createdBy=").append(createdBy);
        sb.append(", createdName=").append(createdName);
        sb.append(", updatedBy=").append(updatedBy);
        sb.append(", updatedName=").append(updatedName);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}