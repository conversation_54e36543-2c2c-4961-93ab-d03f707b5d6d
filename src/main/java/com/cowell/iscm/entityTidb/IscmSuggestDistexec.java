package com.cowell.iscm.entityTidb;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * iscm_suggest_distexec
 * <AUTHOR>
public class IscmSuggestDistexec implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 登记日期
     */
    private Date registerDate;

    /**
     * 调拨类型,1:近效期调拨,2:大库存、滞销商品调拨,3:断货商品调拨
     */
    private Byte allotType;

    /**
     * 登记单号
     */
    private String registerNo;

    /**
     * pos调拨单号
     */
    private String posAllotNo;

    /**
     * pos调拨明细行号
     */
    private Long posAllotDetailId;

    /**
     * 调出公司MDM编码
     */
    private String outCompanyCode;

    /**
     * 调入公司MDM编码
     */
    private String inCompanyCode;

    /**
     * 调出门店MDM编码
     */
    private String outStoreCode;

    /**
     * 调入门店MDM编码
     */
    private String inStoreCode;

    /**
     * 调出门店审批状态 0:未审批 1:审批通过 2:作废
     */
    private Byte outApproveStatus;

    /**
     * 调出门店审批时间
     */
    private Date outApproveTime;

    /**
     * 调出数量
     */
    private BigDecimal outAllotQuantity;

    /**
     * 调入门店审批状态 0:未审批 1:审批通过 2:作废
     */
    private Byte inApproveStatus;

    /**
     * 调入门店审批时间
     */
    private Date inApproveTime;

    /**
     * 调入数量
     */
    private BigDecimal inAllotQuantity;

    /**
     * 调入门店入库时间
     */
    private Date inStockTime;

    /**
     * iscm-调拨数量
     */
    private BigDecimal allotQuantity;

    /**
     * 商品编码
     */
    private String goodsNo;

    /**
     * 生产批号
     */
    private String batchNo;

    /**
     * 生产日期
     */
    private String produceDate;

    /**
     * 有效期至
     */
    private String validityDate;

    /**
     * 海典批次
     */
    private String hdBatchNo;

    /**
     * sap批次
     */
    private String sapBatchNo;

    /**
     * 备注
     */
    private String notes;

    /**
     * 作废原因
     */
    private String voidReason;

    /**
     * 分单前单号
     */
    private String srcDistNo;

    /**
     * 状态(-1删除，0正常)
     */
    private Byte status;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新时间
     */
    private Date gmtUpdate;

    /**
     * 扩展字段
     */
    private String extend;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 创建人ID
     */
    private Long createdBy;

    /**
     * 创建人
     */
    private String createdName;

    /**
     * 更新人ID
     */
    private Long updatedBy;

    /**
     * 更新人
     */
    private String updatedName;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getRegisterDate() {
        return registerDate;
    }

    public void setRegisterDate(Date registerDate) {
        this.registerDate = registerDate;
    }

    public Byte getAllotType() {
        return allotType;
    }

    public void setAllotType(Byte allotType) {
        this.allotType = allotType;
    }

    public String getRegisterNo() {
        return registerNo;
    }

    public void setRegisterNo(String registerNo) {
        this.registerNo = registerNo;
    }

    public String getPosAllotNo() {
        return posAllotNo;
    }

    public void setPosAllotNo(String posAllotNo) {
        this.posAllotNo = posAllotNo;
    }

    public Long getPosAllotDetailId() {
        return posAllotDetailId;
    }

    public void setPosAllotDetailId(Long posAllotDetailId) {
        this.posAllotDetailId = posAllotDetailId;
    }

    public String getOutCompanyCode() {
        return outCompanyCode;
    }

    public void setOutCompanyCode(String outCompanyCode) {
        this.outCompanyCode = outCompanyCode;
    }

    public String getInCompanyCode() {
        return inCompanyCode;
    }

    public void setInCompanyCode(String inCompanyCode) {
        this.inCompanyCode = inCompanyCode;
    }

    public String getOutStoreCode() {
        return outStoreCode;
    }

    public void setOutStoreCode(String outStoreCode) {
        this.outStoreCode = outStoreCode;
    }

    public String getInStoreCode() {
        return inStoreCode;
    }

    public void setInStoreCode(String inStoreCode) {
        this.inStoreCode = inStoreCode;
    }

    public Byte getOutApproveStatus() {
        return outApproveStatus;
    }

    public void setOutApproveStatus(Byte outApproveStatus) {
        this.outApproveStatus = outApproveStatus;
    }

    public Date getOutApproveTime() {
        return outApproveTime;
    }

    public void setOutApproveTime(Date outApproveTime) {
        this.outApproveTime = outApproveTime;
    }

    public BigDecimal getOutAllotQuantity() {
        return outAllotQuantity;
    }

    public void setOutAllotQuantity(BigDecimal outAllotQuantity) {
        this.outAllotQuantity = outAllotQuantity;
    }

    public Byte getInApproveStatus() {
        return inApproveStatus;
    }

    public void setInApproveStatus(Byte inApproveStatus) {
        this.inApproveStatus = inApproveStatus;
    }

    public Date getInApproveTime() {
        return inApproveTime;
    }

    public void setInApproveTime(Date inApproveTime) {
        this.inApproveTime = inApproveTime;
    }

    public BigDecimal getInAllotQuantity() {
        return inAllotQuantity;
    }

    public void setInAllotQuantity(BigDecimal inAllotQuantity) {
        this.inAllotQuantity = inAllotQuantity;
    }

    public Date getInStockTime() {
        return inStockTime;
    }

    public void setInStockTime(Date inStockTime) {
        this.inStockTime = inStockTime;
    }

    public BigDecimal getAllotQuantity() {
        return allotQuantity;
    }

    public void setAllotQuantity(BigDecimal allotQuantity) {
        this.allotQuantity = allotQuantity;
    }

    public String getGoodsNo() {
        return goodsNo;
    }

    public void setGoodsNo(String goodsNo) {
        this.goodsNo = goodsNo;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public String getProduceDate() {
        return produceDate;
    }

    public void setProduceDate(String produceDate) {
        this.produceDate = produceDate;
    }

    public String getValidityDate() {
        return validityDate;
    }

    public void setValidityDate(String validityDate) {
        this.validityDate = validityDate;
    }

    public String getHdBatchNo() {
        return hdBatchNo;
    }

    public void setHdBatchNo(String hdBatchNo) {
        this.hdBatchNo = hdBatchNo;
    }

    public String getSapBatchNo() {
        return sapBatchNo;
    }

    public void setSapBatchNo(String sapBatchNo) {
        this.sapBatchNo = sapBatchNo;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public String getVoidReason() {
        return voidReason;
    }

    public void setVoidReason(String voidReason) {
        this.voidReason = voidReason;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtUpdate() {
        return gmtUpdate;
    }

    public void setGmtUpdate(Date gmtUpdate) {
        this.gmtUpdate = gmtUpdate;
    }

    public String getExtend() {
        return extend;
    }

    public void setExtend(String extend) {
        this.extend = extend;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Long getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    public String getCreatedName() {
        return createdName;
    }

    public void setCreatedName(String createdName) {
        this.createdName = createdName;
    }

    public Long getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(Long updatedBy) {
        this.updatedBy = updatedBy;
    }

    public String getUpdatedName() {
        return updatedName;
    }

    public void setUpdatedName(String updatedName) {
        this.updatedName = updatedName;
    }

    public String getSrcDistNo() {
        return srcDistNo;
    }

    public void setSrcDistNo(String srcDistNo) {
        this.srcDistNo = srcDistNo;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        IscmSuggestDistexec other = (IscmSuggestDistexec) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getRegisterDate() == null ? other.getRegisterDate() == null : this.getRegisterDate().equals(other.getRegisterDate()))
            && (this.getAllotType() == null ? other.getAllotType() == null : this.getAllotType().equals(other.getAllotType()))
            && (this.getRegisterNo() == null ? other.getRegisterNo() == null : this.getRegisterNo().equals(other.getRegisterNo()))
            && (this.getPosAllotNo() == null ? other.getPosAllotNo() == null : this.getPosAllotNo().equals(other.getPosAllotNo()))
            && (this.getPosAllotDetailId() == null ? other.getPosAllotDetailId() == null : this.getPosAllotDetailId().equals(other.getPosAllotDetailId()))
            && (this.getOutCompanyCode() == null ? other.getOutCompanyCode() == null : this.getOutCompanyCode().equals(other.getOutCompanyCode()))
            && (this.getInCompanyCode() == null ? other.getInCompanyCode() == null : this.getInCompanyCode().equals(other.getInCompanyCode()))
            && (this.getOutStoreCode() == null ? other.getOutStoreCode() == null : this.getOutStoreCode().equals(other.getOutStoreCode()))
            && (this.getInStoreCode() == null ? other.getInStoreCode() == null : this.getInStoreCode().equals(other.getInStoreCode()))
            && (this.getOutApproveStatus() == null ? other.getOutApproveStatus() == null : this.getOutApproveStatus().equals(other.getOutApproveStatus()))
            && (this.getOutApproveTime() == null ? other.getOutApproveTime() == null : this.getOutApproveTime().equals(other.getOutApproveTime()))
            && (this.getOutAllotQuantity() == null ? other.getOutAllotQuantity() == null : this.getOutAllotQuantity().equals(other.getOutAllotQuantity()))
            && (this.getInApproveStatus() == null ? other.getInApproveStatus() == null : this.getInApproveStatus().equals(other.getInApproveStatus()))
            && (this.getInApproveTime() == null ? other.getInApproveTime() == null : this.getInApproveTime().equals(other.getInApproveTime()))
            && (this.getInAllotQuantity() == null ? other.getInAllotQuantity() == null : this.getInAllotQuantity().equals(other.getInAllotQuantity()))
            && (this.getInStockTime() == null ? other.getInStockTime() == null : this.getInStockTime().equals(other.getInStockTime()))
            && (this.getAllotQuantity() == null ? other.getAllotQuantity() == null : this.getAllotQuantity().equals(other.getAllotQuantity()))
            && (this.getGoodsNo() == null ? other.getGoodsNo() == null : this.getGoodsNo().equals(other.getGoodsNo()))
            && (this.getBatchNo() == null ? other.getBatchNo() == null : this.getBatchNo().equals(other.getBatchNo()))
            && (this.getProduceDate() == null ? other.getProduceDate() == null : this.getProduceDate().equals(other.getProduceDate()))
            && (this.getValidityDate() == null ? other.getValidityDate() == null : this.getValidityDate().equals(other.getValidityDate()))
            && (this.getHdBatchNo() == null ? other.getHdBatchNo() == null : this.getHdBatchNo().equals(other.getHdBatchNo()))
            && (this.getSapBatchNo() == null ? other.getSapBatchNo() == null : this.getSapBatchNo().equals(other.getSapBatchNo()))
            && (this.getNotes() == null ? other.getNotes() == null : this.getNotes().equals(other.getNotes()))
            && (this.getVoidReason() == null ? other.getVoidReason() == null : this.getVoidReason().equals(other.getVoidReason()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getGmtCreate() == null ? other.getGmtCreate() == null : this.getGmtCreate().equals(other.getGmtCreate()))
            && (this.getGmtUpdate() == null ? other.getGmtUpdate() == null : this.getGmtUpdate().equals(other.getGmtUpdate()))
            && (this.getExtend() == null ? other.getExtend() == null : this.getExtend().equals(other.getExtend()))
            && (this.getVersion() == null ? other.getVersion() == null : this.getVersion().equals(other.getVersion()))
            && (this.getCreatedBy() == null ? other.getCreatedBy() == null : this.getCreatedBy().equals(other.getCreatedBy()))
            && (this.getCreatedName() == null ? other.getCreatedName() == null : this.getCreatedName().equals(other.getCreatedName()))
            && (this.getUpdatedBy() == null ? other.getUpdatedBy() == null : this.getUpdatedBy().equals(other.getUpdatedBy()))
            && (this.getUpdatedName() == null ? other.getUpdatedName() == null : this.getUpdatedName().equals(other.getUpdatedName()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getRegisterDate() == null) ? 0 : getRegisterDate().hashCode());
        result = prime * result + ((getAllotType() == null) ? 0 : getAllotType().hashCode());
        result = prime * result + ((getRegisterNo() == null) ? 0 : getRegisterNo().hashCode());
        result = prime * result + ((getPosAllotNo() == null) ? 0 : getPosAllotNo().hashCode());
        result = prime * result + ((getPosAllotDetailId() == null) ? 0 : getPosAllotDetailId().hashCode());
        result = prime * result + ((getOutCompanyCode() == null) ? 0 : getOutCompanyCode().hashCode());
        result = prime * result + ((getInCompanyCode() == null) ? 0 : getInCompanyCode().hashCode());
        result = prime * result + ((getOutStoreCode() == null) ? 0 : getOutStoreCode().hashCode());
        result = prime * result + ((getInStoreCode() == null) ? 0 : getInStoreCode().hashCode());
        result = prime * result + ((getOutApproveStatus() == null) ? 0 : getOutApproveStatus().hashCode());
        result = prime * result + ((getOutApproveTime() == null) ? 0 : getOutApproveTime().hashCode());
        result = prime * result + ((getOutAllotQuantity() == null) ? 0 : getOutAllotQuantity().hashCode());
        result = prime * result + ((getInApproveStatus() == null) ? 0 : getInApproveStatus().hashCode());
        result = prime * result + ((getInApproveTime() == null) ? 0 : getInApproveTime().hashCode());
        result = prime * result + ((getInAllotQuantity() == null) ? 0 : getInAllotQuantity().hashCode());
        result = prime * result + ((getInStockTime() == null) ? 0 : getInStockTime().hashCode());
        result = prime * result + ((getAllotQuantity() == null) ? 0 : getAllotQuantity().hashCode());
        result = prime * result + ((getGoodsNo() == null) ? 0 : getGoodsNo().hashCode());
        result = prime * result + ((getBatchNo() == null) ? 0 : getBatchNo().hashCode());
        result = prime * result + ((getProduceDate() == null) ? 0 : getProduceDate().hashCode());
        result = prime * result + ((getValidityDate() == null) ? 0 : getValidityDate().hashCode());
        result = prime * result + ((getHdBatchNo() == null) ? 0 : getHdBatchNo().hashCode());
        result = prime * result + ((getSapBatchNo() == null) ? 0 : getSapBatchNo().hashCode());
        result = prime * result + ((getNotes() == null) ? 0 : getNotes().hashCode());
        result = prime * result + ((getVoidReason() == null) ? 0 : getVoidReason().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getGmtCreate() == null) ? 0 : getGmtCreate().hashCode());
        result = prime * result + ((getGmtUpdate() == null) ? 0 : getGmtUpdate().hashCode());
        result = prime * result + ((getExtend() == null) ? 0 : getExtend().hashCode());
        result = prime * result + ((getVersion() == null) ? 0 : getVersion().hashCode());
        result = prime * result + ((getCreatedBy() == null) ? 0 : getCreatedBy().hashCode());
        result = prime * result + ((getCreatedName() == null) ? 0 : getCreatedName().hashCode());
        result = prime * result + ((getUpdatedBy() == null) ? 0 : getUpdatedBy().hashCode());
        result = prime * result + ((getUpdatedName() == null) ? 0 : getUpdatedName().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", registerDate=").append(registerDate);
        sb.append(", allotType=").append(allotType);
        sb.append(", registerNo=").append(registerNo);
        sb.append(", posAllotNo=").append(posAllotNo);
        sb.append(", posAllotDetailId=").append(posAllotDetailId);
        sb.append(", outCompanyCode=").append(outCompanyCode);
        sb.append(", inCompanyCode=").append(inCompanyCode);
        sb.append(", outStoreCode=").append(outStoreCode);
        sb.append(", inStoreCode=").append(inStoreCode);
        sb.append(", outApproveStatus=").append(outApproveStatus);
        sb.append(", outApproveTime=").append(outApproveTime);
        sb.append(", outAllotQuantity=").append(outAllotQuantity);
        sb.append(", inApproveStatus=").append(inApproveStatus);
        sb.append(", inApproveTime=").append(inApproveTime);
        sb.append(", inAllotQuantity=").append(inAllotQuantity);
        sb.append(", inStockTime=").append(inStockTime);
        sb.append(", allotQuantity=").append(allotQuantity);
        sb.append(", goodsNo=").append(goodsNo);
        sb.append(", batchNo=").append(batchNo);
        sb.append(", produceDate=").append(produceDate);
        sb.append(", validityDate=").append(validityDate);
        sb.append(", hdBatchNo=").append(hdBatchNo);
        sb.append(", sapBatchNo=").append(sapBatchNo);
        sb.append(", notes=").append(notes);
        sb.append(", voidReason=").append(voidReason);
        sb.append(", srcDistNo=").append(srcDistNo);
        sb.append(", status=").append(status);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", gmtUpdate=").append(gmtUpdate);
        sb.append(", extend=").append(extend);
        sb.append(", version=").append(version);
        sb.append(", createdBy=").append(createdBy);
        sb.append(", createdName=").append(createdName);
        sb.append(", updatedBy=").append(updatedBy);
        sb.append(", updatedName=").append(updatedName);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}
