package com.cowell.iscm.entityTidb;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> 替换码转换表
 */
public class IscmApplyGoodsReplaceCode implements Serializable {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 公司编码
     */
    private String companyCode;

    /**
     * 主商品编码
     */
    private String masterGoodsNo;

    /**
     * 替换商品编码
     */
    private String replaceGoodsNo;

    /**
     * 转换比率
     */
    private BigDecimal changeRatio;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新时间
     */
    private Date gmtUpdate;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getMasterGoodsNo() {
        return masterGoodsNo;
    }

    public void setMasterGoodsNo(String masterGoodsNo) {
        this.masterGoodsNo = masterGoodsNo;
    }

    public String getReplaceGoodsNo() {
        return replaceGoodsNo;
    }

    public void setReplaceGoodsNo(String replaceGoodsNo) {
        this.replaceGoodsNo = replaceGoodsNo;
    }

    public BigDecimal getChangeRatio() {
        return changeRatio;
    }

    public void setChangeRatio(BigDecimal changeRatio) {
        this.changeRatio = changeRatio;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtUpdate() {
        return gmtUpdate;
    }

    public void setGmtUpdate(Date gmtUpdate) {
        this.gmtUpdate = gmtUpdate;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        IscmApplyGoodsReplaceCode other = (IscmApplyGoodsReplaceCode) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getCompanyCode() == null ? other.getCompanyCode() == null : this.getCompanyCode().equals(other.getCompanyCode()))
            && (this.getMasterGoodsNo() == null ? other.getMasterGoodsNo() == null : this.getMasterGoodsNo().equals(other.getMasterGoodsNo()))
            && (this.getReplaceGoodsNo() == null ? other.getReplaceGoodsNo() == null : this.getReplaceGoodsNo().equals(other.getReplaceGoodsNo()))
            && (this.getChangeRatio() == null ? other.getChangeRatio() == null : this.getChangeRatio().equals(other.getChangeRatio()))
            && (this.getGmtCreate() == null ? other.getGmtCreate() == null : this.getGmtCreate().equals(other.getGmtCreate()))
            && (this.getGmtUpdate() == null ? other.getGmtUpdate() == null : this.getGmtUpdate().equals(other.getGmtUpdate()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getCompanyCode() == null) ? 0 : getCompanyCode().hashCode());
        result = prime * result + ((getMasterGoodsNo() == null) ? 0 : getMasterGoodsNo().hashCode());
        result = prime * result + ((getReplaceGoodsNo() == null) ? 0 : getReplaceGoodsNo().hashCode());
        result = prime * result + ((getChangeRatio() == null) ? 0 : getChangeRatio().hashCode());
        result = prime * result + ((getGmtCreate() == null) ? 0 : getGmtCreate().hashCode());
        result = prime * result + ((getGmtUpdate() == null) ? 0 : getGmtUpdate().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", companyCode=").append(companyCode);
        sb.append(", masterGoodsNo=").append(masterGoodsNo);
        sb.append(", replaceGoodsNo=").append(replaceGoodsNo);
        sb.append(", changeRatio=").append(changeRatio);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", gmtUpdate=").append(gmtUpdate);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}