package com.cowell.iscm.entityTidb;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> 门店请货上浮设置作废数据
 */
public class IscmStoreApplyAutoInvalidFloat implements Serializable {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 参数编号
     */
    private String paramCode;

    /**
     * 参数名称
     */
    private String paramName;

    /**
     * 参数级别 300 平台 500 连锁
     */
    private Integer paramLevel;

    /**
     * 机构Id
     */
    private Long orgId;

    /**
     * sap编码
     */
    private String sapCode;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 上级机构Id
     */
    private Long parentOrgId;

    /**
     * 上级机构名称
     */
    private String parentOrgName;

    /**
     * 门店ID
     */
    private Long storeOrgId;

    /**
     * 门店编码
     */
    private String storeCode;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 商品选择类型 1：单品 2：品类 3：全品 4：高价贵细
     */
    private Byte goodsChooseType;

    /**
     * 商品编码
     */
    private String goodsNo;

    /**
     * 连锁id
     */
    private Long businessid;

    /**
     * 条形码
     */
    private String barCode;

    /**
     * 通用名
     */
    private String curName;

    /**
     * 商品名
     */
    private String goodsName;

    /**
     * 单位
     */
    private String goodsUnit;

    /**
     * 规格
     */
    private String specifications;

    /**
     * 生产厂家
     */
    private String manufacturer;

    /**
     * 销售属性
     */
    private String goodsline;

    /**
     * 特殊属性
     */
    private String specialattributes;

    /**
     * 商品等级
     */
    private Byte goodsLevel;

    /**
     * 开始时间
     */
    private Date startDate;

    /**
     * 结束时间
     */
    private Date endDate;

    /**
     * 是否继承上级 0 否 1 是
     */
    private Byte inheritType;

    /**
     * 生效状态 0 生效中 1 作废
     */
    private Byte effectStatus;

    /**
     * 调整类型 1 上浮 2 下调
     */
    private Byte adjustType;

    /**
     * 调整模式 1 按比例 2 按天数
     */
    private Byte adjustMode;

    /**
     * 上线调整数量
     */
    private Integer adjustUpperQuantity;

    /**
     * 下限调整数量
     */
    private Integer adjustLowerQuantity;

    /**
     * 状态(-1删除，0正常)
     */
    private Byte status;

    /**
     * 扩展字段
     */
    private String extend;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 创建人ID
     */
    private Long createdBy;

    /**
     * 创建人
     */
    private String createdName;

    /**
     * 更新人ID
     */
    private Long updatedBy;

    /**
     * 更新人
     */
    private String updatedName;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新时间
     */
    private Date gmtUpdate;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getParamCode() {
        return paramCode;
    }

    public void setParamCode(String paramCode) {
        this.paramCode = paramCode;
    }

    public String getParamName() {
        return paramName;
    }

    public void setParamName(String paramName) {
        this.paramName = paramName;
    }

    public Integer getParamLevel() {
        return paramLevel;
    }

    public void setParamLevel(Integer paramLevel) {
        this.paramLevel = paramLevel;
    }

    public Long getOrgId() {
        return orgId;
    }

    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    public String getSapCode() {
        return sapCode;
    }

    public void setSapCode(String sapCode) {
        this.sapCode = sapCode;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public Long getParentOrgId() {
        return parentOrgId;
    }

    public void setParentOrgId(Long parentOrgId) {
        this.parentOrgId = parentOrgId;
    }

    public String getParentOrgName() {
        return parentOrgName;
    }

    public void setParentOrgName(String parentOrgName) {
        this.parentOrgName = parentOrgName;
    }

    public Long getStoreOrgId() {
        return storeOrgId;
    }

    public void setStoreOrgId(Long storeOrgId) {
        this.storeOrgId = storeOrgId;
    }

    public String getStoreCode() {
        return storeCode;
    }

    public void setStoreCode(String storeCode) {
        this.storeCode = storeCode;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public Byte getGoodsChooseType() {
        return goodsChooseType;
    }

    public void setGoodsChooseType(Byte goodsChooseType) {
        this.goodsChooseType = goodsChooseType;
    }

    public String getGoodsNo() {
        return goodsNo;
    }

    public void setGoodsNo(String goodsNo) {
        this.goodsNo = goodsNo;
    }

    public Long getBusinessid() {
        return businessid;
    }

    public void setBusinessid(Long businessid) {
        this.businessid = businessid;
    }

    public String getBarCode() {
        return barCode;
    }

    public void setBarCode(String barCode) {
        this.barCode = barCode;
    }

    public String getCurName() {
        return curName;
    }

    public void setCurName(String curName) {
        this.curName = curName;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getGoodsUnit() {
        return goodsUnit;
    }

    public void setGoodsUnit(String goodsUnit) {
        this.goodsUnit = goodsUnit;
    }

    public String getSpecifications() {
        return specifications;
    }

    public void setSpecifications(String specifications) {
        this.specifications = specifications;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    public String getGoodsline() {
        return goodsline;
    }

    public void setGoodsline(String goodsline) {
        this.goodsline = goodsline;
    }

    public String getSpecialattributes() {
        return specialattributes;
    }

    public void setSpecialattributes(String specialattributes) {
        this.specialattributes = specialattributes;
    }

    public Byte getGoodsLevel() {
        return goodsLevel;
    }

    public void setGoodsLevel(Byte goodsLevel) {
        this.goodsLevel = goodsLevel;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public Byte getInheritType() {
        return inheritType;
    }

    public void setInheritType(Byte inheritType) {
        this.inheritType = inheritType;
    }

    public Byte getEffectStatus() {
        return effectStatus;
    }

    public void setEffectStatus(Byte effectStatus) {
        this.effectStatus = effectStatus;
    }

    public Byte getAdjustType() {
        return adjustType;
    }

    public void setAdjustType(Byte adjustType) {
        this.adjustType = adjustType;
    }

    public Byte getAdjustMode() {
        return adjustMode;
    }

    public void setAdjustMode(Byte adjustMode) {
        this.adjustMode = adjustMode;
    }

    public Integer getAdjustUpperQuantity() {
        return adjustUpperQuantity;
    }

    public void setAdjustUpperQuantity(Integer adjustUpperQuantity) {
        this.adjustUpperQuantity = adjustUpperQuantity;
    }

    public Integer getAdjustLowerQuantity() {
        return adjustLowerQuantity;
    }

    public void setAdjustLowerQuantity(Integer adjustLowerQuantity) {
        this.adjustLowerQuantity = adjustLowerQuantity;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public String getExtend() {
        return extend;
    }

    public void setExtend(String extend) {
        this.extend = extend;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Long getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    public String getCreatedName() {
        return createdName;
    }

    public void setCreatedName(String createdName) {
        this.createdName = createdName;
    }

    public Long getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(Long updatedBy) {
        this.updatedBy = updatedBy;
    }

    public String getUpdatedName() {
        return updatedName;
    }

    public void setUpdatedName(String updatedName) {
        this.updatedName = updatedName;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtUpdate() {
        return gmtUpdate;
    }

    public void setGmtUpdate(Date gmtUpdate) {
        this.gmtUpdate = gmtUpdate;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        IscmStoreApplyAutoInvalidFloat other = (IscmStoreApplyAutoInvalidFloat) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getParamCode() == null ? other.getParamCode() == null : this.getParamCode().equals(other.getParamCode()))
            && (this.getParamName() == null ? other.getParamName() == null : this.getParamName().equals(other.getParamName()))
            && (this.getParamLevel() == null ? other.getParamLevel() == null : this.getParamLevel().equals(other.getParamLevel()))
            && (this.getOrgId() == null ? other.getOrgId() == null : this.getOrgId().equals(other.getOrgId()))
            && (this.getSapCode() == null ? other.getSapCode() == null : this.getSapCode().equals(other.getSapCode()))
            && (this.getOrgName() == null ? other.getOrgName() == null : this.getOrgName().equals(other.getOrgName()))
            && (this.getParentOrgId() == null ? other.getParentOrgId() == null : this.getParentOrgId().equals(other.getParentOrgId()))
            && (this.getParentOrgName() == null ? other.getParentOrgName() == null : this.getParentOrgName().equals(other.getParentOrgName()))
            && (this.getStoreOrgId() == null ? other.getStoreOrgId() == null : this.getStoreOrgId().equals(other.getStoreOrgId()))
            && (this.getStoreCode() == null ? other.getStoreCode() == null : this.getStoreCode().equals(other.getStoreCode()))
            && (this.getStoreName() == null ? other.getStoreName() == null : this.getStoreName().equals(other.getStoreName()))
            && (this.getGoodsChooseType() == null ? other.getGoodsChooseType() == null : this.getGoodsChooseType().equals(other.getGoodsChooseType()))
            && (this.getGoodsNo() == null ? other.getGoodsNo() == null : this.getGoodsNo().equals(other.getGoodsNo()))
            && (this.getBusinessid() == null ? other.getBusinessid() == null : this.getBusinessid().equals(other.getBusinessid()))
            && (this.getBarCode() == null ? other.getBarCode() == null : this.getBarCode().equals(other.getBarCode()))
            && (this.getCurName() == null ? other.getCurName() == null : this.getCurName().equals(other.getCurName()))
            && (this.getGoodsName() == null ? other.getGoodsName() == null : this.getGoodsName().equals(other.getGoodsName()))
            && (this.getGoodsUnit() == null ? other.getGoodsUnit() == null : this.getGoodsUnit().equals(other.getGoodsUnit()))
            && (this.getSpecifications() == null ? other.getSpecifications() == null : this.getSpecifications().equals(other.getSpecifications()))
            && (this.getManufacturer() == null ? other.getManufacturer() == null : this.getManufacturer().equals(other.getManufacturer()))
            && (this.getGoodsline() == null ? other.getGoodsline() == null : this.getGoodsline().equals(other.getGoodsline()))
            && (this.getSpecialattributes() == null ? other.getSpecialattributes() == null : this.getSpecialattributes().equals(other.getSpecialattributes()))
            && (this.getGoodsLevel() == null ? other.getGoodsLevel() == null : this.getGoodsLevel().equals(other.getGoodsLevel()))
            && (this.getStartDate() == null ? other.getStartDate() == null : this.getStartDate().equals(other.getStartDate()))
            && (this.getEndDate() == null ? other.getEndDate() == null : this.getEndDate().equals(other.getEndDate()))
            && (this.getInheritType() == null ? other.getInheritType() == null : this.getInheritType().equals(other.getInheritType()))
            && (this.getEffectStatus() == null ? other.getEffectStatus() == null : this.getEffectStatus().equals(other.getEffectStatus()))
            && (this.getAdjustType() == null ? other.getAdjustType() == null : this.getAdjustType().equals(other.getAdjustType()))
            && (this.getAdjustMode() == null ? other.getAdjustMode() == null : this.getAdjustMode().equals(other.getAdjustMode()))
            && (this.getAdjustUpperQuantity() == null ? other.getAdjustUpperQuantity() == null : this.getAdjustUpperQuantity().equals(other.getAdjustUpperQuantity()))
            && (this.getAdjustLowerQuantity() == null ? other.getAdjustLowerQuantity() == null : this.getAdjustLowerQuantity().equals(other.getAdjustLowerQuantity()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getExtend() == null ? other.getExtend() == null : this.getExtend().equals(other.getExtend()))
            && (this.getVersion() == null ? other.getVersion() == null : this.getVersion().equals(other.getVersion()))
            && (this.getCreatedBy() == null ? other.getCreatedBy() == null : this.getCreatedBy().equals(other.getCreatedBy()))
            && (this.getCreatedName() == null ? other.getCreatedName() == null : this.getCreatedName().equals(other.getCreatedName()))
            && (this.getUpdatedBy() == null ? other.getUpdatedBy() == null : this.getUpdatedBy().equals(other.getUpdatedBy()))
            && (this.getUpdatedName() == null ? other.getUpdatedName() == null : this.getUpdatedName().equals(other.getUpdatedName()))
            && (this.getGmtCreate() == null ? other.getGmtCreate() == null : this.getGmtCreate().equals(other.getGmtCreate()))
            && (this.getGmtUpdate() == null ? other.getGmtUpdate() == null : this.getGmtUpdate().equals(other.getGmtUpdate()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getParamCode() == null) ? 0 : getParamCode().hashCode());
        result = prime * result + ((getParamName() == null) ? 0 : getParamName().hashCode());
        result = prime * result + ((getParamLevel() == null) ? 0 : getParamLevel().hashCode());
        result = prime * result + ((getOrgId() == null) ? 0 : getOrgId().hashCode());
        result = prime * result + ((getSapCode() == null) ? 0 : getSapCode().hashCode());
        result = prime * result + ((getOrgName() == null) ? 0 : getOrgName().hashCode());
        result = prime * result + ((getParentOrgId() == null) ? 0 : getParentOrgId().hashCode());
        result = prime * result + ((getParentOrgName() == null) ? 0 : getParentOrgName().hashCode());
        result = prime * result + ((getStoreOrgId() == null) ? 0 : getStoreOrgId().hashCode());
        result = prime * result + ((getStoreCode() == null) ? 0 : getStoreCode().hashCode());
        result = prime * result + ((getStoreName() == null) ? 0 : getStoreName().hashCode());
        result = prime * result + ((getGoodsChooseType() == null) ? 0 : getGoodsChooseType().hashCode());
        result = prime * result + ((getGoodsNo() == null) ? 0 : getGoodsNo().hashCode());
        result = prime * result + ((getBusinessid() == null) ? 0 : getBusinessid().hashCode());
        result = prime * result + ((getBarCode() == null) ? 0 : getBarCode().hashCode());
        result = prime * result + ((getCurName() == null) ? 0 : getCurName().hashCode());
        result = prime * result + ((getGoodsName() == null) ? 0 : getGoodsName().hashCode());
        result = prime * result + ((getGoodsUnit() == null) ? 0 : getGoodsUnit().hashCode());
        result = prime * result + ((getSpecifications() == null) ? 0 : getSpecifications().hashCode());
        result = prime * result + ((getManufacturer() == null) ? 0 : getManufacturer().hashCode());
        result = prime * result + ((getGoodsline() == null) ? 0 : getGoodsline().hashCode());
        result = prime * result + ((getSpecialattributes() == null) ? 0 : getSpecialattributes().hashCode());
        result = prime * result + ((getGoodsLevel() == null) ? 0 : getGoodsLevel().hashCode());
        result = prime * result + ((getStartDate() == null) ? 0 : getStartDate().hashCode());
        result = prime * result + ((getEndDate() == null) ? 0 : getEndDate().hashCode());
        result = prime * result + ((getInheritType() == null) ? 0 : getInheritType().hashCode());
        result = prime * result + ((getEffectStatus() == null) ? 0 : getEffectStatus().hashCode());
        result = prime * result + ((getAdjustType() == null) ? 0 : getAdjustType().hashCode());
        result = prime * result + ((getAdjustMode() == null) ? 0 : getAdjustMode().hashCode());
        result = prime * result + ((getAdjustUpperQuantity() == null) ? 0 : getAdjustUpperQuantity().hashCode());
        result = prime * result + ((getAdjustLowerQuantity() == null) ? 0 : getAdjustLowerQuantity().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getExtend() == null) ? 0 : getExtend().hashCode());
        result = prime * result + ((getVersion() == null) ? 0 : getVersion().hashCode());
        result = prime * result + ((getCreatedBy() == null) ? 0 : getCreatedBy().hashCode());
        result = prime * result + ((getCreatedName() == null) ? 0 : getCreatedName().hashCode());
        result = prime * result + ((getUpdatedBy() == null) ? 0 : getUpdatedBy().hashCode());
        result = prime * result + ((getUpdatedName() == null) ? 0 : getUpdatedName().hashCode());
        result = prime * result + ((getGmtCreate() == null) ? 0 : getGmtCreate().hashCode());
        result = prime * result + ((getGmtUpdate() == null) ? 0 : getGmtUpdate().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", paramCode=").append(paramCode);
        sb.append(", paramName=").append(paramName);
        sb.append(", paramLevel=").append(paramLevel);
        sb.append(", orgId=").append(orgId);
        sb.append(", sapCode=").append(sapCode);
        sb.append(", orgName=").append(orgName);
        sb.append(", parentOrgId=").append(parentOrgId);
        sb.append(", parentOrgName=").append(parentOrgName);
        sb.append(", storeOrgId=").append(storeOrgId);
        sb.append(", storeCode=").append(storeCode);
        sb.append(", storeName=").append(storeName);
        sb.append(", goodsChooseType=").append(goodsChooseType);
        sb.append(", goodsNo=").append(goodsNo);
        sb.append(", businessid=").append(businessid);
        sb.append(", barCode=").append(barCode);
        sb.append(", curName=").append(curName);
        sb.append(", goodsName=").append(goodsName);
        sb.append(", goodsUnit=").append(goodsUnit);
        sb.append(", specifications=").append(specifications);
        sb.append(", manufacturer=").append(manufacturer);
        sb.append(", goodsline=").append(goodsline);
        sb.append(", specialattributes=").append(specialattributes);
        sb.append(", goodsLevel=").append(goodsLevel);
        sb.append(", startDate=").append(startDate);
        sb.append(", endDate=").append(endDate);
        sb.append(", inheritType=").append(inheritType);
        sb.append(", effectStatus=").append(effectStatus);
        sb.append(", adjustType=").append(adjustType);
        sb.append(", adjustMode=").append(adjustMode);
        sb.append(", adjustUpperQuantity=").append(adjustUpperQuantity);
        sb.append(", adjustLowerQuantity=").append(adjustLowerQuantity);
        sb.append(", status=").append(status);
        sb.append(", extend=").append(extend);
        sb.append(", version=").append(version);
        sb.append(", createdBy=").append(createdBy);
        sb.append(", createdName=").append(createdName);
        sb.append(", updatedBy=").append(updatedBy);
        sb.append(", updatedName=").append(updatedName);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", gmtUpdate=").append(gmtUpdate);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}