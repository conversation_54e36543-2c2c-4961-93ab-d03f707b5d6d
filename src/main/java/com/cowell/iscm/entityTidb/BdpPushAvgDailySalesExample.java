package com.cowell.iscm.entityTidb;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class BdpPushAvgDailySalesExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Long offset;

    public BdpPushAvgDailySalesExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Long offset) {
        this.offset = offset;
    }

    public Long getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIsNull() {
            addCriterion("company_code is null");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIsNotNull() {
            addCriterion("company_code is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeEqualTo(String value) {
            addCriterion("company_code =", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotEqualTo(String value) {
            addCriterion("company_code <>", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeGreaterThan(String value) {
            addCriterion("company_code >", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeGreaterThanOrEqualTo(String value) {
            addCriterion("company_code >=", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLessThan(String value) {
            addCriterion("company_code <", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLessThanOrEqualTo(String value) {
            addCriterion("company_code <=", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLike(String value) {
            addCriterion("company_code like", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotLike(String value) {
            addCriterion("company_code not like", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIn(List<String> values) {
            addCriterion("company_code in", values, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotIn(List<String> values) {
            addCriterion("company_code not in", values, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeBetween(String value1, String value2) {
            addCriterion("company_code between", value1, value2, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotBetween(String value1, String value2) {
            addCriterion("company_code not between", value1, value2, "companyCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeIsNull() {
            addCriterion("store_code is null");
            return (Criteria) this;
        }

        public Criteria andStoreCodeIsNotNull() {
            addCriterion("store_code is not null");
            return (Criteria) this;
        }

        public Criteria andStoreCodeEqualTo(String value) {
            addCriterion("store_code =", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotEqualTo(String value) {
            addCriterion("store_code <>", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeGreaterThan(String value) {
            addCriterion("store_code >", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeGreaterThanOrEqualTo(String value) {
            addCriterion("store_code >=", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeLessThan(String value) {
            addCriterion("store_code <", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeLessThanOrEqualTo(String value) {
            addCriterion("store_code <=", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeLike(String value) {
            addCriterion("store_code like", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotLike(String value) {
            addCriterion("store_code not like", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeIn(List<String> values) {
            addCriterion("store_code in", values, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotIn(List<String> values) {
            addCriterion("store_code not in", values, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeBetween(String value1, String value2) {
            addCriterion("store_code between", value1, value2, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotBetween(String value1, String value2) {
            addCriterion("store_code not between", value1, value2, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreAttrIsNull() {
            addCriterion("store_attr is null");
            return (Criteria) this;
        }

        public Criteria andStoreAttrIsNotNull() {
            addCriterion("store_attr is not null");
            return (Criteria) this;
        }

        public Criteria andStoreAttrEqualTo(Byte value) {
            addCriterion("store_attr =", value, "storeAttr");
            return (Criteria) this;
        }

        public Criteria andStoreAttrNotEqualTo(Byte value) {
            addCriterion("store_attr <>", value, "storeAttr");
            return (Criteria) this;
        }

        public Criteria andStoreAttrGreaterThan(Byte value) {
            addCriterion("store_attr >", value, "storeAttr");
            return (Criteria) this;
        }

        public Criteria andStoreAttrGreaterThanOrEqualTo(Byte value) {
            addCriterion("store_attr >=", value, "storeAttr");
            return (Criteria) this;
        }

        public Criteria andStoreAttrLessThan(Byte value) {
            addCriterion("store_attr <", value, "storeAttr");
            return (Criteria) this;
        }

        public Criteria andStoreAttrLessThanOrEqualTo(Byte value) {
            addCriterion("store_attr <=", value, "storeAttr");
            return (Criteria) this;
        }

        public Criteria andStoreAttrIn(List<Byte> values) {
            addCriterion("store_attr in", values, "storeAttr");
            return (Criteria) this;
        }

        public Criteria andStoreAttrNotIn(List<Byte> values) {
            addCriterion("store_attr not in", values, "storeAttr");
            return (Criteria) this;
        }

        public Criteria andStoreAttrBetween(Byte value1, Byte value2) {
            addCriterion("store_attr between", value1, value2, "storeAttr");
            return (Criteria) this;
        }

        public Criteria andStoreAttrNotBetween(Byte value1, Byte value2) {
            addCriterion("store_attr not between", value1, value2, "storeAttr");
            return (Criteria) this;
        }

        public Criteria andGoodsNoIsNull() {
            addCriterion("goods_no is null");
            return (Criteria) this;
        }

        public Criteria andGoodsNoIsNotNull() {
            addCriterion("goods_no is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsNoEqualTo(String value) {
            addCriterion("goods_no =", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotEqualTo(String value) {
            addCriterion("goods_no <>", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoGreaterThan(String value) {
            addCriterion("goods_no >", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoGreaterThanOrEqualTo(String value) {
            addCriterion("goods_no >=", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoLessThan(String value) {
            addCriterion("goods_no <", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoLessThanOrEqualTo(String value) {
            addCriterion("goods_no <=", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoLike(String value) {
            addCriterion("goods_no like", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotLike(String value) {
            addCriterion("goods_no not like", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoIn(List<String> values) {
            addCriterion("goods_no in", values, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotIn(List<String> values) {
            addCriterion("goods_no not in", values, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoBetween(String value1, String value2) {
            addCriterion("goods_no between", value1, value2, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotBetween(String value1, String value2) {
            addCriterion("goods_no not between", value1, value2, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andSpecialCtrlIsNull() {
            addCriterion("special_ctrl is null");
            return (Criteria) this;
        }

        public Criteria andSpecialCtrlIsNotNull() {
            addCriterion("special_ctrl is not null");
            return (Criteria) this;
        }

        public Criteria andSpecialCtrlEqualTo(Byte value) {
            addCriterion("special_ctrl =", value, "specialCtrl");
            return (Criteria) this;
        }

        public Criteria andSpecialCtrlNotEqualTo(Byte value) {
            addCriterion("special_ctrl <>", value, "specialCtrl");
            return (Criteria) this;
        }

        public Criteria andSpecialCtrlGreaterThan(Byte value) {
            addCriterion("special_ctrl >", value, "specialCtrl");
            return (Criteria) this;
        }

        public Criteria andSpecialCtrlGreaterThanOrEqualTo(Byte value) {
            addCriterion("special_ctrl >=", value, "specialCtrl");
            return (Criteria) this;
        }

        public Criteria andSpecialCtrlLessThan(Byte value) {
            addCriterion("special_ctrl <", value, "specialCtrl");
            return (Criteria) this;
        }

        public Criteria andSpecialCtrlLessThanOrEqualTo(Byte value) {
            addCriterion("special_ctrl <=", value, "specialCtrl");
            return (Criteria) this;
        }

        public Criteria andSpecialCtrlIn(List<Byte> values) {
            addCriterion("special_ctrl in", values, "specialCtrl");
            return (Criteria) this;
        }

        public Criteria andSpecialCtrlNotIn(List<Byte> values) {
            addCriterion("special_ctrl not in", values, "specialCtrl");
            return (Criteria) this;
        }

        public Criteria andSpecialCtrlBetween(Byte value1, Byte value2) {
            addCriterion("special_ctrl between", value1, value2, "specialCtrl");
            return (Criteria) this;
        }

        public Criteria andSpecialCtrlNotBetween(Byte value1, Byte value2) {
            addCriterion("special_ctrl not between", value1, value2, "specialCtrl");
            return (Criteria) this;
        }

        public Criteria andDistrForbidIsNull() {
            addCriterion("distr_forbid is null");
            return (Criteria) this;
        }

        public Criteria andDistrForbidIsNotNull() {
            addCriterion("distr_forbid is not null");
            return (Criteria) this;
        }

        public Criteria andDistrForbidEqualTo(Byte value) {
            addCriterion("distr_forbid =", value, "distrForbid");
            return (Criteria) this;
        }

        public Criteria andDistrForbidNotEqualTo(Byte value) {
            addCriterion("distr_forbid <>", value, "distrForbid");
            return (Criteria) this;
        }

        public Criteria andDistrForbidGreaterThan(Byte value) {
            addCriterion("distr_forbid >", value, "distrForbid");
            return (Criteria) this;
        }

        public Criteria andDistrForbidGreaterThanOrEqualTo(Byte value) {
            addCriterion("distr_forbid >=", value, "distrForbid");
            return (Criteria) this;
        }

        public Criteria andDistrForbidLessThan(Byte value) {
            addCriterion("distr_forbid <", value, "distrForbid");
            return (Criteria) this;
        }

        public Criteria andDistrForbidLessThanOrEqualTo(Byte value) {
            addCriterion("distr_forbid <=", value, "distrForbid");
            return (Criteria) this;
        }

        public Criteria andDistrForbidIn(List<Byte> values) {
            addCriterion("distr_forbid in", values, "distrForbid");
            return (Criteria) this;
        }

        public Criteria andDistrForbidNotIn(List<Byte> values) {
            addCriterion("distr_forbid not in", values, "distrForbid");
            return (Criteria) this;
        }

        public Criteria andDistrForbidBetween(Byte value1, Byte value2) {
            addCriterion("distr_forbid between", value1, value2, "distrForbid");
            return (Criteria) this;
        }

        public Criteria andDistrForbidNotBetween(Byte value1, Byte value2) {
            addCriterion("distr_forbid not between", value1, value2, "distrForbid");
            return (Criteria) this;
        }

        public Criteria andApplyForbidIsNull() {
            addCriterion("apply_forbid is null");
            return (Criteria) this;
        }

        public Criteria andApplyForbidIsNotNull() {
            addCriterion("apply_forbid is not null");
            return (Criteria) this;
        }

        public Criteria andApplyForbidEqualTo(Byte value) {
            addCriterion("apply_forbid =", value, "applyForbid");
            return (Criteria) this;
        }

        public Criteria andApplyForbidNotEqualTo(Byte value) {
            addCriterion("apply_forbid <>", value, "applyForbid");
            return (Criteria) this;
        }

        public Criteria andApplyForbidGreaterThan(Byte value) {
            addCriterion("apply_forbid >", value, "applyForbid");
            return (Criteria) this;
        }

        public Criteria andApplyForbidGreaterThanOrEqualTo(Byte value) {
            addCriterion("apply_forbid >=", value, "applyForbid");
            return (Criteria) this;
        }

        public Criteria andApplyForbidLessThan(Byte value) {
            addCriterion("apply_forbid <", value, "applyForbid");
            return (Criteria) this;
        }

        public Criteria andApplyForbidLessThanOrEqualTo(Byte value) {
            addCriterion("apply_forbid <=", value, "applyForbid");
            return (Criteria) this;
        }

        public Criteria andApplyForbidIn(List<Byte> values) {
            addCriterion("apply_forbid in", values, "applyForbid");
            return (Criteria) this;
        }

        public Criteria andApplyForbidNotIn(List<Byte> values) {
            addCriterion("apply_forbid not in", values, "applyForbid");
            return (Criteria) this;
        }

        public Criteria andApplyForbidBetween(Byte value1, Byte value2) {
            addCriterion("apply_forbid between", value1, value2, "applyForbid");
            return (Criteria) this;
        }

        public Criteria andApplyForbidNotBetween(Byte value1, Byte value2) {
            addCriterion("apply_forbid not between", value1, value2, "applyForbid");
            return (Criteria) this;
        }

        public Criteria andValuableIsNull() {
            addCriterion("valuable is null");
            return (Criteria) this;
        }

        public Criteria andValuableIsNotNull() {
            addCriterion("valuable is not null");
            return (Criteria) this;
        }

        public Criteria andValuableEqualTo(Byte value) {
            addCriterion("valuable =", value, "valuable");
            return (Criteria) this;
        }

        public Criteria andValuableNotEqualTo(Byte value) {
            addCriterion("valuable <>", value, "valuable");
            return (Criteria) this;
        }

        public Criteria andValuableGreaterThan(Byte value) {
            addCriterion("valuable >", value, "valuable");
            return (Criteria) this;
        }

        public Criteria andValuableGreaterThanOrEqualTo(Byte value) {
            addCriterion("valuable >=", value, "valuable");
            return (Criteria) this;
        }

        public Criteria andValuableLessThan(Byte value) {
            addCriterion("valuable <", value, "valuable");
            return (Criteria) this;
        }

        public Criteria andValuableLessThanOrEqualTo(Byte value) {
            addCriterion("valuable <=", value, "valuable");
            return (Criteria) this;
        }

        public Criteria andValuableIn(List<Byte> values) {
            addCriterion("valuable in", values, "valuable");
            return (Criteria) this;
        }

        public Criteria andValuableNotIn(List<Byte> values) {
            addCriterion("valuable not in", values, "valuable");
            return (Criteria) this;
        }

        public Criteria andValuableBetween(Byte value1, Byte value2) {
            addCriterion("valuable between", value1, value2, "valuable");
            return (Criteria) this;
        }

        public Criteria andValuableNotBetween(Byte value1, Byte value2) {
            addCriterion("valuable not between", value1, value2, "valuable");
            return (Criteria) this;
        }

        public Criteria andDtpGoodsIsNull() {
            addCriterion("dtp_goods is null");
            return (Criteria) this;
        }

        public Criteria andDtpGoodsIsNotNull() {
            addCriterion("dtp_goods is not null");
            return (Criteria) this;
        }

        public Criteria andDtpGoodsEqualTo(Byte value) {
            addCriterion("dtp_goods =", value, "dtpGoods");
            return (Criteria) this;
        }

        public Criteria andDtpGoodsNotEqualTo(Byte value) {
            addCriterion("dtp_goods <>", value, "dtpGoods");
            return (Criteria) this;
        }

        public Criteria andDtpGoodsGreaterThan(Byte value) {
            addCriterion("dtp_goods >", value, "dtpGoods");
            return (Criteria) this;
        }

        public Criteria andDtpGoodsGreaterThanOrEqualTo(Byte value) {
            addCriterion("dtp_goods >=", value, "dtpGoods");
            return (Criteria) this;
        }

        public Criteria andDtpGoodsLessThan(Byte value) {
            addCriterion("dtp_goods <", value, "dtpGoods");
            return (Criteria) this;
        }

        public Criteria andDtpGoodsLessThanOrEqualTo(Byte value) {
            addCriterion("dtp_goods <=", value, "dtpGoods");
            return (Criteria) this;
        }

        public Criteria andDtpGoodsIn(List<Byte> values) {
            addCriterion("dtp_goods in", values, "dtpGoods");
            return (Criteria) this;
        }

        public Criteria andDtpGoodsNotIn(List<Byte> values) {
            addCriterion("dtp_goods not in", values, "dtpGoods");
            return (Criteria) this;
        }

        public Criteria andDtpGoodsBetween(Byte value1, Byte value2) {
            addCriterion("dtp_goods between", value1, value2, "dtpGoods");
            return (Criteria) this;
        }

        public Criteria andDtpGoodsNotBetween(Byte value1, Byte value2) {
            addCriterion("dtp_goods not between", value1, value2, "dtpGoods");
            return (Criteria) this;
        }

        public Criteria andColdchainindIsNull() {
            addCriterion("coldchainind is null");
            return (Criteria) this;
        }

        public Criteria andColdchainindIsNotNull() {
            addCriterion("coldchainind is not null");
            return (Criteria) this;
        }

        public Criteria andColdchainindEqualTo(Byte value) {
            addCriterion("coldchainind =", value, "coldchainind");
            return (Criteria) this;
        }

        public Criteria andColdchainindNotEqualTo(Byte value) {
            addCriterion("coldchainind <>", value, "coldchainind");
            return (Criteria) this;
        }

        public Criteria andColdchainindGreaterThan(Byte value) {
            addCriterion("coldchainind >", value, "coldchainind");
            return (Criteria) this;
        }

        public Criteria andColdchainindGreaterThanOrEqualTo(Byte value) {
            addCriterion("coldchainind >=", value, "coldchainind");
            return (Criteria) this;
        }

        public Criteria andColdchainindLessThan(Byte value) {
            addCriterion("coldchainind <", value, "coldchainind");
            return (Criteria) this;
        }

        public Criteria andColdchainindLessThanOrEqualTo(Byte value) {
            addCriterion("coldchainind <=", value, "coldchainind");
            return (Criteria) this;
        }

        public Criteria andColdchainindIn(List<Byte> values) {
            addCriterion("coldchainind in", values, "coldchainind");
            return (Criteria) this;
        }

        public Criteria andColdchainindNotIn(List<Byte> values) {
            addCriterion("coldchainind not in", values, "coldchainind");
            return (Criteria) this;
        }

        public Criteria andColdchainindBetween(Byte value1, Byte value2) {
            addCriterion("coldchainind between", value1, value2, "coldchainind");
            return (Criteria) this;
        }

        public Criteria andColdchainindNotBetween(Byte value1, Byte value2) {
            addCriterion("coldchainind not between", value1, value2, "coldchainind");
            return (Criteria) this;
        }

        public Criteria andSpecialAttrIsNull() {
            addCriterion("special_attr is null");
            return (Criteria) this;
        }

        public Criteria andSpecialAttrIsNotNull() {
            addCriterion("special_attr is not null");
            return (Criteria) this;
        }

        public Criteria andSpecialAttrEqualTo(String value) {
            addCriterion("special_attr =", value, "specialAttr");
            return (Criteria) this;
        }

        public Criteria andSpecialAttrNotEqualTo(String value) {
            addCriterion("special_attr <>", value, "specialAttr");
            return (Criteria) this;
        }

        public Criteria andSpecialAttrGreaterThan(String value) {
            addCriterion("special_attr >", value, "specialAttr");
            return (Criteria) this;
        }

        public Criteria andSpecialAttrGreaterThanOrEqualTo(String value) {
            addCriterion("special_attr >=", value, "specialAttr");
            return (Criteria) this;
        }

        public Criteria andSpecialAttrLessThan(String value) {
            addCriterion("special_attr <", value, "specialAttr");
            return (Criteria) this;
        }

        public Criteria andSpecialAttrLessThanOrEqualTo(String value) {
            addCriterion("special_attr <=", value, "specialAttr");
            return (Criteria) this;
        }

        public Criteria andSpecialAttrLike(String value) {
            addCriterion("special_attr like", value, "specialAttr");
            return (Criteria) this;
        }

        public Criteria andSpecialAttrNotLike(String value) {
            addCriterion("special_attr not like", value, "specialAttr");
            return (Criteria) this;
        }

        public Criteria andSpecialAttrIn(List<String> values) {
            addCriterion("special_attr in", values, "specialAttr");
            return (Criteria) this;
        }

        public Criteria andSpecialAttrNotIn(List<String> values) {
            addCriterion("special_attr not in", values, "specialAttr");
            return (Criteria) this;
        }

        public Criteria andSpecialAttrBetween(String value1, String value2) {
            addCriterion("special_attr between", value1, value2, "specialAttr");
            return (Criteria) this;
        }

        public Criteria andSpecialAttrNotBetween(String value1, String value2) {
            addCriterion("special_attr not between", value1, value2, "specialAttr");
            return (Criteria) this;
        }

        public Criteria andPurchaseTypeIsNull() {
            addCriterion("purchase_type is null");
            return (Criteria) this;
        }

        public Criteria andPurchaseTypeIsNotNull() {
            addCriterion("purchase_type is not null");
            return (Criteria) this;
        }

        public Criteria andPurchaseTypeEqualTo(String value) {
            addCriterion("purchase_type =", value, "purchaseType");
            return (Criteria) this;
        }

        public Criteria andPurchaseTypeNotEqualTo(String value) {
            addCriterion("purchase_type <>", value, "purchaseType");
            return (Criteria) this;
        }

        public Criteria andPurchaseTypeGreaterThan(String value) {
            addCriterion("purchase_type >", value, "purchaseType");
            return (Criteria) this;
        }

        public Criteria andPurchaseTypeGreaterThanOrEqualTo(String value) {
            addCriterion("purchase_type >=", value, "purchaseType");
            return (Criteria) this;
        }

        public Criteria andPurchaseTypeLessThan(String value) {
            addCriterion("purchase_type <", value, "purchaseType");
            return (Criteria) this;
        }

        public Criteria andPurchaseTypeLessThanOrEqualTo(String value) {
            addCriterion("purchase_type <=", value, "purchaseType");
            return (Criteria) this;
        }

        public Criteria andPurchaseTypeLike(String value) {
            addCriterion("purchase_type like", value, "purchaseType");
            return (Criteria) this;
        }

        public Criteria andPurchaseTypeNotLike(String value) {
            addCriterion("purchase_type not like", value, "purchaseType");
            return (Criteria) this;
        }

        public Criteria andPurchaseTypeIn(List<String> values) {
            addCriterion("purchase_type in", values, "purchaseType");
            return (Criteria) this;
        }

        public Criteria andPurchaseTypeNotIn(List<String> values) {
            addCriterion("purchase_type not in", values, "purchaseType");
            return (Criteria) this;
        }

        public Criteria andPurchaseTypeBetween(String value1, String value2) {
            addCriterion("purchase_type between", value1, value2, "purchaseType");
            return (Criteria) this;
        }

        public Criteria andPurchaseTypeNotBetween(String value1, String value2) {
            addCriterion("purchase_type not between", value1, value2, "purchaseType");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelIsNull() {
            addCriterion("goods_level is null");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelIsNotNull() {
            addCriterion("goods_level is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelEqualTo(Byte value) {
            addCriterion("goods_level =", value, "goodsLevel");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelNotEqualTo(Byte value) {
            addCriterion("goods_level <>", value, "goodsLevel");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelGreaterThan(Byte value) {
            addCriterion("goods_level >", value, "goodsLevel");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelGreaterThanOrEqualTo(Byte value) {
            addCriterion("goods_level >=", value, "goodsLevel");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelLessThan(Byte value) {
            addCriterion("goods_level <", value, "goodsLevel");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelLessThanOrEqualTo(Byte value) {
            addCriterion("goods_level <=", value, "goodsLevel");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelIn(List<Byte> values) {
            addCriterion("goods_level in", values, "goodsLevel");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelNotIn(List<Byte> values) {
            addCriterion("goods_level not in", values, "goodsLevel");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelBetween(Byte value1, Byte value2) {
            addCriterion("goods_level between", value1, value2, "goodsLevel");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelNotBetween(Byte value1, Byte value2) {
            addCriterion("goods_level not between", value1, value2, "goodsLevel");
            return (Criteria) this;
        }

        public Criteria andGoodsStatusIsNull() {
            addCriterion("goods_status is null");
            return (Criteria) this;
        }

        public Criteria andGoodsStatusIsNotNull() {
            addCriterion("goods_status is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsStatusEqualTo(Byte value) {
            addCriterion("goods_status =", value, "goodsStatus");
            return (Criteria) this;
        }

        public Criteria andGoodsStatusNotEqualTo(Byte value) {
            addCriterion("goods_status <>", value, "goodsStatus");
            return (Criteria) this;
        }

        public Criteria andGoodsStatusGreaterThan(Byte value) {
            addCriterion("goods_status >", value, "goodsStatus");
            return (Criteria) this;
        }

        public Criteria andGoodsStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("goods_status >=", value, "goodsStatus");
            return (Criteria) this;
        }

        public Criteria andGoodsStatusLessThan(Byte value) {
            addCriterion("goods_status <", value, "goodsStatus");
            return (Criteria) this;
        }

        public Criteria andGoodsStatusLessThanOrEqualTo(Byte value) {
            addCriterion("goods_status <=", value, "goodsStatus");
            return (Criteria) this;
        }

        public Criteria andGoodsStatusIn(List<Byte> values) {
            addCriterion("goods_status in", values, "goodsStatus");
            return (Criteria) this;
        }

        public Criteria andGoodsStatusNotIn(List<Byte> values) {
            addCriterion("goods_status not in", values, "goodsStatus");
            return (Criteria) this;
        }

        public Criteria andGoodsStatusBetween(Byte value1, Byte value2) {
            addCriterion("goods_status between", value1, value2, "goodsStatus");
            return (Criteria) this;
        }

        public Criteria andGoodsStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("goods_status not between", value1, value2, "goodsStatus");
            return (Criteria) this;
        }

        public Criteria andLawfulIsNull() {
            addCriterion("lawful is null");
            return (Criteria) this;
        }

        public Criteria andLawfulIsNotNull() {
            addCriterion("lawful is not null");
            return (Criteria) this;
        }

        public Criteria andLawfulEqualTo(Byte value) {
            addCriterion("lawful =", value, "lawful");
            return (Criteria) this;
        }

        public Criteria andLawfulNotEqualTo(Byte value) {
            addCriterion("lawful <>", value, "lawful");
            return (Criteria) this;
        }

        public Criteria andLawfulGreaterThan(Byte value) {
            addCriterion("lawful >", value, "lawful");
            return (Criteria) this;
        }

        public Criteria andLawfulGreaterThanOrEqualTo(Byte value) {
            addCriterion("lawful >=", value, "lawful");
            return (Criteria) this;
        }

        public Criteria andLawfulLessThan(Byte value) {
            addCriterion("lawful <", value, "lawful");
            return (Criteria) this;
        }

        public Criteria andLawfulLessThanOrEqualTo(Byte value) {
            addCriterion("lawful <=", value, "lawful");
            return (Criteria) this;
        }

        public Criteria andLawfulIn(List<Byte> values) {
            addCriterion("lawful in", values, "lawful");
            return (Criteria) this;
        }

        public Criteria andLawfulNotIn(List<Byte> values) {
            addCriterion("lawful not in", values, "lawful");
            return (Criteria) this;
        }

        public Criteria andLawfulBetween(Byte value1, Byte value2) {
            addCriterion("lawful between", value1, value2, "lawful");
            return (Criteria) this;
        }

        public Criteria andLawfulNotBetween(Byte value1, Byte value2) {
            addCriterion("lawful not between", value1, value2, "lawful");
            return (Criteria) this;
        }

        public Criteria andNewableIsNull() {
            addCriterion("newable is null");
            return (Criteria) this;
        }

        public Criteria andNewableIsNotNull() {
            addCriterion("newable is not null");
            return (Criteria) this;
        }

        public Criteria andNewableEqualTo(Byte value) {
            addCriterion("newable =", value, "newable");
            return (Criteria) this;
        }

        public Criteria andNewableNotEqualTo(Byte value) {
            addCriterion("newable <>", value, "newable");
            return (Criteria) this;
        }

        public Criteria andNewableGreaterThan(Byte value) {
            addCriterion("newable >", value, "newable");
            return (Criteria) this;
        }

        public Criteria andNewableGreaterThanOrEqualTo(Byte value) {
            addCriterion("newable >=", value, "newable");
            return (Criteria) this;
        }

        public Criteria andNewableLessThan(Byte value) {
            addCriterion("newable <", value, "newable");
            return (Criteria) this;
        }

        public Criteria andNewableLessThanOrEqualTo(Byte value) {
            addCriterion("newable <=", value, "newable");
            return (Criteria) this;
        }

        public Criteria andNewableIn(List<Byte> values) {
            addCriterion("newable in", values, "newable");
            return (Criteria) this;
        }

        public Criteria andNewableNotIn(List<Byte> values) {
            addCriterion("newable not in", values, "newable");
            return (Criteria) this;
        }

        public Criteria andNewableBetween(Byte value1, Byte value2) {
            addCriterion("newable between", value1, value2, "newable");
            return (Criteria) this;
        }

        public Criteria andNewableNotBetween(Byte value1, Byte value2) {
            addCriterion("newable not between", value1, value2, "newable");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIsNull() {
            addCriterion("category_id is null");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIsNotNull() {
            addCriterion("category_id is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryIdEqualTo(Long value) {
            addCriterion("category_id =", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotEqualTo(Long value) {
            addCriterion("category_id <>", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdGreaterThan(Long value) {
            addCriterion("category_id >", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdGreaterThanOrEqualTo(Long value) {
            addCriterion("category_id >=", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdLessThan(Long value) {
            addCriterion("category_id <", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdLessThanOrEqualTo(Long value) {
            addCriterion("category_id <=", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIn(List<Long> values) {
            addCriterion("category_id in", values, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotIn(List<Long> values) {
            addCriterion("category_id not in", values, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdBetween(Long value1, Long value2) {
            addCriterion("category_id between", value1, value2, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotBetween(Long value1, Long value2) {
            addCriterion("category_id not between", value1, value2, "categoryId");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryIdIsNull() {
            addCriterion("middle_category_id is null");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryIdIsNotNull() {
            addCriterion("middle_category_id is not null");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryIdEqualTo(Long value) {
            addCriterion("middle_category_id =", value, "middleCategoryId");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryIdNotEqualTo(Long value) {
            addCriterion("middle_category_id <>", value, "middleCategoryId");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryIdGreaterThan(Long value) {
            addCriterion("middle_category_id >", value, "middleCategoryId");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryIdGreaterThanOrEqualTo(Long value) {
            addCriterion("middle_category_id >=", value, "middleCategoryId");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryIdLessThan(Long value) {
            addCriterion("middle_category_id <", value, "middleCategoryId");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryIdLessThanOrEqualTo(Long value) {
            addCriterion("middle_category_id <=", value, "middleCategoryId");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryIdIn(List<Long> values) {
            addCriterion("middle_category_id in", values, "middleCategoryId");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryIdNotIn(List<Long> values) {
            addCriterion("middle_category_id not in", values, "middleCategoryId");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryIdBetween(Long value1, Long value2) {
            addCriterion("middle_category_id between", value1, value2, "middleCategoryId");
            return (Criteria) this;
        }

        public Criteria andMiddleCategoryIdNotBetween(Long value1, Long value2) {
            addCriterion("middle_category_id not between", value1, value2, "middleCategoryId");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryIdIsNull() {
            addCriterion("small_category_id is null");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryIdIsNotNull() {
            addCriterion("small_category_id is not null");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryIdEqualTo(Long value) {
            addCriterion("small_category_id =", value, "smallCategoryId");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryIdNotEqualTo(Long value) {
            addCriterion("small_category_id <>", value, "smallCategoryId");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryIdGreaterThan(Long value) {
            addCriterion("small_category_id >", value, "smallCategoryId");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryIdGreaterThanOrEqualTo(Long value) {
            addCriterion("small_category_id >=", value, "smallCategoryId");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryIdLessThan(Long value) {
            addCriterion("small_category_id <", value, "smallCategoryId");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryIdLessThanOrEqualTo(Long value) {
            addCriterion("small_category_id <=", value, "smallCategoryId");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryIdIn(List<Long> values) {
            addCriterion("small_category_id in", values, "smallCategoryId");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryIdNotIn(List<Long> values) {
            addCriterion("small_category_id not in", values, "smallCategoryId");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryIdBetween(Long value1, Long value2) {
            addCriterion("small_category_id between", value1, value2, "smallCategoryId");
            return (Criteria) this;
        }

        public Criteria andSmallCategoryIdNotBetween(Long value1, Long value2) {
            addCriterion("small_category_id not between", value1, value2, "smallCategoryId");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdIsNull() {
            addCriterion("sub_category_id is null");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdIsNotNull() {
            addCriterion("sub_category_id is not null");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdEqualTo(Long value) {
            addCriterion("sub_category_id =", value, "subCategoryId");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdNotEqualTo(Long value) {
            addCriterion("sub_category_id <>", value, "subCategoryId");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdGreaterThan(Long value) {
            addCriterion("sub_category_id >", value, "subCategoryId");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdGreaterThanOrEqualTo(Long value) {
            addCriterion("sub_category_id >=", value, "subCategoryId");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdLessThan(Long value) {
            addCriterion("sub_category_id <", value, "subCategoryId");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdLessThanOrEqualTo(Long value) {
            addCriterion("sub_category_id <=", value, "subCategoryId");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdIn(List<Long> values) {
            addCriterion("sub_category_id in", values, "subCategoryId");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdNotIn(List<Long> values) {
            addCriterion("sub_category_id not in", values, "subCategoryId");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdBetween(Long value1, Long value2) {
            addCriterion("sub_category_id between", value1, value2, "subCategoryId");
            return (Criteria) this;
        }

        public Criteria andSubCategoryIdNotBetween(Long value1, Long value2) {
            addCriterion("sub_category_id not between", value1, value2, "subCategoryId");
            return (Criteria) this;
        }

        public Criteria andPushLevelIsNull() {
            addCriterion("push_level is null");
            return (Criteria) this;
        }

        public Criteria andPushLevelIsNotNull() {
            addCriterion("push_level is not null");
            return (Criteria) this;
        }

        public Criteria andPushLevelEqualTo(String value) {
            addCriterion("push_level =", value, "pushLevel");
            return (Criteria) this;
        }

        public Criteria andPushLevelNotEqualTo(String value) {
            addCriterion("push_level <>", value, "pushLevel");
            return (Criteria) this;
        }

        public Criteria andPushLevelGreaterThan(String value) {
            addCriterion("push_level >", value, "pushLevel");
            return (Criteria) this;
        }

        public Criteria andPushLevelGreaterThanOrEqualTo(String value) {
            addCriterion("push_level >=", value, "pushLevel");
            return (Criteria) this;
        }

        public Criteria andPushLevelLessThan(String value) {
            addCriterion("push_level <", value, "pushLevel");
            return (Criteria) this;
        }

        public Criteria andPushLevelLessThanOrEqualTo(String value) {
            addCriterion("push_level <=", value, "pushLevel");
            return (Criteria) this;
        }

        public Criteria andPushLevelLike(String value) {
            addCriterion("push_level like", value, "pushLevel");
            return (Criteria) this;
        }

        public Criteria andPushLevelNotLike(String value) {
            addCriterion("push_level not like", value, "pushLevel");
            return (Criteria) this;
        }

        public Criteria andPushLevelIn(List<String> values) {
            addCriterion("push_level in", values, "pushLevel");
            return (Criteria) this;
        }

        public Criteria andPushLevelNotIn(List<String> values) {
            addCriterion("push_level not in", values, "pushLevel");
            return (Criteria) this;
        }

        public Criteria andPushLevelBetween(String value1, String value2) {
            addCriterion("push_level between", value1, value2, "pushLevel");
            return (Criteria) this;
        }

        public Criteria andPushLevelNotBetween(String value1, String value2) {
            addCriterion("push_level not between", value1, value2, "pushLevel");
            return (Criteria) this;
        }

        public Criteria andGoodsLineIsNull() {
            addCriterion("goods_line is null");
            return (Criteria) this;
        }

        public Criteria andGoodsLineIsNotNull() {
            addCriterion("goods_line is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsLineEqualTo(Byte value) {
            addCriterion("goods_line =", value, "goodsLine");
            return (Criteria) this;
        }

        public Criteria andGoodsLineNotEqualTo(Byte value) {
            addCriterion("goods_line <>", value, "goodsLine");
            return (Criteria) this;
        }

        public Criteria andGoodsLineGreaterThan(Byte value) {
            addCriterion("goods_line >", value, "goodsLine");
            return (Criteria) this;
        }

        public Criteria andGoodsLineGreaterThanOrEqualTo(Byte value) {
            addCriterion("goods_line >=", value, "goodsLine");
            return (Criteria) this;
        }

        public Criteria andGoodsLineLessThan(Byte value) {
            addCriterion("goods_line <", value, "goodsLine");
            return (Criteria) this;
        }

        public Criteria andGoodsLineLessThanOrEqualTo(Byte value) {
            addCriterion("goods_line <=", value, "goodsLine");
            return (Criteria) this;
        }

        public Criteria andGoodsLineIn(List<Byte> values) {
            addCriterion("goods_line in", values, "goodsLine");
            return (Criteria) this;
        }

        public Criteria andGoodsLineNotIn(List<Byte> values) {
            addCriterion("goods_line not in", values, "goodsLine");
            return (Criteria) this;
        }

        public Criteria andGoodsLineBetween(Byte value1, Byte value2) {
            addCriterion("goods_line between", value1, value2, "goodsLine");
            return (Criteria) this;
        }

        public Criteria andGoodsLineNotBetween(Byte value1, Byte value2) {
            addCriterion("goods_line not between", value1, value2, "goodsLine");
            return (Criteria) this;
        }

        public Criteria andMiddlePackageQtyIsNull() {
            addCriterion("middle_package_qty is null");
            return (Criteria) this;
        }

        public Criteria andMiddlePackageQtyIsNotNull() {
            addCriterion("middle_package_qty is not null");
            return (Criteria) this;
        }

        public Criteria andMiddlePackageQtyEqualTo(BigDecimal value) {
            addCriterion("middle_package_qty =", value, "middlePackageQty");
            return (Criteria) this;
        }

        public Criteria andMiddlePackageQtyNotEqualTo(BigDecimal value) {
            addCriterion("middle_package_qty <>", value, "middlePackageQty");
            return (Criteria) this;
        }

        public Criteria andMiddlePackageQtyGreaterThan(BigDecimal value) {
            addCriterion("middle_package_qty >", value, "middlePackageQty");
            return (Criteria) this;
        }

        public Criteria andMiddlePackageQtyGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("middle_package_qty >=", value, "middlePackageQty");
            return (Criteria) this;
        }

        public Criteria andMiddlePackageQtyLessThan(BigDecimal value) {
            addCriterion("middle_package_qty <", value, "middlePackageQty");
            return (Criteria) this;
        }

        public Criteria andMiddlePackageQtyLessThanOrEqualTo(BigDecimal value) {
            addCriterion("middle_package_qty <=", value, "middlePackageQty");
            return (Criteria) this;
        }

        public Criteria andMiddlePackageQtyIn(List<BigDecimal> values) {
            addCriterion("middle_package_qty in", values, "middlePackageQty");
            return (Criteria) this;
        }

        public Criteria andMiddlePackageQtyNotIn(List<BigDecimal> values) {
            addCriterion("middle_package_qty not in", values, "middlePackageQty");
            return (Criteria) this;
        }

        public Criteria andMiddlePackageQtyBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("middle_package_qty between", value1, value2, "middlePackageQty");
            return (Criteria) this;
        }

        public Criteria andMiddlePackageQtyNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("middle_package_qty not between", value1, value2, "middlePackageQty");
            return (Criteria) this;
        }

        public Criteria andMiddlePackageSwitchBizIsNull() {
            addCriterion("middle_package_switch_biz is null");
            return (Criteria) this;
        }

        public Criteria andMiddlePackageSwitchBizIsNotNull() {
            addCriterion("middle_package_switch_biz is not null");
            return (Criteria) this;
        }

        public Criteria andMiddlePackageSwitchBizEqualTo(String value) {
            addCriterion("middle_package_switch_biz =", value, "middlePackageSwitchBiz");
            return (Criteria) this;
        }

        public Criteria andMiddlePackageSwitchBizNotEqualTo(String value) {
            addCriterion("middle_package_switch_biz <>", value, "middlePackageSwitchBiz");
            return (Criteria) this;
        }

        public Criteria andMiddlePackageSwitchBizGreaterThan(String value) {
            addCriterion("middle_package_switch_biz >", value, "middlePackageSwitchBiz");
            return (Criteria) this;
        }

        public Criteria andMiddlePackageSwitchBizGreaterThanOrEqualTo(String value) {
            addCriterion("middle_package_switch_biz >=", value, "middlePackageSwitchBiz");
            return (Criteria) this;
        }

        public Criteria andMiddlePackageSwitchBizLessThan(String value) {
            addCriterion("middle_package_switch_biz <", value, "middlePackageSwitchBiz");
            return (Criteria) this;
        }

        public Criteria andMiddlePackageSwitchBizLessThanOrEqualTo(String value) {
            addCriterion("middle_package_switch_biz <=", value, "middlePackageSwitchBiz");
            return (Criteria) this;
        }

        public Criteria andMiddlePackageSwitchBizLike(String value) {
            addCriterion("middle_package_switch_biz like", value, "middlePackageSwitchBiz");
            return (Criteria) this;
        }

        public Criteria andMiddlePackageSwitchBizNotLike(String value) {
            addCriterion("middle_package_switch_biz not like", value, "middlePackageSwitchBiz");
            return (Criteria) this;
        }

        public Criteria andMiddlePackageSwitchBizIn(List<String> values) {
            addCriterion("middle_package_switch_biz in", values, "middlePackageSwitchBiz");
            return (Criteria) this;
        }

        public Criteria andMiddlePackageSwitchBizNotIn(List<String> values) {
            addCriterion("middle_package_switch_biz not in", values, "middlePackageSwitchBiz");
            return (Criteria) this;
        }

        public Criteria andMiddlePackageSwitchBizBetween(String value1, String value2) {
            addCriterion("middle_package_switch_biz between", value1, value2, "middlePackageSwitchBiz");
            return (Criteria) this;
        }

        public Criteria andMiddlePackageSwitchBizNotBetween(String value1, String value2) {
            addCriterion("middle_package_switch_biz not between", value1, value2, "middlePackageSwitchBiz");
            return (Criteria) this;
        }

        public Criteria andMiddlePackageSwitchStoreIsNull() {
            addCriterion("middle_package_switch_store is null");
            return (Criteria) this;
        }

        public Criteria andMiddlePackageSwitchStoreIsNotNull() {
            addCriterion("middle_package_switch_store is not null");
            return (Criteria) this;
        }

        public Criteria andMiddlePackageSwitchStoreEqualTo(String value) {
            addCriterion("middle_package_switch_store =", value, "middlePackageSwitchStore");
            return (Criteria) this;
        }

        public Criteria andMiddlePackageSwitchStoreNotEqualTo(String value) {
            addCriterion("middle_package_switch_store <>", value, "middlePackageSwitchStore");
            return (Criteria) this;
        }

        public Criteria andMiddlePackageSwitchStoreGreaterThan(String value) {
            addCriterion("middle_package_switch_store >", value, "middlePackageSwitchStore");
            return (Criteria) this;
        }

        public Criteria andMiddlePackageSwitchStoreGreaterThanOrEqualTo(String value) {
            addCriterion("middle_package_switch_store >=", value, "middlePackageSwitchStore");
            return (Criteria) this;
        }

        public Criteria andMiddlePackageSwitchStoreLessThan(String value) {
            addCriterion("middle_package_switch_store <", value, "middlePackageSwitchStore");
            return (Criteria) this;
        }

        public Criteria andMiddlePackageSwitchStoreLessThanOrEqualTo(String value) {
            addCriterion("middle_package_switch_store <=", value, "middlePackageSwitchStore");
            return (Criteria) this;
        }

        public Criteria andMiddlePackageSwitchStoreLike(String value) {
            addCriterion("middle_package_switch_store like", value, "middlePackageSwitchStore");
            return (Criteria) this;
        }

        public Criteria andMiddlePackageSwitchStoreNotLike(String value) {
            addCriterion("middle_package_switch_store not like", value, "middlePackageSwitchStore");
            return (Criteria) this;
        }

        public Criteria andMiddlePackageSwitchStoreIn(List<String> values) {
            addCriterion("middle_package_switch_store in", values, "middlePackageSwitchStore");
            return (Criteria) this;
        }

        public Criteria andMiddlePackageSwitchStoreNotIn(List<String> values) {
            addCriterion("middle_package_switch_store not in", values, "middlePackageSwitchStore");
            return (Criteria) this;
        }

        public Criteria andMiddlePackageSwitchStoreBetween(String value1, String value2) {
            addCriterion("middle_package_switch_store between", value1, value2, "middlePackageSwitchStore");
            return (Criteria) this;
        }

        public Criteria andMiddlePackageSwitchStoreNotBetween(String value1, String value2) {
            addCriterion("middle_package_switch_store not between", value1, value2, "middlePackageSwitchStore");
            return (Criteria) this;
        }

        public Criteria andSpecialThirtyDaysQtyIsNull() {
            addCriterion("special_thirty_days_qty is null");
            return (Criteria) this;
        }

        public Criteria andSpecialThirtyDaysQtyIsNotNull() {
            addCriterion("special_thirty_days_qty is not null");
            return (Criteria) this;
        }

        public Criteria andSpecialThirtyDaysQtyEqualTo(BigDecimal value) {
            addCriterion("special_thirty_days_qty =", value, "specialThirtyDaysQty");
            return (Criteria) this;
        }

        public Criteria andSpecialThirtyDaysQtyNotEqualTo(BigDecimal value) {
            addCriterion("special_thirty_days_qty <>", value, "specialThirtyDaysQty");
            return (Criteria) this;
        }

        public Criteria andSpecialThirtyDaysQtyGreaterThan(BigDecimal value) {
            addCriterion("special_thirty_days_qty >", value, "specialThirtyDaysQty");
            return (Criteria) this;
        }

        public Criteria andSpecialThirtyDaysQtyGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("special_thirty_days_qty >=", value, "specialThirtyDaysQty");
            return (Criteria) this;
        }

        public Criteria andSpecialThirtyDaysQtyLessThan(BigDecimal value) {
            addCriterion("special_thirty_days_qty <", value, "specialThirtyDaysQty");
            return (Criteria) this;
        }

        public Criteria andSpecialThirtyDaysQtyLessThanOrEqualTo(BigDecimal value) {
            addCriterion("special_thirty_days_qty <=", value, "specialThirtyDaysQty");
            return (Criteria) this;
        }

        public Criteria andSpecialThirtyDaysQtyIn(List<BigDecimal> values) {
            addCriterion("special_thirty_days_qty in", values, "specialThirtyDaysQty");
            return (Criteria) this;
        }

        public Criteria andSpecialThirtyDaysQtyNotIn(List<BigDecimal> values) {
            addCriterion("special_thirty_days_qty not in", values, "specialThirtyDaysQty");
            return (Criteria) this;
        }

        public Criteria andSpecialThirtyDaysQtyBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("special_thirty_days_qty between", value1, value2, "specialThirtyDaysQty");
            return (Criteria) this;
        }

        public Criteria andSpecialThirtyDaysQtyNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("special_thirty_days_qty not between", value1, value2, "specialThirtyDaysQty");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitDaysIsNull() {
            addCriterion("stock_upper_limit_days is null");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitDaysIsNotNull() {
            addCriterion("stock_upper_limit_days is not null");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitDaysEqualTo(Integer value) {
            addCriterion("stock_upper_limit_days =", value, "stockUpperLimitDays");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitDaysNotEqualTo(Integer value) {
            addCriterion("stock_upper_limit_days <>", value, "stockUpperLimitDays");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitDaysGreaterThan(Integer value) {
            addCriterion("stock_upper_limit_days >", value, "stockUpperLimitDays");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitDaysGreaterThanOrEqualTo(Integer value) {
            addCriterion("stock_upper_limit_days >=", value, "stockUpperLimitDays");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitDaysLessThan(Integer value) {
            addCriterion("stock_upper_limit_days <", value, "stockUpperLimitDays");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitDaysLessThanOrEqualTo(Integer value) {
            addCriterion("stock_upper_limit_days <=", value, "stockUpperLimitDays");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitDaysIn(List<Integer> values) {
            addCriterion("stock_upper_limit_days in", values, "stockUpperLimitDays");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitDaysNotIn(List<Integer> values) {
            addCriterion("stock_upper_limit_days not in", values, "stockUpperLimitDays");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitDaysBetween(Integer value1, Integer value2) {
            addCriterion("stock_upper_limit_days between", value1, value2, "stockUpperLimitDays");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitDaysNotBetween(Integer value1, Integer value2) {
            addCriterion("stock_upper_limit_days not between", value1, value2, "stockUpperLimitDays");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitDaysIsNull() {
            addCriterion("stock_lower_limit_days is null");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitDaysIsNotNull() {
            addCriterion("stock_lower_limit_days is not null");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitDaysEqualTo(Integer value) {
            addCriterion("stock_lower_limit_days =", value, "stockLowerLimitDays");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitDaysNotEqualTo(Integer value) {
            addCriterion("stock_lower_limit_days <>", value, "stockLowerLimitDays");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitDaysGreaterThan(Integer value) {
            addCriterion("stock_lower_limit_days >", value, "stockLowerLimitDays");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitDaysGreaterThanOrEqualTo(Integer value) {
            addCriterion("stock_lower_limit_days >=", value, "stockLowerLimitDays");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitDaysLessThan(Integer value) {
            addCriterion("stock_lower_limit_days <", value, "stockLowerLimitDays");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitDaysLessThanOrEqualTo(Integer value) {
            addCriterion("stock_lower_limit_days <=", value, "stockLowerLimitDays");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitDaysIn(List<Integer> values) {
            addCriterion("stock_lower_limit_days in", values, "stockLowerLimitDays");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitDaysNotIn(List<Integer> values) {
            addCriterion("stock_lower_limit_days not in", values, "stockLowerLimitDays");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitDaysBetween(Integer value1, Integer value2) {
            addCriterion("stock_lower_limit_days between", value1, value2, "stockLowerLimitDays");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitDaysNotBetween(Integer value1, Integer value2) {
            addCriterion("stock_lower_limit_days not between", value1, value2, "stockLowerLimitDays");
            return (Criteria) this;
        }

        public Criteria andAverageDailySalesIsNull() {
            addCriterion("average_daily_sales is null");
            return (Criteria) this;
        }

        public Criteria andAverageDailySalesIsNotNull() {
            addCriterion("average_daily_sales is not null");
            return (Criteria) this;
        }

        public Criteria andAverageDailySalesEqualTo(BigDecimal value) {
            addCriterion("average_daily_sales =", value, "averageDailySales");
            return (Criteria) this;
        }

        public Criteria andAverageDailySalesNotEqualTo(BigDecimal value) {
            addCriterion("average_daily_sales <>", value, "averageDailySales");
            return (Criteria) this;
        }

        public Criteria andAverageDailySalesGreaterThan(BigDecimal value) {
            addCriterion("average_daily_sales >", value, "averageDailySales");
            return (Criteria) this;
        }

        public Criteria andAverageDailySalesGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("average_daily_sales >=", value, "averageDailySales");
            return (Criteria) this;
        }

        public Criteria andAverageDailySalesLessThan(BigDecimal value) {
            addCriterion("average_daily_sales <", value, "averageDailySales");
            return (Criteria) this;
        }

        public Criteria andAverageDailySalesLessThanOrEqualTo(BigDecimal value) {
            addCriterion("average_daily_sales <=", value, "averageDailySales");
            return (Criteria) this;
        }

        public Criteria andAverageDailySalesIn(List<BigDecimal> values) {
            addCriterion("average_daily_sales in", values, "averageDailySales");
            return (Criteria) this;
        }

        public Criteria andAverageDailySalesNotIn(List<BigDecimal> values) {
            addCriterion("average_daily_sales not in", values, "averageDailySales");
            return (Criteria) this;
        }

        public Criteria andAverageDailySalesBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("average_daily_sales between", value1, value2, "averageDailySales");
            return (Criteria) this;
        }

        public Criteria andAverageDailySalesNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("average_daily_sales not between", value1, value2, "averageDailySales");
            return (Criteria) this;
        }

        public Criteria andMinDisplayQtyIsNull() {
            addCriterion("min_display_qty is null");
            return (Criteria) this;
        }

        public Criteria andMinDisplayQtyIsNotNull() {
            addCriterion("min_display_qty is not null");
            return (Criteria) this;
        }

        public Criteria andMinDisplayQtyEqualTo(BigDecimal value) {
            addCriterion("min_display_qty =", value, "minDisplayQty");
            return (Criteria) this;
        }

        public Criteria andMinDisplayQtyNotEqualTo(BigDecimal value) {
            addCriterion("min_display_qty <>", value, "minDisplayQty");
            return (Criteria) this;
        }

        public Criteria andMinDisplayQtyGreaterThan(BigDecimal value) {
            addCriterion("min_display_qty >", value, "minDisplayQty");
            return (Criteria) this;
        }

        public Criteria andMinDisplayQtyGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("min_display_qty >=", value, "minDisplayQty");
            return (Criteria) this;
        }

        public Criteria andMinDisplayQtyLessThan(BigDecimal value) {
            addCriterion("min_display_qty <", value, "minDisplayQty");
            return (Criteria) this;
        }

        public Criteria andMinDisplayQtyLessThanOrEqualTo(BigDecimal value) {
            addCriterion("min_display_qty <=", value, "minDisplayQty");
            return (Criteria) this;
        }

        public Criteria andMinDisplayQtyIn(List<BigDecimal> values) {
            addCriterion("min_display_qty in", values, "minDisplayQty");
            return (Criteria) this;
        }

        public Criteria andMinDisplayQtyNotIn(List<BigDecimal> values) {
            addCriterion("min_display_qty not in", values, "minDisplayQty");
            return (Criteria) this;
        }

        public Criteria andMinDisplayQtyBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("min_display_qty between", value1, value2, "minDisplayQty");
            return (Criteria) this;
        }

        public Criteria andMinDisplayQtyNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("min_display_qty not between", value1, value2, "minDisplayQty");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitIsNull() {
            addCriterion("stock_upper_limit is null");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitIsNotNull() {
            addCriterion("stock_upper_limit is not null");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitEqualTo(BigDecimal value) {
            addCriterion("stock_upper_limit =", value, "stockUpperLimit");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitNotEqualTo(BigDecimal value) {
            addCriterion("stock_upper_limit <>", value, "stockUpperLimit");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitGreaterThan(BigDecimal value) {
            addCriterion("stock_upper_limit >", value, "stockUpperLimit");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("stock_upper_limit >=", value, "stockUpperLimit");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitLessThan(BigDecimal value) {
            addCriterion("stock_upper_limit <", value, "stockUpperLimit");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitLessThanOrEqualTo(BigDecimal value) {
            addCriterion("stock_upper_limit <=", value, "stockUpperLimit");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitIn(List<BigDecimal> values) {
            addCriterion("stock_upper_limit in", values, "stockUpperLimit");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitNotIn(List<BigDecimal> values) {
            addCriterion("stock_upper_limit not in", values, "stockUpperLimit");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("stock_upper_limit between", value1, value2, "stockUpperLimit");
            return (Criteria) this;
        }

        public Criteria andStockUpperLimitNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("stock_upper_limit not between", value1, value2, "stockUpperLimit");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitIsNull() {
            addCriterion("stock_lower_limit is null");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitIsNotNull() {
            addCriterion("stock_lower_limit is not null");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitEqualTo(BigDecimal value) {
            addCriterion("stock_lower_limit =", value, "stockLowerLimit");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitNotEqualTo(BigDecimal value) {
            addCriterion("stock_lower_limit <>", value, "stockLowerLimit");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitGreaterThan(BigDecimal value) {
            addCriterion("stock_lower_limit >", value, "stockLowerLimit");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("stock_lower_limit >=", value, "stockLowerLimit");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitLessThan(BigDecimal value) {
            addCriterion("stock_lower_limit <", value, "stockLowerLimit");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitLessThanOrEqualTo(BigDecimal value) {
            addCriterion("stock_lower_limit <=", value, "stockLowerLimit");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitIn(List<BigDecimal> values) {
            addCriterion("stock_lower_limit in", values, "stockLowerLimit");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitNotIn(List<BigDecimal> values) {
            addCriterion("stock_lower_limit not in", values, "stockLowerLimit");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("stock_lower_limit between", value1, value2, "stockLowerLimit");
            return (Criteria) this;
        }

        public Criteria andStockLowerLimitNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("stock_lower_limit not between", value1, value2, "stockLowerLimit");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysSalesIsNull() {
            addCriterion("thirty_days_sales is null");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysSalesIsNotNull() {
            addCriterion("thirty_days_sales is not null");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysSalesEqualTo(BigDecimal value) {
            addCriterion("thirty_days_sales =", value, "thirtyDaysSales");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysSalesNotEqualTo(BigDecimal value) {
            addCriterion("thirty_days_sales <>", value, "thirtyDaysSales");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysSalesGreaterThan(BigDecimal value) {
            addCriterion("thirty_days_sales >", value, "thirtyDaysSales");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysSalesGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("thirty_days_sales >=", value, "thirtyDaysSales");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysSalesLessThan(BigDecimal value) {
            addCriterion("thirty_days_sales <", value, "thirtyDaysSales");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysSalesLessThanOrEqualTo(BigDecimal value) {
            addCriterion("thirty_days_sales <=", value, "thirtyDaysSales");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysSalesIn(List<BigDecimal> values) {
            addCriterion("thirty_days_sales in", values, "thirtyDaysSales");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysSalesNotIn(List<BigDecimal> values) {
            addCriterion("thirty_days_sales not in", values, "thirtyDaysSales");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysSalesBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("thirty_days_sales between", value1, value2, "thirtyDaysSales");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysSalesNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("thirty_days_sales not between", value1, value2, "thirtyDaysSales");
            return (Criteria) this;
        }

        public Criteria andSixtyDaysSalesIsNull() {
            addCriterion("sixty_days_sales is null");
            return (Criteria) this;
        }

        public Criteria andSixtyDaysSalesIsNotNull() {
            addCriterion("sixty_days_sales is not null");
            return (Criteria) this;
        }

        public Criteria andSixtyDaysSalesEqualTo(BigDecimal value) {
            addCriterion("sixty_days_sales =", value, "sixtyDaysSales");
            return (Criteria) this;
        }

        public Criteria andSixtyDaysSalesNotEqualTo(BigDecimal value) {
            addCriterion("sixty_days_sales <>", value, "sixtyDaysSales");
            return (Criteria) this;
        }

        public Criteria andSixtyDaysSalesGreaterThan(BigDecimal value) {
            addCriterion("sixty_days_sales >", value, "sixtyDaysSales");
            return (Criteria) this;
        }

        public Criteria andSixtyDaysSalesGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("sixty_days_sales >=", value, "sixtyDaysSales");
            return (Criteria) this;
        }

        public Criteria andSixtyDaysSalesLessThan(BigDecimal value) {
            addCriterion("sixty_days_sales <", value, "sixtyDaysSales");
            return (Criteria) this;
        }

        public Criteria andSixtyDaysSalesLessThanOrEqualTo(BigDecimal value) {
            addCriterion("sixty_days_sales <=", value, "sixtyDaysSales");
            return (Criteria) this;
        }

        public Criteria andSixtyDaysSalesIn(List<BigDecimal> values) {
            addCriterion("sixty_days_sales in", values, "sixtyDaysSales");
            return (Criteria) this;
        }

        public Criteria andSixtyDaysSalesNotIn(List<BigDecimal> values) {
            addCriterion("sixty_days_sales not in", values, "sixtyDaysSales");
            return (Criteria) this;
        }

        public Criteria andSixtyDaysSalesBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("sixty_days_sales between", value1, value2, "sixtyDaysSales");
            return (Criteria) this;
        }

        public Criteria andSixtyDaysSalesNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("sixty_days_sales not between", value1, value2, "sixtyDaysSales");
            return (Criteria) this;
        }

        public Criteria andNinetyDaysSalesIsNull() {
            addCriterion("ninety_days_sales is null");
            return (Criteria) this;
        }

        public Criteria andNinetyDaysSalesIsNotNull() {
            addCriterion("ninety_days_sales is not null");
            return (Criteria) this;
        }

        public Criteria andNinetyDaysSalesEqualTo(BigDecimal value) {
            addCriterion("ninety_days_sales =", value, "ninetyDaysSales");
            return (Criteria) this;
        }

        public Criteria andNinetyDaysSalesNotEqualTo(BigDecimal value) {
            addCriterion("ninety_days_sales <>", value, "ninetyDaysSales");
            return (Criteria) this;
        }

        public Criteria andNinetyDaysSalesGreaterThan(BigDecimal value) {
            addCriterion("ninety_days_sales >", value, "ninetyDaysSales");
            return (Criteria) this;
        }

        public Criteria andNinetyDaysSalesGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("ninety_days_sales >=", value, "ninetyDaysSales");
            return (Criteria) this;
        }

        public Criteria andNinetyDaysSalesLessThan(BigDecimal value) {
            addCriterion("ninety_days_sales <", value, "ninetyDaysSales");
            return (Criteria) this;
        }

        public Criteria andNinetyDaysSalesLessThanOrEqualTo(BigDecimal value) {
            addCriterion("ninety_days_sales <=", value, "ninetyDaysSales");
            return (Criteria) this;
        }

        public Criteria andNinetyDaysSalesIn(List<BigDecimal> values) {
            addCriterion("ninety_days_sales in", values, "ninetyDaysSales");
            return (Criteria) this;
        }

        public Criteria andNinetyDaysSalesNotIn(List<BigDecimal> values) {
            addCriterion("ninety_days_sales not in", values, "ninetyDaysSales");
            return (Criteria) this;
        }

        public Criteria andNinetyDaysSalesBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("ninety_days_sales between", value1, value2, "ninetyDaysSales");
            return (Criteria) this;
        }

        public Criteria andNinetyDaysSalesNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("ninety_days_sales not between", value1, value2, "ninetyDaysSales");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIsNull() {
            addCriterion("gmt_update is null");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIsNotNull() {
            addCriterion("gmt_update is not null");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateEqualTo(Date value) {
            addCriterion("gmt_update =", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotEqualTo(Date value) {
            addCriterion("gmt_update <>", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateGreaterThan(Date value) {
            addCriterion("gmt_update >", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_update >=", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateLessThan(Date value) {
            addCriterion("gmt_update <", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_update <=", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIn(List<Date> values) {
            addCriterion("gmt_update in", values, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotIn(List<Date> values) {
            addCriterion("gmt_update not in", values, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateBetween(Date value1, Date value2) {
            addCriterion("gmt_update between", value1, value2, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_update not between", value1, value2, "gmtUpdate");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}