package com.cowell.iscm.entityTidb;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> pos调拨完成跟踪销量表
 */
public class IscmSuggestDistexecDone implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 调拨日期
     */
    private Date allotDate;

    /**
     * 登记单号
     */
    private String registerNo;

    /**
     * pos调拨单号
     */
    private String posAllotNo;

    /**
     * 调出公司MDM编码
     */
    private String outCompanyCode;

    /**
     * 调出门店MDM编码
     */
    private String outStoreCode;

    /**
     * 调入公司MDM编码
     */
    private String inCompanyCode;

    /**
     * 调入门店MDM编码
     */
    private String inStoreCode;

    /**
     * 商品编码
     */
    private String goodsNo;

    /**
     * 实际调入数量
     */
    private BigDecimal inAllotQuantity;

    /**
     * 调入后7天销售数量
     */
    private BigDecimal inStoreSales7;

    /**
     * 调入后7天销售成本金额
     */
    private BigDecimal inStorePuramount7;

    /**
     * 调入后14天销售数量
     */
    private BigDecimal inStoreSales14;

    /**
     * 调入后14天销售成本金额
     */
    private BigDecimal inStorePuramount14;

    /**
     * 调入后30天销售数量
     */
    private BigDecimal inStoreSales30;

    /**
     * 调入后30天销售成本金额
     */
    private BigDecimal inStorePuramount30;

    /**
     * 调入后60天销售数量
     */
    private BigDecimal inStoreSales60;

    /**
     * 调入后60天销售成本金额
     */
    private BigDecimal inStorePuramount60;

    /**
     * 调入后90天销售数量
     */
    private BigDecimal inStoreSales90;

    /**
     * 调入后90天销售成本金额
     */
    private BigDecimal inStorePuramount90;

    /**
     * 状态(-1删除，0正常)
     */
    private Byte status;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新时间
     */
    private Date gmtUpdate;

    /**
     * 扩展字段
     */
    private String extend;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 创建人ID
     */
    private Long createdBy;

    /**
     * 创建人
     */
    private String createdName;

    /**
     * 更新人ID
     */
    private Long updatedBy;

    /**
     * 更新人
     */
    private String updatedName;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getAllotDate() {
        return allotDate;
    }

    public void setAllotDate(Date allotDate) {
        this.allotDate = allotDate;
    }

    public String getRegisterNo() {
        return registerNo;
    }

    public void setRegisterNo(String registerNo) {
        this.registerNo = registerNo;
    }

    public String getPosAllotNo() {
        return posAllotNo;
    }

    public void setPosAllotNo(String posAllotNo) {
        this.posAllotNo = posAllotNo;
    }

    public String getOutCompanyCode() {
        return outCompanyCode;
    }

    public void setOutCompanyCode(String outCompanyCode) {
        this.outCompanyCode = outCompanyCode;
    }

    public String getOutStoreCode() {
        return outStoreCode;
    }

    public void setOutStoreCode(String outStoreCode) {
        this.outStoreCode = outStoreCode;
    }

    public String getInCompanyCode() {
        return inCompanyCode;
    }

    public void setInCompanyCode(String inCompanyCode) {
        this.inCompanyCode = inCompanyCode;
    }

    public String getInStoreCode() {
        return inStoreCode;
    }

    public void setInStoreCode(String inStoreCode) {
        this.inStoreCode = inStoreCode;
    }

    public String getGoodsNo() {
        return goodsNo;
    }

    public void setGoodsNo(String goodsNo) {
        this.goodsNo = goodsNo;
    }

    public BigDecimal getInAllotQuantity() {
        return inAllotQuantity;
    }

    public void setInAllotQuantity(BigDecimal inAllotQuantity) {
        this.inAllotQuantity = inAllotQuantity;
    }

    public BigDecimal getInStoreSales7() {
        return inStoreSales7;
    }

    public void setInStoreSales7(BigDecimal inStoreSales7) {
        this.inStoreSales7 = inStoreSales7;
    }

    public BigDecimal getInStorePuramount7() {
        return inStorePuramount7;
    }

    public void setInStorePuramount7(BigDecimal inStorePuramount7) {
        this.inStorePuramount7 = inStorePuramount7;
    }

    public BigDecimal getInStoreSales14() {
        return inStoreSales14;
    }

    public void setInStoreSales14(BigDecimal inStoreSales14) {
        this.inStoreSales14 = inStoreSales14;
    }

    public BigDecimal getInStorePuramount14() {
        return inStorePuramount14;
    }

    public void setInStorePuramount14(BigDecimal inStorePuramount14) {
        this.inStorePuramount14 = inStorePuramount14;
    }

    public BigDecimal getInStoreSales30() {
        return inStoreSales30;
    }

    public void setInStoreSales30(BigDecimal inStoreSales30) {
        this.inStoreSales30 = inStoreSales30;
    }

    public BigDecimal getInStorePuramount30() {
        return inStorePuramount30;
    }

    public void setInStorePuramount30(BigDecimal inStorePuramount30) {
        this.inStorePuramount30 = inStorePuramount30;
    }

    public BigDecimal getInStoreSales60() {
        return inStoreSales60;
    }

    public void setInStoreSales60(BigDecimal inStoreSales60) {
        this.inStoreSales60 = inStoreSales60;
    }

    public BigDecimal getInStorePuramount60() {
        return inStorePuramount60;
    }

    public void setInStorePuramount60(BigDecimal inStorePuramount60) {
        this.inStorePuramount60 = inStorePuramount60;
    }

    public BigDecimal getInStoreSales90() {
        return inStoreSales90;
    }

    public void setInStoreSales90(BigDecimal inStoreSales90) {
        this.inStoreSales90 = inStoreSales90;
    }

    public BigDecimal getInStorePuramount90() {
        return inStorePuramount90;
    }

    public void setInStorePuramount90(BigDecimal inStorePuramount90) {
        this.inStorePuramount90 = inStorePuramount90;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtUpdate() {
        return gmtUpdate;
    }

    public void setGmtUpdate(Date gmtUpdate) {
        this.gmtUpdate = gmtUpdate;
    }

    public String getExtend() {
        return extend;
    }

    public void setExtend(String extend) {
        this.extend = extend;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Long getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    public String getCreatedName() {
        return createdName;
    }

    public void setCreatedName(String createdName) {
        this.createdName = createdName;
    }

    public Long getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(Long updatedBy) {
        this.updatedBy = updatedBy;
    }

    public String getUpdatedName() {
        return updatedName;
    }

    public void setUpdatedName(String updatedName) {
        this.updatedName = updatedName;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        IscmSuggestDistexecDone other = (IscmSuggestDistexecDone) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getAllotDate() == null ? other.getAllotDate() == null : this.getAllotDate().equals(other.getAllotDate()))
            && (this.getRegisterNo() == null ? other.getRegisterNo() == null : this.getRegisterNo().equals(other.getRegisterNo()))
            && (this.getPosAllotNo() == null ? other.getPosAllotNo() == null : this.getPosAllotNo().equals(other.getPosAllotNo()))
            && (this.getOutCompanyCode() == null ? other.getOutCompanyCode() == null : this.getOutCompanyCode().equals(other.getOutCompanyCode()))
            && (this.getOutStoreCode() == null ? other.getOutStoreCode() == null : this.getOutStoreCode().equals(other.getOutStoreCode()))
            && (this.getInCompanyCode() == null ? other.getInCompanyCode() == null : this.getInCompanyCode().equals(other.getInCompanyCode()))
            && (this.getInStoreCode() == null ? other.getInStoreCode() == null : this.getInStoreCode().equals(other.getInStoreCode()))
            && (this.getGoodsNo() == null ? other.getGoodsNo() == null : this.getGoodsNo().equals(other.getGoodsNo()))
            && (this.getInAllotQuantity() == null ? other.getInAllotQuantity() == null : this.getInAllotQuantity().equals(other.getInAllotQuantity()))
            && (this.getInStoreSales7() == null ? other.getInStoreSales7() == null : this.getInStoreSales7().equals(other.getInStoreSales7()))
            && (this.getInStorePuramount7() == null ? other.getInStorePuramount7() == null : this.getInStorePuramount7().equals(other.getInStorePuramount7()))
            && (this.getInStoreSales14() == null ? other.getInStoreSales14() == null : this.getInStoreSales14().equals(other.getInStoreSales14()))
            && (this.getInStorePuramount14() == null ? other.getInStorePuramount14() == null : this.getInStorePuramount14().equals(other.getInStorePuramount14()))
            && (this.getInStoreSales30() == null ? other.getInStoreSales30() == null : this.getInStoreSales30().equals(other.getInStoreSales30()))
            && (this.getInStorePuramount30() == null ? other.getInStorePuramount30() == null : this.getInStorePuramount30().equals(other.getInStorePuramount30()))
            && (this.getInStoreSales60() == null ? other.getInStoreSales60() == null : this.getInStoreSales60().equals(other.getInStoreSales60()))
            && (this.getInStorePuramount60() == null ? other.getInStorePuramount60() == null : this.getInStorePuramount60().equals(other.getInStorePuramount60()))
            && (this.getInStoreSales90() == null ? other.getInStoreSales90() == null : this.getInStoreSales90().equals(other.getInStoreSales90()))
            && (this.getInStorePuramount90() == null ? other.getInStorePuramount90() == null : this.getInStorePuramount90().equals(other.getInStorePuramount90()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getGmtCreate() == null ? other.getGmtCreate() == null : this.getGmtCreate().equals(other.getGmtCreate()))
            && (this.getGmtUpdate() == null ? other.getGmtUpdate() == null : this.getGmtUpdate().equals(other.getGmtUpdate()))
            && (this.getExtend() == null ? other.getExtend() == null : this.getExtend().equals(other.getExtend()))
            && (this.getVersion() == null ? other.getVersion() == null : this.getVersion().equals(other.getVersion()))
            && (this.getCreatedBy() == null ? other.getCreatedBy() == null : this.getCreatedBy().equals(other.getCreatedBy()))
            && (this.getCreatedName() == null ? other.getCreatedName() == null : this.getCreatedName().equals(other.getCreatedName()))
            && (this.getUpdatedBy() == null ? other.getUpdatedBy() == null : this.getUpdatedBy().equals(other.getUpdatedBy()))
            && (this.getUpdatedName() == null ? other.getUpdatedName() == null : this.getUpdatedName().equals(other.getUpdatedName()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getAllotDate() == null) ? 0 : getAllotDate().hashCode());
        result = prime * result + ((getRegisterNo() == null) ? 0 : getRegisterNo().hashCode());
        result = prime * result + ((getPosAllotNo() == null) ? 0 : getPosAllotNo().hashCode());
        result = prime * result + ((getOutCompanyCode() == null) ? 0 : getOutCompanyCode().hashCode());
        result = prime * result + ((getOutStoreCode() == null) ? 0 : getOutStoreCode().hashCode());
        result = prime * result + ((getInCompanyCode() == null) ? 0 : getInCompanyCode().hashCode());
        result = prime * result + ((getInStoreCode() == null) ? 0 : getInStoreCode().hashCode());
        result = prime * result + ((getGoodsNo() == null) ? 0 : getGoodsNo().hashCode());
        result = prime * result + ((getInAllotQuantity() == null) ? 0 : getInAllotQuantity().hashCode());
        result = prime * result + ((getInStoreSales7() == null) ? 0 : getInStoreSales7().hashCode());
        result = prime * result + ((getInStorePuramount7() == null) ? 0 : getInStorePuramount7().hashCode());
        result = prime * result + ((getInStoreSales14() == null) ? 0 : getInStoreSales14().hashCode());
        result = prime * result + ((getInStorePuramount14() == null) ? 0 : getInStorePuramount14().hashCode());
        result = prime * result + ((getInStoreSales30() == null) ? 0 : getInStoreSales30().hashCode());
        result = prime * result + ((getInStorePuramount30() == null) ? 0 : getInStorePuramount30().hashCode());
        result = prime * result + ((getInStoreSales60() == null) ? 0 : getInStoreSales60().hashCode());
        result = prime * result + ((getInStorePuramount60() == null) ? 0 : getInStorePuramount60().hashCode());
        result = prime * result + ((getInStoreSales90() == null) ? 0 : getInStoreSales90().hashCode());
        result = prime * result + ((getInStorePuramount90() == null) ? 0 : getInStorePuramount90().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getGmtCreate() == null) ? 0 : getGmtCreate().hashCode());
        result = prime * result + ((getGmtUpdate() == null) ? 0 : getGmtUpdate().hashCode());
        result = prime * result + ((getExtend() == null) ? 0 : getExtend().hashCode());
        result = prime * result + ((getVersion() == null) ? 0 : getVersion().hashCode());
        result = prime * result + ((getCreatedBy() == null) ? 0 : getCreatedBy().hashCode());
        result = prime * result + ((getCreatedName() == null) ? 0 : getCreatedName().hashCode());
        result = prime * result + ((getUpdatedBy() == null) ? 0 : getUpdatedBy().hashCode());
        result = prime * result + ((getUpdatedName() == null) ? 0 : getUpdatedName().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", allotDate=").append(allotDate);
        sb.append(", registerNo=").append(registerNo);
        sb.append(", posAllotNo=").append(posAllotNo);
        sb.append(", outCompanyCode=").append(outCompanyCode);
        sb.append(", outStoreCode=").append(outStoreCode);
        sb.append(", inCompanyCode=").append(inCompanyCode);
        sb.append(", inStoreCode=").append(inStoreCode);
        sb.append(", goodsNo=").append(goodsNo);
        sb.append(", inAllotQuantity=").append(inAllotQuantity);
        sb.append(", inStoreSales7=").append(inStoreSales7);
        sb.append(", inStorePuramount7=").append(inStorePuramount7);
        sb.append(", inStoreSales14=").append(inStoreSales14);
        sb.append(", inStorePuramount14=").append(inStorePuramount14);
        sb.append(", inStoreSales30=").append(inStoreSales30);
        sb.append(", inStorePuramount30=").append(inStorePuramount30);
        sb.append(", inStoreSales60=").append(inStoreSales60);
        sb.append(", inStorePuramount60=").append(inStorePuramount60);
        sb.append(", inStoreSales90=").append(inStoreSales90);
        sb.append(", inStorePuramount90=").append(inStorePuramount90);
        sb.append(", status=").append(status);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", gmtUpdate=").append(gmtUpdate);
        sb.append(", extend=").append(extend);
        sb.append(", version=").append(version);
        sb.append(", createdBy=").append(createdBy);
        sb.append(", createdName=").append(createdName);
        sb.append(", updatedBy=").append(updatedBy);
        sb.append(", updatedName=").append(updatedName);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}