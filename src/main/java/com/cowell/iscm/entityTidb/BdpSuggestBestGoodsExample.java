package com.cowell.iscm.entityTidb;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class BdpSuggestBestGoodsExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Long offset;

    public BdpSuggestBestGoodsExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Long offset) {
        this.offset = offset;
    }

    public Long getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        protected void addCriterionForJDBCDate(String condition, Date value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value.getTime()), property);
        }

        protected void addCriterionForJDBCDate(String condition, List<Date> values, String property) {
            if (values == null || values.size() == 0) {
                throw new RuntimeException("Value list for " + property + " cannot be null or empty");
            }
            List<java.sql.Date> dateList = new ArrayList<java.sql.Date>();
            Iterator<Date> iter = values.iterator();
            while (iter.hasNext()) {
                dateList.add(new java.sql.Date(iter.next().getTime()));
            }
            addCriterion(condition, dateList, property);
        }

        protected void addCriterionForJDBCDate(String condition, Date value1, Date value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value1.getTime()), new java.sql.Date(value2.getTime()), property);
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andSuggestDateIsNull() {
            addCriterion("suggest_date is null");
            return (Criteria) this;
        }

        public Criteria andSuggestDateIsNotNull() {
            addCriterion("suggest_date is not null");
            return (Criteria) this;
        }

        public Criteria andSuggestDateEqualTo(Date value) {
            addCriterionForJDBCDate("suggest_date =", value, "suggestDate");
            return (Criteria) this;
        }

        public Criteria andSuggestDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("suggest_date <>", value, "suggestDate");
            return (Criteria) this;
        }

        public Criteria andSuggestDateGreaterThan(Date value) {
            addCriterionForJDBCDate("suggest_date >", value, "suggestDate");
            return (Criteria) this;
        }

        public Criteria andSuggestDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("suggest_date >=", value, "suggestDate");
            return (Criteria) this;
        }

        public Criteria andSuggestDateLessThan(Date value) {
            addCriterionForJDBCDate("suggest_date <", value, "suggestDate");
            return (Criteria) this;
        }

        public Criteria andSuggestDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("suggest_date <=", value, "suggestDate");
            return (Criteria) this;
        }

        public Criteria andSuggestDateIn(List<Date> values) {
            addCriterionForJDBCDate("suggest_date in", values, "suggestDate");
            return (Criteria) this;
        }

        public Criteria andSuggestDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("suggest_date not in", values, "suggestDate");
            return (Criteria) this;
        }

        public Criteria andSuggestDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("suggest_date between", value1, value2, "suggestDate");
            return (Criteria) this;
        }

        public Criteria andSuggestDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("suggest_date not between", value1, value2, "suggestDate");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIsNull() {
            addCriterion("company_code is null");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIsNotNull() {
            addCriterion("company_code is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeEqualTo(String value) {
            addCriterion("company_code =", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotEqualTo(String value) {
            addCriterion("company_code <>", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeGreaterThan(String value) {
            addCriterion("company_code >", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeGreaterThanOrEqualTo(String value) {
            addCriterion("company_code >=", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLessThan(String value) {
            addCriterion("company_code <", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLessThanOrEqualTo(String value) {
            addCriterion("company_code <=", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLike(String value) {
            addCriterion("company_code like", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotLike(String value) {
            addCriterion("company_code not like", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIn(List<String> values) {
            addCriterion("company_code in", values, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotIn(List<String> values) {
            addCriterion("company_code not in", values, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeBetween(String value1, String value2) {
            addCriterion("company_code between", value1, value2, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotBetween(String value1, String value2) {
            addCriterion("company_code not between", value1, value2, "companyCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeIsNull() {
            addCriterion("store_code is null");
            return (Criteria) this;
        }

        public Criteria andStoreCodeIsNotNull() {
            addCriterion("store_code is not null");
            return (Criteria) this;
        }

        public Criteria andStoreCodeEqualTo(String value) {
            addCriterion("store_code =", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotEqualTo(String value) {
            addCriterion("store_code <>", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeGreaterThan(String value) {
            addCriterion("store_code >", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeGreaterThanOrEqualTo(String value) {
            addCriterion("store_code >=", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeLessThan(String value) {
            addCriterion("store_code <", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeLessThanOrEqualTo(String value) {
            addCriterion("store_code <=", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeLike(String value) {
            addCriterion("store_code like", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotLike(String value) {
            addCriterion("store_code not like", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeIn(List<String> values) {
            addCriterion("store_code in", values, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotIn(List<String> values) {
            addCriterion("store_code not in", values, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeBetween(String value1, String value2) {
            addCriterion("store_code between", value1, value2, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotBetween(String value1, String value2) {
            addCriterion("store_code not between", value1, value2, "storeCode");
            return (Criteria) this;
        }

        public Criteria andPushHdStatusIsNull() {
            addCriterion("push_hd_status is null");
            return (Criteria) this;
        }

        public Criteria andPushHdStatusIsNotNull() {
            addCriterion("push_hd_status is not null");
            return (Criteria) this;
        }

        public Criteria andPushHdStatusEqualTo(Byte value) {
            addCriterion("push_hd_status =", value, "pushHdStatus");
            return (Criteria) this;
        }

        public Criteria andPushHdStatusNotEqualTo(Byte value) {
            addCriterion("push_hd_status <>", value, "pushHdStatus");
            return (Criteria) this;
        }

        public Criteria andPushHdStatusGreaterThan(Byte value) {
            addCriterion("push_hd_status >", value, "pushHdStatus");
            return (Criteria) this;
        }

        public Criteria andPushHdStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("push_hd_status >=", value, "pushHdStatus");
            return (Criteria) this;
        }

        public Criteria andPushHdStatusLessThan(Byte value) {
            addCriterion("push_hd_status <", value, "pushHdStatus");
            return (Criteria) this;
        }

        public Criteria andPushHdStatusLessThanOrEqualTo(Byte value) {
            addCriterion("push_hd_status <=", value, "pushHdStatus");
            return (Criteria) this;
        }

        public Criteria andPushHdStatusIn(List<Byte> values) {
            addCriterion("push_hd_status in", values, "pushHdStatus");
            return (Criteria) this;
        }

        public Criteria andPushHdStatusNotIn(List<Byte> values) {
            addCriterion("push_hd_status not in", values, "pushHdStatus");
            return (Criteria) this;
        }

        public Criteria andPushHdStatusBetween(Byte value1, Byte value2) {
            addCriterion("push_hd_status between", value1, value2, "pushHdStatus");
            return (Criteria) this;
        }

        public Criteria andPushHdStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("push_hd_status not between", value1, value2, "pushHdStatus");
            return (Criteria) this;
        }

        public Criteria andGoodsNoIsNull() {
            addCriterion("goods_no is null");
            return (Criteria) this;
        }

        public Criteria andGoodsNoIsNotNull() {
            addCriterion("goods_no is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsNoEqualTo(String value) {
            addCriterion("goods_no =", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotEqualTo(String value) {
            addCriterion("goods_no <>", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoGreaterThan(String value) {
            addCriterion("goods_no >", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoGreaterThanOrEqualTo(String value) {
            addCriterion("goods_no >=", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoLessThan(String value) {
            addCriterion("goods_no <", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoLessThanOrEqualTo(String value) {
            addCriterion("goods_no <=", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoLike(String value) {
            addCriterion("goods_no like", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotLike(String value) {
            addCriterion("goods_no not like", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoIn(List<String> values) {
            addCriterion("goods_no in", values, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotIn(List<String> values) {
            addCriterion("goods_no not in", values, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoBetween(String value1, String value2) {
            addCriterion("goods_no between", value1, value2, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotBetween(String value1, String value2) {
            addCriterion("goods_no not between", value1, value2, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNameIsNull() {
            addCriterion("goods_name is null");
            return (Criteria) this;
        }

        public Criteria andGoodsNameIsNotNull() {
            addCriterion("goods_name is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsNameEqualTo(String value) {
            addCriterion("goods_name =", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameNotEqualTo(String value) {
            addCriterion("goods_name <>", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameGreaterThan(String value) {
            addCriterion("goods_name >", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameGreaterThanOrEqualTo(String value) {
            addCriterion("goods_name >=", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameLessThan(String value) {
            addCriterion("goods_name <", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameLessThanOrEqualTo(String value) {
            addCriterion("goods_name <=", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameLike(String value) {
            addCriterion("goods_name like", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameNotLike(String value) {
            addCriterion("goods_name not like", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameIn(List<String> values) {
            addCriterion("goods_name in", values, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameNotIn(List<String> values) {
            addCriterion("goods_name not in", values, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameBetween(String value1, String value2) {
            addCriterion("goods_name between", value1, value2, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameNotBetween(String value1, String value2) {
            addCriterion("goods_name not between", value1, value2, "goodsName");
            return (Criteria) this;
        }

        public Criteria andSpecificationsIsNull() {
            addCriterion("specifications is null");
            return (Criteria) this;
        }

        public Criteria andSpecificationsIsNotNull() {
            addCriterion("specifications is not null");
            return (Criteria) this;
        }

        public Criteria andSpecificationsEqualTo(String value) {
            addCriterion("specifications =", value, "specifications");
            return (Criteria) this;
        }

        public Criteria andSpecificationsNotEqualTo(String value) {
            addCriterion("specifications <>", value, "specifications");
            return (Criteria) this;
        }

        public Criteria andSpecificationsGreaterThan(String value) {
            addCriterion("specifications >", value, "specifications");
            return (Criteria) this;
        }

        public Criteria andSpecificationsGreaterThanOrEqualTo(String value) {
            addCriterion("specifications >=", value, "specifications");
            return (Criteria) this;
        }

        public Criteria andSpecificationsLessThan(String value) {
            addCriterion("specifications <", value, "specifications");
            return (Criteria) this;
        }

        public Criteria andSpecificationsLessThanOrEqualTo(String value) {
            addCriterion("specifications <=", value, "specifications");
            return (Criteria) this;
        }

        public Criteria andSpecificationsLike(String value) {
            addCriterion("specifications like", value, "specifications");
            return (Criteria) this;
        }

        public Criteria andSpecificationsNotLike(String value) {
            addCriterion("specifications not like", value, "specifications");
            return (Criteria) this;
        }

        public Criteria andSpecificationsIn(List<String> values) {
            addCriterion("specifications in", values, "specifications");
            return (Criteria) this;
        }

        public Criteria andSpecificationsNotIn(List<String> values) {
            addCriterion("specifications not in", values, "specifications");
            return (Criteria) this;
        }

        public Criteria andSpecificationsBetween(String value1, String value2) {
            addCriterion("specifications between", value1, value2, "specifications");
            return (Criteria) this;
        }

        public Criteria andSpecificationsNotBetween(String value1, String value2) {
            addCriterion("specifications not between", value1, value2, "specifications");
            return (Criteria) this;
        }

        public Criteria andDosageFormIsNull() {
            addCriterion("dosage_form is null");
            return (Criteria) this;
        }

        public Criteria andDosageFormIsNotNull() {
            addCriterion("dosage_form is not null");
            return (Criteria) this;
        }

        public Criteria andDosageFormEqualTo(String value) {
            addCriterion("dosage_form =", value, "dosageForm");
            return (Criteria) this;
        }

        public Criteria andDosageFormNotEqualTo(String value) {
            addCriterion("dosage_form <>", value, "dosageForm");
            return (Criteria) this;
        }

        public Criteria andDosageFormGreaterThan(String value) {
            addCriterion("dosage_form >", value, "dosageForm");
            return (Criteria) this;
        }

        public Criteria andDosageFormGreaterThanOrEqualTo(String value) {
            addCriterion("dosage_form >=", value, "dosageForm");
            return (Criteria) this;
        }

        public Criteria andDosageFormLessThan(String value) {
            addCriterion("dosage_form <", value, "dosageForm");
            return (Criteria) this;
        }

        public Criteria andDosageFormLessThanOrEqualTo(String value) {
            addCriterion("dosage_form <=", value, "dosageForm");
            return (Criteria) this;
        }

        public Criteria andDosageFormLike(String value) {
            addCriterion("dosage_form like", value, "dosageForm");
            return (Criteria) this;
        }

        public Criteria andDosageFormNotLike(String value) {
            addCriterion("dosage_form not like", value, "dosageForm");
            return (Criteria) this;
        }

        public Criteria andDosageFormIn(List<String> values) {
            addCriterion("dosage_form in", values, "dosageForm");
            return (Criteria) this;
        }

        public Criteria andDosageFormNotIn(List<String> values) {
            addCriterion("dosage_form not in", values, "dosageForm");
            return (Criteria) this;
        }

        public Criteria andDosageFormBetween(String value1, String value2) {
            addCriterion("dosage_form between", value1, value2, "dosageForm");
            return (Criteria) this;
        }

        public Criteria andDosageFormNotBetween(String value1, String value2) {
            addCriterion("dosage_form not between", value1, value2, "dosageForm");
            return (Criteria) this;
        }

        public Criteria andManufacturerIsNull() {
            addCriterion("manufacturer is null");
            return (Criteria) this;
        }

        public Criteria andManufacturerIsNotNull() {
            addCriterion("manufacturer is not null");
            return (Criteria) this;
        }

        public Criteria andManufacturerEqualTo(String value) {
            addCriterion("manufacturer =", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerNotEqualTo(String value) {
            addCriterion("manufacturer <>", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerGreaterThan(String value) {
            addCriterion("manufacturer >", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerGreaterThanOrEqualTo(String value) {
            addCriterion("manufacturer >=", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerLessThan(String value) {
            addCriterion("manufacturer <", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerLessThanOrEqualTo(String value) {
            addCriterion("manufacturer <=", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerLike(String value) {
            addCriterion("manufacturer like", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerNotLike(String value) {
            addCriterion("manufacturer not like", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerIn(List<String> values) {
            addCriterion("manufacturer in", values, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerNotIn(List<String> values) {
            addCriterion("manufacturer not in", values, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerBetween(String value1, String value2) {
            addCriterion("manufacturer between", value1, value2, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerNotBetween(String value1, String value2) {
            addCriterion("manufacturer not between", value1, value2, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andComponentIsNull() {
            addCriterion("component is null");
            return (Criteria) this;
        }

        public Criteria andComponentIsNotNull() {
            addCriterion("component is not null");
            return (Criteria) this;
        }

        public Criteria andComponentEqualTo(String value) {
            addCriterion("component =", value, "component");
            return (Criteria) this;
        }

        public Criteria andComponentNotEqualTo(String value) {
            addCriterion("component <>", value, "component");
            return (Criteria) this;
        }

        public Criteria andComponentGreaterThan(String value) {
            addCriterion("component >", value, "component");
            return (Criteria) this;
        }

        public Criteria andComponentGreaterThanOrEqualTo(String value) {
            addCriterion("component >=", value, "component");
            return (Criteria) this;
        }

        public Criteria andComponentLessThan(String value) {
            addCriterion("component <", value, "component");
            return (Criteria) this;
        }

        public Criteria andComponentLessThanOrEqualTo(String value) {
            addCriterion("component <=", value, "component");
            return (Criteria) this;
        }

        public Criteria andComponentLike(String value) {
            addCriterion("component like", value, "component");
            return (Criteria) this;
        }

        public Criteria andComponentNotLike(String value) {
            addCriterion("component not like", value, "component");
            return (Criteria) this;
        }

        public Criteria andComponentIn(List<String> values) {
            addCriterion("component in", values, "component");
            return (Criteria) this;
        }

        public Criteria andComponentNotIn(List<String> values) {
            addCriterion("component not in", values, "component");
            return (Criteria) this;
        }

        public Criteria andComponentBetween(String value1, String value2) {
            addCriterion("component between", value1, value2, "component");
            return (Criteria) this;
        }

        public Criteria andComponentNotBetween(String value1, String value2) {
            addCriterion("component not between", value1, value2, "component");
            return (Criteria) this;
        }

        public Criteria andThirtySalesQuantityIsNull() {
            addCriterion("thirty_sales_quantity is null");
            return (Criteria) this;
        }

        public Criteria andThirtySalesQuantityIsNotNull() {
            addCriterion("thirty_sales_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andThirtySalesQuantityEqualTo(BigDecimal value) {
            addCriterion("thirty_sales_quantity =", value, "thirtySalesQuantity");
            return (Criteria) this;
        }

        public Criteria andThirtySalesQuantityNotEqualTo(BigDecimal value) {
            addCriterion("thirty_sales_quantity <>", value, "thirtySalesQuantity");
            return (Criteria) this;
        }

        public Criteria andThirtySalesQuantityGreaterThan(BigDecimal value) {
            addCriterion("thirty_sales_quantity >", value, "thirtySalesQuantity");
            return (Criteria) this;
        }

        public Criteria andThirtySalesQuantityGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("thirty_sales_quantity >=", value, "thirtySalesQuantity");
            return (Criteria) this;
        }

        public Criteria andThirtySalesQuantityLessThan(BigDecimal value) {
            addCriterion("thirty_sales_quantity <", value, "thirtySalesQuantity");
            return (Criteria) this;
        }

        public Criteria andThirtySalesQuantityLessThanOrEqualTo(BigDecimal value) {
            addCriterion("thirty_sales_quantity <=", value, "thirtySalesQuantity");
            return (Criteria) this;
        }

        public Criteria andThirtySalesQuantityIn(List<BigDecimal> values) {
            addCriterion("thirty_sales_quantity in", values, "thirtySalesQuantity");
            return (Criteria) this;
        }

        public Criteria andThirtySalesQuantityNotIn(List<BigDecimal> values) {
            addCriterion("thirty_sales_quantity not in", values, "thirtySalesQuantity");
            return (Criteria) this;
        }

        public Criteria andThirtySalesQuantityBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("thirty_sales_quantity between", value1, value2, "thirtySalesQuantity");
            return (Criteria) this;
        }

        public Criteria andThirtySalesQuantityNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("thirty_sales_quantity not between", value1, value2, "thirtySalesQuantity");
            return (Criteria) this;
        }

        public Criteria andReferRetailPriceIsNull() {
            addCriterion("refer_retail_price is null");
            return (Criteria) this;
        }

        public Criteria andReferRetailPriceIsNotNull() {
            addCriterion("refer_retail_price is not null");
            return (Criteria) this;
        }

        public Criteria andReferRetailPriceEqualTo(BigDecimal value) {
            addCriterion("refer_retail_price =", value, "referRetailPrice");
            return (Criteria) this;
        }

        public Criteria andReferRetailPriceNotEqualTo(BigDecimal value) {
            addCriterion("refer_retail_price <>", value, "referRetailPrice");
            return (Criteria) this;
        }

        public Criteria andReferRetailPriceGreaterThan(BigDecimal value) {
            addCriterion("refer_retail_price >", value, "referRetailPrice");
            return (Criteria) this;
        }

        public Criteria andReferRetailPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("refer_retail_price >=", value, "referRetailPrice");
            return (Criteria) this;
        }

        public Criteria andReferRetailPriceLessThan(BigDecimal value) {
            addCriterion("refer_retail_price <", value, "referRetailPrice");
            return (Criteria) this;
        }

        public Criteria andReferRetailPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("refer_retail_price <=", value, "referRetailPrice");
            return (Criteria) this;
        }

        public Criteria andReferRetailPriceIn(List<BigDecimal> values) {
            addCriterion("refer_retail_price in", values, "referRetailPrice");
            return (Criteria) this;
        }

        public Criteria andReferRetailPriceNotIn(List<BigDecimal> values) {
            addCriterion("refer_retail_price not in", values, "referRetailPrice");
            return (Criteria) this;
        }

        public Criteria andReferRetailPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("refer_retail_price between", value1, value2, "referRetailPrice");
            return (Criteria) this;
        }

        public Criteria andReferRetailPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("refer_retail_price not between", value1, value2, "referRetailPrice");
            return (Criteria) this;
        }

        public Criteria andReferGrossProfitIsNull() {
            addCriterion("refer_gross_profit is null");
            return (Criteria) this;
        }

        public Criteria andReferGrossProfitIsNotNull() {
            addCriterion("refer_gross_profit is not null");
            return (Criteria) this;
        }

        public Criteria andReferGrossProfitEqualTo(BigDecimal value) {
            addCriterion("refer_gross_profit =", value, "referGrossProfit");
            return (Criteria) this;
        }

        public Criteria andReferGrossProfitNotEqualTo(BigDecimal value) {
            addCriterion("refer_gross_profit <>", value, "referGrossProfit");
            return (Criteria) this;
        }

        public Criteria andReferGrossProfitGreaterThan(BigDecimal value) {
            addCriterion("refer_gross_profit >", value, "referGrossProfit");
            return (Criteria) this;
        }

        public Criteria andReferGrossProfitGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("refer_gross_profit >=", value, "referGrossProfit");
            return (Criteria) this;
        }

        public Criteria andReferGrossProfitLessThan(BigDecimal value) {
            addCriterion("refer_gross_profit <", value, "referGrossProfit");
            return (Criteria) this;
        }

        public Criteria andReferGrossProfitLessThanOrEqualTo(BigDecimal value) {
            addCriterion("refer_gross_profit <=", value, "referGrossProfit");
            return (Criteria) this;
        }

        public Criteria andReferGrossProfitIn(List<BigDecimal> values) {
            addCriterion("refer_gross_profit in", values, "referGrossProfit");
            return (Criteria) this;
        }

        public Criteria andReferGrossProfitNotIn(List<BigDecimal> values) {
            addCriterion("refer_gross_profit not in", values, "referGrossProfit");
            return (Criteria) this;
        }

        public Criteria andReferGrossProfitBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("refer_gross_profit between", value1, value2, "referGrossProfit");
            return (Criteria) this;
        }

        public Criteria andReferGrossProfitNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("refer_gross_profit not between", value1, value2, "referGrossProfit");
            return (Criteria) this;
        }

        public Criteria andSalesRankIsNull() {
            addCriterion("sales_rank is null");
            return (Criteria) this;
        }

        public Criteria andSalesRankIsNotNull() {
            addCriterion("sales_rank is not null");
            return (Criteria) this;
        }

        public Criteria andSalesRankEqualTo(Integer value) {
            addCriterion("sales_rank =", value, "salesRank");
            return (Criteria) this;
        }

        public Criteria andSalesRankNotEqualTo(Integer value) {
            addCriterion("sales_rank <>", value, "salesRank");
            return (Criteria) this;
        }

        public Criteria andSalesRankGreaterThan(Integer value) {
            addCriterion("sales_rank >", value, "salesRank");
            return (Criteria) this;
        }

        public Criteria andSalesRankGreaterThanOrEqualTo(Integer value) {
            addCriterion("sales_rank >=", value, "salesRank");
            return (Criteria) this;
        }

        public Criteria andSalesRankLessThan(Integer value) {
            addCriterion("sales_rank <", value, "salesRank");
            return (Criteria) this;
        }

        public Criteria andSalesRankLessThanOrEqualTo(Integer value) {
            addCriterion("sales_rank <=", value, "salesRank");
            return (Criteria) this;
        }

        public Criteria andSalesRankIn(List<Integer> values) {
            addCriterion("sales_rank in", values, "salesRank");
            return (Criteria) this;
        }

        public Criteria andSalesRankNotIn(List<Integer> values) {
            addCriterion("sales_rank not in", values, "salesRank");
            return (Criteria) this;
        }

        public Criteria andSalesRankBetween(Integer value1, Integer value2) {
            addCriterion("sales_rank between", value1, value2, "salesRank");
            return (Criteria) this;
        }

        public Criteria andSalesRankNotBetween(Integer value1, Integer value2) {
            addCriterion("sales_rank not between", value1, value2, "salesRank");
            return (Criteria) this;
        }

        public Criteria andPushLevelIsNull() {
            addCriterion("push_level is null");
            return (Criteria) this;
        }

        public Criteria andPushLevelIsNotNull() {
            addCriterion("push_level is not null");
            return (Criteria) this;
        }

        public Criteria andPushLevelEqualTo(String value) {
            addCriterion("push_level =", value, "pushLevel");
            return (Criteria) this;
        }

        public Criteria andPushLevelNotEqualTo(String value) {
            addCriterion("push_level <>", value, "pushLevel");
            return (Criteria) this;
        }

        public Criteria andPushLevelGreaterThan(String value) {
            addCriterion("push_level >", value, "pushLevel");
            return (Criteria) this;
        }

        public Criteria andPushLevelGreaterThanOrEqualTo(String value) {
            addCriterion("push_level >=", value, "pushLevel");
            return (Criteria) this;
        }

        public Criteria andPushLevelLessThan(String value) {
            addCriterion("push_level <", value, "pushLevel");
            return (Criteria) this;
        }

        public Criteria andPushLevelLessThanOrEqualTo(String value) {
            addCriterion("push_level <=", value, "pushLevel");
            return (Criteria) this;
        }

        public Criteria andPushLevelLike(String value) {
            addCriterion("push_level like", value, "pushLevel");
            return (Criteria) this;
        }

        public Criteria andPushLevelNotLike(String value) {
            addCriterion("push_level not like", value, "pushLevel");
            return (Criteria) this;
        }

        public Criteria andPushLevelIn(List<String> values) {
            addCriterion("push_level in", values, "pushLevel");
            return (Criteria) this;
        }

        public Criteria andPushLevelNotIn(List<String> values) {
            addCriterion("push_level not in", values, "pushLevel");
            return (Criteria) this;
        }

        public Criteria andPushLevelBetween(String value1, String value2) {
            addCriterion("push_level between", value1, value2, "pushLevel");
            return (Criteria) this;
        }

        public Criteria andPushLevelNotBetween(String value1, String value2) {
            addCriterion("push_level not between", value1, value2, "pushLevel");
            return (Criteria) this;
        }

        public Criteria andMedicinePeopleIsNull() {
            addCriterion("medicine_people is null");
            return (Criteria) this;
        }

        public Criteria andMedicinePeopleIsNotNull() {
            addCriterion("medicine_people is not null");
            return (Criteria) this;
        }

        public Criteria andMedicinePeopleEqualTo(String value) {
            addCriterion("medicine_people =", value, "medicinePeople");
            return (Criteria) this;
        }

        public Criteria andMedicinePeopleNotEqualTo(String value) {
            addCriterion("medicine_people <>", value, "medicinePeople");
            return (Criteria) this;
        }

        public Criteria andMedicinePeopleGreaterThan(String value) {
            addCriterion("medicine_people >", value, "medicinePeople");
            return (Criteria) this;
        }

        public Criteria andMedicinePeopleGreaterThanOrEqualTo(String value) {
            addCriterion("medicine_people >=", value, "medicinePeople");
            return (Criteria) this;
        }

        public Criteria andMedicinePeopleLessThan(String value) {
            addCriterion("medicine_people <", value, "medicinePeople");
            return (Criteria) this;
        }

        public Criteria andMedicinePeopleLessThanOrEqualTo(String value) {
            addCriterion("medicine_people <=", value, "medicinePeople");
            return (Criteria) this;
        }

        public Criteria andMedicinePeopleLike(String value) {
            addCriterion("medicine_people like", value, "medicinePeople");
            return (Criteria) this;
        }

        public Criteria andMedicinePeopleNotLike(String value) {
            addCriterion("medicine_people not like", value, "medicinePeople");
            return (Criteria) this;
        }

        public Criteria andMedicinePeopleIn(List<String> values) {
            addCriterion("medicine_people in", values, "medicinePeople");
            return (Criteria) this;
        }

        public Criteria andMedicinePeopleNotIn(List<String> values) {
            addCriterion("medicine_people not in", values, "medicinePeople");
            return (Criteria) this;
        }

        public Criteria andMedicinePeopleBetween(String value1, String value2) {
            addCriterion("medicine_people between", value1, value2, "medicinePeople");
            return (Criteria) this;
        }

        public Criteria andMedicinePeopleNotBetween(String value1, String value2) {
            addCriterion("medicine_people not between", value1, value2, "medicinePeople");
            return (Criteria) this;
        }

        public Criteria andOtcAbleIsNull() {
            addCriterion("otc_able is null");
            return (Criteria) this;
        }

        public Criteria andOtcAbleIsNotNull() {
            addCriterion("otc_able is not null");
            return (Criteria) this;
        }

        public Criteria andOtcAbleEqualTo(Byte value) {
            addCriterion("otc_able =", value, "otcAble");
            return (Criteria) this;
        }

        public Criteria andOtcAbleNotEqualTo(Byte value) {
            addCriterion("otc_able <>", value, "otcAble");
            return (Criteria) this;
        }

        public Criteria andOtcAbleGreaterThan(Byte value) {
            addCriterion("otc_able >", value, "otcAble");
            return (Criteria) this;
        }

        public Criteria andOtcAbleGreaterThanOrEqualTo(Byte value) {
            addCriterion("otc_able >=", value, "otcAble");
            return (Criteria) this;
        }

        public Criteria andOtcAbleLessThan(Byte value) {
            addCriterion("otc_able <", value, "otcAble");
            return (Criteria) this;
        }

        public Criteria andOtcAbleLessThanOrEqualTo(Byte value) {
            addCriterion("otc_able <=", value, "otcAble");
            return (Criteria) this;
        }

        public Criteria andOtcAbleIn(List<Byte> values) {
            addCriterion("otc_able in", values, "otcAble");
            return (Criteria) this;
        }

        public Criteria andOtcAbleNotIn(List<Byte> values) {
            addCriterion("otc_able not in", values, "otcAble");
            return (Criteria) this;
        }

        public Criteria andOtcAbleBetween(Byte value1, Byte value2) {
            addCriterion("otc_able between", value1, value2, "otcAble");
            return (Criteria) this;
        }

        public Criteria andOtcAbleNotBetween(Byte value1, Byte value2) {
            addCriterion("otc_able not between", value1, value2, "otcAble");
            return (Criteria) this;
        }

        public Criteria andRxAbleIsNull() {
            addCriterion("rx_able is null");
            return (Criteria) this;
        }

        public Criteria andRxAbleIsNotNull() {
            addCriterion("rx_able is not null");
            return (Criteria) this;
        }

        public Criteria andRxAbleEqualTo(Byte value) {
            addCriterion("rx_able =", value, "rxAble");
            return (Criteria) this;
        }

        public Criteria andRxAbleNotEqualTo(Byte value) {
            addCriterion("rx_able <>", value, "rxAble");
            return (Criteria) this;
        }

        public Criteria andRxAbleGreaterThan(Byte value) {
            addCriterion("rx_able >", value, "rxAble");
            return (Criteria) this;
        }

        public Criteria andRxAbleGreaterThanOrEqualTo(Byte value) {
            addCriterion("rx_able >=", value, "rxAble");
            return (Criteria) this;
        }

        public Criteria andRxAbleLessThan(Byte value) {
            addCriterion("rx_able <", value, "rxAble");
            return (Criteria) this;
        }

        public Criteria andRxAbleLessThanOrEqualTo(Byte value) {
            addCriterion("rx_able <=", value, "rxAble");
            return (Criteria) this;
        }

        public Criteria andRxAbleIn(List<Byte> values) {
            addCriterion("rx_able in", values, "rxAble");
            return (Criteria) this;
        }

        public Criteria andRxAbleNotIn(List<Byte> values) {
            addCriterion("rx_able not in", values, "rxAble");
            return (Criteria) this;
        }

        public Criteria andRxAbleBetween(Byte value1, Byte value2) {
            addCriterion("rx_able between", value1, value2, "rxAble");
            return (Criteria) this;
        }

        public Criteria andRxAbleNotBetween(Byte value1, Byte value2) {
            addCriterion("rx_able not between", value1, value2, "rxAble");
            return (Criteria) this;
        }

        public Criteria andYbAbleIsNull() {
            addCriterion("yb_able is null");
            return (Criteria) this;
        }

        public Criteria andYbAbleIsNotNull() {
            addCriterion("yb_able is not null");
            return (Criteria) this;
        }

        public Criteria andYbAbleEqualTo(Byte value) {
            addCriterion("yb_able =", value, "ybAble");
            return (Criteria) this;
        }

        public Criteria andYbAbleNotEqualTo(Byte value) {
            addCriterion("yb_able <>", value, "ybAble");
            return (Criteria) this;
        }

        public Criteria andYbAbleGreaterThan(Byte value) {
            addCriterion("yb_able >", value, "ybAble");
            return (Criteria) this;
        }

        public Criteria andYbAbleGreaterThanOrEqualTo(Byte value) {
            addCriterion("yb_able >=", value, "ybAble");
            return (Criteria) this;
        }

        public Criteria andYbAbleLessThan(Byte value) {
            addCriterion("yb_able <", value, "ybAble");
            return (Criteria) this;
        }

        public Criteria andYbAbleLessThanOrEqualTo(Byte value) {
            addCriterion("yb_able <=", value, "ybAble");
            return (Criteria) this;
        }

        public Criteria andYbAbleIn(List<Byte> values) {
            addCriterion("yb_able in", values, "ybAble");
            return (Criteria) this;
        }

        public Criteria andYbAbleNotIn(List<Byte> values) {
            addCriterion("yb_able not in", values, "ybAble");
            return (Criteria) this;
        }

        public Criteria andYbAbleBetween(Byte value1, Byte value2) {
            addCriterion("yb_able between", value1, value2, "ybAble");
            return (Criteria) this;
        }

        public Criteria andYbAbleNotBetween(Byte value1, Byte value2) {
            addCriterion("yb_able not between", value1, value2, "ybAble");
            return (Criteria) this;
        }

        public Criteria andSensitiveAbleIsNull() {
            addCriterion("sensitive_able is null");
            return (Criteria) this;
        }

        public Criteria andSensitiveAbleIsNotNull() {
            addCriterion("sensitive_able is not null");
            return (Criteria) this;
        }

        public Criteria andSensitiveAbleEqualTo(Byte value) {
            addCriterion("sensitive_able =", value, "sensitiveAble");
            return (Criteria) this;
        }

        public Criteria andSensitiveAbleNotEqualTo(Byte value) {
            addCriterion("sensitive_able <>", value, "sensitiveAble");
            return (Criteria) this;
        }

        public Criteria andSensitiveAbleGreaterThan(Byte value) {
            addCriterion("sensitive_able >", value, "sensitiveAble");
            return (Criteria) this;
        }

        public Criteria andSensitiveAbleGreaterThanOrEqualTo(Byte value) {
            addCriterion("sensitive_able >=", value, "sensitiveAble");
            return (Criteria) this;
        }

        public Criteria andSensitiveAbleLessThan(Byte value) {
            addCriterion("sensitive_able <", value, "sensitiveAble");
            return (Criteria) this;
        }

        public Criteria andSensitiveAbleLessThanOrEqualTo(Byte value) {
            addCriterion("sensitive_able <=", value, "sensitiveAble");
            return (Criteria) this;
        }

        public Criteria andSensitiveAbleIn(List<Byte> values) {
            addCriterion("sensitive_able in", values, "sensitiveAble");
            return (Criteria) this;
        }

        public Criteria andSensitiveAbleNotIn(List<Byte> values) {
            addCriterion("sensitive_able not in", values, "sensitiveAble");
            return (Criteria) this;
        }

        public Criteria andSensitiveAbleBetween(Byte value1, Byte value2) {
            addCriterion("sensitive_able between", value1, value2, "sensitiveAble");
            return (Criteria) this;
        }

        public Criteria andSensitiveAbleNotBetween(Byte value1, Byte value2) {
            addCriterion("sensitive_able not between", value1, value2, "sensitiveAble");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIsNull() {
            addCriterion("category_id is null");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIsNotNull() {
            addCriterion("category_id is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryIdEqualTo(Long value) {
            addCriterion("category_id =", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotEqualTo(Long value) {
            addCriterion("category_id <>", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdGreaterThan(Long value) {
            addCriterion("category_id >", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdGreaterThanOrEqualTo(Long value) {
            addCriterion("category_id >=", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdLessThan(Long value) {
            addCriterion("category_id <", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdLessThanOrEqualTo(Long value) {
            addCriterion("category_id <=", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIn(List<Long> values) {
            addCriterion("category_id in", values, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotIn(List<Long> values) {
            addCriterion("category_id not in", values, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdBetween(Long value1, Long value2) {
            addCriterion("category_id between", value1, value2, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotBetween(Long value1, Long value2) {
            addCriterion("category_id not between", value1, value2, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIsNull() {
            addCriterion("category is null");
            return (Criteria) this;
        }

        public Criteria andCategoryIsNotNull() {
            addCriterion("category is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryEqualTo(String value) {
            addCriterion("category =", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryNotEqualTo(String value) {
            addCriterion("category <>", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryGreaterThan(String value) {
            addCriterion("category >", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryGreaterThanOrEqualTo(String value) {
            addCriterion("category >=", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryLessThan(String value) {
            addCriterion("category <", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryLessThanOrEqualTo(String value) {
            addCriterion("category <=", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryLike(String value) {
            addCriterion("category like", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryNotLike(String value) {
            addCriterion("category not like", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryIn(List<String> values) {
            addCriterion("category in", values, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryNotIn(List<String> values) {
            addCriterion("category not in", values, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryBetween(String value1, String value2) {
            addCriterion("category between", value1, value2, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryNotBetween(String value1, String value2) {
            addCriterion("category not between", value1, value2, "category");
            return (Criteria) this;
        }

        public Criteria andPromotionTagIsNull() {
            addCriterion("promotion_tag is null");
            return (Criteria) this;
        }

        public Criteria andPromotionTagIsNotNull() {
            addCriterion("promotion_tag is not null");
            return (Criteria) this;
        }

        public Criteria andPromotionTagEqualTo(String value) {
            addCriterion("promotion_tag =", value, "promotionTag");
            return (Criteria) this;
        }

        public Criteria andPromotionTagNotEqualTo(String value) {
            addCriterion("promotion_tag <>", value, "promotionTag");
            return (Criteria) this;
        }

        public Criteria andPromotionTagGreaterThan(String value) {
            addCriterion("promotion_tag >", value, "promotionTag");
            return (Criteria) this;
        }

        public Criteria andPromotionTagGreaterThanOrEqualTo(String value) {
            addCriterion("promotion_tag >=", value, "promotionTag");
            return (Criteria) this;
        }

        public Criteria andPromotionTagLessThan(String value) {
            addCriterion("promotion_tag <", value, "promotionTag");
            return (Criteria) this;
        }

        public Criteria andPromotionTagLessThanOrEqualTo(String value) {
            addCriterion("promotion_tag <=", value, "promotionTag");
            return (Criteria) this;
        }

        public Criteria andPromotionTagLike(String value) {
            addCriterion("promotion_tag like", value, "promotionTag");
            return (Criteria) this;
        }

        public Criteria andPromotionTagNotLike(String value) {
            addCriterion("promotion_tag not like", value, "promotionTag");
            return (Criteria) this;
        }

        public Criteria andPromotionTagIn(List<String> values) {
            addCriterion("promotion_tag in", values, "promotionTag");
            return (Criteria) this;
        }

        public Criteria andPromotionTagNotIn(List<String> values) {
            addCriterion("promotion_tag not in", values, "promotionTag");
            return (Criteria) this;
        }

        public Criteria andPromotionTagBetween(String value1, String value2) {
            addCriterion("promotion_tag between", value1, value2, "promotionTag");
            return (Criteria) this;
        }

        public Criteria andPromotionTagNotBetween(String value1, String value2) {
            addCriterion("promotion_tag not between", value1, value2, "promotionTag");
            return (Criteria) this;
        }

        public Criteria andPromotionNameIsNull() {
            addCriterion("promotion_name is null");
            return (Criteria) this;
        }

        public Criteria andPromotionNameIsNotNull() {
            addCriterion("promotion_name is not null");
            return (Criteria) this;
        }

        public Criteria andPromotionNameEqualTo(String value) {
            addCriterion("promotion_name =", value, "promotionName");
            return (Criteria) this;
        }

        public Criteria andPromotionNameNotEqualTo(String value) {
            addCriterion("promotion_name <>", value, "promotionName");
            return (Criteria) this;
        }

        public Criteria andPromotionNameGreaterThan(String value) {
            addCriterion("promotion_name >", value, "promotionName");
            return (Criteria) this;
        }

        public Criteria andPromotionNameGreaterThanOrEqualTo(String value) {
            addCriterion("promotion_name >=", value, "promotionName");
            return (Criteria) this;
        }

        public Criteria andPromotionNameLessThan(String value) {
            addCriterion("promotion_name <", value, "promotionName");
            return (Criteria) this;
        }

        public Criteria andPromotionNameLessThanOrEqualTo(String value) {
            addCriterion("promotion_name <=", value, "promotionName");
            return (Criteria) this;
        }

        public Criteria andPromotionNameLike(String value) {
            addCriterion("promotion_name like", value, "promotionName");
            return (Criteria) this;
        }

        public Criteria andPromotionNameNotLike(String value) {
            addCriterion("promotion_name not like", value, "promotionName");
            return (Criteria) this;
        }

        public Criteria andPromotionNameIn(List<String> values) {
            addCriterion("promotion_name in", values, "promotionName");
            return (Criteria) this;
        }

        public Criteria andPromotionNameNotIn(List<String> values) {
            addCriterion("promotion_name not in", values, "promotionName");
            return (Criteria) this;
        }

        public Criteria andPromotionNameBetween(String value1, String value2) {
            addCriterion("promotion_name between", value1, value2, "promotionName");
            return (Criteria) this;
        }

        public Criteria andPromotionNameNotBetween(String value1, String value2) {
            addCriterion("promotion_name not between", value1, value2, "promotionName");
            return (Criteria) this;
        }

        public Criteria andPromotionWayIsNull() {
            addCriterion("promotion_way is null");
            return (Criteria) this;
        }

        public Criteria andPromotionWayIsNotNull() {
            addCriterion("promotion_way is not null");
            return (Criteria) this;
        }

        public Criteria andPromotionWayEqualTo(String value) {
            addCriterion("promotion_way =", value, "promotionWay");
            return (Criteria) this;
        }

        public Criteria andPromotionWayNotEqualTo(String value) {
            addCriterion("promotion_way <>", value, "promotionWay");
            return (Criteria) this;
        }

        public Criteria andPromotionWayGreaterThan(String value) {
            addCriterion("promotion_way >", value, "promotionWay");
            return (Criteria) this;
        }

        public Criteria andPromotionWayGreaterThanOrEqualTo(String value) {
            addCriterion("promotion_way >=", value, "promotionWay");
            return (Criteria) this;
        }

        public Criteria andPromotionWayLessThan(String value) {
            addCriterion("promotion_way <", value, "promotionWay");
            return (Criteria) this;
        }

        public Criteria andPromotionWayLessThanOrEqualTo(String value) {
            addCriterion("promotion_way <=", value, "promotionWay");
            return (Criteria) this;
        }

        public Criteria andPromotionWayLike(String value) {
            addCriterion("promotion_way like", value, "promotionWay");
            return (Criteria) this;
        }

        public Criteria andPromotionWayNotLike(String value) {
            addCriterion("promotion_way not like", value, "promotionWay");
            return (Criteria) this;
        }

        public Criteria andPromotionWayIn(List<String> values) {
            addCriterion("promotion_way in", values, "promotionWay");
            return (Criteria) this;
        }

        public Criteria andPromotionWayNotIn(List<String> values) {
            addCriterion("promotion_way not in", values, "promotionWay");
            return (Criteria) this;
        }

        public Criteria andPromotionWayBetween(String value1, String value2) {
            addCriterion("promotion_way between", value1, value2, "promotionWay");
            return (Criteria) this;
        }

        public Criteria andPromotionWayNotBetween(String value1, String value2) {
            addCriterion("promotion_way not between", value1, value2, "promotionWay");
            return (Criteria) this;
        }

        public Criteria andThresholdInfoIsNull() {
            addCriterion("threshold_info is null");
            return (Criteria) this;
        }

        public Criteria andThresholdInfoIsNotNull() {
            addCriterion("threshold_info is not null");
            return (Criteria) this;
        }

        public Criteria andThresholdInfoEqualTo(String value) {
            addCriterion("threshold_info =", value, "thresholdInfo");
            return (Criteria) this;
        }

        public Criteria andThresholdInfoNotEqualTo(String value) {
            addCriterion("threshold_info <>", value, "thresholdInfo");
            return (Criteria) this;
        }

        public Criteria andThresholdInfoGreaterThan(String value) {
            addCriterion("threshold_info >", value, "thresholdInfo");
            return (Criteria) this;
        }

        public Criteria andThresholdInfoGreaterThanOrEqualTo(String value) {
            addCriterion("threshold_info >=", value, "thresholdInfo");
            return (Criteria) this;
        }

        public Criteria andThresholdInfoLessThan(String value) {
            addCriterion("threshold_info <", value, "thresholdInfo");
            return (Criteria) this;
        }

        public Criteria andThresholdInfoLessThanOrEqualTo(String value) {
            addCriterion("threshold_info <=", value, "thresholdInfo");
            return (Criteria) this;
        }

        public Criteria andThresholdInfoLike(String value) {
            addCriterion("threshold_info like", value, "thresholdInfo");
            return (Criteria) this;
        }

        public Criteria andThresholdInfoNotLike(String value) {
            addCriterion("threshold_info not like", value, "thresholdInfo");
            return (Criteria) this;
        }

        public Criteria andThresholdInfoIn(List<String> values) {
            addCriterion("threshold_info in", values, "thresholdInfo");
            return (Criteria) this;
        }

        public Criteria andThresholdInfoNotIn(List<String> values) {
            addCriterion("threshold_info not in", values, "thresholdInfo");
            return (Criteria) this;
        }

        public Criteria andThresholdInfoBetween(String value1, String value2) {
            addCriterion("threshold_info between", value1, value2, "thresholdInfo");
            return (Criteria) this;
        }

        public Criteria andThresholdInfoNotBetween(String value1, String value2) {
            addCriterion("threshold_info not between", value1, value2, "thresholdInfo");
            return (Criteria) this;
        }

        public Criteria andFavInfoIsNull() {
            addCriterion("fav_info is null");
            return (Criteria) this;
        }

        public Criteria andFavInfoIsNotNull() {
            addCriterion("fav_info is not null");
            return (Criteria) this;
        }

        public Criteria andFavInfoEqualTo(String value) {
            addCriterion("fav_info =", value, "favInfo");
            return (Criteria) this;
        }

        public Criteria andFavInfoNotEqualTo(String value) {
            addCriterion("fav_info <>", value, "favInfo");
            return (Criteria) this;
        }

        public Criteria andFavInfoGreaterThan(String value) {
            addCriterion("fav_info >", value, "favInfo");
            return (Criteria) this;
        }

        public Criteria andFavInfoGreaterThanOrEqualTo(String value) {
            addCriterion("fav_info >=", value, "favInfo");
            return (Criteria) this;
        }

        public Criteria andFavInfoLessThan(String value) {
            addCriterion("fav_info <", value, "favInfo");
            return (Criteria) this;
        }

        public Criteria andFavInfoLessThanOrEqualTo(String value) {
            addCriterion("fav_info <=", value, "favInfo");
            return (Criteria) this;
        }

        public Criteria andFavInfoLike(String value) {
            addCriterion("fav_info like", value, "favInfo");
            return (Criteria) this;
        }

        public Criteria andFavInfoNotLike(String value) {
            addCriterion("fav_info not like", value, "favInfo");
            return (Criteria) this;
        }

        public Criteria andFavInfoIn(List<String> values) {
            addCriterion("fav_info in", values, "favInfo");
            return (Criteria) this;
        }

        public Criteria andFavInfoNotIn(List<String> values) {
            addCriterion("fav_info not in", values, "favInfo");
            return (Criteria) this;
        }

        public Criteria andFavInfoBetween(String value1, String value2) {
            addCriterion("fav_info between", value1, value2, "favInfo");
            return (Criteria) this;
        }

        public Criteria andFavInfoNotBetween(String value1, String value2) {
            addCriterion("fav_info not between", value1, value2, "favInfo");
            return (Criteria) this;
        }

        public Criteria andRecommendReasonIsNull() {
            addCriterion("recommend_reason is null");
            return (Criteria) this;
        }

        public Criteria andRecommendReasonIsNotNull() {
            addCriterion("recommend_reason is not null");
            return (Criteria) this;
        }

        public Criteria andRecommendReasonEqualTo(String value) {
            addCriterion("recommend_reason =", value, "recommendReason");
            return (Criteria) this;
        }

        public Criteria andRecommendReasonNotEqualTo(String value) {
            addCriterion("recommend_reason <>", value, "recommendReason");
            return (Criteria) this;
        }

        public Criteria andRecommendReasonGreaterThan(String value) {
            addCriterion("recommend_reason >", value, "recommendReason");
            return (Criteria) this;
        }

        public Criteria andRecommendReasonGreaterThanOrEqualTo(String value) {
            addCriterion("recommend_reason >=", value, "recommendReason");
            return (Criteria) this;
        }

        public Criteria andRecommendReasonLessThan(String value) {
            addCriterion("recommend_reason <", value, "recommendReason");
            return (Criteria) this;
        }

        public Criteria andRecommendReasonLessThanOrEqualTo(String value) {
            addCriterion("recommend_reason <=", value, "recommendReason");
            return (Criteria) this;
        }

        public Criteria andRecommendReasonLike(String value) {
            addCriterion("recommend_reason like", value, "recommendReason");
            return (Criteria) this;
        }

        public Criteria andRecommendReasonNotLike(String value) {
            addCriterion("recommend_reason not like", value, "recommendReason");
            return (Criteria) this;
        }

        public Criteria andRecommendReasonIn(List<String> values) {
            addCriterion("recommend_reason in", values, "recommendReason");
            return (Criteria) this;
        }

        public Criteria andRecommendReasonNotIn(List<String> values) {
            addCriterion("recommend_reason not in", values, "recommendReason");
            return (Criteria) this;
        }

        public Criteria andRecommendReasonBetween(String value1, String value2) {
            addCriterion("recommend_reason between", value1, value2, "recommendReason");
            return (Criteria) this;
        }

        public Criteria andRecommendReasonNotBetween(String value1, String value2) {
            addCriterion("recommend_reason not between", value1, value2, "recommendReason");
            return (Criteria) this;
        }

        public Criteria andCompositeNewIsNull() {
            addCriterion("composite_new is null");
            return (Criteria) this;
        }

        public Criteria andCompositeNewIsNotNull() {
            addCriterion("composite_new is not null");
            return (Criteria) this;
        }

        public Criteria andCompositeNewEqualTo(Byte value) {
            addCriterion("composite_new =", value, "compositeNew");
            return (Criteria) this;
        }

        public Criteria andCompositeNewNotEqualTo(Byte value) {
            addCriterion("composite_new <>", value, "compositeNew");
            return (Criteria) this;
        }

        public Criteria andCompositeNewGreaterThan(Byte value) {
            addCriterion("composite_new >", value, "compositeNew");
            return (Criteria) this;
        }

        public Criteria andCompositeNewGreaterThanOrEqualTo(Byte value) {
            addCriterion("composite_new >=", value, "compositeNew");
            return (Criteria) this;
        }

        public Criteria andCompositeNewLessThan(Byte value) {
            addCriterion("composite_new <", value, "compositeNew");
            return (Criteria) this;
        }

        public Criteria andCompositeNewLessThanOrEqualTo(Byte value) {
            addCriterion("composite_new <=", value, "compositeNew");
            return (Criteria) this;
        }

        public Criteria andCompositeNewIn(List<Byte> values) {
            addCriterion("composite_new in", values, "compositeNew");
            return (Criteria) this;
        }

        public Criteria andCompositeNewNotIn(List<Byte> values) {
            addCriterion("composite_new not in", values, "compositeNew");
            return (Criteria) this;
        }

        public Criteria andCompositeNewBetween(Byte value1, Byte value2) {
            addCriterion("composite_new between", value1, value2, "compositeNew");
            return (Criteria) this;
        }

        public Criteria andCompositeNewNotBetween(Byte value1, Byte value2) {
            addCriterion("composite_new not between", value1, value2, "compositeNew");
            return (Criteria) this;
        }

        public Criteria andIndicationsIsNull() {
            addCriterion("indications is null");
            return (Criteria) this;
        }

        public Criteria andIndicationsIsNotNull() {
            addCriterion("indications is not null");
            return (Criteria) this;
        }

        public Criteria andIndicationsEqualTo(String value) {
            addCriterion("indications =", value, "indications");
            return (Criteria) this;
        }

        public Criteria andIndicationsNotEqualTo(String value) {
            addCriterion("indications <>", value, "indications");
            return (Criteria) this;
        }

        public Criteria andIndicationsGreaterThan(String value) {
            addCriterion("indications >", value, "indications");
            return (Criteria) this;
        }

        public Criteria andIndicationsGreaterThanOrEqualTo(String value) {
            addCriterion("indications >=", value, "indications");
            return (Criteria) this;
        }

        public Criteria andIndicationsLessThan(String value) {
            addCriterion("indications <", value, "indications");
            return (Criteria) this;
        }

        public Criteria andIndicationsLessThanOrEqualTo(String value) {
            addCriterion("indications <=", value, "indications");
            return (Criteria) this;
        }

        public Criteria andIndicationsLike(String value) {
            addCriterion("indications like", value, "indications");
            return (Criteria) this;
        }

        public Criteria andIndicationsNotLike(String value) {
            addCriterion("indications not like", value, "indications");
            return (Criteria) this;
        }

        public Criteria andIndicationsIn(List<String> values) {
            addCriterion("indications in", values, "indications");
            return (Criteria) this;
        }

        public Criteria andIndicationsNotIn(List<String> values) {
            addCriterion("indications not in", values, "indications");
            return (Criteria) this;
        }

        public Criteria andIndicationsBetween(String value1, String value2) {
            addCriterion("indications between", value1, value2, "indications");
            return (Criteria) this;
        }

        public Criteria andIndicationsNotBetween(String value1, String value2) {
            addCriterion("indications not between", value1, value2, "indications");
            return (Criteria) this;
        }

        public Criteria andPictureUrlIsNull() {
            addCriterion("picture_url is null");
            return (Criteria) this;
        }

        public Criteria andPictureUrlIsNotNull() {
            addCriterion("picture_url is not null");
            return (Criteria) this;
        }

        public Criteria andPictureUrlEqualTo(String value) {
            addCriterion("picture_url =", value, "pictureUrl");
            return (Criteria) this;
        }

        public Criteria andPictureUrlNotEqualTo(String value) {
            addCriterion("picture_url <>", value, "pictureUrl");
            return (Criteria) this;
        }

        public Criteria andPictureUrlGreaterThan(String value) {
            addCriterion("picture_url >", value, "pictureUrl");
            return (Criteria) this;
        }

        public Criteria andPictureUrlGreaterThanOrEqualTo(String value) {
            addCriterion("picture_url >=", value, "pictureUrl");
            return (Criteria) this;
        }

        public Criteria andPictureUrlLessThan(String value) {
            addCriterion("picture_url <", value, "pictureUrl");
            return (Criteria) this;
        }

        public Criteria andPictureUrlLessThanOrEqualTo(String value) {
            addCriterion("picture_url <=", value, "pictureUrl");
            return (Criteria) this;
        }

        public Criteria andPictureUrlLike(String value) {
            addCriterion("picture_url like", value, "pictureUrl");
            return (Criteria) this;
        }

        public Criteria andPictureUrlNotLike(String value) {
            addCriterion("picture_url not like", value, "pictureUrl");
            return (Criteria) this;
        }

        public Criteria andPictureUrlIn(List<String> values) {
            addCriterion("picture_url in", values, "pictureUrl");
            return (Criteria) this;
        }

        public Criteria andPictureUrlNotIn(List<String> values) {
            addCriterion("picture_url not in", values, "pictureUrl");
            return (Criteria) this;
        }

        public Criteria andPictureUrlBetween(String value1, String value2) {
            addCriterion("picture_url between", value1, value2, "pictureUrl");
            return (Criteria) this;
        }

        public Criteria andPictureUrlNotBetween(String value1, String value2) {
            addCriterion("picture_url not between", value1, value2, "pictureUrl");
            return (Criteria) this;
        }

        public Criteria andSuggestTypeIsNull() {
            addCriterion("suggest_type is null");
            return (Criteria) this;
        }

        public Criteria andSuggestTypeIsNotNull() {
            addCriterion("suggest_type is not null");
            return (Criteria) this;
        }

        public Criteria andSuggestTypeEqualTo(Byte value) {
            addCriterion("suggest_type =", value, "suggestType");
            return (Criteria) this;
        }

        public Criteria andSuggestTypeNotEqualTo(Byte value) {
            addCriterion("suggest_type <>", value, "suggestType");
            return (Criteria) this;
        }

        public Criteria andSuggestTypeGreaterThan(Byte value) {
            addCriterion("suggest_type >", value, "suggestType");
            return (Criteria) this;
        }

        public Criteria andSuggestTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("suggest_type >=", value, "suggestType");
            return (Criteria) this;
        }

        public Criteria andSuggestTypeLessThan(Byte value) {
            addCriterion("suggest_type <", value, "suggestType");
            return (Criteria) this;
        }

        public Criteria andSuggestTypeLessThanOrEqualTo(Byte value) {
            addCriterion("suggest_type <=", value, "suggestType");
            return (Criteria) this;
        }

        public Criteria andSuggestTypeIn(List<Byte> values) {
            addCriterion("suggest_type in", values, "suggestType");
            return (Criteria) this;
        }

        public Criteria andSuggestTypeNotIn(List<Byte> values) {
            addCriterion("suggest_type not in", values, "suggestType");
            return (Criteria) this;
        }

        public Criteria andSuggestTypeBetween(Byte value1, Byte value2) {
            addCriterion("suggest_type between", value1, value2, "suggestType");
            return (Criteria) this;
        }

        public Criteria andSuggestTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("suggest_type not between", value1, value2, "suggestType");
            return (Criteria) this;
        }

        public Criteria andDealSuggestIsNull() {
            addCriterion("deal_suggest is null");
            return (Criteria) this;
        }

        public Criteria andDealSuggestIsNotNull() {
            addCriterion("deal_suggest is not null");
            return (Criteria) this;
        }

        public Criteria andDealSuggestEqualTo(Byte value) {
            addCriterion("deal_suggest =", value, "dealSuggest");
            return (Criteria) this;
        }

        public Criteria andDealSuggestNotEqualTo(Byte value) {
            addCriterion("deal_suggest <>", value, "dealSuggest");
            return (Criteria) this;
        }

        public Criteria andDealSuggestGreaterThan(Byte value) {
            addCriterion("deal_suggest >", value, "dealSuggest");
            return (Criteria) this;
        }

        public Criteria andDealSuggestGreaterThanOrEqualTo(Byte value) {
            addCriterion("deal_suggest >=", value, "dealSuggest");
            return (Criteria) this;
        }

        public Criteria andDealSuggestLessThan(Byte value) {
            addCriterion("deal_suggest <", value, "dealSuggest");
            return (Criteria) this;
        }

        public Criteria andDealSuggestLessThanOrEqualTo(Byte value) {
            addCriterion("deal_suggest <=", value, "dealSuggest");
            return (Criteria) this;
        }

        public Criteria andDealSuggestIn(List<Byte> values) {
            addCriterion("deal_suggest in", values, "dealSuggest");
            return (Criteria) this;
        }

        public Criteria andDealSuggestNotIn(List<Byte> values) {
            addCriterion("deal_suggest not in", values, "dealSuggest");
            return (Criteria) this;
        }

        public Criteria andDealSuggestBetween(Byte value1, Byte value2) {
            addCriterion("deal_suggest between", value1, value2, "dealSuggest");
            return (Criteria) this;
        }

        public Criteria andDealSuggestNotBetween(Byte value1, Byte value2) {
            addCriterion("deal_suggest not between", value1, value2, "dealSuggest");
            return (Criteria) this;
        }

        public Criteria andComplementQtyIsNull() {
            addCriterion("complement_qty is null");
            return (Criteria) this;
        }

        public Criteria andComplementQtyIsNotNull() {
            addCriterion("complement_qty is not null");
            return (Criteria) this;
        }

        public Criteria andComplementQtyEqualTo(Integer value) {
            addCriterion("complement_qty =", value, "complementQty");
            return (Criteria) this;
        }

        public Criteria andComplementQtyNotEqualTo(Integer value) {
            addCriterion("complement_qty <>", value, "complementQty");
            return (Criteria) this;
        }

        public Criteria andComplementQtyGreaterThan(Integer value) {
            addCriterion("complement_qty >", value, "complementQty");
            return (Criteria) this;
        }

        public Criteria andComplementQtyGreaterThanOrEqualTo(Integer value) {
            addCriterion("complement_qty >=", value, "complementQty");
            return (Criteria) this;
        }

        public Criteria andComplementQtyLessThan(Integer value) {
            addCriterion("complement_qty <", value, "complementQty");
            return (Criteria) this;
        }

        public Criteria andComplementQtyLessThanOrEqualTo(Integer value) {
            addCriterion("complement_qty <=", value, "complementQty");
            return (Criteria) this;
        }

        public Criteria andComplementQtyIn(List<Integer> values) {
            addCriterion("complement_qty in", values, "complementQty");
            return (Criteria) this;
        }

        public Criteria andComplementQtyNotIn(List<Integer> values) {
            addCriterion("complement_qty not in", values, "complementQty");
            return (Criteria) this;
        }

        public Criteria andComplementQtyBetween(Integer value1, Integer value2) {
            addCriterion("complement_qty between", value1, value2, "complementQty");
            return (Criteria) this;
        }

        public Criteria andComplementQtyNotBetween(Integer value1, Integer value2) {
            addCriterion("complement_qty not between", value1, value2, "complementQty");
            return (Criteria) this;
        }

        public Criteria andPopWinIsNull() {
            addCriterion("pop_win is null");
            return (Criteria) this;
        }

        public Criteria andPopWinIsNotNull() {
            addCriterion("pop_win is not null");
            return (Criteria) this;
        }

        public Criteria andPopWinEqualTo(Byte value) {
            addCriterion("pop_win =", value, "popWin");
            return (Criteria) this;
        }

        public Criteria andPopWinNotEqualTo(Byte value) {
            addCriterion("pop_win <>", value, "popWin");
            return (Criteria) this;
        }

        public Criteria andPopWinGreaterThan(Byte value) {
            addCriterion("pop_win >", value, "popWin");
            return (Criteria) this;
        }

        public Criteria andPopWinGreaterThanOrEqualTo(Byte value) {
            addCriterion("pop_win >=", value, "popWin");
            return (Criteria) this;
        }

        public Criteria andPopWinLessThan(Byte value) {
            addCriterion("pop_win <", value, "popWin");
            return (Criteria) this;
        }

        public Criteria andPopWinLessThanOrEqualTo(Byte value) {
            addCriterion("pop_win <=", value, "popWin");
            return (Criteria) this;
        }

        public Criteria andPopWinIn(List<Byte> values) {
            addCriterion("pop_win in", values, "popWin");
            return (Criteria) this;
        }

        public Criteria andPopWinNotIn(List<Byte> values) {
            addCriterion("pop_win not in", values, "popWin");
            return (Criteria) this;
        }

        public Criteria andPopWinBetween(Byte value1, Byte value2) {
            addCriterion("pop_win between", value1, value2, "popWin");
            return (Criteria) this;
        }

        public Criteria andPopWinNotBetween(Byte value1, Byte value2) {
            addCriterion("pop_win not between", value1, value2, "popWin");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("`status` is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("`status` is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Byte value) {
            addCriterion("`status` =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Byte value) {
            addCriterion("`status` <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Byte value) {
            addCriterion("`status` >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("`status` >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Byte value) {
            addCriterion("`status` <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Byte value) {
            addCriterion("`status` <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Byte> values) {
            addCriterion("`status` in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Byte> values) {
            addCriterion("`status` not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Byte value1, Byte value2) {
            addCriterion("`status` between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("`status` not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIsNull() {
            addCriterion("gmt_update is null");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIsNotNull() {
            addCriterion("gmt_update is not null");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateEqualTo(Date value) {
            addCriterion("gmt_update =", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotEqualTo(Date value) {
            addCriterion("gmt_update <>", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateGreaterThan(Date value) {
            addCriterion("gmt_update >", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_update >=", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateLessThan(Date value) {
            addCriterion("gmt_update <", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_update <=", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIn(List<Date> values) {
            addCriterion("gmt_update in", values, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotIn(List<Date> values) {
            addCriterion("gmt_update not in", values, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateBetween(Date value1, Date value2) {
            addCriterion("gmt_update between", value1, value2, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_update not between", value1, value2, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andExtendIsNull() {
            addCriterion("extend is null");
            return (Criteria) this;
        }

        public Criteria andExtendIsNotNull() {
            addCriterion("extend is not null");
            return (Criteria) this;
        }

        public Criteria andExtendEqualTo(String value) {
            addCriterion("extend =", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotEqualTo(String value) {
            addCriterion("extend <>", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendGreaterThan(String value) {
            addCriterion("extend >", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendGreaterThanOrEqualTo(String value) {
            addCriterion("extend >=", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLessThan(String value) {
            addCriterion("extend <", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLessThanOrEqualTo(String value) {
            addCriterion("extend <=", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLike(String value) {
            addCriterion("extend like", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotLike(String value) {
            addCriterion("extend not like", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendIn(List<String> values) {
            addCriterion("extend in", values, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotIn(List<String> values) {
            addCriterion("extend not in", values, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendBetween(String value1, String value2) {
            addCriterion("extend between", value1, value2, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotBetween(String value1, String value2) {
            addCriterion("extend not between", value1, value2, "extend");
            return (Criteria) this;
        }

        public Criteria andVersionIsNull() {
            addCriterion("version is null");
            return (Criteria) this;
        }

        public Criteria andVersionIsNotNull() {
            addCriterion("version is not null");
            return (Criteria) this;
        }

        public Criteria andVersionEqualTo(Integer value) {
            addCriterion("version =", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotEqualTo(Integer value) {
            addCriterion("version <>", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThan(Integer value) {
            addCriterion("version >", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThanOrEqualTo(Integer value) {
            addCriterion("version >=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThan(Integer value) {
            addCriterion("version <", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThanOrEqualTo(Integer value) {
            addCriterion("version <=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionIn(List<Integer> values) {
            addCriterion("version in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotIn(List<Integer> values) {
            addCriterion("version not in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionBetween(Integer value1, Integer value2) {
            addCriterion("version between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotBetween(Integer value1, Integer value2) {
            addCriterion("version not between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("created_by is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("created_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(Long value) {
            addCriterion("created_by =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(Long value) {
            addCriterion("created_by <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(Long value) {
            addCriterion("created_by >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(Long value) {
            addCriterion("created_by >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(Long value) {
            addCriterion("created_by <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(Long value) {
            addCriterion("created_by <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<Long> values) {
            addCriterion("created_by in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<Long> values) {
            addCriterion("created_by not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(Long value1, Long value2) {
            addCriterion("created_by between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(Long value1, Long value2) {
            addCriterion("created_by not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedNameIsNull() {
            addCriterion("created_name is null");
            return (Criteria) this;
        }

        public Criteria andCreatedNameIsNotNull() {
            addCriterion("created_name is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedNameEqualTo(String value) {
            addCriterion("created_name =", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotEqualTo(String value) {
            addCriterion("created_name <>", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameGreaterThan(String value) {
            addCriterion("created_name >", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameGreaterThanOrEqualTo(String value) {
            addCriterion("created_name >=", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameLessThan(String value) {
            addCriterion("created_name <", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameLessThanOrEqualTo(String value) {
            addCriterion("created_name <=", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameLike(String value) {
            addCriterion("created_name like", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotLike(String value) {
            addCriterion("created_name not like", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameIn(List<String> values) {
            addCriterion("created_name in", values, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotIn(List<String> values) {
            addCriterion("created_name not in", values, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameBetween(String value1, String value2) {
            addCriterion("created_name between", value1, value2, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotBetween(String value1, String value2) {
            addCriterion("created_name not between", value1, value2, "createdName");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNull() {
            addCriterion("updated_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNotNull() {
            addCriterion("updated_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByEqualTo(Long value) {
            addCriterion("updated_by =", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotEqualTo(Long value) {
            addCriterion("updated_by <>", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThan(Long value) {
            addCriterion("updated_by >", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThanOrEqualTo(Long value) {
            addCriterion("updated_by >=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThan(Long value) {
            addCriterion("updated_by <", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThanOrEqualTo(Long value) {
            addCriterion("updated_by <=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIn(List<Long> values) {
            addCriterion("updated_by in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotIn(List<Long> values) {
            addCriterion("updated_by not in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByBetween(Long value1, Long value2) {
            addCriterion("updated_by between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotBetween(Long value1, Long value2) {
            addCriterion("updated_by not between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameIsNull() {
            addCriterion("updated_name is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameIsNotNull() {
            addCriterion("updated_name is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameEqualTo(String value) {
            addCriterion("updated_name =", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotEqualTo(String value) {
            addCriterion("updated_name <>", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameGreaterThan(String value) {
            addCriterion("updated_name >", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameGreaterThanOrEqualTo(String value) {
            addCriterion("updated_name >=", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameLessThan(String value) {
            addCriterion("updated_name <", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameLessThanOrEqualTo(String value) {
            addCriterion("updated_name <=", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameLike(String value) {
            addCriterion("updated_name like", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotLike(String value) {
            addCriterion("updated_name not like", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameIn(List<String> values) {
            addCriterion("updated_name in", values, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotIn(List<String> values) {
            addCriterion("updated_name not in", values, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameBetween(String value1, String value2) {
            addCriterion("updated_name between", value1, value2, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotBetween(String value1, String value2) {
            addCriterion("updated_name not between", value1, value2, "updatedName");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}