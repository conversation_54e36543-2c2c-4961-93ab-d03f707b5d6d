package com.cowell.iscm.entityTidb;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * iscm_auto_register
 * <AUTHOR>
public class IscmAutoRegister implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 数据类型,1:滞销,2:大库存,3:大库存&滞销商品调拨
     */
    private Byte dataType;

    /**
     * 登记类型,1:近效期调拨,2:大库存、滞销商品调拨,3:断货商品调拨
     */
    private Byte registerType;

    /**
     * 登记单号
     */
    private String registerOrderNo;

    /**
     * 公司bdp编码
     */
    private String companyBdpCode;

    /**
     * 门店MDM编码
     */
    private String storeCode;

    /**
     * 商品编码
     */
    private String goodsNo;

    /**
     * 批号
     */
    private String batchNo;

    /**
     * 生产厂家
     */
    private String manufacturer;

    /**
     * 登记数量
     */
    private BigDecimal registerQuantity;

    /**
     * 非效期库存数量
     */
    private BigDecimal nonValidityStockQuantity;

    /**
     * 库存上限
     */
    private BigDecimal stockUpperLimit;

    /**
     * 库存下限
     */
    private BigDecimal stockLowerLimit;

    /**
     * 无税库存成本金额
     */
    private BigDecimal noTaxInventoryCostAmount;

    /**
     * 未动销天数
     */
    private Integer nonSaleDays;

    /**
     * 综合日均销量
     */
    private BigDecimal synthesizeAverageDailySales;

    /**
     * 近30天销售数量
     */
    private BigDecimal thirtySalesQuantity;

    /**
     * 近30天销售次数
     */
    private Integer thirtySalesCount;

    /**
     * 总库存数量
     */
    private BigDecimal stockQuantity;

    /**
     * 最小陈列量
     */
    private BigDecimal minDisplayQuantity;

    /**
     * 预计可销售天数
     */
    private BigDecimal expectSaleDays;

    /**
     * 处理状态 1:已生成建议 2：未生成
     */
    private Byte dealStatus;

    /**
     * 状态(-1删除，0正常)
     */
    private Byte status;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新时间
     */
    private Date gmtUpdate;

    /**
     * 扩展字段
     */
    private String extend;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 创建人ID
     */
    private Long createdBy;

    /**
     * 创建人
     */
    private String createdName;

    /**
     * 更新人ID
     */
    private Long updatedBy;

    /**
     * 更新人
     */
    private String updatedName;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Byte getDataType() {
        return dataType;
    }

    public void setDataType(Byte dataType) {
        this.dataType = dataType;
    }

    public Byte getRegisterType() {
        return registerType;
    }

    public void setRegisterType(Byte registerType) {
        this.registerType = registerType;
    }

    public String getRegisterOrderNo() {
        return registerOrderNo;
    }

    public void setRegisterOrderNo(String registerOrderNo) {
        this.registerOrderNo = registerOrderNo;
    }

    public String getCompanyBdpCode() {
        return companyBdpCode;
    }

    public void setCompanyBdpCode(String companyBdpCode) {
        this.companyBdpCode = companyBdpCode;
    }

    public String getStoreCode() {
        return storeCode;
    }

    public void setStoreCode(String storeCode) {
        this.storeCode = storeCode;
    }

    public String getGoodsNo() {
        return goodsNo;
    }

    public void setGoodsNo(String goodsNo) {
        this.goodsNo = goodsNo;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    public BigDecimal getRegisterQuantity() {
        return registerQuantity;
    }

    public void setRegisterQuantity(BigDecimal registerQuantity) {
        this.registerQuantity = registerQuantity;
    }

    public BigDecimal getNonValidityStockQuantity() {
        return nonValidityStockQuantity;
    }

    public void setNonValidityStockQuantity(BigDecimal nonValidityStockQuantity) {
        this.nonValidityStockQuantity = nonValidityStockQuantity;
    }

    public BigDecimal getStockUpperLimit() {
        return stockUpperLimit;
    }

    public void setStockUpperLimit(BigDecimal stockUpperLimit) {
        this.stockUpperLimit = stockUpperLimit;
    }

    public BigDecimal getStockLowerLimit() {
        return stockLowerLimit;
    }

    public void setStockLowerLimit(BigDecimal stockLowerLimit) {
        this.stockLowerLimit = stockLowerLimit;
    }

    public BigDecimal getNoTaxInventoryCostAmount() {
        return noTaxInventoryCostAmount;
    }

    public void setNoTaxInventoryCostAmount(BigDecimal noTaxInventoryCostAmount) {
        this.noTaxInventoryCostAmount = noTaxInventoryCostAmount;
    }

    public Integer getNonSaleDays() {
        return nonSaleDays;
    }

    public void setNonSaleDays(Integer nonSaleDays) {
        this.nonSaleDays = nonSaleDays;
    }

    public BigDecimal getSynthesizeAverageDailySales() {
        return synthesizeAverageDailySales;
    }

    public void setSynthesizeAverageDailySales(BigDecimal synthesizeAverageDailySales) {
        this.synthesizeAverageDailySales = synthesizeAverageDailySales;
    }

    public BigDecimal getThirtySalesQuantity() {
        return thirtySalesQuantity;
    }

    public void setThirtySalesQuantity(BigDecimal thirtySalesQuantity) {
        this.thirtySalesQuantity = thirtySalesQuantity;
    }

    public Integer getThirtySalesCount() {
        return thirtySalesCount;
    }

    public void setThirtySalesCount(Integer thirtySalesCount) {
        this.thirtySalesCount = thirtySalesCount;
    }

    public BigDecimal getStockQuantity() {
        return stockQuantity;
    }

    public void setStockQuantity(BigDecimal stockQuantity) {
        this.stockQuantity = stockQuantity;
    }

    public BigDecimal getMinDisplayQuantity() {
        return minDisplayQuantity;
    }

    public void setMinDisplayQuantity(BigDecimal minDisplayQuantity) {
        this.minDisplayQuantity = minDisplayQuantity;
    }

    public BigDecimal getExpectSaleDays() {
        return expectSaleDays;
    }

    public void setExpectSaleDays(BigDecimal expectSaleDays) {
        this.expectSaleDays = expectSaleDays;
    }

    public Byte getDealStatus() {
        return dealStatus;
    }

    public void setDealStatus(Byte dealStatus) {
        this.dealStatus = dealStatus;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtUpdate() {
        return gmtUpdate;
    }

    public void setGmtUpdate(Date gmtUpdate) {
        this.gmtUpdate = gmtUpdate;
    }

    public String getExtend() {
        return extend;
    }

    public void setExtend(String extend) {
        this.extend = extend;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Long getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    public String getCreatedName() {
        return createdName;
    }

    public void setCreatedName(String createdName) {
        this.createdName = createdName;
    }

    public Long getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(Long updatedBy) {
        this.updatedBy = updatedBy;
    }

    public String getUpdatedName() {
        return updatedName;
    }

    public void setUpdatedName(String updatedName) {
        this.updatedName = updatedName;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        IscmAutoRegister other = (IscmAutoRegister) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getDataType() == null ? other.getDataType() == null : this.getDataType().equals(other.getDataType()))
            && (this.getRegisterType() == null ? other.getRegisterType() == null : this.getRegisterType().equals(other.getRegisterType()))
            && (this.getRegisterOrderNo() == null ? other.getRegisterOrderNo() == null : this.getRegisterOrderNo().equals(other.getRegisterOrderNo()))
            && (this.getCompanyBdpCode() == null ? other.getCompanyBdpCode() == null : this.getCompanyBdpCode().equals(other.getCompanyBdpCode()))
            && (this.getStoreCode() == null ? other.getStoreCode() == null : this.getStoreCode().equals(other.getStoreCode()))
            && (this.getGoodsNo() == null ? other.getGoodsNo() == null : this.getGoodsNo().equals(other.getGoodsNo()))
            && (this.getBatchNo() == null ? other.getBatchNo() == null : this.getBatchNo().equals(other.getBatchNo()))
            && (this.getManufacturer() == null ? other.getManufacturer() == null : this.getManufacturer().equals(other.getManufacturer()))
            && (this.getRegisterQuantity() == null ? other.getRegisterQuantity() == null : this.getRegisterQuantity().equals(other.getRegisterQuantity()))
            && (this.getNonValidityStockQuantity() == null ? other.getNonValidityStockQuantity() == null : this.getNonValidityStockQuantity().equals(other.getNonValidityStockQuantity()))
            && (this.getStockUpperLimit() == null ? other.getStockUpperLimit() == null : this.getStockUpperLimit().equals(other.getStockUpperLimit()))
            && (this.getStockLowerLimit() == null ? other.getStockLowerLimit() == null : this.getStockLowerLimit().equals(other.getStockLowerLimit()))
            && (this.getNoTaxInventoryCostAmount() == null ? other.getNoTaxInventoryCostAmount() == null : this.getNoTaxInventoryCostAmount().equals(other.getNoTaxInventoryCostAmount()))
            && (this.getNonSaleDays() == null ? other.getNonSaleDays() == null : this.getNonSaleDays().equals(other.getNonSaleDays()))
            && (this.getSynthesizeAverageDailySales() == null ? other.getSynthesizeAverageDailySales() == null : this.getSynthesizeAverageDailySales().equals(other.getSynthesizeAverageDailySales()))
            && (this.getThirtySalesQuantity() == null ? other.getThirtySalesQuantity() == null : this.getThirtySalesQuantity().equals(other.getThirtySalesQuantity()))
            && (this.getThirtySalesCount() == null ? other.getThirtySalesCount() == null : this.getThirtySalesCount().equals(other.getThirtySalesCount()))
            && (this.getStockQuantity() == null ? other.getStockQuantity() == null : this.getStockQuantity().equals(other.getStockQuantity()))
            && (this.getMinDisplayQuantity() == null ? other.getMinDisplayQuantity() == null : this.getMinDisplayQuantity().equals(other.getMinDisplayQuantity()))
            && (this.getExpectSaleDays() == null ? other.getExpectSaleDays() == null : this.getExpectSaleDays().equals(other.getExpectSaleDays()))
            && (this.getDealStatus() == null ? other.getDealStatus() == null : this.getDealStatus().equals(other.getDealStatus()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getGmtCreate() == null ? other.getGmtCreate() == null : this.getGmtCreate().equals(other.getGmtCreate()))
            && (this.getGmtUpdate() == null ? other.getGmtUpdate() == null : this.getGmtUpdate().equals(other.getGmtUpdate()))
            && (this.getExtend() == null ? other.getExtend() == null : this.getExtend().equals(other.getExtend()))
            && (this.getVersion() == null ? other.getVersion() == null : this.getVersion().equals(other.getVersion()))
            && (this.getCreatedBy() == null ? other.getCreatedBy() == null : this.getCreatedBy().equals(other.getCreatedBy()))
            && (this.getCreatedName() == null ? other.getCreatedName() == null : this.getCreatedName().equals(other.getCreatedName()))
            && (this.getUpdatedBy() == null ? other.getUpdatedBy() == null : this.getUpdatedBy().equals(other.getUpdatedBy()))
            && (this.getUpdatedName() == null ? other.getUpdatedName() == null : this.getUpdatedName().equals(other.getUpdatedName()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getDataType() == null) ? 0 : getDataType().hashCode());
        result = prime * result + ((getRegisterType() == null) ? 0 : getRegisterType().hashCode());
        result = prime * result + ((getRegisterOrderNo() == null) ? 0 : getRegisterOrderNo().hashCode());
        result = prime * result + ((getCompanyBdpCode() == null) ? 0 : getCompanyBdpCode().hashCode());
        result = prime * result + ((getStoreCode() == null) ? 0 : getStoreCode().hashCode());
        result = prime * result + ((getGoodsNo() == null) ? 0 : getGoodsNo().hashCode());
        result = prime * result + ((getBatchNo() == null) ? 0 : getBatchNo().hashCode());
        result = prime * result + ((getManufacturer() == null) ? 0 : getManufacturer().hashCode());
        result = prime * result + ((getRegisterQuantity() == null) ? 0 : getRegisterQuantity().hashCode());
        result = prime * result + ((getNonValidityStockQuantity() == null) ? 0 : getNonValidityStockQuantity().hashCode());
        result = prime * result + ((getStockUpperLimit() == null) ? 0 : getStockUpperLimit().hashCode());
        result = prime * result + ((getStockLowerLimit() == null) ? 0 : getStockLowerLimit().hashCode());
        result = prime * result + ((getNoTaxInventoryCostAmount() == null) ? 0 : getNoTaxInventoryCostAmount().hashCode());
        result = prime * result + ((getNonSaleDays() == null) ? 0 : getNonSaleDays().hashCode());
        result = prime * result + ((getSynthesizeAverageDailySales() == null) ? 0 : getSynthesizeAverageDailySales().hashCode());
        result = prime * result + ((getThirtySalesQuantity() == null) ? 0 : getThirtySalesQuantity().hashCode());
        result = prime * result + ((getThirtySalesCount() == null) ? 0 : getThirtySalesCount().hashCode());
        result = prime * result + ((getStockQuantity() == null) ? 0 : getStockQuantity().hashCode());
        result = prime * result + ((getMinDisplayQuantity() == null) ? 0 : getMinDisplayQuantity().hashCode());
        result = prime * result + ((getExpectSaleDays() == null) ? 0 : getExpectSaleDays().hashCode());
        result = prime * result + ((getDealStatus() == null) ? 0 : getDealStatus().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getGmtCreate() == null) ? 0 : getGmtCreate().hashCode());
        result = prime * result + ((getGmtUpdate() == null) ? 0 : getGmtUpdate().hashCode());
        result = prime * result + ((getExtend() == null) ? 0 : getExtend().hashCode());
        result = prime * result + ((getVersion() == null) ? 0 : getVersion().hashCode());
        result = prime * result + ((getCreatedBy() == null) ? 0 : getCreatedBy().hashCode());
        result = prime * result + ((getCreatedName() == null) ? 0 : getCreatedName().hashCode());
        result = prime * result + ((getUpdatedBy() == null) ? 0 : getUpdatedBy().hashCode());
        result = prime * result + ((getUpdatedName() == null) ? 0 : getUpdatedName().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", dataType=").append(dataType);
        sb.append(", registerType=").append(registerType);
        sb.append(", registerOrderNo=").append(registerOrderNo);
        sb.append(", companyBdpCode=").append(companyBdpCode);
        sb.append(", storeCode=").append(storeCode);
        sb.append(", goodsNo=").append(goodsNo);
        sb.append(", batchNo=").append(batchNo);
        sb.append(", manufacturer=").append(manufacturer);
        sb.append(", registerQuantity=").append(registerQuantity);
        sb.append(", nonValidityStockQuantity=").append(nonValidityStockQuantity);
        sb.append(", stockUpperLimit=").append(stockUpperLimit);
        sb.append(", stockLowerLimit=").append(stockLowerLimit);
        sb.append(", noTaxInventoryCostAmount=").append(noTaxInventoryCostAmount);
        sb.append(", nonSaleDays=").append(nonSaleDays);
        sb.append(", synthesizeAverageDailySales=").append(synthesizeAverageDailySales);
        sb.append(", thirtySalesQuantity=").append(thirtySalesQuantity);
        sb.append(", thirtySalesCount=").append(thirtySalesCount);
        sb.append(", stockQuantity=").append(stockQuantity);
        sb.append(", minDisplayQuantity=").append(minDisplayQuantity);
        sb.append(", expectSaleDays=").append(expectSaleDays);
        sb.append(", dealStatus=").append(dealStatus);
        sb.append(", status=").append(status);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", gmtUpdate=").append(gmtUpdate);
        sb.append(", extend=").append(extend);
        sb.append(", version=").append(version);
        sb.append(", createdBy=").append(createdBy);
        sb.append(", createdName=").append(createdName);
        sb.append(", updatedBy=").append(updatedBy);
        sb.append(", updatedName=").append(updatedName);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}