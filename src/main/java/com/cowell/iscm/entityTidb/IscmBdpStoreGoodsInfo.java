package com.cowell.iscm.entityTidb;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR> 门店商品信息
 */
public class IscmBdpStoreGoodsInfo implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 公司编码
     */
    private String companyCode;

    /**
     * 公司bdp编码
     */
    private String companyBdpCode;

    /**
     * 门店编码
     */
    private String storeCode;

    /**
     * 商品编码
     */
    private String goodsNo;

    /**
     * 生产厂家
     */
    private String manufacturer;

    /**
     * 库龄天数
     */
    private Integer storageDays;

    /**
     * 滞销天数
     */
    private Integer nonSalesDays;

    /**
     * 近30天销售数量
     */
    private BigDecimal thirtySalesQuantity;

    /**
     * 近30天销售次数
     */
    private Integer thirtySalesCount;

    /**
     * 最小陈列量
     */
    private BigDecimal minDisplayQuantity;

    /**
     * 预计可销售天数
     */
    private BigDecimal expectSaleDays;

    /**
     * 登记数量
     */
    private BigDecimal registerQuantity;

    /**
     * 仓库编码
     */
    private String warehouseCode;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 商品销售属性
     */
    private String goodsline;

    /**
     * 商品的推类等级(销售属性)
     */
    private String pushlevel;

    /**
     * 商品等级
     */
    private Integer goodsLevel;

    /**
     * 禁止配送
     */
    private String forbidDistribute;

    /**
     * 禁止返仓
     */
    private String forbidReturnWarehouse;

    /**
     * 禁止请货
     */
    private String forbidApply;

    /**
     * 禁止调拨
     */
    private String forbidAllot;

    /**
     * 库存上限天数
     */
    private Integer stockUpperLimitDays;

    /**
     * 库存下限天数
     */
    private Integer stockLowerLimitDays;

    /**
     * 海典预测综合日均销量
     */
    private BigDecimal hdSynthesizeAverageDailySales;

    /**
     * 库存上限
     */
    private BigDecimal stockUpperLimit;

    /**
     * 库存下限
     */
    private BigDecimal stockLowerLimit;

    /**
     * bdp预测综合日均销量
     */
    private BigDecimal bdpSynthesizeAverageDailySales;

    /**
     * 总库存数量
     */
    private BigDecimal stockQuantity;

    /**
     * 总库存成本金额
     */
    private BigDecimal costAmount;

    /**
     * 处理状态 0 未退仓 1:已退仓 
     */
    private Byte returnStatus;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getCompanyBdpCode() {
        return companyBdpCode;
    }

    public void setCompanyBdpCode(String companyBdpCode) {
        this.companyBdpCode = companyBdpCode;
    }

    public String getStoreCode() {
        return storeCode;
    }

    public void setStoreCode(String storeCode) {
        this.storeCode = storeCode;
    }

    public String getGoodsNo() {
        return goodsNo;
    }

    public void setGoodsNo(String goodsNo) {
        this.goodsNo = goodsNo;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    public Integer getStorageDays() {
        return storageDays;
    }

    public void setStorageDays(Integer storageDays) {
        this.storageDays = storageDays;
    }

    public Integer getNonSalesDays() {
        return nonSalesDays;
    }

    public void setNonSalesDays(Integer nonSalesDays) {
        this.nonSalesDays = nonSalesDays;
    }

    public BigDecimal getThirtySalesQuantity() {
        return thirtySalesQuantity;
    }

    public void setThirtySalesQuantity(BigDecimal thirtySalesQuantity) {
        this.thirtySalesQuantity = thirtySalesQuantity;
    }

    public Integer getThirtySalesCount() {
        return thirtySalesCount;
    }

    public void setThirtySalesCount(Integer thirtySalesCount) {
        this.thirtySalesCount = thirtySalesCount;
    }

    public BigDecimal getMinDisplayQuantity() {
        return minDisplayQuantity;
    }

    public void setMinDisplayQuantity(BigDecimal minDisplayQuantity) {
        this.minDisplayQuantity = minDisplayQuantity;
    }

    public BigDecimal getExpectSaleDays() {
        return expectSaleDays;
    }

    public void setExpectSaleDays(BigDecimal expectSaleDays) {
        this.expectSaleDays = expectSaleDays;
    }

    public BigDecimal getRegisterQuantity() {
        return registerQuantity;
    }

    public void setRegisterQuantity(BigDecimal registerQuantity) {
        this.registerQuantity = registerQuantity;
    }

    public String getWarehouseCode() {
        return warehouseCode;
    }

    public void setWarehouseCode(String warehouseCode) {
        this.warehouseCode = warehouseCode;
    }

    public String getWarehouseName() {
        return warehouseName;
    }

    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }

    public String getGoodsline() {
        return goodsline;
    }

    public void setGoodsline(String goodsline) {
        this.goodsline = goodsline;
    }

    public String getPushlevel() {
        return pushlevel;
    }

    public void setPushlevel(String pushlevel) {
        this.pushlevel = pushlevel;
    }

    public Integer getGoodsLevel() {
        return goodsLevel;
    }

    public void setGoodsLevel(Integer goodsLevel) {
        this.goodsLevel = goodsLevel;
    }

    public String getForbidDistribute() {
        return forbidDistribute;
    }

    public void setForbidDistribute(String forbidDistribute) {
        this.forbidDistribute = forbidDistribute;
    }

    public String getForbidReturnWarehouse() {
        return forbidReturnWarehouse;
    }

    public void setForbidReturnWarehouse(String forbidReturnWarehouse) {
        this.forbidReturnWarehouse = forbidReturnWarehouse;
    }

    public String getForbidApply() {
        return forbidApply;
    }

    public void setForbidApply(String forbidApply) {
        this.forbidApply = forbidApply;
    }

    public String getForbidAllot() {
        return forbidAllot;
    }

    public void setForbidAllot(String forbidAllot) {
        this.forbidAllot = forbidAllot;
    }

    public Integer getStockUpperLimitDays() {
        return stockUpperLimitDays;
    }

    public void setStockUpperLimitDays(Integer stockUpperLimitDays) {
        this.stockUpperLimitDays = stockUpperLimitDays;
    }

    public Integer getStockLowerLimitDays() {
        return stockLowerLimitDays;
    }

    public void setStockLowerLimitDays(Integer stockLowerLimitDays) {
        this.stockLowerLimitDays = stockLowerLimitDays;
    }

    public BigDecimal getHdSynthesizeAverageDailySales() {
        return hdSynthesizeAverageDailySales;
    }

    public void setHdSynthesizeAverageDailySales(BigDecimal hdSynthesizeAverageDailySales) {
        this.hdSynthesizeAverageDailySales = hdSynthesizeAverageDailySales;
    }

    public BigDecimal getStockUpperLimit() {
        return stockUpperLimit;
    }

    public void setStockUpperLimit(BigDecimal stockUpperLimit) {
        this.stockUpperLimit = stockUpperLimit;
    }

    public BigDecimal getStockLowerLimit() {
        return stockLowerLimit;
    }

    public void setStockLowerLimit(BigDecimal stockLowerLimit) {
        this.stockLowerLimit = stockLowerLimit;
    }

    public BigDecimal getBdpSynthesizeAverageDailySales() {
        return bdpSynthesizeAverageDailySales;
    }

    public void setBdpSynthesizeAverageDailySales(BigDecimal bdpSynthesizeAverageDailySales) {
        this.bdpSynthesizeAverageDailySales = bdpSynthesizeAverageDailySales;
    }

    public BigDecimal getStockQuantity() {
        return stockQuantity;
    }

    public void setStockQuantity(BigDecimal stockQuantity) {
        this.stockQuantity = stockQuantity;
    }

    public BigDecimal getCostAmount() {
        return costAmount;
    }

    public void setCostAmount(BigDecimal costAmount) {
        this.costAmount = costAmount;
    }

    public Byte getReturnStatus() {
        return returnStatus;
    }

    public void setReturnStatus(Byte returnStatus) {
        this.returnStatus = returnStatus;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        IscmBdpStoreGoodsInfo other = (IscmBdpStoreGoodsInfo) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getCompanyCode() == null ? other.getCompanyCode() == null : this.getCompanyCode().equals(other.getCompanyCode()))
            && (this.getCompanyBdpCode() == null ? other.getCompanyBdpCode() == null : this.getCompanyBdpCode().equals(other.getCompanyBdpCode()))
            && (this.getStoreCode() == null ? other.getStoreCode() == null : this.getStoreCode().equals(other.getStoreCode()))
            && (this.getGoodsNo() == null ? other.getGoodsNo() == null : this.getGoodsNo().equals(other.getGoodsNo()))
            && (this.getManufacturer() == null ? other.getManufacturer() == null : this.getManufacturer().equals(other.getManufacturer()))
            && (this.getStorageDays() == null ? other.getStorageDays() == null : this.getStorageDays().equals(other.getStorageDays()))
            && (this.getNonSalesDays() == null ? other.getNonSalesDays() == null : this.getNonSalesDays().equals(other.getNonSalesDays()))
            && (this.getThirtySalesQuantity() == null ? other.getThirtySalesQuantity() == null : this.getThirtySalesQuantity().equals(other.getThirtySalesQuantity()))
            && (this.getThirtySalesCount() == null ? other.getThirtySalesCount() == null : this.getThirtySalesCount().equals(other.getThirtySalesCount()))
            && (this.getMinDisplayQuantity() == null ? other.getMinDisplayQuantity() == null : this.getMinDisplayQuantity().equals(other.getMinDisplayQuantity()))
            && (this.getExpectSaleDays() == null ? other.getExpectSaleDays() == null : this.getExpectSaleDays().equals(other.getExpectSaleDays()))
            && (this.getRegisterQuantity() == null ? other.getRegisterQuantity() == null : this.getRegisterQuantity().equals(other.getRegisterQuantity()))
            && (this.getWarehouseCode() == null ? other.getWarehouseCode() == null : this.getWarehouseCode().equals(other.getWarehouseCode()))
            && (this.getWarehouseName() == null ? other.getWarehouseName() == null : this.getWarehouseName().equals(other.getWarehouseName()))
            && (this.getGoodsline() == null ? other.getGoodsline() == null : this.getGoodsline().equals(other.getGoodsline()))
            && (this.getPushlevel() == null ? other.getPushlevel() == null : this.getPushlevel().equals(other.getPushlevel()))
            && (this.getGoodsLevel() == null ? other.getGoodsLevel() == null : this.getGoodsLevel().equals(other.getGoodsLevel()))
            && (this.getForbidDistribute() == null ? other.getForbidDistribute() == null : this.getForbidDistribute().equals(other.getForbidDistribute()))
            && (this.getForbidReturnWarehouse() == null ? other.getForbidReturnWarehouse() == null : this.getForbidReturnWarehouse().equals(other.getForbidReturnWarehouse()))
            && (this.getForbidApply() == null ? other.getForbidApply() == null : this.getForbidApply().equals(other.getForbidApply()))
            && (this.getForbidAllot() == null ? other.getForbidAllot() == null : this.getForbidAllot().equals(other.getForbidAllot()))
            && (this.getStockUpperLimitDays() == null ? other.getStockUpperLimitDays() == null : this.getStockUpperLimitDays().equals(other.getStockUpperLimitDays()))
            && (this.getStockLowerLimitDays() == null ? other.getStockLowerLimitDays() == null : this.getStockLowerLimitDays().equals(other.getStockLowerLimitDays()))
            && (this.getHdSynthesizeAverageDailySales() == null ? other.getHdSynthesizeAverageDailySales() == null : this.getHdSynthesizeAverageDailySales().equals(other.getHdSynthesizeAverageDailySales()))
            && (this.getStockUpperLimit() == null ? other.getStockUpperLimit() == null : this.getStockUpperLimit().equals(other.getStockUpperLimit()))
            && (this.getStockLowerLimit() == null ? other.getStockLowerLimit() == null : this.getStockLowerLimit().equals(other.getStockLowerLimit()))
            && (this.getBdpSynthesizeAverageDailySales() == null ? other.getBdpSynthesizeAverageDailySales() == null : this.getBdpSynthesizeAverageDailySales().equals(other.getBdpSynthesizeAverageDailySales()))
            && (this.getStockQuantity() == null ? other.getStockQuantity() == null : this.getStockQuantity().equals(other.getStockQuantity()))
            && (this.getCostAmount() == null ? other.getCostAmount() == null : this.getCostAmount().equals(other.getCostAmount()))
            && (this.getReturnStatus() == null ? other.getReturnStatus() == null : this.getReturnStatus().equals(other.getReturnStatus()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getCompanyCode() == null) ? 0 : getCompanyCode().hashCode());
        result = prime * result + ((getCompanyBdpCode() == null) ? 0 : getCompanyBdpCode().hashCode());
        result = prime * result + ((getStoreCode() == null) ? 0 : getStoreCode().hashCode());
        result = prime * result + ((getGoodsNo() == null) ? 0 : getGoodsNo().hashCode());
        result = prime * result + ((getManufacturer() == null) ? 0 : getManufacturer().hashCode());
        result = prime * result + ((getStorageDays() == null) ? 0 : getStorageDays().hashCode());
        result = prime * result + ((getNonSalesDays() == null) ? 0 : getNonSalesDays().hashCode());
        result = prime * result + ((getThirtySalesQuantity() == null) ? 0 : getThirtySalesQuantity().hashCode());
        result = prime * result + ((getThirtySalesCount() == null) ? 0 : getThirtySalesCount().hashCode());
        result = prime * result + ((getMinDisplayQuantity() == null) ? 0 : getMinDisplayQuantity().hashCode());
        result = prime * result + ((getExpectSaleDays() == null) ? 0 : getExpectSaleDays().hashCode());
        result = prime * result + ((getRegisterQuantity() == null) ? 0 : getRegisterQuantity().hashCode());
        result = prime * result + ((getWarehouseCode() == null) ? 0 : getWarehouseCode().hashCode());
        result = prime * result + ((getWarehouseName() == null) ? 0 : getWarehouseName().hashCode());
        result = prime * result + ((getGoodsline() == null) ? 0 : getGoodsline().hashCode());
        result = prime * result + ((getPushlevel() == null) ? 0 : getPushlevel().hashCode());
        result = prime * result + ((getGoodsLevel() == null) ? 0 : getGoodsLevel().hashCode());
        result = prime * result + ((getForbidDistribute() == null) ? 0 : getForbidDistribute().hashCode());
        result = prime * result + ((getForbidReturnWarehouse() == null) ? 0 : getForbidReturnWarehouse().hashCode());
        result = prime * result + ((getForbidApply() == null) ? 0 : getForbidApply().hashCode());
        result = prime * result + ((getForbidAllot() == null) ? 0 : getForbidAllot().hashCode());
        result = prime * result + ((getStockUpperLimitDays() == null) ? 0 : getStockUpperLimitDays().hashCode());
        result = prime * result + ((getStockLowerLimitDays() == null) ? 0 : getStockLowerLimitDays().hashCode());
        result = prime * result + ((getHdSynthesizeAverageDailySales() == null) ? 0 : getHdSynthesizeAverageDailySales().hashCode());
        result = prime * result + ((getStockUpperLimit() == null) ? 0 : getStockUpperLimit().hashCode());
        result = prime * result + ((getStockLowerLimit() == null) ? 0 : getStockLowerLimit().hashCode());
        result = prime * result + ((getBdpSynthesizeAverageDailySales() == null) ? 0 : getBdpSynthesizeAverageDailySales().hashCode());
        result = prime * result + ((getStockQuantity() == null) ? 0 : getStockQuantity().hashCode());
        result = prime * result + ((getCostAmount() == null) ? 0 : getCostAmount().hashCode());
        result = prime * result + ((getReturnStatus() == null) ? 0 : getReturnStatus().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", companyCode=").append(companyCode);
        sb.append(", companyBdpCode=").append(companyBdpCode);
        sb.append(", storeCode=").append(storeCode);
        sb.append(", goodsNo=").append(goodsNo);
        sb.append(", manufacturer=").append(manufacturer);
        sb.append(", storageDays=").append(storageDays);
        sb.append(", nonSalesDays=").append(nonSalesDays);
        sb.append(", thirtySalesQuantity=").append(thirtySalesQuantity);
        sb.append(", thirtySalesCount=").append(thirtySalesCount);
        sb.append(", minDisplayQuantity=").append(minDisplayQuantity);
        sb.append(", expectSaleDays=").append(expectSaleDays);
        sb.append(", registerQuantity=").append(registerQuantity);
        sb.append(", warehouseCode=").append(warehouseCode);
        sb.append(", warehouseName=").append(warehouseName);
        sb.append(", goodsline=").append(goodsline);
        sb.append(", pushlevel=").append(pushlevel);
        sb.append(", goodsLevel=").append(goodsLevel);
        sb.append(", forbidDistribute=").append(forbidDistribute);
        sb.append(", forbidReturnWarehouse=").append(forbidReturnWarehouse);
        sb.append(", forbidApply=").append(forbidApply);
        sb.append(", forbidAllot=").append(forbidAllot);
        sb.append(", stockUpperLimitDays=").append(stockUpperLimitDays);
        sb.append(", stockLowerLimitDays=").append(stockLowerLimitDays);
        sb.append(", hdSynthesizeAverageDailySales=").append(hdSynthesizeAverageDailySales);
        sb.append(", stockUpperLimit=").append(stockUpperLimit);
        sb.append(", stockLowerLimit=").append(stockLowerLimit);
        sb.append(", bdpSynthesizeAverageDailySales=").append(bdpSynthesizeAverageDailySales);
        sb.append(", stockQuantity=").append(stockQuantity);
        sb.append(", costAmount=").append(costAmount);
        sb.append(", returnStatus=").append(returnStatus);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}