package com.cowell.iscm.entityTidb;

import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class IscmStoreApplyAutoInvalidFloatExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Long offset;

    public IscmStoreApplyAutoInvalidFloatExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Long offset) {
        this.offset = offset;
    }

    public Long getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        protected void addCriterionForJDBCDate(String condition, Date value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value.getTime()), property);
        }

        protected void addCriterionForJDBCDate(String condition, List<Date> values, String property) {
            if (values == null || values.size() == 0) {
                throw new RuntimeException("Value list for " + property + " cannot be null or empty");
            }
            List<java.sql.Date> dateList = new ArrayList<java.sql.Date>();
            Iterator<Date> iter = values.iterator();
            while (iter.hasNext()) {
                dateList.add(new java.sql.Date(iter.next().getTime()));
            }
            addCriterion(condition, dateList, property);
        }

        protected void addCriterionForJDBCDate(String condition, Date value1, Date value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value1.getTime()), new java.sql.Date(value2.getTime()), property);
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andParamCodeIsNull() {
            addCriterion("param_code is null");
            return (Criteria) this;
        }

        public Criteria andParamCodeIsNotNull() {
            addCriterion("param_code is not null");
            return (Criteria) this;
        }

        public Criteria andParamCodeEqualTo(String value) {
            addCriterion("param_code =", value, "paramCode");
            return (Criteria) this;
        }

        public Criteria andParamCodeNotEqualTo(String value) {
            addCriterion("param_code <>", value, "paramCode");
            return (Criteria) this;
        }

        public Criteria andParamCodeGreaterThan(String value) {
            addCriterion("param_code >", value, "paramCode");
            return (Criteria) this;
        }

        public Criteria andParamCodeGreaterThanOrEqualTo(String value) {
            addCriterion("param_code >=", value, "paramCode");
            return (Criteria) this;
        }

        public Criteria andParamCodeLessThan(String value) {
            addCriterion("param_code <", value, "paramCode");
            return (Criteria) this;
        }

        public Criteria andParamCodeLessThanOrEqualTo(String value) {
            addCriterion("param_code <=", value, "paramCode");
            return (Criteria) this;
        }

        public Criteria andParamCodeLike(String value) {
            addCriterion("param_code like", value, "paramCode");
            return (Criteria) this;
        }

        public Criteria andParamCodeNotLike(String value) {
            addCriterion("param_code not like", value, "paramCode");
            return (Criteria) this;
        }

        public Criteria andParamCodeIn(List<String> values) {
            addCriterion("param_code in", values, "paramCode");
            return (Criteria) this;
        }

        public Criteria andParamCodeNotIn(List<String> values) {
            addCriterion("param_code not in", values, "paramCode");
            return (Criteria) this;
        }

        public Criteria andParamCodeBetween(String value1, String value2) {
            addCriterion("param_code between", value1, value2, "paramCode");
            return (Criteria) this;
        }

        public Criteria andParamCodeNotBetween(String value1, String value2) {
            addCriterion("param_code not between", value1, value2, "paramCode");
            return (Criteria) this;
        }

        public Criteria andParamNameIsNull() {
            addCriterion("param_name is null");
            return (Criteria) this;
        }

        public Criteria andParamNameIsNotNull() {
            addCriterion("param_name is not null");
            return (Criteria) this;
        }

        public Criteria andParamNameEqualTo(String value) {
            addCriterion("param_name =", value, "paramName");
            return (Criteria) this;
        }

        public Criteria andParamNameNotEqualTo(String value) {
            addCriterion("param_name <>", value, "paramName");
            return (Criteria) this;
        }

        public Criteria andParamNameGreaterThan(String value) {
            addCriterion("param_name >", value, "paramName");
            return (Criteria) this;
        }

        public Criteria andParamNameGreaterThanOrEqualTo(String value) {
            addCriterion("param_name >=", value, "paramName");
            return (Criteria) this;
        }

        public Criteria andParamNameLessThan(String value) {
            addCriterion("param_name <", value, "paramName");
            return (Criteria) this;
        }

        public Criteria andParamNameLessThanOrEqualTo(String value) {
            addCriterion("param_name <=", value, "paramName");
            return (Criteria) this;
        }

        public Criteria andParamNameLike(String value) {
            addCriterion("param_name like", value, "paramName");
            return (Criteria) this;
        }

        public Criteria andParamNameNotLike(String value) {
            addCriterion("param_name not like", value, "paramName");
            return (Criteria) this;
        }

        public Criteria andParamNameIn(List<String> values) {
            addCriterion("param_name in", values, "paramName");
            return (Criteria) this;
        }

        public Criteria andParamNameNotIn(List<String> values) {
            addCriterion("param_name not in", values, "paramName");
            return (Criteria) this;
        }

        public Criteria andParamNameBetween(String value1, String value2) {
            addCriterion("param_name between", value1, value2, "paramName");
            return (Criteria) this;
        }

        public Criteria andParamNameNotBetween(String value1, String value2) {
            addCriterion("param_name not between", value1, value2, "paramName");
            return (Criteria) this;
        }

        public Criteria andParamLevelIsNull() {
            addCriterion("param_level is null");
            return (Criteria) this;
        }

        public Criteria andParamLevelIsNotNull() {
            addCriterion("param_level is not null");
            return (Criteria) this;
        }

        public Criteria andParamLevelEqualTo(Integer value) {
            addCriterion("param_level =", value, "paramLevel");
            return (Criteria) this;
        }

        public Criteria andParamLevelNotEqualTo(Integer value) {
            addCriterion("param_level <>", value, "paramLevel");
            return (Criteria) this;
        }

        public Criteria andParamLevelGreaterThan(Integer value) {
            addCriterion("param_level >", value, "paramLevel");
            return (Criteria) this;
        }

        public Criteria andParamLevelGreaterThanOrEqualTo(Integer value) {
            addCriterion("param_level >=", value, "paramLevel");
            return (Criteria) this;
        }

        public Criteria andParamLevelLessThan(Integer value) {
            addCriterion("param_level <", value, "paramLevel");
            return (Criteria) this;
        }

        public Criteria andParamLevelLessThanOrEqualTo(Integer value) {
            addCriterion("param_level <=", value, "paramLevel");
            return (Criteria) this;
        }

        public Criteria andParamLevelIn(List<Integer> values) {
            addCriterion("param_level in", values, "paramLevel");
            return (Criteria) this;
        }

        public Criteria andParamLevelNotIn(List<Integer> values) {
            addCriterion("param_level not in", values, "paramLevel");
            return (Criteria) this;
        }

        public Criteria andParamLevelBetween(Integer value1, Integer value2) {
            addCriterion("param_level between", value1, value2, "paramLevel");
            return (Criteria) this;
        }

        public Criteria andParamLevelNotBetween(Integer value1, Integer value2) {
            addCriterion("param_level not between", value1, value2, "paramLevel");
            return (Criteria) this;
        }

        public Criteria andOrgIdIsNull() {
            addCriterion("org_id is null");
            return (Criteria) this;
        }

        public Criteria andOrgIdIsNotNull() {
            addCriterion("org_id is not null");
            return (Criteria) this;
        }

        public Criteria andOrgIdEqualTo(Long value) {
            addCriterion("org_id =", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdNotEqualTo(Long value) {
            addCriterion("org_id <>", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdGreaterThan(Long value) {
            addCriterion("org_id >", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdGreaterThanOrEqualTo(Long value) {
            addCriterion("org_id >=", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdLessThan(Long value) {
            addCriterion("org_id <", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdLessThanOrEqualTo(Long value) {
            addCriterion("org_id <=", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdIn(List<Long> values) {
            addCriterion("org_id in", values, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdNotIn(List<Long> values) {
            addCriterion("org_id not in", values, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdBetween(Long value1, Long value2) {
            addCriterion("org_id between", value1, value2, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdNotBetween(Long value1, Long value2) {
            addCriterion("org_id not between", value1, value2, "orgId");
            return (Criteria) this;
        }

        public Criteria andSapCodeIsNull() {
            addCriterion("sap_code is null");
            return (Criteria) this;
        }

        public Criteria andSapCodeIsNotNull() {
            addCriterion("sap_code is not null");
            return (Criteria) this;
        }

        public Criteria andSapCodeEqualTo(String value) {
            addCriterion("sap_code =", value, "sapCode");
            return (Criteria) this;
        }

        public Criteria andSapCodeNotEqualTo(String value) {
            addCriterion("sap_code <>", value, "sapCode");
            return (Criteria) this;
        }

        public Criteria andSapCodeGreaterThan(String value) {
            addCriterion("sap_code >", value, "sapCode");
            return (Criteria) this;
        }

        public Criteria andSapCodeGreaterThanOrEqualTo(String value) {
            addCriterion("sap_code >=", value, "sapCode");
            return (Criteria) this;
        }

        public Criteria andSapCodeLessThan(String value) {
            addCriterion("sap_code <", value, "sapCode");
            return (Criteria) this;
        }

        public Criteria andSapCodeLessThanOrEqualTo(String value) {
            addCriterion("sap_code <=", value, "sapCode");
            return (Criteria) this;
        }

        public Criteria andSapCodeLike(String value) {
            addCriterion("sap_code like", value, "sapCode");
            return (Criteria) this;
        }

        public Criteria andSapCodeNotLike(String value) {
            addCriterion("sap_code not like", value, "sapCode");
            return (Criteria) this;
        }

        public Criteria andSapCodeIn(List<String> values) {
            addCriterion("sap_code in", values, "sapCode");
            return (Criteria) this;
        }

        public Criteria andSapCodeNotIn(List<String> values) {
            addCriterion("sap_code not in", values, "sapCode");
            return (Criteria) this;
        }

        public Criteria andSapCodeBetween(String value1, String value2) {
            addCriterion("sap_code between", value1, value2, "sapCode");
            return (Criteria) this;
        }

        public Criteria andSapCodeNotBetween(String value1, String value2) {
            addCriterion("sap_code not between", value1, value2, "sapCode");
            return (Criteria) this;
        }

        public Criteria andOrgNameIsNull() {
            addCriterion("org_name is null");
            return (Criteria) this;
        }

        public Criteria andOrgNameIsNotNull() {
            addCriterion("org_name is not null");
            return (Criteria) this;
        }

        public Criteria andOrgNameEqualTo(String value) {
            addCriterion("org_name =", value, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameNotEqualTo(String value) {
            addCriterion("org_name <>", value, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameGreaterThan(String value) {
            addCriterion("org_name >", value, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameGreaterThanOrEqualTo(String value) {
            addCriterion("org_name >=", value, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameLessThan(String value) {
            addCriterion("org_name <", value, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameLessThanOrEqualTo(String value) {
            addCriterion("org_name <=", value, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameLike(String value) {
            addCriterion("org_name like", value, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameNotLike(String value) {
            addCriterion("org_name not like", value, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameIn(List<String> values) {
            addCriterion("org_name in", values, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameNotIn(List<String> values) {
            addCriterion("org_name not in", values, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameBetween(String value1, String value2) {
            addCriterion("org_name between", value1, value2, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameNotBetween(String value1, String value2) {
            addCriterion("org_name not between", value1, value2, "orgName");
            return (Criteria) this;
        }

        public Criteria andParentOrgIdIsNull() {
            addCriterion("parent_org_id is null");
            return (Criteria) this;
        }

        public Criteria andParentOrgIdIsNotNull() {
            addCriterion("parent_org_id is not null");
            return (Criteria) this;
        }

        public Criteria andParentOrgIdEqualTo(Long value) {
            addCriterion("parent_org_id =", value, "parentOrgId");
            return (Criteria) this;
        }

        public Criteria andParentOrgIdNotEqualTo(Long value) {
            addCriterion("parent_org_id <>", value, "parentOrgId");
            return (Criteria) this;
        }

        public Criteria andParentOrgIdGreaterThan(Long value) {
            addCriterion("parent_org_id >", value, "parentOrgId");
            return (Criteria) this;
        }

        public Criteria andParentOrgIdGreaterThanOrEqualTo(Long value) {
            addCriterion("parent_org_id >=", value, "parentOrgId");
            return (Criteria) this;
        }

        public Criteria andParentOrgIdLessThan(Long value) {
            addCriterion("parent_org_id <", value, "parentOrgId");
            return (Criteria) this;
        }

        public Criteria andParentOrgIdLessThanOrEqualTo(Long value) {
            addCriterion("parent_org_id <=", value, "parentOrgId");
            return (Criteria) this;
        }

        public Criteria andParentOrgIdIn(List<Long> values) {
            addCriterion("parent_org_id in", values, "parentOrgId");
            return (Criteria) this;
        }

        public Criteria andParentOrgIdNotIn(List<Long> values) {
            addCriterion("parent_org_id not in", values, "parentOrgId");
            return (Criteria) this;
        }

        public Criteria andParentOrgIdBetween(Long value1, Long value2) {
            addCriterion("parent_org_id between", value1, value2, "parentOrgId");
            return (Criteria) this;
        }

        public Criteria andParentOrgIdNotBetween(Long value1, Long value2) {
            addCriterion("parent_org_id not between", value1, value2, "parentOrgId");
            return (Criteria) this;
        }

        public Criteria andParentOrgNameIsNull() {
            addCriterion("parent_org_name is null");
            return (Criteria) this;
        }

        public Criteria andParentOrgNameIsNotNull() {
            addCriterion("parent_org_name is not null");
            return (Criteria) this;
        }

        public Criteria andParentOrgNameEqualTo(String value) {
            addCriterion("parent_org_name =", value, "parentOrgName");
            return (Criteria) this;
        }

        public Criteria andParentOrgNameNotEqualTo(String value) {
            addCriterion("parent_org_name <>", value, "parentOrgName");
            return (Criteria) this;
        }

        public Criteria andParentOrgNameGreaterThan(String value) {
            addCriterion("parent_org_name >", value, "parentOrgName");
            return (Criteria) this;
        }

        public Criteria andParentOrgNameGreaterThanOrEqualTo(String value) {
            addCriterion("parent_org_name >=", value, "parentOrgName");
            return (Criteria) this;
        }

        public Criteria andParentOrgNameLessThan(String value) {
            addCriterion("parent_org_name <", value, "parentOrgName");
            return (Criteria) this;
        }

        public Criteria andParentOrgNameLessThanOrEqualTo(String value) {
            addCriterion("parent_org_name <=", value, "parentOrgName");
            return (Criteria) this;
        }

        public Criteria andParentOrgNameLike(String value) {
            addCriterion("parent_org_name like", value, "parentOrgName");
            return (Criteria) this;
        }

        public Criteria andParentOrgNameNotLike(String value) {
            addCriterion("parent_org_name not like", value, "parentOrgName");
            return (Criteria) this;
        }

        public Criteria andParentOrgNameIn(List<String> values) {
            addCriterion("parent_org_name in", values, "parentOrgName");
            return (Criteria) this;
        }

        public Criteria andParentOrgNameNotIn(List<String> values) {
            addCriterion("parent_org_name not in", values, "parentOrgName");
            return (Criteria) this;
        }

        public Criteria andParentOrgNameBetween(String value1, String value2) {
            addCriterion("parent_org_name between", value1, value2, "parentOrgName");
            return (Criteria) this;
        }

        public Criteria andParentOrgNameNotBetween(String value1, String value2) {
            addCriterion("parent_org_name not between", value1, value2, "parentOrgName");
            return (Criteria) this;
        }

        public Criteria andStoreOrgIdIsNull() {
            addCriterion("store_org_id is null");
            return (Criteria) this;
        }

        public Criteria andStoreOrgIdIsNotNull() {
            addCriterion("store_org_id is not null");
            return (Criteria) this;
        }

        public Criteria andStoreOrgIdEqualTo(Long value) {
            addCriterion("store_org_id =", value, "storeOrgId");
            return (Criteria) this;
        }

        public Criteria andStoreOrgIdNotEqualTo(Long value) {
            addCriterion("store_org_id <>", value, "storeOrgId");
            return (Criteria) this;
        }

        public Criteria andStoreOrgIdGreaterThan(Long value) {
            addCriterion("store_org_id >", value, "storeOrgId");
            return (Criteria) this;
        }

        public Criteria andStoreOrgIdGreaterThanOrEqualTo(Long value) {
            addCriterion("store_org_id >=", value, "storeOrgId");
            return (Criteria) this;
        }

        public Criteria andStoreOrgIdLessThan(Long value) {
            addCriterion("store_org_id <", value, "storeOrgId");
            return (Criteria) this;
        }

        public Criteria andStoreOrgIdLessThanOrEqualTo(Long value) {
            addCriterion("store_org_id <=", value, "storeOrgId");
            return (Criteria) this;
        }

        public Criteria andStoreOrgIdIn(List<Long> values) {
            addCriterion("store_org_id in", values, "storeOrgId");
            return (Criteria) this;
        }

        public Criteria andStoreOrgIdNotIn(List<Long> values) {
            addCriterion("store_org_id not in", values, "storeOrgId");
            return (Criteria) this;
        }

        public Criteria andStoreOrgIdBetween(Long value1, Long value2) {
            addCriterion("store_org_id between", value1, value2, "storeOrgId");
            return (Criteria) this;
        }

        public Criteria andStoreOrgIdNotBetween(Long value1, Long value2) {
            addCriterion("store_org_id not between", value1, value2, "storeOrgId");
            return (Criteria) this;
        }

        public Criteria andStoreCodeIsNull() {
            addCriterion("store_code is null");
            return (Criteria) this;
        }

        public Criteria andStoreCodeIsNotNull() {
            addCriterion("store_code is not null");
            return (Criteria) this;
        }

        public Criteria andStoreCodeEqualTo(String value) {
            addCriterion("store_code =", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotEqualTo(String value) {
            addCriterion("store_code <>", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeGreaterThan(String value) {
            addCriterion("store_code >", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeGreaterThanOrEqualTo(String value) {
            addCriterion("store_code >=", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeLessThan(String value) {
            addCriterion("store_code <", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeLessThanOrEqualTo(String value) {
            addCriterion("store_code <=", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeLike(String value) {
            addCriterion("store_code like", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotLike(String value) {
            addCriterion("store_code not like", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeIn(List<String> values) {
            addCriterion("store_code in", values, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotIn(List<String> values) {
            addCriterion("store_code not in", values, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeBetween(String value1, String value2) {
            addCriterion("store_code between", value1, value2, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotBetween(String value1, String value2) {
            addCriterion("store_code not between", value1, value2, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreNameIsNull() {
            addCriterion("store_name is null");
            return (Criteria) this;
        }

        public Criteria andStoreNameIsNotNull() {
            addCriterion("store_name is not null");
            return (Criteria) this;
        }

        public Criteria andStoreNameEqualTo(String value) {
            addCriterion("store_name =", value, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameNotEqualTo(String value) {
            addCriterion("store_name <>", value, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameGreaterThan(String value) {
            addCriterion("store_name >", value, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameGreaterThanOrEqualTo(String value) {
            addCriterion("store_name >=", value, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameLessThan(String value) {
            addCriterion("store_name <", value, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameLessThanOrEqualTo(String value) {
            addCriterion("store_name <=", value, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameLike(String value) {
            addCriterion("store_name like", value, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameNotLike(String value) {
            addCriterion("store_name not like", value, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameIn(List<String> values) {
            addCriterion("store_name in", values, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameNotIn(List<String> values) {
            addCriterion("store_name not in", values, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameBetween(String value1, String value2) {
            addCriterion("store_name between", value1, value2, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameNotBetween(String value1, String value2) {
            addCriterion("store_name not between", value1, value2, "storeName");
            return (Criteria) this;
        }

        public Criteria andGoodsChooseTypeIsNull() {
            addCriterion("goods_choose_type is null");
            return (Criteria) this;
        }

        public Criteria andGoodsChooseTypeIsNotNull() {
            addCriterion("goods_choose_type is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsChooseTypeEqualTo(Byte value) {
            addCriterion("goods_choose_type =", value, "goodsChooseType");
            return (Criteria) this;
        }

        public Criteria andGoodsChooseTypeNotEqualTo(Byte value) {
            addCriterion("goods_choose_type <>", value, "goodsChooseType");
            return (Criteria) this;
        }

        public Criteria andGoodsChooseTypeGreaterThan(Byte value) {
            addCriterion("goods_choose_type >", value, "goodsChooseType");
            return (Criteria) this;
        }

        public Criteria andGoodsChooseTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("goods_choose_type >=", value, "goodsChooseType");
            return (Criteria) this;
        }

        public Criteria andGoodsChooseTypeLessThan(Byte value) {
            addCriterion("goods_choose_type <", value, "goodsChooseType");
            return (Criteria) this;
        }

        public Criteria andGoodsChooseTypeLessThanOrEqualTo(Byte value) {
            addCriterion("goods_choose_type <=", value, "goodsChooseType");
            return (Criteria) this;
        }

        public Criteria andGoodsChooseTypeIn(List<Byte> values) {
            addCriterion("goods_choose_type in", values, "goodsChooseType");
            return (Criteria) this;
        }

        public Criteria andGoodsChooseTypeNotIn(List<Byte> values) {
            addCriterion("goods_choose_type not in", values, "goodsChooseType");
            return (Criteria) this;
        }

        public Criteria andGoodsChooseTypeBetween(Byte value1, Byte value2) {
            addCriterion("goods_choose_type between", value1, value2, "goodsChooseType");
            return (Criteria) this;
        }

        public Criteria andGoodsChooseTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("goods_choose_type not between", value1, value2, "goodsChooseType");
            return (Criteria) this;
        }

        public Criteria andGoodsNoIsNull() {
            addCriterion("goods_no is null");
            return (Criteria) this;
        }

        public Criteria andGoodsNoIsNotNull() {
            addCriterion("goods_no is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsNoEqualTo(String value) {
            addCriterion("goods_no =", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotEqualTo(String value) {
            addCriterion("goods_no <>", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoGreaterThan(String value) {
            addCriterion("goods_no >", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoGreaterThanOrEqualTo(String value) {
            addCriterion("goods_no >=", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoLessThan(String value) {
            addCriterion("goods_no <", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoLessThanOrEqualTo(String value) {
            addCriterion("goods_no <=", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoLike(String value) {
            addCriterion("goods_no like", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotLike(String value) {
            addCriterion("goods_no not like", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoIn(List<String> values) {
            addCriterion("goods_no in", values, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotIn(List<String> values) {
            addCriterion("goods_no not in", values, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoBetween(String value1, String value2) {
            addCriterion("goods_no between", value1, value2, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotBetween(String value1, String value2) {
            addCriterion("goods_no not between", value1, value2, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andBusinessidIsNull() {
            addCriterion("businessId is null");
            return (Criteria) this;
        }

        public Criteria andBusinessidIsNotNull() {
            addCriterion("businessId is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessidEqualTo(Long value) {
            addCriterion("businessId =", value, "businessid");
            return (Criteria) this;
        }

        public Criteria andBusinessidNotEqualTo(Long value) {
            addCriterion("businessId <>", value, "businessid");
            return (Criteria) this;
        }

        public Criteria andBusinessidGreaterThan(Long value) {
            addCriterion("businessId >", value, "businessid");
            return (Criteria) this;
        }

        public Criteria andBusinessidGreaterThanOrEqualTo(Long value) {
            addCriterion("businessId >=", value, "businessid");
            return (Criteria) this;
        }

        public Criteria andBusinessidLessThan(Long value) {
            addCriterion("businessId <", value, "businessid");
            return (Criteria) this;
        }

        public Criteria andBusinessidLessThanOrEqualTo(Long value) {
            addCriterion("businessId <=", value, "businessid");
            return (Criteria) this;
        }

        public Criteria andBusinessidIn(List<Long> values) {
            addCriterion("businessId in", values, "businessid");
            return (Criteria) this;
        }

        public Criteria andBusinessidNotIn(List<Long> values) {
            addCriterion("businessId not in", values, "businessid");
            return (Criteria) this;
        }

        public Criteria andBusinessidBetween(Long value1, Long value2) {
            addCriterion("businessId between", value1, value2, "businessid");
            return (Criteria) this;
        }

        public Criteria andBusinessidNotBetween(Long value1, Long value2) {
            addCriterion("businessId not between", value1, value2, "businessid");
            return (Criteria) this;
        }

        public Criteria andBarCodeIsNull() {
            addCriterion("bar_code is null");
            return (Criteria) this;
        }

        public Criteria andBarCodeIsNotNull() {
            addCriterion("bar_code is not null");
            return (Criteria) this;
        }

        public Criteria andBarCodeEqualTo(String value) {
            addCriterion("bar_code =", value, "barCode");
            return (Criteria) this;
        }

        public Criteria andBarCodeNotEqualTo(String value) {
            addCriterion("bar_code <>", value, "barCode");
            return (Criteria) this;
        }

        public Criteria andBarCodeGreaterThan(String value) {
            addCriterion("bar_code >", value, "barCode");
            return (Criteria) this;
        }

        public Criteria andBarCodeGreaterThanOrEqualTo(String value) {
            addCriterion("bar_code >=", value, "barCode");
            return (Criteria) this;
        }

        public Criteria andBarCodeLessThan(String value) {
            addCriterion("bar_code <", value, "barCode");
            return (Criteria) this;
        }

        public Criteria andBarCodeLessThanOrEqualTo(String value) {
            addCriterion("bar_code <=", value, "barCode");
            return (Criteria) this;
        }

        public Criteria andBarCodeLike(String value) {
            addCriterion("bar_code like", value, "barCode");
            return (Criteria) this;
        }

        public Criteria andBarCodeNotLike(String value) {
            addCriterion("bar_code not like", value, "barCode");
            return (Criteria) this;
        }

        public Criteria andBarCodeIn(List<String> values) {
            addCriterion("bar_code in", values, "barCode");
            return (Criteria) this;
        }

        public Criteria andBarCodeNotIn(List<String> values) {
            addCriterion("bar_code not in", values, "barCode");
            return (Criteria) this;
        }

        public Criteria andBarCodeBetween(String value1, String value2) {
            addCriterion("bar_code between", value1, value2, "barCode");
            return (Criteria) this;
        }

        public Criteria andBarCodeNotBetween(String value1, String value2) {
            addCriterion("bar_code not between", value1, value2, "barCode");
            return (Criteria) this;
        }

        public Criteria andCurNameIsNull() {
            addCriterion("cur_name is null");
            return (Criteria) this;
        }

        public Criteria andCurNameIsNotNull() {
            addCriterion("cur_name is not null");
            return (Criteria) this;
        }

        public Criteria andCurNameEqualTo(String value) {
            addCriterion("cur_name =", value, "curName");
            return (Criteria) this;
        }

        public Criteria andCurNameNotEqualTo(String value) {
            addCriterion("cur_name <>", value, "curName");
            return (Criteria) this;
        }

        public Criteria andCurNameGreaterThan(String value) {
            addCriterion("cur_name >", value, "curName");
            return (Criteria) this;
        }

        public Criteria andCurNameGreaterThanOrEqualTo(String value) {
            addCriterion("cur_name >=", value, "curName");
            return (Criteria) this;
        }

        public Criteria andCurNameLessThan(String value) {
            addCriterion("cur_name <", value, "curName");
            return (Criteria) this;
        }

        public Criteria andCurNameLessThanOrEqualTo(String value) {
            addCriterion("cur_name <=", value, "curName");
            return (Criteria) this;
        }

        public Criteria andCurNameLike(String value) {
            addCriterion("cur_name like", value, "curName");
            return (Criteria) this;
        }

        public Criteria andCurNameNotLike(String value) {
            addCriterion("cur_name not like", value, "curName");
            return (Criteria) this;
        }

        public Criteria andCurNameIn(List<String> values) {
            addCriterion("cur_name in", values, "curName");
            return (Criteria) this;
        }

        public Criteria andCurNameNotIn(List<String> values) {
            addCriterion("cur_name not in", values, "curName");
            return (Criteria) this;
        }

        public Criteria andCurNameBetween(String value1, String value2) {
            addCriterion("cur_name between", value1, value2, "curName");
            return (Criteria) this;
        }

        public Criteria andCurNameNotBetween(String value1, String value2) {
            addCriterion("cur_name not between", value1, value2, "curName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameIsNull() {
            addCriterion("goods_name is null");
            return (Criteria) this;
        }

        public Criteria andGoodsNameIsNotNull() {
            addCriterion("goods_name is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsNameEqualTo(String value) {
            addCriterion("goods_name =", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameNotEqualTo(String value) {
            addCriterion("goods_name <>", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameGreaterThan(String value) {
            addCriterion("goods_name >", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameGreaterThanOrEqualTo(String value) {
            addCriterion("goods_name >=", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameLessThan(String value) {
            addCriterion("goods_name <", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameLessThanOrEqualTo(String value) {
            addCriterion("goods_name <=", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameLike(String value) {
            addCriterion("goods_name like", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameNotLike(String value) {
            addCriterion("goods_name not like", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameIn(List<String> values) {
            addCriterion("goods_name in", values, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameNotIn(List<String> values) {
            addCriterion("goods_name not in", values, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameBetween(String value1, String value2) {
            addCriterion("goods_name between", value1, value2, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameNotBetween(String value1, String value2) {
            addCriterion("goods_name not between", value1, value2, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsUnitIsNull() {
            addCriterion("goods_unit is null");
            return (Criteria) this;
        }

        public Criteria andGoodsUnitIsNotNull() {
            addCriterion("goods_unit is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsUnitEqualTo(String value) {
            addCriterion("goods_unit =", value, "goodsUnit");
            return (Criteria) this;
        }

        public Criteria andGoodsUnitNotEqualTo(String value) {
            addCriterion("goods_unit <>", value, "goodsUnit");
            return (Criteria) this;
        }

        public Criteria andGoodsUnitGreaterThan(String value) {
            addCriterion("goods_unit >", value, "goodsUnit");
            return (Criteria) this;
        }

        public Criteria andGoodsUnitGreaterThanOrEqualTo(String value) {
            addCriterion("goods_unit >=", value, "goodsUnit");
            return (Criteria) this;
        }

        public Criteria andGoodsUnitLessThan(String value) {
            addCriterion("goods_unit <", value, "goodsUnit");
            return (Criteria) this;
        }

        public Criteria andGoodsUnitLessThanOrEqualTo(String value) {
            addCriterion("goods_unit <=", value, "goodsUnit");
            return (Criteria) this;
        }

        public Criteria andGoodsUnitLike(String value) {
            addCriterion("goods_unit like", value, "goodsUnit");
            return (Criteria) this;
        }

        public Criteria andGoodsUnitNotLike(String value) {
            addCriterion("goods_unit not like", value, "goodsUnit");
            return (Criteria) this;
        }

        public Criteria andGoodsUnitIn(List<String> values) {
            addCriterion("goods_unit in", values, "goodsUnit");
            return (Criteria) this;
        }

        public Criteria andGoodsUnitNotIn(List<String> values) {
            addCriterion("goods_unit not in", values, "goodsUnit");
            return (Criteria) this;
        }

        public Criteria andGoodsUnitBetween(String value1, String value2) {
            addCriterion("goods_unit between", value1, value2, "goodsUnit");
            return (Criteria) this;
        }

        public Criteria andGoodsUnitNotBetween(String value1, String value2) {
            addCriterion("goods_unit not between", value1, value2, "goodsUnit");
            return (Criteria) this;
        }

        public Criteria andSpecificationsIsNull() {
            addCriterion("specifications is null");
            return (Criteria) this;
        }

        public Criteria andSpecificationsIsNotNull() {
            addCriterion("specifications is not null");
            return (Criteria) this;
        }

        public Criteria andSpecificationsEqualTo(String value) {
            addCriterion("specifications =", value, "specifications");
            return (Criteria) this;
        }

        public Criteria andSpecificationsNotEqualTo(String value) {
            addCriterion("specifications <>", value, "specifications");
            return (Criteria) this;
        }

        public Criteria andSpecificationsGreaterThan(String value) {
            addCriterion("specifications >", value, "specifications");
            return (Criteria) this;
        }

        public Criteria andSpecificationsGreaterThanOrEqualTo(String value) {
            addCriterion("specifications >=", value, "specifications");
            return (Criteria) this;
        }

        public Criteria andSpecificationsLessThan(String value) {
            addCriterion("specifications <", value, "specifications");
            return (Criteria) this;
        }

        public Criteria andSpecificationsLessThanOrEqualTo(String value) {
            addCriterion("specifications <=", value, "specifications");
            return (Criteria) this;
        }

        public Criteria andSpecificationsLike(String value) {
            addCriterion("specifications like", value, "specifications");
            return (Criteria) this;
        }

        public Criteria andSpecificationsNotLike(String value) {
            addCriterion("specifications not like", value, "specifications");
            return (Criteria) this;
        }

        public Criteria andSpecificationsIn(List<String> values) {
            addCriterion("specifications in", values, "specifications");
            return (Criteria) this;
        }

        public Criteria andSpecificationsNotIn(List<String> values) {
            addCriterion("specifications not in", values, "specifications");
            return (Criteria) this;
        }

        public Criteria andSpecificationsBetween(String value1, String value2) {
            addCriterion("specifications between", value1, value2, "specifications");
            return (Criteria) this;
        }

        public Criteria andSpecificationsNotBetween(String value1, String value2) {
            addCriterion("specifications not between", value1, value2, "specifications");
            return (Criteria) this;
        }

        public Criteria andManufacturerIsNull() {
            addCriterion("manufacturer is null");
            return (Criteria) this;
        }

        public Criteria andManufacturerIsNotNull() {
            addCriterion("manufacturer is not null");
            return (Criteria) this;
        }

        public Criteria andManufacturerEqualTo(String value) {
            addCriterion("manufacturer =", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerNotEqualTo(String value) {
            addCriterion("manufacturer <>", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerGreaterThan(String value) {
            addCriterion("manufacturer >", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerGreaterThanOrEqualTo(String value) {
            addCriterion("manufacturer >=", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerLessThan(String value) {
            addCriterion("manufacturer <", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerLessThanOrEqualTo(String value) {
            addCriterion("manufacturer <=", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerLike(String value) {
            addCriterion("manufacturer like", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerNotLike(String value) {
            addCriterion("manufacturer not like", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerIn(List<String> values) {
            addCriterion("manufacturer in", values, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerNotIn(List<String> values) {
            addCriterion("manufacturer not in", values, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerBetween(String value1, String value2) {
            addCriterion("manufacturer between", value1, value2, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerNotBetween(String value1, String value2) {
            addCriterion("manufacturer not between", value1, value2, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andGoodslineIsNull() {
            addCriterion("goodsline is null");
            return (Criteria) this;
        }

        public Criteria andGoodslineIsNotNull() {
            addCriterion("goodsline is not null");
            return (Criteria) this;
        }

        public Criteria andGoodslineEqualTo(String value) {
            addCriterion("goodsline =", value, "goodsline");
            return (Criteria) this;
        }

        public Criteria andGoodslineNotEqualTo(String value) {
            addCriterion("goodsline <>", value, "goodsline");
            return (Criteria) this;
        }

        public Criteria andGoodslineGreaterThan(String value) {
            addCriterion("goodsline >", value, "goodsline");
            return (Criteria) this;
        }

        public Criteria andGoodslineGreaterThanOrEqualTo(String value) {
            addCriterion("goodsline >=", value, "goodsline");
            return (Criteria) this;
        }

        public Criteria andGoodslineLessThan(String value) {
            addCriterion("goodsline <", value, "goodsline");
            return (Criteria) this;
        }

        public Criteria andGoodslineLessThanOrEqualTo(String value) {
            addCriterion("goodsline <=", value, "goodsline");
            return (Criteria) this;
        }

        public Criteria andGoodslineLike(String value) {
            addCriterion("goodsline like", value, "goodsline");
            return (Criteria) this;
        }

        public Criteria andGoodslineNotLike(String value) {
            addCriterion("goodsline not like", value, "goodsline");
            return (Criteria) this;
        }

        public Criteria andGoodslineIn(List<String> values) {
            addCriterion("goodsline in", values, "goodsline");
            return (Criteria) this;
        }

        public Criteria andGoodslineNotIn(List<String> values) {
            addCriterion("goodsline not in", values, "goodsline");
            return (Criteria) this;
        }

        public Criteria andGoodslineBetween(String value1, String value2) {
            addCriterion("goodsline between", value1, value2, "goodsline");
            return (Criteria) this;
        }

        public Criteria andGoodslineNotBetween(String value1, String value2) {
            addCriterion("goodsline not between", value1, value2, "goodsline");
            return (Criteria) this;
        }

        public Criteria andSpecialattributesIsNull() {
            addCriterion("specialattributes is null");
            return (Criteria) this;
        }

        public Criteria andSpecialattributesIsNotNull() {
            addCriterion("specialattributes is not null");
            return (Criteria) this;
        }

        public Criteria andSpecialattributesEqualTo(String value) {
            addCriterion("specialattributes =", value, "specialattributes");
            return (Criteria) this;
        }

        public Criteria andSpecialattributesNotEqualTo(String value) {
            addCriterion("specialattributes <>", value, "specialattributes");
            return (Criteria) this;
        }

        public Criteria andSpecialattributesGreaterThan(String value) {
            addCriterion("specialattributes >", value, "specialattributes");
            return (Criteria) this;
        }

        public Criteria andSpecialattributesGreaterThanOrEqualTo(String value) {
            addCriterion("specialattributes >=", value, "specialattributes");
            return (Criteria) this;
        }

        public Criteria andSpecialattributesLessThan(String value) {
            addCriterion("specialattributes <", value, "specialattributes");
            return (Criteria) this;
        }

        public Criteria andSpecialattributesLessThanOrEqualTo(String value) {
            addCriterion("specialattributes <=", value, "specialattributes");
            return (Criteria) this;
        }

        public Criteria andSpecialattributesLike(String value) {
            addCriterion("specialattributes like", value, "specialattributes");
            return (Criteria) this;
        }

        public Criteria andSpecialattributesNotLike(String value) {
            addCriterion("specialattributes not like", value, "specialattributes");
            return (Criteria) this;
        }

        public Criteria andSpecialattributesIn(List<String> values) {
            addCriterion("specialattributes in", values, "specialattributes");
            return (Criteria) this;
        }

        public Criteria andSpecialattributesNotIn(List<String> values) {
            addCriterion("specialattributes not in", values, "specialattributes");
            return (Criteria) this;
        }

        public Criteria andSpecialattributesBetween(String value1, String value2) {
            addCriterion("specialattributes between", value1, value2, "specialattributes");
            return (Criteria) this;
        }

        public Criteria andSpecialattributesNotBetween(String value1, String value2) {
            addCriterion("specialattributes not between", value1, value2, "specialattributes");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelIsNull() {
            addCriterion("goods_level is null");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelIsNotNull() {
            addCriterion("goods_level is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelEqualTo(Byte value) {
            addCriterion("goods_level =", value, "goodsLevel");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelNotEqualTo(Byte value) {
            addCriterion("goods_level <>", value, "goodsLevel");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelGreaterThan(Byte value) {
            addCriterion("goods_level >", value, "goodsLevel");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelGreaterThanOrEqualTo(Byte value) {
            addCriterion("goods_level >=", value, "goodsLevel");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelLessThan(Byte value) {
            addCriterion("goods_level <", value, "goodsLevel");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelLessThanOrEqualTo(Byte value) {
            addCriterion("goods_level <=", value, "goodsLevel");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelIn(List<Byte> values) {
            addCriterion("goods_level in", values, "goodsLevel");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelNotIn(List<Byte> values) {
            addCriterion("goods_level not in", values, "goodsLevel");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelBetween(Byte value1, Byte value2) {
            addCriterion("goods_level between", value1, value2, "goodsLevel");
            return (Criteria) this;
        }

        public Criteria andGoodsLevelNotBetween(Byte value1, Byte value2) {
            addCriterion("goods_level not between", value1, value2, "goodsLevel");
            return (Criteria) this;
        }

        public Criteria andStartDateIsNull() {
            addCriterion("start_date is null");
            return (Criteria) this;
        }

        public Criteria andStartDateIsNotNull() {
            addCriterion("start_date is not null");
            return (Criteria) this;
        }

        public Criteria andStartDateEqualTo(Date value) {
            addCriterionForJDBCDate("start_date =", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("start_date <>", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateGreaterThan(Date value) {
            addCriterionForJDBCDate("start_date >", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("start_date >=", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateLessThan(Date value) {
            addCriterionForJDBCDate("start_date <", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("start_date <=", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateIn(List<Date> values) {
            addCriterionForJDBCDate("start_date in", values, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("start_date not in", values, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("start_date between", value1, value2, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("start_date not between", value1, value2, "startDate");
            return (Criteria) this;
        }

        public Criteria andEndDateIsNull() {
            addCriterion("end_date is null");
            return (Criteria) this;
        }

        public Criteria andEndDateIsNotNull() {
            addCriterion("end_date is not null");
            return (Criteria) this;
        }

        public Criteria andEndDateEqualTo(Date value) {
            addCriterionForJDBCDate("end_date =", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("end_date <>", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateGreaterThan(Date value) {
            addCriterionForJDBCDate("end_date >", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("end_date >=", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateLessThan(Date value) {
            addCriterionForJDBCDate("end_date <", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("end_date <=", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateIn(List<Date> values) {
            addCriterionForJDBCDate("end_date in", values, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("end_date not in", values, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("end_date between", value1, value2, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("end_date not between", value1, value2, "endDate");
            return (Criteria) this;
        }

        public Criteria andInheritTypeIsNull() {
            addCriterion("inherit_type is null");
            return (Criteria) this;
        }

        public Criteria andInheritTypeIsNotNull() {
            addCriterion("inherit_type is not null");
            return (Criteria) this;
        }

        public Criteria andInheritTypeEqualTo(Byte value) {
            addCriterion("inherit_type =", value, "inheritType");
            return (Criteria) this;
        }

        public Criteria andInheritTypeNotEqualTo(Byte value) {
            addCriterion("inherit_type <>", value, "inheritType");
            return (Criteria) this;
        }

        public Criteria andInheritTypeGreaterThan(Byte value) {
            addCriterion("inherit_type >", value, "inheritType");
            return (Criteria) this;
        }

        public Criteria andInheritTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("inherit_type >=", value, "inheritType");
            return (Criteria) this;
        }

        public Criteria andInheritTypeLessThan(Byte value) {
            addCriterion("inherit_type <", value, "inheritType");
            return (Criteria) this;
        }

        public Criteria andInheritTypeLessThanOrEqualTo(Byte value) {
            addCriterion("inherit_type <=", value, "inheritType");
            return (Criteria) this;
        }

        public Criteria andInheritTypeIn(List<Byte> values) {
            addCriterion("inherit_type in", values, "inheritType");
            return (Criteria) this;
        }

        public Criteria andInheritTypeNotIn(List<Byte> values) {
            addCriterion("inherit_type not in", values, "inheritType");
            return (Criteria) this;
        }

        public Criteria andInheritTypeBetween(Byte value1, Byte value2) {
            addCriterion("inherit_type between", value1, value2, "inheritType");
            return (Criteria) this;
        }

        public Criteria andInheritTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("inherit_type not between", value1, value2, "inheritType");
            return (Criteria) this;
        }

        public Criteria andEffectStatusIsNull() {
            addCriterion("effect_status is null");
            return (Criteria) this;
        }

        public Criteria andEffectStatusIsNotNull() {
            addCriterion("effect_status is not null");
            return (Criteria) this;
        }

        public Criteria andEffectStatusEqualTo(Byte value) {
            addCriterion("effect_status =", value, "effectStatus");
            return (Criteria) this;
        }

        public Criteria andEffectStatusNotEqualTo(Byte value) {
            addCriterion("effect_status <>", value, "effectStatus");
            return (Criteria) this;
        }

        public Criteria andEffectStatusGreaterThan(Byte value) {
            addCriterion("effect_status >", value, "effectStatus");
            return (Criteria) this;
        }

        public Criteria andEffectStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("effect_status >=", value, "effectStatus");
            return (Criteria) this;
        }

        public Criteria andEffectStatusLessThan(Byte value) {
            addCriterion("effect_status <", value, "effectStatus");
            return (Criteria) this;
        }

        public Criteria andEffectStatusLessThanOrEqualTo(Byte value) {
            addCriterion("effect_status <=", value, "effectStatus");
            return (Criteria) this;
        }

        public Criteria andEffectStatusIn(List<Byte> values) {
            addCriterion("effect_status in", values, "effectStatus");
            return (Criteria) this;
        }

        public Criteria andEffectStatusNotIn(List<Byte> values) {
            addCriterion("effect_status not in", values, "effectStatus");
            return (Criteria) this;
        }

        public Criteria andEffectStatusBetween(Byte value1, Byte value2) {
            addCriterion("effect_status between", value1, value2, "effectStatus");
            return (Criteria) this;
        }

        public Criteria andEffectStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("effect_status not between", value1, value2, "effectStatus");
            return (Criteria) this;
        }

        public Criteria andAdjustTypeIsNull() {
            addCriterion("adjust_type is null");
            return (Criteria) this;
        }

        public Criteria andAdjustTypeIsNotNull() {
            addCriterion("adjust_type is not null");
            return (Criteria) this;
        }

        public Criteria andAdjustTypeEqualTo(Byte value) {
            addCriterion("adjust_type =", value, "adjustType");
            return (Criteria) this;
        }

        public Criteria andAdjustTypeNotEqualTo(Byte value) {
            addCriterion("adjust_type <>", value, "adjustType");
            return (Criteria) this;
        }

        public Criteria andAdjustTypeGreaterThan(Byte value) {
            addCriterion("adjust_type >", value, "adjustType");
            return (Criteria) this;
        }

        public Criteria andAdjustTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("adjust_type >=", value, "adjustType");
            return (Criteria) this;
        }

        public Criteria andAdjustTypeLessThan(Byte value) {
            addCriterion("adjust_type <", value, "adjustType");
            return (Criteria) this;
        }

        public Criteria andAdjustTypeLessThanOrEqualTo(Byte value) {
            addCriterion("adjust_type <=", value, "adjustType");
            return (Criteria) this;
        }

        public Criteria andAdjustTypeIn(List<Byte> values) {
            addCriterion("adjust_type in", values, "adjustType");
            return (Criteria) this;
        }

        public Criteria andAdjustTypeNotIn(List<Byte> values) {
            addCriterion("adjust_type not in", values, "adjustType");
            return (Criteria) this;
        }

        public Criteria andAdjustTypeBetween(Byte value1, Byte value2) {
            addCriterion("adjust_type between", value1, value2, "adjustType");
            return (Criteria) this;
        }

        public Criteria andAdjustTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("adjust_type not between", value1, value2, "adjustType");
            return (Criteria) this;
        }

        public Criteria andAdjustModeIsNull() {
            addCriterion("adjust_mode is null");
            return (Criteria) this;
        }

        public Criteria andAdjustModeIsNotNull() {
            addCriterion("adjust_mode is not null");
            return (Criteria) this;
        }

        public Criteria andAdjustModeEqualTo(Byte value) {
            addCriterion("adjust_mode =", value, "adjustMode");
            return (Criteria) this;
        }

        public Criteria andAdjustModeNotEqualTo(Byte value) {
            addCriterion("adjust_mode <>", value, "adjustMode");
            return (Criteria) this;
        }

        public Criteria andAdjustModeGreaterThan(Byte value) {
            addCriterion("adjust_mode >", value, "adjustMode");
            return (Criteria) this;
        }

        public Criteria andAdjustModeGreaterThanOrEqualTo(Byte value) {
            addCriterion("adjust_mode >=", value, "adjustMode");
            return (Criteria) this;
        }

        public Criteria andAdjustModeLessThan(Byte value) {
            addCriterion("adjust_mode <", value, "adjustMode");
            return (Criteria) this;
        }

        public Criteria andAdjustModeLessThanOrEqualTo(Byte value) {
            addCriterion("adjust_mode <=", value, "adjustMode");
            return (Criteria) this;
        }

        public Criteria andAdjustModeIn(List<Byte> values) {
            addCriterion("adjust_mode in", values, "adjustMode");
            return (Criteria) this;
        }

        public Criteria andAdjustModeNotIn(List<Byte> values) {
            addCriterion("adjust_mode not in", values, "adjustMode");
            return (Criteria) this;
        }

        public Criteria andAdjustModeBetween(Byte value1, Byte value2) {
            addCriterion("adjust_mode between", value1, value2, "adjustMode");
            return (Criteria) this;
        }

        public Criteria andAdjustModeNotBetween(Byte value1, Byte value2) {
            addCriterion("adjust_mode not between", value1, value2, "adjustMode");
            return (Criteria) this;
        }

        public Criteria andAdjustUpperQuantityIsNull() {
            addCriterion("adjust_upper_quantity is null");
            return (Criteria) this;
        }

        public Criteria andAdjustUpperQuantityIsNotNull() {
            addCriterion("adjust_upper_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andAdjustUpperQuantityEqualTo(Integer value) {
            addCriterion("adjust_upper_quantity =", value, "adjustUpperQuantity");
            return (Criteria) this;
        }

        public Criteria andAdjustUpperQuantityNotEqualTo(Integer value) {
            addCriterion("adjust_upper_quantity <>", value, "adjustUpperQuantity");
            return (Criteria) this;
        }

        public Criteria andAdjustUpperQuantityGreaterThan(Integer value) {
            addCriterion("adjust_upper_quantity >", value, "adjustUpperQuantity");
            return (Criteria) this;
        }

        public Criteria andAdjustUpperQuantityGreaterThanOrEqualTo(Integer value) {
            addCriterion("adjust_upper_quantity >=", value, "adjustUpperQuantity");
            return (Criteria) this;
        }

        public Criteria andAdjustUpperQuantityLessThan(Integer value) {
            addCriterion("adjust_upper_quantity <", value, "adjustUpperQuantity");
            return (Criteria) this;
        }

        public Criteria andAdjustUpperQuantityLessThanOrEqualTo(Integer value) {
            addCriterion("adjust_upper_quantity <=", value, "adjustUpperQuantity");
            return (Criteria) this;
        }

        public Criteria andAdjustUpperQuantityIn(List<Integer> values) {
            addCriterion("adjust_upper_quantity in", values, "adjustUpperQuantity");
            return (Criteria) this;
        }

        public Criteria andAdjustUpperQuantityNotIn(List<Integer> values) {
            addCriterion("adjust_upper_quantity not in", values, "adjustUpperQuantity");
            return (Criteria) this;
        }

        public Criteria andAdjustUpperQuantityBetween(Integer value1, Integer value2) {
            addCriterion("adjust_upper_quantity between", value1, value2, "adjustUpperQuantity");
            return (Criteria) this;
        }

        public Criteria andAdjustUpperQuantityNotBetween(Integer value1, Integer value2) {
            addCriterion("adjust_upper_quantity not between", value1, value2, "adjustUpperQuantity");
            return (Criteria) this;
        }

        public Criteria andAdjustLowerQuantityIsNull() {
            addCriterion("adjust_lower_quantity is null");
            return (Criteria) this;
        }

        public Criteria andAdjustLowerQuantityIsNotNull() {
            addCriterion("adjust_lower_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andAdjustLowerQuantityEqualTo(Integer value) {
            addCriterion("adjust_lower_quantity =", value, "adjustLowerQuantity");
            return (Criteria) this;
        }

        public Criteria andAdjustLowerQuantityNotEqualTo(Integer value) {
            addCriterion("adjust_lower_quantity <>", value, "adjustLowerQuantity");
            return (Criteria) this;
        }

        public Criteria andAdjustLowerQuantityGreaterThan(Integer value) {
            addCriterion("adjust_lower_quantity >", value, "adjustLowerQuantity");
            return (Criteria) this;
        }

        public Criteria andAdjustLowerQuantityGreaterThanOrEqualTo(Integer value) {
            addCriterion("adjust_lower_quantity >=", value, "adjustLowerQuantity");
            return (Criteria) this;
        }

        public Criteria andAdjustLowerQuantityLessThan(Integer value) {
            addCriterion("adjust_lower_quantity <", value, "adjustLowerQuantity");
            return (Criteria) this;
        }

        public Criteria andAdjustLowerQuantityLessThanOrEqualTo(Integer value) {
            addCriterion("adjust_lower_quantity <=", value, "adjustLowerQuantity");
            return (Criteria) this;
        }

        public Criteria andAdjustLowerQuantityIn(List<Integer> values) {
            addCriterion("adjust_lower_quantity in", values, "adjustLowerQuantity");
            return (Criteria) this;
        }

        public Criteria andAdjustLowerQuantityNotIn(List<Integer> values) {
            addCriterion("adjust_lower_quantity not in", values, "adjustLowerQuantity");
            return (Criteria) this;
        }

        public Criteria andAdjustLowerQuantityBetween(Integer value1, Integer value2) {
            addCriterion("adjust_lower_quantity between", value1, value2, "adjustLowerQuantity");
            return (Criteria) this;
        }

        public Criteria andAdjustLowerQuantityNotBetween(Integer value1, Integer value2) {
            addCriterion("adjust_lower_quantity not between", value1, value2, "adjustLowerQuantity");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("`status` is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("`status` is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Byte value) {
            addCriterion("`status` =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Byte value) {
            addCriterion("`status` <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Byte value) {
            addCriterion("`status` >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("`status` >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Byte value) {
            addCriterion("`status` <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Byte value) {
            addCriterion("`status` <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Byte> values) {
            addCriterion("`status` in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Byte> values) {
            addCriterion("`status` not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Byte value1, Byte value2) {
            addCriterion("`status` between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("`status` not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andExtendIsNull() {
            addCriterion("extend is null");
            return (Criteria) this;
        }

        public Criteria andExtendIsNotNull() {
            addCriterion("extend is not null");
            return (Criteria) this;
        }

        public Criteria andExtendEqualTo(String value) {
            addCriterion("extend =", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotEqualTo(String value) {
            addCriterion("extend <>", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendGreaterThan(String value) {
            addCriterion("extend >", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendGreaterThanOrEqualTo(String value) {
            addCriterion("extend >=", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLessThan(String value) {
            addCriterion("extend <", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLessThanOrEqualTo(String value) {
            addCriterion("extend <=", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLike(String value) {
            addCriterion("extend like", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotLike(String value) {
            addCriterion("extend not like", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendIn(List<String> values) {
            addCriterion("extend in", values, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotIn(List<String> values) {
            addCriterion("extend not in", values, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendBetween(String value1, String value2) {
            addCriterion("extend between", value1, value2, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotBetween(String value1, String value2) {
            addCriterion("extend not between", value1, value2, "extend");
            return (Criteria) this;
        }

        public Criteria andVersionIsNull() {
            addCriterion("version is null");
            return (Criteria) this;
        }

        public Criteria andVersionIsNotNull() {
            addCriterion("version is not null");
            return (Criteria) this;
        }

        public Criteria andVersionEqualTo(Integer value) {
            addCriterion("version =", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotEqualTo(Integer value) {
            addCriterion("version <>", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThan(Integer value) {
            addCriterion("version >", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThanOrEqualTo(Integer value) {
            addCriterion("version >=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThan(Integer value) {
            addCriterion("version <", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThanOrEqualTo(Integer value) {
            addCriterion("version <=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionIn(List<Integer> values) {
            addCriterion("version in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotIn(List<Integer> values) {
            addCriterion("version not in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionBetween(Integer value1, Integer value2) {
            addCriterion("version between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotBetween(Integer value1, Integer value2) {
            addCriterion("version not between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("created_by is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("created_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(Long value) {
            addCriterion("created_by =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(Long value) {
            addCriterion("created_by <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(Long value) {
            addCriterion("created_by >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(Long value) {
            addCriterion("created_by >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(Long value) {
            addCriterion("created_by <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(Long value) {
            addCriterion("created_by <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<Long> values) {
            addCriterion("created_by in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<Long> values) {
            addCriterion("created_by not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(Long value1, Long value2) {
            addCriterion("created_by between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(Long value1, Long value2) {
            addCriterion("created_by not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedNameIsNull() {
            addCriterion("created_name is null");
            return (Criteria) this;
        }

        public Criteria andCreatedNameIsNotNull() {
            addCriterion("created_name is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedNameEqualTo(String value) {
            addCriterion("created_name =", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotEqualTo(String value) {
            addCriterion("created_name <>", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameGreaterThan(String value) {
            addCriterion("created_name >", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameGreaterThanOrEqualTo(String value) {
            addCriterion("created_name >=", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameLessThan(String value) {
            addCriterion("created_name <", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameLessThanOrEqualTo(String value) {
            addCriterion("created_name <=", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameLike(String value) {
            addCriterion("created_name like", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotLike(String value) {
            addCriterion("created_name not like", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameIn(List<String> values) {
            addCriterion("created_name in", values, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotIn(List<String> values) {
            addCriterion("created_name not in", values, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameBetween(String value1, String value2) {
            addCriterion("created_name between", value1, value2, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotBetween(String value1, String value2) {
            addCriterion("created_name not between", value1, value2, "createdName");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNull() {
            addCriterion("updated_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNotNull() {
            addCriterion("updated_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByEqualTo(Long value) {
            addCriterion("updated_by =", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotEqualTo(Long value) {
            addCriterion("updated_by <>", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThan(Long value) {
            addCriterion("updated_by >", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThanOrEqualTo(Long value) {
            addCriterion("updated_by >=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThan(Long value) {
            addCriterion("updated_by <", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThanOrEqualTo(Long value) {
            addCriterion("updated_by <=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIn(List<Long> values) {
            addCriterion("updated_by in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotIn(List<Long> values) {
            addCriterion("updated_by not in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByBetween(Long value1, Long value2) {
            addCriterion("updated_by between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotBetween(Long value1, Long value2) {
            addCriterion("updated_by not between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameIsNull() {
            addCriterion("updated_name is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameIsNotNull() {
            addCriterion("updated_name is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameEqualTo(String value) {
            addCriterion("updated_name =", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotEqualTo(String value) {
            addCriterion("updated_name <>", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameGreaterThan(String value) {
            addCriterion("updated_name >", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameGreaterThanOrEqualTo(String value) {
            addCriterion("updated_name >=", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameLessThan(String value) {
            addCriterion("updated_name <", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameLessThanOrEqualTo(String value) {
            addCriterion("updated_name <=", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameLike(String value) {
            addCriterion("updated_name like", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotLike(String value) {
            addCriterion("updated_name not like", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameIn(List<String> values) {
            addCriterion("updated_name in", values, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotIn(List<String> values) {
            addCriterion("updated_name not in", values, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameBetween(String value1, String value2) {
            addCriterion("updated_name between", value1, value2, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotBetween(String value1, String value2) {
            addCriterion("updated_name not between", value1, value2, "updatedName");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIsNull() {
            addCriterion("gmt_update is null");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIsNotNull() {
            addCriterion("gmt_update is not null");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateEqualTo(Date value) {
            addCriterion("gmt_update =", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotEqualTo(Date value) {
            addCriterion("gmt_update <>", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateGreaterThan(Date value) {
            addCriterion("gmt_update >", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_update >=", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateLessThan(Date value) {
            addCriterion("gmt_update <", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_update <=", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIn(List<Date> values) {
            addCriterion("gmt_update in", values, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotIn(List<Date> values) {
            addCriterion("gmt_update not in", values, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateBetween(Date value1, Date value2) {
            addCriterion("gmt_update between", value1, value2, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_update not between", value1, value2, "gmtUpdate");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}