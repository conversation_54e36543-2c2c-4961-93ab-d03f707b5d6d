package com.cowell.iscm.entityTidb;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class IscmStoreDistributeApplyOrderExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Long offset;

    public IscmStoreDistributeApplyOrderExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Long offset) {
        this.offset = offset;
    }

    public Long getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        protected void addCriterionForJDBCDate(String condition, Date value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value.getTime()), property);
        }

        protected void addCriterionForJDBCDate(String condition, List<Date> values, String property) {
            if (values == null || values.size() == 0) {
                throw new RuntimeException("Value list for " + property + " cannot be null or empty");
            }
            List<java.sql.Date> dateList = new ArrayList<java.sql.Date>();
            Iterator<Date> iter = values.iterator();
            while (iter.hasNext()) {
                dateList.add(new java.sql.Date(iter.next().getTime()));
            }
            addCriterion(condition, dateList, property);
        }

        protected void addCriterionForJDBCDate(String condition, Date value1, Date value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value1.getTime()), new java.sql.Date(value2.getTime()), property);
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andPlatformNameIsNull() {
            addCriterion("platform_name is null");
            return (Criteria) this;
        }

        public Criteria andPlatformNameIsNotNull() {
            addCriterion("platform_name is not null");
            return (Criteria) this;
        }

        public Criteria andPlatformNameEqualTo(String value) {
            addCriterion("platform_name =", value, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameNotEqualTo(String value) {
            addCriterion("platform_name <>", value, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameGreaterThan(String value) {
            addCriterion("platform_name >", value, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameGreaterThanOrEqualTo(String value) {
            addCriterion("platform_name >=", value, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameLessThan(String value) {
            addCriterion("platform_name <", value, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameLessThanOrEqualTo(String value) {
            addCriterion("platform_name <=", value, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameLike(String value) {
            addCriterion("platform_name like", value, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameNotLike(String value) {
            addCriterion("platform_name not like", value, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameIn(List<String> values) {
            addCriterion("platform_name in", values, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameNotIn(List<String> values) {
            addCriterion("platform_name not in", values, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameBetween(String value1, String value2) {
            addCriterion("platform_name between", value1, value2, "platformName");
            return (Criteria) this;
        }

        public Criteria andPlatformNameNotBetween(String value1, String value2) {
            addCriterion("platform_name not between", value1, value2, "platformName");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIsNull() {
            addCriterion("company_code is null");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIsNotNull() {
            addCriterion("company_code is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeEqualTo(String value) {
            addCriterion("company_code =", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotEqualTo(String value) {
            addCriterion("company_code <>", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeGreaterThan(String value) {
            addCriterion("company_code >", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeGreaterThanOrEqualTo(String value) {
            addCriterion("company_code >=", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLessThan(String value) {
            addCriterion("company_code <", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLessThanOrEqualTo(String value) {
            addCriterion("company_code <=", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLike(String value) {
            addCriterion("company_code like", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotLike(String value) {
            addCriterion("company_code not like", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIn(List<String> values) {
            addCriterion("company_code in", values, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotIn(List<String> values) {
            addCriterion("company_code not in", values, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeBetween(String value1, String value2) {
            addCriterion("company_code between", value1, value2, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotBetween(String value1, String value2) {
            addCriterion("company_code not between", value1, value2, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyNameIsNull() {
            addCriterion("company_name is null");
            return (Criteria) this;
        }

        public Criteria andCompanyNameIsNotNull() {
            addCriterion("company_name is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyNameEqualTo(String value) {
            addCriterion("company_name =", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotEqualTo(String value) {
            addCriterion("company_name <>", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameGreaterThan(String value) {
            addCriterion("company_name >", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameGreaterThanOrEqualTo(String value) {
            addCriterion("company_name >=", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameLessThan(String value) {
            addCriterion("company_name <", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameLessThanOrEqualTo(String value) {
            addCriterion("company_name <=", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameLike(String value) {
            addCriterion("company_name like", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotLike(String value) {
            addCriterion("company_name not like", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameIn(List<String> values) {
            addCriterion("company_name in", values, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotIn(List<String> values) {
            addCriterion("company_name not in", values, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameBetween(String value1, String value2) {
            addCriterion("company_name between", value1, value2, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotBetween(String value1, String value2) {
            addCriterion("company_name not between", value1, value2, "companyName");
            return (Criteria) this;
        }

        public Criteria andStoreNameIsNull() {
            addCriterion("store_name is null");
            return (Criteria) this;
        }

        public Criteria andStoreNameIsNotNull() {
            addCriterion("store_name is not null");
            return (Criteria) this;
        }

        public Criteria andStoreNameEqualTo(String value) {
            addCriterion("store_name =", value, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameNotEqualTo(String value) {
            addCriterion("store_name <>", value, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameGreaterThan(String value) {
            addCriterion("store_name >", value, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameGreaterThanOrEqualTo(String value) {
            addCriterion("store_name >=", value, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameLessThan(String value) {
            addCriterion("store_name <", value, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameLessThanOrEqualTo(String value) {
            addCriterion("store_name <=", value, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameLike(String value) {
            addCriterion("store_name like", value, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameNotLike(String value) {
            addCriterion("store_name not like", value, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameIn(List<String> values) {
            addCriterion("store_name in", values, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameNotIn(List<String> values) {
            addCriterion("store_name not in", values, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameBetween(String value1, String value2) {
            addCriterion("store_name between", value1, value2, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreNameNotBetween(String value1, String value2) {
            addCriterion("store_name not between", value1, value2, "storeName");
            return (Criteria) this;
        }

        public Criteria andStoreCodeIsNull() {
            addCriterion("store_code is null");
            return (Criteria) this;
        }

        public Criteria andStoreCodeIsNotNull() {
            addCriterion("store_code is not null");
            return (Criteria) this;
        }

        public Criteria andStoreCodeEqualTo(String value) {
            addCriterion("store_code =", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotEqualTo(String value) {
            addCriterion("store_code <>", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeGreaterThan(String value) {
            addCriterion("store_code >", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeGreaterThanOrEqualTo(String value) {
            addCriterion("store_code >=", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeLessThan(String value) {
            addCriterion("store_code <", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeLessThanOrEqualTo(String value) {
            addCriterion("store_code <=", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeLike(String value) {
            addCriterion("store_code like", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotLike(String value) {
            addCriterion("store_code not like", value, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeIn(List<String> values) {
            addCriterion("store_code in", values, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotIn(List<String> values) {
            addCriterion("store_code not in", values, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeBetween(String value1, String value2) {
            addCriterion("store_code between", value1, value2, "storeCode");
            return (Criteria) this;
        }

        public Criteria andStoreCodeNotBetween(String value1, String value2) {
            addCriterion("store_code not between", value1, value2, "storeCode");
            return (Criteria) this;
        }

        public Criteria andApplyDateIsNull() {
            addCriterion("apply_date is null");
            return (Criteria) this;
        }

        public Criteria andApplyDateIsNotNull() {
            addCriterion("apply_date is not null");
            return (Criteria) this;
        }

        public Criteria andApplyDateEqualTo(Date value) {
            addCriterionForJDBCDate("apply_date =", value, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("apply_date <>", value, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateGreaterThan(Date value) {
            addCriterionForJDBCDate("apply_date >", value, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("apply_date >=", value, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateLessThan(Date value) {
            addCriterionForJDBCDate("apply_date <", value, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("apply_date <=", value, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateIn(List<Date> values) {
            addCriterionForJDBCDate("apply_date in", values, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("apply_date not in", values, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("apply_date between", value1, value2, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("apply_date not between", value1, value2, "applyDate");
            return (Criteria) this;
        }

        public Criteria andGenerateTimeIsNull() {
            addCriterion("generate_time is null");
            return (Criteria) this;
        }

        public Criteria andGenerateTimeIsNotNull() {
            addCriterion("generate_time is not null");
            return (Criteria) this;
        }

        public Criteria andGenerateTimeEqualTo(Date value) {
            addCriterion("generate_time =", value, "generateTime");
            return (Criteria) this;
        }

        public Criteria andGenerateTimeNotEqualTo(Date value) {
            addCriterion("generate_time <>", value, "generateTime");
            return (Criteria) this;
        }

        public Criteria andGenerateTimeGreaterThan(Date value) {
            addCriterion("generate_time >", value, "generateTime");
            return (Criteria) this;
        }

        public Criteria andGenerateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("generate_time >=", value, "generateTime");
            return (Criteria) this;
        }

        public Criteria andGenerateTimeLessThan(Date value) {
            addCriterion("generate_time <", value, "generateTime");
            return (Criteria) this;
        }

        public Criteria andGenerateTimeLessThanOrEqualTo(Date value) {
            addCriterion("generate_time <=", value, "generateTime");
            return (Criteria) this;
        }

        public Criteria andGenerateTimeIn(List<Date> values) {
            addCriterion("generate_time in", values, "generateTime");
            return (Criteria) this;
        }

        public Criteria andGenerateTimeNotIn(List<Date> values) {
            addCriterion("generate_time not in", values, "generateTime");
            return (Criteria) this;
        }

        public Criteria andGenerateTimeBetween(Date value1, Date value2) {
            addCriterion("generate_time between", value1, value2, "generateTime");
            return (Criteria) this;
        }

        public Criteria andGenerateTimeNotBetween(Date value1, Date value2) {
            addCriterion("generate_time not between", value1, value2, "generateTime");
            return (Criteria) this;
        }

        public Criteria andGoodsNoIsNull() {
            addCriterion("goods_no is null");
            return (Criteria) this;
        }

        public Criteria andGoodsNoIsNotNull() {
            addCriterion("goods_no is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsNoEqualTo(String value) {
            addCriterion("goods_no =", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotEqualTo(String value) {
            addCriterion("goods_no <>", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoGreaterThan(String value) {
            addCriterion("goods_no >", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoGreaterThanOrEqualTo(String value) {
            addCriterion("goods_no >=", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoLessThan(String value) {
            addCriterion("goods_no <", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoLessThanOrEqualTo(String value) {
            addCriterion("goods_no <=", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoLike(String value) {
            addCriterion("goods_no like", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotLike(String value) {
            addCriterion("goods_no not like", value, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoIn(List<String> values) {
            addCriterion("goods_no in", values, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotIn(List<String> values) {
            addCriterion("goods_no not in", values, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoBetween(String value1, String value2) {
            addCriterion("goods_no between", value1, value2, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNoNotBetween(String value1, String value2) {
            addCriterion("goods_no not between", value1, value2, "goodsNo");
            return (Criteria) this;
        }

        public Criteria andGoodsNameIsNull() {
            addCriterion("goods_name is null");
            return (Criteria) this;
        }

        public Criteria andGoodsNameIsNotNull() {
            addCriterion("goods_name is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsNameEqualTo(String value) {
            addCriterion("goods_name =", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameNotEqualTo(String value) {
            addCriterion("goods_name <>", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameGreaterThan(String value) {
            addCriterion("goods_name >", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameGreaterThanOrEqualTo(String value) {
            addCriterion("goods_name >=", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameLessThan(String value) {
            addCriterion("goods_name <", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameLessThanOrEqualTo(String value) {
            addCriterion("goods_name <=", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameLike(String value) {
            addCriterion("goods_name like", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameNotLike(String value) {
            addCriterion("goods_name not like", value, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameIn(List<String> values) {
            addCriterion("goods_name in", values, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameNotIn(List<String> values) {
            addCriterion("goods_name not in", values, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameBetween(String value1, String value2) {
            addCriterion("goods_name between", value1, value2, "goodsName");
            return (Criteria) this;
        }

        public Criteria andGoodsNameNotBetween(String value1, String value2) {
            addCriterion("goods_name not between", value1, value2, "goodsName");
            return (Criteria) this;
        }

        public Criteria andSpecificationsIsNull() {
            addCriterion("specifications is null");
            return (Criteria) this;
        }

        public Criteria andSpecificationsIsNotNull() {
            addCriterion("specifications is not null");
            return (Criteria) this;
        }

        public Criteria andSpecificationsEqualTo(String value) {
            addCriterion("specifications =", value, "specifications");
            return (Criteria) this;
        }

        public Criteria andSpecificationsNotEqualTo(String value) {
            addCriterion("specifications <>", value, "specifications");
            return (Criteria) this;
        }

        public Criteria andSpecificationsGreaterThan(String value) {
            addCriterion("specifications >", value, "specifications");
            return (Criteria) this;
        }

        public Criteria andSpecificationsGreaterThanOrEqualTo(String value) {
            addCriterion("specifications >=", value, "specifications");
            return (Criteria) this;
        }

        public Criteria andSpecificationsLessThan(String value) {
            addCriterion("specifications <", value, "specifications");
            return (Criteria) this;
        }

        public Criteria andSpecificationsLessThanOrEqualTo(String value) {
            addCriterion("specifications <=", value, "specifications");
            return (Criteria) this;
        }

        public Criteria andSpecificationsLike(String value) {
            addCriterion("specifications like", value, "specifications");
            return (Criteria) this;
        }

        public Criteria andSpecificationsNotLike(String value) {
            addCriterion("specifications not like", value, "specifications");
            return (Criteria) this;
        }

        public Criteria andSpecificationsIn(List<String> values) {
            addCriterion("specifications in", values, "specifications");
            return (Criteria) this;
        }

        public Criteria andSpecificationsNotIn(List<String> values) {
            addCriterion("specifications not in", values, "specifications");
            return (Criteria) this;
        }

        public Criteria andSpecificationsBetween(String value1, String value2) {
            addCriterion("specifications between", value1, value2, "specifications");
            return (Criteria) this;
        }

        public Criteria andSpecificationsNotBetween(String value1, String value2) {
            addCriterion("specifications not between", value1, value2, "specifications");
            return (Criteria) this;
        }

        public Criteria andManufacturerIsNull() {
            addCriterion("manufacturer is null");
            return (Criteria) this;
        }

        public Criteria andManufacturerIsNotNull() {
            addCriterion("manufacturer is not null");
            return (Criteria) this;
        }

        public Criteria andManufacturerEqualTo(String value) {
            addCriterion("manufacturer =", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerNotEqualTo(String value) {
            addCriterion("manufacturer <>", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerGreaterThan(String value) {
            addCriterion("manufacturer >", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerGreaterThanOrEqualTo(String value) {
            addCriterion("manufacturer >=", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerLessThan(String value) {
            addCriterion("manufacturer <", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerLessThanOrEqualTo(String value) {
            addCriterion("manufacturer <=", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerLike(String value) {
            addCriterion("manufacturer like", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerNotLike(String value) {
            addCriterion("manufacturer not like", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerIn(List<String> values) {
            addCriterion("manufacturer in", values, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerNotIn(List<String> values) {
            addCriterion("manufacturer not in", values, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerBetween(String value1, String value2) {
            addCriterion("manufacturer between", value1, value2, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerNotBetween(String value1, String value2) {
            addCriterion("manufacturer not between", value1, value2, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andPosApplyOrderNoIsNull() {
            addCriterion("pos_apply_order_no is null");
            return (Criteria) this;
        }

        public Criteria andPosApplyOrderNoIsNotNull() {
            addCriterion("pos_apply_order_no is not null");
            return (Criteria) this;
        }

        public Criteria andPosApplyOrderNoEqualTo(String value) {
            addCriterion("pos_apply_order_no =", value, "posApplyOrderNo");
            return (Criteria) this;
        }

        public Criteria andPosApplyOrderNoNotEqualTo(String value) {
            addCriterion("pos_apply_order_no <>", value, "posApplyOrderNo");
            return (Criteria) this;
        }

        public Criteria andPosApplyOrderNoGreaterThan(String value) {
            addCriterion("pos_apply_order_no >", value, "posApplyOrderNo");
            return (Criteria) this;
        }

        public Criteria andPosApplyOrderNoGreaterThanOrEqualTo(String value) {
            addCriterion("pos_apply_order_no >=", value, "posApplyOrderNo");
            return (Criteria) this;
        }

        public Criteria andPosApplyOrderNoLessThan(String value) {
            addCriterion("pos_apply_order_no <", value, "posApplyOrderNo");
            return (Criteria) this;
        }

        public Criteria andPosApplyOrderNoLessThanOrEqualTo(String value) {
            addCriterion("pos_apply_order_no <=", value, "posApplyOrderNo");
            return (Criteria) this;
        }

        public Criteria andPosApplyOrderNoLike(String value) {
            addCriterion("pos_apply_order_no like", value, "posApplyOrderNo");
            return (Criteria) this;
        }

        public Criteria andPosApplyOrderNoNotLike(String value) {
            addCriterion("pos_apply_order_no not like", value, "posApplyOrderNo");
            return (Criteria) this;
        }

        public Criteria andPosApplyOrderNoIn(List<String> values) {
            addCriterion("pos_apply_order_no in", values, "posApplyOrderNo");
            return (Criteria) this;
        }

        public Criteria andPosApplyOrderNoNotIn(List<String> values) {
            addCriterion("pos_apply_order_no not in", values, "posApplyOrderNo");
            return (Criteria) this;
        }

        public Criteria andPosApplyOrderNoBetween(String value1, String value2) {
            addCriterion("pos_apply_order_no between", value1, value2, "posApplyOrderNo");
            return (Criteria) this;
        }

        public Criteria andPosApplyOrderNoNotBetween(String value1, String value2) {
            addCriterion("pos_apply_order_no not between", value1, value2, "posApplyOrderNo");
            return (Criteria) this;
        }

        public Criteria andApplyQuantityIsNull() {
            addCriterion("apply_quantity is null");
            return (Criteria) this;
        }

        public Criteria andApplyQuantityIsNotNull() {
            addCriterion("apply_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andApplyQuantityEqualTo(BigDecimal value) {
            addCriterion("apply_quantity =", value, "applyQuantity");
            return (Criteria) this;
        }

        public Criteria andApplyQuantityNotEqualTo(BigDecimal value) {
            addCriterion("apply_quantity <>", value, "applyQuantity");
            return (Criteria) this;
        }

        public Criteria andApplyQuantityGreaterThan(BigDecimal value) {
            addCriterion("apply_quantity >", value, "applyQuantity");
            return (Criteria) this;
        }

        public Criteria andApplyQuantityGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("apply_quantity >=", value, "applyQuantity");
            return (Criteria) this;
        }

        public Criteria andApplyQuantityLessThan(BigDecimal value) {
            addCriterion("apply_quantity <", value, "applyQuantity");
            return (Criteria) this;
        }

        public Criteria andApplyQuantityLessThanOrEqualTo(BigDecimal value) {
            addCriterion("apply_quantity <=", value, "applyQuantity");
            return (Criteria) this;
        }

        public Criteria andApplyQuantityIn(List<BigDecimal> values) {
            addCriterion("apply_quantity in", values, "applyQuantity");
            return (Criteria) this;
        }

        public Criteria andApplyQuantityNotIn(List<BigDecimal> values) {
            addCriterion("apply_quantity not in", values, "applyQuantity");
            return (Criteria) this;
        }

        public Criteria andApplyQuantityBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("apply_quantity between", value1, value2, "applyQuantity");
            return (Criteria) this;
        }

        public Criteria andApplyQuantityNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("apply_quantity not between", value1, value2, "applyQuantity");
            return (Criteria) this;
        }

        public Criteria andApporveQuantityIsNull() {
            addCriterion("apporve_quantity is null");
            return (Criteria) this;
        }

        public Criteria andApporveQuantityIsNotNull() {
            addCriterion("apporve_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andApporveQuantityEqualTo(BigDecimal value) {
            addCriterion("apporve_quantity =", value, "apporveQuantity");
            return (Criteria) this;
        }

        public Criteria andApporveQuantityNotEqualTo(BigDecimal value) {
            addCriterion("apporve_quantity <>", value, "apporveQuantity");
            return (Criteria) this;
        }

        public Criteria andApporveQuantityGreaterThan(BigDecimal value) {
            addCriterion("apporve_quantity >", value, "apporveQuantity");
            return (Criteria) this;
        }

        public Criteria andApporveQuantityGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("apporve_quantity >=", value, "apporveQuantity");
            return (Criteria) this;
        }

        public Criteria andApporveQuantityLessThan(BigDecimal value) {
            addCriterion("apporve_quantity <", value, "apporveQuantity");
            return (Criteria) this;
        }

        public Criteria andApporveQuantityLessThanOrEqualTo(BigDecimal value) {
            addCriterion("apporve_quantity <=", value, "apporveQuantity");
            return (Criteria) this;
        }

        public Criteria andApporveQuantityIn(List<BigDecimal> values) {
            addCriterion("apporve_quantity in", values, "apporveQuantity");
            return (Criteria) this;
        }

        public Criteria andApporveQuantityNotIn(List<BigDecimal> values) {
            addCriterion("apporve_quantity not in", values, "apporveQuantity");
            return (Criteria) this;
        }

        public Criteria andApporveQuantityBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("apporve_quantity between", value1, value2, "apporveQuantity");
            return (Criteria) this;
        }

        public Criteria andApporveQuantityNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("apporve_quantity not between", value1, value2, "apporveQuantity");
            return (Criteria) this;
        }

        public Criteria andModifyResultIsNull() {
            addCriterion("modify_result is null");
            return (Criteria) this;
        }

        public Criteria andModifyResultIsNotNull() {
            addCriterion("modify_result is not null");
            return (Criteria) this;
        }

        public Criteria andModifyResultEqualTo(Byte value) {
            addCriterion("modify_result =", value, "modifyResult");
            return (Criteria) this;
        }

        public Criteria andModifyResultNotEqualTo(Byte value) {
            addCriterion("modify_result <>", value, "modifyResult");
            return (Criteria) this;
        }

        public Criteria andModifyResultGreaterThan(Byte value) {
            addCriterion("modify_result >", value, "modifyResult");
            return (Criteria) this;
        }

        public Criteria andModifyResultGreaterThanOrEqualTo(Byte value) {
            addCriterion("modify_result >=", value, "modifyResult");
            return (Criteria) this;
        }

        public Criteria andModifyResultLessThan(Byte value) {
            addCriterion("modify_result <", value, "modifyResult");
            return (Criteria) this;
        }

        public Criteria andModifyResultLessThanOrEqualTo(Byte value) {
            addCriterion("modify_result <=", value, "modifyResult");
            return (Criteria) this;
        }

        public Criteria andModifyResultIn(List<Byte> values) {
            addCriterion("modify_result in", values, "modifyResult");
            return (Criteria) this;
        }

        public Criteria andModifyResultNotIn(List<Byte> values) {
            addCriterion("modify_result not in", values, "modifyResult");
            return (Criteria) this;
        }

        public Criteria andModifyResultBetween(Byte value1, Byte value2) {
            addCriterion("modify_result between", value1, value2, "modifyResult");
            return (Criteria) this;
        }

        public Criteria andModifyResultNotBetween(Byte value1, Byte value2) {
            addCriterion("modify_result not between", value1, value2, "modifyResult");
            return (Criteria) this;
        }

        public Criteria andPosAverageDailySalesIsNull() {
            addCriterion("pos_average_daily_sales is null");
            return (Criteria) this;
        }

        public Criteria andPosAverageDailySalesIsNotNull() {
            addCriterion("pos_average_daily_sales is not null");
            return (Criteria) this;
        }

        public Criteria andPosAverageDailySalesEqualTo(BigDecimal value) {
            addCriterion("pos_average_daily_sales =", value, "posAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andPosAverageDailySalesNotEqualTo(BigDecimal value) {
            addCriterion("pos_average_daily_sales <>", value, "posAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andPosAverageDailySalesGreaterThan(BigDecimal value) {
            addCriterion("pos_average_daily_sales >", value, "posAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andPosAverageDailySalesGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("pos_average_daily_sales >=", value, "posAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andPosAverageDailySalesLessThan(BigDecimal value) {
            addCriterion("pos_average_daily_sales <", value, "posAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andPosAverageDailySalesLessThanOrEqualTo(BigDecimal value) {
            addCriterion("pos_average_daily_sales <=", value, "posAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andPosAverageDailySalesIn(List<BigDecimal> values) {
            addCriterion("pos_average_daily_sales in", values, "posAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andPosAverageDailySalesNotIn(List<BigDecimal> values) {
            addCriterion("pos_average_daily_sales not in", values, "posAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andPosAverageDailySalesBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("pos_average_daily_sales between", value1, value2, "posAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andPosAverageDailySalesNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("pos_average_daily_sales not between", value1, value2, "posAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andBdpAverageDailySalesIsNull() {
            addCriterion("bdp_average_daily_sales is null");
            return (Criteria) this;
        }

        public Criteria andBdpAverageDailySalesIsNotNull() {
            addCriterion("bdp_average_daily_sales is not null");
            return (Criteria) this;
        }

        public Criteria andBdpAverageDailySalesEqualTo(BigDecimal value) {
            addCriterion("bdp_average_daily_sales =", value, "bdpAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andBdpAverageDailySalesNotEqualTo(BigDecimal value) {
            addCriterion("bdp_average_daily_sales <>", value, "bdpAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andBdpAverageDailySalesGreaterThan(BigDecimal value) {
            addCriterion("bdp_average_daily_sales >", value, "bdpAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andBdpAverageDailySalesGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("bdp_average_daily_sales >=", value, "bdpAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andBdpAverageDailySalesLessThan(BigDecimal value) {
            addCriterion("bdp_average_daily_sales <", value, "bdpAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andBdpAverageDailySalesLessThanOrEqualTo(BigDecimal value) {
            addCriterion("bdp_average_daily_sales <=", value, "bdpAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andBdpAverageDailySalesIn(List<BigDecimal> values) {
            addCriterion("bdp_average_daily_sales in", values, "bdpAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andBdpAverageDailySalesNotIn(List<BigDecimal> values) {
            addCriterion("bdp_average_daily_sales not in", values, "bdpAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andBdpAverageDailySalesBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("bdp_average_daily_sales between", value1, value2, "bdpAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andBdpAverageDailySalesNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("bdp_average_daily_sales not between", value1, value2, "bdpAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andStoreThenMonthSalesIsNull() {
            addCriterion("store_then_month_sales is null");
            return (Criteria) this;
        }

        public Criteria andStoreThenMonthSalesIsNotNull() {
            addCriterion("store_then_month_sales is not null");
            return (Criteria) this;
        }

        public Criteria andStoreThenMonthSalesEqualTo(BigDecimal value) {
            addCriterion("store_then_month_sales =", value, "storeThenMonthSales");
            return (Criteria) this;
        }

        public Criteria andStoreThenMonthSalesNotEqualTo(BigDecimal value) {
            addCriterion("store_then_month_sales <>", value, "storeThenMonthSales");
            return (Criteria) this;
        }

        public Criteria andStoreThenMonthSalesGreaterThan(BigDecimal value) {
            addCriterion("store_then_month_sales >", value, "storeThenMonthSales");
            return (Criteria) this;
        }

        public Criteria andStoreThenMonthSalesGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("store_then_month_sales >=", value, "storeThenMonthSales");
            return (Criteria) this;
        }

        public Criteria andStoreThenMonthSalesLessThan(BigDecimal value) {
            addCriterion("store_then_month_sales <", value, "storeThenMonthSales");
            return (Criteria) this;
        }

        public Criteria andStoreThenMonthSalesLessThanOrEqualTo(BigDecimal value) {
            addCriterion("store_then_month_sales <=", value, "storeThenMonthSales");
            return (Criteria) this;
        }

        public Criteria andStoreThenMonthSalesIn(List<BigDecimal> values) {
            addCriterion("store_then_month_sales in", values, "storeThenMonthSales");
            return (Criteria) this;
        }

        public Criteria andStoreThenMonthSalesNotIn(List<BigDecimal> values) {
            addCriterion("store_then_month_sales not in", values, "storeThenMonthSales");
            return (Criteria) this;
        }

        public Criteria andStoreThenMonthSalesBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("store_then_month_sales between", value1, value2, "storeThenMonthSales");
            return (Criteria) this;
        }

        public Criteria andStoreThenMonthSalesNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("store_then_month_sales not between", value1, value2, "storeThenMonthSales");
            return (Criteria) this;
        }

        public Criteria andStoreThenStockQuantityIsNull() {
            addCriterion("store_then_stock_quantity is null");
            return (Criteria) this;
        }

        public Criteria andStoreThenStockQuantityIsNotNull() {
            addCriterion("store_then_stock_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andStoreThenStockQuantityEqualTo(BigDecimal value) {
            addCriterion("store_then_stock_quantity =", value, "storeThenStockQuantity");
            return (Criteria) this;
        }

        public Criteria andStoreThenStockQuantityNotEqualTo(BigDecimal value) {
            addCriterion("store_then_stock_quantity <>", value, "storeThenStockQuantity");
            return (Criteria) this;
        }

        public Criteria andStoreThenStockQuantityGreaterThan(BigDecimal value) {
            addCriterion("store_then_stock_quantity >", value, "storeThenStockQuantity");
            return (Criteria) this;
        }

        public Criteria andStoreThenStockQuantityGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("store_then_stock_quantity >=", value, "storeThenStockQuantity");
            return (Criteria) this;
        }

        public Criteria andStoreThenStockQuantityLessThan(BigDecimal value) {
            addCriterion("store_then_stock_quantity <", value, "storeThenStockQuantity");
            return (Criteria) this;
        }

        public Criteria andStoreThenStockQuantityLessThanOrEqualTo(BigDecimal value) {
            addCriterion("store_then_stock_quantity <=", value, "storeThenStockQuantity");
            return (Criteria) this;
        }

        public Criteria andStoreThenStockQuantityIn(List<BigDecimal> values) {
            addCriterion("store_then_stock_quantity in", values, "storeThenStockQuantity");
            return (Criteria) this;
        }

        public Criteria andStoreThenStockQuantityNotIn(List<BigDecimal> values) {
            addCriterion("store_then_stock_quantity not in", values, "storeThenStockQuantity");
            return (Criteria) this;
        }

        public Criteria andStoreThenStockQuantityBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("store_then_stock_quantity between", value1, value2, "storeThenStockQuantity");
            return (Criteria) this;
        }

        public Criteria andStoreThenStockQuantityNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("store_then_stock_quantity not between", value1, value2, "storeThenStockQuantity");
            return (Criteria) this;
        }

        public Criteria andStoreUpperLimitIsNull() {
            addCriterion("store_upper_limit is null");
            return (Criteria) this;
        }

        public Criteria andStoreUpperLimitIsNotNull() {
            addCriterion("store_upper_limit is not null");
            return (Criteria) this;
        }

        public Criteria andStoreUpperLimitEqualTo(BigDecimal value) {
            addCriterion("store_upper_limit =", value, "storeUpperLimit");
            return (Criteria) this;
        }

        public Criteria andStoreUpperLimitNotEqualTo(BigDecimal value) {
            addCriterion("store_upper_limit <>", value, "storeUpperLimit");
            return (Criteria) this;
        }

        public Criteria andStoreUpperLimitGreaterThan(BigDecimal value) {
            addCriterion("store_upper_limit >", value, "storeUpperLimit");
            return (Criteria) this;
        }

        public Criteria andStoreUpperLimitGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("store_upper_limit >=", value, "storeUpperLimit");
            return (Criteria) this;
        }

        public Criteria andStoreUpperLimitLessThan(BigDecimal value) {
            addCriterion("store_upper_limit <", value, "storeUpperLimit");
            return (Criteria) this;
        }

        public Criteria andStoreUpperLimitLessThanOrEqualTo(BigDecimal value) {
            addCriterion("store_upper_limit <=", value, "storeUpperLimit");
            return (Criteria) this;
        }

        public Criteria andStoreUpperLimitIn(List<BigDecimal> values) {
            addCriterion("store_upper_limit in", values, "storeUpperLimit");
            return (Criteria) this;
        }

        public Criteria andStoreUpperLimitNotIn(List<BigDecimal> values) {
            addCriterion("store_upper_limit not in", values, "storeUpperLimit");
            return (Criteria) this;
        }

        public Criteria andStoreUpperLimitBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("store_upper_limit between", value1, value2, "storeUpperLimit");
            return (Criteria) this;
        }

        public Criteria andStoreUpperLimitNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("store_upper_limit not between", value1, value2, "storeUpperLimit");
            return (Criteria) this;
        }

        public Criteria andStoreLowerLimitIsNull() {
            addCriterion("store_lower_limit is null");
            return (Criteria) this;
        }

        public Criteria andStoreLowerLimitIsNotNull() {
            addCriterion("store_lower_limit is not null");
            return (Criteria) this;
        }

        public Criteria andStoreLowerLimitEqualTo(BigDecimal value) {
            addCriterion("store_lower_limit =", value, "storeLowerLimit");
            return (Criteria) this;
        }

        public Criteria andStoreLowerLimitNotEqualTo(BigDecimal value) {
            addCriterion("store_lower_limit <>", value, "storeLowerLimit");
            return (Criteria) this;
        }

        public Criteria andStoreLowerLimitGreaterThan(BigDecimal value) {
            addCriterion("store_lower_limit >", value, "storeLowerLimit");
            return (Criteria) this;
        }

        public Criteria andStoreLowerLimitGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("store_lower_limit >=", value, "storeLowerLimit");
            return (Criteria) this;
        }

        public Criteria andStoreLowerLimitLessThan(BigDecimal value) {
            addCriterion("store_lower_limit <", value, "storeLowerLimit");
            return (Criteria) this;
        }

        public Criteria andStoreLowerLimitLessThanOrEqualTo(BigDecimal value) {
            addCriterion("store_lower_limit <=", value, "storeLowerLimit");
            return (Criteria) this;
        }

        public Criteria andStoreLowerLimitIn(List<BigDecimal> values) {
            addCriterion("store_lower_limit in", values, "storeLowerLimit");
            return (Criteria) this;
        }

        public Criteria andStoreLowerLimitNotIn(List<BigDecimal> values) {
            addCriterion("store_lower_limit not in", values, "storeLowerLimit");
            return (Criteria) this;
        }

        public Criteria andStoreLowerLimitBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("store_lower_limit between", value1, value2, "storeLowerLimit");
            return (Criteria) this;
        }

        public Criteria andStoreLowerLimitNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("store_lower_limit not between", value1, value2, "storeLowerLimit");
            return (Criteria) this;
        }

        public Criteria andSynthesizeAverageDailySalesIsNull() {
            addCriterion("synthesize_average_daily_sales is null");
            return (Criteria) this;
        }

        public Criteria andSynthesizeAverageDailySalesIsNotNull() {
            addCriterion("synthesize_average_daily_sales is not null");
            return (Criteria) this;
        }

        public Criteria andSynthesizeAverageDailySalesEqualTo(BigDecimal value) {
            addCriterion("synthesize_average_daily_sales =", value, "synthesizeAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andSynthesizeAverageDailySalesNotEqualTo(BigDecimal value) {
            addCriterion("synthesize_average_daily_sales <>", value, "synthesizeAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andSynthesizeAverageDailySalesGreaterThan(BigDecimal value) {
            addCriterion("synthesize_average_daily_sales >", value, "synthesizeAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andSynthesizeAverageDailySalesGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("synthesize_average_daily_sales >=", value, "synthesizeAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andSynthesizeAverageDailySalesLessThan(BigDecimal value) {
            addCriterion("synthesize_average_daily_sales <", value, "synthesizeAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andSynthesizeAverageDailySalesLessThanOrEqualTo(BigDecimal value) {
            addCriterion("synthesize_average_daily_sales <=", value, "synthesizeAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andSynthesizeAverageDailySalesIn(List<BigDecimal> values) {
            addCriterion("synthesize_average_daily_sales in", values, "synthesizeAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andSynthesizeAverageDailySalesNotIn(List<BigDecimal> values) {
            addCriterion("synthesize_average_daily_sales not in", values, "synthesizeAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andSynthesizeAverageDailySalesBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("synthesize_average_daily_sales between", value1, value2, "synthesizeAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andSynthesizeAverageDailySalesNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("synthesize_average_daily_sales not between", value1, value2, "synthesizeAverageDailySales");
            return (Criteria) this;
        }

        public Criteria andStoreStockQuantityIsNull() {
            addCriterion("store_stock_quantity is null");
            return (Criteria) this;
        }

        public Criteria andStoreStockQuantityIsNotNull() {
            addCriterion("store_stock_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andStoreStockQuantityEqualTo(BigDecimal value) {
            addCriterion("store_stock_quantity =", value, "storeStockQuantity");
            return (Criteria) this;
        }

        public Criteria andStoreStockQuantityNotEqualTo(BigDecimal value) {
            addCriterion("store_stock_quantity <>", value, "storeStockQuantity");
            return (Criteria) this;
        }

        public Criteria andStoreStockQuantityGreaterThan(BigDecimal value) {
            addCriterion("store_stock_quantity >", value, "storeStockQuantity");
            return (Criteria) this;
        }

        public Criteria andStoreStockQuantityGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("store_stock_quantity >=", value, "storeStockQuantity");
            return (Criteria) this;
        }

        public Criteria andStoreStockQuantityLessThan(BigDecimal value) {
            addCriterion("store_stock_quantity <", value, "storeStockQuantity");
            return (Criteria) this;
        }

        public Criteria andStoreStockQuantityLessThanOrEqualTo(BigDecimal value) {
            addCriterion("store_stock_quantity <=", value, "storeStockQuantity");
            return (Criteria) this;
        }

        public Criteria andStoreStockQuantityIn(List<BigDecimal> values) {
            addCriterion("store_stock_quantity in", values, "storeStockQuantity");
            return (Criteria) this;
        }

        public Criteria andStoreStockQuantityNotIn(List<BigDecimal> values) {
            addCriterion("store_stock_quantity not in", values, "storeStockQuantity");
            return (Criteria) this;
        }

        public Criteria andStoreStockQuantityBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("store_stock_quantity between", value1, value2, "storeStockQuantity");
            return (Criteria) this;
        }

        public Criteria andStoreStockQuantityNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("store_stock_quantity not between", value1, value2, "storeStockQuantity");
            return (Criteria) this;
        }

        public Criteria andTransitQuantityIsNull() {
            addCriterion("transit_quantity is null");
            return (Criteria) this;
        }

        public Criteria andTransitQuantityIsNotNull() {
            addCriterion("transit_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andTransitQuantityEqualTo(BigDecimal value) {
            addCriterion("transit_quantity =", value, "transitQuantity");
            return (Criteria) this;
        }

        public Criteria andTransitQuantityNotEqualTo(BigDecimal value) {
            addCriterion("transit_quantity <>", value, "transitQuantity");
            return (Criteria) this;
        }

        public Criteria andTransitQuantityGreaterThan(BigDecimal value) {
            addCriterion("transit_quantity >", value, "transitQuantity");
            return (Criteria) this;
        }

        public Criteria andTransitQuantityGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("transit_quantity >=", value, "transitQuantity");
            return (Criteria) this;
        }

        public Criteria andTransitQuantityLessThan(BigDecimal value) {
            addCriterion("transit_quantity <", value, "transitQuantity");
            return (Criteria) this;
        }

        public Criteria andTransitQuantityLessThanOrEqualTo(BigDecimal value) {
            addCriterion("transit_quantity <=", value, "transitQuantity");
            return (Criteria) this;
        }

        public Criteria andTransitQuantityIn(List<BigDecimal> values) {
            addCriterion("transit_quantity in", values, "transitQuantity");
            return (Criteria) this;
        }

        public Criteria andTransitQuantityNotIn(List<BigDecimal> values) {
            addCriterion("transit_quantity not in", values, "transitQuantity");
            return (Criteria) this;
        }

        public Criteria andTransitQuantityBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("transit_quantity between", value1, value2, "transitQuantity");
            return (Criteria) this;
        }

        public Criteria andTransitQuantityNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("transit_quantity not between", value1, value2, "transitQuantity");
            return (Criteria) this;
        }

        public Criteria andLowerDisplayQuantityIsNull() {
            addCriterion("lower_display_quantity is null");
            return (Criteria) this;
        }

        public Criteria andLowerDisplayQuantityIsNotNull() {
            addCriterion("lower_display_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andLowerDisplayQuantityEqualTo(BigDecimal value) {
            addCriterion("lower_display_quantity =", value, "lowerDisplayQuantity");
            return (Criteria) this;
        }

        public Criteria andLowerDisplayQuantityNotEqualTo(BigDecimal value) {
            addCriterion("lower_display_quantity <>", value, "lowerDisplayQuantity");
            return (Criteria) this;
        }

        public Criteria andLowerDisplayQuantityGreaterThan(BigDecimal value) {
            addCriterion("lower_display_quantity >", value, "lowerDisplayQuantity");
            return (Criteria) this;
        }

        public Criteria andLowerDisplayQuantityGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("lower_display_quantity >=", value, "lowerDisplayQuantity");
            return (Criteria) this;
        }

        public Criteria andLowerDisplayQuantityLessThan(BigDecimal value) {
            addCriterion("lower_display_quantity <", value, "lowerDisplayQuantity");
            return (Criteria) this;
        }

        public Criteria andLowerDisplayQuantityLessThanOrEqualTo(BigDecimal value) {
            addCriterion("lower_display_quantity <=", value, "lowerDisplayQuantity");
            return (Criteria) this;
        }

        public Criteria andLowerDisplayQuantityIn(List<BigDecimal> values) {
            addCriterion("lower_display_quantity in", values, "lowerDisplayQuantity");
            return (Criteria) this;
        }

        public Criteria andLowerDisplayQuantityNotIn(List<BigDecimal> values) {
            addCriterion("lower_display_quantity not in", values, "lowerDisplayQuantity");
            return (Criteria) this;
        }

        public Criteria andLowerDisplayQuantityBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("lower_display_quantity between", value1, value2, "lowerDisplayQuantity");
            return (Criteria) this;
        }

        public Criteria andLowerDisplayQuantityNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("lower_display_quantity not between", value1, value2, "lowerDisplayQuantity");
            return (Criteria) this;
        }

        public Criteria andOrderTypeIsNull() {
            addCriterion("order_type is null");
            return (Criteria) this;
        }

        public Criteria andOrderTypeIsNotNull() {
            addCriterion("order_type is not null");
            return (Criteria) this;
        }

        public Criteria andOrderTypeEqualTo(Byte value) {
            addCriterion("order_type =", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeNotEqualTo(Byte value) {
            addCriterion("order_type <>", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeGreaterThan(Byte value) {
            addCriterion("order_type >", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("order_type >=", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeLessThan(Byte value) {
            addCriterion("order_type <", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeLessThanOrEqualTo(Byte value) {
            addCriterion("order_type <=", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeIn(List<Byte> values) {
            addCriterion("order_type in", values, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeNotIn(List<Byte> values) {
            addCriterion("order_type not in", values, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeBetween(Byte value1, Byte value2) {
            addCriterion("order_type between", value1, value2, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("order_type not between", value1, value2, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderStatusIsNull() {
            addCriterion("order_status is null");
            return (Criteria) this;
        }

        public Criteria andOrderStatusIsNotNull() {
            addCriterion("order_status is not null");
            return (Criteria) this;
        }

        public Criteria andOrderStatusEqualTo(Byte value) {
            addCriterion("order_status =", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusNotEqualTo(Byte value) {
            addCriterion("order_status <>", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusGreaterThan(Byte value) {
            addCriterion("order_status >", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("order_status >=", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusLessThan(Byte value) {
            addCriterion("order_status <", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusLessThanOrEqualTo(Byte value) {
            addCriterion("order_status <=", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusIn(List<Byte> values) {
            addCriterion("order_status in", values, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusNotIn(List<Byte> values) {
            addCriterion("order_status not in", values, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusBetween(Byte value1, Byte value2) {
            addCriterion("order_status between", value1, value2, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("order_status not between", value1, value2, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("`status` is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("`status` is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Byte value) {
            addCriterion("`status` =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Byte value) {
            addCriterion("`status` <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Byte value) {
            addCriterion("`status` >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("`status` >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Byte value) {
            addCriterion("`status` <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Byte value) {
            addCriterion("`status` <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Byte> values) {
            addCriterion("`status` in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Byte> values) {
            addCriterion("`status` not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Byte value1, Byte value2) {
            addCriterion("`status` between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("`status` not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIsNull() {
            addCriterion("gmt_update is null");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIsNotNull() {
            addCriterion("gmt_update is not null");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateEqualTo(Date value) {
            addCriterion("gmt_update =", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotEqualTo(Date value) {
            addCriterion("gmt_update <>", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateGreaterThan(Date value) {
            addCriterion("gmt_update >", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_update >=", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateLessThan(Date value) {
            addCriterion("gmt_update <", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_update <=", value, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateIn(List<Date> values) {
            addCriterion("gmt_update in", values, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotIn(List<Date> values) {
            addCriterion("gmt_update not in", values, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateBetween(Date value1, Date value2) {
            addCriterion("gmt_update between", value1, value2, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andGmtUpdateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_update not between", value1, value2, "gmtUpdate");
            return (Criteria) this;
        }

        public Criteria andExtendIsNull() {
            addCriterion("extend is null");
            return (Criteria) this;
        }

        public Criteria andExtendIsNotNull() {
            addCriterion("extend is not null");
            return (Criteria) this;
        }

        public Criteria andExtendEqualTo(String value) {
            addCriterion("extend =", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotEqualTo(String value) {
            addCriterion("extend <>", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendGreaterThan(String value) {
            addCriterion("extend >", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendGreaterThanOrEqualTo(String value) {
            addCriterion("extend >=", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLessThan(String value) {
            addCriterion("extend <", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLessThanOrEqualTo(String value) {
            addCriterion("extend <=", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLike(String value) {
            addCriterion("extend like", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotLike(String value) {
            addCriterion("extend not like", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendIn(List<String> values) {
            addCriterion("extend in", values, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotIn(List<String> values) {
            addCriterion("extend not in", values, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendBetween(String value1, String value2) {
            addCriterion("extend between", value1, value2, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotBetween(String value1, String value2) {
            addCriterion("extend not between", value1, value2, "extend");
            return (Criteria) this;
        }

        public Criteria andVersionIsNull() {
            addCriterion("version is null");
            return (Criteria) this;
        }

        public Criteria andVersionIsNotNull() {
            addCriterion("version is not null");
            return (Criteria) this;
        }

        public Criteria andVersionEqualTo(Integer value) {
            addCriterion("version =", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotEqualTo(Integer value) {
            addCriterion("version <>", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThan(Integer value) {
            addCriterion("version >", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThanOrEqualTo(Integer value) {
            addCriterion("version >=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThan(Integer value) {
            addCriterion("version <", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThanOrEqualTo(Integer value) {
            addCriterion("version <=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionIn(List<Integer> values) {
            addCriterion("version in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotIn(List<Integer> values) {
            addCriterion("version not in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionBetween(Integer value1, Integer value2) {
            addCriterion("version between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotBetween(Integer value1, Integer value2) {
            addCriterion("version not between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("created_by is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("created_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(Long value) {
            addCriterion("created_by =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(Long value) {
            addCriterion("created_by <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(Long value) {
            addCriterion("created_by >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(Long value) {
            addCriterion("created_by >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(Long value) {
            addCriterion("created_by <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(Long value) {
            addCriterion("created_by <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<Long> values) {
            addCriterion("created_by in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<Long> values) {
            addCriterion("created_by not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(Long value1, Long value2) {
            addCriterion("created_by between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(Long value1, Long value2) {
            addCriterion("created_by not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedNameIsNull() {
            addCriterion("created_name is null");
            return (Criteria) this;
        }

        public Criteria andCreatedNameIsNotNull() {
            addCriterion("created_name is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedNameEqualTo(String value) {
            addCriterion("created_name =", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotEqualTo(String value) {
            addCriterion("created_name <>", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameGreaterThan(String value) {
            addCriterion("created_name >", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameGreaterThanOrEqualTo(String value) {
            addCriterion("created_name >=", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameLessThan(String value) {
            addCriterion("created_name <", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameLessThanOrEqualTo(String value) {
            addCriterion("created_name <=", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameLike(String value) {
            addCriterion("created_name like", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotLike(String value) {
            addCriterion("created_name not like", value, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameIn(List<String> values) {
            addCriterion("created_name in", values, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotIn(List<String> values) {
            addCriterion("created_name not in", values, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameBetween(String value1, String value2) {
            addCriterion("created_name between", value1, value2, "createdName");
            return (Criteria) this;
        }

        public Criteria andCreatedNameNotBetween(String value1, String value2) {
            addCriterion("created_name not between", value1, value2, "createdName");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNull() {
            addCriterion("updated_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNotNull() {
            addCriterion("updated_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByEqualTo(Long value) {
            addCriterion("updated_by =", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotEqualTo(Long value) {
            addCriterion("updated_by <>", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThan(Long value) {
            addCriterion("updated_by >", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThanOrEqualTo(Long value) {
            addCriterion("updated_by >=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThan(Long value) {
            addCriterion("updated_by <", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThanOrEqualTo(Long value) {
            addCriterion("updated_by <=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIn(List<Long> values) {
            addCriterion("updated_by in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotIn(List<Long> values) {
            addCriterion("updated_by not in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByBetween(Long value1, Long value2) {
            addCriterion("updated_by between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotBetween(Long value1, Long value2) {
            addCriterion("updated_by not between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameIsNull() {
            addCriterion("updated_name is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameIsNotNull() {
            addCriterion("updated_name is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameEqualTo(String value) {
            addCriterion("updated_name =", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotEqualTo(String value) {
            addCriterion("updated_name <>", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameGreaterThan(String value) {
            addCriterion("updated_name >", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameGreaterThanOrEqualTo(String value) {
            addCriterion("updated_name >=", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameLessThan(String value) {
            addCriterion("updated_name <", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameLessThanOrEqualTo(String value) {
            addCriterion("updated_name <=", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameLike(String value) {
            addCriterion("updated_name like", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotLike(String value) {
            addCriterion("updated_name not like", value, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameIn(List<String> values) {
            addCriterion("updated_name in", values, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotIn(List<String> values) {
            addCriterion("updated_name not in", values, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameBetween(String value1, String value2) {
            addCriterion("updated_name between", value1, value2, "updatedName");
            return (Criteria) this;
        }

        public Criteria andUpdatedNameNotBetween(String value1, String value2) {
            addCriterion("updated_name not between", value1, value2, "updatedName");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}