package com.cowell.iscm.jobHandler;

import com.cowell.iscm.service.RiskConfigService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

/**
 * 预警发送邮件JobHandler(0 0/5 * * * ?)
 *
 * <AUTHOR>
 */
@JobHandler(value="RiskSendMailHandler")
@Component
public class RiskSendMailHandler extends IJobHandler {

    @Autowired
    private RiskConfigService riskConfigService;

    @Override
    @NewSpan("RiskSendMailHandler")
    public ReturnT<String> execute(String s) throws Exception {
        XxlJobLogger.log("RiskSendMailHandler|XXL-JOB, start-----------------");
        try {
            riskConfigService.sendMail();
        } catch (Exception e) {
            return FAIL;
        }
        XxlJobLogger.log("RiskSendMailHandler|XXL-JOB, finish");
        return SUCCESS;
    }
}
