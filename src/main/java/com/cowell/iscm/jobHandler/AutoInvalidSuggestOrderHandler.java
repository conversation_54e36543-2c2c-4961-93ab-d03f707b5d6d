package com.cowell.iscm.jobHandler;

import com.cowell.iscm.service.IscmAllotSuggestApproveService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

@JobHandler(value="AutoInvalidSuggestOrderHandler")
@Component
public class AutoInvalidSuggestOrderHandler extends IJobHandler {

    private static Logger logger = LoggerFactory.getLogger(AutoInvalidSuggestOrderHandler.class);

    @Autowired
    private IscmAllotSuggestApproveService iscmAllotSuggestApproveService;

    @Override
    @NewSpan("AutoInvalidSuggestOrderHandler")
    public ReturnT<String> execute(String s) throws Exception {
        XxlJobLogger.log("AutoInvalidSuggestOrderHandler|XXL-JOB, toc-----------------");
        try {
            iscmAllotSuggestApproveService.autoInvalidSuggest();
        } catch (Exception e) {
            return FAIL;
        }
        logger.info("AutoInvalidSuggestOrderHandler|XXL-JOB, finish");
        return SUCCESS;
    }
}
