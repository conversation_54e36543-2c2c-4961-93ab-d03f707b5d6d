package com.cowell.iscm.jobHandler;

import com.cowell.iscm.service.IscmSaleTargetMonitorService;
import com.cowell.iscm.service.SyncSAPDataService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

import java.util.ArrayList;

@JobHandler(value="AutoSyncSAPDataHandler")
@Component
public class AutoSyncSAPDataHandler extends IJobHandler {

    private static Logger logger = LoggerFactory.getLogger(AutoInvalidSuggestOrderHandler.class);

    @Autowired
    private SyncSAPDataService syncSAPDataService;

    @Override
    @NewSpan("AutoSyncSAPDataHandler")
    public ReturnT<String> execute(String s) throws Exception {
        XxlJobLogger.log("AutoSyncSAPDataHandler|XXL-JOB, toc-----------------");
        try {
            syncSAPDataService.syncSapDataHander(s);
        } catch (Exception e) {
            logger.error("AutoSyncSAPDataHandler 异常",e);
            return FAIL;
        }
        logger.info("AutoSyncSAPDataHandler|XXL-JOB, finish");
        return SUCCESS;
    }
}
