package com.cowell.iscm.jobHandler;

import com.cowell.iscm.service.RecalculationStoreApplyService;
import com.cowell.iscm.service.StoreApplyParamFloatService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

/**
 * 创建复算定时任务(0 0 0 * * ?)
 *
 * <AUTHOR>
 */
@JobHandler(value="AutoInvalidApplyFloatParamHandler")
@Component
public class AutoInvalidApplyFloatParamHandler extends IJobHandler {

    @Autowired
    private StoreApplyParamFloatService storeApplyParamFloatService;

    @Override
    @NewSpan("AutoInvalidApplyFloatParamHandler")
    public ReturnT<String> execute(String s) throws Exception {
        XxlJobLogger.log("AutoInvalidApplyFloatParamHandler|XXL-JOB, start-----------------");
        try {
            Integer type = 1;//正常作废
            if(StringUtils.isNotBlank(s)) {
                type = Integer.valueOf(s);
            }
            storeApplyParamFloatService.autoInvalidFloat(type);
        } catch (Exception e) {
            XxlJobLogger.log("作废失败",e);
            return FAIL;
        }
        XxlJobLogger.log("AutoInvalidApplyFloatParamHandler|XXL-JOB, finish");
        return SUCCESS;
    }
}
