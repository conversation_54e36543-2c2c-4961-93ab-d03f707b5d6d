package com.cowell.iscm.jobHandler;

import com.cowell.iscm.service.ZDTIntegrateService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

@JobHandler(value="ZDTPushWillPosApproveHandler")
@Component
public class ZDTPushWillPosApproveHandler extends IJobHandler {
    private static Logger logger = LoggerFactory.getLogger(ZDTPushWillPosApproveHandler.class);

    @Autowired
    private ZDTIntegrateService zdtIntegrateService;

    @Override
    @NewSpan("ZDTPushWillPosApproveHandler")
    public ReturnT<String> execute(String s) throws Exception {
        XxlJobLogger.log("AutoSyncAutoRegisterOrderHandler|XXL-JOB, toc-----------------");
        try {
            zdtIntegrateService.pushWillPosApproveOrder(null,true);
        } catch (Exception e) {
            return FAIL;
        }
        logger.info("AutoSyncAutoRegisterOrderHandler|XXL-JOB, finish");
        return SUCCESS;
    }
}
