package com.cowell.iscm.jobHandler;

import com.cowell.iscm.rest.errors.BusinessErrorException;
import com.cowell.iscm.service.AvgSalesRecalculationService;
import com.cowell.iscm.service.ControlTowerService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.stream.Collectors;

/**
 * 创建控制塔提醒任务(0 0 0 * * ?)
 *
 * <AUTHOR>
 */
@JobHandler(value="RefreshStoreAttrJobHandler")
@Component
public class RefreshStoreAttrJobHandler extends IJobHandler {

    @Autowired
    private AvgSalesRecalculationService avgSalesRecalculationService;

    @Override
    @NewSpan("RefreshStoreAttrJobHandler")
    public ReturnT<String> execute(String s) throws Exception {
        XxlJobLogger.log("RefreshStoreAttrJobHandler|XXL-JOB, start-----------------");
        try {
            if (StringUtils.isBlank(s)) {
                throw new BusinessErrorException("参数异常-没有门店编码");
            }
            avgSalesRecalculationService.refreshStoreAttr(Arrays.stream(StringUtils.split(s, ",")).map(String::trim).collect(Collectors.toList()));
        } catch (Exception e) {
            return FAIL;
        }
        XxlJobLogger.log("RefreshStoreAttrJobHandler|XXL-JOB, finish");
        return SUCCESS;
    }
}
