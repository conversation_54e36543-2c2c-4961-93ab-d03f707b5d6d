package com.cowell.iscm.jobHandler;

import com.cowell.iscm.service.IscmPurchaseMinPriceService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

/**
 * 最低价查询预警(0 0 0 * * ?)
 *
 * <AUTHOR>
 */
@JobHandler(value="MinPurchaseSelectWarnHandler")
@Component
public class MinPurchaseSelectWarnHandler extends IJobHandler {

    @Autowired
    private IscmPurchaseMinPriceService iscmPurchaseMinPriceService;

    @Override
    @NewSpan("MinPurchaseSelectWarnHandler")
    public ReturnT<String> execute(String s) throws Exception {
        XxlJobLogger.log("MinPurchaseSelectWarnHandler|XXL-JOB, start-----------------");
        try {
            iscmPurchaseMinPriceService.monitorSelect();
        } catch (Exception e) {
            return FAIL;
        }
        XxlJobLogger.log("MinPurchaseSelectWarnHandler|XXL-JOB, finish");
        return SUCCESS;
    }
}
