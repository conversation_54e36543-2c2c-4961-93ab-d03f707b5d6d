package com.cowell.iscm.jobHandler;

import cn.hutool.core.date.DateUtil;
import com.cowell.iscm.service.IscmAllotSuggestAcceptService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

/**
 * 每隔半小时 处理待更新补偿队列(xxx)中待处理登记单-建议状态
 * <AUTHOR>
 * @date  2021-04-13 12:39:44
 */
@JobHandler(value="UpdateRegisterOrderSuggestStatusHandler")
@Component
public class UpdateRegisterOrderSuggestStatusHandler extends IJobHandler {

    private static Logger logger = LoggerFactory.getLogger(UpdateRegisterOrderSuggestStatusHandler.class);

    @Autowired
    private IscmAllotSuggestAcceptService iscmAllotSuggestAcceptService;

    @Override
    @NewSpan("UpdateRegisterOrderSuggestStatusHandler")
    public ReturnT<String> execute(String s) throws Exception {
        XxlJobLogger.log("UpdateRegisterOrderSuggestStatusHandler|XXL-JOB, toc-----------------");
        try {
            iscmAllotSuggestAcceptService.handleUpdateRegisterOrderSuggestStatus(DateUtil.now());
        } catch (Exception e) {
            return FAIL;
        }
        logger.info("UpdateRegisterOrderSuggestStatusHandler|XXL-JOB, finish");
        return SUCCESS;
    }
}
