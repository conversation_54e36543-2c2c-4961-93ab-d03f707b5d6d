package com.cowell.iscm.jobHandler;

import com.cowell.iscm.config.Constants;
import com.cowell.iscm.entity.IscmStoreApplyParamGoodsOrgValidBlacklist;
import com.cowell.iscm.entity.IscmStoreApplyParamGoodsOrgValidBlacklistExample;
import com.cowell.iscm.entity.IscmStoreApplyParamGoodsOrgValidWhitelist;
import com.cowell.iscm.entity.IscmStoreApplyParamGoodsOrgValidWhitelistExample;
import com.cowell.iscm.mapper.IscmStoreApplyParamGoodsOrgValidBlacklistMapper;
import com.cowell.iscm.mapper.IscmStoreApplyParamGoodsOrgValidWhitelistMapper;
import com.cowell.iscm.mapper.extend.IscmStoreApplyParamGoodsOrgValidBlacklistExtendMapper;
import com.cowell.iscm.mapper.extend.IscmStoreApplyParamGoodsOrgValidWhitelistExtendMapper;
import com.cowell.iscm.mapper.extend.IscmStoreApplyParamGoodsOrgVoidBlacklistExtendMapper;
import com.cowell.iscm.mapper.extend.IscmStoreApplyParamGoodsOrgVoidWhitelistExtendMapper;
import com.cowell.iscm.service.ReturnWarehouseService;
import com.cowell.iscm.service.StoreApplyParamGoodsOrgBlacklistService;
import com.cowell.iscm.service.StoreApplyParamGoodsOrgWhitelistService;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * 自动删除 过期的请货参数黑白名单 并记录到作废名单
 *
 * <AUTHOR>
 */
@JobHandler(value="AutoDeleteStoreApplyParamGoodsOrgValidDatatHandler")
@Component
public class AutoDeleteStoreApplyParamGoodsOrgValidDataHandler extends IJobHandler {

    @Autowired
    private IscmStoreApplyParamGoodsOrgValidWhitelistExtendMapper iscmStoreApplyParamGoodsOrgValidWhitelistExtendMapper;
    @Autowired
    private IscmStoreApplyParamGoodsOrgValidWhitelistMapper iscmStoreApplyParamGoodsOrgValidWhitelistMapper;
    @Autowired
    private IscmStoreApplyParamGoodsOrgVoidWhitelistExtendMapper iscmStoreApplyParamGoodsOrgVoidWhitelistExtendMapper;

    @Autowired
    private IscmStoreApplyParamGoodsOrgValidBlacklistExtendMapper iscmStoreApplyParamGoodsOrgValidBlacklistExtendMapper;
    @Autowired
    private IscmStoreApplyParamGoodsOrgValidBlacklistMapper iscmStoreApplyParamGoodsOrgValidBlacklistMapper;
    @Autowired
    private IscmStoreApplyParamGoodsOrgVoidBlacklistExtendMapper iscmStoreApplyParamGoodsOrgVoidBlacklistExtendMapper;

    private static Logger logger = LoggerFactory.getLogger(AutoDeleteStoreApplyParamGoodsOrgValidDataHandler.class);

    @Override
    @NewSpan("AutoDeleteStoreApplyParamGoodsOrgValidDatatHandler")
    public ReturnT<String> execute(String s) throws Exception {
        XxlJobLogger.log("AutoDeleteReturnWarehouseOrderHandler|XXL-JOB, start-----------------");
        logger.info("AutoDeleteReturnWarehouseOrderHandler|XXL-JOB, start-----------------");
//        try {
//            IscmStoreApplyParamGoodsOrgValidWhitelistExample example=new IscmStoreApplyParamGoodsOrgValidWhitelistExample();
//            example.createCriteria().andEndDateLessThanOrEqualTo(new Date());
//            List<Long>   whitelistIds = iscmStoreApplyParamGoodsOrgValidWhitelistExtendMapper.selectByExample(example);
//            if(CollectionUtils.isEmpty(whitelistIds)) {
//                logger.info("待作废的白名单为空");
//                return SUCCESS;
//            }
//            IscmStoreApplyParamGoodsOrgValidWhitelistExample validexample = new IscmStoreApplyParamGoodsOrgValidWhitelistExample();
//            validexample.createCriteria().andIdIn(whitelistIds);
//            List<IscmStoreApplyParamGoodsOrgValidWhitelist> list = iscmStoreApplyParamGoodsOrgValidWhitelistMapper.selectByExample(validexample);
//            if(CollectionUtils.isNotEmpty(list)){
//                XxlJobLogger.log("AutoDeleteReturnWarehouseOrderHandler|XXL-JOB, 作废白名单 size ="+list.size());
//                iscmStoreApplyParamGoodsOrgVoidWhitelistExtendMapper.batchInsert(list);
//                iscmStoreApplyParamGoodsOrgValidWhitelistMapper.deleteByExample(validexample);
//            }else {
//                XxlJobLogger.log("要作废的id 查询数据为空");
//            }
//        } catch (Exception e) {
//            logger.error("AutoDeleteReturnWarehouseOrderHandler异常", e);
//            return FAIL;
//        }
        try {
            IscmStoreApplyParamGoodsOrgValidBlacklistExample example=new IscmStoreApplyParamGoodsOrgValidBlacklistExample();
            example.createCriteria().andEndDateLessThanOrEqualTo(new Date());
            List<Long>   whitelistIds = iscmStoreApplyParamGoodsOrgValidBlacklistExtendMapper.selectByExample(example);
            if (CollectionUtils.isEmpty(whitelistIds)) {
                logger.info("待作废的黑名单为空");
                return SUCCESS;
            }
            Lists.partition(whitelistIds, Constants.BATCH_INSERT_ONCE_MAX_VALUE).forEach(v -> {
                IscmStoreApplyParamGoodsOrgValidBlacklistExample validexample = new IscmStoreApplyParamGoodsOrgValidBlacklistExample();
                validexample.createCriteria().andIdIn(v);
                List<IscmStoreApplyParamGoodsOrgValidBlacklist> list = iscmStoreApplyParamGoodsOrgValidBlacklistMapper.selectByExample(validexample);
                if(CollectionUtils.isNotEmpty(list)){
                    XxlJobLogger.log("AutoDeleteReturnWarehouseOrderHandler|XXL-JOB, 作废黑名单 size ="+list.size());
                    iscmStoreApplyParamGoodsOrgVoidBlacklistExtendMapper.batchInsert(list);
                    iscmStoreApplyParamGoodsOrgValidBlacklistMapper.deleteByExample(validexample);
                }else {
                    XxlJobLogger.log("要作废的id 查询数据为空");
                }
            });
        } catch (Exception e) {
            logger.error("AutoDeleteReturnWarehouseOrderHandler异常", e);
            return FAIL;
        }
        XxlJobLogger.log("AutoDeleteReturnWarehouseOrderHandler|XXL-JOB, finish");
        return SUCCESS;
    }
}
