package com.cowell.iscm.jobHandler;

import com.cowell.iscm.entity.IscmStoreApplyParamGoodsValidWhitelistExample;
import com.cowell.iscm.service.StoreApplyParamPushHDService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * 定时将所有生效白名单推送海典
 *
 * <AUTHOR>
 */
@JobHandler(value="AutoPushWhiteListHandler")
@Component
public class AutoPushWhiteListHandler extends IJobHandler {

    @Autowired
    private StoreApplyParamPushHDService pushHDService;

    @Override
    @NewSpan("AutoPushWhiteListHandler")
    public ReturnT<String> execute(String s) throws Exception {
        XxlJobLogger.log("AutoPushWhiteListHandler|XXL-JOB, start-----------------");
        try {
            pushHDService.sendAllWhitelist();
        } catch (Exception e) {
            return FAIL;
        }
        XxlJobLogger.log("AutoPushWhiteListHandler|XXL-JOB, finish");
        return SUCCESS;
    }
}
