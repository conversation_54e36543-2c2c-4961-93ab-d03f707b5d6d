package com.cowell.iscm.jobHandler;

import com.cowell.iscm.service.ReturnWarehouseService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

/**
 * 自动删除退仓单(0 0 0 * * ?)
 *
 * <AUTHOR>
 */
@JobHandler(value="AutoDeleteReturnWarehouseOrderHandler")
@Component
public class AutoDeleteReturnWarehouseOrderHandler extends IJobHandler {

    @Autowired
    private ReturnWarehouseService returnWarehouseService;

    @Override
    @NewSpan("AutoDeleteReturnWarehouseOrderHandler")
    public ReturnT<String> execute(String s) throws Exception {
        XxlJobLogger.log("AutoDeleteReturnWarehouseOrderHandler|XXL-JOB, start-----------------");
        try {
            returnWarehouseService.autoDeleteReturnOrderLessThanToday();
        } catch (Exception e) {
            return FAIL;
        }
        XxlJobLogger.log("AutoDeleteReturnWarehouseOrderHandler|XXL-JOB, finish");
        return SUCCESS;
    }
}
