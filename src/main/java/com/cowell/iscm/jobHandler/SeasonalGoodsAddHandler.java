package com.cowell.iscm.jobHandler;

import com.cowell.iscm.service.SeasonalGoodsService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Date;

import static com.xxl.job.core.handler.IJobHandler.FAIL;
import static com.xxl.job.core.handler.IJobHandler.SUCCESS;

/**
 * 季节品新增 下发季节品数据
 */
@JobHandler(value = "SeasonalGoodsAddHandler")
@Component
public class SeasonalGoodsAddHandler extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(SeasonalGoodsAddHandler.class);

    @Autowired
    private SeasonalGoodsService seasonalGoodsService;

    @Override
    @NewSpan("SeasonalGoodsAddHandler")
    public ReturnT<String> execute(String param) throws Exception {
        XxlJobLogger.log("SeasonalGoodsAddHandler|XXL-JOB, start-----------------");
        logger.info("季节商品定时新增-定时任务开始执行,入参param:{}", param);
        try {
            Date date;
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            if (StringUtils.isNotEmpty(param)) {
                date = format.parse(param);
            } else {
                String currentDateStr = format.format(new Date());
                date = format.parse(currentDateStr);
            }
            logger.info("季节商品定时新增-定时任务开始执行,入参param:{},实际执行时间date:{}", param, date);
            seasonalGoodsService.pushSeasonalAddGoods(date);
        } catch (Exception e) {
            logger.error("季节商品定时调用MQ推送-发生错误", e);
            return FAIL;
        }
        XxlJobLogger.log("SeasonalGoodsAddHandler|XXL-JOB, finish");
        return SUCCESS;
    }
}
