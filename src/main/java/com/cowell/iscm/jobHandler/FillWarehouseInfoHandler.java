package com.cowell.iscm.jobHandler;

import com.cowell.iscm.service.ReturnWarehouseService;
import com.cowell.iscm.utils.DateUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 每隔半小时 处理待更新补偿队列(xxx)中待处理登记单-建议状态
 * <AUTHOR>
 * @date  2021-04-13 12:39:44
 */
@JobHandler(value="FillWarehouseInfoHandler")
@Component
public class FillWarehouseInfoHandler extends IJobHandler {

    private static Logger logger = LoggerFactory.getLogger(FillWarehouseInfoHandler.class);

    @Autowired
    private ReturnWarehouseService returnWarehouseService;

    @Override
    @NewSpan("FillWarehouseInfoHandler")
    public ReturnT<String> execute(String s) throws Exception {
        XxlJobLogger.log("FillWarehouseInfoHandler|XXL-JOB, toc-----------------");
        try {
            returnWarehouseService.fillWarehouseInfoByDate(DateUtils.conventDateStrByDate(new Date(), DateUtils.DATE_PATTERN));
        } catch (Exception e) {
            return FAIL;
        }
        logger.info("UpdateRegisterOrderSuggestStatusHandler|XXL-JOB, finish");
        return SUCCESS;
    }

}
