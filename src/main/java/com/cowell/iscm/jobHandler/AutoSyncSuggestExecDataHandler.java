package com.cowell.iscm.jobHandler;

import com.cowell.iscm.service.AllotTrackService;
import com.cowell.iscm.service.AllotTrackSyncDataService;
import com.cowell.iscm.service.IscmAllotSuggestApproveService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 每隔两小时 处理大数据已同步到iscm_suggest_distexec表的内容 到  iscm_suggest_distexec_detail 表
 * <AUTHOR>
 * @date  2021-03-24 16:39:44
 */
@JobHandler(value="AutoSyncSuggestExecDataHandler")
@Component
public class AutoSyncSuggestExecDataHandler extends IJobHandler {

    private static Logger logger = LoggerFactory.getLogger(AutoSyncSuggestExecDataHandler.class);

    @Autowired
    private AllotTrackSyncDataService allotTrackSyncDataService;

    @Override
    @NewSpan("AutoSyncSuggestExecDataHandler")
    public ReturnT<String> execute(String s) throws Exception {
        XxlJobLogger.log("AutoSyncSuggestExecDataHandler|XXL-JOB, toc-----------------");
        try {
            allotTrackSyncDataService.syncSuggestExecDataHander(new Date());
        } catch (Exception e) {
            return FAIL;
        }
        logger.info("AutoSyncSuggestExecDataHandler|XXL-JOB, finish");
        return SUCCESS;
    }
}
