package com.cowell.iscm.jobHandler;

import com.cowell.iscm.service.ReturnWarehouseExecuteGenService;
import com.cowell.iscm.service.ZDTIntegrateService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

@JobHandler(value="DefectiveReturnNoticeHandler")
@Component
public class DefectiveReturnNoticeHandler extends IJobHandler {
    private static Logger logger = LoggerFactory.getLogger(DefectiveReturnNoticeHandler.class);

    @Autowired
    private ReturnWarehouseExecuteGenService returnWarehouseExecuteGenService;

    @Override
    @NewSpan("DefectiveReturnNoticeHandler")
    public ReturnT<String> execute(String s) throws Exception {
        XxlJobLogger.log("DefectiveReturnNoticeHandler|XXL-JOB, toc-----------------");
        try {
            returnWarehouseExecuteGenService.genNoticeDzMsg();
        } catch (Exception e) {
            return FAIL;
        }
        logger.info("DefectiveReturnNoticeHandler|XXL-JOB, finish");
        return SUCCESS;
    }
}
