package com.cowell.iscm.jobHandler;

import com.cowell.iscm.rest.errors.BusinessErrorException;
import com.cowell.iscm.service.ZDTPushReplenishmentPurchaseService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 推式补货集成智店通 JobHandler
 *
 * <AUTHOR>
 */
@Component
public class ZDTPushReplenishmentJobHandler {
    private static final Logger logger = LoggerFactory.getLogger(ZDTPushReplenishmentJobHandler.class);

    @Autowired
    private ZDTPushReplenishmentPurchaseService zdtPushReplenishmentPurchaseService;

    /**
     * 推送分货待办任务 JobHandler
     *
     * @param param
     * @return
     * @throws Exception
     */
    @XxlJob("pushDistributionTaskJobHandler")
    @NewSpan("pushDistributionTaskJobHandler")
    public ReturnT<String> pushDistributionTaskJobHandler(String param) throws Exception {
        XxlJobLogger.log("pushDistributionTaskJobHandler|XXL-JOB, start-----------------");
        XxlJobLogger.log("pushDistributionTaskJobHandler|XXL-JOB, finish");
        return ReturnT.SUCCESS;
    }

    /**
     * 推送请货待办任务 JobHandler
     *
     * @param param
     * @return
     * @throws Exception
     */
    @XxlJob("pushPurchaseTaskJobHandler")
    @NewSpan("pushPurchaseTaskJobHandler")
    public ReturnT<String> pushPurchaseTaskJobHandler(String param) throws Exception {
        XxlJobLogger.log("pushPurchaseTaskJobHandler|XXL-JOB, toc-----------------");
        try {
            logger.info("pushPurchaseTaskJobHandler param -> {}", param);
            zdtPushReplenishmentPurchaseService.pushPurchaseTask();
        } catch (Exception e) {
            logger.error("pushPurchaseTaskJobHandler error!", e);
            return ReturnT.FAIL;
        }
        logger.info("pushPurchaseTaskJobHandler|XXL-JOB, finish");
        return ReturnT.SUCCESS;
    }

    /**
     * 同步请货数据任务 JobHandler
     *
     * @param param
     * @return
     * @throws Exception
     */
    @XxlJob("syncPurchaseDataJobHandler")
    @NewSpan("syncPurchaseDataJobHandler")
    public ReturnT<String> syncPurchaseDataJobHandler(String param) throws Exception {
        XxlJobLogger.log("syncPurchaseDataJobHandler|XXL-JOB, start-----------------");
        try {
            logger.info("syncPurchaseDataJobHandler param -> {}", param);
            int result = zdtPushReplenishmentPurchaseService.syncPurchaseDataHander(new Date());
            if(result<0){
                logger.error("syncPurchaseDataJobHandler 同步请货数据异常");
                throw new BusinessErrorException("同步请货数据异常");
            }
        } catch (Exception e) {
            logger.error("syncPurchaseDataJobHandler error!", e);
            XxlJobLogger.log("syncPurchaseDataJobHandler|XXL-JOB, error");
            return ReturnT.FAIL;
        }
        XxlJobLogger.log("syncPurchaseDataJobHandler|XXL-JOB, finish");
        return ReturnT.SUCCESS;
    }

}
