package com.cowell.iscm.jobHandler;

import com.cowell.iscm.service.DealClearExpiredDataService;
import com.cowell.iscm.service.ZDTPushReplenishmentPurchaseService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

@JobHandler(value="DealClearExpiredDataHandler")
@Component
public class DealClearExpiredDataHandler extends IJobHandler {

    @Autowired
    private DealClearExpiredDataService dealClearExpiredDataService;


    @Autowired
    private ZDTPushReplenishmentPurchaseService zdtPushReplenishmentPurchaseService;

    @Override
    @NewSpan("DealClearExpiredDataHandler")
    public ReturnT<String> execute(String s) throws Exception {
        XxlJobLogger.log("DealClearExpiredDataHandler|XXL-JOB, start-----------------");
        try {
            dealClearExpiredDataService.clearExpiredData();

            zdtPushReplenishmentPurchaseService.deletePurchaseDataT31();
        } catch (Exception e) {
            return FAIL;
        }
        XxlJobLogger.log("DealClearExpiredDataHandler|XXL-JOB, finish");
        return SUCCESS;
    }
}
