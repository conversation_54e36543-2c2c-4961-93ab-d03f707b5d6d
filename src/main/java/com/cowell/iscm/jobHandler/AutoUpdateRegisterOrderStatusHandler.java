package com.cowell.iscm.jobHandler;

import com.cowell.iscm.service.IscmRegisterOrderService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

/**
 * 自动更新登记单状态
 *
 * <AUTHOR>
 */
@JobHandler(value="AutoUpdateRegisterOrderStatusHandler")
@Component
public class AutoUpdateRegisterOrderStatusHandler extends IJobHandler {

    @Autowired
    private IscmRegisterOrderService iscmRegisterOrderService;

    @Override
    @NewSpan("AutoUpdateRegisterOrderStatusHandler")
    public ReturnT<String> execute(String s) throws Exception {
        XxlJobLogger.log("AutoUpdateRegisterOrderStatusHandler|XXL-JOB, start-----------------");
        try {
            iscmRegisterOrderService.autoUpdateRegisterOrderStatus(s);
        } catch (Exception e) {
            XxlJobLogger.log(e);
            return FAIL;
        }
        XxlJobLogger.log("AutoUpdateRegisterOrderStatusHandler|XXL-JOB, finish");
        return SUCCESS;
    }
}
