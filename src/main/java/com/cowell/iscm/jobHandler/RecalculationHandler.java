package com.cowell.iscm.jobHandler;

import com.cowell.iscm.service.RecalculationStoreApplyService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

/**
 * 创建复算定时任务(0 0 0 * * ?)
 *
 * <AUTHOR>
 */
@JobHandler(value="RecalculationHandler")
@Component
public class RecalculationHandler extends IJobHandler {

    @Autowired
    private RecalculationStoreApplyService recalculationStoreApplyService;

    @Override
    @NewSpan("RecalculationHandler")
    public ReturnT<String> execute(String s) throws Exception {
        XxlJobLogger.log("RecalculationHandler|XXL-JOB, start-----------------");
        try {
            recalculationStoreApplyService.createRecalculationDelayJob();
        } catch (Exception e) {
            return FAIL;
        }
        XxlJobLogger.log("RecalculationHandler|XXL-JOB, finish");
        return SUCCESS;
    }
}
