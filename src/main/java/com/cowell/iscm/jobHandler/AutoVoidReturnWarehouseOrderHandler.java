package com.cowell.iscm.jobHandler;

import com.cowell.iscm.service.ReturnWarehouseExecuteService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

/**
 * 自动作废退仓单(0 0 0 * * ?)
 *
 * <AUTHOR>
 */
@JobHandler(value="AutoVoidReturnWarehouseOrderHandler")
@Component
public class AutoVoidReturnWarehouseOrderHandler extends IJobHandler {

    @Autowired
    private ReturnWarehouseExecuteService returnWarehouseExecuteService;

    @Override
    @NewSpan("AutoVoidReturnWarehouseOrderHandler")
    public ReturnT<String> execute(String s) throws Exception {
        XxlJobLogger.log("AutoVoidReturnWarehouseOrderHandler|XXL-JOB, start-----------------");
        try {
            returnWarehouseExecuteService.autoVoid();
        } catch (Exception e) {
            return FAIL;
        }
        XxlJobLogger.log("AutoVoidReturnWarehouseOrderHandler|XXL-JOB, finish");
        return SUCCESS;
    }
}
