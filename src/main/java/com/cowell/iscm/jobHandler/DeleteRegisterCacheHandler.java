package com.cowell.iscm.jobHandler;

import com.cowell.iscm.service.IscmRegisterOrderService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

/**
 * 删除登记单缓存
 *
 * <AUTHOR>
 */
@JobHandler(value="DeleteRegisterCacheHandler")
@Component
public class DeleteRegisterCacheHandler extends IJobHandler {

    @Autowired
    private IscmRegisterOrderService iscmRegisterOrderService;

    @Override
    @NewSpan("DeleteRegisterCacheHandler")
    public ReturnT<String> execute(String s) throws Exception {
        XxlJobLogger.log("AutoUpdateRegisterOrderStatusHandler|XXL-JOB, start-----------------");
        try {
            String[] split = s.split("-");
            iscmRegisterOrderService.deleteRegisterRedisCache(Long.valueOf(split[0]), Byte.valueOf(split[1]));
        } catch (Exception e) {
            XxlJobLogger.log(e);
            return FAIL;
        }
        XxlJobLogger.log("AutoUpdateRegisterOrderStatusHandler|XXL-JOB, finish");
        return SUCCESS;
    }
}
