package com.cowell.iscm.jobHandler;

import com.cowell.iscm.service.IscmSaleTargetMonitorService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

import java.util.ArrayList;

@JobHandler(value="AutoSyncKpiDataHandler")
@Component
public class AutoSyncKpiDataHandler extends IJobHandler {

    private static Logger logger = LoggerFactory.getLogger(AutoInvalidSuggestOrderHandler.class);

    @Autowired
    private IscmSaleTargetMonitorService iscmSaleTargetMonitorService;

    @Override
    @NewSpan("AutoSyncKpiDataHandler")
    public ReturnT<String> execute(String s) throws Exception {
        XxlJobLogger.log("AutoSyncKpiDataHandler|XXL-JOB, toc-----------------");
        try {
            iscmSaleTargetMonitorService.pushSyncDataTask(new ArrayList<>());
        } catch (Exception e) {
            return FAIL;
        }
        logger.info("AutoSyncKpiDataHandler|XXL-JOB, finish");
        return SUCCESS;
    }
}
