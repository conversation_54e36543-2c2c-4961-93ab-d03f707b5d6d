package com.cowell.iscm.jobHandler;

import com.cowell.iscm.service.ControlTowerService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

/**
 * 创建控制塔提醒任务(0 0 0 * * ?)
 *
 * <AUTHOR>
 */
@JobHandler(value="AutoCreateControlTowerWarnJobHandler")
@Component
public class AutoCreateControlTowerWarnJobHandler extends IJobHandler {

    @Autowired
    private ControlTowerService controlTowerService;

    @Override
    @NewSpan("AutoCreateControlTowerWarnJobHandler")
    public ReturnT<String> execute(String s) throws Exception {
        XxlJobLogger.log("AutoCreateControlTowerWarnJobHandler|XXL-JOB, start-----------------");
        try {
            controlTowerService.createTowerWarnJob();
        } catch (Exception e) {
            return FAIL;
        }
        XxlJobLogger.log("AutoCreateControlTowerWarnJobHandler|XXL-JOB, finish");
        return SUCCESS;
    }
}
