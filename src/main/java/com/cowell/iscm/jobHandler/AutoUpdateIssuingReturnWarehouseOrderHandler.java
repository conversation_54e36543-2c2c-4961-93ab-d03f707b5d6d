package com.cowell.iscm.jobHandler;

import com.cowell.iscm.enums.StoreReturnExecuteProcessStatusEnum;
import com.cowell.iscm.service.ReturnWarehouseExecuteService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

/**
 * 自动更新下发中的退仓单(0 0 06 * * ?)
 *
 * <AUTHOR>
 */
@JobHandler(value="AutoUpdateIssuingReturnWarehouseOrderHandler")
@Component
public class AutoUpdateIssuingReturnWarehouseOrderHandler extends IJobHandler {

    @Autowired
    private ReturnWarehouseExecuteService returnWarehouseExecuteService;

    @Override
    @NewSpan("AutoUpdateIssuingReturnWarehouseOrderHandler")
    public ReturnT<String> execute(String s) throws Exception {
        XxlJobLogger.log("AutoUpdateIssuingReturnWarehouseOrderHandler|XXL-JOB, start-----------------");
        try {
            returnWarehouseExecuteService.autoUpdateIssuingOrder(StoreReturnExecuteProcessStatusEnum.ISSUE_FAILED.getCode());
        } catch (Exception e) {
            XxlJobLogger.log(e);
            return FAIL;
        }
        XxlJobLogger.log("AutoUpdateIssuingReturnWarehouseOrderHandler|XXL-JOB, finish");
        return SUCCESS;
    }
}
