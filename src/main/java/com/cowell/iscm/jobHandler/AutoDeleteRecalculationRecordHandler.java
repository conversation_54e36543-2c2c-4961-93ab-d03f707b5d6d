package com.cowell.iscm.jobHandler;

import com.cowell.iscm.service.RecalculationStoreApplyService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

/**
 * 自动删除复算记录(0 0 0 * * ?)
 *
 * <AUTHOR>
 */
@JobHandler(value="AutoDeleteRecalculationRecordHandler")
@Component
public class AutoDeleteRecalculationRecordHandler extends IJobHandler {

    @Autowired
    private RecalculationStoreApplyService recalculationStoreApplyService;

    @Override
    @NewSpan("AutoDeleteRecalculationRecordHandler")
    public ReturnT<String> execute(String s) throws Exception {
        XxlJobLogger.log("AutoDeleteRecalculationRecordHandler|XXL-JOB, start-----------------");
        try {
            // 默认删除3天记录
            recalculationStoreApplyService.deleteRecalculationRecord(StringUtils.isBlank(s) ? 3 : Integer.valueOf(s));
        } catch (Exception e) {
            return FAIL;
        }
        XxlJobLogger.log("AutoDeleteRecalculationRecordHandler|XXL-JOB, finish");
        return SUCCESS;
    }
}
