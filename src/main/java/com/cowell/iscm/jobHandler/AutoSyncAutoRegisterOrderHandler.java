package com.cowell.iscm.jobHandler;

import com.cowell.iscm.service.IscmRegisterOrderService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

@JobHandler(value="AutoSyncAutoRegisterOrderHandler")
@Component
public class AutoSyncAutoRegisterOrderHandler extends IJobHandler {

    private static Logger logger = LoggerFactory.getLogger(AutoSyncAutoRegisterOrderHandler.class);

    @Autowired
    private IscmRegisterOrderService iscmRegisterOrderService;

    @Override
    @NewSpan("AutoSyncAutoRegisterOrderHandler")
    public ReturnT<String> execute(String s) throws Exception {
        XxlJobLogger.log("AutoSyncAutoRegisterOrderHandler|XXL-JOB, toc-----------------");
        try {
            iscmRegisterOrderService.asynAutoRegister();
        } catch (Exception e) {
            return FAIL;
        }
        logger.info("AutoSyncAutoRegisterOrderHandler|XXL-JOB, finish");
        return SUCCESS;
    }
}
