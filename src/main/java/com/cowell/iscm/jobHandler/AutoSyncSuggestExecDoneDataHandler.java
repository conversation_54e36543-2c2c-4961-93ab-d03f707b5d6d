package com.cowell.iscm.jobHandler;

import com.cowell.iscm.service.AllotTrackSyncDataService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 每天1点 处理大数据已同步到iscm_suggest_distexec_done表的内容 到  iscm_suggest_distexec_done_detail 表
 * <AUTHOR>
 * @date  2021-03-24 16:39:44
 */
@JobHandler(value="AutoSyncSuggestExecDoneDataHandler")
@Component
public class AutoSyncSuggestExecDoneDataHandler extends IJobHandler {

    private static Logger logger = LoggerFactory.getLogger(AutoSyncSuggestExecDoneDataHandler.class);

    @Autowired
    private AllotTrackSyncDataService allotTrackSyncDataService;

    @Override
    @NewSpan("AutoSyncSuggestExecDoneDataHandler")
    public ReturnT<String> execute(String s) throws Exception {
        XxlJobLogger.log("AutoSyncSuggestExecDoneDataHandler|XXL-JOB, toc-----------------");
        try {
            allotTrackSyncDataService.syncSuggestExecDoneDataHander(new Date());
        } catch (Exception e) {
            return FAIL;
        }
        logger.info("AutoSyncSuggestExecDoneDataHandler|XXL-JOB, finish");
        return SUCCESS;
    }
}
