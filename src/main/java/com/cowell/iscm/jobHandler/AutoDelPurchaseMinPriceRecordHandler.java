package com.cowell.iscm.jobHandler;

import com.cowell.iscm.service.IscmPurchaseMinPriceService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

/**
 * 自动最低采购价查询记录(0 0 0 * * ?)
 *
 * <AUTHOR>
 */
@JobHandler(value="AutoDelPurchaseMinPriceRecordHandler")
@Component
public class AutoDelPurchaseMinPriceRecordHandler extends IJobHandler {

    @Autowired
    private IscmPurchaseMinPriceService iscmPurchaseMinPriceService;

    @Override
    @NewSpan("AutoDelPurchaseMinPriceRecordHandler")
    public ReturnT<String> execute(String days) throws Exception {
        XxlJobLogger.log("AutoDelPurchaseMinPriceRecordHandler|XXL-JOB, start-----------------");
        try {
            iscmPurchaseMinPriceService.deleteRecordByDays(StringUtils.isBlank(days) ? 7 : Integer.valueOf(days));
        } catch (Exception e) {
            XxlJobLogger.log(e);
            return FAIL;
        }
        XxlJobLogger.log("AutoDelPurchaseMinPriceRecordHandler|XXL-JOB, finish");
        return SUCCESS;
    }
}
