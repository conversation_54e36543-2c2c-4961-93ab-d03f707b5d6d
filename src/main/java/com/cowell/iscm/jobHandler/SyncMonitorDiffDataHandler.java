package com.cowell.iscm.jobHandler;

import com.cowell.iscm.service.PushReplenishmentMonitorService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

/**
 * 定时生成推式补货全链路监控差异数量
 */
@JobHandler(value="SyncMonitorDiffDataHandler")
@Component
public class SyncMonitorDiffDataHandler extends IJobHandler {

    private static Logger logger = LoggerFactory.getLogger(SyncMonitorDiffDataHandler.class);

    @Autowired
    private PushReplenishmentMonitorService pushReplenishmentMonitorService;

    @Override
    @NewSpan("SyncMonitorDiffDataHandler")
    public ReturnT<String> execute(String s) throws Exception {
        XxlJobLogger.log("SyncMonitorDiffDataHandler|XXL-JOB, toc-----------------");
        try {
            pushReplenishmentMonitorService.genCompanyDiff();
        } catch (Exception e) {
            return FAIL;
        }
        logger.info("SyncMonitorDiffDataHandler|XXL-JOB, finish");
        return SUCCESS;
    }
}
