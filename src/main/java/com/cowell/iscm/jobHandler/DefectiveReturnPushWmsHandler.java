package com.cowell.iscm.jobHandler;

import com.cowell.iscm.service.ReturnWarehouseExecuteUpdateService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

@JobHandler(value="DefectiveReturnPushWmsHandler")
@Component
public class DefectiveReturnPushWmsHandler extends IJobHandler {
    private static Logger logger = LoggerFactory.getLogger(DefectiveReturnPushWmsHandler.class);

    @Autowired
    private ReturnWarehouseExecuteUpdateService returnWarehouseExecuteUpdateService;

    @Override
    @NewSpan("DefectiveReturnPushWmsHandler")
    public ReturnT<String> execute(String s) throws Exception {
        XxlJobLogger.log("DefectiveReturnPushWmsHandler|XXL-JOB, toc-----------------");
        try {
            String[] split = StringUtils.split(s, "|");
            returnWarehouseExecuteUpdateService.testPushWms(split[0], Integer.valueOf(split[1]));
        } catch (Exception e) {
            XxlJobLogger.log("DefectiveReturnPushWmsHandler|XXL-JOB, toc error-----------------", e);
            return FAIL;
        }
        logger.info("DefectiveReturnPushWmsHandler|XXL-JOB, finish");
        return SUCCESS;
    }
}
