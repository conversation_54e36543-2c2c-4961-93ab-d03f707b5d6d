package com.cowell.iscm.jobHandler;

import com.cowell.iscm.service.StoreApplyParamGoodsWhitelistService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

/**
 * 定时器作废过期白名单列表
 *
 * <AUTHOR>
 */
@JobHandler(value="AutoVoidWhiteListHandler")
@Component
public class AutoVoidWhiteListHandler extends IJobHandler {

    @Autowired
    private StoreApplyParamGoodsWhitelistService whitelistService;

    @Override
    @NewSpan("AutoVoidWhiteListHandler")
    public ReturnT<String> execute(String s) throws Exception {
        XxlJobLogger.log("AutoVoidWhiteListHandler|XXL-JOB, start-----------------");
        try {
            whitelistService.autoVoidByVoidDate();

        } catch (Exception e) {
            return FAIL;
        }
        XxlJobLogger.log("AutoVoidWhiteListHandler|XXL-JOB, finish");
        return SUCCESS;
    }
}
