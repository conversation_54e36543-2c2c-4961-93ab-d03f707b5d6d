package com.cowell.iscm.jobHandler;

import cn.hutool.core.date.DateUtil;
import com.cowell.iscm.service.IscmRegisterOrderService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

/**
 * iscm_register_result表数据处理，同步单据状态及建议未产生原因到商品登记单及商品登记单明细表
 */
@JobHandler(value="SyncRegisterResultToRegisterOrderHandler")
@Component
public class SyncRegisterResultToRegisterOrderHandler  extends IJobHandler {

    private static Logger logger = LoggerFactory.getLogger(SyncRegisterResultToRegisterOrderHandler.class);

    @Autowired
    private IscmRegisterOrderService iscmRegisterOrderService;

    @Override
    @NewSpan("SyncRegisterResultToRegisterOrderHandler")
    public ReturnT<String> execute(String s) throws Exception {
        XxlJobLogger.log("SyncRegisterResultToRegisterOrderHandler|XXL-JOB, toc-----------------");
        try {
            iscmRegisterOrderService.syncRegisterResult(DateUtil.today());
        } catch (Exception e) {
            return FAIL;
        }
        logger.info("SyncRegisterResultToRegisterOrderHandler|XXL-JOB, finish");
        return SUCCESS;
    }
}
