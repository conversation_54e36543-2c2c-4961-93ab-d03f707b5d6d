package com.cowell.iscm.mapperTidb;

import com.cowell.iscm.entityTidb.IscmApplyGoodsReplaceCode;
import com.cowell.iscm.entityTidb.IscmApplyGoodsReplaceCodeExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface IscmApplyGoodsReplaceCodeMapper {
    long countByExample(IscmApplyGoodsReplaceCodeExample example);

    int deleteByExample(IscmApplyGoodsReplaceCodeExample example);

    int deleteByPrimaryKey(Long id);

    int insert(IscmApplyGoodsReplaceCode record);

    int insertSelective(IscmApplyGoodsReplaceCode record);

    List<IscmApplyGoodsReplaceCode> selectByExample(IscmApplyGoodsReplaceCodeExample example);

    IscmApplyGoodsReplaceCode selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") IscmApplyGoodsReplaceCode record, @Param("example") IscmApplyGoodsReplaceCodeExample example);

    int updateByExample(@Param("record") IscmApplyGoodsReplaceCode record, @Param("example") IscmApplyGoodsReplaceCodeExample example);

    int updateByPrimaryKeySelective(IscmApplyGoodsReplaceCode record);

    int updateByPrimaryKey(IscmApplyGoodsReplaceCode record);
}