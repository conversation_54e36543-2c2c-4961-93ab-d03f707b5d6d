package com.cowell.iscm.mapperTidb;

import com.cowell.iscm.entityTidb.IscmAutoRegister;
import com.cowell.iscm.entityTidb.IscmAutoRegisterExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface IscmAutoRegisterMapper {
    long countByExample(IscmAutoRegisterExample example);

    int deleteByExample(IscmAutoRegisterExample example);

    int deleteByPrimaryKey(Long id);

    int insert(IscmAutoRegister record);

    int insertSelective(IscmAutoRegister record);

    List<IscmAutoRegister> selectByExample(IscmAutoRegisterExample example);

    IscmAutoRegister selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") IscmAutoRegister record, @Param("example") IscmAutoRegisterExample example);

    int updateByExample(@Param("record") IscmAutoRegister record, @Param("example") IscmAutoRegisterExample example);

    int updateByPrimaryKeySelective(IscmAutoRegister record);

    int updateByPrimaryKey(IscmAutoRegister record);
}