package com.cowell.iscm.mapperTidb.extend;

import com.cowell.iscm.service.dto.pushReplenishmentMonitor.BdpIntelligentDistributeDTO;
import com.cowell.iscm.service.dto.pushReplenishmentMonitor.RateCountDTO;
import com.cowell.iscm.service.dto.pushReplenishmentMonitor.StoreTimeDTO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface IscmBdpIntelligentDistributeExtendMapper {

    BdpIntelligentDistributeDTO selectStartAndFinishTime(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate);

    Integer countDistributeStoreQuantity(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate);

    Integer countDistributeGoodsQuantity(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate);

    @Deprecated
    Integer countAllSatisfiedGoodsQuantity(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate);

    @Deprecated
    Integer countPartiallySatisfiedGoodsQuantity1(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate);

    @Deprecated
    Integer countPartiallySatisfiedGoodsQuantity2(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate);

    @Deprecated
    Integer countNoSatisfiedGoodsQuantity(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate);

    List<RateCountDTO> countGroupByRateType(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate);

    List<StoreTimeDTO> selectStoreMinTime(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate, @Param("offset") Integer offset, @Param("limit") Integer limit);

    Long selectStoreMinTimeCount(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate);

    Integer countNoSatisfiedStoreQuantity(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate);
}
