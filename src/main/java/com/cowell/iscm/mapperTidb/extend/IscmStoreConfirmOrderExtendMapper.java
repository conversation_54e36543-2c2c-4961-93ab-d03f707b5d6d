package com.cowell.iscm.mapperTidb.extend;

import com.cowell.iscm.service.dto.pushReplenishmentMonitor.IscmStoreConfirmOrderDTO;
import com.cowell.iscm.service.dto.pushReplenishmentMonitor.StoreConfirmOrderDataDTO;
import com.cowell.iscm.service.dto.pushReplenishmentMonitor.StoreTimeDTO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public interface IscmStoreConfirmOrderExtendMapper {

    Integer countReceivedStoreQuantity(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate);

    Integer countReceivedGoodsQuantity(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate);

    @Deprecated
    Integer countShouldReceiveStoreQuantity(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate);

    @Deprecated
    Integer countShouldReceiveGoodsQuantity(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate);

    @Deprecated
    Integer countWillReceiveGoodsQuantity(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate);

    BigDecimal sumArriveRate(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate);

    List<StoreTimeDTO> selectStoreMinTime(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate, @Param("offset") Integer offset, @Param("limit") Integer limit);

    Long selectStoreMinTimeCount(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate);

    StoreConfirmOrderDataDTO selectStartAndFinishTime(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate);

    List<IscmStoreConfirmOrderDTO> selectShouldReceiveGoods(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate, @Param("offset") Integer offset, @Param("limit") Integer limit,@Param("isWillReceive") Boolean isWillReceive);

    Long selectShouldReceiveGoodsCount(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate,@Param("isWillReceive") Boolean isWillReceive);
}
