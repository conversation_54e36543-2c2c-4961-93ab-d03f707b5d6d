package com.cowell.iscm.mapperTidb.extend;

import com.cowell.iscm.service.dto.visualcenter.suggestMonitor.MonitorDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by schuangxigang on 2022/6/13 17:17.
 */
public interface IscmBdpDailySuggestExtendMapper {
    int selectMonitorDtCount(@Param("companyCodes") List<String> companyCodes, @Param("dtStart") String dtStart, @Param("dtEnd") String dtEnd);
    List<MonitorDTO> selectMonitorList(@Param("companyCodes") List<String> companyCodes, @Param("dtStart") String dtStart, @Param("dtEnd") String dtEnd);
}
