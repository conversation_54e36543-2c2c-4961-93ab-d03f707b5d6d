package com.cowell.iscm.mapperTidb.extend;

import com.cowell.iscm.entityTidb.IscmSuggestDistexecDoneDetail;
import com.cowell.iscm.entityTidb.IscmSuggestDistexecDoneDetailExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface IscmSuggestDistexecDoneDetailExtendMapper {

    List<IscmSuggestDistexecDoneDetail> selectAllotExecutedByOutStoreOrgIdsAndAllotTypes(@Param("registerMonth") Integer registerMonth, @Param("outStoreOrgIds") List<Long> outStoreOrgIds, @Param("inStoreOrgIds") List<Long> inStoreOrgIds, @Param("allotTypes") List<Byte> allotTypes, @Param("start") int start, @Param("pageSize") Integer pageSize);

}
