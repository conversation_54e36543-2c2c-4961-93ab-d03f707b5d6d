package com.cowell.iscm.mapperTidb.extend;

import com.cowell.iscm.entityTidb.IscmManualRegister;
import com.cowell.iscm.entityTidb.IscmManualRegisterExample;
import com.cowell.iscm.service.dto.returnWarehouse.StoreReturnWarehouseConfirmDTO;
import org.apache.commons.lang.StringUtils;
import org.apache.ibatis.annotations.Param;
import scala.annotation.meta.param;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

public interface IscmManualRegisterExtendMapper {

    int updateDealStatusByIds(@Param("ids") List<Long> ids, @Param("dealStatus") Byte dealStatus);

    List<IscmManualRegister> selectWillRegister(@Param("storeCodes")List<String> storeCodes,
                                                @Param("dataTypes") List<Byte> dataTypes,
                                                @Param("sortFieldName") String sortFieldName,
                                                @Param("sortType") Byte sortType,
                                                @Param("registerQuantityStart")BigDecimal registerQuantityStart,
                                                @Param("registerQuantityEnd")BigDecimal registerQuantityEnd,
                                                @Param("costAmountStart")BigDecimal costAmountStart,
                                                @Param("costAmountEnd")BigDecimal costAmountEnd,
                                                @Param("goodsCommonName") String goodsCommonName,
                                                @Param("expectSaleDaysStart") BigDecimal expectSaleDaysStart,
                                                @Param("expectSaleDaysEnd") BigDecimal expectSaleDaysEnd,
                                                @Param("nonSaleDaysStart") Integer nonSaleDaysStart,
                                                @Param("nonSaleDaysEnd") Integer nonSaleDaysEnd,
                                                @Param("goodsNos") List<String> goodsNos,
                                                @Param("manufacturer") String manufacturer,
                                                @Param("ids") List<Long> ids,
                                                @Param("offset") Integer offset,
                                                @Param("limit") Integer limit,
                                                @Param("stockLowerLimitEnd")BigDecimal stockLowerLimitEnd,
                                                @Param("stockLowerLimitStart")BigDecimal stockLowerLimitStart,
                                                @Param("stockQuantityEnd")BigDecimal stockQuantityEnd,
                                                @Param("stockQuantityStart")BigDecimal stockQuantityStart,
                                                @Param("stockUpperLimitEnd")BigDecimal stockUpperLimitEnd,
                                                @Param("stockUpperLimitStart")BigDecimal stockUpperLimitStart,
                                                @Param("thirtySalesCountEnd")BigDecimal thirtySalesCountEnd,
                                                @Param("thirtySalesCountStart")BigDecimal thirtySalesCountStart,
                                                @Param("thirtySalesQuantityEnd")BigDecimal thirtySalesQuantityEnd,
                                                @Param("thirtySalesQuantityStart")BigDecimal thirtySalesQuantityStart
                                                );




    long counttWillRegister(@Param("storeCodes")List<String> storeCodes,
                                                  @Param("dataTypes") List<Byte> dataTypes,
                                                  @Param("registerQuantityStart")BigDecimal registerQuantityStart,
                                                  @Param("registerQuantityEnd")BigDecimal registerQuantityEnd,
                                                  @Param("costAmountStart")BigDecimal costAmountStart,
                                                  @Param("costAmountEnd")BigDecimal costAmountEnd,
                                                  @Param("goodsCommonName") String goodsCommonName,
                                                  @Param("expectSaleDaysStart") BigDecimal expectSaleDaysStart,
                                                  @Param("expectSaleDaysEnd") BigDecimal expectSaleDaysEnd,
                                                  @Param("nonSaleDaysStart") Integer nonSaleDaysStart,
                                                  @Param("nonSaleDaysEnd") Integer nonSaleDaysEnd,
                                                  @Param("goodsNos") List<String> goodsNos,
                                                  @Param("manufacturer") String manufacturer,
                                                  @Param("ids") List<Long> ids,
                                                @Param("stockLowerLimitEnd")BigDecimal stockLowerLimitEnd,
                                                @Param("stockLowerLimitStart")BigDecimal stockLowerLimitStart,
                                                @Param("stockQuantityEnd")BigDecimal stockQuantityEnd,
                                                @Param("stockQuantityStart")BigDecimal stockQuantityStart,
                                                @Param("stockUpperLimitEnd")BigDecimal stockUpperLimitEnd,
                                                @Param("stockUpperLimitStart")BigDecimal stockUpperLimitStart,
                                                @Param("thirtySalesCountEnd")BigDecimal thirtySalesCountEnd,
                                                @Param("thirtySalesCountStart")BigDecimal thirtySalesCountStart,
                                                @Param("thirtySalesQuantityEnd")BigDecimal thirtySalesQuantityEnd,
                                                @Param("thirtySalesQuantityStart")BigDecimal thirtySalesQuantityStart);

    List<IscmManualRegister> selectWillRegisterByStoreCode(@Param("storeCode")String storeCode,
                                                              @Param("dataTypes") List<Byte> dataTypes,
                                                              @Param("registerQuantityStart")BigDecimal registerQuantityStart,
                                                              @Param("registerQuantityEnd")BigDecimal registerQuantityEnd,
                                                              @Param("costAmountStart")BigDecimal costAmountStart,
                                                              @Param("costAmountEnd")BigDecimal costAmountEnd,
                                                              @Param("goodsCommonName") String goodsCommonName,
                                                              @Param("goodsNos") List<String> goodsNos,
                                                              @Param("manufacturer") String manufacturer,
                                                              @Param("expectSaleDaysStart") BigDecimal expectSaleDaysStart,
                                                              @Param("expectSaleDaysEnd") BigDecimal expectSaleDaysEnd,
                                                              @Param("nonSaleDaysStart") Integer nonSaleDaysStart,
                                                              @Param("nonSaleDaysEnd") Integer nonSaleDaysEnd,
                                                              @Param("ids") List<Long> ids,
                                                              @Param("offset") Integer offset,
                                                              @Param("limit") Integer limit,
                                                               @Param("stockLowerLimitEnd")BigDecimal stockLowerLimitEnd,
                                                               @Param("stockLowerLimitStart")BigDecimal stockLowerLimitStart,
                                                               @Param("stockQuantityEnd")BigDecimal stockQuantityEnd,
                                                               @Param("stockQuantityStart")BigDecimal stockQuantityStart,
                                                               @Param("stockUpperLimitEnd")BigDecimal stockUpperLimitEnd,
                                                               @Param("stockUpperLimitStart")BigDecimal stockUpperLimitStart,
                                                               @Param("thirtySalesCountEnd")BigDecimal thirtySalesCountEnd,
                                                               @Param("thirtySalesCountStart")BigDecimal thirtySalesCountStart,
                                                               @Param("thirtySalesQuantityEnd")BigDecimal thirtySalesQuantityEnd,
                                                               @Param("thirtySalesQuantityStart")BigDecimal thirtySalesQuantityStart);


    Long selectIdByStoreCodeAndGoodsNoAndBatchNo(IscmManualRegister iscmManualRegisterParam);

    long countReturnExample(IscmManualRegisterExample example);

    List<StoreReturnWarehouseConfirmDTO> selectReturnByExample(IscmManualRegisterExample example);
}
