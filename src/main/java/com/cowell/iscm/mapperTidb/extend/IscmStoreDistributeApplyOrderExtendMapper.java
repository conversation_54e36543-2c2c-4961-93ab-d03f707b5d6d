package com.cowell.iscm.mapperTidb.extend;

import com.cowell.iscm.service.dto.pushReplenishmentMonitor.*;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface IscmStoreDistributeApplyOrderExtendMapper {
    PosAutoApplyDataDTO countPosDataByOrderType(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate, @Param("orderType") Byte orderType, @Param("orderStatus") Byte orderStatus);

    Integer countForecastQuantity(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate, @Param("orderType") Byte orderType, @Param("orderStatus") Byte orderStatus);

    Integer countStoreQuantityByOrderTypes(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate, @Param("orderTypes") List<Byte> orderTypes, @Param("orderStatus") Byte orderStatus);

    List<CountDataDTO> countDataGroupByOrderType(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate, @Param("orderType") Byte orderType, @Param("orderStatus") Byte orderStatus);

    List<CountDataDTO> countDataGroupByModifyResult(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate, @Param("orderType") Byte orderType, @Param("orderStatus") Byte orderStatus);

    List<StoreTimeDTO> selectStoreMinTime(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate, @Param("orderTypes") List<Byte> orderTypes, @Param("orderStatus") Byte orderStatus);
    @Deprecated
    List<MonitorStoreDTO> selectStoreMinTimeByComCodeAndType(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate, @Param("orderType") Byte orderType, @Param("orderStatus") Byte orderStatus, @Param("offset") Integer offset, @Param("limit") Integer limit);
    @Deprecated
    Integer countStoreMinTimeByComCodeAndType(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate, @Param("orderType") Byte orderType, @Param("orderStatus") Byte orderStatus);

    List<MonitorStoreDTO> selectStoreMinTimeByOrderTypes(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate, @Param("orderTypes") List<Byte> orderTypes, @Param("orderStatus") Byte orderStatus, @Param("offset") Integer offset, @Param("limit") Integer limit);

    Integer countStoreMinTimeByOrderTypes(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate, @Param("orderTypes") List<Byte> orderTypes, @Param("orderStatus") Byte orderStatus);

    List<PosAutoApplyDataDTO> countPosDataGroupByCompany(@Param("companyCodes") List<String> companyCodes, @Param("applyDate") Date applyDate, @Param("orderType") Byte orderType, @Param("orderStatus") Byte orderStatus);
    @Deprecated
    List<StoreQuantityDTO> countForecastQuantityGroupByCompany(@Param("companyCodes") List<String> companyCodes, @Param("applyDate") Date applyDate, @Param("orderType") Byte orderType, @Param("orderStatus") Byte orderStatus);

    Integer countRefuseStoreQuantity(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate, @Param("orderTypes") List<Byte> orderTypes, @Param("orderStatus") Byte orderStatus);
}
