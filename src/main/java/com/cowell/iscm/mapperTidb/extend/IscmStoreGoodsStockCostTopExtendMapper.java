package com.cowell.iscm.mapperTidb.extend;

import com.cowell.iscm.entityTidb.IscmStoreGoodsStockCostTopExample;
import com.cowell.iscm.service.dto.returnWarehouse.StoreReturnWarehouseConfirmDTO;

import java.util.List;

public interface IscmStoreGoodsStockCostTopExtendMapper {
    long countReturnExample(IscmStoreGoodsStockCostTopExample example);

    List<StoreReturnWarehouseConfirmDTO> selectReturnByExample(IscmStoreGoodsStockCostTopExample example);

}
