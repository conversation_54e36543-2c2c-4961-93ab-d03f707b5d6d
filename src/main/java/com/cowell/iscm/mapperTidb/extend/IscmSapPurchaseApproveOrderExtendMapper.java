package com.cowell.iscm.mapperTidb.extend;


import com.cowell.iscm.entityTidb.IscmSapPurchaseApproveOrder;
import com.cowell.iscm.service.dto.pushReplenishmentMonitor.CountDataDTO;
import com.cowell.iscm.service.dto.pushReplenishmentMonitor.StoreTimeDTO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface IscmSapPurchaseApproveOrderExtendMapper {
    List<CountDataDTO> countDataGroupByCheckStatus(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate, @Param("orderType") Byte orderType, @Param("checkStatus") Byte checkStatus);

    List<CountDataDTO> countDataGroupByModifyResult(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate, @Param("orderType") Byte orderType, @Param("checkStatus") Byte checkStatus);

    List<StoreTimeDTO> selectStoreMinTime(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate, @Param("orderType") Byte orderType, @Param("checkStatus") Byte checkStatus, @Param("modifyResultList") List<Byte> modifyResultList, @Param("offset") Integer offset, @Param("limit") Integer limit);

    Long selectStoreMinTimeCount(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate, @Param("orderType") Byte orderType, @Param("checkStatus") Byte checkStatus, @Param("modifyResultList") List<Byte> modifyResultList);

    Integer countRefuseStoreQuantity(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate, @Param("orderType") Byte orderType, @Param("checkStatus") Byte checkStatus);

    Integer countStoreQuantity(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate, @Param("orderType") Byte orderType, @Param("checkStatus") Byte checkStatus);

    Integer countCheckedErrorStoreQuantity(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate, @Param("orderType") Byte orderType);

    List<StoreTimeDTO> selectCheckedErrorStoreMinTime(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate, @Param("orderType") Byte orderType, @Param("offset") Integer offset, @Param("limit") Integer limit);

    Long selectCheckedErrorStoreMinTimeCount(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate, @Param("orderType") Byte orderType);

    Long selectDiffCount(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate, @Param("orderType") Byte orderType, @Param("checkStatus") Byte checkStatus);

    List<IscmSapPurchaseApproveOrder> selectDiffList(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate, @Param("orderType") Byte orderType, @Param("checkStatus") Byte checkStatus, @Param("offset") Integer offset, @Param("limit") Integer limit);
}
