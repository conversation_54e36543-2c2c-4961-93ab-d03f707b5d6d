package com.cowell.iscm.mapperTidb.extend;


import com.cowell.iscm.entityTidb.IscmBdpStoreGoodsInfoExample;
import com.cowell.iscm.service.dto.returnWarehouse.StoreReturnWarehouseConfirmDTO;
import com.cowell.iscm.service.dto.returnWarehouse.StoreWarehouseRelationDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface IscmBdpStoreGoodsInfoExtendMapper {
    List<StoreWarehouseRelationDTO> findWarehouseRelations (@Param("companyCodes") List<String> companyCodes);

    long countReturnExample(IscmBdpStoreGoodsInfoExample example);

    List<StoreReturnWarehouseConfirmDTO> selectReturnByExample(IscmBdpStoreGoodsInfoExample example);
}
