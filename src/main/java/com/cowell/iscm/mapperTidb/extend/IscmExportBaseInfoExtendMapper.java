package com.cowell.iscm.mapperTidb.extend;

import com.cowell.iscm.service.dto.pushReplenishmentMonitor.MonitorAllDataDTO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface IscmExportBaseInfoExtendMapper {

    // 推式补货导出全部的数据list
    List<MonitorAllDataDTO> selectAllExportDataList(@Param("companyCode") String companyCodes, @Param("applyDate") Date applyDate, @Param("start") Integer start, @Param("pageSize") Integer pageSize);

}
