package com.cowell.iscm.mapperTidb.extend;


import com.cowell.iscm.service.dto.controlTower.NonPurchaseDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface IscmGaojiDcBuhuoResultToSapExtendMapper {
    List<NonPurchaseDTO> selectNonPurchaseList(@Param("goodsNos") List<String> goodsNos,
                                               @Param("warehouseCodes") List<String> warehouseCodes,
                                               @Param("start") Integer start,
                                               @Param("pageSize") Integer pageSize);
    Long countNonPurchaseList(@Param("goodsNos") List<String> goodsNos,
                                @Param("warehouseCodes") List<String> warehouseCodes);
}
