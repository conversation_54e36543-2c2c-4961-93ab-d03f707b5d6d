package com.cowell.iscm.mapperTidb.extend;

import com.cowell.iscm.service.dto.pushReplenishmentMonitor.RateCountDTO;
import com.cowell.iscm.service.dto.pushReplenishmentMonitor.StoreTimeDTO;
import com.cowell.iscm.service.dto.pushReplenishmentMonitor.WmsLeaveWarehouseStoreReceiveDTO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface IscmWmsLeaveWarehouseStoreReceiveExtendMapper {

    WmsLeaveWarehouseStoreReceiveDTO selectStartAndFinishTime(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate);

    Integer countLeaveWarehouseStoreQuantity(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate,@Param("orderTypes") List<Byte> orderTypes);

    Integer countLeaveWarehouseOrderQuantity(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate,@Param("orderTypes") List<Byte> orderTypes);

    Integer countAllStoreQuantity(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate,@Param("orderTypes") List<Byte> orderTypes);

    Integer countReceiveStoreQuantity(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate,@Param("orderTypes") List<Byte> orderTypes);

    Integer countLeaveWarehouseGoodsQuantity(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate,@Param("orderTypes") List<Byte> orderTypes);

    @Deprecated
    Integer countAllSatisfiedGoodsQuantity(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate,@Param("orderTypes") List<Byte> orderTypes);

    @Deprecated
    Integer countPartiallySatisfiedGoodsQuantity(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate,@Param("orderTypes") List<Byte> orderTypes);

    @Deprecated
    Integer countNoSatisfiedGoodsQuantity(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate,@Param("orderTypes") List<Byte> orderTypes);

    List<RateCountDTO> countGroupByRateType(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate,@Param("orderTypes") List<Byte> orderTypes);

    List<StoreTimeDTO> selectStoreMinTime(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate, @Param("orderTypes") List<Byte> orderTypes, @Param("offset") Integer offset, @Param("limit") Integer limit);

    Long selectStoreMinTimeCount(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate, @Param("orderTypes") List<Byte> orderTypes);

    Integer countNoSatisfiedStoreQuantity(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate, @Param("orderTypes") List<Byte> orderTypes);

}
