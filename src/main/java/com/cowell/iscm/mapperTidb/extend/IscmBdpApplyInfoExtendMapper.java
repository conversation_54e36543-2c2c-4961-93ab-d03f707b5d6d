package com.cowell.iscm.mapperTidb.extend;

import com.cowell.iscm.entityTidb.IscmBdpApplyInfo;
import com.cowell.iscm.service.dto.applyParam.BdpWarnReportDTO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface IscmBdpApplyInfoExtendMapper {

    int batchUpdateReason(List<IscmBdpApplyInfo> record);

    List<String> getStoreCodesByCompanyCode(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate);

    List<String> getCompanyCodes(@Param("applyDate") Date applyDate);

    List<BdpWarnReportDTO> getBdpWarnByCompanyCode(@Param("companyCode") String companyCode, @Param("storeCodes") List<String> storeCodes, @Param("applyDate") Date applyDate);
}
