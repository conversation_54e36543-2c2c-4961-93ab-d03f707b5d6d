package com.cowell.iscm.mapperTidb.extend;

import com.cowell.iscm.entityTidb.IscmSuggestDistexecDetail;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface IscmSuggestDistexecDetailExtendMapper {

    List<IscmSuggestDistexecDetail> selectAllotExecByOutStoreOrgIdsAndAllotTypes(@Param("registerMonth") Integer registerMonth, @Param("companyOrgIds") List<Long> companyOrgIds, @Param("storeOrgIds") List<Long> storeOrgIds, @Param("allotTypes") List<Byte> allotTypes, @Param("storeAttrs") List<String> storeAttrs, @Param("start") int start, @Param("pageSize") Integer pageSize);


    long updateBatch(@Param("list") List<IscmSuggestDistexecDetail> list);

    long insertBatch(@Param("list") List<IscmSuggestDistexecDetail> list);

    Date selectLastDate();

    List<String> selectDistinctPosAllotNos();

}
