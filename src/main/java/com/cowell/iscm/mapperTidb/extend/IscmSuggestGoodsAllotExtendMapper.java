package com.cowell.iscm.mapperTidb.extend;

import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface IscmSuggestGoodsAllotExtendMapper {

    int updateApproveStatusByIds(@Param("ids") List<Long> ids, @Param("apporveStatus") byte approveStatus,@Param("apporveDate") Date apporveDate);

    List<Long> selectIdsByCompanyCodeByDay(@Param("companyCode") String companyCode, @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    int updateAllotNoByIds(@Param("allotNo") String allotNo, @Param("ids") List<Long> ids);

    List<String> selectGoodsNoByStoreNoAndGoodsNosAndApproveStatus(@Param("storeCode") String storeCode, @Param("goodsNos") List<String> goodsNos, @Param("approveStatus") byte approveStatus);
}
