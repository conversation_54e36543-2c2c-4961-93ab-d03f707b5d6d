package com.cowell.iscm.mapperTidb.extend;

import com.cowell.iscm.entityTidb.IscmBdpIntelligentDistribute;
import com.cowell.iscm.entityTidb.IscmSapTransferOrder;
import com.cowell.iscm.service.dto.pushReplenishmentMonitor.RateCountDTO;
import com.cowell.iscm.service.dto.pushReplenishmentMonitor.SapTransferOrderDTO;
import com.cowell.iscm.service.dto.pushReplenishmentMonitor.StoreTimeDTO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface IscmSapTransferOrderExtendMapper {

    SapTransferOrderDTO selectStartAndFinishTime(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate);

    Integer countTransferStoreQuantity(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate, @Param("orderTypes") List<Byte> orderTypes);

    Integer countTransferOrderQuantity(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate, @Param("orderTypes") List<Byte> orderTypes);

    Integer countTransferGoodsQuantity(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate, @Param("orderTypes") List<Byte> orderTypes);

    Integer countUrgentReceiveStoreQuantity(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate, @Param("orderTypes") List<Byte> orderTypes);

    @Deprecated
    Integer countTransferAllSatisfiedGoodsQuantity(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate, @Param("orderTypes") List<Byte> orderTypes);

    @Deprecated
    Integer countTransferPartiallySatisfiedGoodsQuantity1(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate, @Param("orderTypes") List<Byte> orderTypes);

    @Deprecated
    Integer countTransferPartiallySatisfiedGoodsQuantity2(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate, @Param("orderTypes") List<Byte> orderTypes);

    @Deprecated
    Integer countTransferNoSatisfiedGoodsQuantity(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate, @Param("orderTypes") List<Byte> orderTypes);

    List<RateCountDTO> countGroupByRateType(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate, @Param("orderTypes") List<Byte> orderTypes);

    List<StoreTimeDTO> selectStoreMinTime(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate, @Param("orderTypes") List<Byte> orderTypes, @Param("offset") Integer offset, @Param("limit") Integer limit);

    Long selectStoreMinTimeCount(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate, @Param("orderTypes") List<Byte> orderTypes);

    Integer countNoSatisfiedStoreQuantity(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate, @Param("orderTypes") List<Byte> orderTypes);

    Long selectDiffCount(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate, @Param("orderTypes") List<Byte> orderTypes);

    List<IscmBdpIntelligentDistribute> selectDiffList(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate, @Param("orderTypes") List<Byte> orderTypes, @Param("offset") Integer offset, @Param("limit") Integer limit);
}
