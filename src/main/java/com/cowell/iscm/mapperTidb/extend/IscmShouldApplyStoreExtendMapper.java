package com.cowell.iscm.mapperTidb.extend;


import com.cowell.iscm.service.dto.pushReplenishmentMonitor.StoreQuantityDTO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface IscmShouldApplyStoreExtendMapper {
    Integer countShouldApplyStore(@Param("companyCode") String companyCode, @Param("applyDate") Date applyDate);

    List<StoreQuantityDTO> countShouldApplyStoreGroupByCompany(@Param("companyCodes") List<String> companyCodes, @Param("applyDate") Date applyDate);

}
