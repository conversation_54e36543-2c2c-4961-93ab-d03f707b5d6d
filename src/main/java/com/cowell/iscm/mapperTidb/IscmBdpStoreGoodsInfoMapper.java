package com.cowell.iscm.mapperTidb;

import com.cowell.iscm.entityTidb.IscmBdpStoreGoodsInfo;
import com.cowell.iscm.entityTidb.IscmBdpStoreGoodsInfoExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface IscmBdpStoreGoodsInfoMapper {
    long countByExample(IscmBdpStoreGoodsInfoExample example);

    int deleteByExample(IscmBdpStoreGoodsInfoExample example);

    int deleteByPrimaryKey(Long id);

    int insert(IscmBdpStoreGoodsInfo record);

    int insertSelective(IscmBdpStoreGoodsInfo record);

    List<IscmBdpStoreGoodsInfo> selectByExample(IscmBdpStoreGoodsInfoExample example);

    IscmBdpStoreGoodsInfo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") IscmBdpStoreGoodsInfo record, @Param("example") IscmBdpStoreGoodsInfoExample example);

    int updateByExample(@Param("record") IscmBdpStoreGoodsInfo record, @Param("example") IscmBdpStoreGoodsInfoExample example);

    int updateByPrimaryKeySelective(IscmBdpStoreGoodsInfo record);

    int updateByPrimaryKey(IscmBdpStoreGoodsInfo record);
}