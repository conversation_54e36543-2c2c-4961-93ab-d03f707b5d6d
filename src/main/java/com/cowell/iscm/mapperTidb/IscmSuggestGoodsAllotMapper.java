package com.cowell.iscm.mapperTidb;

import com.cowell.iscm.entityTidb.IscmSuggestGoodsAllot;
import com.cowell.iscm.entityTidb.IscmSuggestGoodsAllotExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface IscmSuggestGoodsAllotMapper {
    long countByExample(IscmSuggestGoodsAllotExample example);

    int deleteByExample(IscmSuggestGoodsAllotExample example);

    int deleteByPrimaryKey(Long id);

    int insert(IscmSuggestGoodsAllot record);

    int insertSelective(IscmSuggestGoodsAllot record);

    List<IscmSuggestGoodsAllot> selectByExample(IscmSuggestGoodsAllotExample example);

    IscmSuggestGoodsAllot selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") IscmSuggestGoodsAllot record, @Param("example") IscmSuggestGoodsAllotExample example);

    int updateByExample(@Param("record") IscmSuggestGoodsAllot record, @Param("example") IscmSuggestGoodsAllotExample example);

    int updateByPrimaryKeySelective(IscmSuggestGoodsAllot record);

    int updateByPrimaryKey(IscmSuggestGoodsAllot record);
}