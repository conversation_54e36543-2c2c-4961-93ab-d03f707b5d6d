package com.cowell.iscm.mapperTidb;

import com.cowell.iscm.entityTidb.IscmGaojiDcBuhuoResultToSap;
import com.cowell.iscm.entityTidb.IscmGaojiDcBuhuoResultToSapExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface IscmGaojiDcBuhuoResultToSapMapper {
    long countByExample(IscmGaojiDcBuhuoResultToSapExample example);

    int deleteByExample(IscmGaojiDcBuhuoResultToSapExample example);

    int deleteByPrimaryKey(Long id);

    int insert(IscmGaojiDcBuhuoResultToSap record);

    int insertSelective(IscmGaojiDcBuhuoResultToSap record);

    List<IscmGaojiDcBuhuoResultToSap> selectByExample(IscmGaojiDcBuhuoResultToSapExample example);

    IscmGaojiDcBuhuoResultToSap selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") IscmGaojiDcBuhuoResultToSap record, @Param("example") IscmGaojiDcBuhuoResultToSapExample example);

    int updateByExample(@Param("record") IscmGaojiDcBuhuoResultToSap record, @Param("example") IscmGaojiDcBuhuoResultToSapExample example);

    int updateByPrimaryKeySelective(IscmGaojiDcBuhuoResultToSap record);

    int updateByPrimaryKey(IscmGaojiDcBuhuoResultToSap record);
}