package com.cowell.iscm.mapperTidb;

import com.cowell.iscm.entityTidb.IscmSuggestDistexecDone;
import com.cowell.iscm.entityTidb.IscmSuggestDistexecDoneExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface IscmSuggestDistexecDoneMapper {
    long countByExample(IscmSuggestDistexecDoneExample example);

    int deleteByExample(IscmSuggestDistexecDoneExample example);

    int deleteByPrimaryKey(Long id);

    int insert(IscmSuggestDistexecDone record);

    int insertSelective(IscmSuggestDistexecDone record);

    List<IscmSuggestDistexecDone> selectByExample(IscmSuggestDistexecDoneExample example);

    IscmSuggestDistexecDone selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") IscmSuggestDistexecDone record, @Param("example") IscmSuggestDistexecDoneExample example);

    int updateByExample(@Param("record") IscmSuggestDistexecDone record, @Param("example") IscmSuggestDistexecDoneExample example);

    int updateByPrimaryKeySelective(IscmSuggestDistexecDone record);

    int updateByPrimaryKey(IscmSuggestDistexecDone record);
}