package com.cowell.iscm.mapperTidb;

import com.cowell.iscm.entityTidb.StoreApplyDateBak;
import com.cowell.iscm.entityTidb.StoreApplyDateBakExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface StoreApplyDateBakMapper {
    long countByExample(StoreApplyDateBakExample example);

    int deleteByExample(StoreApplyDateBakExample example);

    int deleteByPrimaryKey(Long id);

    int insert(StoreApplyDateBak record);

    int insertSelective(StoreApplyDateBak record);

    List<StoreApplyDateBak> selectByExample(StoreApplyDateBakExample example);

    StoreApplyDateBak selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") StoreApplyDateBak record, @Param("example") StoreApplyDateBakExample example);

    int updateByExample(@Param("record") StoreApplyDateBak record, @Param("example") StoreApplyDateBakExample example);

    int updateByPrimaryKeySelective(StoreApplyDateBak record);

    int updateByPrimaryKey(StoreApplyDateBak record);
}