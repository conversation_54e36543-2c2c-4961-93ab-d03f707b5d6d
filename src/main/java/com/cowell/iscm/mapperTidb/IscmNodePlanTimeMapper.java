package com.cowell.iscm.mapperTidb;

import com.cowell.iscm.entityTidb.IscmNodePlanTime;
import com.cowell.iscm.entityTidb.IscmNodePlanTimeExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface IscmNodePlanTimeMapper {
    long countByExample(IscmNodePlanTimeExample example);

    int deleteByExample(IscmNodePlanTimeExample example);

    int deleteByPrimaryKey(Long id);

    int insert(IscmNodePlanTime record);

    int insertSelective(IscmNodePlanTime record);

    List<IscmNodePlanTime> selectByExample(IscmNodePlanTimeExample example);

    IscmNodePlanTime selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") IscmNodePlanTime record, @Param("example") IscmNodePlanTimeExample example);

    int updateByExample(@Param("record") IscmNodePlanTime record, @Param("example") IscmNodePlanTimeExample example);

    int updateByPrimaryKeySelective(IscmNodePlanTime record);

    int updateByPrimaryKey(IscmNodePlanTime record);
}