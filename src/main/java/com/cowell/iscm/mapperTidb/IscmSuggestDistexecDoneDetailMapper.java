package com.cowell.iscm.mapperTidb;

import com.cowell.iscm.entityTidb.IscmSuggestDistexecDoneDetail;
import com.cowell.iscm.entityTidb.IscmSuggestDistexecDoneDetailExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface IscmSuggestDistexecDoneDetailMapper {
    long countByExample(IscmSuggestDistexecDoneDetailExample example);

    int deleteByExample(IscmSuggestDistexecDoneDetailExample example);

    int deleteByPrimaryKey(Long id);

    int insert(IscmSuggestDistexecDoneDetail record);

    int insertSelective(IscmSuggestDistexecDoneDetail record);

    List<IscmSuggestDistexecDoneDetail> selectByExample(IscmSuggestDistexecDoneDetailExample example);

    IscmSuggestDistexecDoneDetail selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") IscmSuggestDistexecDoneDetail record, @Param("example") IscmSuggestDistexecDoneDetailExample example);

    int updateByExample(@Param("record") IscmSuggestDistexecDoneDetail record, @Param("example") IscmSuggestDistexecDoneDetailExample example);

    int updateByPrimaryKeySelective(IscmSuggestDistexecDoneDetail record);

    int updateByPrimaryKey(IscmSuggestDistexecDoneDetail record);
}