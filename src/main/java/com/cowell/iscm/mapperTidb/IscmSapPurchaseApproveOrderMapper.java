package com.cowell.iscm.mapperTidb;

import com.cowell.iscm.entityTidb.IscmSapPurchaseApproveOrder;
import com.cowell.iscm.entityTidb.IscmSapPurchaseApproveOrderExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface IscmSapPurchaseApproveOrderMapper {
    long countByExample(IscmSapPurchaseApproveOrderExample example);

    int deleteByExample(IscmSapPurchaseApproveOrderExample example);

    int deleteByPrimaryKey(Long id);

    int insert(IscmSapPurchaseApproveOrder record);

    int insertSelective(IscmSapPurchaseApproveOrder record);

    List<IscmSapPurchaseApproveOrder> selectByExample(IscmSapPurchaseApproveOrderExample example);

    IscmSapPurchaseApproveOrder selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") IscmSapPurchaseApproveOrder record, @Param("example") IscmSapPurchaseApproveOrderExample example);

    int updateByExample(@Param("record") IscmSapPurchaseApproveOrder record, @Param("example") IscmSapPurchaseApproveOrderExample example);

    int updateByPrimaryKeySelective(IscmSapPurchaseApproveOrder record);

    int updateByPrimaryKey(IscmSapPurchaseApproveOrder record);
}