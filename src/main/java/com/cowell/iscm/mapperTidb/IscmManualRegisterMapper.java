package com.cowell.iscm.mapperTidb;

import com.cowell.iscm.entityTidb.IscmManualRegister;
import com.cowell.iscm.entityTidb.IscmManualRegisterExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface IscmManualRegisterMapper {
    long countByExample(IscmManualRegisterExample example);

    int deleteByExample(IscmManualRegisterExample example);

    int deleteByPrimaryKey(Long id);

    int insert(IscmManualRegister record);

    int insertSelective(IscmManualRegister record);

    List<IscmManualRegister> selectByExample(IscmManualRegisterExample example);

    IscmManualRegister selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") IscmManualRegister record, @Param("example") IscmManualRegisterExample example);

    int updateByExample(@Param("record") IscmManualRegister record, @Param("example") IscmManualRegisterExample example);

    int updateByPrimaryKeySelective(IscmManualRegister record);

    int updateByPrimaryKey(IscmManualRegister record);
}