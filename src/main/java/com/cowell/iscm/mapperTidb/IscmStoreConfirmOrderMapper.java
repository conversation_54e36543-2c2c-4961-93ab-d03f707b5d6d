package com.cowell.iscm.mapperTidb;

import com.cowell.iscm.entityTidb.IscmStoreConfirmOrder;
import com.cowell.iscm.entityTidb.IscmStoreConfirmOrderExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface IscmStoreConfirmOrderMapper {
    long countByExample(IscmStoreConfirmOrderExample example);

    int deleteByExample(IscmStoreConfirmOrderExample example);

    int deleteByPrimaryKey(Long id);

    int insert(IscmStoreConfirmOrder record);

    int insertSelective(IscmStoreConfirmOrder record);

    List<IscmStoreConfirmOrder> selectByExample(IscmStoreConfirmOrderExample example);

    IscmStoreConfirmOrder selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") IscmStoreConfirmOrder record, @Param("example") IscmStoreConfirmOrderExample example);

    int updateByExample(@Param("record") IscmStoreConfirmOrder record, @Param("example") IscmStoreConfirmOrderExample example);

    int updateByPrimaryKeySelective(IscmStoreConfirmOrder record);

    int updateByPrimaryKey(IscmStoreConfirmOrder record);
}