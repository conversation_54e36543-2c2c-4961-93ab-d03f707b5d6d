package com.cowell.iscm.mapperTidb;

import com.cowell.iscm.entityTidb.IscmStorePurchaseTaskDetail;
import com.cowell.iscm.entityTidb.IscmStorePurchaseTaskDetailExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface IscmStorePurchaseTaskDetailMapper {
    long countByExample(IscmStorePurchaseTaskDetailExample example);

    int deleteByExample(IscmStorePurchaseTaskDetailExample example);

    int deleteByPrimaryKey(Long id);

    int insert(IscmStorePurchaseTaskDetail record);

    int insertSelective(IscmStorePurchaseTaskDetail record);

    List<IscmStorePurchaseTaskDetail> selectByExample(IscmStorePurchaseTaskDetailExample example);

    IscmStorePurchaseTaskDetail selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") IscmStorePurchaseTaskDetail record, @Param("example") IscmStorePurchaseTaskDetailExample example);

    int updateByExample(@Param("record") IscmStorePurchaseTaskDetail record, @Param("example") IscmStorePurchaseTaskDetailExample example);

    int updateByPrimaryKeySelective(IscmStorePurchaseTaskDetail record);

    int updateByPrimaryKey(IscmStorePurchaseTaskDetail record);
}