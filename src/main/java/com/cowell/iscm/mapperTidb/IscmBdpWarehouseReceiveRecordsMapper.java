package com.cowell.iscm.mapperTidb;

import com.cowell.iscm.entityTidb.IscmBdpWarehouseReceiveRecords;
import com.cowell.iscm.entityTidb.IscmBdpWarehouseReceiveRecordsExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface IscmBdpWarehouseReceiveRecordsMapper {
    long countByExample(IscmBdpWarehouseReceiveRecordsExample example);

    int deleteByExample(IscmBdpWarehouseReceiveRecordsExample example);

    int deleteByPrimaryKey(Long id);

    int insert(IscmBdpWarehouseReceiveRecords record);

    int insertSelective(IscmBdpWarehouseReceiveRecords record);

    List<IscmBdpWarehouseReceiveRecords> selectByExample(IscmBdpWarehouseReceiveRecordsExample example);

    IscmBdpWarehouseReceiveRecords selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") IscmBdpWarehouseReceiveRecords record, @Param("example") IscmBdpWarehouseReceiveRecordsExample example);

    int updateByExample(@Param("record") IscmBdpWarehouseReceiveRecords record, @Param("example") IscmBdpWarehouseReceiveRecordsExample example);

    int updateByPrimaryKeySelective(IscmBdpWarehouseReceiveRecords record);

    int updateByPrimaryKey(IscmBdpWarehouseReceiveRecords record);
}