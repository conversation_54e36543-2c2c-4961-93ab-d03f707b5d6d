package com.cowell.iscm.mapperTidb;

import com.cowell.iscm.entityTidb.IscmShouldApplyStore;
import com.cowell.iscm.entityTidb.IscmShouldApplyStoreExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface IscmShouldApplyStoreMapper {
    long countByExample(IscmShouldApplyStoreExample example);

    int deleteByExample(IscmShouldApplyStoreExample example);

    int deleteByPrimaryKey(Long id);

    int insert(IscmShouldApplyStore record);

    int insertSelective(IscmShouldApplyStore record);

    List<IscmShouldApplyStore> selectByExample(IscmShouldApplyStoreExample example);

    IscmShouldApplyStore selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") IscmShouldApplyStore record, @Param("example") IscmShouldApplyStoreExample example);

    int updateByExample(@Param("record") IscmShouldApplyStore record, @Param("example") IscmShouldApplyStoreExample example);

    int updateByPrimaryKeySelective(IscmShouldApplyStore record);

    int updateByPrimaryKey(IscmShouldApplyStore record);
}