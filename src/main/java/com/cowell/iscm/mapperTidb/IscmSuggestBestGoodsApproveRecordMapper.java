package com.cowell.iscm.mapperTidb;

import com.cowell.iscm.entityTidb.IscmSuggestBestGoodsApproveRecord;
import com.cowell.iscm.entityTidb.IscmSuggestBestGoodsApproveRecordExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface IscmSuggestBestGoodsApproveRecordMapper {
    long countByExample(IscmSuggestBestGoodsApproveRecordExample example);

    int deleteByExample(IscmSuggestBestGoodsApproveRecordExample example);

    int deleteByPrimaryKey(Long id);

    int insert(IscmSuggestBestGoodsApproveRecord record);

    int insertSelective(IscmSuggestBestGoodsApproveRecord record);

    List<IscmSuggestBestGoodsApproveRecord> selectByExample(IscmSuggestBestGoodsApproveRecordExample example);

    IscmSuggestBestGoodsApproveRecord selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") IscmSuggestBestGoodsApproveRecord record, @Param("example") IscmSuggestBestGoodsApproveRecordExample example);

    int updateByExample(@Param("record") IscmSuggestBestGoodsApproveRecord record, @Param("example") IscmSuggestBestGoodsApproveRecordExample example);

    int updateByPrimaryKeySelective(IscmSuggestBestGoodsApproveRecord record);

    int updateByPrimaryKey(IscmSuggestBestGoodsApproveRecord record);
}