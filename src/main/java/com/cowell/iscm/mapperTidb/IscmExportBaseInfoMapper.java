package com.cowell.iscm.mapperTidb;

import com.cowell.iscm.entityTidb.IscmExportBaseInfo;
import com.cowell.iscm.entityTidb.IscmExportBaseInfoExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface IscmExportBaseInfoMapper {
    long countByExample(IscmExportBaseInfoExample example);

    int deleteByExample(IscmExportBaseInfoExample example);

    int deleteByPrimaryKey(Long id);

    int insert(IscmExportBaseInfo record);

    int insertSelective(IscmExportBaseInfo record);

    List<IscmExportBaseInfo> selectByExample(IscmExportBaseInfoExample example);

    IscmExportBaseInfo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") IscmExportBaseInfo record, @Param("example") IscmExportBaseInfoExample example);

    int updateByExample(@Param("record") IscmExportBaseInfo record, @Param("example") IscmExportBaseInfoExample example);

    int updateByPrimaryKeySelective(IscmExportBaseInfo record);

    int updateByPrimaryKey(IscmExportBaseInfo record);
}