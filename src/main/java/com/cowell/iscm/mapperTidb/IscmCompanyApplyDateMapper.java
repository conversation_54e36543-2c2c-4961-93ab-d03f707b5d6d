package com.cowell.iscm.mapperTidb;

import com.cowell.iscm.entityTidb.IscmCompanyApplyDate;
import com.cowell.iscm.entityTidb.IscmCompanyApplyDateExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface IscmCompanyApplyDateMapper {
    long countByExample(IscmCompanyApplyDateExample example);

    int deleteByExample(IscmCompanyApplyDateExample example);

    int deleteByPrimaryKey(Long id);

    int insert(IscmCompanyApplyDate record);

    int insertSelective(IscmCompanyApplyDate record);

    List<IscmCompanyApplyDate> selectByExample(IscmCompanyApplyDateExample example);

    IscmCompanyApplyDate selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") IscmCompanyApplyDate record, @Param("example") IscmCompanyApplyDateExample example);

    int updateByExample(@Param("record") IscmCompanyApplyDate record, @Param("example") IscmCompanyApplyDateExample example);

    int updateByPrimaryKeySelective(IscmCompanyApplyDate record);

    int updateByPrimaryKey(IscmCompanyApplyDate record);
}