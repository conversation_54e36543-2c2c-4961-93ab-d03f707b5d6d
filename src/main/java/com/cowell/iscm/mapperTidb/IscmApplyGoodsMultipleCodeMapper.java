package com.cowell.iscm.mapperTidb;

import com.cowell.iscm.entityTidb.IscmApplyGoodsMultipleCode;
import com.cowell.iscm.entityTidb.IscmApplyGoodsMultipleCodeExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface IscmApplyGoodsMultipleCodeMapper {
    long countByExample(IscmApplyGoodsMultipleCodeExample example);

    int deleteByExample(IscmApplyGoodsMultipleCodeExample example);

    int deleteByPrimaryKey(Long id);

    int insert(IscmApplyGoodsMultipleCode record);

    int insertSelective(IscmApplyGoodsMultipleCode record);

    List<IscmApplyGoodsMultipleCode> selectByExample(IscmApplyGoodsMultipleCodeExample example);

    IscmApplyGoodsMultipleCode selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") IscmApplyGoodsMultipleCode record, @Param("example") IscmApplyGoodsMultipleCodeExample example);

    int updateByExample(@Param("record") IscmApplyGoodsMultipleCode record, @Param("example") IscmApplyGoodsMultipleCodeExample example);

    int updateByPrimaryKeySelective(IscmApplyGoodsMultipleCode record);

    int updateByPrimaryKey(IscmApplyGoodsMultipleCode record);
}