package com.cowell.iscm.mapperTidb;

import com.cowell.iscm.entityTidb.IscmBdpApplyInfo;
import com.cowell.iscm.entityTidb.IscmBdpApplyInfoExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface IscmBdpApplyInfoMapper {
    long countByExample(IscmBdpApplyInfoExample example);

    int deleteByExample(IscmBdpApplyInfoExample example);

    int deleteByPrimaryKey(Long id);

    int insert(IscmBdpApplyInfo record);

    int insertSelective(IscmBdpApplyInfo record);

    List<IscmBdpApplyInfo> selectByExample(IscmBdpApplyInfoExample example);

    IscmBdpApplyInfo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") IscmBdpApplyInfo record, @Param("example") IscmBdpApplyInfoExample example);

    int updateByExample(@Param("record") IscmBdpApplyInfo record, @Param("example") IscmBdpApplyInfoExample example);

    int updateByPrimaryKeySelective(IscmBdpApplyInfo record);

    int updateByPrimaryKey(IscmBdpApplyInfo record);
}