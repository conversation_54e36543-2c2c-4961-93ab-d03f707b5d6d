package com.cowell.iscm.mapperTidb;

import com.cowell.iscm.entityTidb.IscmSapTransferOrder;
import com.cowell.iscm.entityTidb.IscmSapTransferOrderExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface IscmSapTransferOrderMapper {
    long countByExample(IscmSapTransferOrderExample example);

    int deleteByExample(IscmSapTransferOrderExample example);

    int deleteByPrimaryKey(Long id);

    int insert(IscmSapTransferOrder record);

    int insertSelective(IscmSapTransferOrder record);

    List<IscmSapTransferOrder> selectByExample(IscmSapTransferOrderExample example);

    IscmSapTransferOrder selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") IscmSapTransferOrder record, @Param("example") IscmSapTransferOrderExample example);

    int updateByExample(@Param("record") IscmSapTransferOrder record, @Param("example") IscmSapTransferOrderExample example);

    int updateByPrimaryKeySelective(IscmSapTransferOrder record);

    int updateByPrimaryKey(IscmSapTransferOrder record);
}