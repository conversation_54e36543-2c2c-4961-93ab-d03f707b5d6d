package com.cowell.iscm.mapperTidb;

import com.cowell.iscm.entityTidb.IscmBdpIntelligentDistribute;
import com.cowell.iscm.entityTidb.IscmBdpIntelligentDistributeExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface IscmBdpIntelligentDistributeMapper {
    long countByExample(IscmBdpIntelligentDistributeExample example);

    int deleteByExample(IscmBdpIntelligentDistributeExample example);

    int deleteByPrimaryKey(Long id);

    int insert(IscmBdpIntelligentDistribute record);

    int insertSelective(IscmBdpIntelligentDistribute record);

    List<IscmBdpIntelligentDistribute> selectByExample(IscmBdpIntelligentDistributeExample example);

    IscmBdpIntelligentDistribute selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") IscmBdpIntelligentDistribute record, @Param("example") IscmBdpIntelligentDistributeExample example);

    int updateByExample(@Param("record") IscmBdpIntelligentDistribute record, @Param("example") IscmBdpIntelligentDistributeExample example);

    int updateByPrimaryKeySelective(IscmBdpIntelligentDistribute record);

    int updateByPrimaryKey(IscmBdpIntelligentDistribute record);
}