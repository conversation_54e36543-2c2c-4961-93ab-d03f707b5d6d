package com.cowell.iscm.mapperTidb;

import com.cowell.iscm.entityTidb.IscmSuggestDistexecDetail;
import com.cowell.iscm.entityTidb.IscmSuggestDistexecDetailExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface IscmSuggestDistexecDetailMapper {
    long countByExample(IscmSuggestDistexecDetailExample example);

    int deleteByExample(IscmSuggestDistexecDetailExample example);

    int deleteByPrimaryKey(Long id);

    int insert(IscmSuggestDistexecDetail record);

    int insertSelective(IscmSuggestDistexecDetail record);

    List<IscmSuggestDistexecDetail> selectByExample(IscmSuggestDistexecDetailExample example);

    IscmSuggestDistexecDetail selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") IscmSuggestDistexecDetail record, @Param("example") IscmSuggestDistexecDetailExample example);

    int updateByExample(@Param("record") IscmSuggestDistexecDetail record, @Param("example") IscmSuggestDistexecDetailExample example);

    int updateByPrimaryKeySelective(IscmSuggestDistexecDetail record);

    int updateByPrimaryKey(IscmSuggestDistexecDetail record);
}