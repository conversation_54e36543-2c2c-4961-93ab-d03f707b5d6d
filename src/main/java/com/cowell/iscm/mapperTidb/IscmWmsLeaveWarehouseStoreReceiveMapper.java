package com.cowell.iscm.mapperTidb;

import com.cowell.iscm.entityTidb.IscmWmsLeaveWarehouseStoreReceive;
import com.cowell.iscm.entityTidb.IscmWmsLeaveWarehouseStoreReceiveExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface IscmWmsLeaveWarehouseStoreReceiveMapper {
    long countByExample(IscmWmsLeaveWarehouseStoreReceiveExample example);

    int deleteByExample(IscmWmsLeaveWarehouseStoreReceiveExample example);

    int deleteByPrimaryKey(Long id);

    int insert(IscmWmsLeaveWarehouseStoreReceive record);

    int insertSelective(IscmWmsLeaveWarehouseStoreReceive record);

    List<IscmWmsLeaveWarehouseStoreReceive> selectByExample(IscmWmsLeaveWarehouseStoreReceiveExample example);

    IscmWmsLeaveWarehouseStoreReceive selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") IscmWmsLeaveWarehouseStoreReceive record, @Param("example") IscmWmsLeaveWarehouseStoreReceiveExample example);

    int updateByExample(@Param("record") IscmWmsLeaveWarehouseStoreReceive record, @Param("example") IscmWmsLeaveWarehouseStoreReceiveExample example);

    int updateByPrimaryKeySelective(IscmWmsLeaveWarehouseStoreReceive record);

    int updateByPrimaryKey(IscmWmsLeaveWarehouseStoreReceive record);
}