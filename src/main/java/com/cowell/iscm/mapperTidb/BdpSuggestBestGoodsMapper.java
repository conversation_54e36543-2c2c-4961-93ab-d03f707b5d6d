package com.cowell.iscm.mapperTidb;

import com.cowell.iscm.entityTidb.BdpSuggestBestGoods;
import com.cowell.iscm.entityTidb.BdpSuggestBestGoodsExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BdpSuggestBestGoodsMapper {
    long countByExample(BdpSuggestBestGoodsExample example);

    int deleteByExample(BdpSuggestBestGoodsExample example);

    int deleteByPrimaryKey(Long id);

    int insert(BdpSuggestBestGoods record);

    int insertSelective(BdpSuggestBestGoods record);

    List<BdpSuggestBestGoods> selectByExample(BdpSuggestBestGoodsExample example);

    BdpSuggestBestGoods selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") BdpSuggestBestGoods record, @Param("example") BdpSuggestBestGoodsExample example);

    int updateByExample(@Param("record") BdpSuggestBestGoods record, @Param("example") BdpSuggestBestGoodsExample example);

    int updateByPrimaryKeySelective(BdpSuggestBestGoods record);

    int updateByPrimaryKey(BdpSuggestBestGoods record);
}