package com.cowell.iscm.mapperTidb;

import com.cowell.iscm.entityTidb.SrmDtpSales;
import com.cowell.iscm.entityTidb.SrmDtpSalesExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface SrmDtpSalesMapper {
    long countByExample(SrmDtpSalesExample example);

    int deleteByExample(SrmDtpSalesExample example);

    int deleteByPrimaryKey(Long id);

    int insert(SrmDtpSales record);

    int insertSelective(SrmDtpSales record);

    List<SrmDtpSales> selectByExample(SrmDtpSalesExample example);

    SrmDtpSales selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") SrmDtpSales record, @Param("example") SrmDtpSalesExample example);

    int updateByExample(@Param("record") SrmDtpSales record, @Param("example") SrmDtpSalesExample example);

    int updateByPrimaryKeySelective(SrmDtpSales record);

    int updateByPrimaryKey(SrmDtpSales record);
}

