package com.cowell.iscm.mapperTidb;

import com.cowell.iscm.entityTidb.JymlStoreSkuSuggest;
import com.cowell.iscm.entityTidb.JymlStoreSkuSuggestExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Mapper
@Repository
public interface JymlStoreSkuSuggestMapper {
    long countByExample(JymlStoreSkuSuggestExample example);

    int deleteByExample(JymlStoreSkuSuggestExample example);

    int deleteByPrimaryKey(Long id);

    int insert(JymlStoreSkuSuggest record);

    int insertSelective(JymlStoreSkuSuggest record);

    List<JymlStoreSkuSuggest> selectByExample(JymlStoreSkuSuggestExample example);

    JymlStoreSkuSuggest selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") JymlStoreSkuSuggest record, @Param("example") JymlStoreSkuSuggestExample example);

    int updateByExample(@Param("record") JymlStoreSkuSuggest record, @Param("example") JymlStoreSkuSuggestExample example);

    int updateByPrimaryKeySelective(JymlStoreSkuSuggest record);

    int updateByPrimaryKey(JymlStoreSkuSuggest record);
}