package com.cowell.iscm.mapperTidb;

import com.cowell.iscm.entityTidb.IscmStoreDistributeApplyOrder;
import com.cowell.iscm.entityTidb.IscmStoreDistributeApplyOrderExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface IscmStoreDistributeApplyOrderMapper {
    long countByExample(IscmStoreDistributeApplyOrderExample example);

    int deleteByExample(IscmStoreDistributeApplyOrderExample example);

    int deleteByPrimaryKey(Long id);

    int insert(IscmStoreDistributeApplyOrder record);

    int insertSelective(IscmStoreDistributeApplyOrder record);

    List<IscmStoreDistributeApplyOrder> selectByExample(IscmStoreDistributeApplyOrderExample example);

    IscmStoreDistributeApplyOrder selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") IscmStoreDistributeApplyOrder record, @Param("example") IscmStoreDistributeApplyOrderExample example);

    int updateByExample(@Param("record") IscmStoreDistributeApplyOrder record, @Param("example") IscmStoreDistributeApplyOrderExample example);

    int updateByPrimaryKeySelective(IscmStoreDistributeApplyOrder record);

    int updateByPrimaryKey(IscmStoreDistributeApplyOrder record);
}