package com.cowell.iscm.mapperTidb;

import com.cowell.iscm.entityTidb.IscmStorePurchaseDetail;
import com.cowell.iscm.entityTidb.IscmStorePurchaseDetailExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface IscmStorePurchaseDetailMapper {
    long countByExample(IscmStorePurchaseDetailExample example);

    int deleteByExample(IscmStorePurchaseDetailExample example);

    int deleteByPrimaryKey(Long id);

    int insert(IscmStorePurchaseDetail record);

    int insertSelective(IscmStorePurchaseDetail record);

    List<IscmStorePurchaseDetail> selectByExample(IscmStorePurchaseDetailExample example);

    IscmStorePurchaseDetail selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") IscmStorePurchaseDetail record, @Param("example") IscmStorePurchaseDetailExample example);

    int updateByExample(@Param("record") IscmStorePurchaseDetail record, @Param("example") IscmStorePurchaseDetailExample example);

    int updateByPrimaryKeySelective(IscmStorePurchaseDetail record);

    int updateByPrimaryKey(IscmStorePurchaseDetail record);
}