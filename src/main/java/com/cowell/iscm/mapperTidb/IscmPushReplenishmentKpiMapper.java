package com.cowell.iscm.mapperTidb;

import com.cowell.iscm.entityTidb.IscmPushReplenishmentKpi;
import com.cowell.iscm.entityTidb.IscmPushReplenishmentKpiExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface IscmPushReplenishmentKpiMapper {
    long countByExample(IscmPushReplenishmentKpiExample example);

    int deleteByExample(IscmPushReplenishmentKpiExample example);

    int deleteByPrimaryKey(Long id);

    int insert(IscmPushReplenishmentKpi record);

    int insertSelective(IscmPushReplenishmentKpi record);

    List<IscmPushReplenishmentKpi> selectByExample(IscmPushReplenishmentKpiExample example);

    IscmPushReplenishmentKpi selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") IscmPushReplenishmentKpi record, @Param("example") IscmPushReplenishmentKpiExample example);

    int updateByExample(@Param("record") IscmPushReplenishmentKpi record, @Param("example") IscmPushReplenishmentKpiExample example);

    int updateByPrimaryKeySelective(IscmPushReplenishmentKpi record);

    int updateByPrimaryKey(IscmPushReplenishmentKpi record);
}