package com.cowell.iscm.mapperTidb;

import com.cowell.iscm.entityTidb.BdpPushAvgDailySales;
import com.cowell.iscm.entityTidb.BdpPushAvgDailySalesExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BdpPushAvgDailySalesMapper {
    long countByExample(BdpPushAvgDailySalesExample example);

    int deleteByExample(BdpPushAvgDailySalesExample example);

    int deleteByPrimaryKey(Long id);

    int insert(BdpPushAvgDailySales record);

    int insertSelective(BdpPushAvgDailySales record);

    List<BdpPushAvgDailySales> selectByExample(BdpPushAvgDailySalesExample example);

    BdpPushAvgDailySales selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") BdpPushAvgDailySales record, @Param("example") BdpPushAvgDailySalesExample example);

    int updateByExample(@Param("record") BdpPushAvgDailySales record, @Param("example") BdpPushAvgDailySalesExample example);

    int updateByPrimaryKeySelective(BdpPushAvgDailySales record);

    int updateByPrimaryKey(BdpPushAvgDailySales record);
}