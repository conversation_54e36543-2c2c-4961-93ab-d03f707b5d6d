package com.cowell.iscm.mapperTidb;

import com.cowell.iscm.entityTidb.IscmBdpDailySuggestSum;
import com.cowell.iscm.entityTidb.IscmBdpDailySuggestSumExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface IscmBdpDailySuggestSumMapper {
    long countByExample(IscmBdpDailySuggestSumExample example);

    int deleteByExample(IscmBdpDailySuggestSumExample example);

    int deleteByPrimaryKey(Long id);

    int insert(IscmBdpDailySuggestSum record);

    int insertSelective(IscmBdpDailySuggestSum record);

    List<IscmBdpDailySuggestSum> selectByExample(IscmBdpDailySuggestSumExample example);

    IscmBdpDailySuggestSum selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") IscmBdpDailySuggestSum record, @Param("example") IscmBdpDailySuggestSumExample example);

    int updateByExample(@Param("record") IscmBdpDailySuggestSum record, @Param("example") IscmBdpDailySuggestSumExample example);

    int updateByPrimaryKeySelective(IscmBdpDailySuggestSum record);

    int updateByPrimaryKey(IscmBdpDailySuggestSum record);
}