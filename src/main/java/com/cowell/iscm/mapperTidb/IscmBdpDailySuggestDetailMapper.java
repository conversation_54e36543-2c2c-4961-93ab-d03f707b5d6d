package com.cowell.iscm.mapperTidb;

import com.cowell.iscm.entityTidb.IscmBdpDailySuggestDetail;
import com.cowell.iscm.entityTidb.IscmBdpDailySuggestDetailExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface IscmBdpDailySuggestDetailMapper {
    long countByExample(IscmBdpDailySuggestDetailExample example);

    int deleteByExample(IscmBdpDailySuggestDetailExample example);

    int deleteByPrimaryKey(Long id);

    int insert(IscmBdpDailySuggestDetail record);

    int insertSelective(IscmBdpDailySuggestDetail record);

    List<IscmBdpDailySuggestDetail> selectByExample(IscmBdpDailySuggestDetailExample example);

    IscmBdpDailySuggestDetail selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") IscmBdpDailySuggestDetail record, @Param("example") IscmBdpDailySuggestDetailExample example);

    int updateByExample(@Param("record") IscmBdpDailySuggestDetail record, @Param("example") IscmBdpDailySuggestDetailExample example);

    int updateByPrimaryKeySelective(IscmBdpDailySuggestDetail record);

    int updateByPrimaryKey(IscmBdpDailySuggestDetail record);
}