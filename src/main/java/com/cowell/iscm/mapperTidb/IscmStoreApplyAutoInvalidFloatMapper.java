package com.cowell.iscm.mapperTidb;

import com.cowell.iscm.entityTidb.IscmStoreApplyAutoInvalidFloat;
import com.cowell.iscm.entityTidb.IscmStoreApplyAutoInvalidFloatExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface IscmStoreApplyAutoInvalidFloatMapper {
    long countByExample(IscmStoreApplyAutoInvalidFloatExample example);

    int deleteByExample(IscmStoreApplyAutoInvalidFloatExample example);

    int deleteByPrimaryKey(Long id);

    int insert(IscmStoreApplyAutoInvalidFloat record);

    int insertSelective(IscmStoreApplyAutoInvalidFloat record);

    List<IscmStoreApplyAutoInvalidFloat> selectByExample(IscmStoreApplyAutoInvalidFloatExample example);

    IscmStoreApplyAutoInvalidFloat selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") IscmStoreApplyAutoInvalidFloat record, @Param("example") IscmStoreApplyAutoInvalidFloatExample example);

    int updateByExample(@Param("record") IscmStoreApplyAutoInvalidFloat record, @Param("example") IscmStoreApplyAutoInvalidFloatExample example);

    int updateByPrimaryKeySelective(IscmStoreApplyAutoInvalidFloat record);

    int updateByPrimaryKey(IscmStoreApplyAutoInvalidFloat record);
}