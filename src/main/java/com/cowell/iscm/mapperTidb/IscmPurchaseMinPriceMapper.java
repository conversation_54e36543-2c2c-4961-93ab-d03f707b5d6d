package com.cowell.iscm.mapperTidb;

import com.cowell.iscm.entityTidb.IscmPurchaseMinPrice;
import com.cowell.iscm.entityTidb.IscmPurchaseMinPriceExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface IscmPurchaseMinPriceMapper {
    long countByExample(IscmPurchaseMinPriceExample example);

    int deleteByExample(IscmPurchaseMinPriceExample example);

    int deleteByPrimaryKey(Long id);

    int insert(IscmPurchaseMinPrice record);

    int insertSelective(IscmPurchaseMinPrice record);

    List<IscmPurchaseMinPrice> selectByExample(IscmPurchaseMinPriceExample example);

    IscmPurchaseMinPrice selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") IscmPurchaseMinPrice record, @Param("example") IscmPurchaseMinPriceExample example);

    int updateByExample(@Param("record") IscmPurchaseMinPrice record, @Param("example") IscmPurchaseMinPriceExample example);

    int updateByPrimaryKeySelective(IscmPurchaseMinPrice record);

    int updateByPrimaryKey(IscmPurchaseMinPrice record);
}