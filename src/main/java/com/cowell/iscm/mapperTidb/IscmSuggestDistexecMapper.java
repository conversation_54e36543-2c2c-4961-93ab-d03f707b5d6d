package com.cowell.iscm.mapperTidb;

import com.cowell.iscm.entityTidb.IscmSuggestDistexec;
import com.cowell.iscm.entityTidb.IscmSuggestDistexecExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface IscmSuggestDistexecMapper {
    long countByExample(IscmSuggestDistexecExample example);

    int deleteByExample(IscmSuggestDistexecExample example);

    int deleteByPrimaryKey(Long id);

    int insert(IscmSuggestDistexec record);

    int insertSelective(IscmSuggestDistexec record);

    List<IscmSuggestDistexec> selectByExample(IscmSuggestDistexecExample example);

    IscmSuggestDistexec selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") IscmSuggestDistexec record, @Param("example") IscmSuggestDistexecExample example);

    int updateByExample(@Param("record") IscmSuggestDistexec record, @Param("example") IscmSuggestDistexecExample example);

    int updateByPrimaryKeySelective(IscmSuggestDistexec record);

    int updateByPrimaryKey(IscmSuggestDistexec record);
}