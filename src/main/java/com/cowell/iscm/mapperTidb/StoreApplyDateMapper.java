package com.cowell.iscm.mapperTidb;

import com.cowell.iscm.entityTidb.StoreApplyDate;
import com.cowell.iscm.entityTidb.StoreApplyDateExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface StoreApplyDateMapper {
    long countByExample(StoreApplyDateExample example);

    int deleteByExample(StoreApplyDateExample example);

    int deleteByPrimaryKey(Long id);

    int insert(StoreApplyDate record);

    int insertSelective(StoreApplyDate record);

    List<StoreApplyDate> selectByExample(StoreApplyDateExample example);

    StoreApplyDate selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") StoreApplyDate record, @Param("example") StoreApplyDateExample example);

    int updateByExample(@Param("record") StoreApplyDate record, @Param("example") StoreApplyDateExample example);

    int updateByPrimaryKeySelective(StoreApplyDate record);

    int updateByPrimaryKey(StoreApplyDate record);
}