package com.cowell.iscm.mapperTidb;

import com.cowell.iscm.entityTidb.IscmStoreGoodsStockCostTop;
import com.cowell.iscm.entityTidb.IscmStoreGoodsStockCostTopExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface IscmStoreGoodsStockCostTopMapper {
    long countByExample(IscmStoreGoodsStockCostTopExample example);

    int deleteByExample(IscmStoreGoodsStockCostTopExample example);

    int deleteByPrimaryKey(Long id);

    int insert(IscmStoreGoodsStockCostTop record);

    int insertSelective(IscmStoreGoodsStockCostTop record);

    List<IscmStoreGoodsStockCostTop> selectByExample(IscmStoreGoodsStockCostTopExample example);

    IscmStoreGoodsStockCostTop selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") IscmStoreGoodsStockCostTop record, @Param("example") IscmStoreGoodsStockCostTopExample example);

    int updateByExample(@Param("record") IscmStoreGoodsStockCostTop record, @Param("example") IscmStoreGoodsStockCostTopExample example);

    int updateByPrimaryKeySelective(IscmStoreGoodsStockCostTop record);

    int updateByPrimaryKey(IscmStoreGoodsStockCostTop record);
}