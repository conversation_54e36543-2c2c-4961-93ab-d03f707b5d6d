package com.cowell.iscm.security.oauth2;

import com.cowell.iscm.config.oauth2.OAuth2JwtAccessTokenConverter;
import com.cowell.iscm.rest.errors.BusinessErrorException;
import com.cowell.iscm.rest.errors.ErrorCodeEnum;
import com.cowell.iscm.service.dto.TokenUserDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.common.exceptions.InvalidTokenException;
import org.springframework.security.oauth2.provider.authentication.BearerTokenExtractor;
import org.springframework.security.oauth2.provider.authentication.TokenExtractor;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

@Service
public class TokenAuthenticationManager {

	/*解析解密token字符串的类*/

	@Autowired
    public OAuth2JwtAccessTokenConverter oAuth2JwtAccessTokenConverter;

	/*解析HttpServletRequest cookie获取token的类*/
    private TokenExtractor tokenExtractor = new BearerTokenExtractor();

    /**
     * 根据request解析cookie获取token字符串
     * @param request  HttpServletRequest
     * @return token字符串
     */
    public String getToken(HttpServletRequest request) {
    	Authentication authentication=tokenExtractor.extract(request);
    	if (authentication == null) {
//			throw new InvalidTokenException("Invalid token (token not found)");
            throw new BusinessErrorException(ErrorCodeEnum.INVALID_TOKEN);
		}
		String token = (String) authentication.getPrincipal();
		return token;
    }

    /**
     * 根据request解析cookie获取token字符串
     * @param token  token字符串
     * @return TokenUserDTO token解密后，将用户信息封装的DTO
     */
    public TokenUserDTO getUserInfobyToken(String token){
    	Map<String, Object> map=oAuth2JwtAccessTokenConverter.decode(token);
    	if (map == null) {
			throw new InvalidTokenException("Invalid token (token not found)");
		}
    	TokenUserDTO tokenUserDTO=TokenUserDTO.toDTO(map);
		return tokenUserDTO;
    }

    /**
     * 根据request解析cookie获取用户信息
     * @param request  HttpServletRequest
     * @return TokenUserDTO token解密后，将用户信息封装的DTO
     */
    public TokenUserDTO getUserInfobyToken(HttpServletRequest request){
//    	String token=null;
//		try {
//			token = getToken(request);
//		} catch (Exception e) {
//			return null;
//		}
//		return getUserInfobyToken(token);
        return getUserInfobyToken(getToken(request));
    }

}
