package com.cowell.iscm.service;

import com.cowell.iscm.entity.IscmPushReplenishmentKpiCollect;
import com.cowell.iscm.entityTidb.IscmPushReplenishmentKpi;
import com.cowell.iscm.service.dto.TokenUserDTO;
import com.cowell.iscm.service.dto.configcenter.OrgToBdpMapping;
import com.cowell.iscm.service.dto.saletargetmonitor.IscmKpiCollectDTO;
import com.cowell.iscm.service.dto.saletargetmonitor.WmapeLineChartParam;
import com.cowell.iscm.service.dto.saletargetmonitor.WmapeLineChartResponse;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface IscmSaleTargetMonitorService {
    /**
     * 同步数据
     * @param bdpCompanyCodes 大数据公司编码
     */
    void pushSyncDataTask(List<String> bdpCompanyCodes) throws Exception;

    void syncKpiData(OrgToBdpMapping orgToBdpMapping) throws Exception;

    List<WmapeLineChartResponse> getWmapeLineChart(TokenUserDTO userDTO, WmapeLineChartParam param) throws Exception;

    IscmKpiCollectDTO getDataCollect(TokenUserDTO userDTO, String queryDate) throws Exception;

    List<IscmPushReplenishmentKpi> getTopGoods(TokenUserDTO userDTO, Integer topCount, String queryDate) throws Exception;

    List<String> getModelVersions(TokenUserDTO userDTO) throws Exception;

    void syncKpiCollectData(String modelVersion) throws Exception;

    void exportKpiList(TokenUserDTO userDTO, WmapeLineChartParam param, HttpServletResponse response) throws Exception;
}
