package com.cowell.iscm.service;

import com.cowell.iscm.service.dto.CommonProcessDTO;
import com.cowell.iscm.service.dto.TokenUserDTO;
import com.cowell.iscm.service.dto.returnWarehouse.DelExecParam;
import com.cowell.iscm.service.dto.returnWarehouse.GenExecParam;
import com.cowell.iscm.service.dto.returnWarehouse.ReturnWarehouseMsg;
import com.cowell.iscm.service.dto.returnWarehouse.StoreReturnExecuteOrderImport;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * Created by schuangxigang on 2022/2/9 19:37.
 */
public interface ReturnWarehouseExecuteGenService {
    String genReturnWarehouseExec(TokenUserDTO userDTO, GenExecParam param) throws Exception;

    String importReturnWarehouseExec(TokenUserDTO userDTO, MultipartFile file) throws Exception;

    CommonProcessDTO<StoreReturnExecuteOrderImport> getImportExecProcess(TokenUserDTO userDTO, Integer bizType, HttpServletResponse response) throws Exception ;
    void delete(TokenUserDTO userDTO, DelExecParam param) throws Exception;

    void consumDefectiveReturnData(ReturnWarehouseMsg msg);

    void genNoticeDzMsg();

}
