package com.cowell.iscm.service;

import com.cowell.iscm.service.dto.TokenUserDTO;
import com.cowell.iscm.service.dto.visualcenter.suggestMonitor.MonitorCount;
import com.cowell.iscm.service.dto.visualcenter.suggestMonitor.MonitorDetailDTO;
import com.cowell.iscm.service.dto.visualcenter.suggestMonitor.MonitorDetailQuery;
import com.cowell.iscm.service.feign.response.PageResponse;

import java.util.List;

/**
 * Created by schuangxigang on 2022/6/13 10:35.
 */
public interface DailySuggestMonitorService {
    MonitorCount getMonitorList(TokenUserDTO userDTO, Boolean more);

    PageResponse<List<MonitorDetailDTO>> pageDetailList(TokenUserDTO userDTO, MonitorDetailQuery detailQuery);
}
