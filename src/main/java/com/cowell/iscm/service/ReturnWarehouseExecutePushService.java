package com.cowell.iscm.service;

import com.cowell.iscm.service.dto.TokenUserDTO;
import com.cowell.iscm.service.dto.returnWarehouse.ReturnWarehouseExecuteIssueToOaDTO;
import com.cowell.iscm.service.dto.returnWarehouse.ReturnWarehouseExecuteIssueToPosDTO;
import com.cowell.iscm.service.dto.returnWarehouse.ReturnWarehouseExecuteIssueToWmsDTO;

import java.util.List;

/**
 * 退仓执行推送 服务接口
 *
 * <AUTHOR>
 */
public interface ReturnWarehouseExecutePushService {
    void pushToOA(TokenUserDTO userDTO, ReturnWarehouseExecuteIssueToOaDTO toOaDTO) throws Exception;
    void pushToPOS(TokenUserDTO userDTO, List<ReturnWarehouseExecuteIssueToPosDTO> toPosDTOList) throws Exception;
    void pushToWms(TokenUserDTO userDTO, ReturnWarehouseExecuteIssueToWmsDTO toWmsDTOList) throws Exception;
}
