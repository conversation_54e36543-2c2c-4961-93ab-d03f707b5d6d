package com.cowell.iscm.service;

import com.cowell.iscm.service.dto.TokenUserDTO;
import com.cowell.iscm.service.dto.applyParam.*;
import com.cowell.iscm.service.dto.storeAutonomyAllot.ProcessProgressDTO;
import com.cowell.iscm.service.feign.dto.SpuListVo;
import com.cowell.iscm.service.feign.response.PageResponse;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface StoreApplyParamFloatService {
    PageResponse<List<IscmStoreApplyAutoFloatDTO>> getAutoFolatList(AutoFloatParam param, TokenUserDTO userDTO) throws Exception;

    void saveAutoApplyList(AutoFloatSaveParam param, TokenUserDTO userDTO) throws Exception;

    void invalidAutoFolatList(AutoFloatInvalidParam param, TokenUserDTO userDTO) throws Exception;

    List<Long> getBusinessIdByStoreOrgIds(List<Long> storeOrgIds, TokenUserDTO userDTO) throws Exception;

    void importStoreApplyParamFolat(TokenUserDTO userDTO, Long orgId, Byte goodsChooseType, MultipartFile file) throws Exception;

    ProcessProgressDTO getImportFloatProcess(TokenUserDTO userDTO, Long orgId, Byte goodsChooseType, Integer bizType) throws Exception;

    void autoInvalidFloat(Integer type) throws Exception;

    void downloadErrorImportMsg(TokenUserDTO userDTO, Long orgId, Byte goodsChooseType, HttpServletResponse response) throws Exception;

    void copyGoodsFloatByStore(TokenUserDTO userDTO, AutoFloatGoodsCopyParam param) throws Exception;

    List<String> importGoodsNos(TokenUserDTO userDTO, MultipartFile file) throws Exception;

    List<Long> getAllSelectIds(AutoFloatParam param, TokenUserDTO userDTO) throws Exception;

    PageResponse<List<SpuListVo>> getImportGoodsInfo(ImportFloatGoodsParam param);
}
