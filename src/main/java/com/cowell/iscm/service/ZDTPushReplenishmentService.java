package com.cowell.iscm.service;

import com.cowell.iscm.service.dto.TokenUserDTO;
import com.cowell.iscm.service.dto.zdt.DistributionDTO;
import com.cowell.iscm.service.dto.zdt.DistributionQuery;

/**
 * 推式补货集成智店通服务接口
 *
 * <AUTHOR>
 */
public interface ZDTPushReplenishmentService {

    /**
     * 获取分货任务详情
     *
     * @param userDTO
     * @param query
     * @return
     */
    DistributionDTO getDistributionTask(TokenUserDTO userDTO, DistributionQuery query);

}
