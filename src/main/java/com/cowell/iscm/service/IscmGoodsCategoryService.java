package com.cowell.iscm.service;

import com.cowell.iscm.service.dto.TokenUserDTO;
import com.cowell.iscm.service.dto.goodsCategory.ErrorImportGoodsCategoryInfoDTO;
import com.cowell.iscm.service.dto.goodsCategory.IscmGoodsCategoryDTO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface IscmGoodsCategoryService {

    List<ErrorImportGoodsCategoryInfoDTO> batchImport(MultipartFile file, TokenUserDTO userDTO) throws Exception;

    List<IscmGoodsCategoryDTO> allList(TokenUserDTO userDTO);

    List<IscmGoodsCategoryDTO> getIscmGoodsCategoryListByParentId(Long parentId);

    List<IscmGoodsCategoryDTO> getIscmGoodsCategoryListByIds(List<Long> categoryIds);

    List<IscmGoodsCategoryDTO> getCategoryTreeByLevel(Byte level);
}
