package com.cowell.iscm.service;

import com.cowell.iscm.entity.IscmPushReplenishmentMonitorFinishCache;
import com.cowell.iscm.entityTidb.IscmNodePlanTime;
import com.cowell.iscm.enums.MonitorBusinessTypeEnum;
import com.cowell.iscm.service.dto.TokenUserDTO;
import com.cowell.iscm.service.dto.pushReplenishmentMonitor.*;
import com.cowell.iscm.service.feign.response.PageResponse;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;

public interface PushReplenishmentMonitorService {
    PosAutoApplyDataDTO getPosAutoApplyData(String companyCode, String applyDate, TokenUserDTO userDTO) throws Exception;

    PosApplyOrderDataDTO getPosApplyOrderData(String companyCode, String applyDate, TokenUserDTO userDTO) throws Exception;

    IscmNodePlanTime getPlanTimeByCompanyCode(String companyCode, Date applyDate) throws Exception;

    <T>T getCacheByCompanyCodeAndType(String companyCode, Date applyDate, MonitorBusinessTypeEnum businessTypeEnum, Class<T> objClass) throws Exception;

    void saveCache(IscmPushReplenishmentMonitorFinishCache cache, Date applyDate, MonitorBusinessTypeEnum businessTypeEnum) throws Exception;

    SapPurchaseApproveOrderDataDTO getSapPurchaseApproveOrderData(String companyCode, String applyDate, TokenUserDTO userDTO) throws Exception;

    PageResponse<List<MonitorStoreDTO>> getStoreInfoByPage(String companyCode, String applyDate, Boolean diffViewAble, Byte businessType, List<Byte> orderTypes, Integer page, Integer pageSize, TokenUserDTO userDTO) throws Exception;

    PageResponse<List<IscmStoreDistributeApplyOrderDTO>> getApplyInfoByPage(String companyCode, String applyDate, Byte businessType, Byte orderType, Byte modifyResult, Boolean bdpForecast, Integer page, Integer pageSize, TokenUserDTO userDTO) throws Exception;

    void exportApplyInfo(String companyCode, String applyDate, Byte businessType, Byte orderType, Byte modifyResult, Boolean bdpForecast, TokenUserDTO userDTO) throws Exception;

    LinkedHashMap<String, String> getTableFieldMap(TokenUserDTO userDTO, Byte tableType);

    void exportStoreInfo(String companyCode, String applyDate, Boolean diffViewAble, Byte businessType, List<Byte> orderTypes, TokenUserDTO userDTO) throws Exception;

    BdpIntelligentDistributeDTO getBdpIntelligentDistributeData(String companyCode, String applyDateStr, TokenUserDTO userDTO) throws Exception;

    SapTransferOrderDTO getSapTransferOrderData(String companyCode, String applyDateStr, TokenUserDTO userDTO) throws Exception;

    WmsLeaveWarehouseStoreReceiveDTO getWmsLeaveWarehouseStoreReceiveData(String companyCode, String applyDate, TokenUserDTO userDTO) throws Exception;

    PageResponse<List<IscmBdpIntelligentDistributeDTO>> orderListBdpIntelligentDistribute(String companyCode, String applyDate, Byte rateType, Integer page, Integer pageSize, TokenUserDTO userDTO);

    PageResponse<List<IscmSapTransferOrderDTO>> orderListSapTransferOrder(String companyCode, String applyDateStr, List<Byte> orderTypes, Byte rateType, Boolean diffViewAble, Integer page, Integer pageSize, TokenUserDTO userDTO);

    PageResponse<List<IscmWmsLeaveWarehouseStoreReceiveDTO>> orderListWmsLeaveWarehouseStoreReceive(String companyCode, String applyDate, List<Byte> orderTypes, Byte rateType, Integer page, Integer pageSize, TokenUserDTO userDTO);

    List<DiffDataDTO> getDiffDataList(Long platformOrgId, String applyDate, TokenUserDTO userDTO) throws Exception;

    void exportOrderListWmsLeaveWarehouseStoreReceive(String companyCode, String applyDate, List<Byte> orderTypes, Byte rateType, TokenUserDTO userDTO);

    void exportOrderListSapTransferOrder(String companyCode, String applyDate, List<Byte> orderTypes, Byte rateType, Boolean diffViewAble, TokenUserDTO userDTO);

    void exportOrderListBdpIntelligentDistribute(String companyCode, String applyDate, Byte businessType, Byte rateType, Integer page, Integer pageSize, TokenUserDTO userDTO);

    /**
     * 生成连锁差异
     * @throws Exception
     */
    void genCompanyDiff() throws Exception;

    StoreConfirmOrderDataDTO getStoreConfirmData(String companyCode, String applyDate, TokenUserDTO userDTO) throws Exception;

    PageResponse<List<IscmStoreConfirmOrderDTO>> orderListStoreConfirmOrder(String companyCode, String applyDate,Byte receiveType, Integer page, Integer pageSize, TokenUserDTO userDTO);

    void exportOrderListStoreConfirmOrder(String companyCode, String applyDate, Byte receiveType, Integer page, Integer pageSize, TokenUserDTO userDTO);

    PageResponse<List<IscmSapPurchaseApproveOrderDTO>> getSapPurchaseApproveByPage(String companyCode, String applyDate, Byte orderType, Byte modifyResult, Boolean diffViewAble, Integer page, Integer pageSize, TokenUserDTO userDTO) throws Exception;

    void exportSapPurchaseApprove(String companyCode, String applyDate, Byte orderType, Byte modifyResult, Boolean diffViewAble, TokenUserDTO userDTO);

    void deleteMonitorCache(String companyCode, String applyDate, Byte businessType, TokenUserDTO userDTO) throws Exception;

    void exportAllData(String companyCode, String applyDate, TokenUserDTO userDTO) throws Exception;

    String importNodePlan(MultipartFile file, TokenUserDTO userDTO) throws Exception;
}
