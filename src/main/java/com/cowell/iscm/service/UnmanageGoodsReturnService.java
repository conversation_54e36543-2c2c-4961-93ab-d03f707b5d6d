package com.cowell.iscm.service;

import com.cowell.iscm.service.dto.TokenUserDTO;
import com.cowell.iscm.service.dto.unmanageGoodsReturn.CreateUnmanageGoodsReturnTaskRequest;
import com.cowell.iscm.service.dto.unmanageGoodsReturn.CreateUnmanageGoodsReturnTaskResponse;
import com.cowell.iscm.service.dto.unmanageGoodsReturn.UnmanageGoodsReturnProgressDTO;

/**
 * 不经营品退仓服务接口
 * 
 * <AUTHOR>
 */
public interface UnmanageGoodsReturnService {

    /**
     * 创建不经营品退仓任务
     * 
     * @param userDTO 用户信息
     * @param request 请求参数
     * @return 创建结果
     * @throws Exception 异常
     */
    CreateUnmanageGoodsReturnTaskResponse createTask(TokenUserDTO userDTO, CreateUnmanageGoodsReturnTaskRequest request) throws Exception;

    /**
     * 检查平台是否有进行中的任务
     *
     * @param platformOrgId 平台机构ID
     * @return 是否有进行中的任务
     */
    boolean hasRunningTask(Long platformOrgId);

    /**
     * 获取任务处理进度
     *
     * @param userDTO 用户信息
     * @param taskCode 任务编码
     * @return 处理进度
     */
    UnmanageGoodsReturnProgressDTO getTaskProgress(TokenUserDTO userDTO, String taskCode);
}
