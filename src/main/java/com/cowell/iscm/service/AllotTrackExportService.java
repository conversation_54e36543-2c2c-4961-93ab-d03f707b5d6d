package com.cowell.iscm.service;

import com.cowell.iscm.service.dto.TokenUserDTO;
import com.cowell.iscm.service.dto.visualcenter.allottrack.TrackQuery;

/**
 * 调拨执行跟踪导出服务接口
 *
 * <AUTHOR>
 */
public interface AllotTrackExportService {
    void exportRegister(TokenUserDTO userDTO, TrackQuery trackQuery);

    void exportSuggestUnApproved(TokenUserDTO userDTO, TrackQuery trackQuery);

    void exportSuggestInvalid(TokenUserDTO userDTO, TrackQuery trackQuery);

    void exportSuggestUnExecuted(TokenUserDTO userDTO, TrackQuery trackQuery);

    void exportSuggestExecuted(TokenUserDTO userDTO, TrackQuery trackQuery);

    void exportSuggestRejected(TokenUserDTO userDTO, TrackQuery trackQuery);

    void exportSuggestAllPosted(TokenUserDTO userDTO, TrackQuery trackQuery);
}
