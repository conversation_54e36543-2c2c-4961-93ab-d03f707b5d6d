package com.cowell.iscm.service;

import com.cowell.iscm.service.dto.PaginationQuery;
import com.cowell.iscm.service.dto.TokenUserDTO;
import com.cowell.iscm.service.dto.returnWarehouse.*;
import com.cowell.iscm.service.feign.response.PageResponse;

import java.util.List;
import java.util.Set;

/**
 * 退仓执行 服务接口
 *
 * <AUTHOR>
 */
public interface ReturnWarehouseExecuteService {
    Set<WarehouseDTO> getWarehouseInfos(Integer month);

    Set<Long> getCheckedIds(TokenUserDTO userDTO, ReturnWarehouseExecuteParam param) throws Exception;

    Set<String> getCheckedOrderNos(TokenUserDTO userDTO, ReturnWarehouseExecuteParam param) throws Exception;

    PageResponse<List<StoreReturnExecuteOrderDTO>> pageExecuteList(TokenUserDTO userDTO, ReturnWarehouseExecuteParam param) throws Exception;

    StoreReturnExecuteOrderDetailDTO getExecuteDetail(TokenUserDTO userDTO, ReturnWarehouseExecuteIdParam param) throws Exception;

    PageResponse<List<StoreReturnExecuteOrderDetailListDTO>> pageExecuteDetailList(TokenUserDTO userDTO, ReturnWarehouseExecuteDetailParam param) throws Exception;

    PageResponse<List<StoreReturnExecuteOrderDetailListDTO>> pageExecuteDetailList(TokenUserDTO userDTO, ReturnWarehouseExecuteParam param) throws Exception;

    PageResponse<List<StoreReturnExecuteOrderDetailListDTO>> pageExecuteDetailList(TokenUserDTO userDTO, ReturnWarehouseExecuteIdBatchParam param, PaginationQuery paginationQuery) throws Exception;

    void deleteExecute(TokenUserDTO userDTO, ReturnWarehouseExecuteIdParam param) throws Exception;

    void deleteExecute(TokenUserDTO userDTO, ReturnWarehouseExecuteIdBatchParam param) throws Exception;

    void deleteExecuteDetail(TokenUserDTO userDTO, ReturnWarehouseExecuteIdParam param) throws Exception;

    void deleteExecuteDetail(TokenUserDTO userDTO, ReturnWarehouseExecuteIdBatchParam param) throws Exception;

    void exportExecuteList(TokenUserDTO userDTO, ReturnWarehouseExecuteParam param) throws Exception;

    void issue(TokenUserDTO userDTO, ReturnWarehouseExecuteIssueParam param);

    void reIssue(TokenUserDTO userDTO, ReturnWarehouseExecuteIssueParam param);

    void issueReceiveOA(ReturnWarehouseExecuteIssueFromOaDTO fromOaDTO) throws Exception;

    void issueReceivePOS(List<ReturnWarehouseExecuteIssueFromPosDTO> fromPosDTOList) throws Exception;

    void checkReceiveOA(ReturnWarehouseExecuteIssueFromOaDTO fromOaDTO) throws Exception;

    void checkReceivePOS(List<ReturnWarehouseExecuteIssueFromPosDTO> fromPosDTOList) throws Exception;

    void autoVoid();

    List<StoreReturnExecuteOrderSum> sumOrder(TokenUserDTO userDTO, List<String> orderNoList, List<Byte> processStatusList, Integer month);
    List<StoreReturnExecuteOrderSum> sumOrder(TokenUserDTO userDTO, List<String> orderNoList, List<Byte> processStatusList, Integer month, Boolean asyncUpdate);

    void beforeIssue(TokenUserDTO userDTO, ReturnWarehouseExecuteIssueParam param, boolean reIssue) throws Exception;

    void autoUpdateIssuingOrder(byte processStatus);
}
