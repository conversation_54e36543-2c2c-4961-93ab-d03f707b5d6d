package com.cowell.iscm.service;

import com.cowell.iscm.entity.IscmStoreApplyParamGoodsOrgValidWhitelist;
import com.cowell.iscm.service.dto.TokenUserDTO;
import com.cowell.permission.dto.OrgDTO;

import java.util.List;
import java.util.Map;

public interface StoreApplyParamPushHDService {
    void sendHDNonATMCBySave(Long orgId, String empCode) throws Exception;
    void sendHDNonATMCStoreGroupBySave(Long orgId, Integer orgType, Byte operationFlag,TokenUserDTO userDTO) throws Exception;
    void sendHDNonATMCByExtend(Long orgId, String empCode, Map<Long, OrgDTO> parentMap) throws Exception;
    void sendHDNonATMCByPush(Long orgId, String empCode) throws Exception;
    void sendWhiteListBySave(Long orgId) throws Exception;
    void sendWhiteListByVoid(Long orgId, List<IscmStoreApplyParamGoodsOrgValidWhitelist> list, TokenUserDTO userDTO) throws Exception;

    void sendWhiteListByVoid(List<Long> ids) throws Exception;

    void sendAllWhitelist() throws Exception;
}
