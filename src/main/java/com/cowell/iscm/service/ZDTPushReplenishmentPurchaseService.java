package com.cowell.iscm.service;

import com.cowell.iscm.service.dto.TokenUserDTO;
import com.cowell.iscm.service.dto.zdt.PurchaseDTO;
import com.cowell.iscm.service.dto.zdt.PurchaseQuery;

import java.util.Date;
import java.util.Map;

/**
 * 推式补货集成智店通服务接口-请货单任务推送
 *
 * <AUTHOR>
 */
public interface ZDTPushReplenishmentPurchaseService {

    /**
     * 获取请货单任务推送详情
     *
     * @param userDTO
     * @param query
     * @return
     */
    PurchaseDTO getPurchaseTask(TokenUserDTO userDTO,  PurchaseQuery query);

    /**
     * 推送请货单任务
     *
     */
    void pushPurchaseTask() throws Exception;

    /**
     * 删除 ISCM定时任务删除第T-31天的(iscm_sotre_purchase_detail and  iscm_sotre_purchase_task_detail)数据
     *
     */
    void deletePurchaseDataT31() throws Exception;

    /**
     * 同步请货原始数据到 请货详情表
     * @param date
     */
    int syncPurchaseDataHander(Date date) throws Exception;

    /**
     * 获取指定门店最近的推送任务
     * @param userDTO
     * @param query
     * @return
     */
    Map<String, Object> getRecentTaskByStoreId(TokenUserDTO userDTO, PurchaseQuery query);
}
