package com.cowell.iscm.service;

import java.util.Date;

/**
 * 同步SAP Hana数据到MySQL 定时任务
 * <AUTHOR>
 * @date  2021-03-25 16:36:26
 */
public interface SyncSAPDataService {
    /**
     *  同步SAP Hana数据到MySQL 定时任务
     * 1、直连sap备库，期初时一次性抽取近3个月的业务数据（用创建日期取）-》需要考虑sap数据库的性能，要分批次抽取。
     * 2、每半小时查询sap备库表里过去1小时的变化行（创建时间+更新时间），更新/新增到iscm的表中。
     * 3、iscm表与sap表字段保持一致，方便后续对数据。全表数据都存储，有颜色的字段需要单独存储，其他字段可以合并在一列中。
     * 4、iscm的表该月做归档，主表只保留近3个月的数据集合，其他数据放到其他表中。(删除)
     */
    void syncSapDataHander(String initDate) throws Exception;


}
