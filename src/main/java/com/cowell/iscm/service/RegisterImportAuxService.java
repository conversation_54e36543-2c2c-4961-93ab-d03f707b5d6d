package com.cowell.iscm.service;

import com.cowell.iscm.entity.IscmNearExpiryDateImport;
import com.cowell.iscm.service.feign.dto.SpuListVo;
import com.cowell.iscm.service.feign.dto.StockGoodsCountInfo;
import com.cowell.permission.dto.mdm.MdmStoreBaseDTO;

import java.util.List;
import java.util.Map;

/**
 * 近效期登记批导辅助服务接口
 *
 * <AUTHOR>
 */
public interface RegisterImportAuxService {
    /**
     * 获取门店信息Map
     *
     * @param iscmNearExpiryDateImports
     * @return
     * @throws Exception
     */
    Map<String, MdmStoreBaseDTO> getStoreMap(List<IscmNearExpiryDateImport> iscmNearExpiryDateImports) throws Exception;

    /**
     * 获取商品信息Map
     *
     * @param iscmNearExpiryDateImports
     * @return
     * @throws Exception
     */
    Map<String, SpuListVo> getGoodsMap(List<IscmNearExpiryDateImport> iscmNearExpiryDateImports) throws Exception;

//    /**
//     * 获取库存信息Map
//     *
//     * @param storeMap
//     * @param iscmNearExpiryDateImports
//     * @return
//     * @throws Exception
//     */
//    Map<String, List<StockGoodsCountInfo>> getStockMap(Map<String, MdmStoreBaseDTO> storeMap, List<IscmNearExpiryDateImport> iscmNearExpiryDateImports) throws Exception;

    /**
     * 获取门店名称列表
     *
     * @param storeMap
     * @param iscmNearExpiryDateImports
     * @return
     * @throws Exception
     */
    List<String> getStoreNames(Map<String, MdmStoreBaseDTO> storeMap, List<IscmNearExpiryDateImport> iscmNearExpiryDateImports) throws Exception;
}
