package com.cowell.iscm.service;

import com.cowell.iscm.service.dto.TokenUserDTO;
import com.cowell.iscm.service.dto.applyParam.PageResult;
import com.cowell.iscm.service.dto.storeAccessAuditor.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/16 11:01
 */
public interface IscmStoreAccessAuditorService {

    /**
     * 添加审核员信息
     * @param accessAuditorAddParam
     * @param userDTO
     */
    void addStoreAuditor(StoreAccessAuditorAddParam accessAuditorAddParam, TokenUserDTO userDTO);

    /**
     * 导入
     * @param file
     * @param platformOrgId
     * @param companyOrgId
     * @param companyOrgType
     * @param userDTO
     * @return
     * @throws Exception
     */
    ImportResponseDTO importStoreAuditor(MultipartFile file, Long platformOrgId, Long companyOrgId, Integer companyOrgType, TokenUserDTO userDTO);

    /**
     * 列表查询
     * @param auditorListParam
     * @return
     */
    PageResult<StoreAccessAuditorListDTO> storeAuditorList(StoreAccessAuditorListParam auditorListParam, TokenUserDTO userDTO);

    /**
     * 导出
     * @param auditorListParam
     * @param userDTO
     */
    void export(StoreAccessAuditorListParam auditorListParam, TokenUserDTO userDTO);

    /**
     * 用户组织列表
     * @param orgApiType
     * @param platOrgId
     * @param userDTO
     * @return
     */
    List<OptionDto> userOrgList(Integer orgApiType, Long platOrgId, TokenUserDTO userDTO);

}
