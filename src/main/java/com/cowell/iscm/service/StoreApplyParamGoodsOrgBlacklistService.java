package com.cowell.iscm.service;

import com.cowell.iscm.enums.StoreChooseTypeEnum;
import com.cowell.iscm.service.dto.ImportExcelResult;
import com.cowell.iscm.service.dto.TokenUserDTO;
import com.cowell.iscm.service.dto.applyParam.*;
import com.cowell.iscm.service.feign.dto.OrgTreeSimpleDTO;
import com.cowell.iscm.service.feign.response.PageResponse;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * Created by schuangxigang on 2022/6/26 23:49.
 */
public interface StoreApplyParamGoodsOrgBlacklistService {
    PageResponse<List<StoreApplyParamGoodsOrgBlacklist>> getValidBlacklistLimits(TokenUserDTO userDTO, StoreApplyParamGoodsOrgBlacklistQuery query) throws Exception;

    PageResponse<List<StoreApplyParamGoodsOrgBlacklist>> getVoidBlacklistLimits(TokenUserDTO userDTO, StoreApplyParamGoodsOrgBlacklistQuery query) throws Exception;

    void saveValidBlacklist(TokenUserDTO userDTO, StoreApplyParamGoodsOrgBlacklistAdd param) throws Exception;

    void voidBlacklist(StoreApplyParamGoodsOrgVoid goodsOrgVoid, TokenUserDTO userDTO) throws Exception;

    List<Long> tagEntityCodeListQuery(List<Long> tagIds);

    ImportExcelResult batchImport(TokenUserDTO userDTO, Long orgId, Integer orgType, MultipartFile file) throws Exception;

    Map<Long, OrgTreeSimpleDTO> checkAndGetStoreInfoByChooseType(StoreChooseTypeEnum storeChooseTypeEnum, Long userId, Long orgId, List<Long> storeOrgIds, List<Long> tagIds) throws Exception;

    List<Long> getBlacklistIdsByOrgId(TokenUserDTO userDTO, StoreApplyParamGoodsOrgBlacklistQuery query) throws Exception;

    List<StoreApplyParamGoodsImport> getGoodsList(MultipartFile File, TokenUserDTO userDTO) throws Exception;

    void exportValidBlacklist(TokenUserDTO userDTO, StoreApplyParamGoodsOrgVoid query) throws Exception;

//    void batchDelete(TokenUserDTO userDTO, AutoFloatInvalidParam param) throws Exception;


}
