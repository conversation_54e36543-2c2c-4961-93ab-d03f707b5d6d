package com.cowell.iscm.service;

import com.cowell.iscm.enums.StoreChooseTypeEnum;
import com.cowell.iscm.service.dto.ImportExcelResult;
import com.cowell.iscm.service.dto.TokenUserDTO;
import com.cowell.iscm.service.dto.applyParam.AutoFloatInvalidParam;
import com.cowell.iscm.service.dto.applyParam.StoreApplyParamGoodsCatalogLimit;
import com.cowell.iscm.service.dto.applyParam.StoreApplyParamGoodsCatalogLimitAdd;
import com.cowell.iscm.service.dto.applyParam.StoreApplyParamGoodsCatalogLimitQuery;
import com.cowell.iscm.service.feign.dto.OrgSimpleDTO;
import com.cowell.iscm.service.feign.dto.OrgTreeSimpleDTO;
import com.cowell.iscm.service.feign.dto.SpuNewVo;
import com.cowell.iscm.service.feign.response.PageResponse;
import com.google.common.collect.Lists;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * Created by schuangxigang on 2022/6/26 23:49.
 */
public interface StoreApplyParamGoodsCatalogLimitService {
    PageResponse<List<StoreApplyParamGoodsCatalogLimit>> getCatalogLimits(TokenUserDTO userDTO, StoreApplyParamGoodsCatalogLimitQuery query) throws Exception;

    void saveCatalogLimits(TokenUserDTO userDTO, StoreApplyParamGoodsCatalogLimitAdd param) throws Exception;

    void saveTimely(TokenUserDTO userDTO, StoreApplyParamGoodsCatalogLimit param) throws Exception;

    ImportExcelResult batchImport(TokenUserDTO userDTO, Long orgId, Integer orgType, MultipartFile file) throws Exception;

    void batchDelete(TokenUserDTO userDTO, AutoFloatInvalidParam param) throws Exception;

    Set<Long> getCheckedIds(TokenUserDTO userDTO, Long orgId) throws Exception;

    List<Long> tagEntityCodeListQuery(List<Long> tagIds);

    Map<Long, OrgTreeSimpleDTO> checkAndGetStoreInfoByChooseType(StoreChooseTypeEnum storeChooseTypeEnum, Long userId, Long orgId, List<Long> storeOrgIds, List<Long> tagIds) throws Exception;

    /**
     * 判断商品是否禁止请货类别
     * @param spuNewVo
     * @return true 禁止
     */
    boolean forbidGoodsClass(SpuNewVo spuNewVo);
    }
