package com.cowell.iscm.service;

import com.cowell.iscm.service.dto.TokenUserDTO;
import com.cowell.iscm.service.dto.applyParam.ChooseStoreDTO;
import com.cowell.iscm.service.dto.applyParam.StoreApplyParamNonAutomaticDTO;

/**
 * Created by schuangxigang on 2022/6/23 12:46.
 */
public interface StoreApplyParamNonAutomaticService {
    StoreApplyParamNonAutomaticDTO getApplyList(TokenUserDTO userDTO, Long orgId);

    void saveApplyList(TokenUserDTO userDTO, StoreApplyParamNonAutomaticDTO param) throws Exception;

    ChooseStoreDTO getChooseStoreType(TokenUserDTO userDTO, Long orgId, Integer paramType) throws Exception;
}
