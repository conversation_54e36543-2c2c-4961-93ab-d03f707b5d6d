package com.cowell.iscm.service;

import com.cowell.iscm.service.dto.CommonImportResponse;
import com.cowell.iscm.service.dto.TokenUserDTO;
import com.cowell.iscm.service.dto.applyParam.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * Created by schuangxigang on 2022/6/23 23:07.
 */
public interface StoreApplyParamGoodsUpperLimitService {
    StoreApplyParamGoodsUpperLimitDTO getApplyList(TokenUserDTO userDTO, Long orgId, Integer paramScope);

    void saveApplyList(TokenUserDTO userDTO, StoreApplyParamGoodsUpperLimitDTO param) throws Exception;

    List<StoreApplyParamGoodsUpperLimit> getUpperLimits(TokenUserDTO userDTO, StoreApplyParamGoodsUpperLimitQuery query);

    List<StoreApplyParamGoodsUpperLimitItem> getUpperLimitItems(TokenUserDTO userDTO, StoreApplyParamGoodsUpperLimitItemQuery query);

    GoodsUpperLimitImportRes importUpperLimit(TokenUserDTO userDTO, MultipartFile file) throws Exception;

    CommonImportResponse<ImportApplyCtrlDTO> importApplyCtrl(TokenUserDTO userDTO, MultipartFile file, Byte itemType) throws Exception;

    void batchDelApplyCtrl(List<Long>idList, Byte itemType,Long orgId,TokenUserDTO userDTO) throws Exception;

    void refushDistrCycle(Map<String, List<String>> distrCycleMap);
}
