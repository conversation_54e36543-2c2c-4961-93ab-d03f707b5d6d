package com.cowell.iscm.service;

import com.cowell.iscm.service.dto.CommonImportResponse;
import com.cowell.iscm.service.dto.TokenUserDTO;
import com.cowell.iscm.service.dto.applyParam.PageResult;
import com.cowell.iscm.service.dto.storeAccessAuditor.*;
import com.cowell.iscm.service.dto.storeReplace.ErrorImportDTO;
import com.cowell.iscm.service.dto.storeReplace.IscmStoreReplaceMappingDTO;
import com.cowell.iscm.service.dto.storeReplace.StoreReplaceAddParam;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/16 11:01
 */
public interface IscmStoreReplaceMappingService {


    void addStoreReplaceMapping(StoreReplaceAddParam param, TokenUserDTO userDTO);

    PageResult<IscmStoreReplaceMappingDTO> storeReplaceList(StoreReplaceAddParam param, TokenUserDTO userDTO);
    CommonImportResponse<ErrorImportDTO> importStoreReplaceMapping(MultipartFile file, StoreReplaceAddParam param, TokenUserDTO userDTO);

    void export(StoreReplaceAddParam param, TokenUserDTO userDTO);
}
