package com.cowell.iscm.service;

import com.cowell.iscm.service.dto.TokenUserDTO;
import com.cowell.iscm.service.dto.applyParam.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface StoreApplyParamService {
    /**
     * 自动请货参数列表
     * @param orgId
     * @param paramType
     * @param paramScope
     * @param userDTO
     * @return
     * @throws Exception
     */
    StoreApplyParamDTO getAutoApplyList(Long orgId, Integer orgType,Integer paramType, Integer paramScope, TokenUserDTO userDTO) throws Exception;

    void saveAutoApplyList(StoreApplyParamDTO param, TokenUserDTO userDTO) throws Exception;

    List<DropDownBoxDTO> dropDownBoxList(Integer type) throws Exception;

    void superParentParam(Long orgId, Integer orgType, Integer paramType, Integer paramScope, TokenUserDTO userDTO) throws Exception;

    List<Long> getNoDefaultChooseOrgs(Long orgId, Integer orgType, Integer paramType,  Integer paramScope, TokenUserDTO userDTO) throws Exception;

    void pushParam(PushParamParam param, TokenUserDTO userDTO) throws Exception;

    GoodsLimitImportRes importGoodsLimit(MultipartFile file, TokenUserDTO userDTO) throws Exception;

    void batchDelGoodsLimit(List<Long> idList,Long orgId,TokenUserDTO userDTO) throws Exception;

    void exportStoreGroup(TokenUserDTO userDTO, List<Long> groupIds);
}
