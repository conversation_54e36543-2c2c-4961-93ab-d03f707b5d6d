package com.cowell.iscm.service;

import com.cowell.iscm.service.dto.visualcenter.allotdetail.AllotDetail;
import com.cowell.iscm.service.dto.visualcenter.allotdetail.AllotQuery;
import com.cowell.iscm.service.dto.visualcenter.allotdetail.AllotStatistics;

import java.util.List;

/**
 * 调拨明细服务接口
 *
 * <AUTHOR>
 */
public interface AllotDetailService {

    /**
     * 调拨明细列表
     *
     * @param allotQuery
     * @return
     * @throws Exception
     */
    List<AllotDetail> list(AllotQuery allotQuery) throws Exception;

    /**
     * 调拨明细统计
     *
     * @param details
     * @return
     * @throws Exception
     */
    AllotStatistics statistics(List<AllotDetail> details) throws Exception;
}
