package com.cowell.iscm.service;

import com.cowell.iscm.service.dto.TokenUserDTO;
import com.cowell.iscm.service.dto.autoAllotExecTrackManage.AllotTrackDTO;
import com.cowell.iscm.service.dto.autoAllotExecTrackManage.AllotTrackParam;
import com.cowell.iscm.service.dto.autoAllotExecTrackManage.FavoriteDTO;

import java.util.List;

public interface AutoAllotExecTrackManageService {

    List<FavoriteDTO> getFavoriteList(TokenUserDTO userDTO) throws Exception;

    Boolean favoriteChooseAble(TokenUserDTO userDTO, String favoriteUrl) throws Exception;

    void favoriteSave(TokenUserDTO userDTO, FavoriteDTO favoriteDTO) throws Exception;

    void favoriteDelete(TokenUserDTO userDTO, String favoriteUrl) throws Exception;

    AllotTrackDTO getAllotTrackInfo(TokenUserDTO userDTO, AllotTrackParam param) throws Exception;
}
