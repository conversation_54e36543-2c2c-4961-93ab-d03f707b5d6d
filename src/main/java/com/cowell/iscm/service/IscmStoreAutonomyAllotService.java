package com.cowell.iscm.service;

import com.cowell.iscm.entity.IscmStoreAutonomyAllotSendPosTask;
import com.cowell.iscm.service.dto.TokenUserDTO;
import com.cowell.iscm.service.dto.storeAutonomyAllot.*;
import com.cowell.iscm.service.feign.response.PageResponse;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface IscmStoreAutonomyAllotService {

    void importData(MultipartFile file, TokenUserDTO userDTO) throws Exception;

    void asyncAutonomyAllotImportData(StoreAutonomyAllotImportParamDTO paramDTO) throws Exception;

    void createAllotSuggest(TokenUserDTO userDTO, CreateAllotSuggestDTO createAllotSuggestDTO) throws Exception;

    PageResponse<List<StoreAutonomyAllotImportDTO>> getAllotImportList(TokenUserDTO userDTO, Integer page, Integer pageSize);

    PageResponse<List<StoreAutonomyAllotGoodsDetailDTO>> getAllotDetailList(TokenUserDTO userDTO, AllotDetailListParamDTO paramDTO) throws Exception;

    ProcessProgressDTO getProcessProgress(TokenUserDTO userDTO,Byte processProgressType,Long taskId);

    void exportAllotDetailList(TokenUserDTO userDTO, AllotDetailListParamDTO paramDTO);

    IscmStoreAutonomyAllotSendPosTask sendPos(TokenUserDTO userDTO, AllotDetailListParamDTO paramDTO) throws Exception;

    void asyncDealSendPos(IscmStoreAutonomyAllotSendPosTask task) throws Exception;

    Boolean deleteCacheByImport(Byte type, Long userId);

    void delete(TokenUserDTO userDTO, List<Long> ids);
}
