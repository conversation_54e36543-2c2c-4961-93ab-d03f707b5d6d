package com.cowell.iscm.service;

import com.cowell.iscm.entity.IscmSuggestAllotGoodsDetail;
import com.cowell.iscm.service.dto.TokenUserDTO;
import com.cowell.iscm.service.dto.configcenter.OrgToBdpMapping;
import com.cowell.iscm.service.dto.registerOrder.*;
import com.cowell.iscm.service.feign.response.PageResponse;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.List;

/**
 * 登记单
 * <AUTHOR>
 */
public interface IscmRegisterOrderService {
    // 大库存登记缓存key
    public static final String ISCM_CREATR_STOCK_REGISTER_CACHE_KEY = "ISCM-CREATR-STOCK-REGISTER-CACHE-USERID-";

    // 近效期登记缓存key
    public static final String ISCM_CREATR_NEAR_EXPIRY_REGISTER_CACHE_KEY = "ISCM-CREATR-NEAR-EXPIRY-REGISTER-CACHE-USERID-";

    // 生成近效期登记临时表缓存key
    public static final String ISCM_GEN_NEAR_EXPIRY_REGISTER_TEMP_CACHE_KEY = "ISCM-GEN-NEAR-EXPIRY-REGISTER-TEMP-CACHE-USERID-";

    PageResponse<List<IscmGoodsRegisterOrderDTO>> getRegisterOrderList(TokenUserDTO userDTO, RegisterParamDTO paramDTO) throws Exception;

    void createRegisterOrder(TokenUserDTO userDTO, GenRegisterParam param) throws Exception;

    PageResponse<List<IscmWillGoodsRegisterOrderDetailDTO>> getWillRegisterList(TokenUserDTO userDTO, GenRegisterParam param) throws Exception;

    IscmGoodsRegisterOrderDTO getRegisterOrderDetail(TokenUserDTO userDTO, String registerOrderNo, Integer registerMonth) throws Exception;

    public void asynGenStockRegisterOrder(AsynRegisterParamDTO paramDTO) throws Exception;

    public void asynGenNearExpiryRegisterOrder(AsynRegisterParamDTO paramDTO) throws Exception;

    void submitNearExpiryRegister(TokenUserDTO userDTO) throws Exception;

    RegisterProcessProgressDTO getProcessProgress(TokenUserDTO userDTO, Byte registerType) throws Exception;

    void downloadErrorRegister(TokenUserDTO userDTO, Byte registerType, HttpServletResponse response) throws Exception;

    /**
     * 更新登记单明细建议状态值
     *
     * @param iscmSuggestAllotGoodsDetail
     * @return
     */
    int updateRegisterOrderDetailSuggestStatus(IscmSuggestAllotGoodsDetail iscmSuggestAllotGoodsDetail);

    void asynAutoRegister() throws Exception;

    void asynGenAutoRegisterOrder(OrgToBdpMapping orgToBdpMapping) throws Exception;

    /**
     * 根据登记单号作废登记单
     * @param userDTO
     * @param registerOrderNo
     */
    void invalid(TokenUserDTO userDTO, RegisterBatchParam param);

    /**
     * 同步iscm_register_result数据更新商品登记单及商品登记单明细表的处理状态及未产生建议原因
     */
    void syncRegisterResult(String today);

    String genNearExpiryRegisterData(TokenUserDTO userDTO, List<String> allotGroupCodes) throws Exception;

    void asynGenNearExpiryRegisterOrderTemp(GenNearExpiryRegisterDTO genNearExpiryRegisterDTO) throws Exception;

    PageResponse<List<IscmNearExpiryRegisterTempDTO>> getNearExpiryRegisterList(TokenUserDTO userDTO, FilterRegisterParam param) throws Exception;

    void updateNearExpiryRegisterQuantity(TokenUserDTO userDTO, Long id, BigDecimal registerQuantity) throws Exception;

    NearExpiryProcessBO getNearExpriyRegisterProcess(TokenUserDTO userDTO) throws Exception;

    Long submitTempNearExpiryRegister(TokenUserDTO userDTO, FilterRegisterParam param) throws Exception;

    void asnySubmitTempNearExpiryRegister(AsnySubmitTempDTO submitTempDTO) throws Exception;

    void autoUpdateRegisterOrderStatus(String day);

    Boolean deleteRegisterRedisCache(Long userId, Byte registreType) throws Exception;
}
