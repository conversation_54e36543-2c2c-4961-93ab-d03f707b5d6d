package com.cowell.iscm.service;

import com.cowell.iscm.entity.IscmPushReplenishmentMonitorFinishCache;
import com.cowell.iscm.entityTidb.IscmNodePlanTime;
import com.cowell.iscm.enums.MonitorBusinessTypeEnum;
import com.cowell.iscm.service.dto.TokenUserDTO;
import com.cowell.iscm.service.dto.pushReplenishmentMonitor.*;
import com.cowell.iscm.service.feign.response.PageResponse;

import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;

public interface PushReplenishmentMonitorClearDataService {
    void clearPushReplenishmentMonitorData30DaysBefore();
}
