package com.cowell.iscm.service;

import com.cowell.iscm.enums.StoreChooseTypeEnum;
import com.cowell.iscm.service.dto.ImportExcelResult;
import com.cowell.iscm.service.dto.TokenUserDTO;
import com.cowell.iscm.service.dto.applyParam.*;
import com.cowell.iscm.service.feign.dto.OrgTreeSimpleDTO;
import com.cowell.iscm.service.feign.response.PageResponse;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * Created by schuangxigang on 2022/6/26 23:49.
 */
public interface StoreApplyParamGoodsOrgWhitelistService {
    PageResponse<List<StoreApplyParamGoodsOrgWhitelist>> getValidWhitelistLimits(TokenUserDTO userDTO, StoreApplyParamGoodsOrgWhitelistQuery query) throws Exception;

    PageResponse<List<StoreApplyParamGoodsOrgWhitelist>> getVoidWhitelistLimits(TokenUserDTO userDTO, StoreApplyParamGoodsOrgWhitelistQuery query) throws Exception;

    void saveValidWhitelist(TokenUserDTO userDTO, StoreApplyParamGoodsOrgWhitelistAdd param) throws Exception;

    void voidWhitelist(StoreApplyParamGoodsOrgVoid goodsOrgVoid, TokenUserDTO userDTO) throws Exception;

    List<Long> tagEntityCodeListQuery(List<Long> tagIds);

    ImportExcelResult batchImport(TokenUserDTO userDTO, Long orgId, Integer orgType, MultipartFile file) throws Exception;

    Map<Long, OrgTreeSimpleDTO> checkAndGetStoreInfoByChooseType(StoreChooseTypeEnum storeChooseTypeEnum, Long userId, Long orgId, List<Long> storeOrgIds, List<Long> tagIds) throws Exception;

    List<Long> getWhitelistIdsByOrgId(TokenUserDTO userDTO, StoreApplyParamGoodsOrgWhitelistQuery query) throws Exception;

    List<StoreApplyParamGoodsImport> getGoodsList(MultipartFile File, TokenUserDTO userDTO) throws Exception;
}