package com.cowell.iscm.service.dto.unmanageGoodsReturn;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * 不经营品退仓进度跟踪DTO
 * 
 * <AUTHOR>
 */
@ApiModel("不经营品退仓进度跟踪DTO")
public class UnmanageGoodsReturnProgressDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "任务编码")
    private String taskCode;

    @ApiModelProperty(value = "总处理数量（门店数量）")
    private Integer processCount;

    @ApiModelProperty(value = "成功处理数量（门店数量")
    private Integer successCount;

    @ApiModelProperty(value = "进度条值")
    private BigDecimal processPercent;

    @ApiModelProperty(value = "错误处理数量（门店数量")
    private Integer errorCount;

    @ApiModelProperty(value = "是否处理完成")
    private Boolean processFinished;

    @ApiModelProperty(value = "当前处理阶段")
    private String currentStage;

    @ApiModelProperty(value = "当前处理门店")
    private String currentStore;

    @ApiModelProperty(value = "错误列表")
    private List<UnmanageGoodsReturnErrorDTO> errorList;

    @ApiModelProperty(value = "处理开始时间")
    private Long startTime;

    @ApiModelProperty(value = "处理结束时间")
    private Long endTime;

    public String getTaskCode() {
        return taskCode;
    }

    public void setTaskCode(String taskCode) {
        this.taskCode = taskCode;
    }

    public Integer getProcessCount() {
        return processCount;
    }

    public void setProcessCount(Integer processCount) {
        this.processCount = processCount;
    }

    public Integer getSuccessCount() {
        return successCount;
    }

    public void setSuccessCount(Integer successCount) {
        this.successCount = successCount;
    }

    public Integer getErrorCount() {
        return errorCount;
    }

    public void setErrorCount(Integer errorCount) {
        this.errorCount = errorCount;
    }

    public Boolean getProcessFinished() {
        return processFinished;
    }

    public void setProcessFinished(Boolean processFinished) {
        this.processFinished = processFinished;
    }

    public String getCurrentStage() {
        return currentStage;
    }

    public void setCurrentStage(String currentStage) {
        this.currentStage = currentStage;
    }

    public String getCurrentStore() {
        return currentStore;
    }

    public void setCurrentStore(String currentStore) {
        this.currentStore = currentStore;
    }

    public List<UnmanageGoodsReturnErrorDTO> getErrorList() {
        return errorList;
    }

    public void setErrorList(List<UnmanageGoodsReturnErrorDTO> errorList) {
        this.errorList = errorList;
    }

    public Long getStartTime() {
        return startTime;
    }

    public void setStartTime(Long startTime) {
        this.startTime = startTime;
    }

    public Long getEndTime() {
        return endTime;
    }

    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }




    public BigDecimal getProcessPercent() {
        BigDecimal total = new BigDecimal(processCount);
        // 处理总数量为0的情况（避免除数为0）
        if (total.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.valueOf(100); // 或根据业务返回其他值（如-1表示未开始）
        }
        // 计算已处理数量（总数 - 错误数）
        BigDecimal processed = new BigDecimal(processCount - errorCount);
        // 计算百分比：(已处理 / 总数) × 100，保留2位小数，四舍五入
        return processed.multiply(BigDecimal.valueOf(100)).divide(total, 2, RoundingMode.HALF_UP);
    }

    @Override
    public String toString() {
        return "UnmanageGoodsReturnProgressDTO{" +
                "taskCode='" + taskCode + '\'' +
                ", processCount=" + processCount +
                ", successCount=" + successCount +
                ", errorCount=" + errorCount +
                ", processFinished=" + processFinished +
                ", currentStage='" + currentStage + '\'' +
                ", currentStore='" + currentStore + '\'' +
                ", errorList=" + errorList +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                '}';
    }
}
