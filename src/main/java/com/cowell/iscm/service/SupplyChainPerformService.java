package com.cowell.iscm.service;

import com.cowell.iscm.service.dto.supplyChain.perform.*;

import java.util.List;

public interface SupplyChainPerformService {
    List<GoodStockDTO> queryGoodsStockInfo(Long storeId, Long outStoreId, String keyWord, String goodsNo, Integer performType, String storeQuery, Boolean appQuery);

    List<GoodStockDTO> queryGoodsStockInfoByRetrospectCode(Long storeId, Long outStoreId, String retrospectCode, Integer performType, String storeQuery);
//    List<GoodStockDTO> queryGoodsStockInfoByRetrospectCodeList(RetrospectQueryParam param);
    Boolean queryCompanySwitch(Long storeId, Long outStoreId, Integer performType);
}
