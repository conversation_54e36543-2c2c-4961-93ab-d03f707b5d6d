package com.cowell.iscm.service;

import com.cowell.iscm.entity.IscmStoreApplyParamOrg;
import com.cowell.iscm.enums.ApplyParamCodeEnum;
import com.cowell.iscm.service.dto.TokenUserDTO;
import com.cowell.iscm.service.dto.applyParam.ApplyParamBaseDTO;
import com.cowell.iscm.service.dto.applyParam.AutoApplyParamDTO;
import com.cowell.iscm.service.feign.dto.OrgSimpleDTO;
import com.cowell.permission.dto.OrgDTO;
import com.cowell.permission.vo.OrgTreeVO;

import java.util.List;
import java.util.Map;

/**
 * Created by schuangxigang on 2022/6/23 13:32.
 */
public interface StoreApplyParamOrgService {
    List<AutoApplyParamDTO> getParamList(Long orgId);
    Map<String, AutoApplyParamDTO> getParamMap(Long orgId);
    List<IscmStoreApplyParamOrg> getParamOrgList(TokenUserDTO tokenUserDTO, Long orgId, Integer orgType, List<AutoApplyParamDTO> paramDTOS);
    void saveParamOrgList(Long orgId,Integer orgTyp, List<IscmStoreApplyParamOrg> paramOrgList,TokenUserDTO userDTO) throws Exception;

    OrgTreeVO getOrgTreeVO(TokenUserDTO tokenUserDTO, Long orgId, Integer orgType);

    void paramSetting(OrgTreeVO orgTreeVO, List<OrgDTO> parents, ApplyParamCodeEnum paramCodeEnum, ApplyParamBaseDTO paramBaseDTO,Long orgId);

    // 校验当前用户是否有某个机构的全部权限
    OrgSimpleDTO checkUserAllPermByOrgId(Long userId, Long orgId) throws Exception;
}
