package com.cowell.iscm.service;

import com.cowell.iscm.service.dto.TokenUserDTO;
import com.cowell.iscm.service.dto.tableShowField.IscmTableShowFieldDefaultDTO;
import com.cowell.iscm.service.dto.tableShowField.TableShowFieldEditDTO;

import java.util.List;

public interface IscmTableShowFieldService {

    void editDefault(TokenUserDTO userDTO, IscmTableShowFieldDefaultDTO defaultDTO);

    IscmTableShowFieldDefaultDTO defaultInfo(TokenUserDTO userDTO, Byte tableType);

    void edit(TokenUserDTO userDTO, TableShowFieldEditDTO fieldEditDTO);

    String listByTableType(TokenUserDTO userDTO, Byte tableType);

    String defaultListByTableType(TokenUserDTO userDTO, Byte tableType);
}
