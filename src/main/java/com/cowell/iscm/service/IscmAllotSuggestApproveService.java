package com.cowell.iscm.service;

import com.cowell.iscm.entity.IscmSuggestAllotApproveAllTask;
import com.cowell.iscm.service.dto.TokenUserDTO;
import com.cowell.iscm.service.dto.suggestAllot.*;
import com.cowell.iscm.service.feign.response.PageResponse;

import java.util.List;

/**
 * 调拨建议管理
 * <AUTHOR>
 * @date  2021-02-27 10:35:07
 */
public interface IscmAllotSuggestApproveService {
    /**
     * 查询调拨建议列表
     * @param userDTO
     * @param param
     * @return
     * @throws Exception
     */
    PageResponse<List<IscmSuggestAllotDTO>> getAllotSuggestList(TokenUserDTO userDTO, AllotRequestParam param) throws Exception;

    /**
     * 查询调拨建议详情页列表
     * @param userDTO
     * @param param
     * @return
     * @throws Exception
     */
    PageResponse<List<IscmSuggestGoodsDetailsDTO>> getAllotSuggestDetailList(TokenUserDTO userDTO, AllotRequestParam param) throws Exception;

    /**
     * 全部审批通过
     * @param userDTO
     * @param param
     * @return
     */
    IscmSuggestApproveTaskDTO passAllotSuggestListAll(TokenUserDTO userDTO, AllotRequestParam param) throws Exception ;

    /**
     * 作废调拨建议
     * @param userDTO
     * @param suggestId
     * @throws Exception
     */
    void invalidSuggest(TokenUserDTO userDTO, List<Long> suggestId) throws Exception;

    /**
     * 通过调拨建议
     * @param userDTO
     * @param suggestIds
     * @throws Exception
     */
    IscmSuggestApproveTaskDTO passSuggest(TokenUserDTO userDTO, List<Long> suggestIds) throws Exception;

    /**
     * 编辑实际调拨数量
     * @param userDTO
     * @param suggestId
     * @param realAllotQuantity
     * @throws Exception
     */
    void editSuggest(TokenUserDTO userDTO, Long suggestId, String realAllotQuantity) throws Exception;

    /**
     * 自动作废调拨建议(根据orgId 取配置天数)
     * @throws Exception
     */
    void autoInvalidSuggest() throws Exception;

    /**
     * MQ 消费模式处理 调拨建议 多选行/审批全部 根据 type 区分
     * @param param
     */
    void asynApproveAllSuggest(IscmSuggestAllotApproveAllTask param) throws Exception;

    /**
     * 查询审批全部 任务处理进度
     * @param userDTO
     * @param taskId
     * @return
     */
    IscmSuggestApproveTaskProcessDTO getAllotSuggestListTaskProcess(TokenUserDTO userDTO, Long taskId)throws Exception;
}
