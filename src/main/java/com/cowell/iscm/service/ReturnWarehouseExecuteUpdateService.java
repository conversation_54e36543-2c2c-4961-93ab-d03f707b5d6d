package com.cowell.iscm.service;

import com.cowell.iscm.entity.IscmStoreReturnExecuteOrderDetail;
import com.cowell.iscm.enums.RegisterReturnWarehouseTypeEnum;
import com.cowell.iscm.service.dto.TokenUserDTO;
import com.cowell.iscm.service.dto.returnWarehouse.*;

import java.util.List;

/**
 * 退仓执行更新 服务接口
 *
 * <AUTHOR>
 */
public interface ReturnWarehouseExecuteUpdateService {
    void updateOrder(List<StoreReturnExecuteOrderUpdateDTO> orderUpdateDTOS) throws Exception;
    void updateProcessStatus(String orderNo, List<String> rowNos, Integer month, Byte status) throws Exception;
    void updateByOA(List<ReturnWarehouseExecuteIssueFromOaDetailDTO> fromOaDetailDTOS) throws Exception;
    void updateByPOS(List<ReturnWarehouseExecuteIssueFromPosDTO> fromPosDTOS) throws Exception;
    void updateOrderMain(List<StoreReturnExecuteOrderUpdateDTO> orderUpdateDTOS);
    void updateOrderMain(StoreReturnExecuteOrderUpdateDTO orderUpdateDTO);
    void updateOrderDetail(List<StoreReturnExecuteOrderUpdateDTO> orderUpdateDTOS);
    void updateProcessStatusAll(int month, List<String> orderNos);

    void updateSummedQty(TokenUserDTO userDTO, Integer month, List<StoreReturnExecuteOrderSum> orderSumList);

    void updateRowNo(List<Long> orderIdList, Integer month);

    void updateReturnWarehouseStatus(StoreReturnWarehouseGoods goods, RegisterReturnWarehouseTypeEnum returnWarehouseTypeEnum);
    void updateReturnWarehouseStatus(List<IscmStoreReturnExecuteOrderDetail> orderDetails, RegisterReturnWarehouseTypeEnum returnWarehouseTypeEnum);

    //todo 测试 上线时删除
    void testPushWms(String orderNo, Integer month);
}
