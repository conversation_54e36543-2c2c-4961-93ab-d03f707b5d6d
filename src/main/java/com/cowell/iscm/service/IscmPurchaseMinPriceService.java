package com.cowell.iscm.service;

import com.cowell.iscm.service.dto.TokenUserDTO;
import com.cowell.iscm.service.dto.purchaseMinPrice.IscmPurchaseMinPriceDTO;
import com.cowell.iscm.service.dto.purchaseMinPrice.PurchaseMinPriceParam;

import java.util.List;

public interface IscmPurchaseMinPriceService {
    /**
     * 获取商品详情
     * @param param
     * @param userDTO
     * @return
     * @throws Exception
     */
    List<IscmPurchaseMinPriceDTO> getGoodsList(PurchaseMinPriceParam param, TokenUserDTO userDTO) throws Exception;

    /**
     * 获取商品总条数
     * @param param
     * @param userDTO
     * @return
     * @throws Exception
     */
    Long getGoodsCount(PurchaseMinPriceParam param, TokenUserDTO userDTO) throws Exception;

    /**
     * 获取当前人员查询剩余的查询次数
     * @param userDTO
     * @return
     * @throws Exception
     */
    Integer remainSelectTimes(TokenUserDTO userDTO) throws Exception;

    /**
     * 按天数删除查询记录表
     * @param days
     * @return
     * @throws Exception
     */
    Integer deleteRecordByDays(Integer days) throws Exception;

    /**
     * 查询监控
     */
    void monitorSelect() throws Exception;
}
