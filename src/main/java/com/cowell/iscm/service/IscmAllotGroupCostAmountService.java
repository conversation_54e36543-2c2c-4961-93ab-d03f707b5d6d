package com.cowell.iscm.service;

import com.cowell.iscm.service.dto.TokenUserDTO;
import com.cowell.iscm.service.dto.allotGroupCostAmount.AllotGroupCostAmountCreateDTO;
import com.cowell.iscm.service.dto.allotGroupCostAmount.AllotGroupCostAmountDTO;
import com.cowell.iscm.service.dto.allotGroupCostAmount.AllotGroupCostAmountUpdateDTO;

public interface IscmAllotGroupCostAmountService {
    String createAllotGroupCostAmount(TokenUserDTO userDTO, AllotGroupCostAmountCreateDTO createDTO) throws Exception;

    AllotGroupCostAmountDTO getGroupCostAmount(TokenUserDTO userDTO, Long companyId) throws Exception;

    String updateCostAmount(TokenUserDTO userDTO, AllotGroupCostAmountUpdateDTO updateDTO) throws Exception;
}
