package com.cowell.iscm.service;

import com.cowell.iscm.entity.IscmStoreApplyParamGoodsValidWhitelist;
import com.cowell.iscm.entity.IscmStoreApplyParamGoodsVoidWhitelist;
import com.cowell.iscm.entity.WhiteListPushDTO;
import com.cowell.iscm.entity.WhiteListVoidPushDTO;
import com.cowell.iscm.service.dto.TokenUserDTO;
import com.cowell.iscm.service.dto.applyParam.*;
import com.cowell.iscm.service.feign.dto.MdmStoreBaseDTO;
import com.cowell.iscm.service.feign.response.PageResponse;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

public interface StoreApplyParamGoodsWhitelistService {

    /**
     * 查询接口
     */
    PageResponse<List<StoreApplyParamGoodsOrgWhitelist>> getValidWhitelistLimits(TokenUserDTO userDTO, StoreApplyParamGoodsOrgWhitelistQuery query) throws Exception;

    /**
     * 已作废数据查询
     */
    PageResponse<List<StoreApplyParamGoodsOrgWhitelist>> getVoidWhitelistLimits(TokenUserDTO userDTO, StoreApplyParamGoodsOrgWhitelistQuery query) throws Exception;

    /**
     * 保存接口
     */
    void saveValidWhitelist(TokenUserDTO userDTO, StoreApplyParamGoodsOrgWhitelistAdd param) throws Exception;

    /**
     * 根据ID作废生效白名单
     */
    void voidWhitelist(StoreApplyParamGoodsOrgVoid goodsOrgVoid, TokenUserDTO userDTO) throws Exception;

    /**
     * 根据ID作废生效白名单 - 无权限控制
     */
    void voidWhitelistNoAuth(List<Long> ids) throws Exception;

    /**
     * 根据导入数据查询商品详情
     */
    WhitelistImportDTO getGoodsList(MultipartFile file, TokenUserDTO userDTO) throws Exception;

    /**
     * 查看适用组织
     */
    List<Long> getEffectiveOrgById(Long id);

    /**
     * 根据OrgId获取所有数据ID
     */
    List<Long> getWhitelistIdsByOrgId(TokenUserDTO userDTO, StoreApplyParamGoodsOrgWhitelistQuery query) throws Exception;

    List<WhiteListPushDTO> getValidStoreListByPush(List<IscmStoreApplyParamGoodsValidWhitelist> savedList);

    List<WhiteListVoidPushDTO> getVoidStoreListByPush(List<IscmStoreApplyParamGoodsVoidWhitelist> savedList);

    void autoVoidByVoidDate() throws Exception;

    void exportValidWhitelist(TokenUserDTO userDTO, StoreApplyParamGoodsOrgVoid query);
}
