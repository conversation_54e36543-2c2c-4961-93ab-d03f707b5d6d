package com.cowell.iscm.service;

import com.cowell.iscm.service.dto.TokenUserDTO;
import com.cowell.iscm.service.dto.bestSuggest.DealParam;
import com.cowell.iscm.service.dto.bestSuggest.SuggestBestGoodsDTO;
import com.cowell.iscm.service.feign.response.PageResponse;

import java.util.List;

public interface BestGoodsSuggestService {

    Boolean judgeRemindAble(Long storeId);

    PageResponse<List<SuggestBestGoodsDTO>> getSuggestList(Long storeId, Byte selectType, Byte suggestType, Byte sortType, String sortFieldName, Integer page, Integer pageSize);

    void dealBdpSuggest(DealParam param, TokenUserDTO userDTO);

    Long getRefuseCount(Long storeId);
}
