package com.cowell.iscm.service;

import java.util.Date;

/**
 * 调拨跟踪  pos审批状态 和  调拨完成销量跟踪 数据
 * <AUTHOR>
 * @date  2021-03-25 16:36:26
 */
public interface AllotTrackSyncDataService {
    /**
     * pos审批状态同步
     * 每隔两小时 处理大数据已同步到iscm_suggest_distexec表的内容 到  iscm_suggest_distexec_detail 表
     * @param execTime 当前执行时间
     * @return
     * @throws Exception
     */
    void syncSuggestExecDataHander(Date execTime) throws Exception;


    /**
     * 调拨完成销量 同步
     * 每天1点 处理大数据已同步到iscm_suggest_distexec_done表的内容 到  iscm_suggest_distexec_done_detail 表
     *
     * @param execTime 当前执行时间
     * @return
     * @throws Exception
     */
    void syncSuggestExecDoneDataHander(Date execTime) throws Exception;



}
