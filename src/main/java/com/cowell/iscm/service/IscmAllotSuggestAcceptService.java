package com.cowell.iscm.service;

import com.cowell.iscm.entity.IscmSuggestAllotOriginData;
import com.cowell.iscm.service.dto.suggestAllot.AcceptAllotSuggestDTO;

import java.util.List;

/**
 * 调拨建议 接收调拨建议处理和接收POS调拨单号回调
 * <AUTHOR>
 * @date  2021-02-27 10:35:07
 */
public interface IscmAllotSuggestAcceptService {

    /**
     * 处理pos 系统处理完成调拨建议的 回调
     * @param posReport
     * @return
     * @throws Exception
     */
    boolean managePosTransferApproveSuggest(String posReport) throws Exception;

    /**
     * 接收 bdp 系统推送的 调拨建议
     * @param bdpReport
     * @return
     * @throws Exception
     */
    boolean manageBDPTransferAllotSuggest(String bdpReport) throws Exception;

    /**
     * 模拟审批通过的发送pos push
     * @param ids
     * @throws Exception
     */
    void push(List<Long> ids) throws Exception;

    /**
     * 手动处理接口-接收bdp系统的调拨建议
     * @param acceptAllotSuggest
     * @throws Exception
     */
    boolean restSelfAcceptAllot(AcceptAllotSuggestDTO acceptAllotSuggest) throws Exception;

    /**
     * 异步保存 调拨建议
     * @return
     */
    boolean handleAllotSuggestOriginDataSaveEvent(IscmSuggestAllotOriginData originData);

    /**
     * 半小时一次 补偿2天内 已生成建议但是未更新登记单 suggest_status 的数据
     * @return
     */
    boolean handleUpdateRegisterOrderSuggestStatus(String handleTime);
}
