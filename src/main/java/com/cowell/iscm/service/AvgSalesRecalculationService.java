package com.cowell.iscm.service;

import com.cowell.iscm.entityTidb.StoreApplyDate;
import com.cowell.iscm.enums.CombinedCodeRangeEnum;
import com.cowell.iscm.service.dto.AvgSalesQueryDTO;
import com.cowell.iscm.service.dto.BdpAvgDailySalesDTO;
import com.cowell.iscm.service.dto.applyParam.StoreApplyParamDTO;
import com.cowell.iscm.service.feign.dto.MdmStoreBaseDTO;
import com.cowell.permission.dto.OrgDTO;
import com.cowell.permission.vo.OrgVO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

public interface AvgSalesRecalculationService {

    void avgRecalculationByCompany(String companyCode) throws Exception;

    void avgRecalculationByStores(String companyCode, String storeCodes) throws Exception;

    /**
     * 按照门店用日均销复算
     * @param company 企业
     * @param store 门店
     * @param autoApplyList 请货参数
     * @param storeApplyDate 请货日门店信息
     * @param goodsLines 不自动请货商品经营属性
     * @param goodsClasses 不自动请货商品类型
     * @param goodsSigns 不自动请货商品标记
     * @param specialOnceLimit
     * @param specialThirtyDaysLimit
     * @throws Exception
     */
    void avgRecalculationByStore(Optional<OrgDTO> platform, OrgVO company, OrgVO store, StoreApplyParamDTO autoApplyList, MdmStoreBaseDTO storeApplyDate, List<Byte> goodsLines, List<String> goodsClasses, List<String> goodsSigns, BigDecimal specialOnceLimit, BigDecimal specialThirtyDaysLimit, StoreApplyParamDTO middleApplyList) throws Exception;

    /**
     * bdp推送完日均销回调接口
     */
    void bdpCallbackDealAvg(String companyCode, String storeCodes);

    /**
     * bdp回调请货门店接口
     */
    void bdpCallbackStoreApplyDate();

    void consumerBdpAvgSales(OrgVO store);

    void refreshStoreAttr(List<String> jmdStoreCodes);

    List<BdpAvgDailySalesDTO> getList(AvgSalesQueryDTO param);
}
